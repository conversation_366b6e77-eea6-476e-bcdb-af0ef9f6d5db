{"version": 3, "file": "orderReason-CnKBJLQ8.js", "sources": ["../../src/views/group/system/config/components/orderReason.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"changeReason\": \"Change Reason\",\r\n    \"cancelReason\": \"Order Cancellation Reason\",\r\n    \"offsetReason\": \"Offset Reason\",\r\n    \"repairReason\": \"Repair Reason\",\r\n    \"lockReason\": \"Lock Room Reason\",\r\n    \"save\": \"Save\",\r\n    \"pleaseInputReason\": \"Please input the reason\",\r\n    \"successMessage\": \"Added successfully\",\r\n    \"editSuccessMessage\": \"Edited successfully\",\r\n    \"statusEnabled\": \"Enabled\",\r\n    \"statusDisabled\": \"Disabled\",\r\n    \"saveButton\": \"Save\",\r\n    \"cancelButton\": \"Cancel\",\r\n    \"editButton\": \"Edit\",\r\n    \"operation\": \"Actions\",\r\n    \"status\": \"Status\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"changeReason\": \"换房原因\",\r\n    \"cancelReason\": \"订单取消原因\",\r\n    \"offsetReason\": \"冲调账原因\",\r\n    \"repairReason\": \"维修原因\",\r\n    \"lockReason\": \"锁房原因\",\r\n    \"save\": \"保存\",\r\n    \"pleaseInputReason\": \"请输入原因\",\r\n    \"successMessage\": \"新增成功\",\r\n    \"editSuccessMessage\": \"编辑成功\",\r\n    \"statusEnabled\": \"启用\",\r\n    \"statusDisabled\": \"停用\",\r\n    \"saveButton\": \"保存\",\r\n    \"cancelButton\": \"取消\",\r\n    \"editButton\": \"编辑\",\r\n    \"operation\": \"操作\",\r\n    \"status\": \"状态\"\r\n  },\r\n  \"km\": {\r\n    \"changeReason\": \"មូលហេតុប្តូរបន្ទប់\",\r\n    \"cancelReason\": \"មូលហេតុលុបចោលការកម្មង់\",\r\n    \"offsetReason\": \"មូលហេតុបញ្ច្រាសគណនី\",\r\n    \"repairReason\": \"មូលហេតុជួសជុល\",\r\n    \"lockReason\": \"មូលហេតុចាក់សោបន្ទប់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"pleaseInputReason\": \"សូមបញ្ចូលមូលហេតុ\",\r\n    \"successMessage\": \"បន្ថែមបានជោគជ័យ\",\r\n    \"editSuccessMessage\": \"កែសម្រួលបានជោគជ័យ\",\r\n    \"statusEnabled\": \"បើកដំណើរការ\",\r\n    \"statusDisabled\": \"បិទដំណើរការ\",\r\n    \"saveButton\": \"រក្សាទុក\",\r\n    \"cancelButton\": \"បោះបង់\",\r\n    \"editButton\": \"កែសម្រួល\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"status\": \"ស្ថានភាព\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { GeneralConfigModel } from '@/models/index'\r\n\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { generalConfigApi } from '@/api/modules/index'\r\nimport { BooleanEnum, CANCEL_REASON, CHANGE_REASON, LOCK_REASON, OFFSET_REASON, REPAIR_REASON } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  code: '',\r\n  name: '',\r\n  value: '',\r\n  type: CHANGE_REASON,\r\n  remark: '',\r\n  isG: BooleanEnum.YES,\r\n})\r\nconst formRules = computed<FormRules>(() => ({\r\n  name: [{ required: true, message: t('pleaseInputReason'), trigger: 'blur' }],\r\n}))\r\n\r\nconst dataList = ref<GeneralConfigModel[]>([])\r\n\r\nconst type = ref(CHANGE_REASON)\r\n\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  generalConfigApi.list({ gcode: userStore.gcode, type: type.value, isG: BooleanEnum.YES }).then((res: any) => {\r\n    loading.value = false\r\n    dataList.value = res.data\r\n  })\r\n}\r\n\r\nfunction submit() {\r\n  form.value.value = form.value.name.trim()\r\n  return new Promise<void>((resolve) => {\r\n    formRef.value &&\r\n      formRef.value.validate((valid) => {\r\n        if (valid) {\r\n          generalConfigApi.createGeneralConfigReason(form.value).then((res: any) => {\r\n            if (res.code === 0) {\r\n              form.value.name = ''\r\n              form.value.value = ''\r\n              ElMessage.success({\r\n                message: t('successMessage'),\r\n                center: true,\r\n              })\r\n              resolve()\r\n              getInfo()\r\n            } else {\r\n              ElMessage.error({\r\n                message: res.msg,\r\n                center: true,\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n  })\r\n}\r\n\r\nfunction menuClick(typeCode: string) {\r\n  type.value = typeCode\r\n  form.value.type = typeCode\r\n  getInfo()\r\n}\r\n\r\nconst menuMap = computed(\r\n  () =>\r\n    new Map<string, { index: string; menuName: string }>([\r\n      [CHANGE_REASON, { index: '1', menuName: t('changeReason') }],\r\n      [CANCEL_REASON, { index: '2', menuName: t('cancelReason') }],\r\n      [OFFSET_REASON, { index: '3', menuName: t('offsetReason') }],\r\n      [REPAIR_REASON, { index: '4', menuName: t('repairReason') }],\r\n      [LOCK_REASON, { index: '5', menuName: t('lockReason') }],\r\n    ])\r\n)\r\n/** 左侧三级菜单 */\r\nconst leftmenuMap = reactive([\r\n  { click: CHANGE_REASON, index: '1', menuName: t('changeReason') },\r\n  { click: CANCEL_REASON, index: '2', menuName: t('cancelReason') },\r\n  { click: OFFSET_REASON, index: '3', menuName: t('offsetReason') },\r\n  { click: REPAIR_REASON, index: '4', menuName: t('repairReason') },\r\n  { click: LOCK_REASON, index: '5', menuName: t('lockReason') },\r\n])\r\n// 改变状态\r\nfunction onChangeStatus(row: any) {\r\n  return new Promise<boolean>((resolve) => {\r\n    row.statusLoading = true\r\n    generalConfigApi\r\n      .updateGeneralConfigReasonStatus({\r\n        id: row.id,\r\n        isEnable: row.isEnable === '0' ? '1' : '0',\r\n      })\r\n      .then(() => {\r\n        row.statusLoading = false\r\n        ElMessage.success({\r\n          message: `${row.isEnable === '0' ? t('statusEnabled') : t('statusDisabled')}`,\r\n          center: true,\r\n        })\r\n        return resolve(true)\r\n      })\r\n      .catch(() => {\r\n        row.statusLoading = false\r\n        return resolve(false)\r\n      })\r\n  })\r\n}\r\n\r\nfunction onEdit(row: any) {\r\n  return new Promise<boolean>((resolve) => {\r\n    // 组装表单\r\n    const x = {\r\n      id: row.id,\r\n      gcode: userStore.gcode,\r\n      name: row.name,\r\n      value: row.name,\r\n      type: type.value,\r\n    }\r\n    generalConfigApi.updateGeneralConfig(x).then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success({\r\n          message: t('editSuccessMessage'),\r\n          center: true,\r\n        })\r\n        row.isEdit = false\r\n        return resolve(true)\r\n      } else {\r\n        ElMessage.error({\r\n          message: res.msg,\r\n          center: true,\r\n        })\r\n        return resolve(false)\r\n      }\r\n    })\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-container>\r\n    <el-aside style=\"width: 300px\">\r\n      <el-container>\r\n        <el-main class=\"nopadding\">\r\n          <el-menu default-active=\"1\" class=\"el-menu-vertical-demo\">\r\n            <el-menu-item v-for=\"item of leftmenuMap\" :key=\"item.index\" :index=\"item.index\" @click=\"menuClick(item.click)\">\r\n              {{ item.menuName }}\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </el-main>\r\n      </el-container>\r\n    </el-aside>\r\n    <el-main>\r\n      <div>\r\n        <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-suffix=\":\" label-width=\"250px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :md=\"16\">\r\n              <el-form-item :label=\"menuMap.get(type)?.menuName\" prop=\"name\">\r\n                <el-input v-model=\"form.name\" :placeholder=\"t('pleaseInputReason')\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :md=\"8\">\r\n              <el-button type=\"primary\" @click=\"submit\">\r\n                {{ t('saveButton') }}\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n        <el-table v-loading=\"loading\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" class=\"list-table\" :data=\"dataList\" height=\"100%\">\r\n          <el-table-column :label=\"menuMap.get(type)?.menuName\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.name\" size=\"small\" />\r\n              <span v-else>{{ scope.row.name }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('status')\">\r\n            <template #default=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.isEnable\"\r\n                :loading=\"scope.row.statusLoading\"\r\n                inline-prompt\r\n                :active-text=\"t('statusEnabled')\"\r\n                :inactive-text=\"t('statusDisabled')\"\r\n                active-value=\"1\"\r\n                inactive-value=\"0\"\r\n                :before-change=\"() => onChangeStatus(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('operation')\" align=\"left\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <template v-if=\"scope.row.isEdit\">\r\n                <el-button type=\"primary\" plain size=\"small\" @click=\"onEdit(scope.row)\">\r\n                  {{ t('saveButton') }}\r\n                </el-button>\r\n                <el-button plain size=\"small\" @click=\"scope.row.isEdit = false\">\r\n                  {{ t('cancelButton') }}\r\n                </el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"primary\" plain size=\"small\" @click=\"scope.row.isEdit = true\">\r\n                  {{ t('editButton') }}\r\n                </el-button>\r\n              </template>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-main>\r\n  </el-container>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-menu-item) {\r\n  &.is-active {\r\n    background-color: var(--el-color-primary-light-9);\r\n  }\r\n  &:hover {\r\n    background-color: var(--el-color-primary-light-9);\r\n    color: var(--el-menu-active-color);\r\n  }\r\n}\r\n\r\n.el-main.nopadding {\r\n  padding: 0;\r\n  background: #fff;\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "code", "name", "value", "type", "CHANGE_REASON", "remark", "isG", "BooleanEnum", "YES", "formRules", "computed", "required", "message", "trigger", "dataList", "getInfo", "generalConfigApi", "list", "then", "res", "data", "submit", "trim", "Promise", "resolve", "validate", "valid", "createGeneralConfigReason", "ElMessage", "success", "center", "error", "msg", "onMounted", "menuMap", "Map", "index", "menuName", "CANCEL_REASON", "OFFSET_REASON", "REPAIR_REASON", "LOCK_REASON", "leftmenuMap", "reactive", "click", "typeCode", "row", "statusLoading", "updateGeneralConfigReasonStatus", "id", "isEnable", "catch", "x", "updateGeneralConfig", "isEdit"], "mappings": "2jCAmEM,MAAAA,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,MAAON,EAAUM,MACjBC,KAAM,GACNC,KAAM,GACNC,MAAO,GACPC,KAAMC,EACNC,OAAQ,GACRC,IAAKC,EAAYC,MAEbC,EAAYC,GAAoB,KAAO,CAC3CT,KAAM,CAAC,CAAEU,UAAU,EAAMC,QAASrB,EAAE,qBAAsBsB,QAAS,aAG/DC,EAAWlB,EAA0B,IAErCO,EAAOP,EAAIQ,GAMjB,SAASW,IACPpB,EAAQO,OAAQ,EAChBc,EAAiBC,KAAK,CAAElB,MAAON,EAAUM,MAAOI,KAAMA,EAAKD,MAAOI,IAAKC,EAAYC,MAAOU,MAAMC,IAC9FxB,EAAQO,OAAQ,EAChBY,EAASZ,MAAQiB,EAAIC,IAAA,GACtB,CAGH,SAASC,IAEA,OADPvB,EAAKI,MAAMA,MAAQJ,EAAKI,MAAMD,KAAKqB,OAC5B,IAAIC,SAAeC,IACxB3B,EAAQK,OACNL,EAAQK,MAAMuB,UAAUC,IAClBA,GACFV,EAAiBW,0BAA0B7B,EAAKI,OAAOgB,MAAMC,IAC1C,IAAbA,EAAInB,MACNF,EAAKI,MAAMD,KAAO,GAClBH,EAAKI,MAAMA,MAAQ,GACnB0B,EAAUC,QAAQ,CAChBjB,QAASrB,EAAE,kBACXuC,QAAQ,IAEFN,IACAT,KAERa,EAAUG,MAAM,CACdnB,QAASO,EAAIa,IACbF,QAAQ,GACT,GAEJ,GAEJ,GACJ,CArCHG,GAAU,KACAlB,GAAA,IA6CV,MAAMmB,EAAUxB,GACd,QACMyB,IAAiD,CACnD,CAAC/B,EAAe,CAAEgC,MAAO,IAAKC,SAAU9C,EAAE,kBAC1C,CAAC+C,EAAe,CAAEF,MAAO,IAAKC,SAAU9C,EAAE,kBAC1C,CAACgD,EAAe,CAAEH,MAAO,IAAKC,SAAU9C,EAAE,kBAC1C,CAACiD,EAAe,CAAEJ,MAAO,IAAKC,SAAU9C,EAAE,kBAC1C,CAACkD,EAAa,CAAEL,MAAO,IAAKC,SAAU9C,EAAE,oBAIxCmD,GAAcC,EAAS,CAC3B,CAAEC,MAAOxC,EAAegC,MAAO,IAAKC,SAAU9C,EAAE,iBAChD,CAAEqD,MAAON,EAAeF,MAAO,IAAKC,SAAU9C,EAAE,iBAChD,CAAEqD,MAAOL,EAAeH,MAAO,IAAKC,SAAU9C,EAAE,iBAChD,CAAEqD,MAAOJ,EAAeJ,MAAO,IAAKC,SAAU9C,EAAE,iBAChD,CAAEqD,MAAOH,EAAaL,MAAO,IAAKC,SAAU9C,EAAE,uZAtB7BsD,UACjB1C,EAAKD,MAAQ2C,EACb/C,EAAKI,MAAMC,KAAO0C,OACV9B,IAHV,IAAmB8B,49CAyBKC,QACf,IAAIvB,SAAkBC,IAC3BsB,EAAIC,eAAgB,EACpB/B,EACGgC,gCAAgC,CAC/BC,GAAIH,EAAIG,GACRC,SAA2B,MAAjBJ,EAAII,SAAmB,IAAM,MAExChC,MAAK,KACJ4B,EAAIC,eAAgB,EACpBnB,EAAUC,QAAQ,CAChBjB,QAAS,GAAoB,MAAjBkC,EAAII,SAAmB3D,EAAE,iBAAmBA,EAAE,oBAC1DuC,QAAQ,IAEHN,GAAQ,MAEhB2B,OAAM,KACLL,EAAIC,eAAgB,EACbvB,GAAQ,KAChB,IAnBP,IAAwBsB,oSAuBRA,QACP,IAAIvB,SAAkBC,IAE3B,MAAM4B,EAAI,CACRH,GAAIH,EAAIG,GACRlD,MAAON,EAAUM,MACjBE,KAAM6C,EAAI7C,KACVC,MAAO4C,EAAI7C,KACXE,KAAMA,EAAKD,OAEbc,EAAiBqC,oBAAoBD,GAAGlC,MAAMC,GAC3B,IAAbA,EAAInB,MACN4B,EAAUC,QAAQ,CAChBjB,QAASrB,EAAE,sBACXuC,QAAQ,IAEVgB,EAAIQ,QAAS,EACN9B,GAAQ,KAEfI,EAAUG,MAAM,CACdnB,QAASO,EAAIa,IACbF,QAAQ,IAEHN,GAAQ,KAElB,IAzBL,IAAgBsB"}