{"version": 3, "file": "HCheckList.vue_vue_type_script_setup_true_lang-CZz62mdd.js", "sources": ["../../src/layouts/ui-kit/HCheckList.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nwithDefaults(\r\n  defineProps<{\r\n    options: {\r\n      label?: string | number\r\n      icon?: string\r\n      value: string | number\r\n      disabled?: boolean\r\n    }[]\r\n    disabled?: boolean\r\n  }>(),\r\n  {\r\n    disabled: false,\r\n  },\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  change: [string | number | undefined]\r\n}>()\r\n\r\nconst value = defineModel<string | number>()\r\n\r\nwatch(value, (val) => {\r\n  emits('change', val)\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"inline-flex select-none items-center justify-center of-hidden rounded-md bg-stone-3 dark-bg-stone-7\">\r\n    <button v-for=\"option in options\" :key=\"option.value\" :disabled=\"disabled || option.disabled\" class=\"flex cursor-pointer items-center truncate border-size-0 bg-inherit px-2 py-1.5 text-sm disabled-cursor-not-allowed disabled-opacity-50 hover-not-disabled-bg-ui-primary hover-not-disabled-text-ui-text\" :class=\"{ 'text-ui-text bg-ui-primary': value === option.value }\" @click=\"value = option.value\">\r\n      <SvgIcon v-if=\"option.icon\" :name=\"option.icon\" />\r\n      <template v-else>\r\n        {{ option.label }}\r\n      </template>\r\n    </button>\r\n  </div>\r\n</template>\r\n"], "names": ["emits", "__emit", "value", "_useModel", "watch", "val"], "mappings": "+bAgBA,MAAMA,EAAQC,EAIRC,EAAQC,yBAERC,EAAAF,GAAQG,IACZL,EAAM,SAAUK,EAAG"}