<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽效果预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 40px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .drag-item {
            display: flex;
            align-items: center;
            padding: 16px 18px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 2px solid transparent;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            cursor: move;
        }
        
        .drag-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .drag-item:hover {
            background: linear-gradient(135deg, #f8fbff 0%, #f0f9ff 100%);
            border-color: rgba(64, 158, 255, 0.3);
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 32px rgba(64, 158, 255, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .drag-item:hover::before {
            opacity: 1;
        }
        
        .drag-handle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 40px;
            margin-right: 16px;
            cursor: grab;
        }
        
        .handle-dots {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 3px;
        }
        
        .dot {
            width: 4px;
            height: 4px;
            background-color: #c0c4cc;
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .drag-item:hover .dot {
            background-color: #409eff;
            transform: scale(1.2);
        }
        
        .item-number {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 32px;
            height: 32px;
            margin-right: 18px;
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border-radius: 50%;
            color: #ffffff;
            font-size: 14px;
            font-weight: 700;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
        }
        
        .item-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .item-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
        }
        
        .item-code {
            font-size: 13px;
            color: #909399;
            font-family: 'SF Mono', 'Monaco', monospace;
            background-color: rgba(0, 0, 0, 0.04);
            padding: 2px 6px;
            border-radius: 4px;
            display: inline-block;
            width: fit-content;
        }
        
        .drag-indicator {
            width: 24px;
            height: 24px;
            margin-left: 12px;
            color: #c0c4cc;
            opacity: 0;
            transform: translateX(10px);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .drag-item:hover .drag-indicator {
            opacity: 1;
            transform: translateX(0);
        }
        
        .note {
            margin-top: 30px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #409eff;
            color: #606266;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎨 美化后的拖拽效果预览</h1>
        
        <div class="drag-item">
            <div class="drag-handle">
                <div class="handle-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
            <div class="item-number">1</div>
            <div class="item-content">
                <span class="item-name">豪华大床房</span>
                <span class="item-code">DELUXE_KING</span>
            </div>
            <div class="drag-indicator">⋮⋮</div>
        </div>
        
        <div class="drag-item">
            <div class="drag-handle">
                <div class="handle-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
            <div class="item-number">2</div>
            <div class="item-content">
                <span class="item-name">商务套房</span>
                <span class="item-code">BUSINESS_SUITE</span>
            </div>
            <div class="drag-indicator">⋮⋮</div>
        </div>
        
        <div class="drag-item">
            <div class="drag-handle">
                <div class="handle-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
            <div class="item-number">3</div>
            <div class="item-content">
                <span class="item-name">标准双人房</span>
                <span class="item-code">STANDARD_TWIN</span>
            </div>
            <div class="drag-indicator">⋮⋮</div>
        </div>
        
        <div class="note">
            <strong>✨ 美化特点：</strong><br>
            • 🎯 六点式拖拽手柄，更直观的拖拽提示<br>
            • 🌈 渐变背景和悬停效果，视觉层次丰富<br>
            • 🎨 流畅的动画过渡，提升交互体验<br>
            • 💎 精致的阴影和圆角设计，现代化外观<br>
            • 🔢 醒目的序号标识，清晰的排序指示
        </div>
    </div>
</body>
</html>
