{"version": 3, "file": "createUser-D8F-9UKf.js", "sources": ["../../src/views/group/org/dept-user/components/DetailForm/createUser.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"username\": \"Account\",\r\n    \"password\": \"Password\",\r\n    \"nickname\": \"Name\",\r\n    \"mobile\": \"Mobile\",\r\n    \"email\": \"Email\",\r\n    \"position\": \"Position\",\r\n    \"role\": \"Roles\",\r\n    \"defaultMerchant\": \"Default Login Hotel\",\r\n    \"belongMerchant\": \"Member's Hotel\",\r\n    \"remark\": \"Remark\",\r\n    \"pleaseEnterUsername\": \"Please enter username\",\r\n    \"pleaseEnterPassword\": \"Please enter password\",\r\n    \"pleaseEnterNickname\": \"Please enter name\",\r\n    \"pleaseSelectRole\": \"Please select role\",\r\n    \"accountCreatedSuccess\": \"Successfully added\",\r\n    \"modifyMemberContactInfo\": \"Only modifies the member's contact information within the enterprise, does not affect the member's login mobile number\",\r\n    \"canSelectMultipleRoles\": \"After selecting multiple roles, the member will have all the permissions of these roles\",\r\n    \"selectUserLanguage\": \"The current user's language will be automatically switched to their preferred language after logging into the system.\",\r\n    \"accountConsistsOf\": \"Account consists of numbers and letters\",\r\n    \"passwordConsistsOf\": \"Password consists of numbers and letters\",\r\n    \"pleaseEnterMobile\": \"enter mobile(required for Chinese)\",\r\n    \"pleaseSelectPosition\": \"Please select position\",\r\n    \"canSelectMultipleRolesPlaceholder\": \"You can select multiple roles\",\r\n    \"pleaseSelectHotel\": \"Please select hotel\",\r\n    \"language\": \"Language\",\r\n    \"pleaseEnterEmail\": \"Please enter email\",\r\n    \"usernameMustBe18CharsOrNumbers\": \"Username must be 18 characters or numbers\",\r\n    \"passwordMustBe18CharsOrNumbers\": \"Password must be 18 characters or numbers\",\r\n    \"pleaseEnterValidEmail\": \"Please enter a valid email address\",\r\n    \"nameLength\": \"The account must be composed of letters or numbers, and should be 6 to 18 characters in length.\",\r\n    \"phoneVaild\": \"Please enter the correct phone number.\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"username\": \"账号\",\r\n    \"password\": \"密码\",\r\n    \"nickname\": \"姓名\",\r\n    \"mobile\": \"手机号码\",\r\n    \"email\": \"邮箱\",\r\n    \"position\": \"职位\",\r\n    \"role\": \"角色\",\r\n    \"defaultMerchant\": \"默认登录酒店\",\r\n    \"belongMerchant\": \"成员所属酒店\",\r\n    \"remark\": \"备注\",\r\n    \"pleaseEnterUsername\": \"请输入账号\",\r\n    \"pleaseEnterPassword\": \"请输入密码\",\r\n    \"pleaseEnterNickname\": \"请输入姓名\",\r\n    \"pleaseSelectRole\": \"请选择角色\",\r\n    \"accountCreatedSuccess\": \"新增成功\",\r\n    \"modifyMemberContactInfo\": \"修改的仅是成员在企业内的联系方式，不影响成员的登录手机号\",\r\n    \"selectUserLanguage\": \"当前用户所用语言，登录后系统根据语言自动切换\",\r\n    \"canSelectMultipleRoles\": \"选择多个角色后，成员拥有这些角色的所有权限\",\r\n    \"accountConsistsOf\": \"账号由数字、字母组成\",\r\n    \"passwordConsistsOf\": \"密码由数字、字母组成\",\r\n    \"pleaseEnterMobile\": \"请输入手机号（中国用户必填）\",\r\n    \"pleaseSelectPosition\": \"请选择职位\",\r\n    \"canSelectMultipleRolesPlaceholder\": \"可以选择多个角色\",\r\n    \"pleaseSelectHotel\": \"请选择酒店\",\r\n    \"language\": \"语言\",\r\n    \"pleaseEnterEmail\": \"请输入邮箱\",\r\n    \"usernameMustBe18CharsOrNumbers\": \"用户名必须是6到18位字母或数字\",\r\n    \"passwordMustBe18CharsOrNumbers\": \"密码必须是6到18位字母或数字\",\r\n    \"pleaseEnterValidEmail\": \"请输入有效的邮箱地址\",\r\n    \"nameLength\": \"账号必须为6到18位的字母或数字组成\",\r\n    \"phoneVaild\": \"请输入正确的手机号码\",\r\n  },\r\n  \"km\": {\r\n    \"username\": \"គណនី\",\r\n    \"password\": \"ពាក្យសម្ងាត់\",\r\n    \"nickname\": \"ឈ្មោះ\",\r\n    \"mobile\": \"លេខទូរស័ព្ទ\",\r\n    \"email\": \"អ៊ីមែល\",\r\n    \"position\": \"តំណែង\",\r\n    \"role\": \"តួនាទី\",\r\n    \"defaultMerchant\": \"សណ្ឋាគារចូលដំណើរការលំនាំដើម\",\r\n    \"belongMerchant\": \"សណ្ឋាគាររបស់សមាជិក\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"pleaseEnterUsername\": \"សូមបញ្ចូលគណនី\",\r\n    \"pleaseEnterPassword\": \"សូមបញ្ចូលពាក្យសម្ងាត់\",\r\n    \"pleaseEnterNickname\": \"សូមបញ្ចូលឈ្មោះ\",\r\n    \"pleaseSelectRole\": \"សូមជ្រើសរើសតួនាទី\",\r\n    \"accountCreatedSuccess\": \"បានបន្ថែមដោយជោគជ័យ\",\r\n    \"modifyMemberContactInfo\": \"កែប្រែតែវិធីសាកសពរបស់សមាជិកនៅក្នុងសហគ្រាស មិនប៉ះពាល់ដល់លេខទូរស័ព្ទចូលរបស់សមាជិក\",\r\n    \"canSelectMultipleRoles\": \"បន្ទាប់ពីជ្រើសរើសតួនាទីច្រើន សមាជិកនឹងមានសិទ្ធិទាំងអស់របស់តួនាទីទាំងនេះ\",\r\n    \"selectUserLanguage\": \"ភាសាដែលអ្នកប្រើប្រាស់បច្ចុប្បន្ននឹងត្រូវបានប្តូរដោយស្វ័យប្រវត្តិទៅតាមភាសាដែលពួកគេចូលចិត្តបន្ទាប់ពីចូលទៅក្នុងប្រព័ន្ធ\",\r\n    \"accountConsistsOf\": \"គណនីមានលេខ និងអក្សរ\",\r\n    \"passwordConsistsOf\": \"ពាក្យសម្ងាត់មានលេខ និងអក្សរ\",\r\n    \"pleaseEnterMobile\": \"សូមបញ្ចូលលេខទូរស័ព្ទ (ចាំបាច់សម្រាប់អ្នកប្រើប្រាស់ចិន)\",\r\n    \"pleaseSelectPosition\": \"សូមជ្រើសរើសតំណែង\",\r\n    \"canSelectMultipleRolesPlaceholder\": \"អ្នកអាចជ្រើសរើសតួនាទីច្រើន\",\r\n    \"pleaseSelectHotel\": \"សូមជ្រើសរើសសណ្ឋាគារ\",\r\n    \"language\": \"ភាសា\",\r\n    \"pleaseEnterEmail\": \"សូមបញ្ចូលអ៊ីមែល\",\r\n    \"usernameMustBe18CharsOrNumbers\": \"ឈ្មោះអ្នកប្រើប្រាស់ត្រូវតែមានអក្សរ ឬលេខចំនួន 6 ដល់ 18\",\r\n    \"passwordMustBe18CharsOrNumbers\": \"ពាក្យសម្ងាត់ត្រូវតែមានអក្សរ ឬលេខចំនួន 6 ដល់ 18\",\r\n    \"pleaseEnterValidEmail\": \"សូមបញ្ចូលអាសយដ្ឋានអ៊ីមែលត្រឹមត្រូវ\",\r\n    \"nameLength\": \"គណនីត្រូវតែមានអក្សរ ឬលេខចំនួន 6 ដល់ 18\",\r\n    \"phoneVaild\": \"សូមបញ្ចូលលេខទូរស័ព្ទត្រឹមត្រូវ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel, MerchantModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport dictDataApi from '@/api/modules/system/dict/dictData.api'\r\nimport merchantApi from '@/api/modules/system/merchant/merchant.api'\r\nimport userApi from '@/api/modules/system/user/user.api'\r\nimport roleUserApi from '@/api/modules/user.api'\r\nimport { DICT_TYPE_POST } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  userid: '',\r\n  deptId: '',\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 成员账号 */\r\n  userid: props.userid,\r\n  deptId: props.deptId,\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  username: '',\r\n  password: '',\r\n  nickname: '',\r\n  /** 在当前集团下的联系电话  而非账号表中的电话 */\r\n  mobile: '',\r\n  /** 拥有的角色代码列表 */\r\n  roleIds: [] as string[],\r\n  /** 用户所属门店 */\r\n  belongMerchant: '',\r\n  /** 默认登录门店 */\r\n  defaultMerchant: '',\r\n  /** 在当前集团下的职位 */\r\n  postIds: [],\r\n  /** 工号 */\r\n  // wkno: '',\r\n  // state: '1',\r\n  /** 联系邮箱 */\r\n  email: '',\r\n  /** 账号状态 -2：离职 -1：未激活 0:已暂停 1:正常 */\r\n  remark: '',\r\n  language: 'zh-cn',\r\n})\r\nconst roles = ref<{ id: string; name: string }[]>([])\r\nconst merchants = ref<MerchantModel[]>([])\r\nconst positions = ref<DictDataModel[]>([])\r\nconst formRules = ref<FormRules>({\r\n  username: [\r\n    { required: true, message: t('pleaseEnterUsername'), trigger: 'blur' },\r\n    { min: 6, max: 18, trigger: 'blur', message: t('nameLength') },\r\n    {\r\n      pattern: /^[a-z0-9]{6,18}$/i,\r\n      message: t('usernameMustBe18CharsOrNumbers'),\r\n      trigger: 'blur',\r\n    },\r\n  ],\r\n  password: [\r\n    { required: true, message: t('pleaseEnterPassword'), trigger: 'blur' },\r\n    {\r\n      pattern: /^[a-z0-9!@#$%^&*()]{6,18}$/i,\r\n      message: t('passwordMustBe18CharsOrNumbers'),\r\n      trigger: 'blur',\r\n    },\r\n  ],\r\n  email: [\r\n    {\r\n      pattern: /^[a-z0-9!@#$%^&*()]{6,18}$/i,\r\n      message: t('pleaseEnterValidEmail'),\r\n      trigger: 'blur',\r\n    },\r\n  ],\r\n  nickname: [{ required: true, message: t('pleaseEnterNickname'), trigger: 'blur' }],\r\n  roleIds: [{ required: true, message: t('pleaseSelectRole'), trigger: 'blur' }],\r\n  mobile: [{ pattern: /^1[3-9]\\d{9}$/, message: t('phoneVaild'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  getPositions()\r\n  getRoles()\r\n  getMerchants()\r\n})\r\n\r\nfunction getPositions() {\r\n  dictDataApi.list({ dictType: DICT_TYPE_POST }).then((res: any) => {\r\n    positions.value = res.data\r\n  })\r\n}\r\n\r\nfunction getRoles() {\r\n  roleUserApi\r\n    .getRoleList({\r\n      gcode: userStore.gcode,\r\n      status: '0',\r\n      pageSize: 999,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        roles.value = res.data.list\r\n      }\r\n    })\r\n}\r\n\r\nfunction getMerchants() {\r\n  merchantApi\r\n    .getDepartmentSimpleList({\r\n      gcode: userStore.gcode,\r\n      deptId: props.deptId,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        merchants.value = res.data\r\n      }\r\n    })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            userApi.createUser({ ...form.value, deptId: props.deptId }).then(() => {\r\n              ElMessage.success({\r\n                message: t('accountCreatedSuccess'),\r\n                center: true,\r\n              })\r\n              resolve()\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" label-position=\"top\" :rules=\"formRules\" label-width=\"120px\" label-suffix=\"\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('username')\" prop=\"username\">\r\n            <el-input v-model=\"form.username\" :placeholder=\"t('accountConsistsOf')\" maxlength=\"18\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('password')\" prop=\"password\">\r\n            <el-input v-model=\"form.password\" :placeholder=\"t('passwordConsistsOf')\" maxlength=\"18\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('nickname')\" prop=\"nickname\">\r\n            <el-input v-model=\"form.nickname\" :placeholder=\"t('pleaseEnterNickname')\" maxlength=\"32\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('mobile')\" prop=\"mobile\">\r\n            <el-input v-model=\"form.mobile\" :placeholder=\"t('pleaseEnterMobile')\" maxlength=\"11\">\r\n              <template #prepend> +86 </template>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('email')\" prop=\"email\">\r\n            <el-input v-model=\"form.email\" :placeholder=\"t('pleaseEnterEmail')\" maxlength=\"64\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('position')\">\r\n            <el-select v-model=\"form.postIds\" multiple :placeholder=\"t('pleaseSelectPosition')\" style=\"width: 240px\">\r\n              <el-option v-for=\"item in positions\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('role')\" prop=\"roleIds\">\r\n            <el-select v-model=\"form.roleIds\" multiple collapse-tags collapse-tags-tooltip :placeholder=\"t('canSelectMultipleRolesPlaceholder')\" style=\"width: 240px\">\r\n              <el-option v-for=\"item in roles\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n            </el-select>\r\n            <div class=\"el-form-item-msg\">\r\n              {{ t('canSelectMultipleRoles') }}\r\n            </div>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('language')\" prop=\"language\">\r\n            <el-radio-group v-model=\"form.language\">\r\n              <el-radio-button label=\"中文\" value=\"zh-cn\" />\r\n              <el-radio-button label=\"English(英文)\" value=\"en\" />\r\n              <el-radio-button label=\"ភាសាខ្មែរ(柬埔寨语)\" value=\"km\" />\r\n            </el-radio-group>\r\n            <div class=\"el-form-item-msg\">\r\n              {{ t('selectUserLanguage') }}\r\n            </div>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('defaultMerchant')\" prop=\"defaultMerchant\">\r\n            <el-select v-model=\"form.defaultMerchant\" :placeholder=\"t('pleaseSelectHotel')\" style=\"width: 240px\">\r\n              <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.name\" :value=\"item.hcode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('belongMerchant')\" prop=\"belongMerchant\">\r\n            <el-select v-model=\"form.belongMerchant\" :placeholder=\"t('pleaseSelectHotel')\" style=\"width: 240px\">\r\n              <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.name\" :value=\"item.hcode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('remark')\">\r\n            <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" maxlength=\"250\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "userid", "deptId", "gcode", "username", "password", "nickname", "mobile", "roleIds", "belongMerchant", "defaultMerchant", "postIds", "email", "remark", "language", "roles", "merchants", "positions", "formRules", "required", "message", "trigger", "min", "max", "pattern", "onMounted", "dict<PERSON>ata<PERSON><PERSON>", "list", "dictType", "DICT_TYPE_POST", "then", "res", "value", "data", "roleUserApi", "getRoleList", "status", "pageSize", "code", "merchantApi", "getDepartmentSimpleList", "__expose", "submit", "Promise", "resolve", "validate", "valid", "userApi", "createUser", "ElMessage", "success", "center"], "mappings": "4iCAmHA,MAAMA,EAAQC,GAKRC,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,OAAQV,EAAMU,OACdC,OAAQX,EAAMW,OAEdC,MAAOR,EAAUQ,MACjBC,SAAU,GACVC,SAAU,GACVC,SAAU,GAEVC,OAAQ,GAERC,QAAS,GAETC,eAAgB,GAEhBC,gBAAiB,GAEjBC,QAAS,GAKTC,MAAO,GAEPC,OAAQ,GACRC,SAAU,UAENC,EAAQjB,EAAoC,IAC5CkB,EAAYlB,EAAqB,IACjCmB,EAAYnB,EAAqB,IACjCoB,EAAYpB,EAAe,CAC/BM,SAAU,CACR,CAAEe,UAAU,EAAMC,QAAS3B,EAAE,uBAAwB4B,QAAS,QAC9D,CAAEC,IAAK,EAAGC,IAAK,GAAIF,QAAS,OAAQD,QAAS3B,EAAE,eAC/C,CACE+B,QAAS,oBACTJ,QAAS3B,EAAE,kCACX4B,QAAS,SAGbhB,SAAU,CACR,CAAEc,UAAU,EAAMC,QAAS3B,EAAE,uBAAwB4B,QAAS,QAC9D,CACEG,QAAS,8BACTJ,QAAS3B,EAAE,kCACX4B,QAAS,SAGbT,MAAO,CACL,CACEY,QAAS,8BACTJ,QAAS3B,EAAE,yBACX4B,QAAS,SAGbf,SAAU,CAAC,CAAEa,UAAU,EAAMC,QAAS3B,EAAE,uBAAwB4B,QAAS,SACzEb,QAAS,CAAC,CAAEW,UAAU,EAAMC,QAAS3B,EAAE,oBAAqB4B,QAAS,SACrEd,OAAQ,CAAC,CAAEiB,QAAS,gBAAiBJ,QAAS3B,EAAE,cAAe4B,QAAS,kBAG1EI,GAAU,KAOIC,EAAAC,KAAK,CAAEC,SAAUC,IAAkBC,MAAMC,IACnDd,EAAUe,MAAQD,EAAIE,IAAA,IAKxBC,EACGC,YAAY,CACXhC,MAAOR,EAAUQ,MACjBiC,OAAQ,IACRC,SAAU,MAEXP,MAAMC,IACY,IAAbA,EAAIO,OACAvB,EAAAiB,MAAQD,EAAIE,KAAKN,KAAA,IAM7BY,EACGC,wBAAwB,CACvBrC,MAAOR,EAAUQ,MACjBD,OAAQX,EAAMW,SAEf4B,MAAMC,IACY,IAAbA,EAAIO,OACNtB,EAAUgB,MAAQD,EAAIE,KAAA,GA/Bf,IAoCFQ,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB7C,EAAQiC,OACNjC,EAAQiC,MAAMa,UAAUC,IAClBA,GACMC,EAAAC,WAAW,IAAKhD,EAAKgC,MAAO9B,OAAQX,EAAMW,SAAU4B,MAAK,KAC/DmB,EAAUC,QAAQ,CAChB9B,QAAS3B,EAAE,yBACX0D,QAAQ,IAEFP,GAAA,GACT,GAEJ"}