{"version": 3, "file": "taxConfig.api-CbiiOWBT.js", "sources": ["../../src/api/modules/pms/config/taxConfig.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n/** 税率配置 */\r\nexport default {\r\n\r\n  // 活动门店税率列表\r\n  list: (data: any) => api.get('/admin-api/pms/tax-config/list', { params: data }),\r\n\r\n  // 税率新增\r\n  create: (data: any) => api.post('/admin-api/pms/tax-config/create', data),\r\n\r\n  // 税率详情\r\n  detail: (data: any) => api.get('/admin-api/pms/tax-config/get', { params: data }),\r\n\r\n  // 编辑税率\r\n  edit: (data: any) => api.put('/admin-api/pms/tax-config/update', data),\r\n\r\n  // 删除税率\r\n  delete: (data: any) => api.delete('/admin-api/pms/tax-config/delete', { params: data }),\r\n}\r\n"], "names": ["taxConfigApi", "list", "data", "api", "get", "params", "create", "post", "detail", "edit", "put", "delete"], "mappings": "mCAGA,MAAeA,EAAA,CAGbC,KAAOC,GAAcC,EAAIC,IAAI,iCAAkC,CAAEC,OAAQH,IAGzEI,OAASJ,GAAcC,EAAII,KAAK,mCAAoCL,GAGpEM,OAASN,GAAcC,EAAIC,IAAI,gCAAiC,CAAEC,OAAQH,IAG1EO,KAAOP,GAAcC,EAAIO,IAAI,mCAAoCR,GAGjES,OAAST,GAAcC,EAAIQ,OAAO,mCAAoC,CAAEN,OAAQH"}