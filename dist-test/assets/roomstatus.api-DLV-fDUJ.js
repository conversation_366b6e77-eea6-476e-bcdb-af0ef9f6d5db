import{a as t}from"./index-CkEhI1Zk.js";const a="admin-api/pms/room-status",s={getRoomStatus:s=>t.get(`${a}/list`,{params:s}),getFutureRoomStatus:s=>t.get(`${a}/future-room-status`,{params:s}),getOCC:s=>t.get(`${a}/get-occ`,{params:s}),getOtaApiList:a=>t.get("admin-api/ota-sync/ota-api/list",{params:a}),getExistOtaOrder:a=>t.get("admin-api/pms/order/exist-ota-order",{params:a})};export{s as r};
//# sourceMappingURL=roomstatus.api-DLV-fDUJ.js.map
