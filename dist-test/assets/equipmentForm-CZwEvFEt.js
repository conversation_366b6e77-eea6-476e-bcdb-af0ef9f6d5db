import{d as e,aj as t,ai as a,B as l,b as s,y as r,cD as i,u as d,o,e as c,w as n,f as p,h as m,Y as u,aq as b,c as v,F as y,ag as f,R as V,bw as h,bx as D,i as _,aR as k,j as T,k as N,m as P,aS as C,b1 as g,q as j,b2 as x,b5 as S,x as U,ay as B,aT as w}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                  *//* empty css               *//* empty css                  *//* empty css               *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                     *//* empty css                  */import{d as R}from"./device.api-BsgckoMw.js";import{_ as E}from"./_plugin-vue_export-helper-BCo6x5W8.js";const q=e({__name:"equipmentForm",props:{modelValue:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},infoData:{default:{}}},emits:["update:modelValue","success"],setup(e,{emit:E}){const q=e,M=E,{t:z}=t(),A=a(),F=l({get:()=>q.modelValue,set(e){M("update:modelValue",e)}}),Y=s(),G=s({gcode:A.gcode,hcode:A.hcode,deviceType:void 0,port:"",brandName:"",deviceVerName:"",cardDispenserType:"",version:"",state:"1",remark:"",conf:[{parameterName:"",parameterCode:""}]}),I=s({deviceType:[{required:!0,message:z("selectDevice"),trigger:"change"}],brandName:[{required:!0,message:z("enterDeviceBrand"),trigger:"blur"}]}),O=s([]);const H=s(z("addDevice")),J=s(!1);function K(){Y.value&&Y.value.validate((e=>{e&&(q.isEdit?R.deviceUpdate({...G.value,id:q.infoData.id,deviceCode:q.infoData.deviceCode}).then((e=>{0===e.code&&(k.success(z("editSuccess")),L(),M("success"))})):R.deviceCreate(G.value).then((e=>{0===e.code&&(k.success(z("addSuccess")),L(),M("success"))})))}))}function L(){F.value=!1,G.value={gcode:A.gcode,hcode:A.hcode,deviceType:void 0,brandName:"",deviceVerName:"",cardDispenserType:"",version:"",port:"",state:"1",remark:"",conf:[{parameterName:"",parameterCode:""}]}}function Q(){G.value.conf.push({parameterName:"",parameterCode:""})}return r((()=>{q.isEdit?(H.value=z("editDevice"),i(G.value,q.infoData)):H.value=z("addDevice"),async function(){await R.getDeviceType().then((e=>{O.value=e.data}))}()})),(e,t)=>{const a=T,l=N,s=P,r=C,i=g,k=j,R=x,E=S,q=U,M=B,A=w;return d(F)?(o(),c(M,{key:0,modelValue:d(F),"onUpdate:modelValue":t[8]||(t[8]=e=>_(F)?F.value=e:null),width:"700px",title:d(H),"close-on-click-modal":!1,"append-to-body":"",modal:!0,"destroy-on-close":""},{footer:n((()=>[p(k,{size:"large",onClick:L},{default:n((()=>[m(u(d(z)("cancel")),1)])),_:1}),p(k,{type:"primary",size:"large",onClick:K},{default:n((()=>[m(u(d(z)("save")),1)])),_:1})])),default:n((()=>[b((o(),v("div",null,[p(q,{ref_key:"formRef",ref:Y,model:d(G),rules:d(I),"label-width":"100px","label-suffix":"："},{default:n((()=>[p(s,{label:d(z)("deviceType"),prop:"deviceType"},{default:n((()=>[p(l,{modelValue:d(G).deviceType,"onUpdate:modelValue":t[0]||(t[0]=e=>d(G).deviceType=e),placeholder:d(z)("selectDevice"),class:"w-80"},{default:n((()=>[(o(!0),v(y,null,f(d(O),(e=>(o(),c(a,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),p(s,{label:d(z)("deviceBrand"),prop:"brandName"},{default:n((()=>[p(r,{modelValue:d(G).brandName,"onUpdate:modelValue":t[1]||(t[1]=e=>d(G).brandName=e),placeholder:d(z)("enterDeviceBrand")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),p(s,{label:d(z)("model"),prop:"deviceVerName"},{default:n((()=>[p(r,{modelValue:d(G).deviceVerName,"onUpdate:modelValue":t[2]||(t[2]=e=>d(G).deviceVerName=e),placeholder:d(z)("enterModel")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),"door_lock"===d(G).deviceType?(o(),c(s,{key:0,label:d(z)("cardDispenserType"),prop:"cardDispenserType"},{default:n((()=>[p(r,{modelValue:d(G).cardDispenserType,"onUpdate:modelValue":t[3]||(t[3]=e=>d(G).cardDispenserType=e),placeholder:d(z)("enterCardDispenserType")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])):V("",!0),p(s,{label:d(z)("version"),prop:"version"},{default:n((()=>[p(r,{modelValue:d(G).version,"onUpdate:modelValue":t[4]||(t[4]=e=>d(G).version=e),placeholder:d(z)("enterVersion")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),p(s,{label:d(z)("port"),prop:"port"},{default:n((()=>[p(r,{modelValue:d(G).port,"onUpdate:modelValue":t[5]||(t[5]=e=>d(G).port=e),placeholder:d(z)("enterPort")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),p(s,{label:d(z)("customParams")},{default:n((()=>[(o(!0),v(y,null,f(d(G).conf,((e,a)=>(o(),v("div",{key:a,class:"mb-2",style:{display:"flex","align-items":"center"}},[p(R,null,{default:n((()=>[p(i,{md:10},{default:n((()=>[p(s,{label:d(z)("paramName"),"label-width":"90px"},{default:n((()=>[p(r,{modelValue:e.parameterName,"onUpdate:modelValue":t=>e.parameterName=t,placeholder:d(z)("enterParamName")},null,8,["modelValue","onUpdate:modelValue","placeholder"])])),_:2},1032,["label"])])),_:2},1024),p(i,{md:10},{default:n((()=>[p(s,{label:d(z)("paramCode"),"label-width":"90px"},{default:n((()=>[p(r,{modelValue:e.parameterCode,"onUpdate:modelValue":t=>e.parameterCode=t,placeholder:d(z)("enterParamCode")},null,8,["modelValue","onUpdate:modelValue","placeholder"])])),_:2},1032,["label"])])),_:2},1024),p(i,{md:4},{default:n((()=>[t[9]||(t[9]=m("     ")),a===d(G).conf.length-1?(o(),c(k,{key:0,icon:d(h),circle:"",type:"primary",onClick:Q},null,8,["icon"])):V("",!0),d(G).conf.length>1?(o(),c(k,{key:1,icon:d(D),circle:"",type:"danger",onClick:e=>{return t=a,void G.value.conf.splice(t,1);var t}},null,8,["icon","onClick"])):V("",!0)])),_:2},1024)])),_:2},1024)])))),128))])),_:1},8,["label"]),p(s,{label:d(z)("activeStatus"),prop:"state"},{default:n((()=>[p(E,{modelValue:d(G).state,"onUpdate:modelValue":t[6]||(t[6]=e=>d(G).state=e),"active-text":d(z)("yes"),"active-value":"1","inactive-text":d(z)("no"),"inactive-value":"0","inline-prompt":""},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"]),p(s,{label:d(z)("remark")},{default:n((()=>[p(r,{modelValue:d(G).remark,"onUpdate:modelValue":t[7]||(t[7]=e=>d(G).remark=e),maxlength:"250",rows:4,type:"textarea",placeholder:d(z)("enterRemark")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1},8,["model","rules"])])),[[A,d(J)]])])),_:1},8,["modelValue","title"])):V("",!0)}}});function M(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{addDevice:{t:0,b:{t:2,i:[{t:3}],s:"Add Device"}},editDevice:{t:0,b:{t:2,i:[{t:3}],s:"Edit Device"}},deviceType:{t:0,b:{t:2,i:[{t:3}],s:"Type"}},selectDevice:{t:0,b:{t:2,i:[{t:3}],s:"Please select a device"}},deviceBrand:{t:0,b:{t:2,i:[{t:3}],s:"Brand"}},enterDeviceBrand:{t:0,b:{t:2,i:[{t:3}],s:"Please enter device brand"}},model:{t:0,b:{t:2,i:[{t:3}],s:"Model"}},enterModel:{t:0,b:{t:2,i:[{t:3}],s:"Please enter model"}},cardDispenserType:{t:0,b:{t:2,i:[{t:3}],s:"Card Type"}},enterCardDispenserType:{t:0,b:{t:2,i:[{t:3}],s:"Please enter card dispenser type"}},version:{t:0,b:{t:2,i:[{t:3}],s:"Version"}},enterVersion:{t:0,b:{t:2,i:[{t:3}],s:"Please enter version"}},port:{t:0,b:{t:2,i:[{t:3}],s:"Port"}},enterPort:{t:0,b:{t:2,i:[{t:3}],s:"Please enter port"}},customParams:{t:0,b:{t:2,i:[{t:3}],s:"Parameters"}},paramName:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},enterParamName:{t:0,b:{t:2,i:[{t:3}],s:"Enter Param Name"}},enterParamCode:{t:0,b:{t:2,i:[{t:3}],s:"Enter Param Code"}},activeStatus:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"Yes"}},no:{t:0,b:{t:2,i:[{t:3}],s:"No"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},enterRemark:{t:0,b:{t:2,i:[{t:3}],s:"Please enter remark"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Added successfully"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Edited successfully"}}},"zh-cn":{addDevice:{t:0,b:{t:2,i:[{t:3}],s:"新增设备"}},editDevice:{t:0,b:{t:2,i:[{t:3}],s:"修改设备"}},deviceType:{t:0,b:{t:2,i:[{t:3}],s:"设备类型"}},selectDevice:{t:0,b:{t:2,i:[{t:3}],s:"请选择设备"}},deviceBrand:{t:0,b:{t:2,i:[{t:3}],s:"设备品牌"}},enterDeviceBrand:{t:0,b:{t:2,i:[{t:3}],s:"请输入设备品牌"}},model:{t:0,b:{t:2,i:[{t:3}],s:"型号"}},enterModel:{t:0,b:{t:2,i:[{t:3}],s:"请输入型号"}},cardDispenserType:{t:0,b:{t:2,i:[{t:3}],s:"发卡器类型"}},enterCardDispenserType:{t:0,b:{t:2,i:[{t:3}],s:"请输入发卡器类型"}},version:{t:0,b:{t:2,i:[{t:3}],s:"版本"}},enterVersion:{t:0,b:{t:2,i:[{t:3}],s:"请输入版本"}},port:{t:0,b:{t:2,i:[{t:3}],s:"端口"}},enterPort:{t:0,b:{t:2,i:[{t:3}],s:"请输入端口"}},customParams:{t:0,b:{t:2,i:[{t:3}],s:"自定义参数"}},paramName:{t:0,b:{t:2,i:[{t:3}],s:"参数名称"}},paramCode:{t:0,b:{t:2,i:[{t:3}],s:"参数代码"}},enterParamName:{t:0,b:{t:2,i:[{t:3}],s:"请输入参数名称"}},enterParamCode:{t:0,b:{t:2,i:[{t:3}],s:"请输入参数代码"}},activeStatus:{t:0,b:{t:2,i:[{t:3}],s:"有效标识"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"是"}},no:{t:0,b:{t:2,i:[{t:3}],s:"否"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},enterRemark:{t:0,b:{t:2,i:[{t:3}],s:"请输入备注"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},save:{t:0,b:{t:2,i:[{t:3}],s:"保存"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"新增成功"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"修改成功"}}},km:{addDevice:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែមឧបករណ៍"}},editDevice:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួលឧបករណ៍"}},deviceType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទឧបករណ៍"}},selectDevice:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឧបករណ៍"}},deviceBrand:{t:0,b:{t:2,i:[{t:3}],s:"ម៉ាក"}},enterDeviceBrand:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលម៉ាកឧបករណ៍"}},model:{t:0,b:{t:2,i:[{t:3}],s:"ម៉ូដែល"}},enterModel:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលម៉ូដែល"}},cardDispenserType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទអ្នកចែកកាត"}},enterCardDispenserType:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលប្រភេទអ្នកចែកកាត"}},version:{t:0,b:{t:2,i:[{t:3}],s:"កំណែ"}},enterVersion:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលកំណែ"}},port:{t:0,b:{t:2,i:[{t:3}],s:"ច្រក"}},enterPort:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលច្រក"}},customParams:{t:0,b:{t:2,i:[{t:3}],s:"ប៉ារ៉ាម៉ែត្រផ្ទាល់ខ្លួន"}},paramName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះប៉ារ៉ាម៉ែត្រ"}},enterParamName:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចូលឈ្មោះប៉ារ៉ាម៉ែត្រ"}},enterParamCode:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចូលកូដប៉ារ៉ាម៉ែត្រ"}},activeStatus:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"បាទ/ចាស"}},no:{t:0,b:{t:2,i:[{t:3}],s:"ទេ"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}},enterRemark:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលចំណាំ"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},save:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានបន្ថែមដោយជោគជ័យ"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានកែប្រែដោយជោគជ័យ"}}}}})}M(q);const z=E(q,[["__scopeId","data-v-a187c88e"]]);export{z as default};
//# sourceMappingURL=equipmentForm-CZwEvFEt.js.map
