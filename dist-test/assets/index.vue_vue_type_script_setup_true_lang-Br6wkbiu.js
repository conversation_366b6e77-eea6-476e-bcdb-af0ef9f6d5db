import{d as e,a3 as t,o as s,c as a,g as n,ab as l,h as o,Y as i,u as c,R as d}from"./index-CkEhI1Zk.js";const r={class:"page-header mb-5 flex flex-wrap items-center justify-between gap-5 bg-[var(--g-container-bg)] px-5 py-4 transition-background-color-300"},x={class:"main flex-[1_1_70%]"},m={class:"text-2xl"},p={class:"mt-2 text-sm text-stone-5 empty-hidden"},f={key:0,class:"ml-a flex-none"},g=e({name:"PageHeader",__name:"index",props:{title:{},content:{}},setup(e){const g=t();return(e,t)=>(s(),a("div",r,[n("div",x,[n("div",m,[l(e.$slots,"title",{},(()=>[o(i(e.title),1)]))]),n("div",p,[l(e.$slots,"content",{},(()=>[o(i(e.content),1)]))])]),c(g).default?(s(),a("div",f,[l(e.$slots,"default")])):d("",!0)]))}});export{g as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-Br6wkbiu.js.map
