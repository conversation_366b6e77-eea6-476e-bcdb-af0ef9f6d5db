{"version": 3, "file": "updatebaseprice-CA8prk6J.js", "sources": ["../../src/views/sell/price/price-calendar/components/updatebaseprice.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"batchModifyBasePrice\": \"Batch Modify Base Price\",\r\n    \"channel\": \"Channel\",\r\n    \"allChannels\": \"All Channels\",\r\n    \"storeRoomType\": \"Hotel Room Type\",\r\n    \"effectDate\": \"Effect Date\",\r\n    \"immediateEffect\": \"Immediate Effect\",\r\n    \"notAffectAdjustedPrice\": \"Does not affect adjusted price\",\r\n    \"currentBasePrice\": \"Current Base Price\",\r\n    \"modifiedBasePrice\": \"Modified Base Price\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"saveSuccess\": \"Save successful\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"batchModifyBasePrice\": \"批量修改门市价\",\r\n    \"channel\": \"渠道\",\r\n    \"allChannels\": \"全部渠道\",\r\n    \"storeRoomType\": \"门店房型\",\r\n    \"effectDate\": \"生效时间\",\r\n    \"immediateEffect\": \"即刻生效\",\r\n    \"notAffectAdjustedPrice\": \"不影响调整过的售价\",\r\n    \"currentBasePrice\": \"现门市价\",\r\n    \"modifiedBasePrice\": \"改后门市价\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"saveSuccess\": \"保存成功\"\r\n  },\r\n  \"km\": {\r\n    \"batchModifyBasePrice\": \"កែប្រែតម្លៃមូលដ្ឋានជាក្រុម\",\r\n    \"channel\": \"ឆានែល\",\r\n    \"allChannels\": \"ឆានែលទាំងអស់\",\r\n    \"storeRoomType\": \"ប្រភេទបន្ទប់ហាង\",\r\n    \"effectDate\": \"ពេលវេលាដែលមានប្រសិទ្ធិភាព\",\r\n    \"immediateEffect\": \"មានប្រសិទ្ធិភាពភ្លាមៗ\",\r\n    \"notAffectAdjustedPrice\": \"មិនប៉ះពាល់ដល់តម្លៃដែលបានកែប្រែ\",\r\n    \"currentBasePrice\": \"តម្លៃមូលដ្ឋានបច្ចុប្បន្ន\",\r\n    \"modifiedBasePrice\": \"តម្លៃមូលដ្ឋានបន្ទាប់ពីកែប្រែ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"saveSuccess\": \"រក្សាទុកជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\n\r\nimport { priceCalendarApi, rtApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\nimport 'splitpanes/dist/splitpanes.css'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 是否立即生效 0：否 1：是 */\r\n  immediateEffect: '1',\r\n  /** 生效日期 */\r\n  effectDate: '',\r\n  /** 门店房型列表 */\r\n  merchantRts: [] as string[],\r\n  /** 集团房型列表 */\r\n  groupMts: [] as string[],\r\n})\r\nconst formRules = ref<FormRules>({})\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst rts = ref<{ rtCode: string; rtName: string }[]>([])\r\nconst rtBasePrices = ref<{ rtCode: string; rtName: string; basePrice: number }[]>([])\r\n\r\nonMounted(() => {\r\n  getRts()\r\n  // getInfo()\r\n})\r\n\r\nconst previousSelections = ref([])\r\nconst addList = ref<{ rtCode: string; rtName: string; basePrice: number }[]>([])\r\nfunction getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  rtApi.getBasePrice(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n      addList.value = res.data\r\n      rtBasePrices.value = res.data\r\n      rtBasePrices.value.forEach((ls) => {\r\n        ls.updatePrice = null\r\n        form.value.merchantRts.push(ls.rtCode)\r\n      })\r\n      previousSelections.value = form.value.merchantRts\r\n    }\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  const submitList = addList.value.filter((item) => item.updatePrice !== null && item.updatePrice !== 0)\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    rtPriceList: submitList,\r\n  }\r\n  priceCalendarApi.updateBasePrice(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success({\r\n        message: t('saveSuccess'),\r\n        center: true,\r\n      })\r\n      onCancel()\r\n      emits('success')\r\n    } else {\r\n      ElMessage.error({\r\n        message: res.msg,\r\n        center: true,\r\n      })\r\n    }\r\n  })\r\n}\r\n\r\n// function getInfo() {\r\n//   loading.value = true\r\n//   api.getRtBasePrices({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {\r\n//     loading.value = false\r\n//     rtBasePrices.value = res.data\r\n//   })\r\n// }\r\n\r\n// function onSubmit() {\r\n//   loading.value = true\r\n//   api.edit(form.value).then((res: any) => {\r\n//     loading.value = false\r\n//     ElMessage({\r\n//       type: 'success',\r\n//       message: '修改成功',\r\n//     })\r\n//     formRef.value?.resetFields()\r\n//   })\r\n// }\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction formatDay(date: string) {\r\n  let x = ''\r\n  if (date) {\r\n    x = `${dayjs(date).format('YYYY-MM-DD')}开始`\r\n  }\r\n  return x\r\n}\r\n\r\nfunction handleChange(value: any) {\r\n  loading.value = true\r\n  const added = value.filter((x: any) => !previousSelections.value.includes(x))\r\n  const removed = previousSelections.value.filter((x) => !value.includes(x))\r\n  if (added.length) {\r\n    const filterList = addList.value.filter((item) => item.rtCode === added[0])\r\n    rtBasePrices.value.push(filterList[0])\r\n    loading.value = false\r\n  }\r\n  if (removed.length) {\r\n    rtBasePrices.value = rtBasePrices.value.filter((item) => item.rtCode !== removed[0])\r\n    loading.value = false\r\n  }\r\n  previousSelections.value = value\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" width=\"1000px\" :title=\"t('batchModifyBasePrice')\" :close-on-click-modal=\"false\" append-to-body :modal=\"true\" destroy-on-close>\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"160px\" label-position=\"right\" label-suffix=\"：\">\r\n        <el-card shadow=\"never\" style=\"margin-bottom: 10px\">\r\n          <el-form-item :label=\"t('channel')\">\r\n            {{ t('allChannels') }}\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('storeRoomType')\">\r\n            <el-select v-model=\"form.merchantRts\" multiple @change=\"handleChange\">\r\n              <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('effectDate')\" style=\"margin-bottom: 0\">\r\n            <el-radio-group v-model=\"form.immediateEffect\">\r\n              <el-radio value=\"1\">\r\n                {{ t('immediateEffect') }}\r\n              </el-radio>\r\n              <!--                  <el-radio label=\"0\"> -->\r\n              <!--                    <el-date-picker v-model=\"form.effectDate\" :disabled=\"form.immediateEffect === '1'\" type=\"date\" -->\r\n              <!--                      placeholder=\"请选择日期\" style=\"width: 150px;\" />开始生效 -->\r\n              <!--                  </el-radio> -->\r\n            </el-radio-group>\r\n            <div class=\"el-form-item-msg\">\r\n              {{ t('notAffectAdjustedPrice') }}\r\n            </div>\r\n          </el-form-item>\r\n        </el-card>\r\n        <el-table v-loading=\"loading\" :data=\"rtBasePrices\" border max-height=\"300px\" style=\"margin-bottom: 10px\">\r\n          <el-table-column prop=\"rtName\" :label=\"t('storeRoomType')\" />\r\n          <el-table-column :label=\"t('currentBasePrice')\">\r\n            <template #default=\"{ row }\"> ￥{{ row.basePrice }} </template>\r\n          </el-table-column>\r\n          <el-table-column>\r\n            <template #header>\r\n              <div>{{ t('modifiedBasePrice') }}</div>\r\n              <div v-if=\"form.immediateEffect === '0'\">\r\n                {{ formatDay(form.effectDate) }}\r\n              </div>\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <el-input-number v-model=\"row.updatePrice\" :min=\"0\" :precision=\"2\" controls-position=\"right\" style=\"width: 120px; margin-right: 8px\" />\r\n              <!--                  <el-input  v-model=\"row.updataPrice\" :formatter=\"(value: any) => `￥ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\" -->\r\n              <!--                    :parser=\"(value: string) => value.replace(/\\￥\\s?|(,*)/g, '')\" /> -->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-form>\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          {{ t('save') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.splitpanes.default-theme .splitpanes__pane {\r\n  background-color: #fff;\r\n}\r\n\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "t", "useI18n", "loading", "ref", "formRef", "form", "immediateEffect", "effectDate", "merchantRts", "groupMts", "formRules", "myVisible", "computed", "get", "modelValue", "set", "val", "rts", "rtBasePrices", "onMounted", "params", "gcode", "hcode", "rtApi", "getBasePrice", "then", "res", "code", "value", "data", "addList", "for<PERSON>ach", "ls", "updatePrice", "push", "rtCode", "previousSelections", "getRts", "onSubmit", "submitList", "filter", "item", "rtPriceList", "priceCalendarApi", "updateBasePrice", "ElMessage", "success", "message", "center", "onCancel", "error", "msg", "formatDay", "date", "x", "dayjs", "format", "handleChange", "added", "includes", "removed", "length", "filterList"], "mappings": "utCAuDA,MAAMA,EAAQC,EAQRC,EAAQC,EAIRC,EAAYC,KACZC,EAAEA,GAAMC,IAERC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,gBAAiB,IAEjBC,WAAY,GAEZC,YAAa,GAEbC,SAAU,KAENC,EAAYP,EAAe,IAC3BQ,EAAYC,EAAS,CACzBC,IAAM,IACGnB,EAAMoB,WAEf,GAAAC,CAAIC,GACFpB,EAAM,oBAAqBoB,EAAG,IAG5BC,EAAMd,EAA0C,IAChDe,EAAef,EAA6D,IAElFgB,GAAU,MAOV,WACE,MAAMC,EAAS,CACbC,MAAOvB,EAAUuB,MACjBC,MAAOxB,EAAUwB,OAEnBC,EAAMC,aAAaJ,GAAQK,MAAMC,IACd,IAAbA,EAAIC,OACNV,EAAIW,MAAQF,EAAIG,KAChBC,EAAQF,MAAQF,EAAIG,KACpBX,EAAaU,MAAQF,EAAIG,KACZX,EAAAU,MAAMG,SAASC,IAC1BA,EAAGC,YAAc,KACjB5B,EAAKuB,MAAMpB,YAAY0B,KAAKF,EAAGG,OAAM,IAEpBC,EAAAR,MAAQvB,EAAKuB,MAAMpB,YAAA,GAEzC,CAtBM6B,EAAA,IAIH,MAAAD,EAAqBjC,EAAI,IACzB2B,EAAU3B,EAA6D,IAoB7E,SAASmC,IACD,MAAAC,EAAaT,EAAQF,MAAMY,QAAQC,GAA8B,OAArBA,EAAKR,aAA6C,IAArBQ,EAAKR,cAC9Eb,EAAS,CACbC,MAAOvB,EAAUuB,MACjBC,MAAOxB,EAAUwB,MACjBoB,YAAaH,GAEfI,EAAiBC,gBAAgBxB,GAAQK,MAAMC,IAC5B,IAAbA,EAAIC,MACNkB,EAAUC,QAAQ,CAChBC,QAAS/C,EAAE,eACXgD,QAAQ,IAEDC,KACTrD,EAAM,YAENiD,EAAUK,MAAM,CACdH,QAASrB,EAAIyB,IACbH,QAAQ,GACT,GAEJ,CAuBH,SAASC,KACPtC,EAAUiB,OAAQ,CAAA,CAGpB,SAASwB,GAAUC,GACjB,IAAIC,EAAI,GAID,OAHHD,IACFC,EAAI,GAAGC,EAAMF,GAAMG,OAAO,mBAErBF,CAAA,CAGT,SAASG,GAAa7B,GACpB1B,EAAQ0B,OAAQ,EACV,MAAA8B,EAAQ9B,EAAMY,QAAQc,IAAYlB,EAAmBR,MAAM+B,SAASL,KACpEM,EAAUxB,EAAmBR,MAAMY,QAAQc,IAAO1B,EAAM+B,SAASL,KACvE,GAAII,EAAMG,OAAQ,CACV,MAAAC,EAAahC,EAAQF,MAAMY,QAAQC,GAASA,EAAKN,SAAWuB,EAAM,KACxExC,EAAaU,MAAMM,KAAK4B,EAAW,IACnC5D,EAAQ0B,OAAQ,CAAA,CAEdgC,EAAQC,SACG3C,EAAAU,MAAQV,EAAaU,MAAMY,QAAQC,GAASA,EAAKN,SAAWyB,EAAQ,KACjF1D,EAAQ0B,OAAQ,GAElBQ,EAAmBR,MAAQA,CAAA"}