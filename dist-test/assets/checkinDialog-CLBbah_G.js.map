{"version": 3, "file": "checkinDialog-CLBbah_G.js", "sources": ["../../src/views/room/checkin/checkinDialog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"docheckin\": \"Check-in\",\r\n    \"priceSecrecy\": \"Price Secrecy\",\r\n    \"cancel\": \"Cancel\",\r\n\t\t\"smsReminder\": \"Send SMS Reminder?\",\r\n    \"checkinAndPrintRegistration\": \"Check-in and Print Registration Form\",\r\n    \"checkin\": \"Check-in\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"docheckin\": \"办理入住\",\r\n    \"priceSecrecy\": \"房价保密\",\r\n    \"cancel\": \"取消\",\r\n\t\t\"smsReminder\": \"是否发送短信？\",\r\n    \"checkinAndPrintRegistration\": \"入住并打印登记单\",\r\n    \"checkin\": \"入住\"\r\n  },\r\n  \"km\": {\r\n    \"docheckin\": \"ចូលសំណាក\",\r\n    \"priceSecrecy\": \"ភាពសម្ងាត់តម្លៃ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"smsReminder\": \"ផ្ញើសារជូនដំណឹង?\",\r\n    \"checkinAndPrintRegistration\": \"ចូលសំណាកនិងបោះពុម្ពទម្រង់ចុះឈ្មោះ\",\r\n    \"checkin\": \"ចូលសំណាក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { CheckinType } from '@/models/index'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport CheckinForm from './checkin.vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rCode?: string\r\n    checkinType?: CheckinType\r\n    rtData?: any\r\n    /** 押金配置信息 */\r\n    depositConfig?: any\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    rCode: '',\r\n    checkinType: CheckinType.ALL_DAY,\r\n    rtData: {},\r\n    depositConfig: null,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [value: any]\r\n}>()\r\n// 引入 i18n\r\nconst { t } = useI18n()\r\nconst formRef = ref()\r\nconst isSendSms = ref()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst priceSecrecy = ref(false)\r\nfunction onSubmit() {\r\n  formRef.value.submit()\r\n}\r\n\r\nfunction openOrder(data: any) {\r\n  emits('success', data)\r\n  onCancel()\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" :title=\"t('docheckin')\" width=\"98%\" height=\"90vh\" style=\"overflow-y: auto\" :close-on-click-modal=\"false\" append-to-body destroy-on-close class=\"custom-dialog\" align-center :show-close=\"false\">\r\n      <template #header=\"{ close, titleId, titleClass }\">\r\n        <div class=\"custom-dialog-header\">\r\n          <span :id=\"titleId\" :class=\"titleClass\">{{ t('docheckin') }}</span>\r\n          <el-button :icon=\"Close\" circle size=\"large\" @click=\"onCancel\" />\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"custom-dialog-content\">\r\n        <CheckinForm ref=\"formRef\" v-bind=\"props\" :is-send-sms=\"isSendSms\" :deposit-config=\"props.depositConfig\" @success=\"openOrder\" />\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"custom-dialog-footer\">\r\n          <el-checkbox v-model=\"priceSecrecy\" style=\"margin-right: 20px\">\r\n            {{ t('priceSecrecy') }}\r\n          </el-checkbox>\r\n          <el-checkbox v-model=\"isSendSms\" true-value=\"1\" false-value=\"0\" :label=\"t('smsReminder')\" />&nbsp;&nbsp;\r\n          <el-button size=\"large\" @click=\"onCancel\">\r\n            {{ t('cancel') }}\r\n          </el-button>\r\n          <el-button size=\"large\" type=\"primary\" @click=\"onSubmit()\">\r\n            {{ t('checkin') }}\r\n          </el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n/* 自定义对话框样式 */\r\n.custom-dialog {\r\n  position: fixed;\r\n  top: 5vh;\r\n  left: 1%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 98%;\r\n  height: calc(100vh - 5vh);\r\n  margin: 0;\r\n}\r\n\r\n/* 自定义头部样式 */\r\n.custom-dialog-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0;\r\n}\r\n\r\n/* 内容区域占满高度 */\r\n.custom-dialog-content {\r\n  height: 80vh;\r\n  background-color: #f2f3f5;\r\n  overflow-y: auto;\r\n  box-sizing: border-box;\r\n  flex: 1;\r\n  padding-bottom: 0;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* 底部按钮区域 */\r\n.custom-dialog-footer {\r\n  padding: 10px 20px;\r\n  text-align: right;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "formRef", "ref", "isSendSms", "myVisible", "computed", "get", "modelValue", "set", "val", "priceSecrecy", "openOrder", "data", "onCancel", "value", "submit"], "mappings": "gyGAkCA,MAAMA,EAAQC,EAiBRC,EAAQC,GAKRC,EAAEA,GAAMC,IACRC,EAAUC,IACVC,EAAYD,IACZE,EAAYC,EAAS,CACzBC,IAAM,IACGX,EAAMY,WAEf,GAAAC,CAAIC,GACFZ,EAAM,oBAAqBY,EAAG,IAI5BC,EAAeR,GAAI,GAKzB,SAASS,EAAUC,GACjBf,EAAM,UAAWe,GACRC,GAAA,CAGX,SAASA,IACPT,EAAUU,OAAQ,CAAA,qiCATlBb,EAAQa,MAAMC"}