{"version": 3, "file": "device.api-BsgckoMw.js", "sources": ["../../src/api/modules/pms/device/device.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n/**\r\n * 设备信息接口\r\n */\r\nexport default {\r\n  // 获得设备信息分页\r\n  devicePage: (data: any) => api.get('/admin-api/pms/device-set/page', { params: data }),\r\n  // 创建设备信息\r\n  deviceCreate: (data: any) => api.post('/admin-api/pms/device-set/create', data),\r\n  // 更新设备信息\r\n  deviceUpdate: (data: any) => api.put('/admin-api/pms/device-set/update', data),\r\n  // 获得设备信息\r\n  deviceInfo: (data: any) => api.get('/admin-api/pms/device-set/get', { params: data }),\r\n  // 获取设备类型\r\n  getDeviceType: () => api.get('/admin-api/system/dict-data/get/device-type'),\r\n  // 生成锁号\r\n  generateLockNo: (data: any) => api.post('/admin-api/pms/room/generate-lock-no', data),\r\n  // 配置房锁\r\n  updateLockNo: (data: any) => api.post('/admin-api/pms/room/update-lock-no', data),\r\n  // 获得设备品牌\r\n  listBrand: (data: any) => api.get('/admin-api/pms/device-set/list-brand', { params: data }),\r\n  // 获得发卡器类型\r\n  cardDispenserType: (data: any) => api.get('/admin-api/pms/device-set/card-dispenser-type', { params: data }),\r\n  // 获得门锁型号\r\n  getVerName: (data: any) => api.get('/admin-api/pms/device-set/get-ver-name', { params: data }),\r\n\r\n  // 获得门店门锁配置信息\r\n  getDoorLock: (data: any) => api.get('/admin-api/pms/device-set/get-door-lock', { params: data }),\r\n\r\n  // 获取门店的锁配置信息\r\n  getHotelDoor: (data: any) => api.get('/admin-api/pms/hotel-device-set/get-door', { params: data }),\r\n\r\n  // 获取门店锁列表信息\r\n  getDoorLocks: (data: any) => api.get('/admin-api/pms/hotel-device-set/get-door-locks', { params: data }),\r\n\r\n  /** 获取酒店整体营收 */\r\n  getHotelData: (data: any) => api.get('/admin-api/report/data/hotel/get', { params: data }),\r\n\r\n  /** 创建酒店门锁 */\r\n  createHotelDevice: (data: any) => api.post('/admin-api/pms/hotel-device-set/create', data),\r\n\r\n  /** 更新酒店门锁 */\r\n  updateHotelDevice: (data: any) => api.put('/admin-api/pms/hotel-device-set/update', data),\r\n  /**\r\n   * 获取酒店设备通过id\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getHotelDeviceById: (data: any) => api.get('/admin-api/pms/hotel-device-set/get-by-id', { params: data }),\r\n}\r\n\r\nexport interface HotelDeviceSetRespVO {\r\n  id: number\r\n  /**\r\n   * 集团代码\r\n   */\r\n  gcode: string\r\n\r\n  /**\r\n   * 门店代码\r\n   */\r\n  hcode: string\r\n\r\n  /**\r\n   * 设备代码\r\n   */\r\n  deviceCode: string\r\n\r\n  deviceName: string\r\n\r\n  /**\r\n   * 品牌名称\r\n   */\r\n  brandName: string\r\n\r\n  /**\r\n   * 设备类型\r\n   */\r\n  deviceType: string\r\n\r\n  /**\r\n   * 数据库路径\r\n   */\r\n  url: string\r\n\r\n  /**\r\n   * 版本号\r\n   */\r\n  version: string\r\n\r\n  /**\r\n   * 端口号\r\n   */\r\n  port: string\r\n\r\n  /**\r\n   * 发卡器类型\r\n   */\r\n  cardDispenserType: string\r\n\r\n  /**\r\n   * 是否允许开反锁\r\n   * 0:否 1:是 (门锁有效)\r\n   */\r\n  allowLockOut: string\r\n\r\n  /**\r\n   * 新卡替换旧卡\r\n   * 0:否 1:是 (门锁有效)\r\n   */\r\n  replaceCard: string\r\n\r\n  /**\r\n   * 检查入住时间\r\n   * 0:否 1:是 (门锁有效)\r\n   */\r\n  checkTime: string\r\n\r\n  /**\r\n   * 状态\r\n   * 0:禁用 1:启用\r\n   */\r\n  state: string\r\n\r\n  /**\r\n   * 备注\r\n   */\r\n  remark: string\r\n\r\n  /**\r\n   * 配置信息\r\n   */\r\n  conf: {\r\n    /**\r\n     * 参数属性名称 如：hotelId\r\n     */\r\n    parameterCode: string\r\n    /**\r\n     * 参数名称 如：酒店标识\r\n     */\r\n    parameterName: string\r\n\r\n    /**\r\n     * 参数内容 如：123456\r\n     */\r\n    parameterContent: string\r\n  }[]\r\n}\r\n"], "names": ["deviceApi", "devicePage", "data", "api", "get", "params", "deviceCreate", "post", "deviceUpdate", "put", "deviceInfo", "getDeviceType", "generateLockNo", "updateLockNo", "listBrand", "cardDispenserType", "getVerName", "getDoorLock", "getHotelDoor", "getDoorLocks", "getHotelData", "createHotelDevice", "updateHotelDevice", "getHotelDeviceById"], "mappings": "wCAIA,MAAeA,EAAA,CAEbC,WAAaC,GAAcC,EAAIC,IAAI,iCAAkC,CAAEC,OAAQH,IAE/EI,aAAeJ,GAAcC,EAAII,KAAK,mCAAoCL,GAE1EM,aAAeN,GAAcC,EAAIM,IAAI,mCAAoCP,GAEzEQ,WAAaR,GAAcC,EAAIC,IAAI,gCAAiC,CAAEC,OAAQH,IAE9ES,cAAe,IAAMR,EAAIC,IAAI,+CAE7BQ,eAAiBV,GAAcC,EAAII,KAAK,uCAAwCL,GAEhFW,aAAeX,GAAcC,EAAII,KAAK,qCAAsCL,GAE5EY,UAAYZ,GAAcC,EAAIC,IAAI,uCAAwC,CAAEC,OAAQH,IAEpFa,kBAAoBb,GAAcC,EAAIC,IAAI,gDAAiD,CAAEC,OAAQH,IAErGc,WAAad,GAAcC,EAAIC,IAAI,yCAA0C,CAAEC,OAAQH,IAGvFe,YAAcf,GAAcC,EAAIC,IAAI,0CAA2C,CAAEC,OAAQH,IAGzFgB,aAAehB,GAAcC,EAAIC,IAAI,2CAA4C,CAAEC,OAAQH,IAG3FiB,aAAejB,GAAcC,EAAIC,IAAI,iDAAkD,CAAEC,OAAQH,IAGjGkB,aAAelB,GAAcC,EAAIC,IAAI,mCAAoC,CAAEC,OAAQH,IAGnFmB,kBAAoBnB,GAAcC,EAAII,KAAK,yCAA0CL,GAGrFoB,kBAAoBpB,GAAcC,EAAIM,IAAI,yCAA0CP,GAMpFqB,mBAAqBrB,GAAcC,EAAIC,IAAI,4CAA6C,CAAEC,OAAQH"}