{"version": 3, "file": "roomCheck-B5C77Jvl.js", "sources": ["../../src/views/room/realtime/components/roomCheck.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"roomInspectionApplication\": \"Room Inspection Application\",\r\n      \"roomTypeAndNumber\": \"Room Type & Number\",\r\n      \"guestName\": \"Guest Name\",\r\n      \"cancel\": \"Cancel\",\r\n      \"submit\": \"Submit\",\r\n      \"roomInspectionApplicationSuccess\": \"Room '{rNo}' inspection application successful\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"roomInspectionApplication\": \"查房申请\",\r\n      \"roomTypeAndNumber\": \"房型房号\",\r\n      \"guestName\": \"客人姓名\",\r\n      \"cancel\": \"取消\",\r\n      \"submit\": \"提交\",\r\n      \"roomInspectionApplicationSuccess\": \"房间「{rNo}」申请查房成功\"\r\n    },\r\n    \"km\": {\r\n      \"roomInspectionApplication\": \"ការស្នើសុំត្រួតពិនិត្យបន្ទប់\",\r\n      \"roomTypeAndNumber\": \"ប្រភេទបន្ទប់ និងលេខ\",\r\n      \"guestName\": \"ឈ្មោះភ្ញៀវ\",\r\n      \"cancel\": \"បោះបង់\",\r\n      \"submit\": \"ដាក់ស្នើ\",\r\n      \"roomInspectionApplicationSuccess\": \"ការស្នើសុំត្រួតពិនិត្យបន្ទប់ '{rNo}' បានជោគជ័យ\"\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance } from 'element-plus'\r\nimport { roomApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rCode: string\r\n    rNo: string\r\n    guestName?: string\r\n    rtName: string\r\n  }>(),\r\n  {\r\n    rCode: '',\r\n    rNo: '',\r\n    guestName: '',\r\n    rtName: '',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst formRef = ref<FormInstance>()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  rCode: props.rCode,\r\n  rNo: props.rNo,\r\n  lockedReason: '',\r\n})\r\n\r\nonMounted(() => {})\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        roomApi.updateRoomAllStatus(form.value).then((res: any) => {\r\n          if (res.code !== 0) {\r\n            ElMessage.error(res.msg)\r\n            return\r\n          }\r\n          ElMessage.success({\r\n            message: t('roomInspectionApplicationSuccess', { rNo: props.rNo }),\r\n            type: 'success',\r\n            center: true,\r\n          })\r\n          emits('success', { rCode: props.rCode, rNo: props.rNo })\r\n          onCancel()\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"`${t('roomInspectionApplication')}：${props.rNo}`\" width=\"500px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"form\" label-width=\"100px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('roomTypeAndNumber')\"> {{ props.rtName }}-{{ props.rNo }} </el-form-item>\r\n      <el-form-item :label=\"t('guestName')\">\r\n        {{ props.guestName }}\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('submit') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "form", "gcode", "hcode", "rCode", "rNo", "lockedReason", "onSubmit", "value", "validate", "valid", "roomApi", "updateRoomAllStatus", "then", "res", "code", "ElMessage", "success", "message", "type", "center", "onCancel", "error", "msg", "onMounted"], "mappings": "0nBAkCA,MAAMA,EAAQC,EAeRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,EAAUC,IACVC,EAAYC,EAAS,CACzBC,IAAM,IACGZ,EAAMa,WAEf,GAAAC,CAAIC,GACFb,EAAM,oBAAqBa,EAAG,IAG5BC,EAAOP,EAAI,CACfQ,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBC,MAAOnB,EAAMmB,MACbC,IAAKpB,EAAMoB,IACXC,aAAc,KAKhB,SAASC,IACPd,EAAQe,OACNf,EAAQe,MAAMC,UAAUC,IAClBA,GACFC,EAAQC,oBAAoBX,EAAKO,OAAOK,MAAMC,IAC3B,IAAbA,EAAIC,MAIRC,EAAUC,QAAQ,CAChBC,QAAS7B,EAAE,mCAAoC,CAAEgB,IAAKpB,EAAMoB,MAC5Dc,KAAM,UACNC,QAAQ,IAEJjC,EAAA,UAAW,CAAEiB,MAAOnB,EAAMmB,MAAOC,IAAKpB,EAAMoB,MACzCgB,KATGL,EAAAM,MAAMR,EAAIS,IASb,GACV,GAEJ,CAGL,SAASF,IACP1B,EAAUa,OAAQ,CAAA,QAxBpBgB,GAAU"}