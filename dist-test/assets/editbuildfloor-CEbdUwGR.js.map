{"version": 3, "file": "editbuildfloor-CEbdUwGR.js", "sources": ["../../src/views/merchant/base/room/components/DetailForm/editbuildfloor.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"editMemberInfo\": \"Edit Member Info\",\r\n    \"modifyFloorName\": \"Modify Floor Name\",\r\n    \"modifyBuildingName\": \"Modify Building Name\",\r\n    \"floorName\": \"Floor\",\r\n    \"buildingName\": \"Building\",\r\n    \"pleaseEnterName\": \"Please enter name\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"updateSuccess\": \"Update successful\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"editMemberInfo\": \"编辑会员信息\",\r\n    \"modifyFloorName\": \"修改楼层名称\",\r\n    \"modifyBuildingName\": \"修改楼栋名称\",\r\n    \"floorName\": \"楼层名称\",\r\n    \"buildingName\": \"楼栋名称\",\r\n    \"pleaseEnterName\": \"请输入名称\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"updateSuccess\": \"修改成功\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\"\r\n  },\r\n  \"km\": {\r\n    \"editMemberInfo\": \"កែសម្រួលព័ត៌មានសមាជិក\",\r\n    \"modifyFloorName\": \"កែសម្រួលឈ្មោះជាន់\",\r\n    \"modifyBuildingName\": \"កែសម្រួលឈ្មោះអគារ\",\r\n    \"floorName\": \"ឈ្មោះជាន់\",\r\n    \"buildingName\": \"ឈ្មោះអគារ\",\r\n    \"pleaseEnterName\": \"សូមបញ្ចូលឈ្មោះ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"updateSuccess\": \"កែសម្រួលដោយជោគជ័យ\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { buildingFloorApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<\r\n    {\r\n      modelValue?: boolean\r\n    } & DetailFormProps\r\n  >(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  code: props.code,\r\n  name: props.name,\r\n  parentCode: props.parentCode,\r\n  isFloor: props.isFloor,\r\n})\r\nconst formRules = ref<FormRules>({\r\n  name: [{ required: true, message: t('pleaseEnterName'), trigger: 'blur' }],\r\n})\r\nconst myVisible = ref(props.modelValue)\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        buildingFloorApi.update(form.value).then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success({\r\n              message: t('updateSuccess'),\r\n              center: true,\r\n            })\r\n            onCancel()\r\n            emits('success')\r\n          } else {\r\n            ElMessage.error({\r\n              message: res.msg,\r\n              center: true,\r\n            })\r\n          }\r\n        })\r\n      }\r\n    })\r\n}\r\nconst title = computed(() => {\r\n  let title = t('editMemberInfo')\r\n  if (props.isFloor === '1') {\r\n    title = t('modifyFloorName')\r\n  } else {\r\n    title = t('modifyBuildingName')\r\n  }\r\n  return title\r\n})\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"title\" width=\"500px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close @closed=\"emits('update:modelValue', false)\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"120px\">\r\n      <el-form-item :label=\"form.isFloor === '1' ? t('floorName') : t('buildingName')\" prop=\"name\">\r\n        <el-input v-model=\"form.name\" :placeholder=\"t('pleaseEnterName')\" clearable />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('save') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-cascader) {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "ref", "formRef", "form", "gcode", "hcode", "code", "name", "parentCode", "isFloor", "formRules", "required", "message", "trigger", "myVisible", "modelValue", "onSubmit", "value", "validate", "valid", "buildingFloorApi", "update", "then", "res", "ElMessage", "success", "center", "onCancel", "error", "msg", "title", "computed"], "mappings": "wrBAkDA,MAAMA,EAAQC,EAURC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IACFC,GAAI,GACpB,MAAMC,EAAUD,IACVE,EAAOF,EAAI,CACfG,MAAOL,EAAUK,MACjBC,MAAON,EAAUM,MACjBC,KAAMb,EAAMa,KACZC,KAAMd,EAAMc,KACZC,WAAYf,EAAMe,WAClBC,QAAShB,EAAMgB,UAEXC,EAAYT,EAAe,CAC/BM,KAAM,CAAC,CAAEI,UAAU,EAAMC,QAASf,EAAE,mBAAoBgB,QAAS,WAE7DC,EAAYb,EAAIR,EAAMsB,YAE5B,SAASC,IACPd,EAAQe,OACNf,EAAQe,MAAMC,UAAUC,IAClBA,GACFC,EAAiBC,OAAOlB,EAAKc,OAAOK,MAAMC,IACvB,IAAbA,EAAIjB,MACNkB,EAAUC,QAAQ,CAChBb,QAASf,EAAE,iBACX6B,QAAQ,IAEDC,IACThC,EAAM,YAEN6B,EAAUI,MAAM,CACdhB,QAASW,EAAIM,IACbH,QAAQ,GACT,GAEJ,GAEJ,CAEC,MAAAI,EAAQC,GAAS,KACjBD,IAAAA,EAAQjC,EAAE,kBAMPiC,OAJLA,EADoB,MAAlBrC,EAAMgB,QACAZ,EAAE,mBAEFA,EAAE,sBAELiC,CAAAA,IAET,SAASH,IACPb,EAAUG,OAAQ,CAAA"}