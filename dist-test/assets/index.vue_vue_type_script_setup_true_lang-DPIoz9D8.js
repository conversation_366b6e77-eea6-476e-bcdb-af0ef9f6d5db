import{d as e,b as l,B as a,o,c as t,e as s,w as d,f as i,h as u,a6 as r,u as n,i as m,R as f,q as p,ay as c,aA as y}from"./index-CkEhI1Zk.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css                  */import _ from"./index-C8ITfnJ1.js";const k=e({__name:"index",props:{modelValue:{type:Boolean,default:!1},mode:{},id:{}},emits:["update:modelValue","success"],setup(e,{emit:k}){const V=e,g=k,j=l(),v=a({get:()=>V.modelValue,set(e){g("update:modelValue",e)}}),x=a((()=>""===V.id?"新增渠道":"编辑渠道"));function z(){j.value.submit().then((()=>{g("success"),b()}))}function b(){v.value=!1}return(e,l)=>{const a=p,k=c,g=y;return o(),t("div",null,["dialog"===V.mode?(o(),s(k,{key:0,modelValue:n(v),"onUpdate:modelValue":l[0]||(l[0]=e=>m(v)?v.value=e:null),title:n(x),width:"600px","close-on-click-modal":!1,"append-to-body":"","destroy-on-close":""},{footer:d((()=>[i(a,{size:"large",onClick:b},{default:d((()=>l[2]||(l[2]=[u(" 取消 ")]))),_:1}),i(a,{type:"primary",size:"large",onClick:z},{default:d((()=>l[3]||(l[3]=[u(" 确定 ")]))),_:1})])),default:d((()=>[i(_,r({ref_key:"formRef",ref:j},V),null,16)])),_:1},8,["modelValue","title"])):"drawer"===V.mode?(o(),s(g,{key:1,modelValue:n(v),"onUpdate:modelValue":l[1]||(l[1]=e=>m(v)?v.value=e:null),title:n(x),size:"600px","close-on-click-modal":!1,"destroy-on-close":""},{footer:d((()=>[i(a,{size:"large",onClick:b},{default:d((()=>l[4]||(l[4]=[u(" 取消 ")]))),_:1}),i(a,{type:"primary",size:"large",onClick:z},{default:d((()=>l[5]||(l[5]=[u(" 确定 ")]))),_:1})])),default:d((()=>[i(_,r({ref_key:"formRef",ref:j},V),null,16)])),_:1},8,["modelValue","title"])):f("",!0)])}}});export{k as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-DPIoz9D8.js.map
