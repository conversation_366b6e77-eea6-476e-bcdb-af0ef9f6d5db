{"version": 3, "file": "DynamicConfigForm-DqS7---Y.js", "sources": ["../../src/views/marketing/touch/platform/components/FormMode/DynamicConfigForm.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport { materialApi } from '@/api/modules'\r\nimport storage from '@/utils/storage'\r\nimport { Picture, Search } from '@element-plus/icons-vue'\r\nimport { onMounted, ref, watch } from 'vue'\r\n\r\ninterface ShareConfig {\r\n  video: number\r\n  photoCount: number\r\n  title: string\r\n  isFreeContent: boolean\r\n  coverImage?: string\r\n  // 添加WIFI配置字段\r\n  account?: string\r\n  password?: string\r\n}\r\n\r\ninterface Props {\r\n  configType: string\r\n  modelValue: string\r\n}\r\n\r\nconst props = defineProps<Props>()\r\nconst emit = defineEmits<{\r\n  'update:modelValue': [value: string]\r\n}>()\r\n\r\n// 表单数据\r\nconst formData = ref<ShareConfig>({\r\n  video: 1,\r\n  photoCount: 1,\r\n  title: '',\r\n  isFreeContent: false,\r\n  coverImage: '',\r\n  // 添加WIFI配置默认值\r\n  account: '',\r\n  password: '',\r\n})\r\n\r\n// 封面选择相关\r\nconst showCoverDialog = ref(false)\r\nconst coverLoading = ref(false)\r\nconst coverList = ref([])\r\nconst coverTotal = ref(0)\r\nconst coverSearchName = ref('') // 新增搜索关键词\r\nconst coverQueryParams = ref({\r\n  pageNo: 1,\r\n  pageSize: 20,\r\n  gcode: storage.local.get('gcode'),\r\n  hcode: storage.local.get('hcode'),\r\n  platform: '',\r\n  materialName: '',\r\n  materialType: '',\r\n})\r\n// 添加表单引用和验证规则\r\nconst formRef = ref()\r\nconst wifiRules = {\r\n  account: [{ required: true, message: '请输入WIFI账号', trigger: 'blur' }],\r\n  password: [{ required: true, message: '请输入WIFI密码', trigger: 'blur' }],\r\n}\r\n\r\n// 获取封面图片列表\r\nfunction loadCoverList() {\r\n  coverLoading.value = true\r\n  // 将搜索关键词设置到查询参数中\r\n  coverQueryParams.value.materialName = coverSearchName.value\r\n\r\n  materialApi.getMaterialPage(coverQueryParams.value).then((res: any) => {\r\n    if (res && res.code === 0) {\r\n      coverList.value = res.data.list || []\r\n      coverTotal.value = res.data.total || 0\r\n    }\r\n    else {\r\n      console.error('获取素材列表失败:', res)\r\n      coverList.value = []\r\n      coverTotal.value = 0\r\n    }\r\n  }).catch((error) => {\r\n    console.error('获取素材列表异常:', error)\r\n    coverList.value = []\r\n    coverTotal.value = 0\r\n  }).finally(() => {\r\n    coverLoading.value = false\r\n  })\r\n}\r\n\r\n// 搜索封面\r\nfunction searchCover() {\r\n  coverQueryParams.value.pageNo = 1\r\n  loadCoverList()\r\n}\r\n\r\n// 重置搜索\r\nfunction resetCoverSearch() {\r\n  coverSearchName.value = ''\r\n  coverQueryParams.value.pageNo = 1\r\n  loadCoverList()\r\n}\r\n\r\n// 分页处理\r\nfunction handleCoverPageChange(page: number) {\r\n  coverQueryParams.value.pageNo = page\r\n  loadCoverList()\r\n}\r\n\r\n// 选择封面\r\nfunction selectCover(cover: any) {\r\n  formData.value.coverImage = cover.url\r\n  showCoverDialog.value = false\r\n  updateCode()\r\n}\r\n\r\n// 打开封面选择对话框\r\nfunction openCoverDialog() {\r\n  showCoverDialog.value = true\r\n  coverSearchName.value = ''\r\n  coverQueryParams.value.pageNo = 1\r\n  loadCoverList()\r\n}\r\n\r\n// 生成的配置代码\r\nconst generatedCode = ref('')\r\n\r\n// 解析现有的配置代码\r\nfunction parseExistingCode(code: string) {\r\n  if (!code) { return }\r\n\r\n  try {\r\n    const parts = code.split(',')\r\n    const config: Partial<ShareConfig> = {}\r\n\r\n    parts.forEach((part) => {\r\n      const colonIndex = part.indexOf(':')\r\n      if (colonIndex === -1) { return }\r\n\r\n      const key = part.substring(0, colonIndex)\r\n      const value = part.substring(colonIndex + 1)\r\n\r\n      if (key && value !== undefined) {\r\n        const trimmedKey = key.trim()\r\n        const trimmedValue = value.trim()\r\n        switch (trimmedKey) {\r\n          // 视频分享字段\r\n          case 'video':\r\n            config.video = Number.parseInt(trimmedValue) || 1\r\n            break\r\n          case 'cover':\r\n            config.coverImage = trimmedValue\r\n            break\r\n\r\n          // 笔记分享字段\r\n          case 'photo':\r\n            config.photoCount = Number.parseInt(trimmedValue) || 1\r\n            break\r\n          case 'title':\r\n            config.title = trimmedValue\r\n            break\r\n          case 'isFreeContent':\r\n            config.isFreeContent = trimmedValue === '1' || trimmedValue === 'true'\r\n            break\r\n          // 添加WIFI配置字段解析\r\n          case 'account':\r\n            config.account = trimmedValue\r\n            break\r\n          case 'password':\r\n            config.password = trimmedValue\r\n            break\r\n        }\r\n      }\r\n    })\r\n\r\n    // 只更新解析到的字段，保持其他字段的默认值\r\n    Object.keys(config).forEach((key) => {\r\n      if (config[key] !== undefined) {\r\n        formData.value[key] = config[key]\r\n      }\r\n    })\r\n  }\r\n  catch (error) {\r\n    console.warn('解析配置代码失败:', error)\r\n  }\r\n}\r\n\r\n// 更新配置代码 - 用户通过表单选择参数后自动构建\r\nfunction updateCode() {\r\n  console.log('props', props, 'formData', formData)\r\n  if (props.configType === 'SHARE_NORMAL') { // 更新条件\r\n    const parts = []\r\n\r\n    // 图片数量（必填）\r\n    parts.push(`photo:${formData.value.photoCount || 1}`)\r\n\r\n    // 标题名称（可选，但如果有值就添加）\r\n    if (formData.value.title && formData.value.title.trim()) {\r\n      parts.push(`title:${formData.value.title.trim()}`)\r\n    }\r\n\r\n    // 是否自定义文字（必填）\r\n    parts.push(`isFreeContent:${formData.value.isFreeContent ? '1' : '0'}`)\r\n\r\n    generatedCode.value = parts.join(',')\r\n    emit('update:modelValue', generatedCode.value)\r\n  }\r\n  else if (props.configType === 'SHARE_VIDEO') {\r\n    const parts = []\r\n    // 视频数量（固定为1）\r\n    parts.push(`video:${formData.value.video || 1}`)\r\n    // 封面图片（可选）\r\n    if (formData.value.coverImage && formData.value.coverImage.trim()) {\r\n      parts.push(`cover:${formData.value.coverImage.trim()}`)\r\n    }\r\n    // 是否自定义文字（必填）\r\n    parts.push(`isFreeContent:${formData.value.isFreeContent ? '1' : '0'}`)\r\n    generatedCode.value = parts.join(',')\r\n    emit('update:modelValue', generatedCode.value)\r\n  }\r\n  else if (props.configType === 'WIFI') {\r\n    const parts = []\r\n    // 始终添加账号和密码字段，即使为空\r\n    parts.push(`account:${formData.value.account ? formData.value.account.trim() : ''}`)\r\n    parts.push(`password:${formData.value.password ? formData.value.password.trim() : ''}`)\r\n\r\n    generatedCode.value = parts.join(',')\r\n    emit('update:modelValue', generatedCode.value)\r\n  }\r\n  else if (props.configType === 'USER_REVIEWS') {\r\n    const parts = []\r\n\r\n    // 图片数量（必填）\r\n    parts.push(`photo:${formData.value.photoCount || 1}`)\r\n    // 是否自定义文字（必填）\r\n    parts.push(`isFreeContent:${formData.value.isFreeContent ? '1' : '0'}`)\r\n    generatedCode.value = parts.join(',')\r\n\r\n    emit('update:modelValue', generatedCode.value)\r\n  }\r\n}\r\n\r\n// 监听外部传入的值变化\r\nwatch(() => props.modelValue, (newValue) => {\r\n  if (newValue && newValue !== generatedCode.value) {\r\n    parseExistingCode(newValue)\r\n  }\r\n}, { immediate: true })\r\n\r\n// 监听配置类型变化\r\nwatch(() => props.configType, () => {\r\n  // 重置表单数据\r\n  formData.value = {\r\n    video: 1,\r\n    photoCount: 1,\r\n    title: '',\r\n    isFreeContent: false,\r\n    coverImage: '',\r\n    // 添加WIFI配置重置\r\n    account: '',\r\n    password: '',\r\n  }\r\n  updateCode()\r\n}, { immediate: true })\r\n\r\n// 组件挂载时初始化\r\nonMounted(() => {\r\n  updateCode()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"dynamic-config-form\">\r\n    <!-- 笔记分享配置 -->\r\n    <div v-if=\"configType === 'SHARE_NORMAL'\" class=\"share-config\">\r\n      <el-form-item label=\"图片数量\" prop=\"photoCount\">\r\n        <el-input-number\r\n          v-model=\"formData.photoCount\"\r\n          :min=\"1\"\r\n          :max=\"20\"\r\n          placeholder=\"请输入图片数量\"\r\n          @change=\"updateCode\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"标题名称\" prop=\"title\">\r\n        <el-input\r\n          v-model=\"formData.title\"\r\n          placeholder=\"请输入标题名称\"\r\n          maxlength=\"20\"\r\n          show-word-limit\r\n          @input=\"updateCode\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"AI生成内容\" prop=\"isFreeContent\">\r\n        <el-switch\r\n          v-model=\"formData.isFreeContent\"\r\n          @change=\"updateCode\"\r\n        />\r\n      </el-form-item>\r\n      <el-alert\r\n        description=\"开启：AI自动生成内容  关闭：随机获取素材库文字\"\r\n        type=\"info\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"compact-alert\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 视频分享配置 -->\r\n    <div v-else-if=\"configType === 'SHARE_VIDEO'\" class=\"share-video-config\">\r\n      <el-alert\r\n        description=\"当前视频数量固定\"\r\n        type=\"info\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"compact-alert\"\r\n      />\r\n\r\n      <el-form-item label=\"视频数量\" prop=\"video\">\r\n        <el-input-number\r\n          v-model=\"formData.video\"\r\n          :min=\"1\"\r\n          :max=\"1\"\r\n          disabled\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 自定义文字开关 -->\r\n      <el-form-item label=\"AI生成内容\" prop=\"isFreeContent\">\r\n        <el-switch\r\n          v-model=\"formData.isFreeContent\"\r\n          @change=\"updateCode\"\r\n        />\r\n      </el-form-item>\r\n      <el-alert\r\n        description=\"开启：AI自动生成内容 ； 关闭：随机获取素材库文字\"\r\n        type=\"info\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"compact-alert\"\r\n      />\r\n\r\n      <!-- 封面设置 -->\r\n      <el-form-item label=\"封面设置\">\r\n        <div v-if=\"formData.coverImage\" class=\"cover-preview\">\r\n          <img\r\n            :src=\"formData.coverImage\"\r\n            alt=\"封面图片\"\r\n            class=\"cover-image\"\r\n            style=\"width: 100px; height: 100px; object-fit: cover; border-radius: 4px;\"\r\n          >\r\n          <div class=\"cover-actions\">\r\n            <el-button size=\"small\" @click=\"openCoverDialog\">\r\n              更换封面\r\n            </el-button>\r\n            <el-button size=\"small\" type=\"danger\" @click=\"formData.coverImage = ''\">\r\n              移除封面\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        <el-button v-else type=\"primary\" @click=\"openCoverDialog\">\r\n          <el-icon><Picture /></el-icon>\r\n          选择封面\r\n        </el-button>\r\n      </el-form-item>\r\n    </div>\r\n\r\n    <!-- 店铺主页配置 (预留) -->\r\n    <div v-else-if=\"configType === 'SHOP_URL'\" class=\"shop-config\">\r\n      <!-- 后续扩展店铺配置 -->\r\n      <el-empty description=\"店铺主页配置待开发\" />\r\n    </div>\r\n\r\n    <div v-else-if=\"configType === 'WIFI'\" class=\"share-video-config\">\r\n      <el-form ref=\"formRef\" :model=\"formData\" :rules=\"wifiRules\" label-width=\"120px\">\r\n        <el-form-item label=\"WIFI账号\" prop=\"account\">\r\n          <el-input\r\n            v-model=\"formData.account\"\r\n            placeholder=\"请输入WIFI账号\"\r\n            maxlength=\"30\"\r\n            @input=\"updateCode\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"WIFI密码\" prop=\"password\">\r\n          <el-input\r\n            v-model=\"formData.password\"\r\n            type=\"password\"\r\n            placeholder=\"请输入WIFI密码\"\r\n            maxlength=\"30\"\r\n            @input=\"updateCode\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <el-alert\r\n        description=\"用户可获取WIFI账号和密码信息\"\r\n        type=\"info\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"compact-alert\"\r\n      />\r\n    </div>\r\n    <!-- 携程分享配置 -->\r\n    <div v-else-if=\"configType === 'USER_REVIEWS'\" class=\"share-video-config\">\r\n      <el-form-item label=\"图片数量\" prop=\"photoCount\">\r\n        <el-input-number\r\n          v-model=\"formData.photoCount\"\r\n          :min=\"1\"\r\n          :max=\"20\"\r\n          placeholder=\"请输入图片数量\"\r\n          @change=\"updateCode\"\r\n        />\r\n      </el-form-item>\r\n      <!-- 自定义文字开关 -->\r\n      <el-form-item label=\"AI生成内容\" prop=\"isFreeContent\">\r\n        <el-switch\r\n          v-model=\"formData.isFreeContent\"\r\n          @change=\"updateCode\"\r\n        />\r\n      </el-form-item>\r\n      <el-alert\r\n        description=\"开启：AI自动生成内容 ； 关闭：随机获取素材库文字\"\r\n        type=\"info\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"compact-alert\"\r\n      />\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 封面选择对话框 -->\r\n  <el-dialog\r\n    v-model=\"showCoverDialog\"\r\n    title=\"选择视频封面\"\r\n    width=\"900px\"\r\n    :close-on-click-modal=\"false\"\r\n    destroy-on-close\r\n  >\r\n    <!-- 搜索区域 -->\r\n    <div class=\"cover-search\">\r\n      <el-input\r\n        v-model=\"coverSearchName\"\r\n        placeholder=\"请输入素材名称搜索\"\r\n        clearable\r\n        style=\"width: 300px; margin-right: 12px;\"\r\n        @keyup.enter=\"searchCover\"\r\n        @clear=\"resetCoverSearch\"\r\n      >\r\n        <template #prefix>\r\n          <el-icon><Search /></el-icon>\r\n        </template>\r\n      </el-input>\r\n      <el-button type=\"primary\" :loading=\"coverLoading\" @click=\"searchCover\">\r\n        <el-icon><Search /></el-icon>\r\n        搜索\r\n      </el-button>\r\n      <el-button :disabled=\"coverLoading\" @click=\"resetCoverSearch\">\r\n        重置\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 图片网格 -->\r\n    <div v-loading=\"coverLoading\" class=\"cover-content\">\r\n      <div v-if=\"coverList.length > 0\" class=\"cover-grid\">\r\n        <div\r\n          v-for=\"cover in coverList\"\r\n          :key=\"cover.id\"\r\n          class=\"cover-item\"\r\n          @click=\"selectCover(cover)\"\r\n        >\r\n          <img\r\n            :src=\"cover.url\"\r\n            alt=\"封面图片\"\r\n            style=\"width: 120px; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;\"\r\n          >\r\n          <div class=\"cover-name\" :title=\"cover.materialName\">\r\n            {{ cover.materialName }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else-if=\"!coverLoading\" class=\"no-data\">\r\n        <el-empty description=\"暂无图片素材\" />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div v-if=\"coverTotal > coverQueryParams.pageSize\" class=\"cover-pagination\">\r\n      <el-pagination\r\n        v-model:current-page=\"coverQueryParams.pageNo\"\r\n        :page-size=\"coverQueryParams.pageSize\"\r\n        :total=\"coverTotal\"\r\n        layout=\"prev, pager, next, jumper\"\r\n        @current-change=\"handleCoverPageChange\"\r\n      />\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped>\r\n.cover-selector {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.current-cover {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.cover-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.cover-search {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n}\r\n\r\n.cover-content {\r\n  min-height: 200px;\r\n}\r\n\r\n.cover-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\r\n  gap: 16px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.cover-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.cover-item:hover {\r\n  border-color: #409eff;\r\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n.cover-name {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  text-align: center;\r\n  word-break: break-all;\r\n  max-width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.image-error {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #f5f7fa;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.image-error .el-icon {\r\n  font-size: 20px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.no-data {\r\n  text-align: center;\r\n  padding: 40px 0;\r\n}\r\n\r\n.cover-pagination {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #e4e7ed;\r\n}\r\n/* 重新设计的紧凑型提示框样式 */\r\n.compact-alert {\r\n  margin-bottom: 18px;\r\n\tmargin-left: auto;\r\n  padding: 4px 6px ;\r\n  border-radius: 4px ;\r\n\r\n  font-size: 12px ;\r\n  line-height: 1.3 ;\r\n  background-color: #f0f9ff ;\r\n  border: 1px solid #d1ecf1 ;\r\n  min-height: auto ;\r\n  width: 80% ;\r\n  box-sizing: border-box ;\r\n\r\n\t/* 图标控制 */\r\n\t:deep(.el-alert__icon) {\r\n\t\tfont-size: 19px; /* 图标大小 */\r\n\t\tmargin-right: 6px; /* 图标与文字间距 */\r\n\t}\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emit", "__emit", "formData", "ref", "video", "photoCount", "title", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coverImage", "account", "password", "showCoverDialog", "coverLoading", "coverList", "coverTotal", "coverSearchName", "coverQueryParams", "pageNo", "pageSize", "gcode", "storage", "local", "get", "hcode", "platform", "materialName", "materialType", "formRef", "wifiRules", "required", "message", "trigger", "loadCoverList", "value", "materialApi", "getMaterialPage", "then", "res", "code", "data", "list", "total", "console", "error", "catch", "finally", "searchCover", "resetCoverSearch", "handleCoverPageChange", "page", "openCoverDialog", "generatedCode", "updateCode", "log", "configType", "parts", "push", "trim", "join", "watch", "modelValue", "newValue", "split", "config", "for<PERSON>ach", "part", "colonIndex", "indexOf", "key", "substring", "<PERSON><PERSON><PERSON>", "trimmedValue", "Number", "parseInt", "Object", "keys", "warn", "parseExistingCode", "immediate", "onMounted", "cover", "url"], "mappings": "s8CAsBA,MAAMA,EAAQC,EACRC,EAAOC,EAKPC,GAAWC,EAAiB,CAChCC,MAAO,EACPC,WAAY,EACZC,MAAO,GACPC,eAAe,EACfC,WAAY,GAEZC,QAAS,GACTC,SAAU,KAINC,GAAkBR,GAAI,GACtBS,GAAeT,GAAI,GACnBU,GAAYV,EAAI,IAChBW,GAAaX,EAAI,GACjBY,GAAkBZ,EAAI,IACtBa,GAAmBb,EAAI,CAC3Bc,OAAQ,EACRC,SAAU,GACVC,MAAOC,EAAQC,MAAMC,IAAI,SACzBC,MAAOH,EAAQC,MAAMC,IAAI,SACzBE,SAAU,GACVC,aAAc,GACdC,aAAc,KAGVC,GAAUxB,IACVyB,GAAY,CAChBnB,QAAS,CAAC,CAAEoB,UAAU,EAAMC,QAAS,YAAaC,QAAS,SAC3DrB,SAAU,CAAC,CAAEmB,UAAU,EAAMC,QAAS,YAAaC,QAAS,UAI9D,SAASC,KACPpB,GAAaqB,OAAQ,EAEJjB,GAAAiB,MAAMR,aAAeV,GAAgBkB,MAEtDC,EAAYC,gBAAgBnB,GAAiBiB,OAAOG,MAAMC,IACpDA,GAAoB,IAAbA,EAAIC,MACbzB,GAAUoB,MAAQI,EAAIE,KAAKC,MAAQ,GACxB1B,GAAAmB,MAAQI,EAAIE,KAAKE,OAAS,IAG7BC,QAAAC,MAAM,YAAaN,GAC3BxB,GAAUoB,MAAQ,GAClBnB,GAAWmB,MAAQ,EAAA,IAEpBW,OAAOD,IACAD,QAAAC,MAAM,YAAaA,GAC3B9B,GAAUoB,MAAQ,GAClBnB,GAAWmB,MAAQ,CAAA,IAClBY,SAAQ,KACTjC,GAAaqB,OAAQ,CAAA,GACtB,CAIH,SAASa,KACP9B,GAAiBiB,MAAMhB,OAAS,EAClBe,IAAA,CAIhB,SAASe,KACPhC,GAAgBkB,MAAQ,GACxBjB,GAAiBiB,MAAMhB,OAAS,EAClBe,IAAA,CAIhB,SAASgB,GAAsBC,GAC7BjC,GAAiBiB,MAAMhB,OAASgC,EAClBjB,IAAA,CAWhB,SAASkB,KACPvC,GAAgBsB,OAAQ,EACxBlB,GAAgBkB,MAAQ,GACxBjB,GAAiBiB,MAAMhB,OAAS,EAClBe,IAAA,CAIV,MAAAmB,GAAgBhD,EAAI,IA+D1B,SAASiD,KAEH,GADJV,QAAQW,IAAI,QAASvD,EAAO,WAAYI,IACf,iBAArBJ,EAAMwD,WAA+B,CACvC,MAAMC,EAAQ,GAGdA,EAAMC,KAAK,SAAStD,GAAS+B,MAAM5B,YAAc,KAG7CH,GAAS+B,MAAM3B,OAASJ,GAAS+B,MAAM3B,MAAMmD,QAC/CF,EAAMC,KAAK,SAAStD,GAAS+B,MAAM3B,MAAMmD,UAI3CF,EAAMC,KAAK,kBAAiBtD,GAAS+B,MAAM1B,cAAgB,IAAM,MAEnD4C,GAAAlB,MAAQsB,EAAMG,KAAK,KAC5B1D,EAAA,oBAAqBmD,GAAclB,MAAK,MAAA,GAEjB,gBAArBnC,EAAMwD,WAA8B,CAC3C,MAAMC,EAAQ,GAEdA,EAAMC,KAAK,SAAStD,GAAS+B,MAAM7B,OAAS,KAExCF,GAAS+B,MAAMzB,YAAcN,GAAS+B,MAAMzB,WAAWiD,QACzDF,EAAMC,KAAK,SAAStD,GAAS+B,MAAMzB,WAAWiD,UAGhDF,EAAMC,KAAK,kBAAiBtD,GAAS+B,MAAM1B,cAAgB,IAAM,MACnD4C,GAAAlB,MAAQsB,EAAMG,KAAK,KAC5B1D,EAAA,oBAAqBmD,GAAclB,MAAK,MAAA,GAEjB,SAArBnC,EAAMwD,WAAuB,CACpC,MAAMC,EAAQ,GAERA,EAAAC,KAAK,WAAWtD,GAAS+B,MAAMxB,QAAUP,GAAS+B,MAAMxB,QAAQgD,OAAS,MACzEF,EAAAC,KAAK,YAAYtD,GAAS+B,MAAMvB,SAAWR,GAAS+B,MAAMvB,SAAS+C,OAAS,MAEpEN,GAAAlB,MAAQsB,EAAMG,KAAK,KAC5B1D,EAAA,oBAAqBmD,GAAclB,MAAK,MAAA,GAEjB,iBAArBnC,EAAMwD,WAA+B,CAC5C,MAAMC,EAAQ,GAGdA,EAAMC,KAAK,SAAStD,GAAS+B,MAAM5B,YAAc,KAEjDkD,EAAMC,KAAK,kBAAiBtD,GAAS+B,MAAM1B,cAAgB,IAAM,MACnD4C,GAAAlB,MAAQsB,EAAMG,KAAK,KAE5B1D,EAAA,oBAAqBmD,GAAclB,MAAK,CAC/C,QAIF0B,GAAM,IAAM7D,EAAM8D,aAAaC,IACzBA,GAAYA,IAAaV,GAAclB,OApH7C,SAA2BK,GACzB,GAAKA,EAED,IACI,MAAAiB,EAAQjB,EAAKwB,MAAM,KACnBC,EAA+B,CAAC,EAEhCR,EAAAS,SAASC,IACP,MAAAC,EAAaD,EAAKE,QAAQ,KAChC,IAAuB,IAAnBD,EAAqB,OAEzB,MAAME,EAAMH,EAAKI,UAAU,EAAGH,GACxBjC,EAAQgC,EAAKI,UAAUH,EAAa,GAEtC,GAAAE,QAAiB,IAAVnC,EAAqB,CACxB,MAAAqC,EAAaF,EAAIX,OACjBc,EAAetC,EAAMwB,OAC3B,OAAQa,GAEN,IAAK,QACHP,EAAO3D,MAAQoE,OAAOC,SAASF,IAAiB,EAChD,MACF,IAAK,QACHR,EAAOvD,WAAa+D,EACpB,MAGF,IAAK,QACHR,EAAO1D,WAAamE,OAAOC,SAASF,IAAiB,EACrD,MACF,IAAK,QACHR,EAAOzD,MAAQiE,EACf,MACF,IAAK,gBACIR,EAAAxD,cAAiC,MAAjBgE,GAAyC,SAAjBA,EAC/C,MAEF,IAAK,UACHR,EAAOtD,QAAU8D,EACjB,MACF,IAAK,WACHR,EAAOrD,SAAW6D,EAEtB,KAKJG,OAAOC,KAAKZ,GAAQC,SAASI,SACP,IAAhBL,EAAOK,KACTlE,GAAS+B,MAAMmC,GAAOL,EAAOK,GAAG,UAI/BzB,GACGD,QAAAkC,KAAK,YAAajC,EAAK,CACjC,CA6DEkC,CAAkBhB,EAAQ,GAE3B,CAAEiB,WAAW,IAGVnB,GAAA,IAAM7D,EAAMwD,aAAY,KAE5BpD,GAAS+B,MAAQ,CACf7B,MAAO,EACPC,WAAY,EACZC,MAAO,GACPC,eAAe,EACfC,WAAY,GAEZC,QAAS,GACTC,SAAU,IAED0C,IAAA,GACV,CAAE0B,WAAW,IAGhBC,GAAU,KACG3B,IAAA,q2IA7Jb,SAAqB4B,GACV9E,GAAA+B,MAAMzB,WAAawE,EAAMC,IAClCtE,GAAgBsB,OAAQ,EACbmB,IAAA"}