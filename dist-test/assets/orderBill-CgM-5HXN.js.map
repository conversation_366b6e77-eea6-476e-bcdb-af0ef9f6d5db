{"version": 3, "file": "orderBill-CgM-5HXN.js", "sources": ["../../src/views/print/orderBill.vue"], "sourcesContent": ["<i18n>\r\n{\r\n\t\"en\": {\r\n\t\t\"printRoomBill\": \"Print Billing Details\",\r\n\t\t\"cancel\": \"Cancel\"\r\n\t},\r\n\t\"zh-cn\": {\r\n\t\t\"printRoomBill\": \"打印结账明细单\",\r\n\t\t\"cancel\": \"取消\"\r\n\t},\r\n  \"km\": {\r\n    \"printRoomBill\": \"បោះពុម្ពព័ត៌មានលម្អិតអំពីវិក័យប័ត្រ\",\r\n    \"cancel\": \"បោះបង់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { printApi, printFormApi } from '@/api/modules/index'\r\nimport { BillType, layout_width_map, PrintFormat } from '@/models/dict/constants'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { ElMessage } from 'element-plus'\r\nimport PosBillForm from './posBillForm.vue' // 导入POS小票组件\r\n\r\ndefineOptions({\r\n  name: 'PrintCheckInForm',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    orderNo: string | number\r\n    autoShowPrint?: boolean\r\n    hideDialog?: boolean\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    orderNo: '',\r\n    autoShowPrint: false,\r\n    hideDialog: false,\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  refresh: []\r\n  'print-started': []\r\n}>()\r\n\r\nconst Stimulsoft = window.Stimulsoft\r\n\r\nconst { t } = useI18n() // 解构 t 函数\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  tableAutoHeight: true,\r\n  showWidth: '800px',\r\n})\r\nconst loading = ref(false)\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst originalBillData = ref(null)\r\nconst editableBillData = ref(null)\r\nconst editDialogVisible = ref(false)\r\nconst posBillData = ref(null)\r\n\r\nonMounted(async () => {\r\n  await Promise.all([getPrintLayout(), getPrintInfo()])\r\n})\r\n\r\nconst layout = ref(PrintFormat.POS.toString())\r\n// 添加POS格式判断\r\nconst isPosFormat = computed(() => {\r\n  return layout.value === PrintFormat.POS.toString()\r\n})\r\n\r\nasync function getPrintLayout() {\r\n  printApi\r\n    .getPrintLayout({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      billCode: BillType.BILL,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        layout.value = res.data.layout\r\n        data.value.showWidth = layout_width_map.get(layout.value)?.toString() || '400px'\r\n      }\r\n    })\r\n}\r\n\r\nasync function getPrintInfo() {\r\n  loading.value = true\r\n  try {\r\n    const res = await printFormApi.printSingleRoomBill({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      orderNo: props.orderNo,\r\n    })\r\n    if (res.code === 0) {\r\n      originalBillData.value = JSON.parse(JSON.stringify(res.data))\r\n      editableBillData.value = JSON.parse(JSON.stringify(res.data))\r\n      if (isPosFormat.value) {\r\n        posBillData.value = res.data\r\n\r\n        // 如果设置了自动打印，等待DOM更新后触发打印\r\n        if (props.autoShowPrint) {\r\n          nextTick(() => {\r\n            setTimeout(() => {\r\n              printReport()\r\n            }, 800)\r\n          })\r\n        }\r\n      } else {\r\n        setJson(res.data)\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('获取打印数据出错:', error)\r\n    ElMessage.error('获取打印数据出错')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nconst dialogWidth = computed(() => {\r\n  return editDialogVisible.value ? '800px' : data.value.showWidth\r\n})\r\n\r\nconst timestamp = Date.now()\r\nlet reportInstance: any\r\nconst reportReady = ref(false)\r\n\r\nasync function setJson(json: any) {\r\n  reportReady.value = false\r\n  const licensePath = new URL('/src/assets/license.key', import.meta.url).href\r\n  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)\r\n  const report = new Stimulsoft.Report.StiReport()\r\n\r\n  if (layout.value === PrintFormat.A4.toString()) {\r\n    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printBillA4.mrt?t=${timestamp}`)\r\n    //report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBillA4.mrt`)\r\n  } else if (layout.value === PrintFormat.A412.toString()) {\r\n    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printBillA4-1-2.mrt?t=${timestamp}`)\r\n    //report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBillA4-1-2.mrt`)\r\n  } else if (layout.value === PrintFormat.A413.toString()) {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBillA4-1-3.mrt`)\r\n  } else {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBill78.mrt`)\r\n  }\r\n\r\n  const settingsStore = useSettingsStore()\r\n  const currentLanguage = settingsStore.lang\r\n  const localizationFile = `${currentLanguage}.xml`\r\n  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)\r\n\r\n  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')\r\n  dataSet.readJson(JSON.stringify(json))\r\n  report.dictionary.databases.clear()\r\n  report.regData('JSON', 'JSON', dataSet)\r\n\r\n  const viewerOptions = new Stimulsoft.Viewer.StiViewerOptions()\r\n  viewerOptions.toolbar.visible = false\r\n  const viewer = new Stimulsoft.Viewer.StiViewer(viewerOptions, 'StiViewer', false)\r\n  viewer.report = report\r\n  viewer.renderHtml('report')\r\n  reportInstance = report\r\n  loading.value = false\r\n  reportReady.value = true\r\n\r\n  // 如果设置了自动打印，等待报表渲染完成后触发打印\r\n  if (props.autoShowPrint) {\r\n    setTimeout(() => {\r\n      printReport()\r\n    }, 1000)\r\n  }\r\n}\r\n\r\nfunction printReport() {\r\n  // 通知父组件打印已开始\r\n  emits('print-started')\r\n\r\n  if (isPosFormat.value) {\r\n    // 创建一个隐藏的iframe用于打印\r\n    const printFrame = document.createElement('iframe')\r\n    printFrame.style.position = 'fixed'\r\n    printFrame.style.right = '0'\r\n    printFrame.style.bottom = '0'\r\n    printFrame.style.width = '0'\r\n    printFrame.style.height = '0'\r\n    printFrame.style.border = '0'\r\n\r\n    document.body.appendChild(printFrame)\r\n    const printContent = document.querySelector('#pos-bill-form')?.innerHTML || ''\r\n    const frameDoc = printFrame.contentWindow?.document\r\n    if (!frameDoc) {\r\n      console.error('无法访问打印框架的文档对象')\r\n      return\r\n    }\r\n    frameDoc.write(`\r\n        <html>\r\n        <meta charset=\"UTF-8\">\r\n          <head>\r\n            <title>结账明细单</title>\r\n            <style>\r\n              body {\r\n                margin: 0;\r\n                padding: 0;\r\n              }\r\n              .pos-bill-container {\r\n                width: 70mm;\r\n                margin: 0 auto;\r\n                padding: 5mm;\r\n                font-size: 14px;\r\n                color: black;\r\n              }\r\n              .hotel-title {\r\n                text-align: center;\r\n                font-size: 30px;\r\n                font-weight: bold;\r\n                margin-bottom: 5px;\r\n              }\r\n              .form-title {\r\n                text-align: center;\r\n                font-size: 14px;\r\n                margin-bottom: 4px;\r\n              }\r\n              .bill-info {\r\n                margin-bottom: 4px;\r\n                font-size: 12px;\r\n              }\r\n              .info-table,\r\n              .detail-table {\r\n                width: 100%;\r\n                border-collapse: collapse;\r\n                margin-bottom: 4px;\r\n                font-size: 12px;\r\n              }\r\n              .info-table th,\r\n              .info-table td,\r\n              .detail-table th,\r\n              .detail-table td {\r\n                border: 1px solid #000;\r\n                padding: 1px 2px;\r\n              }\r\n              .info-table th {\r\n                width: 25%;\r\n                text-align: left;\r\n              }\r\n              .center-text {\r\n                text-align: center;\r\n              }\r\n              .total-row {\r\n                font-weight: bold;\r\n              }\r\n              .remark {\r\n                border: 1px solid #000;\r\n                padding: 2px;\r\n                margin-bottom: 4px;\r\n                font-size: 10px;\r\n              }\r\n              .footer {\r\n                width: 95%;\r\n                margin-top: 10px;\r\n                font-size: 12px;\r\n              }\r\n              .footer > div {\r\n                margin-bottom: 10px;\r\n              }\r\n              @media print {\r\n                @page {\r\n                  size: 58mm auto;\r\n                  margin: 0mm;\r\n                }\r\n                body {\r\n                  margin: 0;\r\n                  padding: 0;\r\n                }\r\n              }\r\n            </style>\r\n          </head>\r\n          <body>\r\n            ${printContent}\r\n          </body>\r\n        </html>\r\n      `)\r\n    frameDoc.close()\r\n\r\n    printFrame.onload = () => {\r\n      try {\r\n        if (printFrame.contentWindow) {\r\n          printFrame.contentWindow.print()\r\n        } else {\r\n          console.error('打印窗口未能正确创建')\r\n        }\r\n        setTimeout(() => {\r\n          document.body.removeChild(printFrame)\r\n          // 如果是隐藏模式，打印完成后自动关闭组件\r\n          if (props.hideDialog) {\r\n            myVisible.value = false\r\n          }\r\n        }, 1000)\r\n      } catch (e) {\r\n        console.error('打印失败:', e)\r\n      }\r\n    }\r\n  } else {\r\n    if (!reportReady.value || !reportInstance) {\r\n      ElMessage.warning('报表还未加载完成，请稍后再试')\r\n      return\r\n    }\r\n    reportInstance.print()\r\n    // 如果是隐藏模式，打印完成后自动关闭组件\r\n    if (props.hideDialog) {\r\n      setTimeout(() => {\r\n        myVisible.value = false\r\n      }, 2000)\r\n    }\r\n  }\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction openEditDialog() {\r\n  editDialogVisible.value = true\r\n}\r\n\r\nasync function onEditSave() {\r\n  editDialogVisible.value = false\r\n  await nextTick()\r\n  if (isPosFormat.value) {\r\n    posBillData.value = JSON.parse(JSON.stringify(editableBillData.value))\r\n  } else {\r\n    setJson(editableBillData.value)\r\n  }\r\n}\r\n\r\nasync function onEditCancel() {\r\n  editDialogVisible.value = false\r\n  editableBillData.value = JSON.parse(JSON.stringify(originalBillData.value))\r\n  await nextTick()\r\n  if (isPosFormat.value) {\r\n    posBillData.value = JSON.parse(JSON.stringify(originalBillData.value))\r\n  } else {\r\n    setJson(originalBillData.value)\r\n  }\r\n}\r\n\r\nfunction addConsumptionDetail() {\r\n  if (!editableBillData.value.consumptionDetails) {\r\n    editableBillData.value.consumptionDetails = []\r\n  }\r\n  editableBillData.value.consumptionDetails.push({\r\n    subCode: '',\r\n    subName: '',\r\n    fee: 0,\r\n    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\r\n  })\r\n  calculateTotals()\r\n}\r\n\r\nfunction removeConsumptionDetail(index: number) {\r\n  editableBillData.value.consumptionDetails.splice(index, 1)\r\n  calculateTotals()\r\n}\r\n\r\nfunction addPaymentDetail() {\r\n  if (!editableBillData.value.paymentDetails) {\r\n    editableBillData.value.paymentDetails = []\r\n  }\r\n  editableBillData.value.paymentDetails.push({\r\n    subCode: '',\r\n    subName: '',\r\n    fee: 0,\r\n    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\r\n  })\r\n  calculateTotals()\r\n}\r\n\r\nfunction removePaymentDetail(index: number) {\r\n  editableBillData.value.paymentDetails.splice(index, 1)\r\n  calculateTotals()\r\n}\r\n\r\n// 计算合计金额\r\nfunction calculateTotals() {\r\n  // 计算消费总额\r\n  const consumeTotal =\r\n    editableBillData.value.consumptionDetails?.reduce((sum, item) => {\r\n      return sum + (Number(item.fee) || 0)\r\n    }, 0) || 0\r\n  editableBillData.value.consumeTotalFee = Number(consumeTotal.toFixed(2))\r\n\r\n  // 计算支付总额\r\n  const payTotal =\r\n    editableBillData.value.paymentDetails?.reduce((sum, item) => {\r\n      return sum + (Number(item.fee) || 0)\r\n    }, 0) || 0\r\n  editableBillData.value.payTotalFee = Number(payTotal.toFixed(2))\r\n}\r\n\r\n// 监听明细数据变化\r\nwatch(\r\n  () => editableBillData.value?.consumptionDetails,\r\n  () => {\r\n    calculateTotals()\r\n  },\r\n  { deep: true }\r\n)\r\n\r\nwatch(\r\n  () => editableBillData.value?.paymentDetails,\r\n  () => {\r\n    calculateTotals()\r\n  },\r\n  { deep: true }\r\n)\r\n</script>\r\n\r\n<template>\r\n  <!-- 当hideDialog为true时，使用隐藏的div容器 -->\r\n  <div v-if=\"hideDialog\" style=\"position: fixed; top: -9999px; left: -9999px; visibility: hidden\">\r\n    <!-- 报表区域 -->\r\n    <div v-if=\"!isPosFormat\" id=\"report\" style=\"width: 800px; height: 600px\" />\r\n\r\n    <!-- POS格式使用自定义Vue组件 -->\r\n    <div v-else id=\"pos-bill-form\" style=\"width: 800px; height: 600px\">\r\n      <PosBillForm v-if=\"posBillData\" :form-data=\"posBillData\" />\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 正常的弹窗模式 -->\r\n  <el-dialog v-else v-model=\"myVisible\" :width=\"dialogWidth\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\" style=\"min-height: 600px\">\r\n    <div v-if=\"!editDialogVisible\" style=\"display: flex; flex-direction: column; height: 100%\">\r\n      <!-- 按钮区域 - 当autoShowPrint为true时隐藏 -->\r\n      <div v-if=\"!autoShowPrint\" style=\"display: flex; gap: 10px; justify-content: center; margin-bottom: 10px\">\r\n        <el-button type=\"primary\" :disabled=\"!reportReady && !isPosFormat\" @click=\"printReport\">\r\n          {{ t('printRoomBill') }}\r\n        </el-button>\r\n        <el-button @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button @click=\"openEditDialog\"> 虚拟账单 </el-button>\r\n      </div>\r\n\r\n      <div v-if=\"loading\" class=\"loading-container\">\r\n        <el-skeleton :rows=\"10\" animated />\r\n      </div>\r\n\r\n      <div v-else-if=\"isPosFormat\" id=\"pos-bill-form\" style=\"flex: 1; overflow-y: auto\">\r\n        <PosBillForm v-if=\"posBillData\" :form-data=\"posBillData\" />\r\n        <el-empty v-else description=\"暂无数据\" />\r\n      </div>\r\n\r\n      <div v-else id=\"report\" style=\"flex: 1; overflow-y: auto\" />\r\n    </div>\r\n    <div v-else style=\"padding: 20px; height: 60vh; overflow-y: auto\">\r\n      <el-form :model=\"editableBillData\" label-width=\"120px\">\r\n        <div class=\"form-section\">\r\n          <h3 class=\"section-title\">基本信息</h3>\r\n          <div class=\"form-grid\">\r\n            <el-form-item label=\"酒店名称\">\r\n              <el-input v-model=\"editableBillData.hname\" disabled />\r\n            </el-form-item>\r\n            <el-form-item label=\"客人姓名\">\r\n              <el-input v-model=\"editableBillData.name\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"房号\">\r\n              <el-input v-model=\"editableBillData.rNo\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"楼栋\">\r\n              <el-input v-model=\"editableBillData.buildingName\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"楼层\">\r\n              <el-input v-model=\"editableBillData.floorName\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"房型\">\r\n              <el-input v-model=\"editableBillData.rtName\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"客源类型\">\r\n              <el-input v-model=\"editableBillData.guestSrcTypeName\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"储值金额\">\r\n              <el-input v-model=\"editableBillData.balance\" type=\"number\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"积分\">\r\n              <el-input v-model=\"editableBillData.point\" type=\"number\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"消费总额\">\r\n              <el-input v-model=\"editableBillData.consumeTotalFee\" type=\"number\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"入住时间\">\r\n              <el-date-picker v-model=\"editableBillData.checkinTime\" type=\"datetime\" placeholder=\"选择入住时间\" format=\"YYYY-MM-DD HH:mm:ss\" value-format=\"YYYY-MM-DD HH:mm:ss\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"离店时间\">\r\n              <el-date-picker v-model=\"editableBillData.checkoutTime\" type=\"datetime\" placeholder=\"选择离店时间\" format=\"YYYY-MM-DD HH:mm:ss\" value-format=\"YYYY-MM-DD HH:mm:ss\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"支付总额\">\r\n              <el-input v-model=\"editableBillData.payTotalFee\" type=\"number\" />\r\n            </el-form-item>\r\n          </div>\r\n          <el-form-item label=\"备注信息\" class=\"full-width\">\r\n            <el-input v-model=\"editableBillData.info\" type=\"textarea\" :rows=\"3\" />\r\n          </el-form-item>\r\n        </div>\r\n\r\n        <div class=\"form-section\">\r\n          <h3 class=\"section-title\">消费明细</h3>\r\n          <el-table :data=\"editableBillData?.consumptionDetails || []\" style=\"width: 100%; margin-bottom: 16px\">\r\n            <el-table-column prop=\"subName\" label=\"账目\" min-width=\"160\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.subName\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fee\" label=\"金额\" min-width=\"100\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.fee\" type=\"number\" @input=\"calculateTotals\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column width=\"70\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"danger\" link size=\"small\" @click=\"removeConsumptionDetail(scope.$index)\"> 删除 </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-bottom: 16px\">\r\n            <el-button type=\"primary\" @click=\"addConsumptionDetail\"> 添加消费明细 </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-section\">\r\n          <h3 class=\"section-title\">付款明细</h3>\r\n          <el-table :data=\"editableBillData?.paymentDetails || []\" style=\"width: 100%\">\r\n            <el-table-column prop=\"subName\" label=\"账目\" min-width=\"160\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.subName\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fee\" label=\"金额\" min-width=\"100\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.fee\" type=\"number\" @input=\"calculateTotals\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column width=\"70\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"danger\" link size=\"small\" @click=\"removePaymentDetail(scope.$index)\"> 删除 </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-top: 16px\">\r\n            <el-button type=\"primary\" @click=\"addPaymentDetail\"> 添加付款明细 </el-button>\r\n          </div>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n    <template #footer>\r\n      <template v-if=\"!editDialogVisible\">\r\n        <!-- 报表页底部按钮（如无可省略） -->\r\n      </template>\r\n      <template v-else>\r\n        <el-button @click=\"onEditCancel\"> 复原 </el-button>\r\n        <el-button type=\"primary\" @click=\"onEditSave\"> 保存账单 </el-button>\r\n      </template>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.loading-container {\r\n  padding: 20px;\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n:deep(.el-dialog__body) {\r\n  max-height: 70vh;\r\n  padding: 0;\r\n}\r\n\r\n:deep(.el-dialog__footer) {\r\n  position: sticky;\r\n  bottom: 0;\r\n  background-color: #fff;\r\n  z-index: 1;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\r\n  padding: 15px;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n\r\n  .section-title {\r\n    margin: 0 0 20px 0;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\n.form-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr); /* 每行 2 个输入框 */\r\n  gap: 16px; /* 行间距和列间距 */\r\n  max-width: 100%; /* 适应容器宽度 */\r\n}\r\n\r\n.full-width {\r\n  grid-column: 1 / -1;\r\n}\r\n\r\n.el-form-item {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "Stimulsoft", "window", "t", "useI18n", "userStore", "useUserStore", "data", "ref", "tableAutoHeight", "showWidth", "loading", "myVisible", "computed", "get", "modelValue", "set", "val", "originalBillData", "editableBillData", "editDialogVisible", "posBillData", "onMounted", "async", "Promise", "all", "getPrintLayout", "getPrintInfo", "layout", "PrintFormat", "POS", "toString", "isPosFormat", "value", "printApi", "gcode", "hcode", "billCode", "BillType", "BILL", "then", "res", "code", "_a", "layout_width_map", "printFormApi", "printSingleRoomBill", "orderNo", "JSON", "parse", "stringify", "autoShowPrint", "nextTick", "setTimeout", "printReport", "<PERSON><PERSON><PERSON>", "error", "console", "ElMessage", "dialogWidth", "timestamp", "Date", "now", "reportInstance", "reportReady", "json", "licensePath", "URL", "url", "href", "Base", "StiLicense", "loadFromFile", "report", "Report", "StiReport", "A4", "loadFile", "A412", "A413", "localizationFile", "useSettingsStore", "lang", "Localization", "StiLocalization", "setLocalizationFile", "dataSet", "System", "Data", "DataSet", "read<PERSON>son", "dictionary", "databases", "clear", "regData", "viewerOptions", "Viewer", "StiViewerOptions", "toolbar", "visible", "viewer", "StiViewer", "renderHtml", "printFrame", "document", "createElement", "style", "position", "right", "bottom", "width", "height", "border", "body", "append<PERSON><PERSON><PERSON>", "printContent", "querySelector", "innerHTML", "frameDoc", "_b", "contentWindow", "write", "close", "onload", "print", "<PERSON><PERSON><PERSON><PERSON>", "hideDialog", "e", "warning", "onCancel", "openEditDialog", "onEditSave", "onEditCancel", "addConsumptionDetail", "consumptionDetails", "push", "subCode", "subName", "fee", "createTime", "toISOString", "slice", "replace", "calculateTotals", "addPaymentDetail", "paymentDetails", "consumeTotal", "reduce", "sum", "item", "Number", "consumeTotalFee", "toFixed", "payTotal", "payTotalFee", "watch", "deep", "index", "splice"], "mappings": "8/DA6BA,MAAMA,GAAQC,EAeRC,GAAQC,EAMRC,GAAaC,OAAOD,YAEpBE,EAAEA,IAAMC,IACRC,GAAYC,IACZC,GAAOC,EAAI,CACfC,iBAAiB,EACjBC,UAAW,UAEPC,GAAUH,GAAI,GACdI,GAAYC,EAAS,CACzBC,IAAM,IACGjB,GAAMkB,WAEf,GAAAC,CAAIC,GACFlB,GAAM,oBAAqBkB,EAAG,IAI5BC,GAAmBV,EAAI,MACvBW,GAAmBX,EAAI,MACvBY,GAAoBZ,GAAI,GACxBa,GAAcb,EAAI,MAExBc,GAAUC,gBACFC,QAAQC,IAAI,CAACC,KAAkBC,MAAe,IAGtD,MAAMC,GAASpB,EAAIqB,EAAYC,IAAIC,YAE7BC,GAAcnB,GAAS,IACpBe,GAAOK,QAAUJ,EAAYC,IAAIC,aAG1CR,eAAeG,KACbQ,EACGR,eAAe,CACdS,MAAO9B,GAAU8B,MACjBC,MAAO/B,GAAU+B,MACjBC,SAAUC,EAASC,OAEpBC,MAAMC,UACY,IAAbA,EAAIC,OACCd,GAAAK,MAAQQ,EAAIlC,KAAKqB,OACnBrB,GAAA0B,MAAMvB,WAAY,OAAAiC,EAAiBC,EAAA9B,IAAIc,GAAOK,iBAAQF,aAAc,QAAA,GAE5E,CAGLR,eAAeI,KACbhB,GAAQsB,OAAQ,EACZ,IACI,MAAAQ,QAAYI,EAAaC,oBAAoB,CACjDX,MAAO9B,GAAU8B,MACjBC,MAAO/B,GAAU+B,MACjBW,QAASlD,GAAMkD,UAEA,IAAbN,EAAIC,OACNxB,GAAiBe,MAAQe,KAAKC,MAAMD,KAAKE,UAAUT,EAAIlC,OACvDY,GAAiBc,MAAQe,KAAKC,MAAMD,KAAKE,UAAUT,EAAIlC,OACnDyB,GAAYC,OACdZ,GAAYY,MAAQQ,EAAIlC,KAGpBV,GAAMsD,eACRC,GAAS,KACPC,YAAW,KACGC,IAAA,GACX,IAAG,KAIVC,GAAQd,EAAIlC,aAGTiD,GACCC,QAAAD,MAAM,YAAaA,GAC3BE,EAAUF,MAAM,WAAU,CAC1B,QACA7C,GAAQsB,OAAQ,CAAA,CAClB,CAGI,MAAA0B,GAAc9C,GAAS,IACpBO,GAAkBa,MAAQ,QAAU1B,GAAK0B,MAAMvB,YAGlDkD,GAAYC,KAAKC,MACnB,IAAAC,GACE,MAAAC,GAAcxD,GAAI,GAExBe,eAAegC,GAAQU,GACrBD,GAAY/B,OAAQ,EACpB,MAAMiC,EAAc,IAAoDC,IAAA,4xBAAAC,KAAAC,WAClEpE,GAAWqE,KAAKC,WAAWC,aAAaN,GAC9C,MAAMO,EAAS,IAAIxE,GAAWyE,OAAOC,UAEjC/C,GAAOK,QAAUJ,EAAY+C,GAAG7C,WAC3B0C,EAAAI,SAAS,+FAA+FjB,MAEtGhC,GAAOK,QAAUJ,EAAYiD,KAAK/C,WACpC0C,EAAAI,SAAS,mGAAmGjB,MAE1GhC,GAAOK,QAAUJ,EAAYkD,KAAKhD,WACpC0C,EAAAI,SAAS,sCAETJ,EAAAI,SAAS,kCAGlB,MAEMG,EAAmB,GAFHC,IACgBC,WAEtCjF,GAAWqE,KAAKa,aAAaC,gBAAgBC,oBAAoBL,GAEjE,MAAMM,EAAU,IAAIrF,GAAWsF,OAAOC,KAAKC,QAAQ,QACnDH,EAAQI,SAAS1C,KAAKE,UAAUe,IACzBQ,EAAAkB,WAAWC,UAAUC,QACrBpB,EAAAqB,QAAQ,OAAQ,OAAQR,GAE/B,MAAMS,EAAgB,IAAI9F,GAAW+F,OAAOC,iBAC5CF,EAAcG,QAAQC,SAAU,EAChC,MAAMC,EAAS,IAAInG,GAAW+F,OAAOK,UAAUN,EAAe,aAAa,GAC3EK,EAAO3B,OAASA,EAChB2B,EAAOE,WAAW,UACDvC,GAAAU,EACjB9D,GAAQsB,OAAQ,EAChB+B,GAAY/B,OAAQ,EAGhBpC,GAAMsD,eACRE,YAAW,KACGC,IAAA,GACX,IACL,CAGF,SAASA,aAIP,GAFAvD,GAAM,iBAEFiC,GAAYC,MAAO,CAEf,MAAAsE,EAAaC,SAASC,cAAc,UAC1CF,EAAWG,MAAMC,SAAW,QAC5BJ,EAAWG,MAAME,MAAQ,IACzBL,EAAWG,MAAMG,OAAS,IAC1BN,EAAWG,MAAMI,MAAQ,IACzBP,EAAWG,MAAMK,OAAS,IAC1BR,EAAWG,MAAMM,OAAS,IAEjBR,SAAAS,KAAKC,YAAYX,GAC1B,MAAMY,GAAe,OAAAxE,EAAS6D,SAAAY,cAAc,4BAAmBC,YAAa,GACtEC,EAAW,OAAAC,EAAWhB,EAAAiB,oBAAe,EAAAD,EAAAf,SAC3C,IAAKc,EAEH,YADA7D,QAAQD,MAAM,iBAGhB8D,EAASG,MAAM,0zEAmFLN,iDAIVG,EAASI,QAETnB,EAAWoB,OAAS,KACd,IACEpB,EAAWiB,cACbjB,EAAWiB,cAAcI,QAEzBnE,QAAQD,MAAM,cAEhBH,YAAW,KACAmD,SAAAS,KAAKY,YAAYtB,GAEtB1G,GAAMiI,aACRlH,GAAUqB,OAAQ,EAAA,GAEnB,WACI8F,GACCtE,QAAAD,MAAM,QAASuE,EAAC,EAE5B,KACK,CACL,IAAK/D,GAAY/B,QAAU8B,GAEzB,YADAL,EAAUsE,QAAQ,kBAGpBjE,GAAe6D,QAEX/H,GAAMiI,YACRzE,YAAW,KACTzC,GAAUqB,OAAQ,CAAA,GACjB,IACL,CACF,CAGF,SAASgG,KACPrH,GAAUqB,OAAQ,CAAA,CAGpB,SAASiG,KACP9G,GAAkBa,OAAQ,CAAA,CAG5BV,eAAe4G,KACb/G,GAAkBa,OAAQ,QACpBmB,IACFpB,GAAYC,MACdZ,GAAYY,MAAQe,KAAKC,MAAMD,KAAKE,UAAU/B,GAAiBc,QAE/DsB,GAAQpC,GAAiBc,MAC3B,CAGFV,eAAe6G,KACbhH,GAAkBa,OAAQ,EAC1Bd,GAAiBc,MAAQe,KAAKC,MAAMD,KAAKE,UAAUhC,GAAiBe,cAC9DmB,IACFpB,GAAYC,MACdZ,GAAYY,MAAQe,KAAKC,MAAMD,KAAKE,UAAUhC,GAAiBe,QAE/DsB,GAAQrC,GAAiBe,MAC3B,CAGF,SAASoG,KACFlH,GAAiBc,MAAMqG,qBACTnH,GAAAc,MAAMqG,mBAAqB,IAE7BnH,GAAAc,MAAMqG,mBAAmBC,KAAK,CAC7CC,QAAS,GACTC,QAAS,GACTC,IAAK,EACLC,YAAY,IAAI9E,MAAO+E,cAAcC,MAAM,EAAG,IAAIC,QAAQ,IAAK,OAEjDC,IAAA,CAQlB,SAASC,KACF7H,GAAiBc,MAAMgH,iBACT9H,GAAAc,MAAMgH,eAAiB,IAEzB9H,GAAAc,MAAMgH,eAAeV,KAAK,CACzCC,QAAS,GACTC,QAAS,GACTC,IAAK,EACLC,YAAY,IAAI9E,MAAO+E,cAAcC,MAAM,EAAG,IAAIC,QAAQ,IAAK,OAEjDC,IAAA,CASlB,SAASA,aAED,MAAAG,GACJ,OAAAvG,KAAiBV,MAAMqG,6BAAoBa,QAAO,CAACC,EAAKC,IAC/CD,GAAOE,OAAOD,EAAKX,MAAQ,IACjC,KAAM,EACXvH,GAAiBc,MAAMsH,gBAAkBD,OAAOJ,EAAaM,QAAQ,IAG/D,MAAAC,GACJ,OAAAlC,KAAiBtF,MAAMgH,yBAAgBE,QAAO,CAACC,EAAKC,IAC3CD,GAAOE,OAAOD,EAAKX,MAAQ,IACjC,KAAM,EACXvH,GAAiBc,MAAMyH,YAAcJ,OAAOG,EAASD,QAAQ,GAAE,QAIjEG,GACE,WAAM,OAAA,OAAAhH,EAAAxB,GAAiBc,YAAO,EAAAU,EAAA2F,kBAAA,IAC9B,KACkBS,IAAA,GAElB,CAAEa,MAAM,IAGVD,GACE,WAAM,OAAA,OAAAhH,EAAAxB,GAAiBc,YAAO,EAAAU,EAAAsG,cAAA,IAC9B,KACkBF,IAAA,GAElB,CAAEa,MAAM,0+HAtDuBC,WAC/B1I,GAAiBc,MAAMqG,mBAAmBwB,OAAOD,EAAO,QACxCd,KAFlB,IAAiCc,u1BAkBJA,WAC3B1I,GAAiBc,MAAMgH,eAAea,OAAOD,EAAO,QACpCd,KAFlB,IAA6Bc"}