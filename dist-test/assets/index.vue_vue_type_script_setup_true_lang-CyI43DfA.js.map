{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-CyI43DfA.js", "sources": ["../../src/views/group/group-price/price-approval/components/FormMode/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { DetailFormProps } from '../../types'\r\nimport DetailForm from '../DetailForm/index.vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n  } & DetailFormProps>(),\r\n  {\r\n    modelValue: false,\r\n  },\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [\r\n    value: boolean,\r\n  ]\r\n  'editStatus': [\r\n    value: boolean,\r\n  ]\r\n  'success': []\r\n}>()\r\n\r\nconst formRef = ref()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst title = computed(() => {\r\n  let t = '新增价格审批'\r\n  if (props.handle === 'create') {\r\n    t = '新增价格审批'\r\n  }\r\n  if (props.handle === 'edit') {\r\n    t = '编辑价格审批'\r\n  }\r\n  return t\r\n})\r\n\r\nfunction onSubmit() {\r\n  // submit() 为组件内部方法\r\n  formRef.value.submit().then(() => {\r\n    emits('success')\r\n    onCancel()\r\n  })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog\r\n      v-if=\"props.handle === 'create'\" v-model=\"myVisible\" :title=\"title\" width=\"500px\"\r\n      :close-on-click-modal=\"false\" append-to-body destroy-on-close\r\n    >\r\n      <DetailForm ref=\"formRef\" v-bind=\"props\" />\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel()\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          确定\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n    <el-dialog\r\n      v-if=\"props.handle === 'edit'\" v-model=\"myVisible\" :title=\"title\" size=\"500px\"\r\n      :close-on-click-modal=\"false\" destroy-on-close\r\n    >\r\n      <DetailForm ref=\"formRef\" v-bind=\"props\" />\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel()\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          确定\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "emits", "__emit", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "title", "t", "handle", "onSubmit", "value", "submit", "then", "onCancel"], "mappings": "2bAIA,MAAMA,EAAQC,EASRC,EAAQC,EAURC,EAAUC,IAEVC,EAAYC,EAAS,CACzBC,IAAM,IACGR,EAAMS,WAEf,GAAAC,CAAIC,GACFT,EAAM,oBAAqBS,EAAG,IAI5BC,EAAQL,GAAS,KACrB,IAAIM,EAAI,SAOD,MANc,WAAjBb,EAAMc,SACJD,EAAA,UAEe,SAAjBb,EAAMc,SACJD,EAAA,UAECA,CAAA,IAGT,SAASE,IAEPX,EAAQY,MAAMC,SAASC,MAAK,KAC1BhB,EAAM,WACGiB,GAAA,GACV,CAGH,SAASA,IACPb,EAAUU,OAAQ,CAAA"}