{"version": 3, "file": "priceHourRule.api-Bxzs45RC.js", "sources": ["../../src/api/modules/pms/pricerule/priceHourRule.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/price-hour-rule'\r\n/**\r\n * 钟点房计费规则\r\n */\r\nexport default {\r\n  /**\r\n   * 更新钟点房计费规则\r\n   * @param data\r\n   */\r\n  // updatePriceHourRule: (data: any) => {\r\n  //     return api.put(`${BASE_PATH}/update`, data);\r\n  // },\r\n  updatePriceHourRule: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n  /**\r\n   * 获得钟点房计费规则\r\n   * @param data\r\n   */\r\n  // getPriceHourRule: (data: {\r\n  //     gcode: string,\r\n  //     hcode: string,\r\n  // }) => {\r\n  //     return api.get(`${BASE_PATH}/get`, {params: data});\r\n  // }\r\n  getPriceHourRule: (data: any) => api.get(`${BASE_PATH}/get`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "priceHourRuleApi", "updatePriceHourRule", "data", "api", "put", "getPriceHourRule", "get", "params"], "mappings": "wCAEA,MAAMA,EAAY,gCAIHC,EAAA,CAQbC,oBAAsBC,GAAcC,EAAIC,IAAI,GAAGL,WAAoBG,GAWnEG,iBAAmBH,GAAcC,EAAIG,IAAI,GAAGP,QAAiB,CAAEQ,OAAQL"}