import{d as t,aj as e,ai as o,b as a,B as i,y as s,ae as l,r as n,av as r,o as p,c,u as m,f as d,R as u,g as b,w as g,F as h,ag as j,h as v,Y as k,b6 as f,e as y,bD as C,l as _,j as D,k as T,q as x,aS as N,x as w,bF as M,bt as O,a7 as S,cu as z,aL as P,E as L,bP as Y,bE as V,bz as A,aX as I,bG as q}from"./index-CkEhI1Zk.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 *//* empty css                 *//* empty css                         *//* empty css                *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                 */import{b as E}from"./book.api-ERXvEXQF.js";import{d as R}from"./dictData.api-DUabpYqy.js";import{O as B,N as U,i as F,k as G}from"./constants-Cg3j_uH4.js";import{D as J}from"./DictTypeEnum-DKIIlHnN.js";import H from"./order-ChSzi_-7.js";import K from"./event-bus-CDHrRX6w.js";import{u as $}from"./usePagination-DYjsSSf4.js";import{_ as Q}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                   */import"./index-i2MX-1er.js";/* empty css                    *//* empty css                    */import"./order.api-B-JCVvq6.js";import"./account-Dw8d3GK0.js";/* empty css                   */import"./index-3RMLzyhA.js";import"./index-ADu0XAHG.js";/* empty css                        *//* empty css                          *//* empty css                       *//* empty css                  *//* empty css                          */import"./el-form-item-l0sNRNKZ.js";/* empty css                        *//* empty css                        *//* empty css                *//* empty css                *//* empty css               *//* empty css               *//* empty css                        *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-CJlU19fC.js";import"./account.api-CSMEUacF.js";import"./rentGoods.api-IR0dWMfk.js";import"./orderBill-CgM-5HXN.js";import"./print.api-DEqPqKcI.js";import"./printForm.api-BOg76CjP.js";import"./posBillForm-Cigy56-i.js";import"./togetherBill-ByLEtbff.js";import"./index-BqUz2moK.js";import"./generalConfig.api-CEBBd8kx.js";import"./index-Dlhx8lGo.js";import"./index-M2JMYKA8.js";import"./consume-DgDuQkgE.js";import"./index-DAulSAJI.js";import"./index-D8c6PuWt.js";/* empty css                */import"./index-CDbn0nBx.js";import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";import"./member.api-2tU9HGvl.js";import"./arSet.api-BZHDDSla.js";import"./hotelParamConfig.api-CbdvhUfn.js";import"./indemnityGoods.api-BzuE6zcC.js";import"./retailGoods.api-CPINo1es.js";import"./auth.api-C96jzWEY.js";import"./decimal-gPLAeiS8.js";import"./payment-vLdXRLoR.js";import"./checkinForm-DTcWmPmJ.js";import"./posCheckInForm-BJfHKK6-.js";import"./preAuth-CG1cg58P.js";import"./index-K7z-WsFs.js";import"./index.vue_vue_type_script_setup_true_lang-DK1kOKr4.js";/* empty css                */import"./couponConfig.api-DtISSIXY.js";import"./CouponDialog-DTl141Zt.js";import"./coupon.api-aMKZ7FC4.js";/* empty css                       */import"./index-B36WBY8p.js";import"./index-C6K_fo9Y.js";import"./index-_28SpMym.js";/* empty css                   */import"./index-DnGZTrHX.js";import"./list-cSBPeYXE.js";/* empty css                      */import"./index.vue_vue_type_script_setup_true_lang-DvSmdj52.js";import"./index-Cjr3dIX4.js";import"./detail-Dh370UMq.js";import"./rentCompensation-DGZqTwko.js";/* empty css                   */import"./rentCreate-DgokBdtt.js";import"./rent.api-DzgTHAr9.js";import"./rentEdit-kpM-6Ev1.js";import"./rentReturn-Bn7G8O-o.js";import"./goodsModal-DNVgoATn.js";import"./grantModal-S8hNNB6E.js";import"./invoicedModal-XEk1BZXP.js";import"./remark-D99iiFr3.js";import"./splitAccount-DpqmmImE.js";import"./bookingDetail-BY3bduLn.js";import"./serverTime.api-D89oCqKL.js";import"./timeutils-Ib6GkGcq.js";import"./cancelPopUP-BbPXaQdi.js";import"./arrangeRooms-CPfs5GXR.js";import"./index-CkWKDwTG.js";import"./index-Eu7Cs0xe.js";import"./checkModal-tyH9Ceqi.js";/* empty css                        */import"./CardReader-BeR26SIt.js";import"./myStringUtil-D79vpWmP.js";import"./sensitive-la8iBfdn.js";import"./log-BF-F0S6C.js";import"./roomLog.api-D1y-EVTQ.js";import"./user.api-BYl7ypOS.js";import"./orderDetail-B1c5B-Jp.js";import"./customer.api-dB3m63zZ.js";import"./device.api-BsgckoMw.js";import"./roomCardLog.api-pw0J1hl7.js";import"./roomCardUtil-DBQw7z7m.js";import"./index-CYIY_xy7.js";import"./utils-S8-xpbSs.js";import"./index-DcUuNG9v.js";import"./route-block-B_A1xBdJ.js";import"./roomContinue-Cwa93jZh.js";/* empty css                       */import"./roomExchange-DyqICf4D.js";import"./arrangeRooms-DLQ6Ij2m.js";import"./roomCardLogList-DIxcpNbT.js";import"./orderList-DtQU5x9j.js";import"./mergeForm-C0XQeWLX.js";import"./teamBookDetail-CaYBewxN.js";import"./arrangeRts-C83hWsgy.js";import"./GeneralConfigTypeEnum-DERqowgl.js";import"./teamMainOrder-RmJelicD.js";import"./teamReception-BVmeD-Jb.js";const W={class:"order-list-container"},X={key:0,class:"operation-tips"},Z={class:"search-area"},tt={class:"channel-selector"},et={class:"filter-group"},ot={class:"order-list-wrapper"},at={class:"order-list"},it=["onClick","onDblclick"],st={key:0,class:"operation-hint"},lt={key:1,class:"new-order-badge"},nt={class:"order-header"},rt={class:"order-status"},pt={class:"order-number selectable-text"},ct={key:0,class:"order-channel"},mt={class:"check-in-time selectable-text"},dt={class:"order-content"},ut={class:"room-info"},bt={class:"room-count-list"},gt={class:"room-num"},ht={class:"room-type-list"},jt={class:"selectable-text"},vt={class:"guest-info"},kt={class:"guest-name selectable-text"},ft={class:"order-footer"},yt={class:"stay-duration selectable-text"},Ct={class:"stay-dates selectable-text"},_t={class:"view-details"},Dt={class:"pagination-container"},Tt=t({name:"RoomOrderList",__name:"rightOrderList",setup(t){const{t:Q}=e(),Tt=o(),xt=a(""),Nt=i((()=>[{value:"-1",label:Q("options.all")},{value:"lobby",label:Q("options.lobby")},{value:"ctrip",label:Q("options.ctrip")},{value:"meituan",label:Q("options.meituan")},{value:"fliggy",label:Q("options.fliggy")},{value:"tiktok",label:Q("options.tiktok")},{value:"jd",label:Q("options.jd")},{value:"xiaohongshu",label:Q("options.xiaohongshu")}])),wt=a("detail"),Mt=a("individual"),Ot=a(null);let St=null;const zt=a(!0);function Pt(t){return Ot.value===t.bookNo}function Lt(){zt.value=!1,localStorage.setItem("order-operation-tips-seen","true")}s((()=>{xt.value=Nt.value[0].value,function(){const t=[B.OVER,B.CANCEL,B.NOSHOW,B.IN_BOOKING];R.getDictDataBatch(qt).then((e=>{Et.value=e.data.filter((e=>e.dictType===J.BOOK_STATUS&&t.includes(e.code)))}))}(),Ut();localStorage.getItem("order-operation-tips-seen")&&(zt.value=!1),K.on("sse_message_ota_order",(t=>{!function(t){if(t.messageType===F.OTA_ORDER)!function(t){let e;try{e="string"==typeof t?JSON.parse(t):t}catch(o){return void console.error("Error parsing clientData:",o)}if(e&&e.clientId){let t=e.data;"string"==typeof t&&(t=JSON.parse(t));const o={...t,state:t.state||"no_check_in",contact:t.guestName,roomTypeClass:[{rtCode:t.rtCode,rtName:t.rtName,num:t.roomNum}],createTime:new Date};Rt.value.newOrderIds.add(o.bookNo),setTimeout((()=>{Rt.value.newOrderIds.delete(o.bookNo)}),3e5),Rt.value.dataList.unshift(o)}else console.warn("clientData 或 clientId 无效:",e)}(t);else console.warn("未知消息类型:",t.messageType)}(t)})),K.on("sse_message_ota_order_cancel",(t=>{!function(t){if(t.messageType===F.OTA_ORDER_CANCEL)!function(t){var e;let o;try{o="string"==typeof t?JSON.parse(t):t}catch(a){return void console.error("Error parsing clientData:",a)}if(o&&o.clientId){let t=o.data;if("string"==typeof t)try{t=JSON.parse(t)}catch(a){return void console.error("Error parsing orderData:",a)}const i=t.bookNo||(null==(e=t.message)?void 0:e.bookNo);if(!i)return void console.warn("无法获取订单号，orderData:",t);const s=Rt.value.dataList.findIndex((t=>t.bookNo===i));-1!==s&&(Rt.value.dataList.splice(s,1),Rt.value.newOrderIds.delete(i))}else console.warn("clientData 或 clientId 无效:",o)}(t);else console.warn("未知消息类型:",t.messageType)}(t)}))})),l((()=>{K.off("sse_message_ota_order"),K.off("sse_message_ota_order_cancel")}));const{pagination:Yt,getParams:Vt,onSizeChange:At,onCurrentChange:It}=$(),qt=[J.BOOK_STATUS],Et=a([]);const Rt=a({loading:!1,tableAutoHeight:!1,formModeProps:{visible:!1,bookNo:"",noType:U.BOOK},search:{state:B.IN_BOOKING.toString(),channelCode:"-1",searchContent:"",dateType:"-1"},dataList:[],newOrderIds:new Set}),Bt=n({gcode:Tt.gcode,hcode:Tt.hcode});function Ut(){Rt.value.loading=!0;const t={...Vt(),...Bt,channelCode:"-1"===Rt.value.search.channelCode?"":Rt.value.search.channelCode,keyWords:Rt.value.search.searchContent,timeType:"2",state:"-1"===Rt.value.search.state?"":Rt.value.search.state};if("0"===Rt.value.search.dateType){const e=r().format("YYYY-MM-DD");t.startTime=`${e} 00:00:00`,t.endTime=`${e} 23:59:59`}else if("1"===Rt.value.search.dateType){const e=r().format("YYYY-MM-DD"),o=r().add(7,"day").format("YYYY-MM-DD");t.startTime=`${e} 00:00:00`,t.endTime=`${o} 23:59:59`}E.bookPageList(t).then((t=>{Rt.value.loading=!1,Rt.value.dataList=t.data.list,Yt.value.total=t.data.total}))}function Ft(t=1){It(t).then((()=>Ut()))}const Gt=i((()=>[{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/tiktok.png",channelCode:"tiktok",channelName:Q("channels.tiktok")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/fliggy.png",channelCode:"fliggy",channelName:Q("channels.fliggy")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/meituan.png",channelCode:"meituan",channelName:Q("channels.meituan")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/store.png",channelCode:"lobby",channelName:Q("channels.lobby")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/elong.png",channelCode:"elong",channelName:Q("channels.elong")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/quar.png",channelCode:"qunar",channelName:Q("channels.qunar")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/ctrip.png",channelCode:"ctrip",channelName:Q("channels.ctrip")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/logo.png",channelCode:"mini_app",channelName:Q("channels.miniApp")},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/jd.png",channelCode:"jd",channelName:Q("channels.jd")}]));function Jt(t){const e=Gt.value.find((e=>e.channelCode===t));return null==e?void 0:e.image}async function Ht(t){Rt.value.formModeProps.bookNo=t.bookNo,Rt.value.formModeProps.visible=!0,t.bookType===G.GROUP?Rt.value.formModeProps.noType=U.TEAM:Rt.value.formModeProps.noType=U.BOOK}function Kt(t){return Rt.value.newOrderIds.has(t.bookNo)}return(t,e)=>{const o=C,a=L,i=_,s=D,l=T,n=x,E=N,R=w,B=Y,U=M,F=V,G=A,J=I,K=q,$=O;return p(),c("div",W,[m(zt)?(p(),c("div",X,[d(o,{title:m(Q)("operationTips"),type:"info",closable:!0,"show-icon":"",onClose:Lt},null,8,["title"])])):u("",!0),b("div",Z,[d(R,{model:m(Rt).search,class:"search-form"},{default:g((()=>[b("div",tt,[d(i,{modelValue:m(Rt).search.channelCode,"onUpdate:modelValue":e[0]||(e[0]=t=>m(Rt).search.channelCode=t),size:"small",onChange:e[1]||(e[1]=t=>Ut())},{default:g((()=>[(p(!0),c(h,null,j(m(Nt),((t,e)=>(p(),y(a,{key:e,value:t.value},{default:g((()=>[v(k(t.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"])]),b("div",et,[d(l,{modelValue:m(Rt).search.state,"onUpdate:modelValue":e[2]||(e[2]=t=>m(Rt).search.state=t),size:"small",onChange:e[3]||(e[3]=t=>Ut())},{default:g((()=>[d(s,{label:m(Q)("allStatus"),value:"-1"},{default:g((()=>[v(k(m(Q)("allStatus")),1)])),_:1},8,["label"]),(p(!0),c(h,null,j(m(Et),((t,e)=>(p(),y(s,{key:e,label:t.label,value:t.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),d(l,{modelValue:m(Rt).search.dateType,"onUpdate:modelValue":e[4]||(e[4]=t=>m(Rt).search.dateType=t),size:"small",onChange:e[5]||(e[5]=t=>Ut())},{default:g((()=>[d(s,{label:m(Q)("checkinDate"),value:"-1"},null,8,["label"]),d(s,{label:m(Q)("todayCheckin"),value:"0"},null,8,["label"]),d(s,{label:m(Q)("nextSevenDays"),value:"1"},null,8,["label"])])),_:1},8,["modelValue"]),d(E,{modelValue:m(Rt).search.searchContent,"onUpdate:modelValue":e[7]||(e[7]=t=>m(Rt).search.searchContent=t),placeholder:m(Q)("searchPlaceholder"),size:"small",clearable:"",onClear:e[8]||(e[8]=t=>Ut())},{append:g((()=>[d(n,{icon:m(f),size:"small",onClick:e[6]||(e[6]=t=>Ut())},null,8,["icon"])])),_:1},8,["modelValue","placeholder"])])])),_:1},8,["model"])]),b("div",ot,[b("div",at,[m(Rt).loading?(p(),y(B,{key:0,rows:10,animated:""})):0===m(Rt).dataList.length?(p(),y(U,{key:1,description:m(Q)("noData"),"image-size":100},null,8,["description"])):(p(!0),c(h,{key:2},j(m(Rt).dataList,((t,o)=>(p(),c("div",{key:o,class:S(["order-card",{"order-card--selected":Pt(t)}]),onClick:e=>function(t,e){const o=window.getSelection();if(o&&o.toString().length>0)return;const a=e.target;"SPAN"===a.tagName||a.classList.contains("selectable-text")||(e.preventDefault(),St&&(clearTimeout(St),St=null),St=setTimeout((()=>{Ot.value===t.bookNo?Ot.value=null:Ot.value=t.bookNo}),200))}(t,e),onDblclick:e=>function(t,e){e.preventDefault(),St&&(clearTimeout(St),St=null),Ht(t)}(t,e)},[Pt(t)?u("",!0):(p(),c("div",st,[d(F,{class:"click-icon"},{default:g((()=>[d(m(z))])),_:1})])),Kt(t)?(p(),c("div",lt,[d(G,{type:"danger",effect:"dark",size:"small"},{default:g((()=>e[10]||(e[10]=[v(" 新订单 ")]))),_:1})])):u("",!0),b("div",nt,[b("div",rt,[d(G,{type:"no_check_in"===t.state?"primary":"info",effect:"light",size:"small"},{default:g((()=>{var e;return[v(k((null==(e=m(Et).find((e=>e.code===t.state)))?void 0:e.label)||t.state),1)]})),_:2},1032,["type"])]),b("div",pt,k(t.outOrderNo?t.outOrderNo:t.bookNo),1),Jt(t.channelCode)?(p(),c("div",ct,[Jt(t.channelCode)?(p(),y(J,{key:0,class:"channel-icon",src:Jt(t.channelCode),fit:"cover"},null,8,["src"])):u("",!0)])):u("",!0),b("div",mt,k(m(r)(t.planCheckinTime).format("MM/DD HH:mm")),1)]),b("div",dt,[b("div",ut,[b("div",bt,[(p(!0),c(h,null,j(t.roomTypeClass||[],((t,e)=>(p(),c("div",{key:e,class:"room-count-item selectable-text"},[b("span",gt,k(t.num),1),v(k(m(Q)("rooms")),1)])))),128))]),b("div",ht,[(p(!0),c(h,null,j(t.roomTypeClass,((t,e)=>(p(),c("div",{key:e,class:"room-type-item selectable-text"},[t.rtName&&t.rtName.length>23?(p(),y(K,{key:0,effect:"dark",placement:"top-start",content:t.rtName},{default:g((()=>[b("span",jt,k(t.rtName),1)])),_:2},1032,["content"])):(p(),c(h,{key:1},[v(k(t.rtName),1)],64))])))),128))])]),b("div",vt,[b("div",kt,k(t.contact),1)])]),b("div",ft,[b("div",yt,[m(r)(t.planCheckinTime).format("YYYY-MM-DD")!==m(r)(t.planCheckoutTime).format("YYYY-MM-DD")?(p(),c(h,{key:0},[v(k(m(r)(t.planCheckoutTime).diff(m(r)(t.planCheckinTime),"day")+1)+" "+k(m(Q)("nights")),1)],64)):u("",!0)]),b("div",Ct,k(m(r)(t.planCheckinTime).format("MM/DD"))+" - "+k(m(r)(t.planCheckoutTime).format("MM/DD")),1),b("div",_t,[d(n,{type:"primary",size:"small",link:"",onClick:P((e=>Ht(t)),["stop"])},{default:g((()=>[v(k(m(Q)("viewDetails")),1)])),_:2},1032,["onClick"])])])],42,it)))),128))])]),b("div",Dt,[m(Yt).total>10&&!m(Rt).loading?(p(),y($,{key:0,"current-page":m(Yt).pageNo,total:m(Yt).total,"page-size":m(Yt).pageSize,layout:"prev, pager, next","hide-on-single-page":!1,class:"pagination",background:"",size:"small",onCurrentChange:Ft},null,8,["current-page","total","page-size"])):u("",!0)]),m(Rt).formModeProps.visible?(p(),y(H,{key:1,modelValue:m(Rt).formModeProps.visible,"onUpdate:modelValue":e[9]||(e[9]=t=>m(Rt).formModeProps.visible=t),no:m(Rt).formModeProps.bookNo,"no-type":m(Rt).formModeProps.noType,"tab-name":m(wt),"tab-type":m(Mt),onSuccess:Ut},null,8,["modelValue","no","no-type","tab-name","tab-type"])):u("",!0)])}}});function xt(t){const e=t;e.__i18n=e.__i18n||[],e.__i18n.push({locale:"",resource:{en:{viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"Details"}},more:{t:0,b:{t:2,i:[{t:3}],s:"More"}},allStatus:{t:0,b:{t:2,i:[{t:3}],s:"All"}},checkinDate:{t:0,b:{t:2,i:[{t:3}],s:"Check-in"}},todayCheckin:{t:0,b:{t:2,i:[{t:3}],s:"Today"}},nextSevenDays:{t:0,b:{t:2,i:[{t:3}],s:"Next 7D"}},searchPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Name/Phone/Order"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"No data"}},nights:{t:0,b:{t:2,i:[{t:3}],s:"N"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"R"}},operationTips:{t:0,b:{t:2,i:[{t:3}],s:"Tips: Click to select order, Double-click to open detail dialog"}},clickToSelect:{t:0,b:{t:2,i:[{t:3}],s:"Click to select"}},doubleClickToViewDialog:{t:0,b:{t:2,i:[{t:3}],s:"Double-click to open detail dialog"}},channels:{tiktok:{t:0,b:{t:2,i:[{t:3}],s:"TikTok"}},fliggy:{t:0,b:{t:2,i:[{t:3}],s:"Fliggy"}},meituan:{t:0,b:{t:2,i:[{t:3}],s:"Meituan"}},lobby:{t:0,b:{t:2,i:[{t:3}],s:"Lobby"}},elong:{t:0,b:{t:2,i:[{t:3}],s:"Elong"}},qunar:{t:0,b:{t:2,i:[{t:3}],s:"Qunar"}},ctrip:{t:0,b:{t:2,i:[{t:3}],s:"Ctrip"}},miniApp:{t:0,b:{t:2,i:[{t:3}],s:"Mini App"}},jd:{t:0,b:{t:2,i:[{t:3}],s:"JD"}}},options:{all:{t:0,b:{t:2,i:[{t:3}],s:"All"}},lobby:{t:0,b:{t:2,i:[{t:3}],s:"Lobby"}},meituan:{t:0,b:{t:2,i:[{t:3}],s:"Meituan"}},ctrip:{t:0,b:{t:2,i:[{t:3}],s:"Ctrip"}},fliggy:{t:0,b:{t:2,i:[{t:3}],s:"Fliggy"}},tiktok:{t:0,b:{t:2,i:[{t:3}],s:"TikTok"}},xiaohongshu:{t:0,b:{t:2,i:[{t:3}],s:"XHS"}},jd:{t:0,b:{t:2,i:[{t:3}],s:"JD"}}}},"zh-cn":{viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"详情"}},more:{t:0,b:{t:2,i:[{t:3}],s:"更多"}},allStatus:{t:0,b:{t:2,i:[{t:3}],s:"全部"}},checkinDate:{t:0,b:{t:2,i:[{t:3}],s:"全部日期"}},todayCheckin:{t:0,b:{t:2,i:[{t:3}],s:"今日"}},nextSevenDays:{t:0,b:{t:2,i:[{t:3}],s:"未来7天"}},searchPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"姓名/手机/订单号"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"暂无数据"}},nights:{t:0,b:{t:2,i:[{t:3}],s:"晚"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"间"}},operationTips:{t:0,b:{t:2,i:[{t:3}],s:"提示：单击选中订单，双击打开详情"}},clickToSelect:{t:0,b:{t:2,i:[{t:3}],s:"单击选中"}},doubleClickToViewDialog:{t:0,b:{t:2,i:[{t:3}],s:"双击打开详情"}},channels:{tiktok:{t:0,b:{t:2,i:[{t:3}],s:"抖音"}},fliggy:{t:0,b:{t:2,i:[{t:3}],s:"飞猪"}},meituan:{t:0,b:{t:2,i:[{t:3}],s:"美团"}},lobby:{t:0,b:{t:2,i:[{t:3}],s:"门店"}},elong:{t:0,b:{t:2,i:[{t:3}],s:"艺龙"}},qunar:{t:0,b:{t:2,i:[{t:3}],s:"去哪儿"}},ctrip:{t:0,b:{t:2,i:[{t:3}],s:"携程"}},miniApp:{t:0,b:{t:2,i:[{t:3}],s:"小程序"}},jd:{t:0,b:{t:2,i:[{t:3}],s:"京东"}}},options:{all:{t:0,b:{t:2,i:[{t:3}],s:"全部"}},lobby:{t:0,b:{t:2,i:[{t:3}],s:"门店"}},meituan:{t:0,b:{t:2,i:[{t:3}],s:"美团"}},ctrip:{t:0,b:{t:2,i:[{t:3}],s:"携程"}},fliggy:{t:0,b:{t:2,i:[{t:3}],s:"飞猪"}},tiktok:{t:0,b:{t:2,i:[{t:3}],s:"抖音"}},xiaohongshu:{t:0,b:{t:2,i:[{t:3}],s:"小红书"}},jd:{t:0,b:{t:2,i:[{t:3}],s:"京东"}}}},km:{viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានលម្អិត"}},more:{t:0,b:{t:2,i:[{t:3}],s:"ច្រើនទៀត"}},allStatus:{t:0,b:{t:2,i:[{t:3}],s:"ទាំងអស់"}},checkinDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទចូល"}},todayCheckin:{t:0,b:{t:2,i:[{t:3}],s:"ថ្ងៃនេះ"}},nextSevenDays:{t:0,b:{t:2,i:[{t:3}],s:"៧ថ្ងៃបន្ទាប់"}},searchPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ/ទូរស័ព្ទ/លេខកម្មង់"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានទិន្នន័យ"}},nights:{t:0,b:{t:2,i:[{t:3}],s:"យប់"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់"}},operationTips:{t:0,b:{t:2,i:[{t:3}],s:"ជំនួយ: ចុចដើម្បីជ្រើសរើសកម្មង់, ចុចពីរដងដើម្បីបើកផ្ទាំងព័ត៌មានលម្អិត"}},clickToSelect:{t:0,b:{t:2,i:[{t:3}],s:"ចុចដើម្បីជ្រើសរើស"}},doubleClickToViewDialog:{t:0,b:{t:2,i:[{t:3}],s:"ចុចពីរដងដើម្បីបើកផ្ទាំងព័ត៌មានលម្អិត"}},channels:{tiktok:{t:0,b:{t:2,i:[{t:3}],s:"TikTok"}},fliggy:{t:0,b:{t:2,i:[{t:3}],s:"Fliggy"}},meituan:{t:0,b:{t:2,i:[{t:3}],s:"Meituan"}},lobby:{t:0,b:{t:2,i:[{t:3}],s:"ហាង"}},elong:{t:0,b:{t:2,i:[{t:3}],s:"Elong"}},qunar:{t:0,b:{t:2,i:[{t:3}],s:"Qunar"}},ctrip:{t:0,b:{t:2,i:[{t:3}],s:"Ctrip"}},miniApp:{t:0,b:{t:2,i:[{t:3}],s:"កម្មវិធីតូច"}},jd:{t:0,b:{t:2,i:[{t:3}],s:"JD"}}},options:{all:{t:0,b:{t:2,i:[{t:3}],s:"ទាំងអស់"}},lobby:{t:0,b:{t:2,i:[{t:3}],s:"ហាង"}},meituan:{t:0,b:{t:2,i:[{t:3}],s:"Meituan"}},ctrip:{t:0,b:{t:2,i:[{t:3}],s:"Ctrip"}},fliggy:{t:0,b:{t:2,i:[{t:3}],s:"Fliggy"}},tiktok:{t:0,b:{t:2,i:[{t:3}],s:"TikTok"}},xiaohongshu:{t:0,b:{t:2,i:[{t:3}],s:"XHS"}},jd:{t:0,b:{t:2,i:[{t:3}],s:"JD"}}}}}})}xt(Tt);const Nt=Q(Tt,[["__scopeId","data-v-5670ea46"]]);export{Nt as default};
//# sourceMappingURL=rightOrderList-Tnl_KmfV.js.map
