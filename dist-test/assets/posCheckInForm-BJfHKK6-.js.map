{"version": 3, "file": "posCheckInForm-BJfHKK6-.js", "sources": ["../../src/views/print/posCheckInForm.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { useI18n } from 'vue-i18n'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst props = defineProps({\r\n  formData: {\r\n    type: Object,\r\n    required: true,\r\n  },\r\n})\r\n\r\nonMounted(() => {})\r\n\r\n// 格式化日期时间\r\nconst formatDateTime = (dateStr) => {\r\n  if (!dateStr) {\r\n    return ''\r\n  }\r\n  return dateStr\r\n}\r\n\r\n// 酒店信息\r\nconst hotelInfo = computed(() => {\r\n  return {\r\n    name: props.formData.hname,\r\n    phone: props.formData.frontPhone || '',\r\n    address: props.formData.address || '',\r\n  }\r\n})\r\n\r\n// 当前时间\r\nconst currentTime = ref(props.formData.printDate || new Date().toLocaleString())\r\n\r\n// 同住人信息\r\nconst togetherGuests = computed(() => {\r\n  return props.formData.togetherRespVOList || []\r\n})\r\n\r\n// 格式化生日为 yyyy/mm/dd\r\nconst formatBirthday = (dateStr) => {\r\n  if (!dateStr) {\r\n    return ''\r\n  }\r\n  try {\r\n    const date = new Date(dateStr)\r\n    return `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`\r\n  } catch (e) {\r\n    return dateStr\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"pos-print-container\">\r\n    <div class=\"hotel-title\">{{ hotelInfo.name }}</div>\r\n    <div class=\"form-title\">入住登记单</div>\r\n    <div class=\"order-info\">\r\n      <div class=\"order-no\">NO:{{ props.formData.orderNo }}</div>\r\n      <div class=\"print-time\">打印时间:{{ currentTime }}</div>\r\n    </div>\r\n\r\n    <table class=\"info-table\">\r\n      <tbody>\r\n        <tr>\r\n          <th>房号</th>\r\n          <td colspan=\"3\">{{ props.formData.rNo }}({{ props.formData.buildingName }} - {{ props.formData.floorName }})</td>\r\n        </tr>\r\n        <tr>\r\n          <th>首日价</th>\r\n          <td>{{ props.formData.firstDayFee }}</td>\r\n          <th>房型</th>\r\n          <td>{{ props.formData.rtName }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>房包早</th>\r\n          <td>{{ props.formData.roomBkNum || 0 }}</td>\r\n          <th>赠早</th>\r\n          <td>{{ props.formData.bkNum || 0 }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>姓名</th>\r\n          <td>{{ props.formData.name }}</td>\r\n          <th>民族</th>\r\n          <td>{{ props.formData.nation || '' }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>手机号</th>\r\n          <td colspan=\"3\">{{ props.formData.phone || '' }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>证件类型</th>\r\n          <td colspan=\"3\">{{ props.formData.idTypeName }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>证件号</th>\r\n          <td colspan=\"3\">{{ props.formData.idNo }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>性别</th>\r\n          <td colspan=\"3\">{{ props.formData.sex }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>出生日期</th>\r\n          <td colspan=\"3\">{{ formatBirthday(props.formData.birthday) }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>地址</th>\r\n          <td colspan=\"3\">{{ props.formData.userAddress || '' }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>入住时间</th>\r\n          <td colspan=\"3\">{{ formatDateTime(props.formData.checkinTime) }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>离店时间</th>\r\n          <td colspan=\"3\">{{ formatDateTime(props.formData.checkoutTime) }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>入住天数</th>\r\n          <td colspan=\"3\">{{ props.formData.nights }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>{{ props.formData.subName }}</th>\r\n          <td colspan=\"3\">{{ props.formData.fee || 0 }}</td>\r\n        </tr>\r\n\t\t\t\t<tr>\r\n\t\t\t\t\t<th>订单备注</th>\r\n\t\t\t\t\t<td colspan=\"3\">{{ props.formData.orderRemark || '' }}</td>\r\n\t\t\t\t</tr>\r\n        <tr>\r\n          <th>备注</th>\r\n          <td colspan=\"3\">{{ props.formData.remark || '' }}</td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 同住人信息 -->\r\n    <div v-if=\"togetherGuests.length > 0\" class=\"together-guests\">\r\n      <div class=\"section-title\">同住人</div>\r\n      <table class=\"info-table together-table\">\r\n        <tbody>\r\n          <tr v-for=\"(guest, index) in togetherGuests\" :key=\"index\">\r\n            <td>姓名：{{ guest.name || '-' }} 手机号：{{ guest.phone || '-' }} 证件号：{{ guest.idNo || '-' }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <div class=\"footer\">\r\n      <div class=\"footer-line\">责任签名：</div>\r\n      <div class=\"footer-line\">酒店电话：{{ hotelInfo.phone }}</div>\r\n      <div class=\"footer-line\">酒店地址：{{ hotelInfo.address }}</div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.pos-print-container {\r\n  width: 76mm;\r\n  margin: 0 auto;\r\n  padding: 2mm;\r\n  font-size: 14px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  border: 1px solid #000;\r\n  color: black;\r\n  padding: 5mm;\r\n}\r\n\r\n.hotel-title {\r\n  text-align: center;\r\n  font-size: 30px;\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.order-info {\r\n  width: 95%;\r\n  text-align: left;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.order-no,\r\n.print-time {\r\n  margin-top: 5px;\r\n  text-align: left;\r\n  font-size: 12px;\r\n}\r\n\r\n.form-title {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.info-table {\r\n  width: 95%;\r\n  border-collapse: collapse;\r\n  margin-bottom: 10px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.info-table,\r\n.info-table th,\r\n.info-table td {\r\n  border: 1px solid #000;\r\n}\r\n\r\n.info-table th {\r\n  text-align: left;\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n  width: 25%;\r\n}\r\n\r\n.info-table td {\r\n  padding: 3px 5px;\r\n  text-align: left;\r\n  font-size: 12px;\r\n}\r\n\r\n.section-title {\r\n  text-align: left;\r\n  font-weight: bold;\r\n  margin: 10px 0 5px 0;\r\n  width: 95%;\r\n}\r\n\r\n.together-guests {\r\n  margin-top: 10px;\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.together-table {\r\n  width: 95%;\r\n}\r\n\r\n.footer {\r\n  width: 95%;\r\n  margin-top: 10px;\r\n  font-size: 12px;\r\n}\r\n\r\n.footer-line {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"], "names": ["useI18n", "useUserStore", "props", "__props", "onMounted", "formatDateTime", "dateStr", "hotelInfo", "computed", "name", "formData", "hname", "phone", "frontPhone", "address", "currentTime", "ref", "printDate", "Date", "toLocaleString", "togetherGuests", "togetherRespVOList", "formatBirthday", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "e"], "mappings": "4vBAKsBA,IACSC,IAE/B,MAAMC,EAAQC,EAOdC,GAAU,SAGJ,MAAAC,EAAkBC,GACjBA,GACI,GAMLC,EAAYC,GAAS,KAClB,CACLC,KAAMP,EAAMQ,SAASC,MACrBC,MAAOV,EAAMQ,SAASG,YAAc,GACpCC,QAASZ,EAAMQ,SAASI,SAAW,OAKjCC,EAAcC,EAAId,EAAMQ,SAASO,gBAAiBC,MAAOC,kBAGzDC,EAAiBZ,GAAS,IACvBN,EAAMQ,SAASW,oBAAsB,KAIxCC,EAAkBhB,IACtB,IAAKA,EACI,MAAA,GAEL,IACI,MAAAiB,EAAO,IAAIL,KAAKZ,GACf,MAAA,GAAGiB,EAAKC,iBAAiBC,OAAOF,EAAKG,WAAa,GAAGC,SAAS,EAAG,QAAQF,OAAOF,EAAKK,WAAWD,SAAS,EAAG,aAC5GE,GACA,OAAAvB,CAAA"}