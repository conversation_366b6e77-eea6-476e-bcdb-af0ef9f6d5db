{"version": 3, "file": "memberset-Da6F6FHO.js", "sources": ["../../src/views/group/system/config/components/memberset.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"smsReminder\": \"SMS Reminder\",\r\n    \"birthday\": \"Birthday\",\r\n    \"point\": \"Point Expiry\",\r\n    \"money\": \"Balance Change\",\r\n    \"level\": \"Level Change\",\r\n    \"registerChangeFee\": \"Allow manual modification of registration fee\",\r\n    \"cancelNum\": \"Allowed number of cancellations per day\",\r\n    \"bookAheadDays\": \"Maximum days to book in advance\",\r\n    \"memberInfoSearch\": \"Member info search control\",\r\n    \"searchUnrestricted\": \"Unrestricted, display all\",\r\n    \"searchPrecise\": \"Precise search by name, phone, or ID\",\r\n    \"pointsWithNonOwnerCard\": \"Points for non-owner card holders\",\r\n    \"ptsCouponsTogether\": \"Can points and coupons be used together\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"save\": \"Save\",\r\n    \"cancel\": \"Cancel\",\r\n    \"edit\": \"Edit\",\r\n    \"successMessage\": \"Modification successful\",\r\n    \"errorMessage\": \"Error: {message}\",\r\n    \"noLimit\": \"No limit\",\r\n    \"pendingPaymentTime\": \"Auto-cancel unpaid orders after minutes:\",\r\n    \"pendingPaymentTooltip\": \"Online channels: WeChat booking mini-program\",\r\n    \"minutes\": \"minutes\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"smsReminder\": \"短信提醒\",\r\n    \"birthday\": \"生日\",\r\n    \"point\": \"积分过期\",\r\n    \"money\": \"余额变动\",\r\n    \"level\": \"等级变动\",\r\n    \"registerChangeFee\": \"会员注册进行现付或者挂房账时,是否允许手工修改金额\",\r\n    \"cancelNum\": \"会员每日允许取消预订单的次数\",\r\n    \"bookAheadDays\": \"会员最大提前多少天预订\",\r\n    \"memberInfoSearch\": \"会员列表信息查询管控\",\r\n    \"searchUnrestricted\": \"不限制，全部展示\",\r\n    \"searchPrecise\": \"通过姓名，手机号，证件号精确查询\",\r\n    \"pointsWithNonOwnerCard\": \"非会员本人入住是否积分\",\r\n    \"ptsCouponsTogether\": \"积分抵扣消费是否可以与优惠券同时使用\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"save\": \"保存\",\r\n    \"cancel\": \"取消\",\r\n    \"edit\": \"编辑\",\r\n    \"successMessage\": \"修改成功\",\r\n    \"errorMessage\": \"错误: {message}\",\r\n    \"noLimit\": \"不限制\",\r\n    \"pendingPaymentTime\": \"未支付订单多少分钟内自动取消：\",\r\n    \"pendingPaymentTooltip\": \"线上渠道:微信订房小程序\",\r\n    \"minutes\": \"分钟\"\r\n  },\r\n  \"km\": {\r\n    \"smsReminder\": \"ការរំលឹក SMS\",\r\n    \"birthday\": \"ថ្ងៃកំណើត\",\r\n    \"point\": \"ការផុតកំណត់ពិន្ទុ\",\r\n    \"money\": \"ការផ្លាស់ប្តូរសមតុល្យ\",\r\n    \"level\": \"ការផ្លាស់ប្តូរកម្រិត\",\r\n    \"registerChangeFee\": \"អនុញ្ញាតឱ្យកែប្រែថ្លៃចុះឈ្មោះដោយដៃ\",\r\n    \"cancelNum\": \"ចំនួនការលុបចោលដែលអនុញ្ញាតក្នុងមួយថ្ងៃ\",\r\n    \"bookAheadDays\": \"ចំនួនថ្ងៃអតិបរមាសម្រាប់ការកក់ជាមុន\",\r\n    \"memberInfoSearch\": \"ការគ្រប់គ្រងការស្វែងរកព័ត៌មានសមាជិក\",\r\n    \"searchUnrestricted\": \"មិនមានការកំណត់ បង្ហាញទាំងអស់\",\r\n    \"searchPrecise\": \"ស្វែងរកដោយភាពជាក់លាក់តាមឈ្មោះ លេខទូរសព្ទ ឬលេខអត្តសញ្ញាណ\",\r\n    \"pointsWithNonOwnerCard\": \"ពិន្ទុសម្រាប់អ្នកកាន់កាតមិនមែនម្ចាស់\",\r\n    \"ptsCouponsTogether\": \"តើពិន្ទុនិងកូប៉ុងអាចប្រើជាមួយគ្នាបានទេ\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"edit\": \"កែសម្រួល\",\r\n    \"successMessage\": \"កែប្រែបានជោគជ័យ\",\r\n    \"errorMessage\": \"កំហុស: {message}\",\r\n    \"noLimit\": \"មិនមានការកំណត់\",\r\n    \"pendingPaymentTime\": \"លុបចោលការបញ្ជាទិញដែលមិនទាន់បង់ប្រាក់ក្រោយពេល៖\",\r\n    \"pendingPaymentTooltip\": \"ឆានែលអនឡាញ៖ កម្មវិធីតូចកក់បន្ទប់ WeChat\",\r\n    \"minutes\": \"នាទី\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { groupParamConfigApi } from '@/api/modules/index'\r\nimport { DictTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst loading = ref(false)\r\nconst isEdit = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 参数类型 */\r\n  paramType: DictTypeEnum.MEMBERCONFIG,\r\n  /** 参数值 json对象 paramType的不同，json对象也不同 */\r\n  value: {\r\n    smsNotice: {\r\n      birthday: '1',\r\n      point: '1',\r\n      money: '1',\r\n      level: '1',\r\n    },\r\n    registerChangeFee: '0',\r\n    cancelNum: 0,\r\n    bookAheadDays: 15,\r\n    memberInfoSearch: '0',\r\n    pointsWithNonOwnerCard: '1',\r\n    ptsCouponsTogether: '0',\r\n    pendingPaymentTime: 0,\r\n  },\r\n})\r\n\r\nconst formRules = ref<FormRules>({})\r\n\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  groupParamConfigApi.getGroupParamConfigMember(userStore.gcode).then((res: any) => {\r\n    form.value = res.data\r\n    loading.value = false\r\n  })\r\n}\r\n\r\nfunction onEdit() {\r\n  return new Promise<void>((resolve) => {\r\n    formRef.value &&\r\n      formRef.value.validate((valid) => {\r\n        if (valid) {\r\n          groupParamConfigApi.updateGroupParamConfig(form.value).then((res: any) => {\r\n            if (res.code === 0) {\r\n              ElMessage.success({\r\n                message: t('successMessage'),\r\n                center: true,\r\n              })\r\n              isEdit.value = false\r\n              resolve()\r\n            } else {\r\n              ElMessage.error({\r\n                message: t('errorMessage', { message: res.msg }),\r\n                center: true,\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"ml-[20px]\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-suffix=\"：\" label-width=\"380px\">\r\n      <el-form-item :label=\"t('smsReminder')\">\r\n        <el-checkbox v-model=\"form.value.smsNotice.birthday\" true-value=\"1\" false-value=\"0\" :disabled=\"!isEdit\">\r\n          {{ t('birthday') }}\r\n        </el-checkbox>\r\n        <el-checkbox v-model=\"form.value.smsNotice.point\" true-value=\"1\" false-value=\"0\" :disabled=\"!isEdit\">\r\n          {{ t('point') }}\r\n        </el-checkbox>\r\n        <el-checkbox v-model=\"form.value.smsNotice.money\" true-value=\"1\" false-value=\"0\" :disabled=\"!isEdit\">\r\n          {{ t('money') }}\r\n        </el-checkbox>\r\n        <el-checkbox v-model=\"form.value.smsNotice.level\" true-value=\"1\" false-value=\"0\" :disabled=\"!isEdit\">\r\n          {{ t('level') }}\r\n        </el-checkbox>\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('registerChangeFee')\">\r\n        <el-switch v-model=\"form.value.registerChangeFee\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" :disabled=\"!isEdit\" />\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('cancelNum')\">\r\n        <el-input-number v-model=\"form.value.cancelNum\" :min=\"0\" :disabled=\"!isEdit\" />\r\n        <div class=\"el-form-item-msg\">\r\n          {{ t('noLimit') }}\r\n        </div>\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('bookAheadDays')\">\r\n        <el-input-number v-model=\"form.value.bookAheadDays\" :min=\"0\" :disabled=\"!isEdit\" />\r\n        <div class=\"el-form-item-msg\">\r\n          {{ t('noLimit') }}\r\n        </div>\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('memberInfoSearch')\">\r\n        <el-radio-group v-model=\"form.value.memberInfoSearch\" :disabled=\"!isEdit\">\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('searchPrecise') }}\r\n          </el-radio>\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('searchUnrestricted') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('pointsWithNonOwnerCard')\">\r\n        <el-radio-group v-model=\"form.value.pointsWithNonOwnerCard\" :disabled=\"!isEdit\">\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('yes') }}\r\n          </el-radio>\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('no') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('ptsCouponsTogether')\">\r\n        <el-radio-group v-model=\"form.value.ptsCouponsTogether\" :disabled=\"!isEdit\">\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('yes') }}\r\n          </el-radio>\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('no') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <template #label>\r\n          <span>{{ t('pendingPaymentTime') }}</span>\r\n          <div class=\"ml-[3px] pt-[2px]\">\r\n            <el-tooltip :content=\"t('pendingPaymentTooltip')\" placement=\"top-start\" effect=\"dark\">\r\n              <el-icon><QuestionFilled /></el-icon>\r\n            </el-tooltip>\r\n          </div>\r\n        </template>\r\n        <el-input-number v-model=\"form.value.pendingPaymentTime\" :controls=\"false\" :precision=\"0\" :min=\"0\" :disabled=\"!isEdit\" /><span class=\"ml-10px\">{{ t('minutes') }}</span>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button v-if=\"!isEdit\" type=\"primary\" plain @click=\"isEdit = !isEdit\">\r\n          {{ t('edit') }}\r\n        </el-button>\r\n        <el-button v-if=\"isEdit\" @click=\"isEdit = !isEdit\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button v-if=\"isEdit\" type=\"primary\" @click=\"onEdit\">\r\n          {{ t('save') }}\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-menu-item.is-active) {\r\n  color: #0488fb !important;\r\n  background-color: #e7effb !important;\r\n}\r\n\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "isEdit", "formRef", "form", "gcode", "paramType", "DictTypeEnum", "MEMBERCONFIG", "value", "smsNotice", "birthday", "point", "money", "level", "registerChangeFee", "cancelNum", "bookAheadDays", "memberInfoSearch", "pointsWithNonOwnerCard", "ptsCouponsTogether", "pendingPaymentTime", "formRules", "onEdit", "Promise", "resolve", "validate", "valid", "groupParamConfigApi", "updateGroupParamConfig", "then", "res", "code", "ElMessage", "success", "message", "center", "error", "msg", "onMounted", "getGroupParamConfigMember", "data"], "mappings": "w5BAyFA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IAERC,EAAUC,GAAI,GACdC,EAASD,GAAI,GACbE,EAAUF,IACVG,EAAOH,EAAI,CAEfI,MAAOT,EAAUS,MAEjBC,UAAWC,EAAaC,aAExBC,MAAO,CACLC,UAAW,CACTC,SAAU,IACVC,MAAO,IACPC,MAAO,IACPC,MAAO,KAETC,kBAAmB,IACnBC,UAAW,EACXC,cAAe,GACfC,iBAAkB,IAClBC,uBAAwB,IACxBC,mBAAoB,IACpBC,mBAAoB,KAIlBC,EAAYrB,EAAe,IAcjC,SAASsB,IACA,OAAA,IAAIC,SAAeC,IACxBtB,EAAQM,OACNN,EAAQM,MAAMiB,UAAUC,IAClBA,GACFC,EAAoBC,uBAAuBzB,EAAKK,OAAOqB,MAAMC,IAC1C,IAAbA,EAAIC,MACNC,EAAUC,QAAQ,CAChBC,QAASrC,EAAE,kBACXsC,QAAQ,IAEVlC,EAAOO,OAAQ,EACPgB,KAERQ,EAAUI,MAAM,CACdF,QAASrC,EAAE,eAAgB,CAAEqC,QAASJ,EAAIO,MAC1CF,QAAQ,GACT,GAEJ,GAEJ,GACJ,QAlCHG,GAAU,KAKRvC,EAAQS,OAAQ,EAChBmB,EAAoBY,0BAA0B5C,EAAUS,OAAOyB,MAAMC,IACnE3B,EAAKK,MAAQsB,EAAIU,KACjBzC,EAAQS,OAAQ,CAAA,GAPV"}