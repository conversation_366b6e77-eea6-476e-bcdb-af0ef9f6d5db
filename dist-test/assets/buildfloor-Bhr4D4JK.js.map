{"version": 3, "file": "buildfloor-Bhr4D4JK.js", "sources": ["../../src/views/merchant/base/room/components/DetailForm/buildfloor.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"addBuildingFloor\": \"Add Building Floor\",\r\n    \"addType\": \"Add Type\",\r\n    \"building\": \"Building\",\r\n    \"floor\": \"Floor\",\r\n    \"superior\": \"Superior\",\r\n    \"pleaseSelectSuperior\": \"Please select superior, default is top level\",\r\n    \"buildingName\": \"Building\",\r\n    \"floorName\": \"Floor Name\",\r\n    \"pleaseEnterName\": \"Please enter name\",\r\n    \"createBuildingSuccess\": \"Building created successful\",\r\n    \"createFloorSuccess\": \"Floor created successful\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"addBuildingFloor\": \"新增楼栋楼层\",\r\n    \"addType\": \"新增类型\",\r\n    \"building\": \"楼栋\",\r\n    \"floor\": \"楼层\",\r\n    \"superior\": \"上级\",\r\n    \"pleaseSelectSuperior\": \"请选择上级，默认为顶级\",\r\n    \"buildingName\": \"楼栋名称\",\r\n    \"floorName\": \"楼层名称\",\r\n    \"pleaseEnterName\": \"请输入名称\",\r\n    \"createBuildingSuccess\": \"楼栋创建成功\",\r\n    \"createFloorSuccess\": \"楼层创建成功\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\"\r\n  },\r\n  \"km\": {\r\n    \"addBuildingFloor\": \"បន្ថែមអាគារ និងជាន់\",\r\n    \"addType\": \"បន្ថែមប្រភេទ\",\r\n    \"building\": \"អាគារ\",\r\n    \"floor\": \"ជាន់\",\r\n    \"superior\": \"ថ្នាក់លើ\",\r\n    \"pleaseSelectSuperior\": \"សូមជ្រើសរើសថ្នាក់លើ លំនាំដើមគឺថ្នាក់កំពូល\",\r\n    \"buildingName\": \"ឈ្មោះអាគារ\",\r\n    \"floorName\": \"ឈ្មោះជាន់\",\r\n    \"pleaseEnterName\": \"សូមបញ្ចូលឈ្មោះ\",\r\n    \"createBuildingSuccess\": \"បង្កើតអាគារដោយជោគជ័យ\",\r\n    \"createFloorSuccess\": \"បង្កើតជាន់ដោយជោគជ័យ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\n\r\nimport type { DetailFormProps } from '../../types'\r\nimport { buildingFloorApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { handleTree } from '@/utils/tree'\r\n\r\nconst props = withDefaults(\r\n  defineProps<\r\n    {\r\n      modelValue?: boolean\r\n    } & DetailFormProps\r\n  >(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  parentCode: '0',\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  name: '',\r\n  isFloor: '',\r\n  typecode: '0',\r\n})\r\nconst formRules = ref<FormRules>({\r\n  name: [{ required: true, message: t('pleaseEnterName'), trigger: 'blur' }],\r\n})\r\nconst myVisible = ref(props.modelValue)\r\nconst myTree = ref([])\r\n\r\nonMounted(() => {\r\n  getBuildFloor()\r\n})\r\n\r\nfunction getBuildFloor() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  buildingFloorApi.list(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      myTree.value = handleTree(res.data)\r\n\r\n      myTree.value.map((item) => {\r\n        delete item.children\r\n        return item\r\n        // if (item.children) {\r\n        //   item.children.forEach((child) => {\r\n        //     child.disabled = true\r\n        //   })\r\n        // }\r\n      })\r\n    }\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        let params\r\n        let successMessage\r\n\r\n        if (form.value.typecode === '0') {\r\n          params = {\r\n            gcode: userStore.gcode,\r\n            hcode: userStore.hcode,\r\n            name: form.value.name,\r\n            parentCode: '0',\r\n            isFloor: '0',\r\n          }\r\n          successMessage = t('createBuildingSuccess')\r\n        } else {\r\n          params = {\r\n            gcode: userStore.gcode,\r\n            hcode: userStore.hcode,\r\n            name: form.value.name,\r\n            parentCode: form.value.parentCode,\r\n            isFloor: '1',\r\n          }\r\n          successMessage = t('createFloorSuccess')\r\n        }\r\n\r\n        buildingFloorApi.create(params).then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success({\r\n              message: successMessage,\r\n              center: true,\r\n            })\r\n            onCancel()\r\n            emits('success')\r\n          } else {\r\n            ElMessage.error({\r\n              message: res.msg,\r\n              center: true,\r\n            })\r\n          }\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\nfunction typeName() {\r\n  form.value.name = form.value.name ? '' : form.value.name\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('addBuildingFloor')\" width=\"500px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close @closed=\"emits('update:modelValue', false)\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"120px\">\r\n      <el-form-item :label=\"t('addType')\">\r\n        <el-radio-group v-model=\"form.typecode\" @change=\"typeName\">\r\n          <el-radio value=\"0\">\r\n            {{ t('building') }}\r\n          </el-radio>\r\n          <el-radio value=\"1\">\r\n            {{ t('floor') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"form.typecode === '1'\" :label=\"t('superior')\" prop=\"code\">\r\n        <el-cascader\r\n          v-model=\"form.parentCode\"\r\n          :options=\"myTree\"\r\n          :props=\"{\r\n            value: 'code',\r\n            label: 'name',\r\n            emitPath: false,\r\n          }\"\r\n          :show-all-levels=\"false\"\r\n          :placeholder=\"t('pleaseSelectSuperior')\"\r\n          clearable\r\n        />\r\n      </el-form-item>\r\n      <el-form-item :label=\"form.typecode === '1' ? t('floorName') : t('buildingName')\" prop=\"name\">\r\n        <el-input v-model=\"form.name\" :placeholder=\"t('pleaseEnterName')\" clearable maxlength=\"30\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('save') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-cascader) {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "formRef", "ref", "form", "parentCode", "gcode", "hcode", "name", "isFloor", "typecode", "formRules", "required", "message", "trigger", "myVisible", "modelValue", "myTree", "onSubmit", "value", "validate", "valid", "params", "successMessage", "buildingFloorApi", "create", "then", "res", "code", "ElMessage", "success", "center", "onCancel", "error", "msg", "typeName", "onMounted", "list", "handleTree", "data", "map", "item", "children", "getBuildFloor"], "mappings": "2+BA0DA,MAAMA,EAAQC,EAURC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,IACVC,EAAOD,EAAI,CACfE,WAAY,IACZC,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,MACjBC,KAAM,GACNC,QAAS,GACTC,SAAU,MAENC,EAAYR,EAAe,CAC/BK,KAAM,CAAC,CAAEI,UAAU,EAAMC,QAASf,EAAE,mBAAoBgB,QAAS,WAE7DC,EAAYZ,EAAIT,EAAMsB,YACtBC,EAASd,EAAI,IA4BnB,SAASe,IACPhB,EAAQiB,OACNjB,EAAQiB,MAAMC,UAAUC,IACtB,GAAIA,EAAO,CACL,IAAAC,EACAC,EAEwB,MAAxBnB,EAAKe,MAAMT,UACJY,EAAA,CACPhB,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,MACjBC,KAAMJ,EAAKe,MAAMX,KACjBH,WAAY,IACZI,QAAS,KAEXc,EAAiBzB,EAAE,2BAEVwB,EAAA,CACPhB,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,MACjBC,KAAMJ,EAAKe,MAAMX,KACjBH,WAAYD,EAAKe,MAAMd,WACvBI,QAAS,KAEXc,EAAiBzB,EAAE,uBAGrB0B,EAAiBC,OAAOH,GAAQI,MAAMC,IACnB,IAAbA,EAAIC,MACNC,EAAUC,QAAQ,CAChBjB,QAASU,EACTQ,QAAQ,IAEDC,IACTpC,EAAM,YAENiC,EAAUI,MAAM,CACdpB,QAASc,EAAIO,IACbH,QAAQ,GACT,GAEJ,IAEJ,CAGL,SAASC,IACPjB,EAAUI,OAAQ,CAAA,CAEpB,SAASgB,IACP/B,EAAKe,MAAMX,KAAOJ,EAAKe,MAAMX,KAAO,GAAKJ,EAAKe,MAAMX,IAAA,QA5EtD4B,GAAU,MAIV,WACE,MAAMd,EAAS,CACbhB,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,OAEnBiB,EAAiBa,KAAKf,GAAQI,MAAMC,IACjB,IAAbA,EAAIC,OACCX,EAAAE,MAAQmB,EAAWX,EAAIY,MAEvBtB,EAAAE,MAAMqB,KAAKC,WACTA,EAAKC,SACLD,KAMR,GAEJ,CAtBaE,EAAA"}