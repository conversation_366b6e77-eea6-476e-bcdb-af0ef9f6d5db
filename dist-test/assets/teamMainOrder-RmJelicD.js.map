{"version": 3, "file": "teamMainOrder-RmJelicD.js", "sources": ["../../src/views/order/info/components/orderdetail/teamMainOrder.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"teamInfo\": {\r\n        \"title\": \"Team Information\",\r\n        \"teamName\": \"Team Name\",\r\n        \"contact\": \"Contact Person\",\r\n        \"plannedArrivalTime\": \"Planned Arrival Time\"\r\n      },\r\n      \"teamType\": {\r\n        \"meetingGroup\": \"Meeting Group\",\r\n        \"travelGroup\": \"Travel Group\",\r\n        \"other\": \"Other\"\r\n      },\r\n      \"statistics\": {\r\n        \"roomStatistics\": \"Room Statistics\",\r\n        \"totalRooms\": \"Total Rooms\",\r\n        \"checkedInRooms\": \"Checked-in Rooms\",\r\n        \"checkedInPersons\": \"Checked-in Persons\",\r\n        \"creditRooms\": \"Credit Rooms\",\r\n        \"creditPersons\": \"Credit Persons\",\r\n        \"settledRooms\": \"Settled Rooms\",\r\n        \"settledPersons\": \"Settled Persons\"\r\n      },\r\n      \"roomType\": {\r\n        \"distribution\": \"Room Type Distribution\",\r\n        \"roomUnit\": \" rooms\"\r\n      },\r\n      \"remark\": {\r\n        \"title\": \"Remarks\"\r\n      },\r\n      \"status\": {\r\n        \"checkedIn\": \"Checked In\",\r\n        \"checkedOut\": \"Checked Out\",\r\n        \"credit\": \"Credit\",\r\n        \"unknown\": \"Unknown\"\r\n      },\r\n      \"actions\": {\r\n        \"printTeamRegistration\": \"Print Team Registration Form\"\r\n      },\r\n      \"common\": {\r\n        \"none\": \"None\"\r\n      }\r\n    },\r\n    \"zh-cn\": {\r\n      \"teamInfo\": {\r\n        \"title\": \"团队信息\",\r\n        \"teamName\": \"团队名称\",\r\n        \"contact\": \"联系人\",\r\n        \"plannedArrivalTime\": \"预抵时间\"\r\n      },\r\n      \"teamType\": {\r\n        \"meetingGroup\": \"会议团\",\r\n        \"travelGroup\": \"旅行团\",\r\n        \"other\": \"其他\"\r\n      },\r\n      \"statistics\": {\r\n        \"roomStatistics\": \"房间统计\",\r\n        \"totalRooms\": \"总房间数\",\r\n        \"checkedInRooms\": \"已入住房间\",\r\n        \"checkedInPersons\": \"入住人数\",\r\n        \"creditRooms\": \"挂账房间\",\r\n        \"creditPersons\": \"挂账人数\",\r\n        \"settledRooms\": \"已结账房间\",\r\n        \"settledPersons\": \"已结账人数\"\r\n      },\r\n      \"roomType\": {\r\n        \"distribution\": \"房型分布\",\r\n        \"roomUnit\": \"间\"\r\n      },\r\n      \"remark\": {\r\n        \"title\": \"备注信息\"\r\n      },\r\n      \"status\": {\r\n        \"checkedIn\": \"在住\",\r\n        \"checkedOut\": \"退房\",\r\n        \"credit\": \"挂账\",\r\n        \"unknown\": \"未知\"\r\n      },\r\n      \"actions\": {\r\n        \"printTeamRegistration\": \"打印团队登记单\"\r\n      },\r\n      \"common\": {\r\n        \"none\": \"暂无\"\r\n      }\r\n    },\r\n    \"km\": {\r\n      \"teamInfo\": {\r\n        \"title\": \"ព័ត៌មានក្រុម\",\r\n        \"teamName\": \"ឈ្មោះក្រុម\",\r\n        \"contact\": \"អ្នកទំនាក់ទំនង\",\r\n        \"plannedArrivalTime\": \"ពេលវេលាមកដល់ដែលបានគ្រោង\"\r\n      },\r\n      \"teamType\": {\r\n        \"meetingGroup\": \"ក្រុមប្រជុំ\",\r\n        \"travelGroup\": \"ក្រុមទេសចរណ៍\",\r\n        \"other\": \"ផ្សេងៗ\"\r\n      },\r\n      \"statistics\": {\r\n        \"roomStatistics\": \"ស្ថិតិបន្ទប់\",\r\n        \"totalRooms\": \"បន្ទប់សរុប\",\r\n        \"checkedInRooms\": \"បន្ទប់ដែលបានចូលស្នាក់\",\r\n        \"checkedInPersons\": \"ចំនួនអ្នកចូលស្នាក់\",\r\n        \"creditRooms\": \"បន្ទប់កាន់កាប់\",\r\n        \"creditPersons\": \"ចំនួនអ្នកកាន់កាប់\",\r\n        \"settledRooms\": \"បន្ទប់ដែលបានទូទាត់\",\r\n        \"settledPersons\": \"ចំនួនអ្នកបានទូទាត់\"\r\n      },\r\n      \"roomType\": {\r\n        \"distribution\": \"ការចែកចាយប្រភេទបន្ទប់\",\r\n        \"roomUnit\": \" បន្ទប់\"\r\n      },\r\n      \"remark\": {\r\n        \"title\": \"ព័ត៌មានកំណត់ចំណាំ\"\r\n      },\r\n      \"status\": {\r\n        \"checkedIn\": \"កំពុងស្នាក់\",\r\n        \"checkedOut\": \"ចេញ\",\r\n        \"credit\": \"កាន់កាប់\",\r\n        \"unknown\": \"មិនស្គាល់\"\r\n      },\r\n      \"actions\": {\r\n        \"printTeamRegistration\": \"បោះពុម្ពបែបបទចុះឈ្មោះក្រុម\"\r\n      },\r\n      \"common\": {\r\n        \"none\": \"គ្មាន\"\r\n      }\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { orderApi } from '@/api/modules/index'\r\nimport { OrderState } from '@/models'\r\n\r\nimport useUserStore from '@/store/modules/user'\r\nimport PrintMergeForm from '@/views/print/mergeForm.vue'\r\nimport { Calendar, Flag, InfoFilled, Key } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\n\r\n// 团队主单页面\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    teamCode: number | string // 团队代码\r\n    bindCode: number | string // 绑定代码\r\n    isEntryAccount: string // 是否可以入账操作, 当团队下的订单状态都是预订中时，不允许入账操作\r\n  }>(),\r\n  {\r\n    teamCode: '',\r\n    bindCode: '',\r\n    isEntryAccount: '0',\r\n  }\r\n)\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst dt = ref({\r\n  gcode: '', // 集团代码\r\n  hcode: '', // 门店代码\r\n  teamCode: '', // 团队代码\r\n  teamName: '', // 团队名称\r\n  teamType: '', // 团队类型;请看数据字典\r\n  contact: '', // 联系人\r\n  planCheckinTime: null, // 预抵时间 (Date | null)\r\n  roomCount: 0, // 房间数\r\n  checkInRoomCount: 0, // 已入住房间数\r\n  personCount: 0, // 入住人数\r\n  creditPersonCount: 0, // 挂账人数\r\n  creditRoomCount: 0, // 挂账房间数\r\n  settlePersonCount: 0, // 已结账人数\r\n  settleRoomCount: 0, // 已结账房间数\r\n  remark: '', // 备注\r\n  teamRooms: [] as OrderRoomRespVO[], // 团队房间列表 (OrderRoomRespVO[])\r\n  roomTypeCounts: [] as RoomTypeCount[], // 房型数量统计 (RoomTypeCount[])\r\n})\r\n\r\n// 定义子对象类型\r\ninterface OrderRoomRespVO {\r\n  /** 订单号 */\r\n  orderNo: string\r\n  /** 客人姓名 */\r\n  name: string\r\n  /** 房型代码 */\r\n  rtCode: string\r\n  /** 房型名称 */\r\n  rtName: string\r\n  /** 房号 */\r\n  rNo: string\r\n  /** 订单类型; general：普通订单 join：联房订单 group：团队订单 */\r\n  orderType: string\r\n  /** 绑定代码; 当为直接入住时，值为联房号(单个订单也生成唯一的联房号，多个订单生成相同的联房号)。当订单来自预订时，值为预订单号. */\r\n  bindCode: string\r\n  /** 是否主订单; 是否为主订单（0否 1是） */\r\n  isMain: string\r\n  /** 订单状态; 订单状态（在住:check_in 、已离店:check_out） */\r\n  state: string\r\n  /** 入住时间 */\r\n  checkinTime: Date | null\r\n  /** 预离时间 */\r\n  planCheckoutTime: Date | null\r\n  /** 退房时间 */\r\n  checkoutTime: Date | null\r\n  /** 同住人列表 */\r\n  orderTogethers?: any[]\r\n}\r\n\r\ninterface RoomTypeCount {\r\n  rtName: string // 房型名称\r\n  count: number // 房间数量\r\n}\r\n\r\n// 表格展开行控制\r\nconst expandedRows = ref([])\r\n// 打印联房订单\r\nconst printMergeFormVisible = ref(false)\r\nonMounted(async () => {\r\n  await Promise.all([getOrderDetail()])\r\n  // 默认展开第一行\r\n  if (dt.value.teamRooms && dt.value.teamRooms.length > 0) {\r\n    expandedRows.value = [dt.value.teamRooms[0].orderNo]\r\n  }\r\n})\r\n\r\nasync function getOrderDetail() {\r\n  const res = await orderApi.getTeamMainOrder({\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    teamCode: props.teamCode,\r\n  })\r\n  dt.value = res.data\r\n}\r\nconst data = ref({\r\n  tableAutoHeight: true,\r\n  showWidth: '400px',\r\n})\r\nfunction getTagType(index) {\r\n  const types = ['primary', 'success', 'warning', 'danger', 'info']\r\n  return types[index % types.length]\r\n}\r\n\r\n// 获取状态对应的类型\r\nfunction getStatusType(status) {\r\n  if (status === OrderState.CHECK_IN) {\r\n    return 'success'\r\n  }\r\n  if (status === OrderState.CREDIT) {\r\n    return 'warning'\r\n  }\r\n  if (status === OrderState.CHECK_OUT) {\r\n    return 'info'\r\n  }\r\n  return 'info'\r\n}\r\n\r\n// 获取状态对应的文本\r\nfunction getStatusText(status) {\r\n  if (status === OrderState.CHECK_IN) {\r\n    return t('status.checkedIn')\r\n  }\r\n  if (status === OrderState.CHECK_OUT) {\r\n    return t('status.checkedOut')\r\n  }\r\n  if (status === OrderState.CREDIT) {\r\n    return t('status.credit')\r\n  }\r\n  return t('status.unknown')\r\n}\r\n\r\n// 格式化时间\r\nfunction formatTime(time) {\r\n  return time ? dayjs(time).format('MM-DD HH:mm') : t('common.none')\r\n}\r\n// 获取团队类型文本\r\nfunction getTeamTypeText(teamType) {\r\n  if (teamType === 'meeting_group') {\r\n    return t('teamType.meetingGroup')\r\n  }\r\n  if (teamType === 'travel_group') {\r\n    return t('teamType.travelGroup')\r\n  }\r\n  return t('teamType.other')\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"team-reception-container\">\r\n    <!-- 团队概览卡片 -->\r\n    <el-card class=\"overview-card\" shadow=\"hover\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <div class=\"header-title\">\r\n            <el-icon><Flag /></el-icon>\r\n            <span>{{ t('teamInfo.title') }}</span>\r\n            <el-tag type=\"primary\" effect=\"plain\" class=\"ml-2\">\r\n              {{ getTeamTypeText(dt.teamType) }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"printMergeFormVisible = true\">\r\n              {{ t('actions.printTeamRegistration') }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 基本信息区域 -->\r\n      <div class=\"team-basic-info\">\r\n        <div class=\"info-row\">\r\n          <div class=\"info-item\">\r\n            <span class=\"info-label\">{{ t('teamInfo.teamName') }}：</span>\r\n            <span class=\"info-value\">{{ dt.teamName || t('common.none') }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <span class=\"info-label\">{{ t('teamInfo.contact') }}：</span>\r\n            <span class=\"info-value\">{{ dt.contact || t('common.none') }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <span class=\"info-label\">{{ t('teamInfo.plannedArrivalTime') }}：</span>\r\n            <span class=\"info-value\">\r\n              <el-icon><Calendar /></el-icon>\r\n              {{ formatTime(dt.planCheckinTime) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 统计数据卡片组 -->\r\n      <div class=\"stats-section\">\r\n        <div class=\"section-title\">\r\n          <el-icon><InfoFilled /></el-icon>\r\n          <span>{{ t('statistics.roomStatistics') }}</span>\r\n        </div>\r\n        <div class=\"stats-cards\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.roomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.totalRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.checkInRoomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.checkedInRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.personCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.checkedInPersons') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.creditRoomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.creditRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.creditPersonCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.creditPersons') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.settleRoomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.settledRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.settlePersonCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.settledPersons') }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 房型分布 -->\r\n      <div class=\"room-type-section\">\r\n        <div class=\"section-title\">\r\n          <el-icon><Key /></el-icon>\r\n          <span>{{ t('roomType.distribution') }}</span>\r\n        </div>\r\n        <div class=\"room-type-distribution\">\r\n          <div v-for=\"(item, index) in dt.roomTypeCounts\" :key=\"index\" class=\"room-type-item\">\r\n            <el-tag :type=\"getTagType(index)\" effect=\"light\">\r\n              {{ item.rtName }}\r\n            </el-tag>\r\n            <span class=\"room-type-count\">{{ item.count }}间</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 备注信息 -->\r\n      <div v-if=\"dt.remark\" class=\"remark-section\">\r\n        <div class=\"section-title\">\r\n          <el-icon><InfoFilled /></el-icon>\r\n          <span>{{ t('remark.title') }}</span>\r\n        </div>\r\n        <div class=\"remark-container\">\r\n          <div class=\"remark-content\">\r\n            {{ dt.remark }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n  <!-- 打印团队/联房入住登记单 -->\r\n  <PrintMergeForm v-if=\"printMergeFormVisible\" v-model=\"printMergeFormVisible\" :bind-code=\"props.bindCode\" />\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 整体容器样式\r\n.team-reception-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n// 卡片通用样式\r\n.el-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: box-shadow 0.3s;\r\n\r\n  &:hover {\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n// 卡片头部样式\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 5px;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.ml-2 {\r\n  margin-left: 8px;\r\n}\r\n\r\n// 基本信息区域\r\n.team-basic-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  min-width: 250px;\r\n}\r\n\r\n.info-label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 10px;\r\n}\r\n\r\n.info-value {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #303133;\r\n}\r\n\r\n// 统计区域\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 15px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.stat-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 120px;\r\n  height: 90px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s ease;\r\n  cursor: default;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.stat-label {\r\n  margin-top: 5px;\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n// 房型分布区域\r\n.room-type-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.room-type-distribution {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.room-type-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.room-type-count {\r\n  font-weight: bold;\r\n  color: #606266;\r\n}\r\n\r\n// 备注信息区域\r\n.remark-section {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.ml-2 {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.team-basic-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 15px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.room-type-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.remark-section {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.guest-info-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n// 修复表格展开行样式\r\n:deep(.el-table__expanded-cell) {\r\n  padding: 15px !important;\r\n}\r\n\r\n:deep(.el-table__expand-icon) {\r\n  transform-origin: center;\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n:deep(.el-table__expand-icon--expanded) {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n// 修复空状态样式\r\n:deep(.el-empty) {\r\n  padding: 20px 0;\r\n}\r\n\r\n// 修复表格内部样式\r\n:deep(.el-table__body) td {\r\n  padding: 8px 0;\r\n}\r\n\r\n// 修复卡片内边距\r\n:deep(.el-card__body) {\r\n  padding: 15px;\r\n}\r\n// 时间信息样式\r\n.time-info-inline {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  white-space: nowrap;\r\n}\r\n\r\n.time-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 13px;\r\n}\r\n\r\n.divider {\r\n  margin: 0 8px;\r\n  color: #dcdfe6;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.room-count-tag {\r\n  margin-left: 10px;\r\n  font-weight: normal;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "dt", "ref", "gcode", "hcode", "teamCode", "teamName", "teamType", "contact", "planCheckinTime", "roomCount", "checkInRoomCount", "personCount", "creditPersonCount", "creditRoomCount", "settlePersonCount", "settleRoomCount", "remark", "teamRooms", "roomTypeCounts", "expandedRows", "printMergeFormVisible", "async", "getOrderDetail", "res", "orderApi", "getTeamMainOrder", "value", "data", "getTagType", "index", "types", "length", "onMounted", "Promise", "all", "orderNo", "tableAutoHeight", "showWidth", "time", "dayjs", "format"], "mappings": "s2DA6IA,MAAMA,EAAQC,GAYRC,EAAEA,IAAMC,IACRC,GAAYC,IACZC,GAAKC,EAAI,CACbC,MAAO,GACPC,MAAO,GACPC,SAAU,GACVC,SAAU,GACVC,SAAU,GACVC,QAAS,GACTC,gBAAiB,KACjBC,UAAW,EACXC,iBAAkB,EAClBC,YAAa,EACbC,kBAAmB,EACnBC,gBAAiB,EACjBC,kBAAmB,EACnBC,gBAAiB,EACjBC,OAAQ,GACRC,UAAW,GACXC,eAAgB,KAuCZC,GAAelB,EAAI,IAEnBmB,GAAwBnB,GAAI,GASlCoB,eAAeC,KACP,MAAAC,QAAYC,EAASC,iBAAiB,CAC1CvB,MAAOJ,GAAUI,MACjBC,MAAOL,GAAUK,MACjBC,SAAUV,EAAMU,WAElBJ,GAAG0B,MAAQH,EAAII,IAAA,CAMjB,SAASC,GAAWC,GAClB,MAAMC,EAAQ,CAAC,UAAW,UAAW,UAAW,SAAU,QACnD,OAAAA,EAAMD,EAAQC,EAAMC,OAAM,QAtBnCC,GAAUX,gBACFY,QAAQC,IAAI,CAACZ,OAEftB,GAAG0B,MAAMT,WAAajB,GAAG0B,MAAMT,UAAUc,OAAS,IACpDZ,GAAaO,MAAQ,CAAC1B,GAAG0B,MAAMT,UAAU,GAAGkB,SAAO,IAYtClC,EAAA,CACfmC,iBAAiB,EACjBC,UAAW,8TAwCY/B,iBAEdV,GADQ,kBAAbU,EACO,wBAEM,iBAAbA,EACO,uBAEF,wBAPX,IAAyBA,0jBAJLgC,wBACXA,EAAOC,EAAMD,GAAME,OAAO,eAAiB5C,GAAE,g5CADtD,IAAoB0C"}