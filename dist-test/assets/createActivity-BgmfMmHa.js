import{d as e,aj as t,ai as a,b as l,B as i,y as s,aR as o,aq as r,u as n,o as c,c as p,f as m,w as d,g as u,F as b,ag as g,e as h,bw as v,R as f,bx as y,Y as V,aS as C,m as _,s as D,aQ as j,q as x,bK as P,j as M,k as N,b5 as k,x as S,aT as F}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css                 *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";import{c as L}from"./couponActivity.api-CJVCjug9.js";import{m as E}from"./member.api-2tU9HGvl.js";import{r as w}from"./rechargePlan.api-CkxheMlB.js";import{d as A}from"./dictData.api-DUabpYqy.js";import{m as U}from"./merchant.api-BtmIsRm3.js";import{D as R}from"./DictTypeEnum-DKIIlHnN.js";import{y as T}from"./timeutils-Ib6GkGcq.js";import{h as q}from"./tree-1O_Xr3Eu.js";import{u as G}from"./index-C3o6k9C-.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";const H={style:{width:"100%","margin-bottom":"10px"}},B={style:{width:"100%","margin-bottom":"10px"}},I={style:{width:"100%","margin-bottom":"10px"}},Y={style:{display:"flex","justify-content":"space-between","margin-left":"5px"}},z={class:"el-form-item-msg"},K=e({__name:"createActivity",props:{activityCode:{default:""},handle:{},isEdit:{type:Boolean}},setup(e,{expose:O}){const K=e,{t:Q}=t(),J=a(),W=l(!1),X=l(),Z=l({activityCode:K.activityCode,gcode:J.gcode,hcode:J.hcode,activityName:"",storeFee:null,giveFee:0,givePoint:0,isG:"0",giveFeeIndate:365,givePointIndate:365,tickets:[{templateName:"",templateCode:"",num:0}],processDate:"",startDate:"",endDate:"",channels:[],mts:[],times:1,state:"1",rechargeActivityMerchants:[]}),$=l([]),ee=l([]),te=l([]),ae=l({activityName:[{required:!0,message:Q("inputName"),trigger:"blur"}],storeFee:[{required:!0,message:Q("inputRecharge"),trigger:"blur"}],processDate:[{required:!0,message:Q("inputStartEnd"),trigger:"blur"}],channels:[{required:!0,message:Q("selectChannel"),trigger:"blur"}],mts:[{required:!0,message:Q("selectMemberLevel"),trigger:"blur"}],rechargeActivityMerchants:[{required:!0,message:Q("selectStores"),trigger:"blur"}]}),le=i((()=>({currency:"CNY",currencyDisplay:"symbol",precision:2,hideCurrencySymbolOnFocus:!0,hideGroupingSeparatorOnFocus:!0,hideNegligibleDecimalDigitsOnFocus:!0,autoDecimalDigits:!1,useGrouping:!0,accountingSign:!1})));G(le.value),s((()=>{A.getDictDataBatch(ie).then((e=>{te.value=e.data.filter((e=>e.dictType===R.RECHARGE_CHANNEL))})),function(){const e={gcode:J.gcode,hcode:J.hcode};U.list(e).then((e=>{0===e.code&&($.value=e.data)}))}(),E.listMemberType(J.gcode,"1").then((e=>{0===e.code&&(ee.value=e.data)})),function(){const e={gcode:J.gcode,hcode:J.hcode,state:"1"};L.getTreeTemplateList(e).then((e=>{if(se.value=e.data,0===e.code){let t=[];t=q(e.data),t.forEach((e=>{e.children&&oe.value.push(e)}))}}))}()}));const ie=[R.RECHARGE_CHANNEL];const se=l(),oe=l([]);function re(){Z.value.tickets.push({templateName:"",templateCode:"",num:0})}function ne(e){return e.getTime()<Date.now()-864e5}return O({submit:()=>(Z.value.startDate=T(Z.value.processDate[0]),Z.value.endDate=T(Z.value.processDate[1]),new Promise((e=>{X.value&&X.value.validate((t=>{t&&w.create(Z.value).then((t=>{0===t.code?(o.success({message:Q("successMessage"),center:!0}),e()):o.error({message:Q("errorMessage",{msg:t.msg}),center:!0})}))}))})))}),(e,t)=>{const a=C,l=_,i=D,s=j,o=x,L=P,E=M,w=N,A=k,U=S,R=F;return r((c(),p("div",null,[m(U,{ref_key:"formRef",ref:X,model:n(Z),rules:n(ae),"label-suffix":"：","label-width":"140px"},{default:d((()=>[m(l,{label:n(Q)("activityName"),prop:"activityName"},{default:d((()=>[m(a,{modelValue:n(Z).activityName,"onUpdate:modelValue":t[0]||(t[0]=e=>n(Z).activityName=e),placeholder:n(Q)("inputName"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(l,{label:n(Q)("storeFee"),prop:"storeFee"},{default:d((()=>[m(i,{modelValue:n(Z).storeFee,"onUpdate:modelValue":t[1]||(t[1]=e=>n(Z).storeFee=e),precision:2,step:.1,controls:!1,placeholder:n(Q)("inputRecharge")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(l,{label:n(Q)("coupons")},{default:d((()=>[u("div",H,[m(l,{label:n(Q)("giveFee"),prop:"giveFee"},{default:d((()=>[m(i,{modelValue:n(Z).giveFee,"onUpdate:modelValue":t[2]||(t[2]=e=>n(Z).giveFee=e),min:0,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"])])),_:1},8,["label"])]),u("div",B,[m(l,{label:n(Q)("givePoint"),prop:"givePoint"},{default:d((()=>[m(i,{modelValue:n(Z).givePoint,"onUpdate:modelValue":t[3]||(t[3]=e=>n(Z).givePoint=e),min:0,"controls-position":"right",style:{width:"150px"}},null,8,["modelValue"])])),_:1},8,["label"])]),u("div",I,[m(l,{label:n(Q)("coupons")},{default:d((()=>[(c(!0),p(b,null,g(n(Z).tickets,((e,t)=>(c(),p("div",{key:t,style:{display:"flex","align-items":"center","margin-bottom":"10px"}},[m(s,{modelValue:e.templateCode,"onUpdate:modelValue":t=>e.templateCode=t,clearable:"",placeholder:n(Q)("selectStores"),options:n(oe),props:{value:"code",label:"name"},style:{width:"200px"},onChange:t=>function(e){e.templateCode&&(e.templateName=se.value.find((t=>t.code===e.templateCode[1])).name,e.templateCode=e.templateCode[1])}(e)},null,8,["modelValue","onUpdate:modelValue","placeholder","options","onChange"]),m(l,{label:n(Q)("number")},{default:d((()=>[m(i,{modelValue:e.num,"onUpdate:modelValue":t=>e.num=t,max:99,min:0,"controls-position":"right",style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["label"]),u("div",Y,[t===n(Z).tickets.length-1?(c(),h(o,{key:0,icon:n(v),circle:"",type:"primary",onClick:re},null,8,["icon"])):f("",!0),n(Z).tickets.length>1?(c(),h(o,{key:1,icon:n(y),circle:"",type:"danger",onClick:e=>function(e){Z.value.tickets.splice(e,1)}(t)},null,8,["icon","onClick"])):f("",!0)])])))),128))])),_:1},8,["label"])])])),_:1},8,["label"]),m(l,{label:n(Q)("effectiveTime"),prop:"processDate"},{default:d((()=>[m(L,{modelValue:n(Z).processDate,"onUpdate:modelValue":t[4]||(t[4]=e=>n(Z).processDate=e),"disabled-date":ne,"end-placeholder":n(Q)("endDatePlaceholder"),"range-separator":n(Q)("rangeSeparator"),"start-placeholder":n(Q)("startDatePlaceholder"),style:{width:"200px"},type:"daterange"},null,8,["modelValue","end-placeholder","range-separator","start-placeholder"])])),_:1},8,["label"]),m(l,{label:n(Q)("applicableChannels"),prop:"channels"},{default:d((()=>[m(w,{modelValue:n(Z).channels,"onUpdate:modelValue":t[5]||(t[5]=e=>n(Z).channels=e),multiple:"",placeholder:n(Q)("selectChannel"),prop:"channels"},{default:d((()=>[(c(!0),p(b,null,g(n(te),(e=>(c(),h(E,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),m(l,{label:n(Q)("participatingStores"),prop:"rechargeActivityMerchants"},{default:d((()=>[m(w,{modelValue:n(Z).rechargeActivityMerchants,"onUpdate:modelValue":t[6]||(t[6]=e=>n(Z).rechargeActivityMerchants=e),multiple:"",placeholder:n(Q)("selectStores")},{default:d((()=>[(c(!0),p(b,null,g(n($),(e=>(c(),h(E,{key:e.hcode,label:e.hname,value:e.hcode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),m(l,{label:n(Q)("applicableMemberLevels"),prop:"mts"},{default:d((()=>[m(w,{modelValue:n(Z).mts,"onUpdate:modelValue":t[7]||(t[7]=e=>n(Z).mts=e),multiple:"",placeholder:n(Q)("selectMemberLevel"),prop:"mts"},{default:d((()=>[(c(!0),p(b,null,g(n(ee),(e=>(c(),h(E,{key:e.mtCode,label:e.mtName,value:e.mtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),m(l,{label:n(Q)("participationLimit")},{default:d((()=>[m(i,{modelValue:n(Z).times,"onUpdate:modelValue":t[8]||(t[8]=e=>n(Z).times=e),min:0,max:99,"controls-position":"right"},null,8,["modelValue"]),u("div",z,V(n(Q)("inputParticipation")),1)])),_:1},8,["label"]),m(l,{label:n(Q)("status")},{default:d((()=>[m(A,{modelValue:n(Z).state,"onUpdate:modelValue":t[9]||(t[9]=e=>n(Z).state=e),"active-text":n(Q)("online"),"active-value":"1","inactive-text":n(Q)("offline"),"inactive-value":"0","inline-prompt":""},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1},8,["model","rules"])])),[[R,n(W)]])}}});function Q(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{activityName:{t:0,b:{t:2,i:[{t:3}],s:"Activity Name"}},storeFee:{t:0,b:{t:2,i:[{t:3}],s:"Recharge Money"}},giveFee:{t:0,b:{t:2,i:[{t:3}],s:"Give Amount"}},givePoint:{t:0,b:{t:2,i:[{t:3}],s:"Give Points"}},coupons:{t:0,b:{t:2,i:[{t:3}],s:"Give Coupons"}},effectiveTime:{t:0,b:{t:2,i:[{t:3}],s:"Effective Time"}},applicableChannels:{t:0,b:{t:2,i:[{t:3}],s:"Channels"}},participatingStores:{t:0,b:{t:2,i:[{t:3}],s:"Hotel"}},applicableMemberLevels:{t:0,b:{t:2,i:[{t:3}],s:"Member Levels"}},participationLimit:{t:0,b:{t:2,i:[{t:3}],s:"Participation Limit"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},inputName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the activity name"}},inputRecharge:{t:0,b:{t:2,i:[{t:3}],s:"enter money"}},inputStartEnd:{t:0,b:{t:2,i:[{t:3}],s:"select start and end time"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"select channels"}},selectMemberLevel:{t:0,b:{t:2,i:[{t:3}],s:"select member levels"}},selectStores:{t:0,b:{t:2,i:[{t:3}],s:"select participating stores"}},inputParticipation:{t:0,b:{t:2,i:[{t:3}],s:"Enter 0 for unlimited"}},successMessage:{t:0,b:{t:2,i:[{t:3}],s:"Added successfully"}},errorMessage:{t:0,b:{t:2,i:[{t:3,v:"Error: "},{t:4,k:"msg"}]}},online:{t:0,b:{t:2,i:[{t:3}],s:"Online"}},offline:{t:0,b:{t:2,i:[{t:3}],s:"Offline"}},number:{t:0,b:{t:2,i:[{t:3}],s:"number"}},endDatePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"End Date"}},startDatePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Start Date"}},rangeSeparator:{t:0,b:{t:2,i:[{t:3}],s:"to"}}},"zh-cn":{activityName:{t:0,b:{t:2,i:[{t:3}],s:"活动名称"}},storeFee:{t:0,b:{t:2,i:[{t:3}],s:"充值金额"}},giveFee:{t:0,b:{t:2,i:[{t:3}],s:"赠送金额"}},givePoint:{t:0,b:{t:2,i:[{t:3}],s:"赠送积分"}},coupons:{t:0,b:{t:2,i:[{t:3}],s:"赠送优惠券"}},effectiveTime:{t:0,b:{t:2,i:[{t:3}],s:"生效时间"}},applicableChannels:{t:0,b:{t:2,i:[{t:3}],s:"适用渠道"}},participatingStores:{t:0,b:{t:2,i:[{t:3}],s:"参与门店"}},applicableMemberLevels:{t:0,b:{t:2,i:[{t:3}],s:"适用会员级别"}},participationLimit:{t:0,b:{t:2,i:[{t:3}],s:"参加次数限制"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},inputName:{t:0,b:{t:2,i:[{t:3}],s:"请输入活动名称"}},inputRecharge:{t:0,b:{t:2,i:[{t:3}],s:"请输入充值金额"}},inputStartEnd:{t:0,b:{t:2,i:[{t:3}],s:"请选择起止时间"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"请选择渠道"}},selectMemberLevel:{t:0,b:{t:2,i:[{t:3}],s:"请选择会员级别"}},selectStores:{t:0,b:{t:2,i:[{t:3}],s:"请适用门店"}},inputParticipation:{t:0,b:{t:2,i:[{t:3}],s:"输入0表示不限制"}},successMessage:{t:0,b:{t:2,i:[{t:3}],s:"新增成功"}},errorMessage:{t:0,b:{t:2,i:[{t:3,v:"错误："},{t:4,k:"msg"}]}},online:{t:0,b:{t:2,i:[{t:3}],s:"上线"}},offline:{t:0,b:{t:2,i:[{t:3}],s:"下线"}},number:{t:0,b:{t:2,i:[{t:3}],s:"数量"}},endDatePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"结束日期"}},startDatePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"开始日期"}},rangeSeparator:{t:0,b:{t:2,i:[{t:3}],s:"至"}}},km:{activityName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះសកម្មភាព"}},storeFee:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនប្រាក់បញ្ចូល"}},giveFee:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនអំណោយ"}},givePoint:{t:0,b:{t:2,i:[{t:3}],s:"ពិន្ទុអំណោយ"}},coupons:{t:0,b:{t:2,i:[{t:3}],s:"គូប៉ុងអំណោយ"}},effectiveTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាមានសុពលភាព"}},applicableChannels:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែលអាចអនុវត្តបាន"}},participatingStores:{t:0,b:{t:2,i:[{t:3}],s:"ហាងចូលរួម"}},applicableMemberLevels:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតសមាជិកដែលអាចអនុវត្តបាន"}},participationLimit:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់ចំនួនដងចូលរួម"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},inputName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះសកម្មភាព"}},inputRecharge:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលចំនួនប្រាក់បញ្ចូល"}},inputStartEnd:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសពេលវេលាចាប់ផ្តើមនិងបញ្ចប់"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឆានែល"}},selectMemberLevel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកម្រិតសមាជិក"}},selectStores:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសហាងចូលរួម"}},inputParticipation:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចូល 0 ដើម្បីមិនកំណត់"}},successMessage:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែមជោគជ័យ"}},errorMessage:{t:0,b:{t:2,i:[{t:3,v:"កំហុស៖ "},{t:4,k:"msg"}]}},online:{t:0,b:{t:2,i:[{t:3}],s:"អនុវត្ត"}},offline:{t:0,b:{t:2,i:[{t:3}],s:"ឈប់អនុវត្ត"}},number:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួន"}},endDatePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"ថ្ងៃបញ្ចប់"}},startDatePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"ថ្ងៃចាប់ផ្តើម"}},rangeSeparator:{t:0,b:{t:2,i:[{t:3}],s:"ដល់"}}}}})}Q(K);const J=O(K,[["__scopeId","data-v-bc4dfabf"]]);export{J as default};
//# sourceMappingURL=createActivity-BgmfMmHa.js.map
