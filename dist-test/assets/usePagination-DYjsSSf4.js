import{b as e}from"./index-CkEhI1Zk.js";function a(){const a=e({pageNo:1,pageSize:10,total:0,sizes:[10,20,50,100],layout:"total, sizes, ->, prev, pager, next, jumper",sort:null,order:null});return{pagination:a,getParams:function(){return{pageNo:a.value.pageNo,pageSize:a.value.pageSize,from:(a.value.pageNo-1)*a.value.pageSize,...a.value.sort&&a.value.order&&{sort:a.value.sort,order:a.value.order}}},onSizeChange:async function(e){a.value.pageSize=e},onCurrentChange:async function(e){a.value.pageNo=e},onSortChange:async function(e,o){a.value.sort=e,a.value.order="ascending"===o?"asc":"desc"}}}export{a as u};
//# sourceMappingURL=usePagination-DYjsSSf4.js.map
