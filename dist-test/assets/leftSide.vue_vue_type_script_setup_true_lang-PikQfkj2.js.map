{"version": 3, "file": "leftSide.vue_vue_type_script_setup_true_lang-PikQfkj2.js", "sources": ["../../src/layouts/components/Topbar/Toolbar/leftSide.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport Tools from './tools.vue'\r\n\r\ndefineOptions({\r\n  name: 'ToolbarLeftSide',\r\n})\r\n\r\nconst settingsStore = useSettingsStore()\r\n</script>\r\n\r\n<template>\r\n  <div class=\"flex items-center\">\r\n    <div v-if=\"settingsStore.mode === 'mobile'\" class=\"flex-center cursor-pointer px-2 py-1 -rotate-z-180\" @click=\"settingsStore.toggleSidebarCollapse()\">\r\n      <SvgIcon name=\"toolbar-collapse\" :rotate=\"settingsStore.settings.app.direction === 'rtl' ? 180 : 0\" />\r\n    </div>\r\n    <Tools mode=\"left-side\" />\r\n  </div>\r\n</template>\r\n"], "names": ["settingsStore", "useSettingsStore"], "mappings": "gQAQA,MAAMA,EAAgBC"}