{"version": 3, "file": "nightReview-CNoY6f1u.js", "sources": ["../../src/assets/icons/night_review.svg", "../../src/api/modules/pms/nightaudi/nightAudi.api.ts", "../../src/layouts/components/Topbar/Toolbar/nightReview.vue"], "sourcesContent": ["export default \"data:image/svg+xml,%3csvg%20t='1724058398823'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='4555'%20width='32'%20height='32'%3e%3cpath%20d='M512%2066.6C266%2066.6%2066.6%20266%2066.6%20512%2066.6%20758%20266%20957.4%20512%20957.4c246%200%20445.4-199.4%20445.4-445.4C957.4%20266%20758%2066.6%20512%2066.6z%20m-28.3%20755.6C380.6%20808.1%20291.5%20743%20246.7%20649c-1.1-2.2-0.1-4.9%202.1-6%200.9-0.4%201.9-0.5%202.8-0.4l2.3%200.5c159.2%2029.4%20312.2-75.8%20341.6-235%203.3-17.6%204.9-35.4%204.9-53.3%200.1-49.6-12.5-98.4-36.5-141.8-0.6-1.2-0.2-2.6%201-3.3%200.4-0.2%201-0.3%201.5-0.3l1.4%200.2C737%20233%20855.3%20389%20832%20558.1c-23.3%20169.2-179.2%20287.4-348.3%20264.1z'%20p-id='4556'%20fill='%23909295'%3e%3c/path%3e%3c/svg%3e\"", "import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/night-audi'\r\n/** 集团夜审设置 */\r\nexport default {\r\n\r\n  /**\r\n   * 夜审\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createNightAudi: (data: any) =>\r\n    api.post(`${BASE_PATH}/begin`, data),\r\n  /**\r\n   * 是否已夜审\r\n   * @param hcode 门店代码\r\n   * @returns true 已夜审 false 未夜审\r\n   */\r\n  isNightAudi: (hcode: string) =>\r\n    api.get(`${BASE_PATH}/is-night-audi`, { params: { hcode } }),\r\n}\r\n", "<i18n>\r\n{\r\n  \"en\": {\r\n    \"nightReview\": \"Night Audit\",\r\n    \"startNightReview\": \"Start Night Audit\",\r\n    \"conductingNightReview\": \"Conducting Night Audit\",\r\n    \"todayNightReview\": \"Today Night Audit Completed\",\r\n    \"autoNightReview\": \"The current system uses automatic night audit, no action required.\",\r\n    \"nightReviewWork\": \"Night Audit Tasks\",\r\n    \"reviewTask1Main\": \"1. Verify Order Prices\",\r\n    \"reviewTask1Detail\": \"Please verify that all room and order prices are correct in advance.\",\r\n    \"reviewTask2Main\": \"2. Automatically Renew Orders Not Yet Left\",\r\n    \"reviewTask2Detail\": \"Handle NoShow for orders not yet arrived; retain guaranteed pre-orders.\",\r\n    \"reviewTask3Main\": \"3. Generate Daily Room Fees\",\r\n    \"reviewTask3Detail\": \"Generate night review room fees for overnight rooms.\",\r\n    \"reviewTask4Main\": \"4. Finalize Reports\",\r\n    \"reviewTask4Detail\": \"Generate daily report data.\",\r\n    \"reviewTask5Main\": \"5. Advance Business Date\",\r\n    \"reviewTask5Detail\": \"Complete night audit.\",\r\n    \"businessDate\": \"Business date\",\r\n    \"operator\": \"Operator\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"nightReview\": \"夜审\",\r\n    \"startNightReview\": \"开始夜审\",\r\n    \"conductingNightReview\": \"正在夜审\",\r\n    \"todayNightReview\": \"今天已夜审\",\r\n    \"autoNightReview\": \"当前系统为自动夜审,无需操作。\",\r\n    \"nightReviewWork\": \"夜审工作\",\r\n    \"reviewTask1Main\": \"1. 核查订单房价\",\r\n    \"reviewTask1Detail\": \"请提前核对所有房间和订单价格是否正确。\",\r\n    \"reviewTask2Main\": \"2. 应离未离订单自动续住，应到未到预订单NoShow处理\",\r\n    \"reviewTask2Detail\": \"保留担保预订单。\",\r\n    \"reviewTask3Main\": \"3. 生成当日房费\",\r\n    \"reviewTask3Detail\": \"为过夜房间生成夜审房费。\",\r\n    \"reviewTask4Main\": \"4. 固化报表\",\r\n    \"reviewTask4Detail\": \"生成当日报表数据。\",\r\n    \"reviewTask5Main\": \"5. 推进营业日期\",\r\n    \"reviewTask5Detail\": \"完成夜审。\",\r\n    \"businessDate\": \"营业时期\",\r\n    \"operator\": \"操作员\"\r\n  },\r\n  \"km\": {\r\n    \"nightReview\": \"ការត្រួតពិនិត្យពេលយប់\",\r\n    \"startNightReview\": \"ចាប់ផ្តើមការត្រួតពិនិត្យពេលយប់\",\r\n    \"conductingNightReview\": \"កំពុងធ្វើការត្រួតពិនិត្យពេលយប់\",\r\n    \"todayNightReview\": \"ការត្រួតពិនិត្យពេលយប់ថ្ងៃនេះបានបញ្ចប់\",\r\n    \"autoNightReview\": \"ប្រព័ន្ធបច្ចុប្បន្នប្រើការត្រួតពិនិត្យពេលយប់ស្វ័យប្រវត្តិ មិនចាំបាច់ធ្វើសកម្មភាពទេ។\",\r\n    \"nightReviewWork\": \"កិច្ចការត្រួតពិនិត្យពេលយប់\",\r\n    \"reviewTask1Main\": \"១. ពិនិត្យតម្លៃការបញ្ជាទិញ\",\r\n    \"reviewTask1Detail\": \"សូមពិនិត្យថាតម្លៃបន្ទប់និងការបញ្ជាទិញទាំងអស់ត្រឹមត្រូវជាមុន។\",\r\n    \"reviewTask2Main\": \"២. បន្តការបញ្ជាទិញដែលមិនទាន់ចាកចេញដោយស្វ័យប្រវត្តិ\",\r\n    \"reviewTask2Detail\": \"ដោះស្រាយ NoShow សម្រាប់ការបញ្ជាទិញដែលមិនទាន់មកដល់ រក្សាការកក់ជាមុនដែលមានការធានា។\",\r\n    \"reviewTask3Main\": \"៣. បង្កើតថ្លៃបន្ទប់ប្រចាំថ្ងៃ\",\r\n    \"reviewTask3Detail\": \"បង្កើតថ្លៃបន្ទប់ត្រួតពិនិត្យពេលយប់សម្រាប់បន្ទប់ស្នាក់ពេលយប់។\",\r\n    \"reviewTask4Main\": \"៤. បញ្ចប់របាយការណ៍\",\r\n    \"reviewTask4Detail\": \"បង្កើតទិន្នន័យរបាយការណ៍ប្រចាំថ្ងៃ។\",\r\n    \"reviewTask5Main\": \"៥. ជំរុញកាលបរិច្ឆេទអាជីវកម្ម\",\r\n    \"reviewTask5Detail\": \"បញ្ចប់ការត្រួតពិនិត្យពេលយប់។\",\r\n    \"businessDate\": \"កាលបរិច្ឆេទអាជីវកម្ម\",\r\n    \"operator\": \"អ្នកប្រតិបត្តិ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport generalConfigApi from '@/api/modules/pms/config/generalConfig.api'\r\nimport nightAudiApi from '@/api/modules/pms/nightaudi/nightAudi.api'\r\nimport nightAudiSetApi from '@/api/modules/pms/nightaudi/nightAudiSet.api'\r\nimport useUserStore from '@/store/modules/user'\r\nimport storage from '@/utils/storage'\r\nimport { InfoFilled } from '@element-plus/icons-vue'\r\n\r\ndefineOptions({\r\n  name: 'NightReview',\r\n})\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst percentage = ref(0)\r\nconst type = ref(1)\r\nconst colors = [\r\n  { color: '#f56c6c', percentage: 20 },\r\n  { color: '#e6a23c', percentage: 40 },\r\n  { color: '#5cb87a', percentage: 60 },\r\n  { color: '#1989fa', percentage: 80 },\r\n  { color: '#6f7ad3', percentage: 100 },\r\n]\r\n\r\nconst visible = ref(false)\r\nfunction onClick() {\r\n  visible.value = true\r\n}\r\nconst bizDate = ref<string>('')\r\nfunction onStart() {\r\n  nightAudiApi.createNightAudi({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      type.value = 2\r\n      const time = setInterval(() => {\r\n        percentage.value = (percentage.value % 100) + 10\r\n        if (percentage.value === 100) {\r\n          type.value = 3\r\n          clearInterval(time)\r\n          getBizDate()\r\n          storage.local.set('bizDate', bizDate.value)\r\n        }\r\n      }, 1e3)\r\n    }\r\n  })\r\n}\r\nconst data = ref({\r\n  /**\r\n   * 自动审核;0：手动夜审 1：自动夜审\r\n   */\r\n  auto: '0',\r\n  /**\r\n   * 手动夜审时间范围结束;手动夜审有效，如：06:00\r\n   */\r\n  handEndTime: '',\r\n  /**\r\n   * 手动夜审时间范围开始;手动夜审有效，如：04:00\r\n   */\r\n  handStartTime: '',\r\n})\r\n\r\nonMounted(() => {\r\n  isNightAudi()\r\n  getNightAudiSet()\r\n  getBizDate()\r\n})\r\n\r\nfunction isNightAudi() {\r\n  nightAudiApi.isNightAudi(userStore.hcode).then((res: any) => {\r\n    if (res.code === 0 && res.data === true) {\r\n      type.value = 3\r\n    }\r\n  })\r\n}\r\n\r\nfunction getNightAudiSet() {\r\n  nightAudiSetApi.getNightAudiSet(userStore.gcode).then((res: any) => {\r\n    if (res.code === 0) {\r\n      data.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nfunction getBizDate() {\r\n  generalConfigApi.getBizData({ hcode: userStore.hcode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      bizDate.value = res.data\r\n    }\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"flex-center cursor-[pointer]\">\r\n    <el-tooltip :content=\"t('nightReview')\" placement=\"bottom\" effect=\"light\">\r\n      <div class=\"icon-with-text\" @click=\"onClick\">\r\n        <img src=\"@/assets/icons/night_review.svg\" style=\"filter: grayscale(1) brightness(2)\" class=\"w-[19px]\" alt=\"\" />\r\n        <span class=\"report-text\">{{ t('nightReview') }}</span>\r\n      </div>\r\n    </el-tooltip>\r\n  </div>\r\n  <el-dialog v-model=\"visible\" :title=\"t('nightReview')\" width=\"800px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <div class=\"box\">\r\n      <el-progress type=\"dashboard\" :percentage=\"percentage\" :color=\"colors\" />\r\n      <div style=\"margin-top: 28px\">\r\n        <span> {{ t('businessDate') }}:&nbsp;&nbsp; {{ bizDate }} </span>\r\n        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\r\n        <span>\r\n          {{ t('operator') }}: &nbsp;&nbsp;<span>{{ userStore.username }}</span>\r\n        </span>\r\n      </div>\r\n      <el-button v-if=\"type === 1\" type=\"primary\" class=\"btn\" @click=\"onStart\">\r\n        {{ t('startNightReview') }}\r\n      </el-button>\r\n      <div v-else-if=\"type === 2\" class=\"btn conducting\">\r\n        <b>{{ t('conductingNightReview') }}&nbsp;&nbsp;{{ percentage }}%</b>\r\n      </div>\r\n      <div v-else-if=\"type === 3\" class=\"btn already\">\r\n        {{ t('todayNightReview') }}\r\n      </div>\r\n      <div v-else-if=\"type === 4\" class=\"btn special\">\r\n        <el-icon size=\"20\" color=\"#F0924A\" style=\"position: relative; top: 4px\">\r\n          <InfoFilled />\r\n        </el-icon>\r\n        {{ t('autoNightReview') }}\r\n      </div>\r\n      <div class=\"text_box\">\r\n        <div>{{ t('nightReviewWork') }}</div>\r\n        <p>\r\n          {{ t('reviewTask1Main') }} <span>{{ t('reviewTask1Detail') }}</span>\r\n        </p>\r\n        <p>\r\n          {{ t('reviewTask2Main') }} <span>{{ t('reviewTask2Detail') }}</span>\r\n        </p>\r\n        <p>\r\n          {{ t('reviewTask3Main') }} <span>{{ t('reviewTask3Detail') }}</span>\r\n        </p>\r\n        <p>\r\n          {{ t('reviewTask4Main') }} <span>{{ t('reviewTask4Detail') }}</span>\r\n        </p>\r\n        <p>\r\n          {{ t('reviewTask5Main') }} <span>{{ t('reviewTask5Detail') }}</span>\r\n        </p>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box {\r\n  padding: 16px 48px;\r\n  text-align: center;\r\n\r\n  .btn {\r\n    width: 255px;\r\n    height: 35px;\r\n    margin: 50px 0 30px;\r\n    line-height: 35px;\r\n    text-align: center;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .already {\r\n    display: inline-block;\r\n    background-color: rgb(239 239 239);\r\n  }\r\n\r\n  .conducting {\r\n    display: inline-block;\r\n    color: #333;\r\n    background-color: #efefef;\r\n  }\r\n\r\n  .special {\r\n    display: inline-block;\r\n    color: rgb(16 16 16);\r\n    background-color: #fff;\r\n    border: 1px solid rgb(236 236 235);\r\n  }\r\n\r\n  .text_box {\r\n    padding: 20px 42px;\r\n    text-align: left;\r\n    background-color: #d9e3e6;\r\n    border: 1px solid rgb(239 239 239 / 100%);\r\n    border-radius: 12px;\r\n\r\n    p {\r\n      margin: 18px 0 0 18px;\r\n      color: #333;\r\n\r\n      span {\r\n        margin-left: 16px;\r\n        color: #6c6c6c;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.icon-with-text {\r\n  cursor: pointer;\r\n  @include flex-center(column);\r\n  padding: 0 10px;\r\n\r\n  .report-text {\r\n    line-height: 1.5;\r\n    font-size: 12px;\r\n    color: rgb(255 255 255 / 85%);\r\n  }\r\n}\r\n</style>\r\n"], "names": ["BASE_PATH", "nightAudiApi", "data", "api", "post", "hcode", "get", "params", "t", "useI18n", "userStore", "useUserStore", "percentage", "ref", "type", "colors", "color", "visible", "onClick", "value", "bizDate", "onStart", "gcode", "then", "res", "code", "time", "setInterval", "clearInterval", "getBizDate", "storage", "local", "set", "auto", "handEndTime", "handStartTime", "generalConfigApi", "getBizData", "onMounted", "nightAudiSetApi", "getNightAudiSet"], "mappings": "qjBAAA,MCEMA,EAAY,2BAEHC,EAOKC,GAChBC,EAAIC,KAAK,GAAGJ,UAAmBE,GARpBD,EAcCI,GACZF,EAAIG,IAAI,GAAGN,kBAA2B,CAAEO,OAAQ,CAAEF,oSCyDhD,MAAAG,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAaC,EAAI,GACjBC,EAAOD,EAAI,GACXE,EAAS,CACb,CAAEC,MAAO,UAAWJ,WAAY,IAChC,CAAEI,MAAO,UAAWJ,WAAY,IAChC,CAAEI,MAAO,UAAWJ,WAAY,IAChC,CAAEI,MAAO,UAAWJ,WAAY,IAChC,CAAEI,MAAO,UAAWJ,WAAY,MAG5BK,EAAUJ,GAAI,GACpB,SAASK,IACPD,EAAQE,OAAQ,CAAA,CAEZ,MAAAC,EAAUP,EAAY,IAC5B,SAASQ,IACPpB,EAA6B,CAAEqB,MAAOZ,EAAUY,MAAOjB,MAAOK,EAAUL,QAASkB,MAAMC,IACjF,GAAa,IAAbA,EAAIC,KAAY,CAClBX,EAAKK,MAAQ,EACP,MAAAO,EAAOC,aAAY,KACZf,EAAAO,MAASP,EAAWO,MAAQ,IAAO,GACrB,MAArBP,EAAWO,QACbL,EAAKK,MAAQ,EACbS,cAAcF,GACHG,IACXC,EAAQC,MAAMC,IAAI,UAAWZ,EAAQD,OAAK,GAE3C,IAAG,IAET,CAEH,MAAMjB,EAAOW,EAAI,CAIfoB,KAAM,IAINC,YAAa,GAIbC,cAAe,KAyBjB,SAASN,IACUO,EAAAC,WAAW,CAAEhC,MAAOK,EAAUL,QAASkB,MAAMC,IAC3C,IAAbA,EAAIC,OACNL,EAAQD,MAAQK,EAAItB,KAAA,GAEvB,QA3BHoC,GAAU,KAORrC,EAAyBS,EAAUL,OAAOkB,MAAMC,IAC7B,IAAbA,EAAIC,OAA2B,IAAbD,EAAItB,OACxBY,EAAKK,MAAQ,EAAA,IAMjBoB,EAAgBC,gBAAgB9B,EAAUY,OAAOC,MAAMC,IACpC,IAAbA,EAAIC,OACNvB,EAAKiB,MAAQK,EAAItB,KAAA,IAdV2B,GAAA,gOFhIE"}