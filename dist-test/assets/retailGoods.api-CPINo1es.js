import{a as t}from"./index-CkEhI1Zk.js";const e="/admin-api/erp",a={pageList:a=>t.get(`${e}/product/page/stock`,{params:a}),list:a=>t.get(`${e}/product/page/pay-stock`,{params:a}),getProductListCategory:a=>t.get(`${e}/product-category/list`,{params:a}),getProductSimpleListCategory:a=>t.get(`${e}/product-category/simple-list`,{params:a}),createProductCategory:a=>t.post(`${e}/product-category/create`,a),updateProductCategory:a=>t.put(`${e}/product-category/update`,a),deleteProductCategory:a=>t.delete(`${e}/product-category/delete`,{params:a}),createProduct:a=>t.post(`${e}/product/create`,a),updateProduct:a=>t.put(`${e}/product/update`,a),changeStatus:a=>t.put(`${e}/retail-goods/update-status`,a),deleteProduct:a=>t.delete(`${e}/product/delete`,{params:a}),getSimpleList:a=>t.get(`${e}/product-unit/simple-list`,{params:a})};export{a as r};
//# sourceMappingURL=retailGoods.api-CPINo1es.js.map
