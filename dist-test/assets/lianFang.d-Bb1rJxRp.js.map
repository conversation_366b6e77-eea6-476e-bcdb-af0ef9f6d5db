{"version": 3, "file": "lianFang.d-Bb1rJxRp.js", "sources": ["../../src/views/room/components/lianFang/lianFang.d.ts"], "sourcesContent": ["/** 需要联房的订单(接待单) */\r\ndeclare namespace lianFangType {\r\n  /** 请求需要联房类型 */\r\n  interface lianFangTypeQuery extends hgCode, PageQuery {\r\n    /** 需排除的房号 */\r\n    pcrNo?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 关键字(预订单查询用) */\r\n    keyword?: string\r\n  }\r\n  interface PageResultReceptionOrderRespVO {\r\n    /** 数据 */\r\n    list?: ReceptionOrderRespVO[]\r\n    /** 总量 */\r\n    total?: number\r\n  }\r\n  /** 需要关联的订单 接待单 */\r\n  interface ReceptionOrderRespVO {\r\n    /** 入住时间 */\r\n    checkinTime?: string\r\n    /** 退房时间 */\r\n    checkoutTime?: string\r\n    /** 客人名称 */\r\n    name?: string\r\n    /** 订单号 */\r\n    orderNo?: string\r\n    /** 预离时间 */\r\n    planCheckoutTime?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n    /** 订单状态;订单状态（在住:check_in 、已离店:check_out） */\r\n    state?: string\r\n  }\r\n\r\n  /** 预订单分页结果 */\r\n  interface PageResultBookOrderRespVO {\r\n    /** 数据 */\r\n    list?: BookOrderRespVO[]\r\n    /** 总量 */\r\n    total?: number\r\n  }\r\n\r\n  /** 预订单数据 */\r\n  interface BookOrderRespVO {\r\n    /** 预订单ID */\r\n    id?: number\r\n    /** 预订单号 */\r\n    bookNo?: string\r\n    /** 预抵时间 */\r\n    planCheckinTime?: string\r\n    /** 预离时间 */\r\n    planCheckoutTime?: string\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n    /** 状态 */\r\n    state?: string\r\n    /** 状态名称 */\r\n    stateName?: string\r\n    /** 联系人 */\r\n    contact?: string\r\n    /** 电话 */\r\n    phone?: string\r\n  }\r\n}\r\nexport = lianFangType\r\n"], "names": ["require_lianFang_d_041", "exports", "module", "lianFangType"], "mappings": "qCAuEAA,KAAA,CAAA,gCAAAC,EAAAC,GAAAA,EAASD,QAAAE,YAAA"}