{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-DvSmdj52.js", "sources": ["../../src/components/SearchBar/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\ndefineOptions({\r\n  name: 'SearchBar',\r\n})\r\n\r\nwithDefaults(\r\n  defineProps<{\r\n    showToggle?: boolean\r\n    background?: boolean\r\n  }>(),\r\n  {\r\n    showToggle: true,\r\n    background: false,\r\n  },\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  toggle: [\r\n    value: boolean,\r\n  ]\r\n}>()\r\n\r\nconst fold = defineModel<boolean>('fold', {\r\n  default: true,\r\n})\r\n\r\nfunction toggle() {\r\n  fold.value = !fold.value\r\n  emits('toggle', fold.value)\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div\r\n    class=\"relative\" :class=\"{\r\n      'py-4': showToggle,\r\n      'px-4 bg-[var(--g-bg)] transition': background,\r\n    }\"\r\n  >\r\n    <slot :fold=\"fold\" :toggle=\"toggle\" />\r\n    <div v-if=\"showToggle\" class=\"absolute bottom-0 left-0 w-full translate-y-1/2 text-center\">\r\n      <button class=\"h-5 inline-flex cursor-pointer select-none items-center border-size-0 rounded bg-[var(--g-bg)] px-2 text-xs font-medium outline-none\" @click=\"toggle\">\r\n        <SvgIcon :name=\"fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top' \" />\r\n      </button>\r\n    </div>\r\n  </div>\r\n</template>\r\n"], "names": ["emits", "__emit", "fold", "_useModel", "__props", "toggle", "value"], "mappings": "waAgBA,MAAMA,EAAQC,EAMRC,EAAOC,EAAqBC,EAAA,QAIlC,SAASC,IACFH,EAAAI,OAASJ,EAAKI,MACbN,EAAA,SAAUE,EAAKI,MAAK"}