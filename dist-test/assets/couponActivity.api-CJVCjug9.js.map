{"version": 3, "file": "couponActivity.api-CJVCjug9.js", "sources": ["../../src/api/modules/marketing/coupon/couponActivity.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n/**\r\n * @description: 优惠券活动\r\n */\r\nexport default {\r\n  /**\r\n   * @description: 优惠券活动列表\r\n   */\r\n  // list: (data: {\r\n  //   gcode: string;\r\n  //   hcode?: string;\r\n  //   activityName?: string; // 赠券活动名称\r\n  //   activityType?: string; // 赠券活动类型\r\n  //   dateStart?: string; // 开始日期\r\n  //   dateEnd?: string; // 结束日期\r\n  //   state?: string; //状态\r\n  //   from?: number;\r\n  //   limit?: number;\r\n  // }) => api.post(\"marketing/coupon/activity/list\", data, {}),\r\n  list: (data: any) => api.get('/admin-api/marketing/coupon-activity/list', { params: data }),\r\n  /**\r\n   * 活动明细\r\n   * @param gcode\r\n   * @param activityCode 活动代码\r\n   * @returns\r\n   */\r\n  detail: (data: any) => api.get('/admin-api/marketing/coupon-activity/get', { params: data }),\r\n  /**\r\n   * 创建活动\r\n   * @param data\r\n   * @returns\r\n   */\r\n  // create: (data: any) => api.post(\"marketing/coupon/activity/create\", data, {}),\r\n  create: (data: any) => api.post('/admin-api/marketing/coupon-activity/create', data),\r\n  /**\r\n   * 编辑活动\r\n   * @param data\r\n   * @returns\r\n   */\r\n  edit: (data: any) => api.put('/admin-api/marketing/coupon-activity/update', data),\r\n\r\n  // 活动修改状态\r\n  changeStatus: (data: any) => api.put('/admin-api/marketing/coupon-activity/update-state', data),\r\n\r\n  // 获取优惠卷模版列表\r\n  getTemplateList: (data: any) => api.get('/admin-api/marketing/coupon-template/list', { params: data }),\r\n\r\n  // 树形筛选优惠卷模版列表\r\n  getTreeTemplateList: (data: any) => api.get('/admin-api/marketing/coupon-template/coupon-templatelist-for-recharge-activity', { params: data }),\r\n\r\n}\r\n"], "names": ["couponActivityApi", "list", "data", "api", "get", "params", "detail", "create", "post", "edit", "put", "changeStatus", "getTemplateList", "getTreeTemplateList"], "mappings": "wCAIA,MAAeA,EAAA,CAebC,KAAOC,GAAcC,EAAIC,IAAI,4CAA6C,CAAEC,OAAQH,IAOpFI,OAASJ,GAAcC,EAAIC,IAAI,2CAA4C,CAAEC,OAAQH,IAOrFK,OAASL,GAAcC,EAAIK,KAAK,8CAA+CN,GAM/EO,KAAOP,GAAcC,EAAIO,IAAI,8CAA+CR,GAG5ES,aAAeT,GAAcC,EAAIO,IAAI,oDAAqDR,GAG1FU,gBAAkBV,GAAcC,EAAIC,IAAI,4CAA6C,CAAEC,OAAQH,IAG/FW,oBAAsBX,GAAcC,EAAIC,IAAI,iFAAkF,CAAEC,OAAQH"}