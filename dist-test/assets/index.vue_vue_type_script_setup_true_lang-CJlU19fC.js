import{d as a,ab as e,au as t}from"./index-CkEhI1Zk.js";const l=a({name:"Auth",__name:"index",props:{value:{},all:{type:Boolean}},setup(a){const l=a,s=t();return(a,t)=>(l.all?s.authAll("string"==typeof l.value?[l.value]:l.value):s.auth(l.value))?e(a.$slots,"default",{key:0}):e(a.$slots,"no-auth",{key:1})}});export{l as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-CJlU19fC.js.map
