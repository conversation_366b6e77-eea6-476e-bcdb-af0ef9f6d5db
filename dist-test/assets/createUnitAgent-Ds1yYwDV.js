import{d as e,aj as t,ai as a,b as l,av as i,y as s,aR as d,aq as o,u as n,o as r,c as u,f as m,w as c,h as b,Y as p,e as g,g as f,F as v,ag as _,R as h,E as y,l as S,m as V,b1 as P,j as D,k as x,b2 as N,b5 as C,aS as A,bK as L,b7 as E,s as U,b8 as T,x as j,aT as R}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css               *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import{m as k}from"./member.api-2tU9HGvl.js";import{c as w}from"./channel.api-CM6FWEgD.js";import{g as q}from"./generalConfig.api-CEBBd8kx.js";import{c as I}from"./customer.api-dB3m63zZ.js";import{m as F}from"./merchant.api-BtmIsRm3.js";import{u as H}from"./user.api-BYl7ypOS.js";import{D as M}from"./DictTypeEnum-DKIIlHnN.js";import{y as z}from"./timeutils-Ib6GkGcq.js";import{_ as Q}from"./_plugin-vue_export-helper-BCo6x5W8.js";const O={style:{height:"650px"}},W={key:0},B={style:{margin:"0 5px"}},G=e({__name:"createUnitAgent",setup(e,{expose:Q}){const{t:G}=t(),K=a(),Y=l(!1),X=l(),J=l({gcode:K.gcode,belongHcode:K.hcode,belongHname:K.hname,paCode:"",paName:"",shortName:"",legalPerson:"",telephone:"",address:"",paType:"1",channel:"",isShare:"0",contact:"",phone:"",email:"",seller:"",startDate:(new Date).toString(),endDate:i().add(1,"year").toString(),isEnable:"1",isHidePrice:"0",isCredit:"0",sellLevel:"",commissionLevel:"",remark:"",arSet:{id:"",gcode:K.gcode,arSetCode:"",arSetName:"",unitCode:"",unitName:"",creditAccName:"",creditAccType:"0",creditPayDays:"1",creditPayFix:0,creditQuotaType:"0",creditQuota:0,creditValidType:"0",creditStartDate:"",creditEndDate:"",creditMinusAcc:"0",isEnable:"1",arSetMerchants:[K.hcode],isManual:"0"}}),Z=l([]),$=l([]),ee=l({paName:[{required:!0,message:G("nameRequired"),trigger:"blur"}],sellLevel:[{required:!0,message:G("selectCustomerLevel"),trigger:"blur"}],startDate:[{required:!0,message:G("selectStartDate"),trigger:"blur"}],endDate:[{required:!0,message:G("selectEndDate"),trigger:"blur"}],commissionLevel:[{required:!0,message:G("selectCommissionStrategy"),trigger:"blur"}],"arSet.arSetName":[{required:!0,message:G("accountSetNameRequired"),trigger:"blur"}],creditDateRange:[{validator:function(e,t,a){const{creditStartDate:l,creditEndDate:i}=J.value.arSet;l&&i?a():a(new Error(G("startAndEndDateRequired")))},trigger:"blur"}]});s((()=>{!function(){const e={gcode:K.gcode,hcode:K.hcode,isEnable:1};w.getChannelSimpleList(e).then((e=>{0===e.code&&(Z.value=e.data)}))}(),F.getSimpleList(K.gcode).then((e=>{$.value=e.data})),k.getTypeSimpleList(K.gcode,"1").then((e=>{0===e.code&&(de.value=e.data)})),function(){const e={gcode:K.gcode,hcode:K.hcode};H.listSeller(e).then((e=>{se.value=e.data}))}(),le(),ae.value=M.BROKERAGE_LEVEL,q.list({gcode:K.gcode,type:ae.value}).then((e=>{ie.value=e.data}))}));const te=l([]),ae=l("");function le(){ae.value="0"===J.value.paType?M.PROTOCOL_LEVEL:M.AGENT_LEVEL,q.list({gcode:K.gcode,type:ae.value}).then((e=>{te.value=e.data}))}const ie=l([]);const se=l();const de=l([]);function oe(){J.value.sellLevel="",le()}return Q({submit:()=>new Promise((e=>{X.value&&X.value.validate((t=>{if(t){if("1"===J.value.paType&&!J.value.channel)return d.error(G("selectChannelWhenAgent"));J.value.startDate=z(J.value.startDate),J.value.endDate=z(J.value.endDate),J.value.arSet.creditStartDate=J.value.arSet.creditStartDate?z(J.value.arSet.creditStartDate):"",J.value.arSet.creditEndDate=J.value.arSet.creditEndDate?z(J.value.arSet.creditEndDate):"",I.create(J.value).then((t=>{0===t.code?(d.success({message:G("editSuccess"),center:!0}),e()):d.error({message:t.msg,center:!0})}))}else d.error(G("companyInfoIncomplete"))}))}))}),(e,t)=>{const a=y,l=S,i=V,s=P,d=D,k=x,w=N,q=C,I=A,F=L,H=E,M=U,z=T,Q=j,$=R;return o((r(),u("div",O,[m(Q,{ref_key:"formRef",ref:X,model:n(J),rules:n(ee),"label-width":"140px","label-suffix":"："},{default:c((()=>[m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("type")},{default:c((()=>[m(l,{modelValue:n(J).paType,"onUpdate:modelValue":t[0]||(t[0]=e=>n(J).paType=e),onChange:oe},{default:c((()=>[m(a,{value:"0"},{default:c((()=>[b(p(n(G)("protocolUnit")),1)])),_:1}),m(a,{value:"1"},{default:c((()=>[b(p(n(G)("agent")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>["1"===n(J).paType?(r(),g(i,{key:0,label:n(G)("channel"),style:{position:"relative"}},{default:c((()=>[t[30]||(t[30]=f("span",{style:{position:"absolute",left:"-66px",color:"red"}},"*",-1)),m(k,{modelValue:n(J).channel,"onUpdate:modelValue":t[1]||(t[1]=e=>n(J).channel=e),placeholder:n(G)("selectChannel")},{default:c((()=>[(r(!0),u(v,null,_(n(Z),(e=>(r(),g(d,{key:e.channelCode,label:e.channelName,value:e.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])):h("",!0)])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("belongType")},{default:c((()=>[m(l,{modelValue:n(J).isShare,"onUpdate:modelValue":t[2]||(t[2]=e=>n(J).isShare=e)},{default:c((()=>[m(a,{value:"0"},{default:c((()=>[b(p(n(G)("singleStoreApplicable")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("belongingStore")},{default:c((()=>[b(p(n(K).hname),1)])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("allowCredit")},{default:c((()=>[m(q,{modelValue:n(J).isCredit,"onUpdate:modelValue":t[3]||(t[3]=e=>n(J).isCredit=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(G)("yes"),"inactive-text":n(G)("no")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("hidePrice")},{default:c((()=>[m(q,{modelValue:n(J).isHidePrice,"onUpdate:modelValue":t[4]||(t[4]=e=>n(J).isHidePrice=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(G)("yes"),"inactive-text":n(G)("no")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,null,{default:c((()=>[m(s,{md:24},{default:c((()=>[m(z,{class:"demo-tabs","model-value":"first"},{default:c((()=>[m(H,{label:n(G)("companyInfo"),name:"first"},{default:c((()=>[m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("name"),prop:"paName"},{default:c((()=>[m(I,{modelValue:n(J).paName,"onUpdate:modelValue":t[5]||(t[5]=e=>n(J).paName=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("shortName")},{default:c((()=>[m(I,{modelValue:n(J).shortName,"onUpdate:modelValue":t[6]||(t[6]=e=>n(J).shortName=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("legalPerson")},{default:c((()=>[m(I,{modelValue:n(J).legalPerson,"onUpdate:modelValue":t[7]||(t[7]=e=>n(J).legalPerson=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("telephone")},{default:c((()=>[m(I,{modelValue:n(J).telephone,"onUpdate:modelValue":t[8]||(t[8]=e=>n(J).telephone=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:24},{default:c((()=>[m(i,{label:n(G)("address")},{default:c((()=>[m(I,{modelValue:n(J).address,"onUpdate:modelValue":t[9]||(t[9]=e=>n(J).address=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("startDate"),prop:"startDate"},{default:c((()=>[m(F,{modelValue:n(J).startDate,"onUpdate:modelValue":t[10]||(t[10]=e=>n(J).startDate=e),type:"date",placeholder:n(G)("selectDate")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("endDate"),prop:"endDate"},{default:c((()=>[m(F,{modelValue:n(J).endDate,"onUpdate:modelValue":t[11]||(t[11]=e=>n(J).endDate=e),type:"date",placeholder:n(G)("selectDate")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("salesPerson")},{default:c((()=>[m(k,{modelValue:n(J).seller,"onUpdate:modelValue":t[12]||(t[12]=e=>n(J).seller=e)},{default:c((()=>[(r(!0),u(v,null,_(n(se),(e=>(r(),g(d,{key:e.username,label:e.nickname,value:e.username},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("status")},{default:c((()=>[m(q,{modelValue:n(J).isEnable,"onUpdate:modelValue":t[13]||(t[13]=e=>n(J).isEnable=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(G)("valid"),"inactive-text":n(G)("invalid")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("customerLevel"),prop:"sellLevel"},{default:c((()=>[m(k,{modelValue:n(J).sellLevel,"onUpdate:modelValue":t[14]||(t[14]=e=>n(J).sellLevel=e)},{default:c((()=>[(r(!0),u(v,null,_(n(te),(e=>(r(),g(d,{key:e.code,label:e.name,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("commissionStrategy")},{default:c((()=>[m(k,{modelValue:n(J).commissionLevel,"onUpdate:modelValue":t[15]||(t[15]=e=>n(J).commissionLevel=e),prop:"commissionLevel"},{default:c((()=>[(r(!0),u(v,null,_(n(ie),(e=>(r(),g(d,{key:e.code,label:e.name,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:24},{default:c((()=>[m(i,{label:n(G)("remarks")},{default:c((()=>[m(I,{modelValue:n(J).remark,"onUpdate:modelValue":t[16]||(t[16]=e=>n(J).remark=e),type:"textarea",rows:5,maxlength:"250"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["label"]),m(H,{label:n(G)("contactPerson"),name:"second"},{default:c((()=>[m(w,{gutter:20},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("contactName")},{default:c((()=>[m(I,{modelValue:n(J).contact,"onUpdate:modelValue":t[17]||(t[17]=e=>n(J).contact=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("contactPhone")},{default:c((()=>[m(I,{modelValue:n(J).phone,"onUpdate:modelValue":t[18]||(t[18]=e=>n(J).phone=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:24},{default:c((()=>[m(i,{label:n(G)("email")},{default:c((()=>[m(I,{modelValue:n(J).email,"onUpdate:modelValue":t[19]||(t[19]=e=>n(J).email=e)},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["label"]),"1"===n(J).isCredit?(r(),g(H,{key:0,label:n(G)("creditSetting"),name:"third"},{default:c((()=>[m(w,{gutter:24},{default:c((()=>[m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("accountSetName"),prop:"arSet.arSetName"},{default:c((()=>[m(I,{modelValue:n(J).arSet.arSetName,"onUpdate:modelValue":t[20]||(t[20]=e=>n(J).arSet.arSetName=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{md:12},{default:c((()=>[m(i,{label:n(G)("companyName")},{default:c((()=>[b(p(n(J).paName),1)])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:24},{default:c((()=>[m(i,{label:n(G)("accountType")},{default:c((()=>[m(l,{modelValue:n(J).arSet.creditAccType,"onUpdate:modelValue":t[21]||(t[21]=e=>n(J).arSet.creditAccType=e),class:"ml-4"},{default:c((()=>[m(a,{value:"0",size:"large"},{default:c((()=>[b(p(n(G)("creditAccount")),1)])),_:1}),m(a,{value:"1",size:"large"},{default:c((()=>[b(p(n(G)("prepaidAccount")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),"0"===n(J).arSet.creditAccType?(r(),u("div",W,[m(w,{gutter:20},{default:c((()=>[m(s,{md:24},{default:c((()=>[m(i,{label:n(G)("settlementPeriod")},{default:c((()=>[m(l,{modelValue:n(J).arSet.creditPayDays,"onUpdate:modelValue":t[23]||(t[23]=e=>n(J).arSet.creditPayDays=e),class:"ml-4"},{default:c((()=>[m(a,{value:"1",size:"large"},{default:c((()=>[b(p(n(G)("permanent")),1)])),_:1}),m(a,{value:"0",size:"large"},{default:c((()=>[b(p(n(G)("fixedPeriod")),1)])),_:1}),"0"===n(J).arSet.creditPayDays?(r(),g(M,{key:0,modelValue:n(J).arSet.creditPayFix,"onUpdate:modelValue":t[22]||(t[22]=e=>n(J).arSet.creditPayFix=e),class:"mx-4",min:0,placeholder:n(G)("pleaseEnterFixedPeriod"),"controls-position":"right",precision:0,style:{width:"200px",margin:"0 8px"}},null,8,["modelValue","placeholder"])):h("",!0)])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:24},{default:c((()=>[m(i,{label:n(G)("maximumLimit")},{default:c((()=>[m(l,{modelValue:n(J).arSet.creditQuotaType,"onUpdate:modelValue":t[25]||(t[25]=e=>n(J).arSet.creditQuotaType=e),class:"ml-4"},{default:c((()=>[m(a,{value:"0",size:"large"},{default:c((()=>[b(p(n(G)("unlimited")),1)])),_:1}),m(a,{value:"1",size:"large"},{default:c((()=>[b(p(n(G)("limited")),1)])),_:1}),"1"===n(J).arSet.creditQuotaType?(r(),g(M,{key:0,modelValue:n(J).arSet.creditQuota,"onUpdate:modelValue":t[24]||(t[24]=e=>n(J).arSet.creditQuota=e),class:"mx-4",min:0,placeholder:n(G)("inputMaxLimit"),"controls-position":"right",precision:2,style:{width:"200px",margin:"0 8px"}},null,8,["modelValue","placeholder"])):h("",!0)])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:24},{default:c((()=>[m(i,{label:n(G)("validityPeriod")},{default:c((()=>[m(l,{modelValue:n(J).arSet.creditValidType,"onUpdate:modelValue":t[26]||(t[26]=e=>n(J).arSet.creditValidType=e),class:"ml-4"},{default:c((()=>[m(a,{value:"0",size:"large"},{default:c((()=>[b(p(n(G)("permanentlyValid")),1)])),_:1}),m(a,{value:"1",size:"large"},{default:c((()=>[b(p(n(G)("fixedValid")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),"1"===n(J).arSet.creditValidType?(r(),g(i,{key:0,label:n(G)("dateRange"),prop:"creditDateRange"},{default:c((()=>[m(F,{modelValue:n(J).arSet.creditStartDate,"onUpdate:modelValue":t[27]||(t[27]=e=>n(J).arSet.creditStartDate=e),type:"date",placeholder:n(G)("startDate")},null,8,["modelValue","placeholder"]),f("span",B,p(n(G)("to")),1),m(F,{modelValue:n(J).arSet.creditEndDate,"onUpdate:modelValue":t[28]||(t[28]=e=>n(J).arSet.creditEndDate=e),type:"date",placeholder:n(G)("endDate")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])):h("",!0)])),_:1})])),_:1})])):h("",!0),m(w,{gutter:20},{default:c((()=>[m(s,{md:20},{default:c((()=>[m(i,{label:n(G)("allowNegativeAccount")},{default:c((()=>[m(q,{modelValue:n(J).arSet.creditMinusAcc,"onUpdate:modelValue":t[29]||(t[29]=e=>n(J).arSet.creditMinusAcc=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(G)("yes"),"inactive-text":n(G)("no")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),m(w,{gutter:20},{default:c((()=>[m(s,{md:20},{default:c((()=>[m(i,{label:n(G)("hotelScope")},{default:c((()=>[b(p(n(K).hname),1)])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["label"])):h("",!0)])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),[[$,n(Y)]])}}});function K(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{type:{t:0,b:{t:2,i:[{t:3}],s:"Type"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"Protocol Unit"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"Agent"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"Channel"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"Select Channel"}},belongType:{t:0,b:{t:2,i:[{t:3}],s:"Belonging Type"}},singleStoreApplicable:{t:0,b:{t:2,i:[{t:3}],s:"Single Hotel"}},belongingStore:{t:0,b:{t:2,i:[{t:3}],s:"Belonging Hotel"}},allowCredit:{t:0,b:{t:2,i:[{t:3}],s:"Allow Credit"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"Yes"}},no:{t:0,b:{t:2,i:[{t:3}],s:"No"}},hidePrice:{t:0,b:{t:2,i:[{t:3}],s:"Hide Price"}},registrationFormHidePrice:{t:0,b:{t:2,i:[{t:3}],s:"Registration Form Hide Price"}},companyInfo:{t:0,b:{t:2,i:[{t:3}],s:"Company Info"}},name:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},shortName:{t:0,b:{t:2,i:[{t:3}],s:"Short Name"}},legalPerson:{t:0,b:{t:2,i:[{t:3}],s:"Legal Person"}},telephone:{t:0,b:{t:2,i:[{t:3}],s:"Telephone"}},address:{t:0,b:{t:2,i:[{t:3}],s:"Address"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"Start Date"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"End Date"}},salesPerson:{t:0,b:{t:2,i:[{t:3}],s:"Sales Person"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"Valid"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"Invalid"}},customerLevel:{t:0,b:{t:2,i:[{t:3}],s:"Customer Level"}},commissionStrategy:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Level"}},remarks:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},contactPerson:{t:0,b:{t:2,i:[{t:3}],s:"Contact Person"}},contactName:{t:0,b:{t:2,i:[{t:3}],s:"Contact Name"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"Contact Phone"}},email:{t:0,b:{t:2,i:[{t:3}],s:"Email"}},creditSetting:{t:0,b:{t:2,i:[{t:3}],s:"Credit Setting"}},accountSetName:{t:0,b:{t:2,i:[{t:3}],s:"AR Name"}},companyName:{t:0,b:{t:2,i:[{t:3}],s:"Client Name"}},accountType:{t:0,b:{t:2,i:[{t:3}],s:"Account Type"}},creditAccount:{t:0,b:{t:2,i:[{t:3}],s:"Credit Account"}},prepaidAccount:{t:0,b:{t:2,i:[{t:3}],s:"Prepaid Account"}},settlementPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Settle Period"}},permanent:{t:0,b:{t:2,i:[{t:3}],s:"Permanent"}},fixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Fixed Period"}},maximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"Maximum Limit"}},unlimited:{t:0,b:{t:2,i:[{t:3}],s:"Unlimited"}},limited:{t:0,b:{t:2,i:[{t:3}],s:"Limited"}},validityPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Validity Period"}},permanentlyValid:{t:0,b:{t:2,i:[{t:3}],s:"Permanently Valid"}},fixedValid:{t:0,b:{t:2,i:[{t:3}],s:"Fixed Valid"}},allowNegativeAccount:{t:0,b:{t:2,i:[{t:3}],s:"Allow Negative"}},hotelScope:{t:0,b:{t:2,i:[{t:3}],s:"Hotel Scope"}},nameRequired:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the name"}},selectCustomerLevel:{t:0,b:{t:2,i:[{t:3}],s:"Please select the customer level"}},selectStartDate:{t:0,b:{t:2,i:[{t:3}],s:"Please select the start date"}},selectEndDate:{t:0,b:{t:2,i:[{t:3}],s:"Please select the end date"}},selectCommissionStrategy:{t:0,b:{t:2,i:[{t:3}],s:"Please select the commission strategy"}},accountSetNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the account set name"}},startAndEndDateRequired:{t:0,b:{t:2,i:[{t:3}],s:"Start date and end date cannot be empty"}},selectChannelWhenAgent:{t:0,b:{t:2,i:[{t:3}],s:"Channel cannot be empty when selecting agent type"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Edit successful"}},companyInfoIncomplete:{t:0,b:{t:2,i:[{t:3}],s:"Company information and accounts settings are incomplete"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"Select Date"}},to:{t:0,b:{t:2,i:[{t:3}],s:"To"}},pleaseEnterFixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Please enter fixed period"}},inputMaxLimit:{t:0,b:{t:2,i:[{t:3}],s:"Please enter max limit"}},dateRange:{t:0,b:{t:2,i:[{t:3}],s:"Date range"}}},"zh-cn":{type:{t:0,b:{t:2,i:[{t:3}],s:"类型"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"渠道"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"选择渠道"}},belongType:{t:0,b:{t:2,i:[{t:3}],s:"所属类型"}},singleStoreApplicable:{t:0,b:{t:2,i:[{t:3}],s:"单店适用"}},belongingStore:{t:0,b:{t:2,i:[{t:3}],s:"所属门店"}},allowCredit:{t:0,b:{t:2,i:[{t:3}],s:"允许挂账"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"是"}},no:{t:0,b:{t:2,i:[{t:3}],s:"否"}},hidePrice:{t:0,b:{t:2,i:[{t:3}],s:"登记单隐藏房价"}},registrationFormHidePrice:{t:0,b:{t:2,i:[{t:3}],s:"登记单隐藏房价"}},companyInfo:{t:0,b:{t:2,i:[{t:3}],s:"公司信息"}},name:{t:0,b:{t:2,i:[{t:3}],s:"名称"}},shortName:{t:0,b:{t:2,i:[{t:3}],s:"简称"}},legalPerson:{t:0,b:{t:2,i:[{t:3}],s:"法人"}},telephone:{t:0,b:{t:2,i:[{t:3}],s:"电话"}},address:{t:0,b:{t:2,i:[{t:3}],s:"地址"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"开始日期"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"结束日期"}},salesPerson:{t:0,b:{t:2,i:[{t:3}],s:"销售人员"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"有效"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"无效"}},customerLevel:{t:0,b:{t:2,i:[{t:3}],s:"客户级别"}},commissionStrategy:{t:0,b:{t:2,i:[{t:3}],s:"佣金策略级别"}},remarks:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},contactPerson:{t:0,b:{t:2,i:[{t:3}],s:"联系人"}},contactName:{t:0,b:{t:2,i:[{t:3}],s:"姓名"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"电话"}},email:{t:0,b:{t:2,i:[{t:3}],s:"Email"}},creditSetting:{t:0,b:{t:2,i:[{t:3}],s:"挂账设置"}},accountSetName:{t:0,b:{t:2,i:[{t:3}],s:"账套名称"}},companyName:{t:0,b:{t:2,i:[{t:3}],s:"公司名称"}},accountType:{t:0,b:{t:2,i:[{t:3}],s:"账户类型"}},creditAccount:{t:0,b:{t:2,i:[{t:3}],s:"信用账户"}},prepaidAccount:{t:0,b:{t:2,i:[{t:3}],s:"预付账户"}},settlementPeriod:{t:0,b:{t:2,i:[{t:3}],s:"结算账期"}},permanent:{t:0,b:{t:2,i:[{t:3}],s:"永久账期"}},fixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"固定账期"}},maximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"最大额度"}},unlimited:{t:0,b:{t:2,i:[{t:3}],s:"不限额度"}},limited:{t:0,b:{t:2,i:[{t:3}],s:"限制额度"}},validityPeriod:{t:0,b:{t:2,i:[{t:3}],s:"有效时间"}},permanentlyValid:{t:0,b:{t:2,i:[{t:3}],s:"永久有效"}},fixedValid:{t:0,b:{t:2,i:[{t:3}],s:"固定有效"}},allowNegativeAccount:{t:0,b:{t:2,i:[{t:3}],s:"允许负账"}},hotelScope:{t:0,b:{t:2,i:[{t:3}],s:"酒店范围"}},nameRequired:{t:0,b:{t:2,i:[{t:3}],s:"请输入名称"}},selectCustomerLevel:{t:0,b:{t:2,i:[{t:3}],s:"请选择客户级别"}},selectStartDate:{t:0,b:{t:2,i:[{t:3}],s:"请选择开始日期"}},selectEndDate:{t:0,b:{t:2,i:[{t:3}],s:"请选择结束日期"}},selectCommissionStrategy:{t:0,b:{t:2,i:[{t:3}],s:"请选择佣金策略"}},accountSetNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"请输入账套名称"}},startAndEndDateRequired:{t:0,b:{t:2,i:[{t:3}],s:"开始日期和结束日期不能为空"}},selectChannelWhenAgent:{t:0,b:{t:2,i:[{t:3}],s:"选择中介类型时渠道不能为空"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"编辑成功"}},companyInfoIncomplete:{t:0,b:{t:2,i:[{t:3}],s:"公司信息、挂账设置模块有信息未填写"}},to:{t:0,b:{t:2,i:[{t:3}],s:"至"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"选择日期"}},pleaseEnterFixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"请输入固定账期"}},inputMaxLimit:{t:0,b:{t:2,i:[{t:3}],s:"请输入最大限额"}},dateRange:{t:0,b:{t:2,i:[{t:3}],s:"日期范围"}}},km:{type:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទ"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"អង្គភាពព្រមាន"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"ភ្នាក់ងារ"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសឆានែល"}},belongType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទជាកម្មសិទ្ធ"}},singleStoreApplicable:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារតែមួយ"}},belongingStore:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារជាកម្មសិទ្ធ"}},allowCredit:{t:0,b:{t:2,i:[{t:3}],s:"អនុញ្ញាតឱ្យខ្ចីប្រាក់"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"បាទ/ចាស"}},no:{t:0,b:{t:2,i:[{t:3}],s:"ទេ"}},hidePrice:{t:0,b:{t:2,i:[{t:3}],s:"លាក់តម្លៃនៅលើទម្រង់ចុះឈ្មោះ"}},registrationFormHidePrice:{t:0,b:{t:2,i:[{t:3}],s:"លាក់តម្លៃនៅលើទម្រង់ចុះឈ្មោះ"}},companyInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានក្រុមហ៊ុន"}},name:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},shortName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះខ្លី"}},legalPerson:{t:0,b:{t:2,i:[{t:3}],s:"មន្ត្រីច្បាប់"}},telephone:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទ"}},address:{t:0,b:{t:2,i:[{t:3}],s:"អាសយដ្ឋាន"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទចាប់ផ្តើម"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទបញ្ចប់"}},salesPerson:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកលក់"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"មានសុពលភាព"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានសុពលភាព"}},customerLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតអតិថិជន"}},commissionStrategy:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតយុទ្ធសាស្ត្រកំរៃ"}},remarks:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}},contactPerson:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកទំនាក់ទំនង"}},contactName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទ"}},email:{t:0,b:{t:2,i:[{t:3}],s:"អ៊ីមែល"}},creditSetting:{t:0,b:{t:2,i:[{t:3}],s:"ការកំណត់ឥណទាន"}},accountSetName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះគណនី"}},companyName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះក្រុមហ៊ុន"}},accountType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទគណនី"}},creditAccount:{t:0,b:{t:2,i:[{t:3}],s:"គណនីឥណទាន"}},prepaidAccount:{t:0,b:{t:2,i:[{t:3}],s:"គណនីបង់មុន"}},settlementPeriod:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលសំណង"}},permanent:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលអចិន្ត្រៃយ៍"}},fixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលថេរ"}},maximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"ដែនកំណត់អតិបរមា"}},unlimited:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានដែនកំណត់"}},limited:{t:0,b:{t:2,i:[{t:3}],s:"ដែនកំណត់"}},validityPeriod:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលសុពលភាព"}},permanentlyValid:{t:0,b:{t:2,i:[{t:3}],s:"មានសុពលភាពអចិន្ត្រៃយ៍"}},fixedValid:{t:0,b:{t:2,i:[{t:3}],s:"សុពលភាពថេរ"}},allowNegativeAccount:{t:0,b:{t:2,i:[{t:3}],s:"អនុញ្ញាតឱ្យគណនីអវិជ្ជមាន"}},hotelScope:{t:0,b:{t:2,i:[{t:3}],s:"វិសាលភាពសណ្ឋាគារ"}},nameRequired:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះ"}},selectCustomerLevel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកម្រិតអតិថិជន"}},selectStartDate:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកាលបរិច្ឆេទចាប់ផ្តើម"}},selectEndDate:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកាលបរិច្ឆេទបញ្ចប់"}},selectCommissionStrategy:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសយុទ្ធសាស្ត្រកំរៃ"}},accountSetNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះគណនី"}},startAndEndDateRequired:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទចាប់ផ្តើមនិងបញ្ចប់មិនអាចទទេបានទេ"}},selectChannelWhenAgent:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែលមិនអាចទទេបានទេនៅពេលជ្រើសរើសប្រភេទភ្នាក់ងារ"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួលដោយជោគជ័យ"}},companyInfoIncomplete:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានក្រុមហ៊ុន និងការកំណត់គណនីមានព័ត៌មានមិនពេញលេញ"}},to:{t:0,b:{t:2,i:[{t:3}],s:"ដល់"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសកាលបរិច្ឆេទ"}},pleaseEnterFixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលរយៈពេលថេរ"}},inputMaxLimit:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលដែនកំណត់អតិបរមា"}},dateRange:{t:0,b:{t:2,i:[{t:3}],s:"ចន្លោះកាលបរិច្ឆេទ"}}}}})}K(G);const Y=Q(G,[["__scopeId","data-v-951915e9"]]);export{Y as default};
//# sourceMappingURL=createUnitAgent-Ds1yYwDV.js.map
