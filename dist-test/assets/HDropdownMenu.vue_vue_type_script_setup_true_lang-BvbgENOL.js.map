{"version": 3, "file": "HDropdownMenu.vue_vue_type_script_setup_true_lang-BvbgENOL.js", "sources": ["../../src/layouts/ui-kit/HDropdownMenu.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nconst props = defineProps<{\r\n  items: {\r\n    label: string\r\n    disabled?: boolean\r\n    hide?: boolean\r\n    handle?: () => void\r\n  }[][]\r\n}>()\r\n\r\nconst myItems = computed(() => {\r\n  return props.items.map((item) => {\r\n    return item.filter(v => !v.hide)\r\n  }).filter(v => v.length)\r\n})\r\n</script>\r\n\r\n<template>\r\n  <VMenu :show-triggers=\"['hover']\" :auto-hide=\"false\" :popper-triggers=\"['hover', 'click']\" :delay=\"200\" v-bind=\"$attrs\">\r\n    <slot />\r\n    <template #popper>\r\n      <div v-for=\"(item, index) in myItems\" :key=\"index\" class=\"b-b-1 b-b-stone-2 b-b-solid p-1 dark-b-b-1 dark-b-b-stone-7 last-b-b-size-0\">\r\n        <button v-for=\"(v, i) in item\" :key=\"i\" :disabled=\"v.disabled\" class=\"w-full flex cursor-pointer items-center gap-2 border-size-0 rounded-md bg-inherit px-2 py-1.5 text-sm text-dark disabled-cursor-not-allowed dark-text-white disabled-opacity-50 hover-not-disabled-bg-stone-1 dark-hover-not-disabled-bg-stone-9\" @click=\"v.handle\">\r\n          {{ v.label }}\r\n        </button>\r\n      </div>\r\n    </template>\r\n  </VMenu>\r\n</template>\r\n"], "names": ["props", "__props", "myItems", "computed", "items", "map", "item", "filter", "v", "hide", "length"], "mappings": "qNACA,MAAMA,EAAQC,EASRC,EAAUC,GAAS,IAChBH,EAAMI,MAAMC,KAAKC,GACfA,EAAKC,QAAYC,IAACA,EAAEC,SAC1BF,QAAOC,GAAKA,EAAEE"}