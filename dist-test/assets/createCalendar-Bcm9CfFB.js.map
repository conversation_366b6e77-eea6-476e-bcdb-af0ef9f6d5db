{"version": 3, "file": "createCalendar-Bcm9CfFB.js", "sources": ["../../src/views/sell/price/calendar/components/DetailForm/createCalendar.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\n// import api from '@/api/modules/calendar.api'\r\nimport useUserStore from '@/store/modules/user'\r\n// import merchantApi from '@/api/modules/merchant.api'\r\nconst props = withDefaults(\r\n  defineProps<DetailFormProps>(),\r\n  {\r\n    calendarCode: '',\r\n  },\r\n)\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  calendarCode: '',\r\n  calendarName: '',\r\n  merchant: { hcode: '', hname: '' },\r\n  scope: '1',\r\n  dateScope: [],\r\n  calendarMode: '0',\r\n  calendarCondition: '',\r\n  calendarConditions: [],\r\n  calendarDate: '',\r\n  isEnable: false,\r\n  isUse: false,\r\n})\r\nconst formRules = ref<FormRules>({\r\n  calendarName: [\r\n    { required: true, message: '请输入日历名称', trigger: 'blur' },\r\n  ],\r\n})\r\n\r\n// 酒店列表\r\nconst hotels = ref<{ hcode: string, hname: string }[]>([])\r\nonMounted(() => {\r\n  getHotels()\r\n})\r\n\r\nfunction getHotels() {\r\n  merchantApi.list({ gcode: userStore.gcode, state: 'ZT_ZSYY' }).then((res: any) => {\r\n    hotels.value = res.data.list\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value && formRef.value.validate((valid) => {\r\n        if (valid) {\r\n          api.create(form.value).then(() => {\r\n            ElMessage.success({\r\n              message: '新增成功',\r\n              center: true,\r\n            })\r\n            resolve()\r\n          })\r\n        }\r\n      })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"120px\" label-suffix=\"：\">\r\n      <el-form-item label=\"日历名称\" prop=\"calendarName\">\r\n        <el-input v-model=\"form.calendarName\" placeholder=\"请输入日历名称\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"时间范围\">\r\n        <el-date-picker\r\n          v-model=\"form.calendarDate\" type=\"daterange\" range-separator=\"到\" start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"选择方式\">\r\n        <el-radio-group v-model=\"form.calendarMode\" class=\"ml-4\">\r\n          <el-radio value=\"0\" size=\"large\">\r\n            按周\r\n          </el-radio>\r\n          <el-radio value=\"1\" size=\"large\">\r\n            按日\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"选择条件\">\r\n        <el-checkbox-group v-if=\"form.calendarMode === '0'\" v-model=\"form.calendarConditions\">\r\n          <el-checkbox label=\"0\">\r\n            周日\r\n          </el-checkbox>\r\n          <el-checkbox label=\"1\">\r\n            周一\r\n          </el-checkbox>\r\n          <el-checkbox label=\"2\">\r\n            周二\r\n          </el-checkbox>\r\n          <el-checkbox label=\"3\">\r\n            周三\r\n          </el-checkbox>\r\n          <el-checkbox label=\"4\">\r\n            周四\r\n          </el-checkbox>\r\n          <el-checkbox label=\"5\">\r\n            周五\r\n          </el-checkbox>\r\n          <el-checkbox label=\"6\">\r\n            周六\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n        <el-checkbox-group v-if=\"form.calendarMode === '1'\" v-model=\"form.calendarConditions\">\r\n          <el-checkbox v-for=\"i in 31\" :label=\"i\" />\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "calendarCode", "calendarName", "merchant", "hcode", "hname", "scope", "dateScope", "calendarMode", "calendarCondition", "calendarConditions", "calendarDate", "isEnable", "isUse", "formRules", "required", "message", "trigger", "hotels", "onMounted", "merchantApi", "list", "state", "then", "res", "value", "data", "__expose", "submit", "Promise", "resolve", "validate", "valid", "api", "create", "ElMessage", "success", "center"], "mappings": "qyBAYA,MAAMA,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,MAAON,EAAUM,MACjBC,aAAc,GACdC,aAAc,GACdC,SAAU,CAAEC,MAAO,GAAIC,MAAO,IAC9BC,MAAO,IACPC,UAAW,GACXC,aAAc,IACdC,kBAAmB,GACnBC,mBAAoB,GACpBC,aAAc,GACdC,UAAU,EACVC,OAAO,IAEHC,EAAYjB,EAAe,CAC/BK,aAAc,CACZ,CAAEa,UAAU,EAAMC,QAAS,UAAWC,QAAS,WAK7CC,EAASrB,EAAwC,WACvDsB,GAAU,KAKIC,YAAAC,KAAK,CAAErB,MAAON,EAAUM,MAAOsB,MAAO,YAAaC,MAAMC,IAC5DN,EAAAO,MAAQD,EAAIE,KAAKL,IAAA,GALhB,IASCM,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBhC,EAAQ2B,OAAS3B,EAAQ2B,MAAMM,UAAUC,IACnCA,GACFC,IAAIC,OAAOnC,EAAK0B,OAAOF,MAAK,KAC1BY,EAAUC,QAAQ,CAChBpB,QAAS,OACTqB,QAAQ,IAEFP,GAAA,GACT,GAEJ"}