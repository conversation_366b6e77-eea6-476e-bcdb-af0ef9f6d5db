{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-Do_Ql3Te.js", "sources": ["../../src/layouts/components/MenuPanel/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { MenuInjection, MenuProps } from './types'\r\nimport Item from './item.vue'\r\nimport SubMenu from './sub.vue'\r\nimport { rootMenuInjectionKey } from './types'\r\n\r\ndefineOptions({\r\n  name: 'MainMenu',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<MenuProps>(),\r\n  {\r\n    mode: 'vertical',\r\n    collapse: false,\r\n    showCollapseName: false,\r\n    rounded: false,\r\n    direction: 'ltr',\r\n  },\r\n)\r\n\r\nconst activeIndex = ref<MenuInjection['activeIndex']>(props.value)\r\nconst items = ref<MenuInjection['items']>({})\r\nconst subMenus = ref<MenuInjection['subMenus']>({})\r\nconst openedMenus = ref<MenuInjection['openedMenus']>([])\r\nconst mouseInMenu = ref<MenuInjection['mouseInMenu']>([])\r\n\r\n// 解析传入的 menu 数据，并保存到 items 和 subMenus 对象中\r\nfunction initItems(menu: MenuProps['menu'], parentPaths: string[] = []) {\r\n  menu.forEach((item) => {\r\n    const index = item.path ?? JSON.stringify(item)\r\n    if (item.children) {\r\n      const indexPath = [...parentPaths, index]\r\n      subMenus.value[index] = {\r\n        index,\r\n        indexPath,\r\n        active: false,\r\n      }\r\n      initItems(item.children, indexPath)\r\n    }\r\n    else {\r\n      items.value[index] = {\r\n        index,\r\n        indexPath: parentPaths,\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\nconst openMenu: MenuInjection['openMenu'] = (index) => {\r\n  if (openedMenus.value.includes(index)) {\r\n    return\r\n  }\r\n  openedMenus.value.push(index)\r\n}\r\nconst closeMenu: MenuInjection['closeMenu'] = (index) => {\r\n  if (Array.isArray(index)) {\r\n    nextTick(() => {\r\n      closeMenu(index.at(-1)!)\r\n      if (index.length > 1) {\r\n        closeMenu(index.slice(0, -1))\r\n      }\r\n    })\r\n    return\r\n  }\r\n  Object.keys(subMenus.value).forEach((item) => {\r\n    if (subMenus.value[item].indexPath.includes(index)) {\r\n      openedMenus.value = openedMenus.value.filter(item => item !== index)\r\n    }\r\n  })\r\n}\r\n\r\nfunction setSubMenusActive(index: string) {\r\n  for (const key in subMenus.value) {\r\n    subMenus.value[key].active = false\r\n  }\r\n  subMenus.value[index]?.indexPath.forEach((idx) => {\r\n    subMenus.value[idx].active = true\r\n  })\r\n  items.value[index]?.indexPath.forEach((idx) => {\r\n    subMenus.value[idx].active = true\r\n  })\r\n}\r\n\r\nconst handleMenuItemClick: MenuInjection['handleMenuItemClick'] = (index) => {\r\n  if (props.mode === 'horizontal' || props.collapse) {\r\n    openedMenus.value = []\r\n  }\r\n  setSubMenusActive(index)\r\n}\r\nconst handleSubMenuClick: MenuInjection['handleSubMenuClick'] = (index) => {\r\n  if (openedMenus.value.includes(index)) {\r\n    closeMenu(index)\r\n  }\r\n  else {\r\n    openMenu(index)\r\n  }\r\n}\r\n\r\nfunction initMenu() {\r\n  const activeItem = activeIndex.value && items.value[activeIndex.value]\r\n  setSubMenusActive(activeIndex.value)\r\n  if (!activeItem || props.collapse) {\r\n    return\r\n  }\r\n  // 展开该菜单项的路径上所有子菜单\r\n  activeItem.indexPath.forEach((index) => {\r\n    const subMenu = subMenus.value[index]\r\n    subMenu && openMenu(index)\r\n  })\r\n}\r\n\r\nwatch(() => props.menu, (val) => {\r\n  initItems(val)\r\n  initMenu()\r\n}, {\r\n  deep: true,\r\n  immediate: true,\r\n})\r\n\r\nwatch(() => props.value, (currentValue) => {\r\n  if (!items.value[currentValue]) {\r\n    activeIndex.value = ''\r\n  }\r\n  const item = items.value[currentValue] || (activeIndex.value && items.value[activeIndex.value]) || items.value[props.value]\r\n  if (item) {\r\n    activeIndex.value = item.index\r\n  }\r\n  else {\r\n    activeIndex.value = currentValue\r\n  }\r\n  initMenu()\r\n})\r\n\r\nwatch(() => props.collapse, (value) => {\r\n  if (value) {\r\n    openedMenus.value = []\r\n  }\r\n  initMenu()\r\n})\r\n\r\nprovide(rootMenuInjectionKey, reactive({\r\n  props,\r\n  items,\r\n  subMenus,\r\n  activeIndex,\r\n  openedMenus,\r\n  mouseInMenu,\r\n  openMenu,\r\n  closeMenu,\r\n  handleMenuItemClick,\r\n  handleSubMenuClick,\r\n}))\r\n</script>\r\n\r\n<template>\r\n  <div\r\n    class=\"h-full w-full flex flex-col of-hidden transition-all\" :class=\"{\r\n      'flex-row! w-auto!': props.mode === 'horizontal',\r\n      'py-1': props.mode === 'vertical',\r\n    }\"\r\n  >\r\n    <template v-for=\"item in menu\" :key=\"item.path ?? JSON.stringify(item)\">\r\n      <template v-if=\"item.meta?.menu !== false\">\r\n        <SubMenu v-if=\"item.children?.length\" :menu=\"item\" :unique-key=\"[item.path ?? (item.children.every(item => item.meta?.menu === false) ? item.children[0].path! : JSON.stringify(item))]\" />\r\n        <Item v-else :item=\"item\" :unique-key=\"[item.path ?? (item.children?.every(item => item.meta?.menu === false) ? item.children[0].path! : JSON.stringify(item))]\" @click=\"handleMenuItemClick(item.path ?? (item.children?.every(item => item.meta?.menu === false) ? item.children[0].path! : JSON.stringify(item)))\" />\r\n      </template>\r\n    </template>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "activeIndex", "ref", "value", "items", "subMenus", "openedMenus", "mouseInMenu", "initItems", "menu", "parentPaths", "for<PERSON>ach", "item", "index", "path", "JSON", "stringify", "children", "indexPath", "active", "openMenu", "includes", "push", "closeMenu", "Array", "isArray", "nextTick", "at", "length", "slice", "Object", "keys", "filter", "setSubMenusActive", "key", "_a", "idx", "_b", "handleMenuItemClick", "mode", "collapse", "initMenu", "activeItem", "watch", "val", "deep", "immediate", "currentValue", "provide", "rootMenuInjectionKey", "reactive", "handleSubMenuClick"], "mappings": "weAUA,MAAMA,EAAQC,EAWRC,EAAcC,EAAkCH,EAAMI,OACtDC,EAAQF,EAA4B,IACpCG,EAAWH,EAA+B,IAC1CI,EAAcJ,EAAkC,IAChDK,EAAcL,EAAkC,IAGtD,SAASM,EAAUC,EAAyBC,EAAwB,IAC7DD,EAAAE,SAASC,IACZ,MAAMC,EAAQD,EAAKE,MAAQC,KAAKC,UAAUJ,GAC1C,GAAIA,EAAKK,SAAU,CACjB,MAAMC,EAAY,IAAIR,EAAaG,GAC1BR,EAAAF,MAAMU,GAAS,CACtBA,QACAK,YACAC,QAAQ,GAEAX,EAAAI,EAAKK,SAAUC,EAAS,MAG5Bd,EAAAD,MAAMU,GAAS,CACnBA,QACAK,UAAWR,EACb,GAEH,CAGG,MAAAU,EAAuCP,IACvCP,EAAYH,MAAMkB,SAASR,IAGnBP,EAAAH,MAAMmB,KAAKT,EAAK,EAExBU,EAAyCV,IACzCW,MAAMC,QAAQZ,GAChBa,GAAS,KACGH,EAAAV,EAAMc,IAAG,IACfd,EAAMe,OAAS,GACjBL,EAAUV,EAAMgB,MAAM,GAAG,GAAG,IAKlCC,OAAOC,KAAK1B,EAASF,OAAOQ,SAASC,IAC/BP,EAASF,MAAMS,GAAMM,UAAUG,SAASR,KAC1CP,EAAYH,MAAQG,EAAYH,MAAM6B,QAAOpB,GAAQA,IAASC,IAAK,GAEtE,EAGH,SAASoB,EAAkBpB,WACd,IAAA,MAAAqB,KAAO7B,EAASF,MAChBE,EAAAF,MAAM+B,GAAKf,QAAS,EAE/B,OAAAgB,EAAA9B,EAASF,MAAMU,OAAQK,UAAUP,SAASyB,IAC/B/B,EAAAF,MAAMiC,GAAKjB,QAAS,CAAA,IAE/B,OAAAkB,EAAAjC,EAAMD,MAAMU,OAAQK,UAAUP,SAASyB,IAC5B/B,EAAAF,MAAMiC,GAAKjB,QAAS,CAAA,GAC9B,CAGG,MAAAmB,EAA6DzB,KAC9C,eAAfd,EAAMwC,MAAyBxC,EAAMyC,YACvClC,EAAYH,MAAQ,IAEtB8B,EAAkBpB,EAAK,EAWzB,SAAS4B,IACP,MAAMC,EAAazC,EAAYE,OAASC,EAAMD,MAAMF,EAAYE,OAChE8B,EAAkBhC,EAAYE,OACzBuC,IAAc3C,EAAMyC,UAIdE,EAAAxB,UAAUP,SAASE,IACZR,EAASF,MAAMU,IACpBO,EAASP,EAAK,GAC1B,QAGH8B,GAAM,IAAM5C,EAAMU,OAAOmC,IACvBpC,EAAUoC,GACDH,GAAA,GACR,CACDI,MAAM,EACNC,WAAW,IAGbH,GAAM,IAAM5C,EAAMI,QAAQ4C,IACnB3C,EAAMD,MAAM4C,KACf9C,EAAYE,MAAQ,IAEtB,MAAMS,EAAOR,EAAMD,MAAM4C,IAAkB9C,EAAYE,OAASC,EAAMD,MAAMF,EAAYE,QAAWC,EAAMD,MAAMJ,EAAMI,OAEnHF,EAAYE,MADVS,EACkBA,EAAKC,MAGLkC,EAEbN,GAAA,IAGXE,GAAM,IAAM5C,EAAMyC,WAAWrC,IACvBA,IACFG,EAAYH,MAAQ,IAEbsC,GAAA,IAGXO,EAAQC,EAAsBC,EAAS,CACrCnD,QACAK,QACAC,WACAJ,cACAK,cACAC,cACAa,WACAG,YACAe,sBACAa,mBA7D+DtC,IAC3DP,EAAYH,MAAMkB,SAASR,GAC7BU,EAAUV,GAGVO,EAASP,EAAK"}