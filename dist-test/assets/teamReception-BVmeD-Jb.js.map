{"version": 3, "file": "teamReception-BVmeD-Jb.js", "sources": ["../../src/views/order/info/components/orderdetail/teamReception.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"teamInfo\": {\r\n      \"title\": \"Team Information\",\r\n      \"teamName\": \"Team Name\",\r\n      \"contact\": \"Contact Person\",\r\n      \"plannedArrivalTime\": \"Planned Arrival Time\"\r\n    },\r\n    \"teamType\": {\r\n      \"meetingGroup\": \"Meeting Group\",\r\n      \"travelGroup\": \"Travel Group\",\r\n      \"other\": \"Other\"\r\n    },\r\n    \"statistics\": {\r\n      \"roomStatistics\": \"Room Statistics\",\r\n      \"totalRooms\": \"Total Rooms\",\r\n      \"checkedInRooms\": \"Checked-in Rooms\",\r\n      \"checkedInPersons\": \"Checked-in Persons\",\r\n      \"creditRooms\": \"Credit Rooms\",\r\n      \"creditPersons\": \"Credit Persons\",\r\n      \"settledRooms\": \"Settled Rooms\",\r\n      \"settledPersons\": \"Settled Persons\"\r\n    },\r\n    \"roomType\": {\r\n      \"distribution\": \"Room Type Distribution\",\r\n      \"roomUnit\": \" rooms\"\r\n    },\r\n    \"remark\": {\r\n      \"title\": \"Remarks\"\r\n    },\r\n    \"roomGuest\": {\r\n      \"title\": \"Room Guest Information\",\r\n      \"totalRooms\": \"Total {count} rooms\"\r\n    },\r\n    \"guestInfo\": {\r\n      \"title\": \"Guest Information\",\r\n      \"totalGuests\": \"Total {count} guests\",\r\n      \"noGuestInfo\": \"No guest information\",\r\n      \"name\": \"Name\",\r\n      \"gender\": \"Gender\",\r\n      \"status\": \"Status\",\r\n      \"mainGuest\": \"Main Guest\"\r\n    },\r\n    \"roomInfo\": {\r\n      \"title\": \"Room Information\"\r\n    },\r\n    \"timeInfo\": {\r\n      \"arrivalDeparture\": \"Arrival/Departure Time\",\r\n      \"checkin\": \"Check-in\",\r\n      \"departure\": \"Departure\"\r\n    },\r\n    \"status\": {\r\n      \"checkedIn\": \"Checked In\",\r\n      \"checkedOut\": \"Checked Out\",\r\n      \"credit\": \"Credit\",\r\n      \"unknown\": \"Unknown\"\r\n    },\r\n    \"gender\": {\r\n      \"male\": \"Male\",\r\n      \"female\": \"Female\",\r\n      \"unknown\": \"Unknown\"\r\n    },\r\n    \"actions\": {\r\n      \"printTeamRegistration\": \"Print Team Registration\",\r\n      \"createNewCard\": \"Create New Card\",\r\n      \"createCohabitantCard\": \"Create Cohabitant Card\"\r\n    },\r\n    \"messages\": {\r\n      \"pleaseAssignRoomFirst\": \"Please assign room before creating card\",\r\n      \"noLockConfigured\": \"Hotel has no lock configured. Please go to 'Lock Configuration' to select the lock model used by the hotel. If there is no matching lock model, please contact the service provider.\",\r\n      \"cardLogSuccess\": \"Card creation log recorded successfully\",\r\n      \"cardLogFailed\": \"Card creation log recording failed:\",\r\n      \"cardCreatedSuccess\": \"Room {roomNo} card created successfully\",\r\n      \"downloadClient\": \"Download Hotel-Agent Client\",\r\n      \"clientRequired\": \"Reading room cards can only be operated in Hotel-Agent, please {downloadLink}.\"\r\n    },\r\n    \"notifications\": {\r\n      \"cardInfo\": \"Card Information\"\r\n    },\r\n    \"common\": {\r\n      \"none\": \"None\",\r\n      \"actions\": \"Actions\",\r\n      \"status\": \"Status\"\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"teamInfo\": {\r\n      \"title\": \"团队信息\",\r\n      \"teamName\": \"团队名称\",\r\n      \"contact\": \"联系人\",\r\n      \"plannedArrivalTime\": \"预抵时间\"\r\n    },\r\n    \"teamType\": {\r\n      \"meetingGroup\": \"会议团\",\r\n      \"travelGroup\": \"旅行团\",\r\n      \"other\": \"其他\"\r\n    },\r\n    \"statistics\": {\r\n      \"roomStatistics\": \"房间统计\",\r\n      \"totalRooms\": \"总房间数\",\r\n      \"checkedInRooms\": \"已入住房间\",\r\n      \"checkedInPersons\": \"入住人数\",\r\n      \"creditRooms\": \"挂账房间\",\r\n      \"creditPersons\": \"挂账人数\",\r\n      \"settledRooms\": \"已结账房间\",\r\n      \"settledPersons\": \"已结账人数\"\r\n    },\r\n    \"roomType\": {\r\n      \"distribution\": \"房型分布\",\r\n      \"roomUnit\": \"间\"\r\n    },\r\n    \"remark\": {\r\n      \"title\": \"备注信息\"\r\n    },\r\n    \"roomGuest\": {\r\n      \"title\": \"房间住客信息\",\r\n      \"totalRooms\": \"共 {count} 间房\"\r\n    },\r\n    \"guestInfo\": {\r\n      \"title\": \"住客信息\",\r\n      \"totalGuests\": \"共 {count} 人\",\r\n      \"noGuestInfo\": \"暂无住客信息\",\r\n      \"name\": \"姓名\",\r\n      \"gender\": \"性别\",\r\n      \"status\": \"状态\",\r\n      \"mainGuest\": \"主客\"\r\n    },\r\n    \"roomInfo\": {\r\n      \"title\": \"房间信息\"\r\n    },\r\n    \"timeInfo\": {\r\n      \"arrivalDeparture\": \"抵离时间\",\r\n      \"checkin\": \"入住\",\r\n      \"departure\": \"离店\"\r\n    },\r\n    \"status\": {\r\n      \"checkedIn\": \"在住\",\r\n      \"checkedOut\": \"退房\",\r\n      \"credit\": \"挂账\",\r\n      \"unknown\": \"未知\"\r\n    },\r\n    \"gender\": {\r\n      \"male\": \"男\",\r\n      \"female\": \"女\",\r\n      \"unknown\": \"未知\"\r\n    },\r\n    \"actions\": {\r\n      \"printTeamRegistration\": \"打印团队登记单\",\r\n      \"createNewCard\": \"制新卡\",\r\n      \"createCohabitantCard\": \"制同住卡\"\r\n    },\r\n    \"messages\": {\r\n      \"pleaseAssignRoomFirst\": \"制卡前请先排房\",\r\n      \"noLockConfigured\": \"酒店没有配置门锁，请到房锁配置里选择酒店所用门锁型号，如果没有匹配的门锁型号，请联系服务商。\",\r\n      \"cardLogSuccess\": \"制卡日志记录成功\",\r\n      \"cardLogFailed\": \"制卡日志记录失败:\",\r\n      \"cardCreatedSuccess\": \"{roomNo}房制卡成功\",\r\n      \"downloadClient\": \"下载Hotel-Agent客户端\",\r\n      \"clientRequired\": \"读取房卡只能在Hotel-Agent中操作，请{downloadLink}。\"\r\n    },\r\n    \"notifications\": {\r\n      \"cardInfo\": \"房卡信息\"\r\n    },\r\n    \"common\": {\r\n      \"none\": \"暂无\",\r\n      \"actions\": \"操作\",\r\n      \"status\": \"状态\"\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"teamInfo\": {\r\n      \"title\": \"ព័ត៌មានក្រុម\",\r\n      \"teamName\": \"ឈ្មោះក្រុម\",\r\n      \"contact\": \"អ្នកទំនាក់ទំនង\",\r\n      \"plannedArrivalTime\": \"ពេលវេលាមកដល់ដែលបានគ្រោង\"\r\n    },\r\n    \"teamType\": {\r\n      \"meetingGroup\": \"ក្រុមប្រជុំ\",\r\n      \"travelGroup\": \"ក្រុមទេសចរណ៍\",\r\n      \"other\": \"ផ្សេងទៀត\"\r\n    },\r\n    \"statistics\": {\r\n      \"roomStatistics\": \"ស្ថិតិបន្ទប់\",\r\n      \"totalRooms\": \"បន្ទប់សរុប\",\r\n      \"checkedInRooms\": \"បន្ទប់ដែលបានចូលស្នាក់\",\r\n      \"checkedInPersons\": \"ចំនួនអ្នកចូលស្នាក់\",\r\n      \"creditRooms\": \"បន្ទប់កាន់កាប់\",\r\n      \"creditPersons\": \"ចំនួនអ្នកកាន់កាប់\",\r\n      \"settledRooms\": \"បន្ទប់ដែលបានទូទាត់\",\r\n      \"settledPersons\": \"ចំនួនអ្នកបានទូទាត់\"\r\n    },\r\n    \"roomType\": {\r\n      \"distribution\": \"ការចែកចាយប្រភេទបន្ទប់\",\r\n      \"roomUnit\": \"បន្ទប់\"\r\n    },\r\n    \"remark\": {\r\n      \"title\": \"ព័ត៌មានកំណត់ចំណាំ\"\r\n    },\r\n    \"roomGuest\": {\r\n      \"title\": \"ព័ត៌មានភ្ញៀវបន្ទប់\",\r\n      \"totalRooms\": \"សរុប {count} បន្ទប់\"\r\n    },\r\n    \"guestInfo\": {\r\n      \"title\": \"ព័ត៌មានភ្ញៀវ\",\r\n      \"totalGuests\": \"សរុប {count} នាក់\",\r\n      \"noGuestInfo\": \"គ្មានព័ត៌មានភ្ញៀវ\",\r\n      \"name\": \"ឈ្មោះ\",\r\n      \"gender\": \"ភេទ\",\r\n      \"status\": \"ស្ថានភាព\",\r\n      \"mainGuest\": \"ភ្ញៀវសំខាន់\"\r\n    },\r\n    \"roomInfo\": {\r\n      \"title\": \"ព័ត៌មានបន្ទប់\"\r\n    },\r\n    \"timeInfo\": {\r\n      \"arrivalDeparture\": \"ពេលវេលាមកដល់/ចាកចេញ\",\r\n      \"checkin\": \"ចូលស្នាក់\",\r\n      \"departure\": \"ចាកចេញ\"\r\n    },\r\n    \"status\": {\r\n      \"checkedIn\": \"កំពុងស្នាក់\",\r\n      \"checkedOut\": \"ចេញ\",\r\n      \"credit\": \"កាន់កាប់\",\r\n      \"unknown\": \"មិនស្គាល់\"\r\n    },\r\n    \"gender\": {\r\n      \"male\": \"ប្រុស\",\r\n      \"female\": \"ស្រី\",\r\n      \"unknown\": \"មិនស្គាល់\"\r\n    },\r\n    \"actions\": {\r\n      \"printTeamRegistration\": \"បោះពុម្ពបែបបទចុះឈ្មោះក្រុម\",\r\n      \"createNewCard\": \"បង្កើតកាតថ្មី\",\r\n      \"createCohabitantCard\": \"បង្កើតកាតអ្នកស្នាក់រួម\"\r\n    },\r\n    \"messages\": {\r\n      \"pleaseAssignRoomFirst\": \"សូមកំណត់បន្ទប់មុនពេលបង្កើតកាត\",\r\n      \"noLockConfigured\": \"សណ្ឋាគារមិនបានកំណត់រចនាសម្ព័ន្ធសោ។ សូមទៅកាន់ 'ការកំណត់រចនាសម្ព័ន្ធសោ' ដើម្បីជ្រើសរើសម៉ូដែលសោដែលសណ្ឋាគារប្រើ។ ប្រសិនបើមិនមានម៉ូដែលសោដែលត្រូវគ្នា សូមទាក់ទងអ្នកផ្តល់សេវាកម្ម។\",\r\n      \"cardLogSuccess\": \"កំណត់ត្រាបង្កើតកាតបានជោគជ័យ\",\r\n      \"cardLogFailed\": \"កំណត់ត្រាបង្កើតកាតបរាជ័យ:\",\r\n      \"cardCreatedSuccess\": \"បន្ទប់ {roomNo} បង្កើតកាតបានជោគជ័យ\",\r\n      \"downloadClient\": \"ទាញយក Hotel-Agent Client\",\r\n      \"clientRequired\": \"ការអានកាតអាចធ្វើបានតែនៅក្នុង Hotel-Agent ប៉ុណ្ណោះ សូម{downloadLink}។\"\r\n    },\r\n    \"notifications\": {\r\n      \"cardInfo\": \"ព័ត៌មានកាត\"\r\n    },\r\n    \"common\": {\r\n      \"none\": \"គ្មាន\",\r\n      \"actions\": \"សកម្មភាព\",\r\n      \"status\": \"ស្ថានភាព\"\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { HotelDeviceSetRespVO } from '@/api/modules/pms/device/device.api.ts'\r\nimport { deviceApi, orderApi, serverTimeApi } from '@/api/modules/index'\r\nimport roomCardLogApi from '@/api/modules/pms/room/roomCardLog.api'\r\nimport { BooleanEnum, ClientMethodEnum, OrderState } from '@/models'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { CardReader } from '@/store/websocket/CardReader.ts'\r\nimport { generateCardInfoHtml } from '@/utils/roomCardUtil.ts'\r\nimport PrintMergeForm from '@/views/print/mergeForm.vue'\r\nimport { Calendar, Flag, InfoFilled, Key, School, Timer, UserFilled } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\nimport { ElNotification } from 'element-plus'\r\nimport { ref } from 'vue'\r\n// 团队接待页面\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    teamCode: number | string // 团队代码\r\n    bindCode: number | string // 绑定代码\r\n    isEntryAccount: string // 是否可以入账操作\r\n  }>(),\r\n  {\r\n    teamCode: '',\r\n    bindCode: '',\r\n    isEntryAccount: '0',\r\n  }\r\n)\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst dt = ref({\r\n  gcode: '', // 集团代码\r\n  hcode: '', // 门店代码\r\n  teamCode: '', // 团队代码\r\n  teamName: '', // 团队名称\r\n  teamType: '', // 团队类型;请看数据字典\r\n  contact: '', // 联系人\r\n  planCheckinTime: null, // 预抵时间 (Date | null)\r\n  roomCount: 0, // 房间数\r\n  checkInRoomCount: 0, // 已入住房间数\r\n  personCount: 0, // 入住人数\r\n  creditPersonCount: 0, // 挂账人数\r\n  creditRoomCount: 0, // 挂账房间数\r\n  settlePersonCount: 0, // 已结账人数\r\n  settleRoomCount: 0, // 已结账房间数\r\n  remark: '', // 备注\r\n  teamRooms: [] as OrderRoomRespVO[], // 团队房间列表 (OrderRoomRespVO[])\r\n  roomTypeCounts: [] as RoomTypeCount[], // 房型数量统计 (RoomTypeCount[])\r\n})\r\n\r\n// 定义子对象类型\r\ninterface OrderRoomRespVO {\r\n  /** 订单号 */\r\n  orderNo: string\r\n  /** 客人姓名 */\r\n  name: string\r\n  /** 房型代码 */\r\n  rtCode: string\r\n  /** 房型名称 */\r\n  rtName: string\r\n  /** 房号 */\r\n  rNo: string\r\n  mac: string\r\n  lockVersion: string\r\n  /** 订单类型; general：普通订单 join：联房订单 group：团队订单 */\r\n  orderType: string\r\n  /** 绑定代码; 当为直接入住时，值为联房号(单个订单也生成唯一的联房号，多个订单生成相同的联房号)。当订单来自预订时，值为预订单号. */\r\n  bindCode: string\r\n  /** 是否主订单; 是否为主订单（0否 1是） */\r\n  isMain: string\r\n  /** 订单状态; 订单状态（在住:check_in 、已离店:check_out） */\r\n  state: string\r\n  /** 入住时间 */\r\n  checkinTime: Date | null\r\n  /** 预离时间 */\r\n  planCheckoutTime: Date | null\r\n  /** 退房时间 */\r\n  checkoutTime: Date | null\r\n  /** 同住人列表 */\r\n  orderTogethers?: any[]\r\n}\r\n\r\ninterface RoomTypeCount {\r\n  rtName: string // 房型名称\r\n  count: number // 房间数量\r\n}\r\n/** 服务器时间 */\r\nconst serverTime = ref('')\r\n// 表格展开行控制\r\nconst expandedRows = ref([])\r\nconst printMergeFormVisible = ref(false)\r\nonMounted(async () => {\r\n  await Promise.all([getOrderDetail()])\r\n  getServerTime()\r\n  // 获取门锁配置信息\r\n  // getHotelDoorConfig()\r\n\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n  // 默认展开第一行\r\n  if (dt.value.teamRooms && dt.value.teamRooms.length > 0) {\r\n    expandedRows.value = [dt.value.teamRooms[0].orderNo]\r\n  }\r\n})\r\nconst popoverContent = ref()\r\nonUnmounted(() => {\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n  if (popoverContent.value && popoverContent.value.length > 0) {\r\n    popoverContent.value.removeEventListener('scroll', handleScroll)\r\n  }\r\n})\r\n\r\n/** 获取服务器时间 */\r\nasync function getServerTime() {\r\n  const res = await serverTimeApi.serverTime(userStore.gcode, '0')\r\n  serverTime.value = res.data\r\n}\r\n\r\nasync function getOrderDetail() {\r\n  const res = await orderApi.getTeamMainOrder({\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    teamCode: props.teamCode,\r\n  })\r\n  dt.value = res.data\r\n}\r\n\r\nfunction getTagType(index) {\r\n  const types = ['primary', 'success', 'warning', 'danger', 'info']\r\n  return types[index % types.length]\r\n}\r\n\r\n// 获取状态对应的类型\r\nfunction getStatusType(status) {\r\n  if (status === OrderState.CHECK_IN) {\r\n    return 'success'\r\n  }\r\n  if (status === OrderState.CREDIT) {\r\n    return 'warning'\r\n  }\r\n  if (status === OrderState.CHECK_OUT) {\r\n    return 'info'\r\n  }\r\n  return 'info'\r\n}\r\n\r\n// 获取状态对应的文本\r\nfunction getStatusText(status) {\r\n  if (status === OrderState.CHECK_IN) {\r\n    return '在住'\r\n  }\r\n  if (status === OrderState.CHECK_OUT) {\r\n    return '退房'\r\n  }\r\n  if (status === OrderState.CREDIT) {\r\n    return '挂账'\r\n  }\r\n  return '未知'\r\n}\r\n\r\n// 获取团队类型文本\r\nfunction getTeamTypeText(teamType) {\r\n  if (teamType === 'meeting_group') {\r\n    return t('teamType.meetingGroup')\r\n  }\r\n  if (teamType === 'travel_group') {\r\n    return t('teamType.travelGroup')\r\n  }\r\n  return t('teamType.other')\r\n}\r\n\r\n// 格式化时间\r\nfunction formatTime(time) {\r\n  return time ? dayjs(time).format('MM-DD HH:mm') : '暂无'\r\n}\r\n/** 列表查询条件 */\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n/**\r\n * 获取酒店的门锁配置\r\n */\r\nconst hotelDoorConfig = ref<HotelDeviceSetRespVO>()\r\n/**\r\n * 获取酒店的门锁配置\r\n */\r\nfunction getHotelDoorConfig(lockVersion?: string): Promise<void> {\r\n  const params = {\r\n    ...queryParams,\r\n    ...(lockVersion && { lockVersion }),\r\n  }\r\n  return deviceApi.getHotelDoor(params).then((res: any) => {\r\n    if (res.code === 0 && res.data) {\r\n      hotelDoorConfig.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取服务器的系统时间戳 */\r\nfunction getSysTime(): Promise<string> {\r\n  return serverTimeApi.serverTime(userStore.gcode, '3').then((res: any) => {\r\n    if (res.code === 0) {\r\n      return res.data // 返回字符串类型的时间戳\r\n    }\r\n    return ''\r\n  })\r\n}\r\n/** 制房卡 */\r\nasync function writeLockCard(row: any, newCard: boolean) {\r\n  // 判断是否已经排房，如果没有排房，则不能制卡\r\n  if (!row.rNo) {\r\n    ElMessage.warning(t('messages.pleaseAssignRoomFirst'))\r\n    return\r\n  }\r\n  if (!isInClient()) {\r\n    return\r\n  }\r\n\r\n  if (!row.lockVersion) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('messages.noLockConfigured'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n\r\n  await getHotelDoorConfig(row.lockVersion)\r\n\r\n  // 如果没有门锁的配置，则提示\r\n  if (!hotelDoorConfig.value) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('messages.noLockConfigured'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n  CardReader.initCardReader((message: string) => {\r\n    const data = JSON.parse(message)\r\n    if (data && data.succeed === true && data.method === ClientMethodEnum.WRITELOCKCARD) {\r\n      if (data.cardInfo) {\r\n        ElNotification({\r\n          title: t('notifications.cardInfo'),\r\n          dangerouslyUseHTMLString: true,\r\n          message: generateCardInfoHtml(data),\r\n          position: 'bottom-left',\r\n          type: 'success',\r\n        })\r\n        // 异步记录制卡日志，不影响主业务\r\n        roomCardLogApi\r\n          .createRoomCardLog({\r\n            gcode: userStore.gcode,\r\n            hcode: userStore.hcode,\r\n            name: row.name,\r\n            rNo: row.rNo,\r\n            cardNo: data.cardInfo.cardNo,\r\n            orderNo: row.orderNo,\r\n            type: newCard ? '0' : '1',\r\n            periodTime: dayjs(Number(data.cardInfo.expire) * 1000).format('YYYY-MM-DD HH:mm:ss'),\r\n          })\r\n          .then((res: any) => {\r\n            if (res.code === 0) {\r\n              console.log('制卡日志记录成功')\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            // 日志记录失败不影响主业务，只记录错误\r\n            console.error('制卡日志记录失败:', error)\r\n          })\r\n      }\r\n      ElMessage({\r\n        message: t('messages.cardCreatedSuccess', { roomNo: row.rNo }),\r\n        type: 'success',\r\n      })\r\n    }\r\n  })\r\n  // 获取系统时间戳\r\n  const sysTimeString = await getSysTime() // 调用异步方法\r\n  if (!sysTimeString) {\r\n    console.error('Failed to get system time')\r\n    return\r\n  }\r\n  // 单位秒\r\n  const timeStampInSeconds = Math.floor(Number.parseInt(sysTimeString, 10) / 1000)\r\n  // 构建 json 对象\r\n  const json = {\r\n    method: ClientMethodEnum.WRITELOCKCARD, // 写门锁卡\r\n    lockVer: hotelDoorConfig.value.version, // 门锁类型\r\n    cardInfo: {\r\n      roomNo: row.rNo, // 房间号，字符串\r\n      checkin: timeStampInSeconds, // 入住时间戳，单位秒，整型\r\n      expire: Math.floor(dayjs(row.planCheckoutTime).valueOf() / 1000), // 到期时间戳，单位秒，整型\r\n      allowLockOut: hotelDoorConfig.value?.allowLockOut === BooleanEnum.YES, // 是否允许开反锁，布尔值\r\n      replaceCard: newCard, // 新卡替换旧卡，布尔值\r\n      checkTime: hotelDoorConfig.value?.checkTime === BooleanEnum.YES, // 检查入住时间，布尔值\r\n      lockNo: row.lockNo, // 门锁号，字符串\r\n      mac: row.mac, //  门锁mac地址\r\n      buildNo: row.buildNo, // 楼栋号\r\n      floorNo: row.floorNo, // 楼层号\r\n    },\r\n  }\r\n\r\n  // 检查 confParameter 是否有值\r\n  if (hotelDoorConfig.value.conf && hotelDoorConfig.value.conf.length > 0) {\r\n    // 将 confParameter 转换为 cardInfo 对象\r\n    hotelDoorConfig.value.conf.forEach((param) => {\r\n      json.cardInfo[param.parameterCode] = param.parameterContent\r\n    })\r\n  }\r\n\r\n  // 将 json 对象转换为字符串\r\n  const jsonString = JSON.stringify(json)\r\n  console.log('jsonString', jsonString)\r\n  const timer = setInterval(() => {\r\n    if (CardReader.isConnected) {\r\n      CardReader.handleLockCard(jsonString)\r\n      clearInterval(timer)\r\n    }\r\n  }, 200)\r\n}\r\n\r\nfunction isInClient(): boolean {\r\n  if (typeof (window as any).CallBridge === 'undefined' && typeof (window as any).__RUNNING_IN_PMS_AGENT__ === 'undefined') {\r\n    // 提示没有下载客户端不能读取房卡，并提示下载客户端的路径\r\n    const downloadLink = `<a href=\"${import.meta.env.VITE_APP_CLIENT_DOWNLOAD_URL}\" target=\"_blank\">下载Hotel-Agent客户端</a>`\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('messages.clientRequired', { downloadLink }),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n// 获取性别文本\r\nfunction getGenderText(sex) {\r\n  if (sex === '1') {\r\n    return t('gender.male')\r\n  }\r\n  if (sex === '0') {\r\n    return t('gender.female')\r\n  }\r\n  return t('gender.unknown')\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"team-reception-container\">\r\n    <!-- 团队概览卡片 -->\r\n    <el-card class=\"overview-card\" shadow=\"hover\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <div class=\"header-title\">\r\n            <el-icon><Flag /></el-icon>\r\n            <span>团队信息</span>\r\n            <el-tag type=\"primary\" effect=\"plain\" class=\"ml-2\">\r\n              {{ getTeamTypeText(dt.teamType) }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"printMergeFormVisible = true\">\r\n              {{ t('actions.printTeamRegistration') }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 基本信息区域 -->\r\n      <div class=\"team-basic-info\">\r\n        <div class=\"info-row\">\r\n          <div class=\"info-item\">\r\n            <span class=\"info-label\">{{ t('teamInfo.teamName') }}：</span>\r\n            <span class=\"info-value\">{{ dt.teamName || t('common.none') }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <span class=\"info-label\">{{ t('teamInfo.contact') }}：</span>\r\n            <span class=\"info-value\">{{ dt.contact || t('common.none') }}</span>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <span class=\"info-label\">{{ t('teamInfo.plannedArrivalTime') }}：</span>\r\n            <span class=\"info-value\">\r\n              <el-icon><Calendar /></el-icon>\r\n              {{ formatTime(dt.planCheckinTime) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 统计数据卡片组 -->\r\n      <div class=\"stats-section\">\r\n        <div class=\"section-title\">\r\n          <el-icon><InfoFilled /></el-icon>\r\n          <span>{{ t('statistics.roomStatistics') }}</span>\r\n        </div>\r\n        <div class=\"stats-cards\">\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.roomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.totalRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.checkInRoomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.checkedInRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.personCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.checkedInPersons') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.creditRoomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.creditRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.creditPersonCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.creditPersons') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.settleRoomCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.settledRooms') }}\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-card\">\r\n            <div class=\"stat-value\">\r\n              {{ dt.settlePersonCount }}\r\n            </div>\r\n            <div class=\"stat-label\">\r\n              {{ t('statistics.settledPersons') }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 房型分布 -->\r\n      <div class=\"room-type-section\">\r\n        <div class=\"section-title\">\r\n          <el-icon><Key /></el-icon>\r\n          <span>{{ t('roomType.distribution') }}</span>\r\n        </div>\r\n        <div class=\"room-type-distribution\">\r\n          <div v-for=\"(item, index) in dt.roomTypeCounts\" :key=\"index\" class=\"room-type-item\">\r\n            <el-tag :type=\"getTagType(index)\" effect=\"light\">\r\n              {{ item.rtName }}\r\n            </el-tag>\r\n            <span class=\"room-type-count\">{{ item.count }}{{ t('roomType.roomUnit') }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 备注信息 -->\r\n      <div v-if=\"dt.remark\" class=\"remark-section\">\r\n        <div class=\"section-title\">\r\n          <el-icon><InfoFilled /></el-icon>\r\n          <span>{{ t('remark.title') }}</span>\r\n        </div>\r\n        <div class=\"remark-container\">\r\n          <div class=\"remark-content\">\r\n            {{ dt.remark }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 房间住客信息表格 -->\r\n    <el-card class=\"rooms-card\" shadow=\"hover\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <div class=\"header-title\">\r\n            <el-icon><School /></el-icon>\r\n            <span>{{ t('roomGuest.title') }}</span>\r\n            <el-tag type=\"info\" effect=\"plain\" class=\"room-count-tag\">\r\n              {{ t('roomGuest.totalRooms', { count: dt.teamRooms.length }) }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <el-table :data=\"dt.teamRooms\" default-expand-all border class=\"room-table\">\r\n        <el-table-column type=\"expand\">\r\n          <template #default=\"expandProps\">\r\n            <div class=\"guest-info-container\">\r\n              <div class=\"guest-info-header\" style=\"margin-bottom: 5px\">\r\n                <div class=\"guest-info-title\">\r\n                  <el-icon><UserFilled /></el-icon>\r\n                  <span>{{ t('guestInfo.title') }}</span>\r\n                  <el-tag size=\"small\" type=\"info\" effect=\"plain\">\r\n                    {{ t('guestInfo.totalGuests', { count: expandProps.row.orderTogethers?.length || 0 }) }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n\r\n              <el-empty v-if=\"!expandProps.row.orderTogethers || expandProps.row.orderTogethers.length === 0\" :description=\"t('guestInfo.noGuestInfo')\" />\r\n\r\n              <el-table v-else :data=\"expandProps.row.orderTogethers\" class=\"guest-table\" stripe border>\r\n                <el-table-column prop=\"name\" :label=\"t('guestInfo.name')\" min-width=\"120\">\r\n                  <template #default=\"{ row }\">\r\n                    <div class=\"guest-name\">\r\n                      <span>{{ row.name }}</span>\r\n                      <el-tag v-if=\"row.isMain === '1'\" size=\"small\" type=\"success\" effect=\"plain\">\r\n                        {{ t('guestInfo.mainGuest') }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('guestInfo.gender')\" width=\"80\" align=\"center\">\r\n                  <template #default=\"{ row }\">\r\n                    <el-tag size=\"small\" :type=\"row.sex === '1' ? 'primary' : 'danger'\" effect=\"plain\">\r\n                      {{ getGenderText(row.sex) }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('guestInfo.status')\" width=\"100\" align=\"center\">\r\n                  <template #default=\"{ row }\">\r\n                    <el-tag size=\"small\" :type=\"row.state === OrderState.CHECK_IN ? 'success' : row.state === OrderState.CREDIT ? 'warning' : 'info'\">\r\n                      {{ getStatusText(row.state) }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('common.actions')\" align=\"center\" width=\"285\">\r\n                  <el-button link type=\"primary\" size=\"small\" @click=\"writeLockCard(expandProps.row, false)\">\r\n                    <el-icon><Key /></el-icon> {{ t('actions.createCohabitantCard') }}\r\n                  </el-button>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column :label=\"t('roomInfo.title')\" min-width=\"180\">\r\n          <template #default=\"scope\">\r\n            <div class=\"room-info\">\r\n              <div class=\"room-number\">\r\n                <el-tag size=\"large\" effect=\"dark\" class=\"room-tag\">\r\n                  {{ scope.row.rNo }}\r\n                </el-tag>\r\n                <span class=\"room-type\" style=\"margin-left: 5px\">{{ scope.row.rtName }}</span>\r\n                <el-badge :value=\"scope.row.orderTogethers?.length || 0\" type=\"primary\" style=\"margin-left: 10px\">\r\n                  <el-icon><UserFilled /></el-icon>\r\n                </el-badge>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column :label=\"t('timeInfo.arrivalDeparture')\" width=\"280\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <div class=\"time-info-inline\">\r\n              <div class=\"time-item\">\r\n                <el-icon><Calendar /></el-icon>\r\n                <span>{{ t('timeInfo.checkin') }}: {{ formatTime(scope.row.checkinTime) }}</span>\r\n              </div>\r\n              <div class=\"divider\">|</div>\r\n              <div class=\"time-item\">\r\n                <el-icon><Timer /></el-icon>\r\n                <span>{{ t('timeInfo.departure') }}: {{ formatTime(scope.row.planCheckoutTime) }}</span>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column :label=\"t('common.status')\" align=\"center\" width=\"100\">\r\n          <template #default=\"{ row }\">\r\n            <el-tag :type=\"row.state === OrderState.CHECK_IN ? 'success' : row.state === OrderState.CREDIT ? 'warning' : 'info'\" effect=\"dark\">\r\n              {{ getStatusText(row.state) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column :label=\"t('common.actions')\" align=\"center\" width=\"300\">\r\n          <template #default=\"scope\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button type=\"primary\" size=\"small\" plain @click=\"writeLockCard(scope.row, true)\">\r\n                <el-icon><Key /></el-icon> {{ t('actions.createNewCard') }}\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-card>\r\n  </div>\r\n  <!-- 打印团队/联房入住登记单 -->\r\n  <PrintMergeForm v-if=\"printMergeFormVisible\" v-model=\"printMergeFormVisible\" :bind-code=\"props.bindCode\" />\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 整体容器样式\r\n.team-reception-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n// 卡片通用样式\r\n.el-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: box-shadow 0.3s;\r\n\r\n  &:hover {\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n// 卡片头部样式\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 5px;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.ml-2 {\r\n  margin-left: 8px;\r\n}\r\n\r\n// 基本信息区域\r\n.team-basic-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  min-width: 250px;\r\n}\r\n\r\n.info-label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 10px;\r\n}\r\n\r\n.info-value {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #303133;\r\n}\r\n\r\n// 统计区域\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 15px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.stat-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 120px;\r\n  height: 90px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s ease;\r\n  cursor: default;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.stat-label {\r\n  margin-top: 5px;\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n// 房型分布区域\r\n.room-type-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.room-type-distribution {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.room-type-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.room-type-count {\r\n  font-weight: bold;\r\n  color: #606266;\r\n}\r\n\r\n// 备注信息区域\r\n.remark-section {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.ml-2 {\r\n  margin-left: 8px;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.team-basic-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.stats-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 15px;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.room-type-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.remark-section {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.guest-info-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n// 修复表格展开行样式\r\n:deep(.el-table__expanded-cell) {\r\n  padding: 15px !important;\r\n}\r\n\r\n:deep(.el-table__expand-icon) {\r\n  transform-origin: center;\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n:deep(.el-table__expand-icon--expanded) {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n// 修复空状态样式\r\n:deep(.el-empty) {\r\n  padding: 20px 0;\r\n}\r\n\r\n// 修复表格内部样式\r\n:deep(.el-table__body) td {\r\n  padding: 8px 0;\r\n}\r\n\r\n// 修复卡片内边距\r\n:deep(.el-card__body) {\r\n  padding: 15px;\r\n}\r\n// 时间信息样式\r\n.time-info-inline {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  white-space: nowrap;\r\n}\r\n\r\n.time-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 13px;\r\n}\r\n\r\n.divider {\r\n  margin: 0 8px;\r\n  color: #dcdfe6;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.room-count-tag {\r\n  margin-left: 10px;\r\n  font-weight: normal;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "dt", "ref", "gcode", "hcode", "teamCode", "teamName", "teamType", "contact", "planCheckinTime", "roomCount", "checkInRoomCount", "personCount", "creditPersonCount", "creditRoomCount", "settlePersonCount", "settleRoomCount", "remark", "teamRooms", "roomTypeCounts", "serverTime", "expandedRows", "printMergeFormVisible", "onMounted", "async", "Promise", "all", "getOrderDetail", "res", "serverTimeApi", "value", "data", "getServerTime", "window", "CallBridge", "__RUNNING_IN_PMS_AGENT__", "<PERSON><PERSON><PERSON><PERSON>", "closeSocket", "length", "orderNo", "popoverContent", "orderApi", "getTeamMainOrder", "getTagType", "index", "types", "getStatusText", "status", "OrderState", "CHECK_IN", "CHECK_OUT", "CREDIT", "formatTime", "time", "dayjs", "format", "onUnmounted", "removeEventListener", "handleScroll", "queryParams", "reactive", "hotelDoorConfig", "writeLockCard", "row", "newCard", "rNo", "ElMessage", "warning", "showClose", "message", "downloadLink", "type", "dangerouslyUseHTMLString", "isInClient", "lockVersion", "params", "deviceApi", "getHotelDoor", "then", "code", "getHotelDoorConfig", "initCardReader", "JSON", "parse", "succeed", "method", "ClientMethodEnum", "WRITELOCKCARD", "cardInfo", "ElNotification", "title", "generateCardInfoHtml", "position", "roomCardLogApi", "createRoomCardLog", "name", "cardNo", "periodTime", "Number", "expire", "console", "log", "catch", "error", "roomNo", "sysTimeString", "timeStampInSeconds", "Math", "floor", "parseInt", "json", "lock<PERSON>er", "version", "checkin", "planCheckoutTime", "valueOf", "allowLockOut", "_a", "BooleanEnum", "YES", "replaceCard", "checkTime", "_b", "lockNo", "mac", "buildNo", "floorNo", "conf", "for<PERSON>ach", "param", "parameterCode", "parameterContent", "jsonString", "stringify", "timer", "setInterval", "isConnected", "handleLockCard", "clearInterval", "sex"], "mappings": "kzFA+QA,MAAMA,EAAQC,GAYRC,EAAEA,IAAMC,IACRC,GAAYC,IACZC,GAAKC,EAAI,CACbC,MAAO,GACPC,MAAO,GACPC,SAAU,GACVC,SAAU,GACVC,SAAU,GACVC,QAAS,GACTC,gBAAiB,KACjBC,UAAW,EACXC,iBAAkB,EAClBC,YAAa,EACbC,kBAAmB,EACnBC,gBAAiB,EACjBC,kBAAmB,EACnBC,gBAAiB,EACjBC,OAAQ,GACRC,UAAW,GACXC,eAAgB,KAwCZC,GAAalB,EAAI,IAEjBmB,GAAenB,EAAI,IACnBoB,GAAwBpB,GAAI,GAClCqB,GAAUC,gBACFC,QAAQC,IAAI,CAACC,OAwBrBH,iBACE,MAAMI,QAAYC,EAAcT,WAAWrB,GAAUI,MAAO,KAC5DiB,GAAWU,MAAQF,EAAIG,IAAA,CAzBTC,QAI4B,IAA9BC,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,EAAWC,cAGTpC,GAAG6B,MAAMZ,WAAajB,GAAG6B,MAAMZ,UAAUoB,OAAS,IACpDjB,GAAaS,MAAQ,CAAC7B,GAAG6B,MAAMZ,UAAU,GAAGqB,SAAO,IAGvD,MAAMC,GAAiBtC,IAgBvBsB,eAAeG,KACP,MAAAC,QAAYa,EAASC,iBAAiB,CAC1CvC,MAAOJ,GAAUI,MACjBC,MAAOL,GAAUK,MACjBC,SAAUV,EAAMU,WAElBJ,GAAG6B,MAAQF,EAAIG,IAAA,CAGjB,SAASY,GAAWC,GAClB,MAAMC,EAAQ,CAAC,UAAW,UAAW,UAAW,SAAU,QACnD,OAAAA,EAAMD,EAAQC,EAAMP,OAAM,CAkBnC,SAASQ,GAAcC,GACjB,OAAAA,IAAWC,EAAWC,SACjB,KAELF,IAAWC,EAAWE,UACjB,KAELH,IAAWC,EAAWG,OACjB,KAEF,IAAA,CAeT,SAASC,GAAWC,GAClB,OAAOA,EAAOC,EAAMD,GAAME,OAAO,eAAiB,IAAA,CAtEpDC,GAAY,UACgC,IAA9BvB,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,EAAWC,cAETG,GAAeV,OAASU,GAAeV,MAAMQ,OAAS,GACzCE,GAAAV,MAAM2B,oBAAoB,SAAUC,aAAY,IAoEnE,MAAMC,GAAcC,EAAiB,CACnCzD,MAAOJ,GAAUI,MACjBC,MAAOL,GAAUK,QAKbyD,GAAkB3D,IA0BTsB,eAAAsC,GAAcC,EAAUC,WAEjC,IAACD,EAAIE,IAEP,YADUC,EAAAC,QAAQtE,GAAE,mCAGlB,IA8GN,WACE,QAA0C,IAA9BoC,OAAeC,iBAAkF,IAA5CD,OAAeE,yBAA0C,CASjH,OANG+B,EAAA,CACRE,WAAW,EACXC,QAASxE,GAAE,0BAA2B,CAAEyE,aAHrB,8IAInBC,KAAM,UACNC,0BAA0B,KAErB,CAAA,CAEF,OAAA,CAAA,CA1HFC,GACH,OAGE,IAACV,EAAIW,YAOP,YANUR,EAAA,CACRE,WAAW,EACXC,QAASxE,GAAE,6BACX0E,KAAM,UACNC,0BAA0B,IAQ1B,SA7CN,SAA4BE,GAC1B,MAAMC,EAAS,IACVhB,MACCe,GAAe,CAAEA,gBAEvB,OAAOE,EAAUC,aAAaF,GAAQG,MAAMlD,IACzB,IAAbA,EAAImD,MAAcnD,EAAIG,OACxB8B,GAAgB/B,MAAQF,EAAIG,KAAA,GAE/B,CAiCKiD,CAAmBjB,EAAIW,cAGxBb,GAAgB/B,MAOnB,YANUoC,EAAA,CACRE,WAAW,EACXC,QAASxE,GAAE,6BACX0E,KAAM,UACNC,0BAA0B,IAInBpC,EAAA6C,gBAAgBZ,IACnB,MAAAtC,EAAOmD,KAAKC,MAAMd,GACpBtC,IAAyB,IAAjBA,EAAKqD,SAAoBrD,EAAKsD,SAAWC,EAAiBC,gBAChExD,EAAKyD,WACQC,EAAA,CACbC,MAAO7F,GAAE,0BACT2E,0BAA0B,EAC1BH,QAASsB,EAAqB5D,GAC9B6D,SAAU,cACVrB,KAAM,YAGRsB,EACGC,kBAAkB,CACjB3F,MAAOJ,GAAUI,MACjBC,MAAOL,GAAUK,MACjB2F,KAAMhC,EAAIgC,KACV9B,IAAKF,EAAIE,IACT+B,OAAQjE,EAAKyD,SAASQ,OACtBzD,QAASwB,EAAIxB,QACbgC,KAAMP,EAAU,IAAM,IACtBiC,WAAY3C,EAAqC,IAA/B4C,OAAOnE,EAAKyD,SAASW,SAAgB5C,OAAO,yBAE/DuB,MAAMlD,IACY,IAAbA,EAAImD,MACNqB,QAAQC,IAAI,WAAU,IAGzBC,OAAOC,IAEEH,QAAAG,MAAM,YAAaA,EAAK,KAG5BrC,EAAA,CACRG,QAASxE,GAAE,8BAA+B,CAAE2G,OAAQzC,EAAIE,MACxDM,KAAM,YACP,IAIC,MAAAkC,QAhFC5E,EAAcT,WAAWrB,GAAUI,MAAO,KAAK2E,MAAMlD,GACzC,IAAbA,EAAImD,KACCnD,EAAIG,KAEN,KA6ET,IAAK0E,EAEH,YADAL,QAAQG,MAAM,6BAIV,MAAAG,EAAqBC,KAAKC,MAAMV,OAAOW,SAASJ,EAAe,IAAM,KAErEK,EAAO,CACXzB,OAAQC,EAAiBC,cACzBwB,QAASlD,GAAgB/B,MAAMkF,QAC/BxB,SAAU,CACRgB,OAAQzC,EAAIE,IACZgD,QAASP,EACTP,OAAQQ,KAAKC,MAAMtD,EAAMS,EAAImD,kBAAkBC,UAAY,KAC3DC,cAAc,OAAAC,EAAAxD,GAAgB/B,YAAhB,EAAAuF,EAAuBD,gBAAiBE,EAAYC,IAClEC,YAAaxD,EACbyD,WAAW,OAAAC,EAAA7D,GAAgB/B,YAAhB,EAAA4F,EAAuBD,aAAcH,EAAYC,IAC5DI,OAAQ5D,EAAI4D,OACZC,IAAK7D,EAAI6D,IACTC,QAAS9D,EAAI8D,QACbC,QAAS/D,EAAI+D,UAKbjE,GAAgB/B,MAAMiG,MAAQlE,GAAgB/B,MAAMiG,KAAKzF,OAAS,GAEpEuB,GAAgB/B,MAAMiG,KAAKC,SAASC,IAClCnB,EAAKtB,SAASyC,EAAMC,eAAiBD,EAAME,gBAAA,IAKzC,MAAAC,EAAalD,KAAKmD,UAAUvB,GAC1BV,QAAAC,IAAI,aAAc+B,GACpB,MAAAE,EAAQC,aAAY,KACpBnG,EAAWoG,cACbpG,EAAWqG,eAAeL,GAC1BM,cAAcJ,GAAK,GAEpB,IAAG,qUAjKiB/H,oBAEdV,GADQ,kBAAbU,EACO,wBAEM,iBAAbA,EACO,uBAEF,wBAPX,IAAyBA,u8GAoLFoI,QAEZ9I,GADG,MAAR8I,EACO,cAEC,MAARA,EACO,gBAEF,wBAPX,IAAuBA"}