{"version": 3, "file": "basicofig-B7UYNlFJ.js", "sources": ["../../src/views/group/system/config/components/basicofig.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"serviceFlag\": \"Service Flag\",\r\n    \"code\": \"Code\",\r\n    \"name\": \"Name\",\r\n    \"remark\": \"Remark\",\r\n    \"newAdd\": \"Add\",\r\n    \"edit\": \"Edit\",\r\n    \"save\": \"Save\",\r\n    \"cancel\": \"Cancel\",\r\n    \"hotelInfoTip\": \"Tip: Hotel basic information page display\",\r\n    \"placeholderCode\": \"Please enter code\",\r\n    \"placeholderName\": \"Please enter name\",\r\n    \"placeholderRemark\": \"Please enter remark\",\r\n    \"operation\": \"Actions\",\r\n    \"saveSuccess\": \"Save Successful.\",\r\n    \"editSuccess\": \"Update Successful.\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"serviceFlag\": \"服务标识\",\r\n    \"code\": \"编码\",\r\n    \"name\": \"名称\",\r\n    \"remark\": \"备注\",\r\n    \"newAdd\": \"新增\",\r\n    \"edit\": \"编辑\",\r\n    \"save\": \"保存\",\r\n    \"cancel\": \"取消\",\r\n    \"hotelInfoTip\": \"提示:酒店基本信息页面显示\",\r\n    \"placeholderCode\": \"请输入编码\",\r\n    \"placeholderName\": \"请输入名称\",\r\n    \"placeholderRemark\": \"请输入备注\",\r\n    \"operation\": \"操作\",\r\n    \"saveSuccess\": \"保存成功\",\r\n    \"editSuccess\": \"修改成功\"\r\n  },\r\n  \"km\": {\r\n    \"serviceFlag\": \"សញ្ញាសេវាកម្ម\",\r\n    \"code\": \"កូដ\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"remark\": \"កំណត់សម្គាល់\",\r\n    \"newAdd\": \"បន្ថែម\",\r\n    \"edit\": \"កែសម្រួល\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"hotelInfoTip\": \"ព័ត៌មាន: ទំព័រព័ត៌មានមូលដ្ឋានសណ្ឋាគារ\",\r\n    \"placeholderCode\": \"សូមបញ្ចូលកូដ\",\r\n    \"placeholderName\": \"សូមបញ្ចូលឈ្មោះ\",\r\n    \"placeholderRemark\": \"សូមបញ្ចូលកំណត់សម្គាល់\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"saveSuccess\": \"រក្សាទុកដោយជោគជ័យ\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { GeneralConfigModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { generalConfigApi } from '@/api/modules/index'\r\nimport { BooleanEnum, DICT_TYPE_HOTEL_SERVICE, DictTypeEnum } from '@/models/index'\r\n\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  id: 0,\r\n  gcode: userStore.gcode,\r\n  code: '',\r\n  name: '',\r\n  value: '',\r\n  type: DictTypeEnum.HOTEL_SERVICE.toString(),\r\n\r\n  remark: '',\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  value: [{ required: true, message: t('placeholderCode'), trigger: 'blur' }],\r\n  name: [{ required: true, message: t('placeholderName'), trigger: 'blur' }],\r\n})\r\nconst formInline = ref({\r\n  gcode: userStore.gcode,\r\n  code: '',\r\n  name: '',\r\n  value: '',\r\n  type: DictTypeEnum.HOTEL_SERVICE.toString(),\r\n  remark: '',\r\n})\r\nconst dataList = ref<GeneralConfigModel[]>([])\r\n\r\nconst type = ref(DictTypeEnum.HOTEL_SERVICE.toString())\r\n\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  generalConfigApi.list({ gcode: userStore.gcode, isG: BooleanEnum.YES, type: type.value }).then((res: any) => {\r\n    loading.value = false\r\n    dataList.value = res.data\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  return new Promise<void>((resolve) => {\r\n    formRef.value &&\r\n      formRef.value.validate((valid) => {\r\n        if (valid) {\r\n          generalConfigApi.createGeneralConfig(formInline.value).then((res: any) => {\r\n            if (res.code === 0) {\r\n              ElMessage({\r\n                message: t('saveSuccess'), // 使用国际化字符串\r\n                type: 'success',\r\n              })\r\n              getInfo()\r\n            }\r\n            resolve()\r\n          })\r\n        }\r\n      })\r\n  })\r\n}\r\n\r\nfunction menuClick(typeCode: string) {\r\n  type.value = typeCode\r\n  getInfo()\r\n}\r\n\r\nfunction onEdit(item: any) {\r\n  form.value = item\r\n  return new Promise<void>((resolve) => {\r\n    generalConfigApi.updateGeneralConfig(form.value).then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success({\r\n          message: t('editSuccess'), // 使用国际化字符串\r\n          center: true,\r\n        })\r\n        item.isEdit = false\r\n      } else {\r\n        ElMessage.error({\r\n          message: res.msg,\r\n          center: true,\r\n        })\r\n      }\r\n      resolve()\r\n    })\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-container>\r\n    <el-aside style=\"width: 200px\">\r\n      <el-container>\r\n        <el-main class=\"nopadding\">\r\n          <el-menu default-active=\"1\" class=\"el-menu-vertical-demo\">\r\n            <el-menu-item index=\"1\" @click=\"menuClick(DICT_TYPE_HOTEL_SERVICE)\">\r\n              <span>{{ t('serviceFlag') }}</span>\r\n              <!-- 使用国际化字符串 -->\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </el-main>\r\n      </el-container>\r\n    </el-aside>\r\n    <el-main>\r\n      <div>\r\n        <el-form v-if=\"type === DictTypeEnum.HOTEL_SERVICE.toString()\" ref=\"formRef\" :inline=\"true\" :rules=\"formRules\" :model=\"formInline\" class=\"demo-form-inline\">\r\n          <el-form-item :label=\"t('code')\" prop=\"value\">\r\n            <el-input v-model=\"formInline.value\" :placeholder=\"t('placeholderCode')\" clearable maxlength=\"30\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('name')\" prop=\"name\">\r\n            <el-input v-model=\"formInline.name\" :placeholder=\"t('placeholderName')\" clearable maxlength=\"30\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('remark')\">\r\n            <el-input v-model=\"formInline.remark\" :placeholder=\"t('placeholderRemark')\" clearable maxlength=\"50\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"onSubmit\">\r\n              {{ t('newAdd') }}\r\n            </el-button>\r\n            <!-- 使用国际化字符串 -->\r\n          </el-form-item>\r\n        </el-form>\r\n        <div class=\"config-info\">\r\n          {{ t('hotelInfoTip') }}\r\n          <!-- 使用国际化字符串 -->\r\n        </div>\r\n        <el-table v-loading=\"loading\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" class=\"list-table\" :data=\"dataList\" height=\"100%\">\r\n          <el-table-column :label=\"t('code')\" align=\"left\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.value\" maxlength=\"30\" />\r\n              <span v-else>{{ scope.row.value }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('name')\" align=\"left\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.name\" maxlength=\"30\" />\r\n              <span v-else>{{ scope.row.name }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('remark')\" align=\"left\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.remark\" maxlength=\"50\" />\r\n              <span v-else>{{ scope.row.remark }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('operation')\" align=\"left\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <template v-if=\"scope.row.isEdit\">\r\n                <el-button type=\"primary\" plain size=\"small\" @click=\"onEdit(scope.row)\">\r\n                  {{ t('save') }}\r\n                  <!-- 使用国际化字符串 -->\r\n                </el-button>\r\n                <el-button plain size=\"small\" @click=\"scope.row.isEdit = false\">\r\n                  {{ t('cancel') }}\r\n                  <!-- 使用国际化字符串 -->\r\n                </el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"primary\" plain size=\"small\" @click=\"scope.row.isEdit = true\">\r\n                  {{ t('edit') }}\r\n                  <!-- 使用国际化字符串 -->\r\n                </el-button>\r\n              </template>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-main>\r\n  </el-container>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-menu-item) {\r\n  &.is-active {\r\n    background-color: var(--el-color-primary-light-9);\r\n  }\r\n  &:hover {\r\n    background-color: var(--el-color-primary-light-9);\r\n    color: var(--el-menu-active-color);\r\n  }\r\n}\r\n\r\n.el-main.nopadding {\r\n  padding: 0;\r\n  background: #fff;\r\n}\r\n\r\n.config-info {\r\n  margin-bottom: 20px;\r\n  color: red;\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "formRef", "form", "id", "gcode", "code", "name", "value", "type", "DictTypeEnum", "HOTEL_SERVICE", "toString", "remark", "formRules", "required", "message", "trigger", "formInline", "dataList", "getInfo", "generalConfigApi", "list", "isG", "BooleanEnum", "YES", "then", "res", "data", "onSubmit", "Promise", "resolve", "validate", "valid", "createGeneralConfig", "ElMessage", "onMounted", "typeCode", "item", "updateGeneralConfig", "success", "center", "isEdit", "error", "msg"], "mappings": "m/BAgEA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,GAAI,EACJC,MAAOT,EAAUS,MACjBC,KAAM,GACNC,KAAM,GACNC,MAAO,GACPC,KAAMC,EAAaC,cAAcC,WAEjCC,OAAQ,KAGJC,EAAYb,EAAe,CAC/BO,MAAO,CAAC,CAAEO,UAAU,EAAMC,QAASlB,EAAE,mBAAoBmB,QAAS,SAClEV,KAAM,CAAC,CAAEQ,UAAU,EAAMC,QAASlB,EAAE,mBAAoBmB,QAAS,WAE7DC,EAAajB,EAAI,CACrBI,MAAOT,EAAUS,MACjBC,KAAM,GACNC,KAAM,GACNC,MAAO,GACPC,KAAMC,EAAaC,cAAcC,WACjCC,OAAQ,KAEJM,EAAWlB,EAA0B,IAErCQ,EAAOR,EAAIS,EAAaC,cAAcC,YAM5C,SAASQ,IACPpB,EAAQQ,OAAQ,EAChBa,EAAiBC,KAAK,CAAEjB,MAAOT,EAAUS,MAAOkB,IAAKC,EAAYC,IAAKhB,KAAMA,EAAKD,QAASkB,MAAMC,IAC9F3B,EAAQQ,OAAQ,EAChBW,EAASX,MAAQmB,EAAIC,IAAA,GACtB,CAGH,SAASC,IACA,OAAA,IAAIC,SAAeC,IACxB7B,EAAQM,OACNN,EAAQM,MAAMwB,UAAUC,IAClBA,GACFZ,EAAiBa,oBAAoBhB,EAAWV,OAAOkB,MAAMC,IAC1C,IAAbA,EAAIrB,OACI6B,EAAA,CACRnB,QAASlB,EAAE,eACXW,KAAM,YAEAW,KAEFW,GAAA,GACT,GAEJ,GACJ,QA7BHK,GAAU,KACAhB,GAAA,iVA+BSiB,OACjB5B,EAAKD,MAAQ6B,OACLjB,IAFV,IAAmBiB,iyEAKHC,QACdnC,EAAKK,MAAQ8B,EACN,IAAIR,SAAeC,IACxBV,EAAiBkB,oBAAoBpC,EAAKK,OAAOkB,MAAMC,IACpC,IAAbA,EAAIrB,MACN6B,EAAUK,QAAQ,CAChBxB,QAASlB,EAAE,eACX2C,QAAQ,IAEVH,EAAKI,QAAS,GAEdP,EAAUQ,MAAM,CACd3B,QAASW,EAAIiB,IACbH,QAAQ,IAGJV,GAAA,GACT,IAjBL,IAAgBO"}