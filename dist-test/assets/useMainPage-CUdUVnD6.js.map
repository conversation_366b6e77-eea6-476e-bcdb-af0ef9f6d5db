{"version": 3, "file": "useMainPage-CUdUVnD6.js", "sources": ["../../src/utils/composables/useMainPage.ts"], "sourcesContent": ["import useSettingsStore from '@/store/modules/settings'\r\nimport useTabbarStore from '@/store/modules/tabbar'\r\n\r\nexport default function useMainPage() {\r\n  const route = useRoute()\r\n  const router = useRouter()\r\n\r\n  const settingsStore = useSettingsStore()\r\n  const tabbarStore = useTabbarStore()\r\n\r\n  const tabbar = useTabbar()\r\n\r\n  function reload() {\r\n    router.push({\r\n      name: 'reload',\r\n    })\r\n  }\r\n\r\n  function setCustomTitle(title: string) {\r\n    settingsStore.setCustomTitle(route.fullPath, title)\r\n    if (settingsStore.settings.tabbar.enable) {\r\n      tabbarStore.setCustomTitle({\r\n        tabId: tabbar.getId(),\r\n        title,\r\n      })\r\n    }\r\n  }\r\n\r\n  function resetCustomTitle() {\r\n    settingsStore.resetCustomTitle(route.fullPath)\r\n    if (settingsStore.settings.tabbar.enable) {\r\n      tabbarStore.resetCustomTitle(tabbar.getId())\r\n    }\r\n  }\r\n\r\n  function maximize(status: boolean) {\r\n    settingsStore.setMainPageMaximize(status)\r\n  }\r\n\r\n  return {\r\n    reload,\r\n    setCustomTitle,\r\n    resetCustomTitle,\r\n    maximize,\r\n  }\r\n}\r\n"], "names": ["useMainPage", "route", "useRoute", "router", "useRouter", "settingsStore", "useSettingsStore", "tabbarStore", "useTabbarStore", "tabbar", "useTabbar", "reload", "push", "name", "setCustomTitle", "title", "fullPath", "settings", "enable", "tabId", "getId", "resetCustomTitle", "maximize", "status", "setMainPageMaximize"], "mappings": "0GAGA,SAAwBA,IACtB,MAAMC,EAAQC,IACRC,EAASC,IAETC,EAAgBC,IAChBC,EAAcC,IAEdC,EAASC,IA6BR,MAAA,CACLC,OA5BF,WACER,EAAOS,KAAK,CACVC,KAAM,UACP,EA0BDC,eAvBF,SAAwBC,GACRV,EAAAS,eAAeb,EAAMe,SAAUD,GACzCV,EAAcY,SAASR,OAAOS,QAChCX,EAAYO,eAAe,CACzBK,MAAOV,EAAOW,QACdL,SAEJ,EAiBAM,iBAdF,WACgBhB,EAAAgB,iBAAiBpB,EAAMe,UACjCX,EAAcY,SAASR,OAAOS,QACpBX,EAAAc,iBAAiBZ,EAAOW,QACtC,EAWAE,SARF,SAAkBC,GAChBlB,EAAcmB,oBAAoBD,EAAM,EAS5C"}