{"version": 3, "file": "verifylist-D1qm-vQb.js", "sources": ["../../src/views/merchant-finance/ar/acc-verify/verifylist.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"accountName\": \"Acct Set\",\r\n    \"accountPeriod\": \"Period\",\r\n    \"permanentAccountPeriod\": \"Permanent Period\",\r\n    \"days\": \"days\",\r\n    \"validityPeriod\": \"Validity\",\r\n    \"to\": \"To\",\r\n    \"receivePayment\": \"Receive\",\r\n    \"accountBalance\": \"Balance\",\r\n    \"unverifiedAmount\": \"Unverified\",\r\n    \"availableAmount\": \"Available\",\r\n    \"maximumLimit\": \"Max Limit\",\r\n    \"accountType\": \"Type\",\r\n    \"unverified\": \"Unverified\",\r\n    \"verified\": \"Verified\",\r\n    \"reversed\": \"Reversed\",\r\n    \"postingDate\": \"Posting Date\",\r\n    \"businessDate\": \"Business Date\",\r\n    \"enterSearch\": \"Enter name, room, order, or external order\",\r\n    \"filter\": \"Filter\",\r\n    \"batchVerification\": \"Batch Verify\",\r\n    \"guestRoom\": \"Guest/Room No.\",\r\n    \"orderNumber\": \"Order No.\",\r\n    \"externalOrderNumber\": \"External Order No.\",\r\n    \"unverifiedAmountColumn\": \"Unverified\",\r\n    \"generationTime\": \"Generation Time\",\r\n    \"businessDay\": \"Business Day\",\r\n    \"status\": \"Status\",\r\n    \"reversedStatus\": \"Reversed\",\r\n    \"unverifiedStatus\": \"Unverified\",\r\n    \"verifiedStatus\": \"Verified\",\r\n    \"remark\": \"Remark\",\r\n    \"operation\": \"Actions\",\r\n    \"verification\": \"Verify\",\r\n    \"revoke\": \"Revoke\",\r\n    \"confirmRevoke\": \"Confirm revoke?\",\r\n    \"creditAccount\": \"Credit\",\r\n    \"prepaidAccount\": \"Prepaid\",\r\n    \"successRevoke\": \"Revoke successful\",\r\n    \"revokeFailed\": \"Revoke failed\",\r\n    \"begin\": \"Begin\",\r\n    \"end\": \"End\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"accountName\": \"账户名称\",\r\n    \"accountPeriod\": \"账期\",\r\n    \"permanentAccountPeriod\": \"永久账期\",\r\n    \"days\": \"天\",\r\n    \"validityPeriod\": \"有效期\",\r\n    \"to\": \"至\",\r\n    \"receivePayment\": \"收款\",\r\n    \"accountBalance\": \"账户余额\",\r\n    \"unverifiedAmount\": \"未核销金额\",\r\n    \"availableAmount\": \"可用额度\",\r\n    \"maximumLimit\": \"最高限额\",\r\n    \"accountType\": \"账务类型\",\r\n    \"unverified\": \"未核销\",\r\n    \"verified\": \"已核销\",\r\n    \"reversed\": \"已冲调\",\r\n    \"postingDate\": \"挂账日期\",\r\n    \"businessDate\": \"营业日期\",\r\n    \"enterSearch\": \"请输入姓名、房号、订单号、外部订单号\",\r\n    \"filter\": \"查询\",\r\n    \"batchVerification\": \"批量核销\",\r\n    \"guestRoom\": \"入住人/房号\",\r\n    \"orderNumber\": \"订单号\",\r\n    \"externalOrderNumber\": \"外部单号\",\r\n    \"unverifiedAmountColumn\": \"未核金额\",\r\n    \"generationTime\": \"生成时间\",\r\n    \"businessDay\": \"营业日\",\r\n    \"status\": \"状态\",\r\n    \"reversedStatus\": \"已冲调\",\r\n    \"unverifiedStatus\": \"未核销\",\r\n    \"verifiedStatus\": \"已核销\",\r\n    \"remark\": \"备注\",\r\n    \"operation\": \"操作\",\r\n    \"verification\": \"核销\",\r\n    \"revoke\": \"撤销\",\r\n    \"confirmRevoke\": \"确认撤销吗？\",\r\n    \"creditAccount\": \"信用账户\",\r\n    \"prepaidAccount\": \"预付账户\",\r\n    \"successRevoke\": \"撤销成功\",\r\n    \"revokeFailed\": \"撤销失败\",\r\n    \"begin\":\"开始\",\r\n    \"end\":\"结束\"\r\n  },\r\n  \"km\": {\r\n    \"accountName\": \"ឈ្មោះគណនី\",\r\n    \"accountPeriod\": \"រយៈពេលគណនី\",\r\n    \"permanentAccountPeriod\": \"រយៈពេលគណនីអចិន្ត្រៃយ៍\",\r\n    \"days\": \"ថ្ងៃ\",\r\n    \"validityPeriod\": \"រយៈពេលសុពលភាព\",\r\n    \"to\": \"ទៅ\",\r\n    \"receivePayment\": \"ទទួលប្រាក់\",\r\n    \"accountBalance\": \"សមតុល្យគណនី\",\r\n    \"unverifiedAmount\": \"មិនបានផ្ទៀងផ្ទាត់\",\r\n    \"availableAmount\": \"ចំនួនដែលអាចប្រើបាន\",\r\n    \"maximumLimit\": \"ដែនកំណត់អតិបរមា\",\r\n    \"accountType\": \"ប្រភេទគណនី\",\r\n    \"unverified\": \"មិនបានផ្ទៀងផ្ទាត់\",\r\n    \"verified\": \"បានផ្ទៀងផ្ទាត់\",\r\n    \"reversed\": \"បានបញ្ច្រាស\",\r\n    \"postingDate\": \"កាលបរិច្ឆេទបង្ហោង\",\r\n    \"businessDate\": \"កាលបរិច្ឆេទអាជីវកម្ម\",\r\n    \"enterSearch\": \"សូមបញ្ចូលឈ្មោះ លេខបន្ទប់ លេខដឹកនាំ ឬលេខដឹកនាំខាងក្រៅ\",\r\n    \"filter\": \"ស្វែងរក\",\r\n    \"batchVerification\": \"ផ្ទៀងផ្ទាត់ជាក្រុម\",\r\n    \"guestRoom\": \"ភ្ញៀវ/លេខបន្ទប់\",\r\n    \"orderNumber\": \"លេខដឹកនាំ\",\r\n    \"externalOrderNumber\": \"លេខដឹកនាំខាងក្រៅ\",\r\n    \"unverifiedAmountColumn\": \"ចំនួនដែលមិនបានផ្ទៀងផ្ទាត់\",\r\n    \"generationTime\": \"ពេលវេលាបង្កើត\",\r\n    \"businessDay\": \"ថ្ងៃអាជីវកម្ម\",\r\n    \"status\": \"ស្ថានភាព\",\r\n    \"reversedStatus\": \"បានបញ្ច្រាស\",\r\n    \"unverifiedStatus\": \"មិនបានផ្ទៀងផ្ទាត់\",\r\n    \"verifiedStatus\": \"បានផ្ទៀងផ្ទាត់\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"verification\": \"ផ្ទៀងផ្ទាត់\",\r\n    \"revoke\": \"ដកហូត\",\r\n    \"confirmRevoke\": \"បញ្ជាក់ការដកហូត?\",\r\n    \"creditAccount\": \"គណនីឥណទាន\",\r\n    \"prepaidAccount\": \"គណនីបង់មុន\",\r\n    \"successRevoke\": \"ដកហូតដោយជោគជ័យ\",\r\n    \"revokeFailed\": \"ដកហូតបរាជ័យ\",\r\n    \"begin\": \"ចាប់ផ្តើម\",\r\n    \"end\": \"បញ្ចប់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { ArSetAccountListRespVO, ArSetRespVO } from '@/models/index'\r\nimport type bookDetails from '@/views/room/booking/bookDetails.d'\r\n\r\nimport { arSetApi } from '@/api/modules/index'\r\nimport { AccountState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nimport { formattedMoney } from '@/utils/moneyUtil'\r\nimport dayjs from 'dayjs'\r\nimport { LocationQueryRaw } from 'vue-router'\r\nimport RechargeForm from './components/DetailForm/recharge.vue'\r\nimport VerifyForm from './components/DetailForm/verify.vue'\r\nimport VerifyLogForm from './components/DetailForm/verifylog.vue'\r\n\r\ndefineOptions({\r\n  name: 'MerchantFinanceArAccOffList',\r\n})\r\nconst p = defineProps<{\r\n  arSetCode: string\r\n  creditAccType: string\r\n}>()\r\nconst router = useRouter()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n\r\n  // 详情\r\n  formModeProps: {\r\n    visible: false,\r\n    verifyLogVisible: false,\r\n    accNos: [] as string[],\r\n    arSetCode: '',\r\n  },\r\n  formModeArSet: {\r\n    visible: false,\r\n    arSetCode: '',\r\n  },\r\n\r\n  // 搜索\r\n  search: {\r\n    // 账套代码\r\n    arSetCode: '',\r\n    // 账务类型 0: 未核销, 1: 已核销\r\n    isVerify: '0',\r\n    // 日期\r\n    date: '',\r\n    // 日期开始\r\n    startDate: '',\r\n    // 日期结束\r\n    endDate: '',\r\n    // 日期搜索类型 0: 挂账日期, 1: 营业日期\r\n    dateType: '0',\r\n    // 关键词\r\n    keyWords: '',\r\n  },\r\n  // 批量操作\r\n  batch: {\r\n    enable: true,\r\n    selectionDataList: [],\r\n  },\r\n  // 列表数据\r\n  dataList: [] as ArSetAccountListRespVO[],\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\ndefineExpose({\r\n  childMethod,\r\n})\r\n\r\nconst isShow = ref(false)\r\nconst clickArSetCode = ref('')\r\nfunction childMethod(arSetCode: string, creditAccType: string) {\r\n  isShow.value = creditAccType === '0'\r\n  clickArSetCode.value = arSetCode\r\n  getDataList(arSetCode)\r\n  getInfo(arSetCode)\r\n}\r\n\r\nfunction getDataList(arSetCode: string) {\r\n  data.value.loading = true\r\n  const [startDate, endDate] = data.value.search.date || ['', ''] // 从 date 中解构开始和结束日期\r\n  // 格式化日期为 'YYYY-MM-DD' 格式\r\n  const formattedStartDate = startDate ? dayjs(startDate).format('YYYY-MM-DD') : ''\r\n  const formattedEndDate = endDate ? dayjs(endDate).format('YYYY-MM-DD') : ''\r\n  const params = {\r\n    arSetCode,\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isVerify: isShow.value ? (data.value.search.isVerify === '2' ? '' : data.value.search.isVerify) : '1',\r\n    state: data.value.search.isVerify === '2' ? AccountState.REDEEMED : '',\r\n    dateRange: data.value.search.dateType, // 日期类型\r\n    startDate: formattedStartDate, // 开始日期\r\n    endDate: formattedEndDate, // 结束日期\r\n    keyWords: data.value.search.keyWords,\r\n    ...getParams(),\r\n  }\r\n  arSetApi.getArSetAccSetAccountPage(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      data.value.loading = false\r\n      data.value.dataList = res.data.list\r\n      pagination.value.total = res.data.total\r\n    }\r\n  })\r\n}\r\n\r\nfunction searchClick() {\r\n  getDataList(clickArSetCode.value)\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList(p.arSetCode))\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList(p.arSetCode))\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList(p.arSetCode))\r\n}\r\nconst selectedList = ref<any[]>([])\r\nfunction onVerifyList() {\r\n  const accNoList = [] as string[]\r\n  selectedList.value.forEach((o: any) => {\r\n    accNoList.push(o.accNo)\r\n  })\r\n  data.value.formModeProps.accNos = accNoList\r\n  data.value.formModeProps.arSetCode = p.arSetCode\r\n  data.value.formModeProps.visible = true\r\n}\r\n\r\nfunction onVerify(row: any) {\r\n  const codeList = [] as string[]\r\n  codeList.push(row.accNo)\r\n  data.value.formModeProps.accNos = codeList\r\n  data.value.formModeProps.arSetCode = p.arSetCode\r\n  data.value.formModeProps.visible = true\r\n}\r\nfunction onLog(row: any) {\r\n  arSetApi.revocation(row).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success({\r\n        message: t('successRevoke'),\r\n        center: true,\r\n      })\r\n      childMethod(p.arSetCode, p.creditAccType)\r\n    } else {\r\n      ElMessage.error({\r\n        message: res.msg,\r\n        center: true,\r\n      })\r\n    }\r\n  })\r\n}\r\nfunction getRowKey(row: any) {\r\n  return row.accNo\r\n}\r\n\r\nfunction handleSelectionChange(row: any) {\r\n  selectedList.value = row\r\n}\r\nconst arSerInfo = ref<ArSetRespVO>({} as ArSetRespVO)\r\n// 获取账套内容\r\nfunction getInfo(arSetCode: string) {\r\n  arSetApi.getArSet(userStore.gcode, arSetCode).then((res: any) => {\r\n    data.value.loading = false\r\n    if (res.code === 0) {\r\n      arSerInfo.value = res.data\r\n    }\r\n  })\r\n}\r\nfunction recharge() {\r\n  data.value.formModeArSet.visible = true\r\n  data.value.formModeArSet.arSetCode = p.arSetCode\r\n}\r\n/** 新窗口打开 */\r\nfunction navigateAndReload(row: ArSetAccountListRespVO) {\r\n  // general:订单 book:预订单 group:团队主单 cash:现付账订单   现付账订单不能点击打开 LocationQueryRaw\r\n  if (row.accType == 'general' || row.accType == 'book') {\r\n    const query: bookDetails.queryTypes = {\r\n      /** 是否全屏 */\r\n      fullscreen: 'true',\r\n      /** 订单号 */\r\n      no: row.no!,\r\n      /** 订单类型 */\r\n      noType: row.accType == 'general' ? 'order' : 'book',\r\n      /** 打开弹窗为必传 */\r\n      modelValue: 'true',\r\n      /** 显示的tabs页 */\r\n      tabName: 'account',\r\n    }\r\n    window.open(router.resolve({ name: 'orderDetails', query: { ...query } }).href, '_blank')\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <search-bar :show-toggle=\"false\">\r\n      <div class=\"account-info\">\r\n        <div class=\"account-header\">\r\n          <span class=\"account-name\">{{ arSerInfo.arSetName }}</span>\r\n          <el-tag type=\"primary\" effect=\"light\" round class=\"account-type\">\r\n            {{ isShow ? t('creditAccount') : t('prepaidAccount') }}\r\n          </el-tag>\r\n          <span class=\"account-period\"> {{ t('accountPeriod') }}：{{ arSerInfo.creditPayDays === '0' ? `${arSerInfo.creditPayFix}${t('days')}` : t('permanentAccountPeriod') }} </span>\r\n          <span class=\"account-validity\"> {{ t('validityPeriod') }}：{{ arSerInfo.creditStartDate }} {{ t('to') }} {{ arSerInfo.creditEndDate }} </span>\r\n        </div>\r\n      </div>\r\n      <!-- 账户金额信息卡片 -->\r\n      <el-row :gutter=\"24\" class=\"amount-cards\">\r\n        <el-col :span=\"6\">\r\n          <el-card shadow=\"hover\" class=\"amount-card\">\r\n            <div class=\"amount-content\">\r\n              <span class=\"amount-label\">{{ t('accountBalance') }}</span>\r\n              <div class=\"amount-value\">\r\n                <span class=\"money\">￥{{ formattedMoney(arSerInfo.balance) }}</span>\r\n                <el-button type=\"primary\" size=\"small\" link @click=\"recharge\">\r\n                  {{ t('receivePayment') }}\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col v-show=\"isShow\" :span=\"6\">\r\n          <el-card shadow=\"hover\" class=\"amount-card\">\r\n            <div class=\"amount-content\">\r\n              <span class=\"amount-label\">{{ t('unverifiedAmount') }}</span>\r\n              <span class=\"amount-value money\">￥{{ formattedMoney(arSerInfo.unverifiedAmount) }}</span>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col v-show=\"isShow\" :span=\"6\">\r\n          <el-card shadow=\"hover\" class=\"amount-card\">\r\n            <div class=\"amount-content\">\r\n              <span class=\"amount-label\">{{ t('availableAmount') }}</span>\r\n              <span class=\"amount-value money\">￥{{ formattedMoney(arSerInfo.availableAmount) }}</span>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col v-show=\"isShow\" :span=\"6\">\r\n          <el-card shadow=\"hover\" class=\"amount-card\">\r\n            <div class=\"amount-content\">\r\n              <span class=\"amount-label\">{{ t('maximumLimit') }}</span>\r\n              <span class=\"amount-value money\">￥{{ formattedMoney(arSerInfo.creditQuota) }}</span>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n      <el-form :model=\"data.search\" size=\"default\" label-width=\"100px\" inline-message inline class=\"search-form\">\r\n        <el-form-item v-if=\"isShow\" :label=\"t('accountType')\">\r\n          <el-radio-group v-model=\"data.search.isVerify\" @change=\"getDataList(p.arSetCode)\">\r\n            <el-radio-button value=\"0\">\r\n              {{ t('unverified') }}\r\n            </el-radio-button>\r\n            <el-radio-button value=\"1\">\r\n              {{ t('verified') }}\r\n            </el-radio-button>\r\n            <el-radio-button value=\"2\">\r\n              {{ t('reversed') }}\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-select v-model=\"data.search.dateType\" style=\"width: 100px\">\r\n            <el-option :label=\"t('postingDate')\" value=\"0\" />\r\n            <el-option :label=\"t('businessDate')\" value=\"1\" />\r\n          </el-select>\r\n          <el-date-picker v-model=\"data.search.date\" type=\"daterange\" :range-separator=\"t('to')\" :start-placeholder=\"t('begin')\" :end-placeholder=\"t('end')\" style=\"width: 240px\" />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-input v-model=\"data.search.keyWords\" :placeholder=\"t('enterSearch')\" clearable style=\"width: 300px\" />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchClick()\">\r\n            <template #icon>\r\n              <svg-icon name=\"ep:search\" />\r\n            </template>\r\n            {{ t('filter') }}\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </search-bar>\r\n    <el-divider border-style=\"dashed\" />\r\n    <el-button v-if=\"data.search.isVerify === '0' && isShow\" v-auth=\"'finance:brokerage-record:update'\" size=\"default\" :disabled=\"selectedList.length === 0\" @click=\"onVerifyList\">\r\n      {{ t('batchVerification') }}\r\n    </el-button>\r\n    <el-table\r\n      v-loading=\"data.loading\"\r\n      class=\"list-table\"\r\n      :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\"\r\n      :data=\"data.dataList\"\r\n      highlight-current-row\r\n      height=\"658\"\r\n      :row-key=\"getRowKey\"\r\n      @sort-change=\"sortChange\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column v-if=\"data.batch.enable\" type=\"selection\" width=\"50\" align=\"center\" fixed />\r\n      <el-table-column :label=\"t('guestRoom')\" min-width=\"150\">\r\n        <template #default=\"scope\">\r\n          <span>{{ scope.row.verifyName }}/{{ scope.row.rNo }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"no\" :label=\"t('orderNumber')\" min-width=\"160\" show-overflow-tooltip />\r\n      <el-table-column prop=\"outOrderNo\" :label=\"t('externalOrderNumber')\" min-width=\"160\" show-overflow-tooltip />\r\n      <el-table-column :label=\"t('unverifiedAmountColumn')\" align=\"right\" min-width=\"100\">\r\n        <template #default=\"scope\">\r\n          <span class=\"cursor-pointer\" :class=\"[{ 'text-[var(--el-color-primary)]': scope.row.accType == 'general' || scope.row.accType == 'book' }]\" @click=\"navigateAndReload(scope.row)\"> ￥{{ formattedMoney(scope.row.fee) }} </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"createTime\" :label=\"t('generationTime')\" min-width=\"150\" show-overflow-tooltip />\r\n      <el-table-column prop=\"bizDate\" :label=\"t('businessDay')\" min-width=\"100\" show-overflow-tooltip />\r\n      <el-table-column :label=\"t('status')\" align=\"center\" width=\"80\">\r\n        <template #default=\"scope\">\r\n          <el-tag :type=\"scope.row.state === AccountState.REDEEMED ? 'info' : scope.row.isVerify === '1' ? 'success' : 'warning'\" size=\"small\">\r\n            {{ scope.row.state === AccountState.REDEEMED ? t('reversedStatus') : scope.row.isVerify === '1' ? t('verifiedStatus') : t('unverifiedStatus') }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"remark\" :label=\"t('remark')\" min-width=\"120\" show-overflow-tooltip />\r\n      <el-table-column :label=\"t('operation')\" width=\"120\" align=\"center\" fixed=\"right\">\r\n        <template #default=\"scope\">\r\n          <div v-if=\"isShow && data.search.isVerify !== '2'\" v-auth=\"'finance:brokerage-record:update'\">\r\n            <el-link v-if=\"scope.row.isVerify === '0'\" type=\"primary\" @click=\"onVerify(scope.row)\">\r\n              {{ t('verification') }}\r\n            </el-link>\r\n            <el-popconfirm v-if=\"scope.row.isVerify === '1'\" :title=\"t('confirmRevoke')\" @confirm=\"onLog(scope.row)\">\r\n              <template #reference>\r\n                <el-link type=\"primary\">\r\n                  {{ t('revoke') }}\r\n                </el-link>\r\n              </template>\r\n            </el-popconfirm>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-pagination\r\n      v-if=\"pagination.total > 10\"\r\n      :current-page=\"pagination.pageNo\"\r\n      :total=\"pagination.total\"\r\n      :page-size=\"pagination.pageSize\"\r\n      :page-sizes=\"pagination.sizes\"\r\n      :layout=\"pagination.layout\"\r\n      :hide-on-single-page=\"false\"\r\n      class=\"pagination\"\r\n      background\r\n      @size-change=\"sizeChange\"\r\n      @current-change=\"currentChange\"\r\n    />\r\n    <VerifyForm v-if=\"data.formModeProps.visible\" v-model=\"data.formModeProps.visible\" :ar-set-code=\"data.formModeProps.arSetCode\" :acc-nos=\"data.formModeProps.accNos\" @success=\"childMethod(p.arSetCode, p.creditAccType)\" />\r\n    <VerifyLogForm v-if=\"data.formModeProps.verifyLogVisible\" v-model=\"data.formModeProps.verifyLogVisible\" :ar-set-code=\"data.formModeProps.arSetCode\" :acc-nos=\"data.formModeProps.accNos\" @success=\"childMethod(p.arSetCode, p.creditAccType)\" />\r\n    <RechargeForm v-if=\"data.formModeArSet.visible\" v-model=\"data.formModeArSet.visible\" :ar-set-code=\"data.formModeArSet.arSetCode\" @success=\"childMethod(p.arSetCode, p.creditAccType)\" />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-link {\r\n  margin: 0 10px;\r\n}\r\n\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.action-card .content {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  /* 垂直居中 */\r\n  justify-content: center;\r\n\r\n  /* 水平居中 */\r\n  height: 100%;\r\n\r\n  /* 确保容器有足够的高度 */\r\n  font-size: 14px;\r\n}\r\n\r\n.item {\r\n  text-align: center;\r\n\r\n  /* 文本居中 */\r\n}\r\n\r\n.intro {\r\n  font-size: 16px;\r\n  // 字体加粗\r\n  font-weight: bold;\r\n}\r\n\r\n.account-info {\r\n  margin-bottom: 16px;\r\n\r\n  .account-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 16px;\r\n\r\n    .account-name {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .account-type {\r\n      margin-right: 24px;\r\n    }\r\n\r\n    .account-period,\r\n    .account-validity {\r\n      color: #606266;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.amount-cards {\r\n  margin-bottom: 20px;\r\n\r\n  .amount-card {\r\n    .amount-content {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      gap: 8px;\r\n\r\n      .amount-label {\r\n        color: #606266;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .amount-value {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        .money {\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #303133;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["p", "__props", "router", "useRouter", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "userStore", "useUserStore", "data", "ref", "loading", "tableAutoHeight", "formModeProps", "visible", "verifyLogVisible", "accNos", "arSetCode", "formModeArSet", "search", "isVerify", "date", "startDate", "endDate", "dateType", "key<PERSON>ords", "batch", "enable", "selectionDataList", "dataList", "t", "useI18n", "__expose", "<PERSON><PERSON><PERSON><PERSON>", "isShow", "clickArSetCode", "creditAccType", "value", "getDataList", "arSetApi", "getArSet", "gcode", "then", "res", "code", "arSerInfo", "getInfo", "formattedStartDate", "dayjs", "format", "formattedEndDate", "params", "hcode", "state", "AccountState", "REDEEMED", "date<PERSON><PERSON><PERSON>", "getArSetAccSetAccountPage", "list", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "selectedList", "onVerifyList", "accNoList", "for<PERSON>ach", "o", "push", "accNo", "getRowKey", "row", "handleSelectionChange", "recharge", "accType", "query", "fullscreen", "no", "noType", "modelValue", "tabName", "window", "open", "resolve", "name", "href", "codeList", "revocation", "ElMessage", "success", "message", "center", "error", "msg"], "mappings": "qxEAwJA,MAAMA,GAAIC,EAIJC,GAASC,KACTC,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,mBAAcC,GAAiBC,aAAAA,IAAiBC,IACzEC,GAAYC,IACZC,GAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAGjBC,cAAe,CACbC,SAAS,EACTC,kBAAkB,EAClBC,OAAQ,GACRC,UAAW,IAEbC,cAAe,CACbJ,SAAS,EACTG,UAAW,IAIbE,OAAQ,CAENF,UAAW,GAEXG,SAAU,IAEVC,KAAM,GAENC,UAAW,GAEXC,QAAS,GAETC,SAAU,IAEVC,SAAU,IAGZC,MAAO,CACLC,QAAQ,EACRC,kBAAmB,IAGrBC,SAAU,MAGNC,EAAEA,IAAMC,IAEDC,EAAA,CACXC,iBAGI,MAAAC,GAASxB,GAAI,GACbyB,GAAiBzB,EAAI,IAClB,SAAAuB,GAAYhB,EAAmBmB,GACtCF,GAAOG,MAA0B,MAAlBD,EACfD,GAAeE,MAAQpB,EACvBqB,GAAYrB,GA4Fd,SAAiBA,GACfsB,EAASC,SAASjC,GAAUkC,MAAOxB,GAAWyB,MAAMC,IAClDlC,GAAK4B,MAAM1B,SAAU,EACJ,IAAbgC,EAAIC,OACNC,GAAUR,MAAQM,EAAIlC,KAAA,GAEzB,CAjGDqC,CAAQ7B,EAAS,CAGnB,SAASqB,GAAYrB,GACnBR,GAAK4B,MAAM1B,SAAU,EACf,MAACW,EAAWC,GAAWd,GAAK4B,MAAMlB,OAAOE,MAAQ,CAAC,GAAI,IAEtD0B,EAAqBzB,EAAY0B,EAAM1B,GAAW2B,OAAO,cAAgB,GACzEC,EAAmB3B,EAAUyB,EAAMzB,GAAS0B,OAAO,cAAgB,GACnEE,EAAS,CACblC,YACAwB,MAAOlC,GAAUkC,MACjBW,MAAO7C,GAAU6C,MACjBhC,SAAUc,GAAOG,MAAwC,MAA/B5B,GAAK4B,MAAMlB,OAAOC,SAAmB,GAAKX,GAAK4B,MAAMlB,OAAOC,SAAY,IAClGiC,MAAsC,MAA/B5C,GAAK4B,MAAMlB,OAAOC,SAAmBkC,EAAaC,SAAW,GACpEC,UAAW/C,GAAK4B,MAAMlB,OAAOK,SAC7BF,UAAWyB,EACXxB,QAAS2B,EACTzB,SAAUhB,GAAK4B,MAAMlB,OAAOM,YACzBvB,MAELqC,EAASkB,0BAA0BN,GAAQT,MAAMC,IAC9B,IAAbA,EAAIC,OACNnC,GAAK4B,MAAM1B,SAAU,EAChBF,GAAA4B,MAAMR,SAAWc,EAAIlC,KAAKiD,KACpBzD,GAAAoC,MAAMsB,MAAQhB,EAAIlC,KAAKkD,MAAA,GAErC,CAQH,SAASC,GAAWC,GAClB1D,GAAa0D,GAAMnB,MAAK,IAAMJ,GAAYzC,GAAEoB,YAAU,CAI/C,SAAA6C,GAAcC,EAAO,GAC5B3D,GAAgB2D,GAAMrB,MAAK,IAAMJ,GAAYzC,GAAEoB,YAAU,CAI3D,SAAS+C,IAAWC,KAAEA,EAAMC,MAAAA,IACb7D,GAAA4D,EAAMC,GAAOxB,MAAK,IAAMJ,GAAYzC,GAAEoB,YAAU,CAEzD,MAAAkD,GAAezD,EAAW,IAChC,SAAS0D,KACP,MAAMC,EAAY,GACLF,GAAA9B,MAAMiC,SAASC,IAChBF,EAAAG,KAAKD,EAAEE,MAAK,IAEnBhE,GAAA4B,MAAMxB,cAAcG,OAASqD,EAC7B5D,GAAA4B,MAAMxB,cAAcI,UAAYpB,GAAEoB,UAClCR,GAAA4B,MAAMxB,cAAcC,SAAU,CAAA,CA0BrC,SAAS4D,GAAUC,GACjB,OAAOA,EAAIF,KAAA,CAGb,SAASG,GAAsBD,GAC7BR,GAAa9B,MAAQsC,CAAA,CAEjB,MAAA9B,GAAYnC,EAAiB,IAUnC,SAASmE,KACFpE,GAAA4B,MAAMnB,cAAcJ,SAAU,EAC9BL,GAAA4B,MAAMnB,cAAcD,UAAYpB,GAAEoB,SAAA,yzGAtEvCqB,GAAYH,GAAeE,+yCAyE7B,SAA2BsC,GAEzB,GAAmB,WAAfA,EAAIG,SAAuC,QAAfH,EAAIG,QAAmB,CACrD,MAAMC,EAAgC,CAEpCC,WAAY,OAEZC,GAAIN,EAAIM,GAERC,OAAuB,WAAfP,EAAIG,QAAuB,QAAU,OAE7CK,WAAY,OAEZC,QAAS,WAEXC,OAAOC,KAAKvF,GAAOwF,QAAQ,CAAEC,KAAM,eAAgBT,MAAO,IAAKA,KAAWU,KAAM,SAAQ,CAC1F,w8BA7DF,SAAkBd,GAChB,MAAMe,EAAW,GACRA,EAAAlB,KAAKG,EAAIF,OACbhE,GAAA4B,MAAMxB,cAAcG,OAAS0E,EAC7BjF,GAAA4B,MAAMxB,cAAcI,UAAYpB,GAAEoB,UAClCR,GAAA4B,MAAMxB,cAAcC,SAAU,CAAA,qLAEtB6D,aACbpC,EAASoD,WAAWhB,GAAKjC,MAAMC,IACZ,IAAbA,EAAIC,MACNgD,EAAUC,QAAQ,CAChBC,QAAShE,GAAE,iBACXiE,QAAQ,IAEE9D,GAAApC,GAAEoB,UAAWpB,GAAEuC,gBAE3BwD,EAAUI,MAAM,CACdF,QAASnD,EAAIsD,IACbF,QAAQ,GACT,IAZP,IAAepB"}