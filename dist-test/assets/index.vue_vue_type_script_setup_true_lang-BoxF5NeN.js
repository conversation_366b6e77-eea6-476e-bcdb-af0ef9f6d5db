import e from"./HDropdown-7tWwMFON.js";import{_ as o}from"./HTabList.vue_vue_type_script_setup_true_lang-CHW-QDXA.js";import{d as t,W as n,o as i,e as r,w as a,f as s,u as c,_ as l}from"./index-CkEhI1Zk.js";const m=t({name:"ColorScheme",__name:"index",setup(t){const m=n();function d(e){var o;const{startViewTransition:t}=(n=()=>{m.currentColorScheme&&m.setColorScheme("dark"===m.currentColorScheme?"light":"dark")},{startViewTransition:function(){if(document.startViewTransition&&!window.matchMedia("(prefers-reduced-motion: reduce)").matches)return document.startViewTransition((async()=>{await Promise.resolve(n())}));n()}});var n;null==(o=t())||o.ready.then((()=>{const o=e.clientX,t=e.clientY,n=[`circle(0px at ${o}px ${t}px)`,`circle(${Math.hypot(Math.max(o,innerWidth-o),Math.max(t,innerHeight-t))}px at ${o}px ${t}px)`];document.documentElement.animate({clipPath:"dark"!==m.settings.app.colorScheme?n:n.reverse()},{duration:300,easing:"ease-out",pseudoElement:"dark"!==m.settings.app.colorScheme?"::view-transition-new(root)":"::view-transition-old(root)"})}))}return(t,n)=>{const u=l,p=o,h=e;return i(),r(h,{class:"flex-center cursor-pointer p-2"},{dropdown:a((()=>[s(p,{modelValue:c(m).settings.app.colorScheme,"onUpdate:modelValue":n[0]||(n[0]=e=>c(m).settings.app.colorScheme=e),options:[{icon:"i-ri:sun-line",label:"",value:"light"},{icon:"i-ri:moon-line",label:"",value:"dark"},{icon:"i-codicon:color-mode",label:"",value:""}],class:"m-3"},null,8,["modelValue"])])),default:a((()=>[s(u,{name:{light:"i-ri:sun-line",dark:"i-ri:moon-line","":"i-codicon:color-mode"}[c(m).settings.app.colorScheme],onClick:d},null,8,["name"])])),_:1})}}});export{m as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-BoxF5NeN.js.map
