{"version": 3, "file": "myTemplateEdit-rZOGbXZG.js", "sources": ["../../src/views/marketing/sms/template/components/DetailForm/myTemplateEdit.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"editSuccess\": \"Edit Successful\",\r\n    \"addSuccess\": \"Add Successful\",\r\n    \"cancel\": \"Cancel\",\r\n    \"submit\": \"Submit\",\r\n    \"templateName\": \"Template Name\",\r\n    \"enterTemplateName\": \"Please enter the template name\",\r\n    \"templateContent\": \"Template Content\",\r\n    \"enterTemplateContent\": \"Please enter the template content\",\r\n    \"selectSendingScene\": \"Please select the sending scene\",\r\n    \"selectApplicationChannel\": \"Please select the application channel\",\r\n    \"smsPreview\": \"SMS Preview\",\r\n    \"hotelParameters\": \"Hotel Parameters\",\r\n    \"groupName\": \"Group Name\",\r\n    \"orderParameters\": \"Order Parameters\",\r\n    \"checkInTime\": \"Check-in Time\",\r\n    \"guestParameters\": \"Guest Parameters\",\r\n    \"membershipCardNumber\": \"Membership Card Number\",\r\n    \"accessibleWebsite\": \"Accessible Website\",\r\n    \"contactPhone\": \"Contact Phone\",\r\n    \"applicationDescription\": \"Application Description\",\r\n    \"isGroupOwned\": \"Is Group Owned\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"associatedStore\": \"Associated Store\",\r\n    \"selectStore\": \"Please select a store\",\r\n    \"licenseImageURL\": \"License Image URL\",\r\n    \"fileType\": \"File Type\",\r\n    \"companyLicense\": \"Company License (Signature is Company Name)\",\r\n    \"appStoreScreenshot\": \"App Store Backend Screenshot (Signature is App Name)\",\r\n    \"icpRecordScreenshot\": \"ICP Record Screenshot (Signature is Website Name)\",\r\n    \"wechatPlatformScreenshot\": \"WeChat Public Platform Screenshot (Signature is Public Account/Mini Program Name)\",\r\n    \"trademarkCertificate\": \"Trademark Registration Certificate/Trademark Soft Copyright Proof (Signature is Trademark Name)\",\r\n    \"signatureContent\": \"Signature Content\",\r\n    \"signatureContentPlaceholder\": \"Please enter the signature content\",\r\n    \"editSignature\": \"Edit Signature\",\r\n    \"addSignature\": \"Add Signature\",\r\n    \"selectFileType\": \"Please select file type\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"editSuccess\": \"编辑成功\",\r\n    \"addSuccess\": \"新增成功\",\r\n    \"cancel\": \"取消\",\r\n    \"submit\": \"提交\",\r\n    \"templateName\": \"模板名称\",\r\n    \"enterTemplateName\": \"请输入模板名称\",\r\n    \"templateContent\": \"模板内容\",\r\n    \"enterTemplateContent\": \"请输入模板内容\",\r\n    \"selectSendingScene\": \"请选择发送场景\",\r\n    \"selectApplicationChannel\": \"请选择应用渠道\",\r\n    \"smsPreview\": \"短信预览\",\r\n    \"hotelParameters\": \"酒店参数\",\r\n    \"groupName\": \"集团名称\",\r\n    \"orderParameters\": \"订单参数\",\r\n    \"checkInTime\": \"入住时间\",\r\n    \"guestParameters\": \"客人参数\",\r\n    \"membershipCardNumber\": \"会员卡号\",\r\n    \"accessibleWebsite\": \"可访问网址\",\r\n    \"contactPhone\": \"联系人电话\",\r\n    \"applicationDescription\": \"申请说明\",\r\n    \"isGroupOwned\": \"是否为集团所有\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"associatedStore\": \"所属门店\",\r\n    \"selectStore\": \"请选择门店\",\r\n    \"licenseImageURL\": \"营业执照图片URL\",\r\n    \"fileType\": \"文件类型\",\r\n    \"companyLicense\": \"公司营业执照(签名是公司名称)\",\r\n    \"appStoreScreenshot\": \"应用商店APP管理后台全屏截图(签名是APP名称)\",\r\n    \"icpRecordScreenshot\": \"ICP备案截图(签名是网站名称)\",\r\n    \"wechatPlatformScreenshot\": \"微信公众平台管理界面全屏截图(签名是公众号/小程序名称)\",\r\n    \"trademarkCertificate\": \"商标注册证书/商标软著权证明(签名是商标名称)\",\r\n    \"signatureContent\": \"签名内容\",\r\n    \"signatureContentPlaceholder\": \"请输入签名内容\",\r\n    \"editSignature\": \"编辑签名\",\r\n    \"addSignature\": \"新增签名\",\r\n    \"selectFileType\": \"请选择文件类型\"\r\n  },\r\n  \"km\": {\r\n    \"editSuccess\": \"កែសម្រួលជោគជ័យ\",\r\n    \"addSuccess\": \"បន្ថែមជោគជ័យ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"submit\": \"ដាក់ស្នើ\",\r\n    \"templateName\": \"ឈ្មោះគំរូ\",\r\n    \"enterTemplateName\": \"សូមបញ្ចូលឈ្មោះគំរូ\",\r\n    \"templateContent\": \"មាតិកាគំរូ\",\r\n    \"enterTemplateContent\": \"សូមបញ្ចូលមាតិកាគំរូ\",\r\n    \"selectSendingScene\": \"សូមជ្រើសរើសឈុតផ្ញើ\",\r\n    \"selectApplicationChannel\": \"សូមជ្រើសរើសឆានែលកម្មវិធី\",\r\n    \"smsPreview\": \"មើលសារមុនផ្ញើ\",\r\n    \"hotelParameters\": \"ប៉ារ៉ាម៉ែត្រសណ្ឋាគារ\",\r\n    \"groupName\": \"ឈ្មោះក្រុមហ៊ុន\",\r\n    \"orderParameters\": \"ប៉ារ៉ាម៉ែត្រការបញ្ជាទិញ\",\r\n    \"checkInTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n    \"guestParameters\": \"ប៉ារ៉ាម៉ែត្រភ្ញៀវ\",\r\n    \"membershipCardNumber\": \"លេខកាតសមាជិក\",\r\n    \"accessibleWebsite\": \"គេហទំព័រដែលអាចចូលប្រើបាន\",\r\n    \"contactPhone\": \"លេខទូរស័ព្ទទំនាក់ទំនង\",\r\n    \"applicationDescription\": \"ការពិពណ៌នាកម្មវិធី\",\r\n    \"isGroupOwned\": \"ជាកម្មសិទ្ធិរបស់ក្រុមហ៊ុន\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"associatedStore\": \"ហាងដែលភ្ជាប់\",\r\n    \"selectStore\": \"សូមជ្រើសរើសហាង\",\r\n    \"licenseImageURL\": \"URL រូបភាពអាជ្ញាប័ណ្ណ\",\r\n    \"fileType\": \"ប្រភេទឯកសារ\",\r\n    \"companyLicense\": \"អាជ្ញាប័ណ្ណក្រុមហ៊ុន (ហត្ថលេខាគឺឈ្មោះក្រុមហ៊ុន)\",\r\n    \"appStoreScreenshot\": \"រូបថតអេក្រង់ផ្នែកខាងក្រោយ App Store (ហត្ថលេខាគឺឈ្មោះកម្មវិធី)\",\r\n    \"icpRecordScreenshot\": \"រូបថតកំណត់ត្រា ICP (ហត្ថលេខាគឺឈ្មោះគេហទំព័រ)\",\r\n    \"wechatPlatformScreenshot\": \"រូបថតអេក្រង់វេទិកាសាធារណៈ WeChat (ហត្ថលេខាគឺឈ្មោះគណនីសាធារណៈ/កម្មវិធីតូច)\",\r\n    \"trademarkCertificate\": \"វិញ្ញាបនប័ត្រកំណត់សញ្ញាពាណិជ្ជកម្ម/ភស្តុតាងសិទ្ធិអ្នកនិពន្ធសញ្ញាពាណិជ្ជកម្ម (ហត្ថលេខាគឺឈ្មោះសញ្ញាពាណិជ្ជកម្ម)\",\r\n    \"signatureContent\": \"មាតិកាហត្ថលេខា\",\r\n    \"signatureContentPlaceholder\": \"សូមបញ្ចូលមាតិកាហត្ថលេខា\",\r\n    \"editSignature\": \"កែសម្រួលហត្ថលេខា\",\r\n    \"addSignature\": \"បន្ថែមហត្ថលេខា\",\r\n    \"selectFileType\": \"សូមជ្រើសរើសប្រភេទឯកសារ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\n\r\nimport type { DetailFormProps } from '../../types'\r\nimport moban from '@/api/modules/moban.api'\r\nimport sign from '@/api/modules/sign.api'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  templateCode: '',\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  templateCode: props.templateCode,\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n  /** 模板名称 */\r\n  templateName: '',\r\n  /** 内容 */\r\n  content: '',\r\n  /** 是否系统模板 */\r\n  isSys: '0',\r\n  /** 模板类型 0:普通模板 1:营销模板 */\r\n  templateType: '0',\r\n  /** 是否集团短信模板 0:否 1：是 */\r\n  isG: '0',\r\n  /** 状态 0:待审核 1:启用 2：停用 3:未通过 */\r\n  state: '0',\r\n  /** 未通过理由 */\r\n  reason: '',\r\n  scene: '',\r\n  channer: '',\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  templateName: [{ required: true, message: t('enterTemplateName'), trigger: 'blur' }],\r\n  content: [{ required: true, message: t('enterTemplateContent'), trigger: 'blur' }],\r\n})\r\n\r\nconst merchants = ref<{ hcode: string; hname: string }[]>([])\r\n\r\nonMounted(() => {\r\n  // getInfo()\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  const prms = {\r\n    gCode: userStore.gcode,\r\n    hCode: userStore.hcode,\r\n  }\r\n  moban.list(prms).then((res: any) => {\r\n    loading.value = false\r\n    form.value = res.data\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      if (form.value.templateCode === '') {\r\n        formRef.value &&\r\n          formRef.value.validate((valid) => {\r\n            // getInfo()\r\n            if (valid) {\r\n              const prms = {\r\n                creator: 'admin',\r\n                gcode: '1781282000378556416',\r\n                hcode: '1781282590420660224',\r\n                isGroup: 0,\r\n                sendScene: '1',\r\n                tplContent: '{vipName}你已成功注册为{hotelName}会员,会员卡号为{roomCode}',\r\n                tplName: '新建模板2',\r\n                useChannel: 100000001,\r\n              }\r\n              const prms1 = {\r\n                id: 2,\r\n                cellNumber: '13875935213',\r\n                description: '申请签名',\r\n                gcode: '1781282000378556416',\r\n                hcode: '1781282590420660224',\r\n                isGroup: 0,\r\n                licenseUrl: 'http://sms.yiduohua.net/abc.jpg',\r\n                proveType: 1,\r\n                sign: '一朵花',\r\n                website: 'pms.yiduohua.net',\r\n              }\r\n              sign.update(prms1).then(() => {\r\n                ElMessage.success({\r\n                  message: t('addSuccess'),\r\n                  center: true,\r\n                })\r\n                // resolve()\r\n              })\r\n            }\r\n          })\r\n      }\r\n    })\r\n  },\r\n})\r\n\r\nconst y = ref(0)\r\n\r\nfunction x() {\r\n  y.value = form.value.content.length\r\n}\r\n\r\nconst textareaRef = ref(null)\r\nconst textareaContent = ref('')\r\n\r\nfunction insertTextAtPosition(textToInsert) {\r\n  textToInsert = `#${textToInsert}#`\r\n  if (textareaRef.value) {\r\n    const inputElement = textareaRef.value.$el.querySelector('textarea')\r\n    if (inputElement) {\r\n      const startPos = inputElement.selectionStart\r\n      const endPos = inputElement.selectionEnd\r\n      const wrappedText = `${textToInsert}`\r\n      textareaContent.value = textareaContent.value.substring(0, startPos) + wrappedText + textareaContent.value.substring(endPos, textareaContent.value.length)\r\n      // 重新设置光标位置\r\n      inputElement.selectionStart = startPos + textToInsert.length\r\n      inputElement.selectionEnd = startPos + textToInsert.length\r\n    }\r\n  }\r\n}\r\n\r\nfunction handleInput(event) {\r\n  textareaContent.value = event.target.value\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"120px\" label-suffix=\"：\">\r\n      <div class=\"flex-banner\">\r\n        <div class=\"form-left\">\r\n          <el-form-item :label=\"t('templateName')\" prop=\"templateName\">\r\n            <el-input v-model=\"form.templateName\" :placeholder=\"t('enterTemplateName')\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('selectSendingScene')\">\r\n            <el-select v-model=\"form.scene\" collapse-tags collapse-tags-tooltip :placeholder=\"t('selectSendingScene')\" style=\"width: 150px\">\r\n              <el-option :label=\"t('addOrder')\" value=\"1\" />\r\n              <el-option :label=\"t('cancelOrder')\" value=\"2\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('selectApplicationChannel')\">\r\n            <el-select v-model=\"form.channer\" collapse-tags collapse-tags-tooltip :placeholder=\"t('selectApplicationChannel')\" style=\"width: 150px\">\r\n              <el-option :label=\"t('store')\" value=\"1\" />\r\n              <el-option :label=\"t('wechat')\" value=\"2\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('smsPreview')\">\r\n            {{ textareaContent }}\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"form-right\">\r\n          <el-form-item :label=\"t('hotelParameters')\">\r\n            <div class=\"label-flex\" @click=\"insertTextAtPosition('gropName')\">\r\n              {{ t('groupName') }}\r\n            </div>\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('orderParameters')\">\r\n            <div class=\"label-flex\" @click=\"insertTextAtPosition('roomTime')\">\r\n              {{ t('checkInTime') }}\r\n            </div>\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('guestParameters')\">\r\n            <div class=\"label-flex\" @click=\"insertTextAtPosition('memenberCard')\">\r\n              {{ t('membershipCardNumber') }}\r\n            </div>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-input ref=\"textareaRef\" v-model=\"textareaContent\" type=\"textarea\" :rows=\"6\" :placeholder=\"t('signatureContentPlaceholder')\" @input=\"handleInput\" />\r\n          </el-form-item>\r\n        </div>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.flex-banner {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  height: 400px;\r\n\r\n  .form-left {\r\n    width: 28%;\r\n\r\n    /* border: 1px solid; */\r\n  }\r\n\r\n  .form-right {\r\n    width: 68%;\r\n\r\n    /* border: 1px solid; */\r\n    .label-flex {\r\n      padding: 0 10px;\r\n      margin-right: 5px;\r\n      margin-bottom: 5px;\r\n      font-size: 12px;\r\n      cursor: pointer;\r\n      user-select: none;\r\n      border: 1px solid #efefef;\r\n      border-radius: 5px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "templateCode", "gcode", "hcode", "templateName", "content", "isSys", "templateType", "isG", "state", "reason", "scene", "channer", "formRules", "required", "message", "trigger", "onMounted", "__expose", "submit", "Promise", "resolve", "value", "validate", "valid", "prms1", "id", "cellNumber", "description", "isGroup", "licenseUrl", "proveType", "sign", "website", "update", "then", "ElMessage", "success", "center", "textareaRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insertTextAtPosition", "textToInsert", "inputElement", "$el", "querySelector", "startPos", "selectionStart", "endPos", "selectionEnd", "wrappedText", "substring", "length", "handleInput", "event", "target"], "mappings": "stBAkIA,MAAMA,EAAQC,GAIRC,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,aAAcV,EAAMU,aAEpBC,MAAOP,EAAUO,MAEjBC,MAAOR,EAAUQ,MAEjBC,aAAc,GAEdC,QAAS,GAETC,MAAO,IAEPC,aAAc,IAEdC,IAAK,IAELC,MAAO,IAEPC,OAAQ,GACRC,MAAO,GACPC,QAAS,KAGLC,EAAYf,EAAe,CAC/BM,aAAc,CAAC,CAAEU,UAAU,EAAMC,QAAStB,EAAE,qBAAsBuB,QAAS,SAC3EX,QAAS,CAAC,CAAES,UAAU,EAAMC,QAAStB,EAAE,wBAAyBuB,QAAS,WAGzDlB,EAAwC,IAE1DmB,GAAU,SAgBGC,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACQ,KAA5BrB,EAAKsB,MAAMrB,cACbF,EAAQuB,OACNvB,EAAQuB,MAAMC,UAAUC,IAEtB,GAAIA,EAAO,CAWT,MAAMC,EAAQ,CACZC,GAAI,EACJC,WAAY,cACZC,YAAa,OACb1B,MAAO,sBACPC,MAAO,sBACP0B,QAAS,EACTC,WAAY,kCACZC,UAAW,EACXC,KAAM,MACNC,QAAS,oBAEXD,EAAKE,OAAOT,GAAOU,MAAK,KACtBC,EAAUC,QAAQ,CAChBtB,QAAStB,EAAE,cACX6C,QAAQ,GACT,GAEF,IAEJ,MAMDxC,EAAI,GAMR,MAAAyC,EAAczC,EAAI,MAClB0C,EAAkB1C,EAAI,IAE5B,SAAS2C,EAAqBC,GAE5B,GADAA,EAAe,IAAIA,KACfH,EAAYjB,MAAO,CACrB,MAAMqB,EAAeJ,EAAYjB,MAAMsB,IAAIC,cAAc,YACzD,GAAIF,EAAc,CAChB,MAAMG,EAAWH,EAAaI,eACxBC,EAASL,EAAaM,aACtBC,EAAc,GAAGR,IACvBF,EAAgBlB,MAAQkB,EAAgBlB,MAAM6B,UAAU,EAAGL,GAAYI,EAAcV,EAAgBlB,MAAM6B,UAAUH,EAAQR,EAAgBlB,MAAM8B,QAEtIT,EAAAI,eAAiBD,EAAWJ,EAAaU,OACzCT,EAAAM,aAAeH,EAAWJ,EAAaU,MAAA,CACtD,CACF,CAGF,SAASC,EAAYC,GACHd,EAAAlB,MAAQgC,EAAMC,OAAOjC,KAAA"}