{"version": 3, "file": "togetherBill-ByLEtbff.js", "sources": ["../../src/views/print/togetherBill.vue"], "sourcesContent": ["<i18n>\r\n{\r\n\t\"en\": {\r\n\t\t\"printGuestBill\": \"Print Guest Bill\",\r\n\t\t\"cancel\": \"Cancel\"\r\n\t},\r\n\t\"zh-cn\": {\r\n\t\t\"printGuestBill\": \"打印宾客账单\",\r\n\t\t\"cancel\": \"取消\"\r\n\t},\r\n  \"km\": {\r\n    \"printGuestBill\": \"បោះពុម្ពវិក័យប័ត្រភ្ញៀវ\",\r\n    \"cancel\": \"បោះបង់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { printApi, printFormApi } from '@/api/modules/index'\r\nimport { BillType, layout_width_map, PrintFormat } from '@/models/dict/constants'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { ElMessage } from 'element-plus'\r\nimport { computed, createApp, nextTick, onMounted, ref } from 'vue'\r\n\r\nimport PosTogetherBill from './posTogetherBill.vue'\r\n\r\ndefineOptions({\r\n  name: 'PrintCheckInForm',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    togetherCode: string | number\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    togetherCode: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  refresh: []\r\n}>()\r\n\r\n// 声明 Stimulsoft 类型\r\n// @ts-ignore\r\nconst Stimulsoft = window.Stimulsoft\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  tableAutoHeight: true,\r\n  showWidth: '600px',\r\n})\r\nconst dialogWidth = computed(() => {\r\n  return editDialogVisible.value ? '600px' : data.value.showWidth\r\n})\r\nconst loading = ref(false)\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nonMounted(async () => {\r\n  await Promise.all([getPrintLayout(), getPrintInfo()])\r\n})\r\n\r\nconst layout = ref(PrintFormat.POS.toString())\r\nconst isPosFormat = computed(() => layout.value === PrintFormat.POS.toString())\r\n\r\nasync function getPrintLayout() {\r\n  printApi\r\n    .getPrintLayout({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      billCode: BillType.BILL,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        layout.value = res.data.layout\r\n        data.value.showWidth = layout_width_map.get(layout.value) ?? '400px'\r\n      }\r\n    })\r\n}\r\n\r\nconst originalBillData = ref(null)\r\nconst editableBillData = ref(null)\r\nconst editDialogVisible = ref(false)\r\n\r\nasync function getPrintInfo() {\r\n  loading.value = true\r\n  printFormApi\r\n    .printTogetherBill({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      togetherCode: props.togetherCode,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        originalBillData.value = JSON.parse(JSON.stringify(res.data))\r\n        editableBillData.value = JSON.parse(JSON.stringify(res.data))\r\n        setJson(res.data)\r\n        loading.value = false\r\n      }\r\n    })\r\n}\r\n\r\nconst timestamp = Date.now()\r\nlet reportInstance: any\r\nconst reportReady = ref(false)\r\n\r\nasync function setJson(json: any) {\r\n  reportReady.value = false\r\n  const licensePath = new URL('/src/assets/license.key', import.meta.url).href\r\n  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)\r\n  const report = new Stimulsoft.Report.StiReport()\r\n  if (layout.value === PrintFormat.A4.toString()) {\r\n    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printTogetherBillA4.mrt?t=${timestamp}`)\r\n  } else if (layout.value === PrintFormat.A412.toString()) {\r\n    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printTogetherBillA4-1-2.mrt?t=${timestamp}`)\r\n  } else if (layout.value === PrintFormat.A413.toString()) {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printTogetherBillA4-1-3.mrt?t=${timestamp}`)\r\n  } else {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printTogetherBill78.mrt?t=${timestamp}`)\r\n  }\r\n\r\n  const settingsStore = useSettingsStore()\r\n  const currentLanguage = settingsStore.lang\r\n  const localizationFile = `${currentLanguage}.xml`\r\n  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)\r\n\r\n  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')\r\n  dataSet.readJson(JSON.stringify(json))\r\n  report.dictionary.databases.clear()\r\n  report.regData('JSON', 'JSON', dataSet)\r\n  const viewerOptions = new Stimulsoft.Viewer.StiViewerOptions()\r\n  viewerOptions.toolbar.visible = false\r\n  const viewer = new Stimulsoft.Viewer.StiViewer(viewerOptions, 'StiViewer', false)\r\n  viewer.report = report\r\n  viewer.renderHtml('report')\r\n  reportInstance = report\r\n  loading.value = false\r\n  reportReady.value = true\r\n}\r\n\r\nfunction openEditDialog() {\r\n  editDialogVisible.value = true\r\n}\r\n\r\nasync function onEditSave() {\r\n  editDialogVisible.value = false\r\n  await nextTick()\r\n  setJson(editableBillData.value)\r\n}\r\n\r\nasync function onEditCancel() {\r\n  editDialogVisible.value = false\r\n  editableBillData.value = JSON.parse(JSON.stringify(originalBillData.value))\r\n  await nextTick()\r\n  setJson(editableBillData.value)\r\n}\r\n\r\nfunction printReport() {\r\n  if (!reportReady.value || !reportInstance) {\r\n    ElMessage.warning('报表还未加载完成，请稍后再试')\r\n    return\r\n  }\r\n  reportInstance.print()\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n  editableBillData.value = JSON.parse(JSON.stringify(originalBillData.value))\r\n}\r\n\r\nfunction removeConsumptionDetail(index: number) {\r\n  editableBillData.value.consumptionDetails.splice(index, 1)\r\n}\r\n\r\nfunction addConsumptionDetail() {\r\n  if (!editableBillData.value.consumptionDetails) {\r\n    editableBillData.value.consumptionDetails = []\r\n  }\r\n  editableBillData.value.consumptionDetails.push({\r\n    subCode: '',\r\n    subName: '',\r\n    fee: 0,\r\n    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\r\n  })\r\n}\r\n\r\nfunction removePaymentDetail(index: number) {\r\n  editableBillData.value.paymentDetails.splice(index, 1)\r\n}\r\n\r\nfunction addPaymentDetail() {\r\n  if (!editableBillData.value.paymentDetails) {\r\n    editableBillData.value.paymentDetails = []\r\n  }\r\n  editableBillData.value.paymentDetails.push({\r\n    subCode: '',\r\n    subName: '',\r\n    fee: 0,\r\n    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\r\n  })\r\n}\r\n\r\nfunction refreshReport() {\r\n  setJson(editableBillData.value)\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :width=\"dialogWidth\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\" style=\"min-height: 600px\">\r\n    <div v-if=\"!editDialogVisible\" style=\"display: flex; flex-direction: column; height: 100%\">\r\n      <div style=\"display: flex; gap: 10px; justify-content: center; margin-bottom: 10px\">\r\n        <el-button type=\"primary\" :disabled=\"!reportReady\" @click=\"printReport\">\r\n          {{ t('printGuestBill') }}\r\n        </el-button>\r\n        <el-button @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button\r\n          @click=\"\r\n            () => {\r\n              editDialogVisible = true\r\n            }\r\n          \"\r\n        >\r\n          虚拟账单\r\n        </el-button>\r\n      </div>\r\n      <div id=\"report\" style=\"flex: 1; overflow-y: auto\" />\r\n    </div>\r\n    <div v-else>\r\n      <el-form :model=\"editableBillData\" label-width=\"100px\" style=\"height: 60vh; overflow-y: auto; padding: 20px\">\r\n        <div class=\"form-section\">\r\n          <h3 class=\"section-title\">基本信息</h3>\r\n          <el-form-item label=\"客人姓名\">\r\n            <el-input v-model=\"editableBillData.name\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"房号\">\r\n            <el-input v-model=\"editableBillData.rNo\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"房型\">\r\n            <el-input v-model=\"editableBillData.rtName\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"储值金额\">\r\n            <el-input v-model=\"editableBillData.balance\" type=\"number\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"入住时间\">\r\n            <el-input v-model=\"editableBillData.checkinTime\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"离店时间\">\r\n            <el-input v-model=\"editableBillData.checkoutTime\" />\r\n          </el-form-item>\r\n        </div>\r\n        <div class=\"form-section\">\r\n          <h3 class=\"section-title\">消费明细</h3>\r\n          <el-table :data=\"editableBillData?.consumptionDetails || []\" style=\"width: 100%; margin-bottom: 16px\">\r\n            <el-table-column prop=\"subName\" label=\"账目\" width=\"120\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.subName\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"时间\" width=\"180\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.createTime\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fee\" label=\"金额\" width=\"120\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.fee\" type=\"number\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column width=\"80\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"danger\" link @click=\"removeConsumptionDetail(scope.$index)\"> 删除 </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-bottom: 16px\">\r\n            <el-button type=\"primary\" @click=\"addConsumptionDetail\"> 添加消费明细 </el-button>\r\n          </div>\r\n        </div>\r\n        <div class=\"form-section\">\r\n          <h3 class=\"section-title\">付款明细</h3>\r\n          <el-table :data=\"editableBillData?.paymentDetails || []\" style=\"width: 100%\">\r\n            <el-table-column prop=\"subName\" label=\"账目\" width=\"120\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.subName\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"时间\" width=\"180\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.createTime\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"fee\" label=\"金额\" width=\"120\">\r\n              <template #default=\"scope\">\r\n                <el-input v-model=\"scope.row.fee\" type=\"number\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column width=\"80\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"danger\" link @click=\"removePaymentDetail(scope.$index)\"> 删除 </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-top: 16px\">\r\n            <el-button type=\"primary\" @click=\"addPaymentDetail\"> 添加付款明细 </el-button>\r\n          </div>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n    <template #footer>\r\n      <template v-if=\"!editDialogVisible\">\r\n        <!-- 报表页底部按钮（如无可省略） -->\r\n      </template>\r\n      <template v-else>\r\n        <el-button @click=\"onEditCancel\"> 复原 </el-button>\r\n        <el-button type=\"primary\" @click=\"onEditSave\"> 保存账单 </el-button>\r\n      </template>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n  }\r\n}\r\n\r\n:deep(.el-dialog__body) {\r\n  max-height: 70vh;\r\n  padding: 0;\r\n}\r\n\r\n:deep(.el-dialog__footer) {\r\n  position: sticky;\r\n  bottom: 0;\r\n  background-color: #fff;\r\n  z-index: 1;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\r\n  padding: 15px;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 24px;\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n\r\n  .section-title {\r\n    margin: 0 0 20px 0;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #303133;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "Stimulsoft", "window", "t", "useI18n", "userStore", "useUserStore", "data", "ref", "tableAutoHeight", "showWidth", "dialogWidth", "computed", "editDialogVisible", "value", "loading", "myVisible", "get", "modelValue", "set", "val", "onMounted", "async", "Promise", "all", "getPrintLayout", "getPrintInfo", "layout", "PrintFormat", "POS", "toString", "printApi", "gcode", "hcode", "billCode", "BillType", "BILL", "then", "res", "code", "layout_width_map", "originalBillData", "editableBillData", "printFormApi", "printTogetherBill", "togetherCode", "JSON", "parse", "stringify", "<PERSON><PERSON><PERSON>", "timestamp", "Date", "now", "reportInstance", "reportReady", "json", "licensePath", "URL", "url", "href", "Base", "StiLicense", "loadFromFile", "report", "Report", "StiReport", "A4", "loadFile", "A412", "A413", "localizationFile", "useSettingsStore", "lang", "Localization", "StiLocalization", "setLocalizationFile", "dataSet", "System", "Data", "DataSet", "read<PERSON>son", "dictionary", "databases", "clear", "regData", "viewerOptions", "Viewer", "StiViewerOptions", "toolbar", "visible", "viewer", "StiViewer", "renderHtml", "onEditSave", "nextTick", "onEditCancel", "printReport", "print", "ElMessage", "warning", "onCancel", "addConsumptionDetail", "consumptionDetails", "push", "subCode", "subName", "fee", "createTime", "toISOString", "slice", "replace", "addPaymentDetail", "paymentDetails", "index", "splice"], "mappings": "0yCA+BA,MAAMA,EAAQC,EAWRC,EAAQC,EAORC,EAAaC,OAAOD,YAEpBE,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAOC,EAAI,CACfC,iBAAiB,EACjBC,UAAW,UAEPC,EAAcC,GAAS,IACpBC,EAAkBC,MAAQ,QAAUP,EAAKO,MAAMJ,YAElDK,EAAUP,GAAI,GACdQ,EAAYJ,EAAS,CACzBK,IAAM,IACGpB,EAAMqB,WAEf,GAAAC,CAAIC,GACFrB,EAAM,oBAAqBqB,EAAG,IAIlCC,GAAUC,gBACFC,QAAQC,IAAI,CAACC,IAAkBC,MAAe,IAGtD,MAAMC,EAASnB,EAAIoB,EAAYC,IAAIC,YAGnCR,eAAeG,IACbM,EACGN,eAAe,CACdO,MAAO3B,EAAU2B,MACjBC,MAAO5B,EAAU4B,MACjBC,SAAUC,EAASC,OAEpBC,MAAMC,IACY,IAAbA,EAAIC,OACCZ,EAAAb,MAAQwB,EAAI/B,KAAKoB,OACxBpB,EAAKO,MAAMJ,UAAY8B,EAAiBvB,IAAIU,EAAOb,QAAU,QAAA,GAEhE,CAdeF,GAAS,IAAMe,EAAOb,QAAUc,EAAYC,IAAIC,aAiB9D,MAAAW,EAAmBjC,EAAI,MACvBkC,EAAmBlC,EAAI,MACvBK,EAAoBL,GAAI,GAE9Bc,eAAeI,KACbX,EAAQD,OAAQ,EAChB6B,EACGC,kBAAkB,CACjBZ,MAAO3B,EAAU2B,MACjBC,MAAO5B,EAAU4B,MACjBY,aAAchD,EAAMgD,eAErBR,MAAMC,IACY,IAAbA,EAAIC,OACNE,EAAiB3B,MAAQgC,KAAKC,MAAMD,KAAKE,UAAUV,EAAI/B,OACvDmC,EAAiB5B,MAAQgC,KAAKC,MAAMD,KAAKE,UAAUV,EAAI/B,OACvD0C,GAAQX,EAAI/B,MACZQ,EAAQD,OAAQ,EAAA,GAEnB,CAGC,MAAAoC,GAAYC,KAAKC,MACnB,IAAAC,GACE,MAAAC,GAAc9C,GAAI,GAExBc,eAAe2B,GAAQM,GACrBD,GAAYxC,OAAQ,EACpB,MAAM0C,EAAc,IAAoDC,IAAA,4xBAAAC,KAAAC,WAClE1D,EAAW2D,KAAKC,WAAWC,aAAaN,GAC9C,MAAMO,EAAS,IAAI9D,EAAW+D,OAAOC,UACjCtC,EAAOb,QAAUc,EAAYsC,GAAGpC,WAC3BiC,EAAAI,SAAS,uGAAuGjB,MAC9GvB,EAAOb,QAAUc,EAAYwC,KAAKtC,WACpCiC,EAAAI,SAAS,2GAA2GjB,MAClHvB,EAAOb,QAAUc,EAAYyC,KAAKvC,WAC3CiC,EAAOI,SAAS,gDAAkFjB,MAElGa,EAAOI,SAAS,4CAA8EjB,MAGhG,MAEMoB,EAAmB,GAFHC,IACgBC,WAEtCvE,EAAW2D,KAAKa,aAAaC,gBAAgBC,oBAAoBL,GAEjE,MAAMM,EAAU,IAAI3E,EAAW4E,OAAOC,KAAKC,QAAQ,QACnDH,EAAQI,SAASlC,KAAKE,UAAUO,IACzBQ,EAAAkB,WAAWC,UAAUC,QACrBpB,EAAAqB,QAAQ,OAAQ,OAAQR,GAC/B,MAAMS,EAAgB,IAAIpF,EAAWqF,OAAOC,iBAC5CF,EAAcG,QAAQC,SAAU,EAChC,MAAMC,EAAS,IAAIzF,EAAWqF,OAAOK,UAAUN,EAAe,aAAa,GAC3EK,EAAO3B,OAASA,EAChB2B,EAAOE,WAAW,UACDvC,GAAAU,EACjBhD,EAAQD,OAAQ,EAChBwC,GAAYxC,OAAQ,CAAA,CAOtBQ,eAAeuE,KACbhF,EAAkBC,OAAQ,QACpBgF,IACN7C,GAAQP,EAAiB5B,MAAK,CAGhCQ,eAAeyE,KACblF,EAAkBC,OAAQ,EAC1B4B,EAAiB5B,MAAQgC,KAAKC,MAAMD,KAAKE,UAAUP,EAAiB3B,cAC9DgF,IACN7C,GAAQP,EAAiB5B,MAAK,CAGhC,SAASkF,KACF1C,GAAYxC,OAAUuC,GAI3BA,GAAe4C,QAHbC,EAAUC,QAAQ,iBAGC,CAGvB,SAASC,KACPpF,EAAUF,OAAQ,EAClB4B,EAAiB5B,MAAQgC,KAAKC,MAAMD,KAAKE,UAAUP,EAAiB3B,OAAM,CAO5E,SAASuF,KACF3D,EAAiB5B,MAAMwF,qBACT5D,EAAA5B,MAAMwF,mBAAqB,IAE7B5D,EAAA5B,MAAMwF,mBAAmBC,KAAK,CAC7CC,QAAS,GACTC,QAAS,GACTC,IAAK,EACLC,YAAY,IAAIxD,MAAOyD,cAAcC,MAAM,EAAG,IAAIC,QAAQ,IAAK,MAChE,CAOH,SAASC,KACFrE,EAAiB5B,MAAMkG,iBACTtE,EAAA5B,MAAMkG,eAAiB,IAEzBtE,EAAA5B,MAAMkG,eAAeT,KAAK,CACzCC,QAAS,GACTC,QAAS,GACTC,IAAK,EACLC,YAAY,IAAIxD,MAAOyD,cAAcC,MAAM,EAAG,IAAIC,QAAQ,IAAK,MAChE,49EA7B8BG,gBAC/BvE,EAAiB5B,MAAMwF,mBAAmBY,OAAOD,EAAO,GAD1D,IAAiCA,w/BAgBJA,gBAC3BvE,EAAiB5B,MAAMkG,eAAeE,OAAOD,EAAO,GADtD,IAA6BA"}