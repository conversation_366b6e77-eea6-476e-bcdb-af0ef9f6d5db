import{d as e,ai as t,aj as l,b as a,av as s,y as o,aR as i,aq as n,u,o as c,c as d,f as r,w as p,h as b,Y as y,F as m,ag as g,e as h,g as f,R as v,aD as T,aS as S,m as k,E as C,l as V,j as _,k as D,bv as R,bK as L,n as O,p as w,x as U,aT as N}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css                          *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css                   */import{m as P}from"./member.api-2tU9HGvl.js";import{c as j}from"./channel.api-CM6FWEgD.js";import{g as E}from"./generalConfig.api-CEBBd8kx.js";import{p as x}from"./priceStrategy.api-Deh17XpK.js";import{r as A}from"./rt.api-5a8-At7-.js";import{d as I}from"./dictData.api-DUabpYqy.js";import{m as q}from"./merchant.api-BtmIsRm3.js";import{$ as M,a0 as Y,a1 as G,F as z,B as F,r as B,a2 as H,q as K,v as W,u as $,a3 as X,a4 as J,j as Q,G as Z,A as ee}from"./constants-Cg3j_uH4.js";import{y as te}from"./timeutils-Ib6GkGcq.js";import{_ as le}from"./_plugin-vue_export-helper-BCo6x5W8.js";const ae={class:"equity"},se={class:"equity"},oe={class:"equity"},ie={class:"equity"},ne={key:0},ue={key:1},ce={key:2},de=e({__name:"createStrategy",setup(e,{expose:le}){const de=t(),{t:re}=l(),pe=a(!1),be=a(),ye=a({gcode:de.gcode,strategyCode:"",strategyName:"",condition:{guestSrc:{guestSrcType:"0",guestSrcCodes:[]},rt:{rtType:"1",rtCodes:["0"]},checkinType:{type:"0",checkinTypes:[]},orderSource:["0"]},strategy:{discountsType:{type:M.DISCOUNT.toString(),value:null,priceHandle:Y.ROUND.toString()},rightsTypes:[{type:G.DELAY.toString(),value:"120",fee:""},{type:G.MULTI_POINT.toString(),value:"1",fee:""},{type:G.BREAKFAST.toString(),value:"1",fee:""}]},scope:{weeks:["1","2","3","4","5","6","7"],channels:[z.LOBBY],effectDate:[new Date,s(new Date).add(1,"year")],startDate:"",endDate:""},isEnable:F.YES,isG:F.YES,hotelCodes:[de.hcode]}),me=a({strategyName:[{required:!0,message:re("strategyNameRequired"),trigger:"blur"}]}),ge=a([]),he=a([]),fe=a([]);o((()=>{P.listMemberType(de.gcode,"1").then((e=>{0===e.code&&(he.value=e.data)})),function(){const e={gcode:de.gcode,isEnable:F.YES};j.getChannelSimpleList(e).then((e=>{0===e.code&&(ge.value=e.data)}))}(),function(){const e={gcode:de.gcode,isVirtual:F.NO,isGRt:F.YES,isEnable:F.YES};A.getRoomTypeSimpleList(e).then((e=>{0===e.code&&(Se.value=e.data)}))}(),q.getSimpleList(de.gcode).then((e=>{0===e.code&&(fe.value=e.data)})),I.getDictDataBatch(ve).then((e=>{Te.value.srcTypeList=e.data.filter((e=>e.dictType===B)),Te.value.rzConstants=e.data.filter((e=>e.dictType===K&&[Q.ALL_DAY,Q.LONG_STAY,Q.TRAVEL_GROUP,Q.MEETING_GROUP].includes(e.code))),Te.value.szConstants=e.data.filter((e=>e.dictType===W)),Te.value.lyConstants=e.data.filter((e=>e.dictType===$)),Te.value.qzsConstants=e.data.filter((e=>e.dictType===X)),Te.value.qylConstants=e.data.filter((e=>e.dictType===J)),Te.value.delayConstants=e.data.filter((e=>e.dictType===G.DELAY)),Te.value.multiPointConstants=e.data.filter((e=>e.dictType===G.MULTI_POINT))}))}));const ve=[B,H,K,W,$,X,J,G.DELAY,G.MULTI_POINT],Te=a({srcTypeList:[],rzConstants:[],szConstants:[],lyConstants:[],qzsConstants:[],qylConstants:[],delayConstants:[],multiPointConstants:[]});const Se=a([]);function ke(e){return e.getTime()<Date.now()-864e5}function Ce(e){e.includes("0")&&e.length>1?ye.value.condition.orderSource=e.filter((e=>"0"!==e)):e.includes("0")&&(ye.value.condition.orderSource=["0"])}function Ve(e){e.includes("0")&&e.length>1?ye.value.condition.rt.rtCodes=e.filter((e=>"0"!==e)):e.includes("0")&&(ye.value.condition.rt.rtCodes=["0"])}function _e(e){e.includes("0")&&e.length>1?ye.value.condition.guestSrc.guestSrcCodes=e.filter((e=>"0"!==e)):e.includes("0")&&(ye.value.condition.guestSrc.guestSrcCodes=["0"])}function De(){ye.value.condition.checkinType.checkinTypes=[]}function Re(e){ye.value.condition.guestSrc.guestSrcCodes=[],e!==Z.AGENT&&e!==Z.PROTOCOL||function(e){const t=e===Z.PROTOCOL?H:ee;E.list({gcode:de.gcode,type:t}).then((e=>{Le.value=e.data}))}(e)}le({submit:()=>new Promise((e=>{be.value&&be.value.validate((t=>{t&&(ye.value.scope.startDate=te(ye.value.scope.effectDate[0]),ye.value.scope.endDate=te(ye.value.scope.effectDate[1]),x.createPriceStrategy(ye.value).then((t=>{0===t.code?(i.success({message:re("saveSuccess"),center:!0}),e()):i.error({message:t.msg,center:!0})})))}))}))});const Le=a([]);function Oe(){const e=ye.value.strategy.rightsTypes.map((e=>e.type)),t=Te.value.qylConstants.filter((t=>!e.includes(t.code)));ye.value.strategy.rightsTypes.push({type:t[0].code,value:"",fee:""})}return(e,t)=>{const l=T,a=S,s=k,o=C,i=V,P=_,j=D,E=R,x=L,A=O,I=w,q=U,Y=N;return n((c(),d("div",null,[r(q,{ref_key:"formRef",ref:be,model:u(ye),rules:u(me),"label-width":"160px","label-suffix":"："},{default:p((()=>[r(l,{"content-position":"left"},{default:p((()=>[b(y(u(re)("strategyInfo")),1)])),_:1}),r(s,{label:u(re)("strategyName")},{default:p((()=>[r(a,{modelValue:u(ye).strategyName,"onUpdate:modelValue":t[0]||(t[0]=e=>u(ye).strategyName=e),placeholder:u(re)("strategyNamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),r(l,{"content-position":"left"},{default:p((()=>[b(y(u(re)("rules")),1)])),_:1}),r(s,{label:u(re)("guestSrcType")},{default:p((()=>[r(i,{modelValue:u(ye).condition.guestSrc.guestSrcType,"onUpdate:modelValue":t[1]||(t[1]=e=>u(ye).condition.guestSrc.guestSrcType=e),onChange:t[2]||(t[2]=e=>Re(u(ye).condition.guestSrc.guestSrcType))},{default:p((()=>[(c(!0),d(m,null,g(u(Te).srcTypeList,(e=>(c(),h(o,{key:e.code,value:e.code},{default:p((()=>[b(y(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),u(ye).condition.guestSrc.guestSrcType!==u(Z).ALL&&u(ye).condition.guestSrc.guestSrcType!==u(Z).WALK_IN&&""!==u(ye).condition.guestSrc.guestSrcType?(c(),h(s,{key:0,label:u(re)("level")},{default:p((()=>[f("div",ae,[r(j,{modelValue:u(ye).condition.guestSrc.guestSrcCodes,"onUpdate:modelValue":t[3]||(t[3]=e=>u(ye).condition.guestSrc.guestSrcCodes=e),placeholder:u(re)("selectLevel"),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",style:{width:"240px"},onChange:_e},{default:p((()=>[u(ye).condition.guestSrc.guestSrcType===u(Z).MEMBER?(c(),d(m,{key:0},[r(P,{label:u(re)("allMemberLevel"),value:"0"},null,8,["label"]),(c(!0),d(m,null,g(u(he),(e=>(c(),h(P,{key:e.mtCode,label:e.mtName,value:e.mtCode},null,8,["label","value"])))),128))],64)):v("",!0),u(ye).condition.guestSrc.guestSrcType===u(Z).AGENT||u(ye).condition.guestSrc.guestSrcType===u(Z).PROTOCOL?(c(),d(m,{key:1},[r(P,{label:u(re)("noLimit"),value:"0"},null,8,["label"]),(c(!0),d(m,null,g(u(Le),(e=>(c(),h(P,{key:e.code,label:e.name,value:e.code},null,8,["label","value"])))),128))],64)):v("",!0)])),_:1},8,["modelValue","placeholder"])])])),_:1},8,["label"])):v("",!0),r(s,{label:u(re)("roomType")},{default:p((()=>[f("div",se,[r(j,{modelValue:u(ye).condition.rt.rtCodes,"onUpdate:modelValue":t[4]||(t[4]=e=>u(ye).condition.rt.rtCodes=e),placeholder:u(re)("selectRoomType"),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",style:{width:"240px"},onChange:Ve},{default:p((()=>[r(P,{label:u(re)("allRoomType"),value:"0"},null,8,["label"]),(c(!0),d(m,null,g(u(Se),(e=>(c(),h(P,{key:e.rtCode,label:e.rtName,value:e.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])])),_:1},8,["label"]),r(s,{label:u(re)("checkinType")},{default:p((()=>[f("div",oe,[r(j,{modelValue:u(ye).condition.checkinType.type,"onUpdate:modelValue":t[5]||(t[5]=e=>u(ye).condition.checkinType.type=e),style:{width:"150px"},onChange:De},{default:p((()=>[r(P,{label:u(re)("allDayRoom"),value:"0"},null,8,["label"]),r(P,{label:u(re)("hourlyRoom"),value:"1"},null,8,["label"])])),_:1},8,["modelValue"]),r(j,{modelValue:u(ye).condition.checkinType.checkinTypes,"onUpdate:modelValue":t[6]||(t[6]=e=>u(ye).condition.checkinType.checkinTypes=e),placeholder:u(re)("selectCheckInType"),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",style:{width:"240px"}},{default:p((()=>["1"===u(ye).condition.checkinType.type?(c(!0),d(m,{key:0},g(u(Te).szConstants,(e=>(c(),h(P,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128)):(c(!0),d(m,{key:1},g(u(Te).rzConstants,(e=>(c(),h(P,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])])),_:1},8,["label"]),r(s,{label:u(re)("orderSource")},{default:p((()=>[r(j,{modelValue:u(ye).condition.orderSource,"onUpdate:modelValue":t[7]||(t[7]=e=>u(ye).condition.orderSource=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"240px"},clearable:"",onChange:Ce},{default:p((()=>[r(P,{label:u(re)("noLimitSource"),value:"0"},null,8,["label"]),(c(!0),d(m,null,g(u(Te).lyConstants,(e=>(c(),h(P,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),r(l,{"content-position":"left"},{default:p((()=>[b(y(u(re)("priceRights")),1)])),_:1}),r(s,{label:u(re)("discountType")},{default:p((()=>[f("div",ie,[r(j,{modelValue:u(ye).strategy.discountsType.type,"onUpdate:modelValue":t[8]||(t[8]=e=>u(ye).strategy.discountsType.type=e),style:{width:"150px"}},{default:p((()=>[r(P,{label:u(re)("priceDiscount"),value:u(M).DISCOUNT},null,8,["label","value"]),r(P,{label:u(re)("priceReduction"),value:u(M).REDUCE},null,8,["label","value"])])),_:1},8,["modelValue"]),u(ye).strategy.discountsType.type===u(M).DISCOUNT?(c(),d(m,{key:0},[r(a,{modelValue:u(ye).strategy.discountsType.value,"onUpdate:modelValue":t[9]||(t[9]=e=>u(ye).strategy.discountsType.value=e),placeholder:u(re)("represents"),"controls-position":"right",style:{width:"200px","margin-right":"8px"}},null,8,["modelValue","placeholder"]),b(y(u(re)("discount")),1)],64)):(c(),d(m,{key:1},[r(a,{modelValue:u(ye).strategy.discountsType.value,"onUpdate:modelValue":t[10]||(t[10]=e=>u(ye).strategy.discountsType.value=e),placeholder:u(re)("enterDiscountAmount"),"controls-position":"right",style:{width:"200px","margin-right":"8px"}},null,8,["modelValue","placeholder"]),b(y(u(re)("yuan")),1)],64))])])),_:1},8,["label"]),r(s,{label:u(re)("priceMethod")},{default:p((()=>[r(j,{modelValue:u(ye).strategy.discountsType.priceHandle,"onUpdate:modelValue":t[11]||(t[11]=e=>u(ye).strategy.discountsType.priceHandle=e),"collapse-tags":"","collapse-tags-tooltip":"",style:{width:"240px"}},{default:p((()=>[(c(!0),d(m,null,g(u(Te).qzsConstants,(e=>(c(),h(P,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),r(s,{label:u(re)("rightsType")},{default:p((()=>[(c(!0),d(m,null,g(u(ye).strategy.rightsTypes,((e,l)=>(c(),d("div",{key:l,class:"equity"},[r(j,{modelValue:e.type,"onUpdate:modelValue":t=>e.type=t,style:{width:"150px"}},{default:p((()=>[(c(!0),d(m,null,g(u(Te).qylConstants,(e=>(c(),h(P,{key:e.code,label:e.label,value:e.code,disabled:e.code===u(G).MULTI_POINT&&u(ye).condition.guestSrc.guestSrcType!==u(Z).MEMBER},null,8,["label","value","disabled"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"]),e.type===u(G).DELAY?(c(),d("div",ne,[r(j,{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,style:{width:"120px"}},{default:p((()=>[(c(!0),d(m,null,g(u(Te).delayConstants,(e=>(c(),h(P,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128)),t[16]||(t[16]=b('" '))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):v("",!0),e.type===u(G).MULTI_POINT?(c(),d("div",ue,[r(j,{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,style:{width:"120px"}},{default:p((()=>[(c(!0),d(m,null,g(u(Te).multiPointConstants,(e=>(c(),h(P,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):v("",!0),e.type===u(G).BREAKFAST?(c(),d("div",ce,[r(j,{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,style:{width:"120px"}},{default:p((()=>[r(P,{label:u(re)("breakfastOption1"),value:"1"},null,8,["label"]),r(P,{label:u(re)("breakfastOption2"),value:"2"},null,8,["label"]),r(P,{label:u(re)("breakfastOption3"),value:"3"},null,8,["label"]),r(P,{label:u(re)("breakfastOption4"),value:"4"},null,8,["label"])])),_:2},1032,["modelValue","onUpdate:modelValue"]),r(a,{modelValue:e.fee,"onUpdate:modelValue":t=>e.fee=t,placeholder:u(re)("breakfastFeePlaceholder"),"controls-position":"right",style:{width:"120px","margin-right":"8px"}},null,8,["modelValue","onUpdate:modelValue","placeholder"])])):v("",!0),u(ye).strategy.rightsTypes.length>1?(c(),h(E,{key:3,type:"primary",underline:!1,onClick:e=>function(e){ye.value.strategy.rightsTypes.splice(e,1)}(l)},{default:p((()=>[b(y(u(re)("delete")),1)])),_:2},1032,["onClick"])):v("",!0),t[17]||(t[17]=b("     ")),u(ye).strategy.rightsTypes.length<u(Te).qylConstants.length&&u(ye).strategy.rightsTypes.length===l+1?(c(),h(E,{key:4,type:"primary",underline:!1,onClick:Oe},{default:p((()=>[b(y(u(re)("add")),1)])),_:1})):v("",!0)])))),128))])),_:1},8,["label"]),r(l,{"content-position":"left"},{default:p((()=>[b(y(u(re)("applicationScope")),1)])),_:1}),r(s,{label:u(re)("effectiveDate"),prop:"effectDate"},{default:p((()=>[r(x,{modelValue:u(ye).scope.effectDate,"onUpdate:modelValue":t[12]||(t[12]=e=>u(ye).scope.effectDate=e),"disabled-date":ke,"end-placeholder":u(re)("endDate"),"range-separator":u(re)("to"),"start-placeholder":u(re)("startDate"),type:"daterange"},null,8,["modelValue","end-placeholder","range-separator","start-placeholder"])])),_:1},8,["label"]),r(s,{label:u(re)("week")},{default:p((()=>[r(I,{modelValue:u(ye).scope.weeks,"onUpdate:modelValue":t[13]||(t[13]=e=>u(ye).scope.weeks=e)},{default:p((()=>[r(A,{key:"7",value:"7"},{default:p((()=>[b(y(u(re)("sun")),1)])),_:1}),r(A,{key:"1",value:"1"},{default:p((()=>[b(y(u(re)("wen")),1)])),_:1}),r(A,{key:"2",value:"2"},{default:p((()=>[b(y(u(re)("tue")),1)])),_:1}),r(A,{key:"3",value:"3"},{default:p((()=>[b(y(u(re)("wed")),1)])),_:1}),r(A,{key:"4",value:"4"},{default:p((()=>[b(y(u(re)("thu")),1)])),_:1}),r(A,{key:"5",value:"5"},{default:p((()=>[b(y(u(re)("fri")),1)])),_:1}),r(A,{key:"6",value:"6"},{default:p((()=>[b(y(u(re)("sat")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),r(s,{label:u(re)("channels")},{default:p((()=>[r(j,{modelValue:u(ye).scope.channels,"onUpdate:modelValue":t[14]||(t[14]=e=>u(ye).scope.channels=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:u(re)("pleaseSelect"),style:{width:"240px"},clearable:""},{default:p((()=>[(c(!0),d(m,null,g(u(ge),(e=>(c(),h(P,{key:e.channelCode,label:e.channelName,value:e.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),r(s,{label:u(re)("hotels")},{default:p((()=>[r(j,{modelValue:u(ye).hotelCodes,"onUpdate:modelValue":t[15]||(t[15]=e=>u(ye).hotelCodes=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:u(re)("pleaseSelect"),style:{width:"240px"},clearable:""},{default:p((()=>[(c(!0),d(m,null,g(u(fe),(e=>(c(),h(P,{key:e.hcode,label:e.hname,value:e.hcode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1},8,["model","rules"])])),[[Y,u(pe)]])}}});function re(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{"zh-cn":{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"策略信息"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"房价策略名称"}},rules:{t:0,b:{t:2,i:[{t:3}],s:"规则"}},guestSrcType:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},level:{t:0,b:{t:2,i:[{t:3}],s:"级别"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"订单来源"}},priceRights:{t:0,b:{t:2,i:[{t:3}],s:"价格权益"}},discountType:{t:0,b:{t:2,i:[{t:3}],s:"优惠类"}},priceMethod:{t:0,b:{t:2,i:[{t:3}],s:"价格方式"}},rightsType:{t:0,b:{t:2,i:[{t:3}],s:"权益类"}},effectiveDate:{t:0,b:{t:2,i:[{t:3}],s:"生效日期"}},week:{t:0,b:{t:2,i:[{t:3}],s:"星期"}},applicationScope:{t:0,b:{t:2,i:[{t:3}],s:"应用范围"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},save:{t:0,b:{t:2,i:[{t:3}],s:"保存"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"提交"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"删除"}},add:{t:0,b:{t:2,i:[{t:3}],s:"添加"}},allMemberLevel:{t:0,b:{t:2,i:[{t:3}],s:"全部会员级别"}},noLimit:{t:0,b:{t:2,i:[{t:3}],s:"不限级别"}},allRoomType:{t:0,b:{t:2,i:[{t:3}],s:"全部集团房型"}},allDayRoom:{t:0,b:{t:2,i:[{t:3}],s:"全天房"}},hourlyRoom:{t:0,b:{t:2,i:[{t:3}],s:"钟点房"}},strategyNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"请输入策略名称"}},saveSuccess:{t:0,b:{t:2,i:[{t:3}],s:"新增成功"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"请选择级别"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"请选择房型"}},selectCheckInType:{t:0,b:{t:2,i:[{t:3}],s:"请选择入住类型"}},noLimitSource:{t:0,b:{t:2,i:[{t:3}],s:"不限来源"}},priceDiscount:{t:0,b:{t:2,i:[{t:3}],s:"房价折扣"}},priceReduction:{t:0,b:{t:2,i:[{t:3}],s:"房价立减"}},represents:{t:0,b:{t:2,i:[{t:3}],s:"0.88折代表88折"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"折"}},enterDiscountAmount:{t:0,b:{t:2,i:[{t:3}],s:"填写立减金额"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"元"}},to:{t:0,b:{t:2,i:[{t:3}],s:"至"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"结束日期"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"开始日期"}},sun:{t:0,b:{t:2,i:[{t:3}],s:"周日"}},wen:{t:0,b:{t:2,i:[{t:3}],s:"周一"}},tue:{t:0,b:{t:2,i:[{t:3}],s:"周二"}},wed:{t:0,b:{t:2,i:[{t:3}],s:"周三"}},thu:{t:0,b:{t:2,i:[{t:3}],s:"周四"}},fri:{t:0,b:{t:2,i:[{t:3}],s:"周五"}},sat:{t:0,b:{t:2,i:[{t:3}],s:"周六"}},channels:{t:0,b:{t:2,i:[{t:3}],s:"应用渠道"}},hotels:{t:0,b:{t:2,i:[{t:3}],s:"应用酒店"}},pleaseSelect:{t:0,b:{t:2,i:[{t:3}],s:"请选择"}},breakfastOption1:{t:0,b:{t:2,i:[{t:3}],s:"1份"}},breakfastOption2:{t:0,b:{t:2,i:[{t:3}],s:"2份"}},breakfastOption3:{t:0,b:{t:2,i:[{t:3}],s:"3份"}},breakfastOption4:{t:0,b:{t:2,i:[{t:3}],s:"4份"}},breakfastFeePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"多少元一份"}},strategyNamePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"请输入策略名称"}}},en:{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Info"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Name"}},rules:{t:0,b:{t:2,i:[{t:3}],s:"Rules"}},guestSrcType:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source Type"}},level:{t:0,b:{t:2,i:[{t:3}],s:"Level"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Type"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"Order Source"}},priceRights:{t:0,b:{t:2,i:[{t:3}],s:"Price Rights"}},discountType:{t:0,b:{t:2,i:[{t:3}],s:"Discount Type"}},priceMethod:{t:0,b:{t:2,i:[{t:3}],s:"Price Method"}},rightsType:{t:0,b:{t:2,i:[{t:3}],s:"Rights Type"}},effectiveDate:{t:0,b:{t:2,i:[{t:3}],s:"Effective Date"}},week:{t:0,b:{t:2,i:[{t:3}],s:"Week"}},applicationScope:{t:0,b:{t:2,i:[{t:3}],s:"Scope"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"Submit"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"Delete"}},add:{t:0,b:{t:2,i:[{t:3}],s:"Add"}},allMemberLevel:{t:0,b:{t:2,i:[{t:3}],s:"All Member Levels"}},noLimit:{t:0,b:{t:2,i:[{t:3}],s:"No Limit"}},allRoomType:{t:0,b:{t:2,i:[{t:3}],s:"All Group Room Types"}},allDayRoom:{t:0,b:{t:2,i:[{t:3}],s:"Full Day Room"}},hourlyRoom:{t:0,b:{t:2,i:[{t:3}],s:"Hourly Room"}},strategyNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"Strategy name is required"}},saveSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Added successfully"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"Please Select Level"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Please Select Room Type"}},selectCheckInType:{t:0,b:{t:2,i:[{t:3}],s:"Please Select Check-in Type"}},noLimitSource:{t:0,b:{t:2,i:[{t:3}],s:"Unlimited source"}},priceDiscount:{t:0,b:{t:2,i:[{t:3}],s:"Price Discount"}},priceReduction:{t:0,b:{t:2,i:[{t:3}],s:"Price Reduction"}},represents:{t:0,b:{t:2,i:[{t:3}],s:"0.88 represents an 88% discount"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"% discount"}},enterDiscountAmount:{t:0,b:{t:2,i:[{t:3}],s:"Enter the discount amount"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"$"}},to:{t:0,b:{t:2,i:[{t:3}],s:"To"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"End Date"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"Start Date"}},sun:{t:0,b:{t:2,i:[{t:3}],s:"Sun"}},wen:{t:0,b:{t:2,i:[{t:3}],s:"Wen"}},tue:{t:0,b:{t:2,i:[{t:3}],s:"Tue"}},wed:{t:0,b:{t:2,i:[{t:3}],s:"Wed"}},thu:{t:0,b:{t:2,i:[{t:3}],s:"Thu"}},fri:{t:0,b:{t:2,i:[{t:3}],s:"Fri"}},sat:{t:0,b:{t:2,i:[{t:3}],s:"Sat"}},channels:{t:0,b:{t:2,i:[{t:3}],s:"Channels"}},hotels:{t:0,b:{t:2,i:[{t:3}],s:"Hotels"}},pleaseSelect:{t:0,b:{t:2,i:[{t:3}],s:"Please Select"}},breakfastOption1:{t:0,b:{t:2,i:[{t:3}],s:"1 portion"}},breakfastOption2:{t:0,b:{t:2,i:[{t:3}],s:"2 portions"}},breakfastOption3:{t:0,b:{t:2,i:[{t:3}],s:"3 portions"}},breakfastOption4:{t:0,b:{t:2,i:[{t:3}],s:"4 portions"}},breakfastFeePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Price per portion"}},strategyNamePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Please enter strategy name"}}},km:{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានអំពីយុទ្ធសាស្ត្រ"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះយុទ្ធសាស្ត្រតម្លៃបន្ទប់"}},rules:{t:0,b:{t:2,i:[{t:3}],s:"ច្បាប់"}},guestSrcType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទអតិថិជន"}},level:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិត"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលស្នាក់នៅ"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពការកម្មង់"}},priceRights:{t:0,b:{t:2,i:[{t:3}],s:"សិទ្ធិតម្លៃ"}},discountType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទការបញ្ចុះតម្លៃ"}},priceMethod:{t:0,b:{t:2,i:[{t:3}],s:"វិធីសាស្ត្រតម្លៃ"}},rightsType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទសិទ្ធិ"}},effectiveDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទដែលមានប្រសិទ្ធភាព"}},week:{t:0,b:{t:2,i:[{t:3}],s:"សប្តាហ៍"}},applicationScope:{t:0,b:{t:2,i:[{t:3}],s:"វិសាលភាពនៃកម្មវិធី"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},save:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"ដាក់ស្នើ"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"លុប"}},add:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែម"}},allMemberLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតសមាជិកទាំងអស់"}},noLimit:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានកំណត់"}},allRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់ក្រុមទាំងអស់"}},allDayRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ពេញមួយថ្ងៃ"}},hourlyRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ម៉ោង"}},strategyNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ"}},saveSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានបន្ថែមដោយជោគជ័យ"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកម្រិត"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទបន្ទប់"}},selectCheckInType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទចូលស្នាក់នៅ"}},noLimitSource:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានកំណត់ប្រភព"}},priceDiscount:{t:0,b:{t:2,i:[{t:3}],s:"ការបញ្ចុះតម្លៃបន្ទប់"}},priceReduction:{t:0,b:{t:2,i:[{t:3}],s:"ការកាត់បន្ថយតម្លៃបន្ទប់"}},represents:{t:0,b:{t:2,i:[{t:3}],s:"0.88 តំណាងឱ្យការបញ្ចុះតម្លៃ 88%"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"ការបញ្ចុះតម្លៃ"}},enterDiscountAmount:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចូលចំនួនទឹកប្រាក់បញ្ចុះតម្លៃ"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"ដុល្លារ"}},to:{t:0,b:{t:2,i:[{t:3}],s:"ដល់"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទបញ្ចប់"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទចាប់ផ្តើម"}},sun:{t:0,b:{t:2,i:[{t:3}],s:"អាទិត្យ"}},wen:{t:0,b:{t:2,i:[{t:3}],s:"ច័ន្ទ"}},tue:{t:0,b:{t:2,i:[{t:3}],s:"អង្គារ"}},wed:{t:0,b:{t:2,i:[{t:3}],s:"ពុធ"}},thu:{t:0,b:{t:2,i:[{t:3}],s:"ព្រហស្បតិ៍"}},fri:{t:0,b:{t:2,i:[{t:3}],s:"សុក្រ"}},sat:{t:0,b:{t:2,i:[{t:3}],s:"សៅរ៍"}},channels:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},hotels:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារ"}},pleaseSelect:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើស"}},breakfastOption1:{t:0,b:{t:2,i:[{t:3}],s:"១ចំណិត"}},breakfastOption2:{t:0,b:{t:2,i:[{t:3}],s:"២ចំណិត"}},breakfastOption3:{t:0,b:{t:2,i:[{t:3}],s:"៣ចំណិត"}},breakfastOption4:{t:0,b:{t:2,i:[{t:3}],s:"៤ចំណិត"}},breakfastFeePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃក្នុងមួយចំណិត"}},strategyNamePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ"}}}}})}re(de);const pe=le(de,[["__scopeId","data-v-a9d1dc74"]]);export{pe as default};
//# sourceMappingURL=createStrategy-BSA1Ab9A.js.map
