import{d as e,aj as t,ai as a,B as o,b as l,D as s,y as r,o as i,e as n,w as u,f as m,h as p,Y as c,u as d,aq as b,c as g,g as h,F as y,ag as v,aM as f,R as F,p as N,m as T,aS as k,q as _,b5 as C,x as j,bt as w,ay as S,aT as R,a7 as V,av as P,n as x,t as z,bz as O,v as D}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                *//* empty css                  *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                          */import{a as G}from"./account.api-CSMEUacF.js";import{u as A}from"./usePagination-DYjsSSf4.js";import{_ as M}from"./_plugin-vue_export-helper-BCo6x5W8.js";const E=["element-loading-text"],Y={class:"filter-section"},U={class:"filter-wrapper"},$={class:"formula-text"},L={class:"arrears-formula"},q={class:"formula-note"},B={key:0,class:"no-data"},H={key:1,class:"table-container"},I={class:"type-title"},J={key:0,class:"sub-group-title"},K={key:2,class:"pagination"},Q=e({__name:"reminderDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(e,{emit:M}){const Q=e,W=M,{t:X}=t(),Z=a(),ee=o({get:()=>Q.modelValue,set:e=>W("update:modelValue",e)}),te=l(!1),ae=l([]),oe=l([]),le=l({prop:"",order:""}),se=l({types:["1","3","4"],rNo:"",isShowTogether:0}),re=[{label:X("singleOrder"),value:"1"},{label:X("joinedRoom"),value:"3"},{label:X("team"),value:"4"}],{pagination:ie,getParams:ne,onSizeChange:ue,onCurrentChange:me,onSortChange:pe}=A();const ce=o((()=>{const e={};return ae.value.forEach((t=>{const a=t.typeName||"未分类";e[a]||(e[a]={});let o="";o="2"===t.type?t.orderNo:"3"===t.type?t.bindCode:"4"===t.type?t.teamCode:"all",e[a][o]||(e[a][o]=[]),e[a][o].push(t)})),le.value.prop&&le.value.order&&Object.keys(e).forEach((t=>{Object.keys(e[t]).forEach((a=>{e[t][a]=function(e,t,a){if(!t||!a)return e;const o=[...e],l="ascending"===a;return o.sort(((e,a)=>{const o=e[t],s=a[t];return"number"==typeof o&&"number"==typeof s?l?o-s:s-o:"string"==typeof o&&"string"==typeof s?l?o.localeCompare(s):s.localeCompare(o):0})),o}(e[t][a],le.value.prop,le.value.order)}))})),e}));function de(e){const{columns:t,data:a}=e,o=[];return t.forEach(((e,t)=>{if(0===t)return void(o[t]="合计");const l=e.property;if(["consumeFee","payFee","preFee","addRoomFee","arrearsFee"].includes(l)){const e=a.reduce(((e,t)=>e+(Number(t[l])||0)),0);o[t]=`¥${fe(e)}`}else o[t]=""})),o}async function be(){var e,t;te.value=!0;try{const a={gcode:Z.gcode,hcode:Z.hcode,pageNo:ie.value.pageNo,pageSize:ie.value.pageSize};se.value.types.length>0&&se.value.types.length<3&&(a.types=se.value.types.join(",")),se.value.rNo.trim()&&(a.rNo=se.value.rNo.trim()),a.isShowTogether=se.value.isShowTogether;const o=await G.getReminderList(a);if(0===o.code){const a=(null==(e=o.data)?void 0:e.list)||o.data||[];ae.value=a,oe.value=[...a],ie.value.total=(null==(t=o.data)?void 0:t.total)||0}}catch(a){console.error("获取催押数据失败:",a)}finally{te.value=!1}}function ge(e){ie.value.pageSize=e,ie.value.pageNo=1,be()}function he(e){ie.value.pageNo=e,be()}function ye(){ie.value.pageNo=1,be()}function ve(){se.value.types=["1","3","4"],se.value.rNo="",se.value.isShowTogether=0,ie.value.pageNo=1,be()}function fe(e){return e.toFixed(2)}function Fe(e){return P(e).format("YYYY-MM-DD HH:mm")}function Ne({prop:e,order:t}){le.value.prop=e,le.value.order=t}return s((()=>Q.modelValue),(e=>{e&&(ie.value.pageNo=1,be())})),r((()=>{Q.modelValue&&be()})),(e,t)=>{const a=x,o=N,l=T,s=k,r=_,P=C,G=j,A=z,M=O,Q=D,W=w,Z=S,oe=R;return i(),n(Z,{modelValue:ee.value,"onUpdate:modelValue":t[4]||(t[4]=e=>ee.value=e),title:d(X)("title"),width:"80%","close-on-click-modal":!1,"destroy-on-close":"",class:"reminder-dialog"},{footer:u((()=>[m(r,{onClick:t[3]||(t[3]=e=>ee.value=!1)},{default:u((()=>[p(c(d(X)("close")),1)])),_:1})])),default:u((()=>[b((i(),g("div",{"element-loading-text":d(X)("loading"),class:"dialog-content"},[h("div",Y,[h("div",U,[m(G,{model:se.value,inline:"",class:"filter-form"},{default:u((()=>[m(l,{label:d(X)("filterType")},{default:u((()=>[m(o,{modelValue:se.value.types,"onUpdate:modelValue":t[0]||(t[0]=e=>se.value.types=e),class:"type-checkboxes"},{default:u((()=>[(i(),g(y,null,v(re,(e=>m(a,{key:e.value,label:e.value},{default:u((()=>[p(c(e.label),1)])),_:2},1032,["label"]))),64))])),_:1},8,["modelValue"])])),_:1},8,["label"]),m(l,{label:d(X)("roomNoSearch")},{default:u((()=>[m(s,{modelValue:se.value.rNo,"onUpdate:modelValue":t[1]||(t[1]=e=>se.value.rNo=e),placeholder:d(X)("roomNoPlaceholder"),clearable:"",style:{width:"150px"},onKeyup:f(ye,["enter"])},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(l,null,{default:u((()=>[m(r,{type:"primary",onClick:ye},{default:u((()=>[p(c(d(X)("search")),1)])),_:1}),m(r,{onClick:ve},{default:u((()=>[p(c(d(X)("reset")),1)])),_:1})])),_:1}),m(l,{label:d(X)("showTogether")},{default:u((()=>[m(P,{modelValue:se.value.isShowTogether,"onUpdate:modelValue":t[2]||(t[2]=e=>se.value.isShowTogether=e),"active-value":1,"inactive-value":0,onChange:ye},null,8,["modelValue"])])),_:1},8,["label"])])),_:1},8,["model"]),h("div",$,[h("div",L,c(d(X)("arrearsFormula")),1),h("div",q,c(d(X)("addRoomFeeNote")),1)])])]),te.value||0!==ae.value.length?(i(),g("div",H,[(i(!0),g(y,null,v(ce.value,((e,t)=>(i(),g("div",{key:t,class:"type-section"},[h("h2",I,c(t),1),(i(!0),g(y,null,v(e,((t,a)=>{return i(),g("div",{key:a,class:"sub-group-section"},[Object.keys(e).length>1?(i(),g("h4",J,c((o=t[0],"2"===o.type?`订单号: ${o.orderNo}`:"3"===o.type?`联房组: ${o.bindCode}`:"4"===o.type?`团队: ${o.teamCode}`:"")),1)):F("",!0),m(Q,{data:t,stripe:"","show-summary":"",border:"","summary-method":de,onSortChange:Ne},{default:u((()=>[m(A,{label:d(X)("roomNo")},{default:u((({row:e})=>[h("span",{class:V({"text-red font-bold":e.arrearsFee>0})},c(e.rNo),3)])),_:1},8,["label"]),m(A,{label:d(X)("guestName")},{default:u((({row:e})=>[p(c(e.name)+" ",1),1!==se.value.isShowTogether||1!==e.isMain&&"1"!==e.isMain?F("",!0):(i(),n(M,{key:0,type:"primary",size:"small",class:"main-guest-tag"},{default:u((()=>[p(c(d(X)("mainGuest")),1)])),_:1}))])),_:1},8,["label"]),m(A,{label:d(X)("checkinTime")},{default:u((({row:e})=>[p(c(Fe(e.checkinTime)),1)])),_:1},8,["label"]),m(A,{label:d(X)("planCheckoutTime")},{default:u((({row:e})=>[p(c(Fe(e.planCheckoutTime)),1)])),_:1},8,["label"]),m(A,{prop:"checkinTypeName",label:d(X)("checkinType")},null,8,["label"]),m(A,{prop:"guestSrcTypeName",label:d(X)("guestSrcType")},null,8,["label"]),m(A,{prop:"levelOrCompanyName",label:d(X)("levelOrCompany")},null,8,["label"]),m(A,{prop:"price",label:d(X)("roomPrice"),align:"right",sortable:"custom"},{default:u((({row:e})=>[p(" ¥"+c(fe(e.price)),1)])),_:1},8,["label"]),m(A,{prop:"consumeFee",label:d(X)("consumeFee"),align:"right",sortable:"custom"},{default:u((({row:e})=>[p(" ¥"+c(fe(e.consumeFee)),1)])),_:1},8,["label"]),m(A,{prop:"payFee",label:d(X)("payFee"),align:"right",sortable:"custom"},{default:u((({row:e})=>[p(" ¥"+c(fe(e.payFee)),1)])),_:1},8,["label"]),m(A,{prop:"preFee",label:d(X)("preFee"),align:"right",sortable:"custom"},{default:u((({row:e})=>[p(" ¥"+c(fe(e.preFee)),1)])),_:1},8,["label"]),m(A,{prop:"addRoomFee",label:d(X)("addRoomFee"),align:"right",sortable:"custom"},{default:u((({row:e})=>[p(" ¥"+c(fe(e.addRoomFee)),1)])),_:1},8,["label"]),m(A,{prop:"arrearsFee",label:d(X)("arrearsFee"),align:"right",sortable:"custom"},{default:u((({row:e})=>[h("span",{class:V({"text-red font-bold":e.arrearsFee>0})}," ¥"+c(fe(e.arrearsFee)),3)])),_:1},8,["label"])])),_:2},1032,["data"])]);var o})),128))])))),128))])):(i(),g("div",B,c(d(X)("noData")),1)),d(ie).total>0?(i(),g("div",K,[m(W,{"current-page":d(ie).pageNo,total:d(ie).total,"page-size":d(ie).pageSize,"page-sizes":d(ie).sizes,layout:d(ie).layout,"hide-on-single-page":!1,background:"",onSizeChange:ge,onCurrentChange:he},null,8,["current-page","total","page-size","page-sizes","layout"])])):F("",!0)],8,E)),[[oe,te.value]])])),_:1},8,["modelValue","title"])}}});function W(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{title:{t:0,b:{t:2,i:[{t:3}],s:"Payment Reminder"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"Guest Name"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"Room No"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Time"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Plan Checkout Time"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Type"}},guestSrcType:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source Type"}},levelOrCompany:{t:0,b:{t:2,i:[{t:3}],s:"Level/Company"}},roomPrice:{t:0,b:{t:2,i:[{t:3}],s:"Room Price"}},consumeFee:{t:0,b:{t:2,i:[{t:3}],s:"Consume Fee"}},payFee:{t:0,b:{t:2,i:[{t:3}],s:"Pay Fee"}},preFee:{t:0,b:{t:2,i:[{t:3}],s:"Pre-auth Amount"}},addRoomFee:{t:0,b:{t:2,i:[{t:3}],s:"Add Room Fee"}},arrearsFee:{t:0,b:{t:2,i:[{t:3}],s:"Arrears Fee"}},close:{t:0,b:{t:2,i:[{t:3}],s:"Close"}},loading:{t:0,b:{t:2,i:[{t:3}],s:"Loading..."}},noData:{t:0,b:{t:2,i:[{t:3}],s:"No data"}},filterType:{t:0,b:{t:2,i:[{t:3}],s:"Filter Type"}},singleOrder:{t:0,b:{t:2,i:[{t:3}],s:"Single Order"}},joinedRoom:{t:0,b:{t:2,i:[{t:3}],s:"Joined Room"}},team:{t:0,b:{t:2,i:[{t:3}],s:"Team"}},roomNoSearch:{t:0,b:{t:2,i:[{t:3}],s:"Room No Search"}},roomNoPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Enter room number"}},search:{t:0,b:{t:2,i:[{t:3}],s:"Search"}},reset:{t:0,b:{t:2,i:[{t:3}],s:"Reset"}},showTogether:{t:0,b:{t:2,i:[{t:3}],s:"Show Together Guests"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"Main"}},arrearsFormula:{t:0,b:{t:2,i:[{t:3}],s:"Arrears = Consume Fee + Add Room Fee - Pre-auth Amount - Pay Fee"}},addRoomFeeNote:{t:0,b:{t:2,i:[{t:3}],s:"Add Room Fee: Full day room fee for overnight stays on the same day"}}},"zh-cn":{title:{t:0,b:{t:2,i:[{t:3}],s:"催押"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"客人姓名"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"入住时间"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"预离时间"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},guestSrcType:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},levelOrCompany:{t:0,b:{t:2,i:[{t:3}],s:"会员级别/公司"}},roomPrice:{t:0,b:{t:2,i:[{t:3}],s:"房价"}},consumeFee:{t:0,b:{t:2,i:[{t:3}],s:"消费合计"}},payFee:{t:0,b:{t:2,i:[{t:3}],s:"付款合计"}},preFee:{t:0,b:{t:2,i:[{t:3}],s:"预授权金额"}},addRoomFee:{t:0,b:{t:2,i:[{t:3}],s:"需加收房费"}},arrearsFee:{t:0,b:{t:2,i:[{t:3}],s:"预计欠费"}},close:{t:0,b:{t:2,i:[{t:3}],s:"关闭"}},loading:{t:0,b:{t:2,i:[{t:3}],s:"加载中..."}},noData:{t:0,b:{t:2,i:[{t:3}],s:"暂无数据"}},filterType:{t:0,b:{t:2,i:[{t:3}],s:"筛选类型"}},singleOrder:{t:0,b:{t:2,i:[{t:3}],s:"单订单"}},joinedRoom:{t:0,b:{t:2,i:[{t:3}],s:"联房"}},team:{t:0,b:{t:2,i:[{t:3}],s:"团队"}},roomNoSearch:{t:0,b:{t:2,i:[{t:3}],s:"房号查询"}},roomNoPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"请输入房号"}},search:{t:0,b:{t:2,i:[{t:3}],s:"查询"}},reset:{t:0,b:{t:2,i:[{t:3}],s:"重置"}},showTogether:{t:0,b:{t:2,i:[{t:3}],s:"显示同住客人"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"主客"}},arrearsFormula:{t:0,b:{t:2,i:[{t:3}],s:"预计欠款=消费总计+需加收房费－预授权金额－付款总计"}},addRoomFeeNote:{t:0,b:{t:2,i:[{t:3}],s:"需加收房费：当天过夜要加收全天的房费"}}},km:{title:{t:0,b:{t:2,i:[{t:3}],s:"រំលឹកការបង់ប្រាក់"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះភ្ញៀវ"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចូលស្នាក់នៅ"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាគ្រោងចាកចេញ"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលស្នាក់នៅ"}},guestSrcType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទប្រភពភ្ញៀវ"}},levelOrCompany:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិត/ក្រុមហ៊ុន"}},roomPrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃបន្ទប់"}},consumeFee:{t:0,b:{t:2,i:[{t:3}],s:"ថ្លៃប្រើប្រាស់សរុប"}},payFee:{t:0,b:{t:2,i:[{t:3}],s:"ថ្លៃបង់សរុប"}},preFee:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនទឹកប្រាក់អនុញ្ញាតមុន"}},addRoomFee:{t:0,b:{t:2,i:[{t:3}],s:"ថ្លៃបន្ទប់បន្ថែម"}},arrearsFee:{t:0,b:{t:2,i:[{t:3}],s:"ថ្លៃជំពាក់គ្រោង"}},close:{t:0,b:{t:2,i:[{t:3}],s:"បិទ"}},loading:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងផ្ទុក..."}},noData:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានទិន្នន័យ"}},filterType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទតម្រង"}},singleOrder:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ជាទិញតែមួយ"}},joinedRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ភ្ជាប់"}},team:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុម"}},roomNoSearch:{t:0,b:{t:2,i:[{t:3}],s:"ស្វែងរកលេខបន្ទប់"}},roomNoPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចូលលេខបន្ទប់"}},search:{t:0,b:{t:2,i:[{t:3}],s:"ស្វែងរក"}},reset:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់ឡើងវិញ"}},showTogether:{t:0,b:{t:2,i:[{t:3}],s:"បង្ហាញភ្ញៀវស្នាក់នៅជាមួយ"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវសំខាន់"}},arrearsFormula:{t:0,b:{t:2,i:[{t:3}],s:"ថ្លៃជំពាក់គ្រោង = ថ្លៃប្រើប្រាស់សរុប + ថ្លៃបន្ទប់បន្ថែម - ចំនួនទឹកប្រាក់អនុញ្ញាតមុន - ថ្លៃបង់សរុប"}},addRoomFeeNote:{t:0,b:{t:2,i:[{t:3}],s:"ថ្លៃបន្ទប់បន្ថែម៖ ថ្លៃបន្ទប់ពេញមួយថ្ងៃសម្រាប់ការស្នាក់នៅពេញមួយយប់នៅថ្ងៃដដែល"}}}}})}W(Q);const X=M(Q,[["__scopeId","data-v-1e17634d"]]);export{X as default};
//# sourceMappingURL=reminderDialog-BDj_z1aE.js.map
