{"version": 3, "file": "roomClean-Bunys1gF.js", "sources": ["../../src/views/room/realtime/components/roomClean.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"selectCleaner\": \"Please select a cleaner\",\r\n    \"noCleanerFound\": \"No cleaners found. Please add members in: Store -> Member Roles.\",\r\n    \"cleanRoomSuccess\": \"Room \\\"{rNo}\\\" cleaned successfully\",\r\n    \"cleanRoomTitle\": \"Clean Room: {rNo}\",\r\n    \"cleanerLabel\": \"Cleaner\",\r\n    \"remarkLabel\": \"Remark\",\r\n    \"remarkPlaceholder\": \"Please enter remarks\",\r\n    \"cancel\": \"Cancel\",\r\n    \"submit\": \"Submit\",\r\n    \"selectCleanerPlaceholder\": \"Please select a cleaner\",\r\n    \"cleanerRequired\": \"Please select a cleaner\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"selectCleaner\": \"请选择清洁工\",\r\n    \"noCleanerFound\": \"没有找到任何清洁工，请先到:门店->成员角色 添加\",\r\n    \"cleanRoomSuccess\": \"房间「{rNo}」置干净成功\",\r\n    \"cleanRoomTitle\": \"置干净：{rNo}\",\r\n    \"cleanerLabel\": \"清洁工\",\r\n    \"remarkLabel\": \"备注\",\r\n    \"remarkPlaceholder\": \"请输入备注\",\r\n    \"cancel\": \"取消\",\r\n    \"submit\": \"提交\",\r\n    \"selectCleanerPlaceholder\": \"请选择清洁工\",\r\n    \"cleanerRequired\": \"请选择清洁工\"\r\n  },\r\n  \"km\": {\r\n    \"selectCleaner\": \"សូមជ្រើសរើសអ្នកសម្អាត\",\r\n    \"noCleanerFound\": \"រកមិនឃើញអ្នកសម្អាតទេ។ សូមបន្ថែមសមាជិកនៅ: ហាង -> តួនាទីសមាជិក។\",\r\n    \"cleanRoomSuccess\": \"បន្ទប់ \\\"{rNo}\\\" ត្រូវបានសម្អាតដោយជោគជ័យ\",\r\n    \"cleanRoomTitle\": \"សម្អាតបន្ទប់: {rNo}\",\r\n    \"cleanerLabel\": \"អ្នកសម្អាត\",\r\n    \"remarkLabel\": \"កំណត់ចំណាំ\",\r\n    \"remarkPlaceholder\": \"សូមបញ្ចូលកំណត់ចំណាំ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"submit\": \"ដាក់ស្នើ\",\r\n    \"selectCleanerPlaceholder\": \"សូមជ្រើសរើសអ្នកសម្អាត\",\r\n    \"cleanerRequired\": \"សូមជ្រើសរើសអ្នកសម្អាត\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { roomApi, userApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rCode: string\r\n    rNo: string\r\n  }>(),\r\n  {\r\n    rCode: '',\r\n    rNo: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\n\r\nconst formRef = ref<FormInstance>()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  rCode: props.rCode,\r\n  cleaner: '',\r\n  remark: '',\r\n})\r\nconst formRules = ref<FormRules>({\r\n  cleaner: [{ required: true, message: t('cleanerRequired'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  // 获取清洁工列表\r\n  getCleanerList()\r\n})\r\n// 清洁工列表\r\nconst cleanerList = ref<{ username: string; nickname: string }[]>([])\r\nfunction getCleanerList() {\r\n  userApi.getCleanerList({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      cleanerList.value = res.data\r\n      if (cleanerList.value.length === 0) {\r\n        ElMessage.error(t('noCleanerFound'))\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        roomApi.turnClean(form.value).then((res: any) => {\r\n          if (res.code !== 0) {\r\n            ElMessage.error(res.msg)\r\n            return\r\n          }\r\n          ElMessage.success({\r\n            message: t('cleanRoomSuccess', { rNo: props.rNo }),\r\n            type: 'success',\r\n            center: true,\r\n          })\r\n          emits('success', {\r\n            rCode: props.rCode,\r\n            rNo: props.rNo,\r\n            state: res.data.state,\r\n          })\r\n          onCancel()\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('cleanRoomTitle', { rNo: props.rNo })\" width=\"500px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"100px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('cleanerLabel')\">\r\n        <el-select v-model=\"form.cleaner\" :placeholder=\"t('selectCleanerPlaceholder')\">\r\n          <el-option v-for=\"item in cleanerList\" :key=\"item.username\" :label=\"item.nickname\" :value=\"item.username\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('remarkLabel')\">\r\n        <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('remarkPlaceholder')\" maxlength=\"255\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('submit') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "form", "gcode", "hcode", "rCode", "cleaner", "remark", "formRules", "required", "message", "trigger", "onMounted", "userApi", "getCleanerList", "then", "res", "code", "cleanerList", "value", "data", "length", "ElMessage", "error", "onSubmit", "validate", "valid", "roomApi", "turnClean", "success", "rNo", "type", "center", "state", "onCancel", "msg"], "mappings": "q0BAiDA,MAAMA,EAAQC,EAYRC,EAAQC,GAKRC,EAAEA,GAAMC,IAERC,EAAYC,IAEZC,EAAUC,IACVC,EAAYC,EAAS,CACzBC,IAAM,IACGZ,EAAMa,WAEf,GAAAC,CAAIC,GACFb,EAAM,oBAAqBa,EAAG,IAG5BC,EAAOP,EAAI,CACfQ,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBC,MAAOnB,EAAMmB,MACbC,QAAS,GACTC,OAAQ,KAEJC,EAAYb,EAAe,CAC/BW,QAAS,CAAC,CAAEG,UAAU,EAAMC,QAASpB,EAAE,mBAAoBqB,QAAS,WAGtEC,GAAU,KAORC,EAAQC,eAAe,CAAEX,MAAOX,EAAUW,MAAOC,MAAOZ,EAAUY,QAASW,MAAMC,IAC9D,IAAbA,EAAIC,OACNC,EAAYC,MAAQH,EAAII,KACS,IAA7BF,EAAYC,MAAME,QACVC,EAAAC,MAAMjC,EAAE,mBACpB,GAVW,IAGX,MAAA4B,EAAcvB,EAA8C,IAYlE,SAAS6B,IACP9B,EAAQyB,OACNzB,EAAQyB,MAAMM,UAAUC,IAClBA,GACFC,EAAQC,UAAU1B,EAAKiB,OAAOJ,MAAMC,IACjB,IAAbA,EAAIC,MAIRK,EAAUO,QAAQ,CAChBnB,QAASpB,EAAE,mBAAoB,CAAEwC,IAAK5C,EAAM4C,MAC5CC,KAAM,UACNC,QAAQ,IAEV5C,EAAM,UAAW,CACfiB,MAAOnB,EAAMmB,MACbyB,IAAK5C,EAAM4C,IACXG,MAAOjB,EAAII,KAAKa,QAETC,KAbGZ,EAAAC,MAAMP,EAAImB,IAab,GACV,GAEJ,CAGL,SAASD,IACPtC,EAAUuB,OAAQ,CAAA"}