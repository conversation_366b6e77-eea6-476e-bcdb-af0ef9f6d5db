import{a as e}from"./index-CkEhI1Zk.js";const t="admin-api/pms/general-config",a={list:a=>e.get(`${t}/list`,{params:a}),getGeneralConfigList:(a,o)=>e.get(`${t}/list`,{params:{gcode:a,type:o}}),getPayAccountList:a=>e.get(`${t}/list-pay-account`,{params:{gcode:a}}),getConsumeAccountList:a=>e.get(`${t}/list-consume-account`,{params:{gcode:a}}),updateGeneralConfig:a=>e.put(`${t}/update`,a,{}),updateGeneralConfigReasonStatus:a=>e.put(`${t}/update-reason-status`,a,{}),updateGeneralConfigPayAccountStatus:a=>e.put(`${t}/update-pay-account-status`,a,{}),updateGeneralConfigConsumeAccountStatus:a=>e.put(`${t}/update-consume-account-status`,a,{}),getGeneralConfig:a=>e.get(`${t}/get`,{params:a}),updateDefaultPlanCheckinTime:a=>e.put(`${t}/update-default-plan-checkin-time`,a,{}),updateGeneralConfigReason:a=>e.put(`${t}/update-reason`,a,{}),createGeneralConfigReason:a=>e.post(`${t}/create-reason`,a,{}),createGeneralConfig:a=>e.post(`${t}/create`,a,{}),createGeneralConfigRoomFeature:a=>e.post(`${t}/create-room-feature`,a,{}),updateGeneralConfigRoomFeature:a=>e.put(`${t}/update-room-feature`,a,{}),updateGeneralConfigRoomFeatureStatus:a=>e.put(`${t}/update-room-feature-status`,a,{}),getBizData:a=>e.get(`${t}/get-biz-date`,{params:a}),getCheckOutTime:a=>e.get(`${t}/get-check-out-time`,{params:a}),getHowMinuteConfig:a=>e.get(`${t}/get-how-minute`,{params:a})};export{a as g};
//# sourceMappingURL=generalConfig.api-CEBBd8kx.js.map
