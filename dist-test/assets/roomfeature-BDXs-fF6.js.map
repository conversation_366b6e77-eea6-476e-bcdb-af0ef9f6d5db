{"version": 3, "file": "roomfeature-BDXs-fF6.js", "sources": ["../../src/views/merchant/system/config/components/roomfeature.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"serviceFlag\": \"Service Flag\",\r\n    \"code\": \"Code\",\r\n    \"name\": \"Feature\",\r\n    \"remark\": \"Remark\",\r\n    \"newAdd\": \"Add\",\r\n    \"edit\": \"Edit\",\r\n    \"save\": \"Save\",\r\n    \"cancel\": \"Cancel\",\r\n    \"hotelInfoTip\": \"Tip: Used to select room features in the room\",\r\n    \"placeholderName\": \"Please enter name\",\r\n    \"placeholderRemark\": \"Please enter remark\",\r\n    \"operation\": \"Actions\",\r\n\t\t\"saveSuccess\": \"Save Successful.\",\r\n\t\t\"editSuccess\": \"Update Successful.\",\r\n\t\t\"enable\": \"Enable\",\r\n\t\t\"disable\": \"Disable\",\r\n\t\t\"status\": \"Status\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"serviceFlag\": \"服务标识\",\r\n    \"code\": \"编码\",\r\n    \"name\": \"特征\",\r\n    \"remark\": \"备注\",\r\n    \"newAdd\": \"新增\",\r\n    \"edit\": \"编辑\",\r\n    \"save\": \"保存\",\r\n    \"cancel\": \"取消\",\r\n    \"hotelInfoTip\": \"提示:用于在房间中选择房间特征\",\r\n    \"placeholderName\": \"请输入特征\",\r\n    \"placeholderRemark\": \"请输入备注\",\r\n    \"operation\": \"操作\",\r\n\t\t\"saveSuccess\": \"保存成功\",\r\n\t\t\"editSuccess\": \"修改成功\",\r\n\t\t\"enable\": \"启用\",\r\n\t\t\"disable\": \"停用\",\r\n\t\t\"status\": \"状态\"\r\n  },\r\n  \"km\": {\r\n    \"serviceFlag\": \"សញ្ញាសេវាកម្ម\",\r\n    \"code\": \"កូដ\",\r\n    \"name\": \"លក្ខណៈពិសេស\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"newAdd\": \"បន្ថែមថ្មី\",\r\n    \"edit\": \"កែសម្រួល\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"hotelInfoTip\": \"ព័ត៌មានសំខាន់៖ ប្រើសម្រាប់ជ្រើសរើសលក្ខណៈពិសេសនៃបន្ទប់\",\r\n    \"placeholderName\": \"សូមបញ្ចូលឈ្មោះ\",\r\n    \"placeholderRemark\": \"សូមបញ្ចូលចំណាំ\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"saveSuccess\": \"រក្សាទុកដោយជោគជ័យ\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\",\r\n    \"enable\": \"បើក\",\r\n    \"disable\": \"បិទ\",\r\n    \"status\": \"ស្ថានភាព\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { GeneralConfigModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { generalConfigApi } from '@/api/modules/index'\r\nimport { DictTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  id: 0,\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  name: '',\r\n  type: DictTypeEnum.ROOM_FEATURE,\r\n  remark: '',\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  name: [{ required: true, message: t('placeholderName'), trigger: 'blur' }],\r\n})\r\nconst formInline = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  name: '',\r\n  type: DictTypeEnum.ROOM_FEATURE,\r\n  remark: '',\r\n})\r\nconst dataList = ref<GeneralConfigModel[]>([])\r\n\r\nconst type = ref(DictTypeEnum.ROOM_FEATURE)\r\n\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  generalConfigApi.list({ gcode: userStore.gcode, hcode: userStore.hcode, type: type.value }).then((res: any) => {\r\n    loading.value = false\r\n    dataList.value = res.data\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  return new Promise<void>((resolve) => {\r\n    formRef.value &&\r\n      formRef.value.validate((valid) => {\r\n        if (valid) {\r\n          generalConfigApi.createGeneralConfigRoomFeature(formInline.value).then((res: any) => {\r\n            if (res.code === 0) {\r\n              ElMessage({\r\n                message: t('saveSuccess'),\r\n                type: 'success',\r\n              })\r\n              getInfo()\r\n            }\r\n            resolve()\r\n          })\r\n        }\r\n      })\r\n  })\r\n}\r\n\r\nfunction onEdit(item: any) {\r\n  form.value = item\r\n  return new Promise<void>((resolve) => {\r\n    generalConfigApi.updateGeneralConfigRoomFeature(form.value).then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success({\r\n          message: t('editSuccess'),\r\n          center: true,\r\n        })\r\n        item.isEdit = false\r\n      } else {\r\n        ElMessage.error({\r\n          message: res.msg,\r\n          center: true,\r\n        })\r\n      }\r\n      resolve()\r\n    })\r\n  })\r\n}\r\n\r\nfunction onChangeStatus(row: any) {\r\n  return new Promise<boolean>((resolve) => {\r\n    row.statusLoading = true\r\n    generalConfigApi\r\n      .updateGeneralConfigRoomFeatureStatus({\r\n        id: row.id,\r\n        isEnable: row.isEnable === '0' ? '1' : '0',\r\n      })\r\n      .then((res: any) => {\r\n        row.statusLoading = false\r\n        if (res.code === 0) {\r\n          ElMessage.success({\r\n            message: `${row.state === '0' ? '启用' : '停用'}成功`,\r\n            center: true,\r\n          })\r\n          getInfo()\r\n        } else {\r\n          ElMessage.error({\r\n            message: res.msg,\r\n            center: true,\r\n          })\r\n        }\r\n        return resolve(true)\r\n      })\r\n      .catch(() => {\r\n        row.statusLoading = false\r\n        return resolve(false)\r\n      })\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-container>\r\n    <el-main>\r\n      <div>\r\n        <el-form v-if=\"type === DictTypeEnum.ROOM_FEATURE\" ref=\"formRef\" :inline=\"true\" :rules=\"formRules\" :model=\"formInline\" class=\"demo-form-inline\">\r\n          <el-form-item :label=\"t('name')\" prop=\"name\">\r\n            <el-input v-model=\"formInline.name\" :placeholder=\"t('placeholderName')\" clearable maxlength=\"30\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('remark')\">\r\n            <el-input v-model=\"formInline.remark\" :placeholder=\"t('placeholderRemark')\" clearable style=\"width: 400px\" maxlength=\"50\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"onSubmit\">\r\n              {{ t('newAdd') }}\r\n            </el-button>\r\n            <!-- 使用国际化字符串 -->\r\n          </el-form-item>\r\n        </el-form>\r\n        <div class=\"config-info\">\r\n          {{ t('hotelInfoTip') }}\r\n          <!-- 使用国际化字符串 -->\r\n        </div>\r\n        <el-table v-loading=\"loading\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" class=\"list-table\" :data=\"dataList\" height=\"100%\">\r\n          <el-table-column :label=\"t('name')\" align=\"left\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.name\" maxlength=\"30\" />\r\n              <span v-else>{{ scope.row.name }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('status')\">\r\n            <template #default=\"scope\">\r\n              <el-switch v-model=\"scope.row.isEnable\" :loading=\"scope.row.statusLoading\" inline-prompt :active-text=\"t('enable')\" active-value=\"1\" inactive-value=\"0\" :inactive-text=\"t('disable')\" :before-change=\"() => onChangeStatus(scope.row)\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('remark')\" align=\"left\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.remark\" maxlength=\"50\" />\r\n              <span v-else>{{ scope.row.remark }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('operation')\" align=\"left\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <template v-if=\"scope.row.isEdit\">\r\n                <el-button type=\"primary\" plain size=\"small\" @click=\"onEdit(scope.row)\">\r\n                  {{ t('save') }}\r\n                  <!-- 使用国际化字符串 -->\r\n                </el-button>\r\n                <el-button plain size=\"small\" @click=\"scope.row.isEdit = false\">\r\n                  {{ t('cancel') }}\r\n                  <!-- 使用国际化字符串 -->\r\n                </el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"primary\" plain size=\"small\" @click=\"scope.row.isEdit = true\">\r\n                  {{ t('edit') }}\r\n                  <!-- 使用国际化字符串 -->\r\n                </el-button>\r\n              </template>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-main>\r\n  </el-container>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-menu-item.is-active) {\r\n  color: #0488fb !important;\r\n  background-color: #e7effb !important;\r\n}\r\n\r\n.el-main.nopadding {\r\n  padding: 0;\r\n  background: #fff;\r\n}\r\n\r\n.config-info {\r\n  margin-bottom: 20px;\r\n  color: red;\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "formRef", "form", "id", "gcode", "hcode", "name", "type", "DictTypeEnum", "ROOM_FEATURE", "remark", "formRules", "required", "message", "trigger", "formInline", "dataList", "getInfo", "value", "generalConfigApi", "list", "then", "res", "data", "onSubmit", "Promise", "resolve", "validate", "valid", "createGeneralConfigRoomFeature", "code", "ElMessage", "onMounted", "row", "statusLoading", "updateGeneralConfigRoomFeatureStatus", "isEnable", "success", "state", "center", "error", "msg", "catch", "item", "updateGeneralConfigRoomFeature", "isEdit"], "mappings": "06BAqEA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,GAAI,EACJC,MAAOT,EAAUS,MACjBC,MAAOV,EAAUU,MACjBC,KAAM,GACNC,KAAMC,EAAaC,aACnBC,OAAQ,KAGJC,EAAYX,EAAe,CAC/BM,KAAM,CAAC,CAAEM,UAAU,EAAMC,QAAShB,EAAE,mBAAoBiB,QAAS,WAE7DC,EAAaf,EAAI,CACrBI,MAAOT,EAAUS,MACjBC,MAAOV,EAAUU,MACjBC,KAAM,GACNC,KAAMC,EAAaC,aACnBC,OAAQ,KAEJM,EAAWhB,EAA0B,IAErCO,EAAOP,EAAIQ,EAAaC,cAM9B,SAASQ,IACPlB,EAAQmB,OAAQ,EAChBC,EAAiBC,KAAK,CAAEhB,MAAOT,EAAUS,MAAOC,MAAOV,EAAUU,MAAOE,KAAMA,EAAKW,QAASG,MAAMC,IAChGvB,EAAQmB,OAAQ,EAChBF,EAASE,MAAQI,EAAIC,IAAA,GACtB,CAGH,SAASC,IACA,OAAA,IAAIC,SAAeC,IACxBzB,EAAQiB,OACNjB,EAAQiB,MAAMS,UAAUC,IAClBA,GACFT,EAAiBU,+BAA+Bd,EAAWG,OAAOG,MAAMC,IACrD,IAAbA,EAAIQ,OACIC,EAAA,CACRlB,QAAShB,EAAE,eACXU,KAAM,YAEAU,KAEFS,GAAA,GACT,GAEJ,GACJ,QA7BHM,GAAU,KACAf,GAAA,0oDAoDcgB,QACf,IAAIR,SAAkBC,IAC3BO,EAAIC,eAAgB,EACpBf,EACGgB,qCAAqC,CACpChC,GAAI8B,EAAI9B,GACRiC,SAA2B,MAAjBH,EAAIG,SAAmB,IAAM,MAExCf,MAAMC,IACLW,EAAIC,eAAgB,EACH,IAAbZ,EAAIQ,MACNC,EAAUM,QAAQ,CAChBxB,SAA0B,MAAdoB,EAAIK,MAAgB,KAAO,MAA9B,KACTC,QAAQ,IAEFtB,KAERc,EAAUS,MAAM,CACd3B,QAASS,EAAImB,IACbF,QAAQ,IAGLb,GAAQ,MAEhBgB,OAAM,KACLT,EAAIC,eAAgB,EACbR,GAAQ,KAChB,IA3BP,IAAwBO,+iBArBRU,QACdzC,EAAKgB,MAAQyB,EACN,IAAIlB,SAAeC,IACxBP,EAAiByB,+BAA+B1C,EAAKgB,OAAOG,MAAMC,IAC/C,IAAbA,EAAIQ,MACNC,EAAUM,QAAQ,CAChBxB,QAAShB,EAAE,eACX0C,QAAQ,IAEVI,EAAKE,QAAS,GAEdd,EAAUS,MAAM,CACd3B,QAASS,EAAImB,IACbF,QAAQ,IAGJb,GAAA,GACT,IAjBL,IAAgBiB"}