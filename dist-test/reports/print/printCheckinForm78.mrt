<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <printCheckinForm Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>printCheckinForm</Alias>
        <HeadersString />
        <Key />
        <Name>printCheckinForm</Name>
        <PathData>D:\11\2\aflower-pms-front\public\reports\print\printCheckinForm.json</PathData>
      </printCheckinForm>
    </Databases>
    <DataSources isList="true" count="2">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="33">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>hname,System.String</value>
          <value>orderNo,System.String</value>
          <value>printDate,System.String</value>
          <value>buildingName,System.String</value>
          <value>floorName,System.String</value>
          <value>rtName,System.String</value>
          <value>firstDayFee,System.Decimal</value>
          <value>roomBkNum,System.Decimal</value>
          <value>bkNum,System.Decimal</value>
          <value>name,System.String</value>
          <value>nation,System.String</value>
          <value>birthday,System.String</value>
          <value>idType,System.String</value>
          <value>idTypeName,System.String</value>
          <value>idNo,System.String</value>
          <value>sex,System.String</value>
          <value>checkinTime,System.String</value>
          <value>checkoutTime,System.String</value>
          <value>nights,System.Decimal</value>
          <value>phone,System.String</value>
          <value>userAddress,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>isBreakfast,System.String</value>
          <value>frontPhone,System.String</value>
          <value>address,System.String</value>
          <value>remark,System.String</value>
          <value>togetherRespVOList,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>c02b7fccff9f4c399ab5416ca341efa4</Key>
        <Name>root</Name>
        <NameInSource>printCheckinForm.root</NameInSource>
      </root>
      <root_togetherRespVOList Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_togetherRespVOList</Alias>
        <Columns isList="true" count="11">
          <value>orderNo,System.String</value>
          <value>name,System.String</value>
          <value>nation,System.String</value>
          <value>birthday,System.String</value>
          <value>idType,System.String</value>
          <value>idTypeName,System.String</value>
          <value>idNo,System.String</value>
          <value>sex,System.String</value>
          <value>phone,System.String</value>
          <value>userAddress,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>a6fc3840f5ef40a2bbe36fbd0fe6f5b1</Key>
        <Name>root_togetherRespVOList</Name>
        <NameInSource>printCheckinForm.root_togetherRespVOList</NameInSource>
      </root_togetherRespVOList>
    </DataSources>
    <Relations isList="true" count="1">
      <root Ref="5" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_togetherRespVOList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>togetherRespVOList</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>06eccb5783794c06b1713b30f7c4ff85</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="6" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="6">
        <ReportTitleBand1 Ref="7" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,6.4,2.6</ClientRectangle>
          <Components isList="true" count="4">
            <Text5 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,16,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text5</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>{root.hname}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text1 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,6.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>入住登记单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,6.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text3</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>NO:{root.orderNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,6.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text4</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>打印时间:{Time}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </ReportTitleBand1>
        <页眉1 Ref="12" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.8,6.4,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </页眉1>
        <DataBand1 Ref="13" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,4.6,6.4,10</ClientRectangle>
          <Components isList="true" count="35">
            <Text37 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text39 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.rNo} ({root.buildingName}-{root.floorName})</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text41 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,0.6,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.rtName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>首日价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0.6,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.firstDayFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>房包早</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text48 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>入住时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.2,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.roomBkNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,1.2,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>赠早</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,1.2,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.bkNum}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text52 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.8,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text53 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,1.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>民族</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text54 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,1.8,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.nation}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text59 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,6,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.checkinTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text60 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text61 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,7.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>入住天数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text62 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,7.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.subName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text63 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,6.6,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.checkoutTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,7.2,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.nights}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text67 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,7.8,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.fee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
            <Text70 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <CanShrink>True</CanShrink>
              <ClientRectangle>0,8.4,6.4,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>备注：
{root.remark}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text40 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,0.6,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <文本1 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本1</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>证件类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,3,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本2</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.idTypeName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本3 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,3.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本3</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>证件号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,3.6,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本4</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.idNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
            <文本5 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.4,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本5</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>手机号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本6 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,2.4,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本6</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.phone}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,5.4,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本7</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>地址</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,5.4,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本8</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.userAddress}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,4.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本9</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本10 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,4.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本10</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>出生日期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本11 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,4.2,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本11</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.sex}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,4.8,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本12</Name>
              <Page isRef="6" />
              <Parent isRef="13" />
              <Text>{root.birthday}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <栏首1 Ref="49" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,15.4,6.4,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <文本14 Ref="50" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,6.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>文本14</Name>
              <Page isRef="6" />
              <Parent isRef="49" />
              <PrintOn>OnlyLastPage</PrintOn>
              <Text>同住人信息：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </栏首1>
        <数据区1 Ref="51" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <CanBreak>True</CanBreak>
          <ClientRectangle>0,16.8,6.4,1.2</ClientRectangle>
          <Components isList="true" count="1">
            <文本13 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,6.4,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <GrowToHeight>True</GrowToHeight>
              <Name>文本13</Name>
              <Page isRef="6" />
              <Parent isRef="51" />
              <PrintOn>OnlyLastPage</PrintOn>
              <Text>姓名：{root_togetherRespVOList.name} 手机号：{root_togetherRespVOList.phone}
证件号：{root_togetherRespVOList.idNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_togetherRespVOList</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
          <Sort isList="true" count="0" />
        </数据区1>
        <数据区2 Ref="53" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.8,6.4,2.4</ClientRectangle>
          <Components isList="true" count="4">
            <Text12 Ref="54" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,6.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text12</Name>
              <Page isRef="6" />
              <Parent isRef="53" />
              <Text>
宾客签名：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text12>
            <Text11 Ref="55" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0.8,6.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text11</Name>
              <Page isRef="6" />
              <Parent isRef="53" />
              <Text>
酒店电话：{root.frontPhone}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text11>
            <Text6 Ref="56" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,1.6,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text6</Name>
              <Page isRef="6" />
              <Parent isRef="53" />
              <Text>
酒店地址：</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
            </Text6>
            <Text7 Ref="57" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>1.8,1.6,4.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text7</Name>
              <Page isRef="6" />
              <Parent isRef="53" />
              <Text>
{root.address}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区2</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
          <Sort isList="true" count="0" />
        </数据区2>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>4b3b5a8ccbb545c4950fcc89aaabd32a</Guid>
      <Margins>0.8,0.8,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>23</PageHeight>
      <PageWidth>8</PageWidth>
      <Report isRef="0" />
      <UnlimitedBreakable>False</UnlimitedBreakable>
      <UnlimitedHeight>True</UnlimitedHeight>
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>入住单</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>6/18/2025 5:25:26 PM</ReportChanged>
  <ReportCreated>10/31/2024 4:39:32 PM</ReportCreated>
  <ReportFile>D:\11\2\aflower-pms-front\public\reports\print\printCheckinForm78.mrt</ReportFile>
  <ReportGuid>6655650cc3b649b2aeaf09bd7fc871ca</ReportGuid>
  <ReportName>入住单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 入住单 : Stimulsoft.Report.StiReport
    {
        public 入住单()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>JS</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>