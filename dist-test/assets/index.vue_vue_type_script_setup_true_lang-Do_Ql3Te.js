import{r as e,I as a}from"./item-BWmZJmeN.js";import{_ as l}from"./sub.vue_vue_type_script_setup_true_lang-CdO6GRrv.js";import{d as n,b as u,D as t,af as i,r as o,o as r,c as s,F as v,ag as c,a7 as d,ah as h,e as m,R as p}from"./index-CkEhI1Zk.js";const f=n({name:"MainMenu",__name:"index",props:{menu:{},value:{},mode:{default:"vertical"},collapse:{type:Boolean,default:!1},showCollapseName:{type:Boolean,default:!1},rounded:{type:Boolean,default:!1},direction:{default:"ltr"}},setup(n){const f=n,y=u(f.value),x=u({}),k=u({}),_=u([]),g=u([]);function M(e,a=[]){e.forEach((e=>{const l=e.path??JSON.stringify(e);if(e.children){const n=[...a,l];k.value[l]={index:l,indexPath:n,active:!1},M(e.children,n)}else x.value[l]={index:l,indexPath:a}}))}const N=e=>{_.value.includes(e)||_.value.push(e)},O=e=>{Array.isArray(e)?h((()=>{O(e.at(-1)),e.length>1&&O(e.slice(0,-1))})):Object.keys(k.value).forEach((a=>{k.value[a].indexPath.includes(e)&&(_.value=_.value.filter((a=>a!==e)))}))};function P(e){var a,l;for(const n in k.value)k.value[n].active=!1;null==(a=k.value[e])||a.indexPath.forEach((e=>{k.value[e].active=!0})),null==(l=x.value[e])||l.indexPath.forEach((e=>{k.value[e].active=!0}))}const S=e=>{("horizontal"===f.mode||f.collapse)&&(_.value=[]),P(e)};function b(){const e=y.value&&x.value[y.value];P(y.value),e&&!f.collapse&&e.indexPath.forEach((e=>{k.value[e]&&N(e)}))}return t((()=>f.menu),(e=>{M(e),b()}),{deep:!0,immediate:!0}),t((()=>f.value),(e=>{x.value[e]||(y.value="");const a=x.value[e]||y.value&&x.value[y.value]||x.value[f.value];y.value=a?a.index:e,b()})),t((()=>f.collapse),(e=>{e&&(_.value=[]),b()})),i(e,o({props:f,items:x,subMenus:k,activeIndex:y,openedMenus:_,mouseInMenu:g,openMenu:N,closeMenu:O,handleMenuItemClick:S,handleSubMenuClick:e=>{_.value.includes(e)?O(e):N(e)}})),(e,n)=>(r(),s("div",{class:d(["h-full w-full flex flex-col of-hidden transition-all",{"flex-row! w-auto!":"horizontal"===f.mode,"py-1":"vertical"===f.mode}])},[(r(!0),s(v,null,c(e.menu,(e=>{var n,u,t;return r(),s(v,{key:e.path??JSON.stringify(e)},[!1!==(null==(n=e.meta)?void 0:n.menu)?(r(),s(v,{key:0},[(null==(u=e.children)?void 0:u.length)?(r(),m(l,{key:0,menu:e,"unique-key":[e.path??(e.children.every((e=>{var a;return!1===(null==(a=e.meta)?void 0:a.menu)}))?e.children[0].path:JSON.stringify(e))]},null,8,["menu","unique-key"])):(r(),m(a,{key:1,item:e,"unique-key":[e.path??((null==(t=e.children)?void 0:t.every((e=>{var a;return!1===(null==(a=e.meta)?void 0:a.menu)})))?e.children[0].path:JSON.stringify(e))],onClick:a=>{var l;return S(e.path??((null==(l=e.children)?void 0:l.every((e=>{var a;return!1===(null==(a=e.meta)?void 0:a.menu)})))?e.children[0].path:JSON.stringify(e)))}},null,8,["item","unique-key","onClick"]))],64)):p("",!0)],64)})),128))],2))}});export{f as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-Do_Ql3Te.js.map
