{"version": 3, "file": "accSetList-BnsNs7By.js", "sources": ["../../src/views/cash/debit/components/accSetList.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { AccSetModel } from '@/models/index'\r\nimport { accSetApi, dictData<PERSON>pi } from '@/api/modules/index'\r\nimport { CONSUME_ACCOUNT } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { getAuth } from '@/utils/index'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst userStore = useUserStore()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n  // 列表数据\r\n  dataList: [] as AccSetModel[],\r\n})\r\n\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  /** 账套名称 */\r\n  accName: '',\r\n  /** 科目代码 */\r\n  subCode: '',\r\n})\r\n\r\nonMounted(() => {\r\n  getConsumeSubjects()\r\n})\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  accSetApi.getAllMerchantAccSetList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data\r\n  })\r\n}\r\n\r\n// 获取消费科目\r\nconst dictTypes = [CONSUME_ACCOUNT]\r\nconst subjectList = ref<any[]>([])\r\nfunction getConsumeSubjects() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    subjectList.value = res.data.filter((item: any) => item.dictType === CONSUME_ACCOUNT)\r\n  })\r\n}\r\n\r\nconst subProps = {\r\n  expandTrigger: 'hover' as const,\r\n  value: 'code',\r\n  label: 'name',\r\n}\r\n\r\nfunction onEdit(row: any) {}\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    if (props.modelValue) {\r\n      getDataList()\r\n    }\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n    emits('success')\r\n  },\r\n})\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nconst adding = ref(false)\r\nfunction onSubmit() {\r\n  if (!form.value.accName) {\r\n    return ElMessage.error('账户名称不能为空!')\r\n  } else if (!form.value.subCode) {\r\n    return ElMessage.error('消费科目不能为空!')\r\n  } else {\r\n    adding.value = true\r\n    accSetApi\r\n      .createAccSet({\r\n        gcode: userStore.gcode,\r\n        hcodes: [userStore.hcode],\r\n        accName: form.value.accName,\r\n        subCode: form.value.subCode,\r\n        subName: subjectList.value.find((_item) => {\r\n          return _item.code === form.value.subCode\r\n        }).label,\r\n        isEnable: '1',\r\n        accCode: '',\r\n      })\r\n      .then((res: any) => {\r\n        adding.value = false\r\n        if (res.code === 0) {\r\n          ElMessage.success({\r\n            message: '新增成功',\r\n            center: true,\r\n          })\r\n          getDataList()\r\n        } else {\r\n          ElMessage.error({\r\n            message: res.msg,\r\n            center: true,\r\n          })\r\n        }\r\n      })\r\n      .catch(() => {\r\n        adding.value = false\r\n      })\r\n  }\r\n}\r\nconst editing = ref(false)\r\nfunction onSave(row: any) {\r\n  if (!row.accName) {\r\n    return ElMessage.error('账户名称不能为空!')\r\n  } else {\r\n    editing.value = true\r\n    accSetApi\r\n      .updateAccSet({\r\n        id: row.id,\r\n        gcode: userStore.gcode,\r\n        hcodes: [userStore.hcode],\r\n        accName: row.accName,\r\n        accCode: row.accCode,\r\n        subCode: row.subCode,\r\n        subName: row.subName,\r\n        isEnable: row.state,\r\n      })\r\n      .then((res: any) => {\r\n        editing.value = false\r\n        if (res.code === 0) {\r\n          ElMessage.success({\r\n            message: '修改成功',\r\n            center: true,\r\n          })\r\n          getDataList()\r\n          row.isEdit = false\r\n        } else {\r\n          ElMessage.error({\r\n            message: res.msg,\r\n            center: true,\r\n          })\r\n        }\r\n      })\r\n      .catch(() => {\r\n        editing.value = false\r\n      })\r\n  }\r\n}\r\n\r\nfunction onChangeStatus(row: any) {\r\n  return new Promise<boolean>((resolve) => {\r\n    row.statusLoading = true\r\n    accSetApi\r\n      .updateAccSetAccStatus({\r\n        gcode: userStore.gcode,\r\n        hcode: userStore.hcode,\r\n        accCode: row.accCode,\r\n        state: row.state === '0' ? '1' : '0',\r\n      })\r\n      .then((res: any) => {\r\n        row.statusLoading = false\r\n        if (res.code === 0) {\r\n          ElMessage.success({\r\n            message: `${row.state === '0' ? '启用' : '停用'}成功`,\r\n            center: true,\r\n          })\r\n          getDataList()\r\n        } else {\r\n          ElMessage.error({\r\n            message: res.msg,\r\n            center: true,\r\n          })\r\n        }\r\n        return resolve(true)\r\n      })\r\n      .catch(() => {\r\n        row.statusLoading = false\r\n        return resolve(false)\r\n      })\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <el-dialog v-model=\"myVisible\" title=\"账套管理\" width=\"900px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <page-main>\r\n        <el-form :model=\"form\" size=\"default\" label-width=\"100px\" inline-message inline label-suffix=\"：\" class=\"search-form\">\r\n          <el-form-item label=\"账套名称\">\r\n            <el-input v-model=\"form.accName\" placeholder=\"请输入账套名称\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"消费科目\">\r\n            <el-select v-model=\"form.subCode\" style=\"width: 200px\" placeholder=\"请选择消费科目\">\r\n              <el-option v-for=\"item in subjectList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item style=\"float: right\">\r\n            <el-button v-auth=\"'finance:acc-set:create'\" type=\"primary\" :loading=\"adding\" @click=\"onSubmit\">\r\n              <template #icon>\r\n                <svg-icon name=\"ep:plus\" />\r\n              </template>\r\n              添加\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\">\r\n          <el-table-column label=\"账套名称\">\r\n            <template #default=\"scope\">\r\n              <ElInput v-if=\"scope.row.isEdit\" v-model=\"scope.row.accName\" />\r\n              <span v-else>{{ scope.row.accName }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"subName\" label=\"科目名称\" />\r\n          <el-table-column label=\"状态\">\r\n            <template #default=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.state\"\r\n                :disabled=\"getAuth('finance:acc-set:create')\"\r\n                :loading=\"scope.row.statusLoading\"\r\n                inline-prompt\r\n                active-text=\"启用\"\r\n                active-value=\"1\"\r\n                inactive-value=\"0\"\r\n                inactive-text=\"停用\"\r\n                :before-change=\"() => onChangeStatus(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <ElTableColumn label=\"操作\" width=\"200\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <template v-if=\"scope.row.isEdit\">\r\n                <ElButton type=\"primary\" plain size=\"small\" :loading=\"editing\" @click=\"onSave(scope.row)\"> 保存 </ElButton>\r\n                <ElButton type=\"danger\" plain size=\"small\" @click=\"scope.row.isEdit = false\"> 取消 </ElButton>\r\n              </template>\r\n              <template v-else>\r\n                <ElButton v-auth=\"'finance:acc-set:create'\" type=\"primary\" plain size=\"small\" @click=\"scope.row.isEdit = true\"> 编辑 </ElButton>\r\n              </template>\r\n            </template>\r\n          </ElTableColumn>\r\n        </el-table>\r\n      </page-main>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "data", "ref", "loading", "tableAutoHeight", "dataList", "form", "gcode", "hcode", "accName", "subCode", "getDataList", "value", "params", "accSetApi", "getAllMerchantAccSetList", "then", "res", "onMounted", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "subjectList", "filter", "item", "dictType", "CONSUME_ACCOUNT", "myVisible", "computed", "get", "modelValue", "set", "val", "adding", "onSubmit", "createAccSet", "hcodes", "subName", "find", "_item", "code", "label", "isEnable", "accCode", "ElMessage", "success", "message", "center", "error", "msg", "catch", "editing", "row", "Promise", "resolve", "statusLoading", "updateAccSetAccStatus", "state", "updateAccSet", "id", "isEdit"], "mappings": "ypCAOA,MAAMA,EAAQC,EASRC,EAAQC,EAIRC,EAAYC,IAEZC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAEjBC,SAAU,KAGNC,EAAOJ,EAAI,CACfK,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,MAEjBC,QAAS,GAETC,QAAS,KAOX,SAASC,IACPV,EAAKW,MAAMT,SAAU,EACrB,MAAMU,EAAS,CACbN,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,OAEnBM,EAAUC,yBAAyBF,GAAQG,MAAMC,IAC/ChB,EAAKW,MAAMT,SAAU,EAChBF,EAAAW,MAAMP,SAAWY,EAAIhB,IAAA,GAC3B,CAbHiB,GAAU,KAoBRC,EAAYC,iBAAiBC,GAAWL,MAAMC,IAChCK,EAAAV,MAAQK,EAAIhB,KAAKsB,QAAQC,GAAcA,EAAKC,WAAaC,GAAe,GApBnE,IAgBf,MAAAL,EAAY,CAACK,GACbJ,EAAcpB,EAAW,IAe/B,MAAMyB,EAAYC,EAAS,CACzBC,IAAM,KACAlC,EAAMmC,YACInB,IAEPhB,EAAMmC,YAEf,GAAAC,CAAIC,GACFnC,EAAM,oBAAqBmC,GAC3BnC,EAAM,UAAS,IAQboC,EAAS/B,GAAI,GACnB,SAASgC,IACH,OAAC5B,EAAKM,MAAMH,QAEJH,EAAKM,MAAMF,SAGrBuB,EAAOrB,OAAQ,OACfE,EACGqB,aAAa,CACZ5B,MAAOR,EAAUQ,MACjB6B,OAAQ,CAACrC,EAAUS,OACnBC,QAASH,EAAKM,MAAMH,QACpBC,QAASJ,EAAKM,MAAMF,QACpB2B,QAASf,EAAYV,MAAM0B,MAAMC,GACxBA,EAAMC,OAASlC,EAAKM,MAAMF,UAChC+B,MACHC,SAAU,IACVC,QAAS,KAEV3B,MAAMC,IACLgB,EAAOrB,OAAQ,EACE,IAAbK,EAAIuB,MACNI,EAAUC,QAAQ,CAChBC,QAAS,OACTC,QAAQ,IAEEpC,KAEZiC,EAAUI,MAAM,CACdF,QAAS7B,EAAIgC,IACbF,QAAQ,GACT,IAGJG,OAAM,KACLjB,EAAOrB,OAAQ,CAAA,KA/BZgC,EAAUI,MAAM,aAFhBJ,EAAUI,MAAM,YAmCzB,CAEI,MAAAG,EAAUjD,GAAI,87DAuCIkD,QACf,IAAIC,SAAkBC,IAC3BF,EAAIG,eAAgB,EACpBzC,EACG0C,sBAAsB,CACrBjD,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,MACjBmC,QAASS,EAAIT,QACbc,MAAqB,MAAdL,EAAIK,MAAgB,IAAM,MAElCzC,MAAMC,IACLmC,EAAIG,eAAgB,EACH,IAAbtC,EAAIuB,MACNI,EAAUC,QAAQ,CAChBC,SAA0B,MAAdM,EAAIK,MAAgB,KAAO,MAA9B,KACTV,QAAQ,IAEEpC,KAEZiC,EAAUI,MAAM,CACdF,QAAS7B,EAAIgC,IACbF,QAAQ,IAGLO,GAAQ,MAEhBJ,OAAM,KACLE,EAAIG,eAAgB,EACbD,GAAQ,KAChB,IA7BP,IAAwBF,4PAtCxB,SAAgBA,GACV,IAACA,EAAI3C,QACA,OAAAmC,EAAUI,MAAM,aAEvBG,EAAQvC,OAAQ,EAChBE,EACG4C,aAAa,CACZC,GAAIP,EAAIO,GACRpD,MAAOR,EAAUQ,MACjB6B,OAAQ,CAACrC,EAAUS,OACnBC,QAAS2C,EAAI3C,QACbkC,QAASS,EAAIT,QACbjC,QAAS0C,EAAI1C,QACb2B,QAASe,EAAIf,QACbK,SAAUU,EAAIK,QAEfzC,MAAMC,IACLkC,EAAQvC,OAAQ,EACC,IAAbK,EAAIuB,MACNI,EAAUC,QAAQ,CAChBC,QAAS,OACTC,QAAQ,IAEEpC,IACZyC,EAAIQ,QAAS,GAEbhB,EAAUI,MAAM,CACdF,QAAS7B,EAAIgC,IACbF,QAAQ,GACT,IAGJG,OAAM,KACLC,EAAQvC,OAAQ,CAAA,GAEtB"}