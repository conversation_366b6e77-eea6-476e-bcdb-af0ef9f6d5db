{"version": 3, "file": "roomRepair-DSubUqw7.js", "sources": ["../../src/views/room/realtime/components/roomRepair.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"repairTitle\": \"Set Repair: {rNo}\",\r\n      \"startTime\": \"Start Time\",\r\n      \"endTime\": \"End Time\",\r\n      \"repairReason\": \"Repair Reason\",\r\n      \"pleaseSetStartTime\": \"Please set start time\",\r\n      \"pleaseSetEndTime\": \"Please set end time\",\r\n      \"pleaseEnterRepairReason\": \"Please enter repair reason\",\r\n      \"pleaseSelectRepairReason\": \"Please select repair reason\",\r\n      \"cancel\": \"Cancel\",\r\n      \"submit\": \"Submit\",\r\n      \"confirm\": \"Confirm\",\r\n      \"confirmOperation\": \"Confirm Operation\",\r\n      \"confirmForceRepair\": \"The current status of this room does not allow repair. Do you want to force it to repair status?\",\r\n      \"forceRepairFailed\": \"Force repair failed\",\r\n      \"cancelForceRepair\": \"Force repair cancelled\",\r\n      \"repairFailed\": \"Repair failed\",\r\n      \"repairSuccessMessage\": \"Room '{rNo}' set to repair successfully, will start at {startTime}.\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"repairTitle\": \"置维修：{rNo}\",\r\n      \"startTime\": \"开始时间\",\r\n      \"endTime\": \"结束时间\",\r\n      \"repairReason\": \"维修原因\",\r\n      \"pleaseSetStartTime\": \"请设置开始时间\",\r\n      \"pleaseSetEndTime\": \"请设置结束时间\",\r\n      \"pleaseEnterRepairReason\": \"请输入维修原因\",\r\n      \"pleaseSelectRepairReason\": \"请选择维修原因\",\r\n      \"cancel\": \"取消\",\r\n      \"submit\": \"提交\",\r\n      \"confirm\": \"确定\",\r\n      \"confirmOperation\": \"确认操作\",\r\n      \"confirmForceRepair\": \"该房间当前状态不允许维修，是否强制置为维修状态？\",\r\n      \"forceRepairFailed\": \"强制置维修失败\",\r\n      \"cancelForceRepair\": \"已取消强制置维修\",\r\n      \"repairFailed\": \"置维修失败\",\r\n      \"repairSuccessMessage\": \"房间「{rNo}」置维修成功，将于{startTime}开始.\"\r\n    },\r\n    \"km\": {\r\n      \"repairTitle\": \"កំណត់ការជួសជុល៖ {rNo}\",\r\n      \"startTime\": \"ពេលវេលាចាប់ផ្តើម\",\r\n      \"endTime\": \"ពេលវេលាបញ្ចប់\",\r\n      \"repairReason\": \"មូលហេតុជួសជុល\",\r\n      \"pleaseSetStartTime\": \"សូមកំណត់ពេលវេលាចាប់ផ្តើម\",\r\n      \"pleaseSetEndTime\": \"សូមកំណត់ពេលវេលាបញ្ចប់\",\r\n      \"pleaseEnterRepairReason\": \"សូមបញ្ចូលមូលហេតុជួសជុល\",\r\n      \"pleaseSelectRepairReason\": \"សូមជ្រើសរើសមូលហេតុជួសជុល\",\r\n      \"cancel\": \"បោះបង់\",\r\n      \"submit\": \"ដាក់ស្នើ\",\r\n      \"confirm\": \"បញ្ជាក់\",\r\n      \"confirmOperation\": \"បញ្ជាក់ប្រតិបត្តិការ\",\r\n      \"confirmForceRepair\": \"ស្ថានភាពបច្ចុប្បន្នរបស់បន្ទប់នេះមិនអនុញ្ញាតឱ្យជួសជុលទេ។ តើអ្នកចង់បង្ខំឱ្យវាទៅជាស្ថានភាពជួសជុលទេ?\",\r\n      \"forceRepairFailed\": \"បង្ខំជួសជុលបរាជ័យ\",\r\n      \"cancelForceRepair\": \"បានបោះបង់ការបង្ខំជួសជុល\",\r\n      \"repairFailed\": \"ការជួសជុលបរាជ័យ\",\r\n      \"repairSuccessMessage\": \"បន្ទប់ '{rNo}' កំណត់ការជួសជុលដោយជោគជ័យ នឹងចាប់ផ្តើមនៅ {startTime}។\"\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { generalConfigApi, roomApi } from '@/api/modules/index'\r\nimport { BooleanEnum, GeneralConfigTypeEnum, RoomState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rCode: string\r\n    rNo: string\r\n  }>(),\r\n  {\r\n    rCode: '',\r\n    rNo: '',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst formRef = ref<FormInstance>()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  rCode: props.rCode,\r\n  targetState: RoomState.OO,\r\n  repairStartTime: dayjs().add(5, 'minute').format('YYYY-MM-DD HH:mm'),\r\n  repairEndTime: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm'),\r\n  repairReason: '',\r\n  isForceRepair: '0',\r\n})\r\nconst formRules = ref<FormRules>({\r\n  repairStartTime: [{ required: true, message: t('pleaseSetStartTime'), trigger: 'blur' }],\r\n  repairEndTime: [{ required: true, message: t('pleaseSetEndTime'), trigger: 'blur' }],\r\n  repairReason: [{ required: true, message: t('pleaseEnterRepairReason'), trigger: 'blur' }],\r\n})\r\n\r\n// 时间相关\r\nfunction disabledDate(time: any) {\r\n  return time.getTime() < Date.now() - 8.64e7\r\n}\r\nfunction disabledCheckoutTime(time: any) {\r\n  const checkInDateTimeMp = new Date(form.value.repairStartTime).getTime()\r\n  return time.getTime() <= checkInDateTimeMp\r\n}\r\n\r\nonMounted(() => {\r\n  getRepairReasons()\r\n})\r\n/**\r\n * 锁房原因列表\r\n */\r\nconst repairReasons = ref<{ code: string; value: string }[]>([])\r\nfunction getRepairReasons() {\r\n  generalConfigApi\r\n    .list({\r\n      gcode: userStore.gcode,\r\n      type: GeneralConfigTypeEnum.REPAIR_REASON,\r\n      isEnable: BooleanEnum.YES,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code !== 0) {\r\n        ElMessage.error(res.msg)\r\n        return\r\n      }\r\n      repairReasons.value = res.data\r\n    })\r\n}\r\nconst selectRepair = ref('')\r\nfunction repairChange(newValue: any) {\r\n  // newValue 是 form.lockedReason 的当前值，即选中项的 code\r\n  const selectRepair = repairReasons.value.find((r) => r.code === newValue)\r\n  if (selectRepair) {\r\n    // 获取选中项的 label 值\r\n    form.value.repairReason = selectRepair.value\r\n  }\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        form.value.repairStartTime = dayjs(form.value.repairStartTime).format('YYYY-MM-DD HH:mm')\r\n        form.value.repairEndTime = dayjs(form.value.repairEndTime).format('YYYY-MM-DD HH:mm')\r\n        submitRepairRequest()\r\n      }\r\n    })\r\n}\r\n\r\n// 提交维修请求\r\nfunction submitRepairRequest() {\r\n  roomApi.updateRoomAllStatus(form.value).then((res: any) => {\r\n    if (res.code !== 0) {\r\n      // 当错误码为1009001027时，弹出确认框\r\n      if (res.code === 1009001027) {\r\n        ElMessageBox.confirm(t('confirmForceRepair'), t('confirmOperation'), {\r\n          confirmButtonText: t('confirm'),\r\n          cancelButtonText: t('cancel'),\r\n          type: 'warning',\r\n        })\r\n          .then(() => {\r\n            // 用户点击确定，设置强制维修标志为1，重新提交\r\n            form.value.isForceRepair = '1'\r\n            roomApi.updateRoomAllStatus(form.value).then((forceRes: any) => {\r\n              if (forceRes.code !== 0) {\r\n                ElMessage.error(forceRes.msg || t('forceRepairFailed'))\r\n                return\r\n              }\r\n              handleSuccess(forceRes)\r\n            })\r\n          })\r\n          .catch(() => {\r\n            // 用户点击取消，不做任何操作\r\n            ElMessage.info(t('cancelForceRepair'))\r\n          })\r\n      } else {\r\n        // 其他错误码，显示错误信息\r\n        ElMessage.error(res.msg || t('repairFailed'))\r\n      }\r\n      return\r\n    }\r\n    handleSuccess(res)\r\n  })\r\n}\r\n\r\n// 处理成功响应\r\nfunction handleSuccess(res: any) {\r\n  ElMessage.success({\r\n    message: t('repairSuccessMessage', { rNo: props.rNo, startTime: form.value.repairStartTime }),\r\n    type: 'success',\r\n    center: true,\r\n  })\r\n  emits('success', {\r\n    rCode: props.rCode,\r\n    rNo: props.rNo,\r\n    state: res.data.state,\r\n  })\r\n  onCancel()\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction CheckinTime() {\r\n  const time = dayjs(form.value.repairEndTime).format('HH:mm')\r\n  if (dayjs(form.value.repairStartTime).format('YYYY-MM-DD') >= dayjs(form.value.repairEndTime).format('YYYY-MM-DD')) {\r\n    form.value.repairEndTime = `${dayjs(form.value.repairStartTime).add(1, 'day').format('YYYY-MM-DD')} ${time}`\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('repairTitle', { rNo: props.rNo })\" width=\"500px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"100px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('startTime')\" prop=\"repairStartTime\">\r\n        <el-date-picker v-model=\"form.repairStartTime\" type=\"datetime\" format=\"YYYY-MM-DD HH:mm\" :placeholder=\"t('startTime')\" style=\"width: 200px\" :clearable=\"false\" :disabled-date=\"disabledDate\" @change=\"CheckinTime\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('endTime')\" prop=\"repairEndTime\">\r\n        <el-date-picker v-model=\"form.repairEndTime\" type=\"datetime\" format=\"YYYY-MM-DD HH:mm\" :placeholder=\"t('endTime')\" style=\"width: 200px\" :clearable=\"false\" :disabled-date=\"disabledCheckoutTime\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\">\r\n        <el-select v-model=\"selectRepair\" :placeholder=\"t('pleaseSelectRepairReason')\" @change=\"repairChange\">\r\n          <el-option v-for=\"item in repairReasons\" :key=\"item.code\" :label=\"item.value\" :value=\"item.code\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('repairReason')\" prop=\"repairReason\">\r\n        <el-input v-model=\"form.repairReason\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('pleaseEnterRepairReason')\" maxlength=\"255\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('submit') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "form", "gcode", "hcode", "rCode", "targetState", "RoomState", "OO", "repairStartTime", "dayjs", "add", "format", "repairEndTime", "repairReason", "isForceRepair", "formRules", "required", "message", "trigger", "disabledDate", "time", "getTime", "Date", "now", "disabledCheckoutTime", "checkInDateTimeMp", "value", "onMounted", "generalConfigApi", "list", "type", "GeneralConfigTypeEnum", "REPAIR_REASON", "isEnable", "BooleanEnum", "YES", "then", "res", "code", "repairReasons", "data", "ElMessage", "error", "msg", "selectRepair", "repairChange", "newValue", "find", "r", "onSubmit", "validate", "valid", "roomApi", "updateRoomAllStatus", "handleSuccess", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "forceRes", "catch", "info", "success", "rNo", "startTime", "center", "state", "onCancel", "CheckinTime"], "mappings": "uhCAqEA,MAAMA,EAAQC,EAWRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,EAAUC,IACVC,EAAYC,EAAS,CACzBC,IAAM,IACGZ,EAAMa,WAEf,GAAAC,CAAIC,GACFb,EAAM,oBAAqBa,EAAG,IAG5BC,EAAOP,EAAI,CACfQ,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBC,MAAOnB,EAAMmB,MACbC,YAAaC,EAAUC,GACvBC,gBAAiBC,IAAQC,IAAI,EAAG,UAAUC,OAAO,oBACjDC,cAAeH,IAAQC,IAAI,EAAG,OAAOC,OAAO,oBAC5CE,aAAc,GACdC,cAAe,MAEXC,EAAYrB,EAAe,CAC/Bc,gBAAiB,CAAC,CAAEQ,UAAU,EAAMC,QAAS5B,EAAE,sBAAuB6B,QAAS,SAC/EN,cAAe,CAAC,CAAEI,UAAU,EAAMC,QAAS5B,EAAE,oBAAqB6B,QAAS,SAC3EL,aAAc,CAAC,CAAEG,UAAU,EAAMC,QAAS5B,EAAE,2BAA4B6B,QAAS,WAInF,SAASC,EAAaC,GACpB,OAAOA,EAAKC,UAAYC,KAAKC,MAAQ,KAAA,CAEvC,SAASC,EAAqBJ,GAC5B,MAAMK,EAAoB,IAAIH,KAAKrB,EAAKyB,MAAMlB,iBAAiBa,UACxD,OAAAD,EAAKC,WAAaI,CAAA,CAG3BE,GAAU,KAQRC,EACGC,KAAK,CACJ3B,MAAOX,EAAUW,MACjB4B,KAAMC,EAAsBC,cAC5BC,SAAUC,EAAYC,MAEvBC,MAAMC,IACY,IAAbA,EAAIC,KAIRC,EAAcb,MAAQW,EAAIG,KAHdC,EAAAC,MAAML,EAAIM,IAGI,GAlBb,IAKb,MAAAJ,EAAgB7C,EAAuC,IAgBvD,MAAAkD,EAAelD,EAAI,IACzB,SAASmD,EAAaC,GAEdF,MAAAA,EAAeL,EAAcb,MAAMqB,MAAMC,GAAMA,EAAEV,OAASQ,IAC5DF,IAEG3C,EAAAyB,MAAMb,aAAe+B,EAAalB,MACzC,CAGF,SAASuB,IACPxD,EAAQiC,OACNjC,EAAQiC,MAAMwB,UAAUC,IAClBA,IACGlD,EAAAyB,MAAMlB,gBAAkBC,EAAMR,EAAKyB,MAAMlB,iBAAiBG,OAAO,oBACjEV,EAAAyB,MAAMd,cAAgBH,EAAMR,EAAKyB,MAAMd,eAAeD,OAAO,oBAQxEyC,EAAQC,oBAAoBpD,EAAKyB,OAAOU,MAAMC,IAC3B,IAAbA,EAAIC,KA6BRgB,EAAcjB,GA3BK,aAAbA,EAAIC,KACNiB,EAAaC,QAAQnE,EAAE,sBAAuBA,EAAE,oBAAqB,CACnEoE,kBAAmBpE,EAAE,WACrBqE,iBAAkBrE,EAAE,UACpByC,KAAM,YAELM,MAAK,KAEJnC,EAAKyB,MAAMZ,cAAgB,IAC3BsC,EAAQC,oBAAoBpD,EAAKyB,OAAOU,MAAMuB,IACtB,IAAlBA,EAASrB,KAIbgB,EAAcK,GAHZlB,EAAUC,MAAMiB,EAAShB,KAAOtD,EAAE,qBAGd,GACvB,IAEFuE,OAAM,KAEKnB,EAAAoB,KAAKxE,EAAE,qBAAoB,IAIzCoD,EAAUC,MAAML,EAAIM,KAAOtD,EAAE,gBAIhB,IArCO,GAEvB,CAwCL,SAASiE,EAAcjB,GACrBI,EAAUqB,QAAQ,CAChB7C,QAAS5B,EAAE,uBAAwB,CAAE0E,IAAK9E,EAAM8E,IAAKC,UAAW/D,EAAKyB,MAAMlB,kBAC3EsB,KAAM,UACNmC,QAAQ,IAEV9E,EAAM,UAAW,CACfiB,MAAOnB,EAAMmB,MACb2D,IAAK9E,EAAM8E,IACXG,MAAO7B,EAAIG,KAAK0B,QAETC,GAAA,CAGX,SAASA,IACPxE,EAAU+B,OAAQ,CAAA,CAGpB,SAAS0C,IACP,MAAMhD,EAAOX,EAAMR,EAAKyB,MAAMd,eAAeD,OAAO,SAChDF,EAAMR,EAAKyB,MAAMlB,iBAAiBG,OAAO,eAAiBF,EAAMR,EAAKyB,MAAMd,eAAeD,OAAO,gBACnGV,EAAKyB,MAAMd,cAAgB,GAAGH,EAAMR,EAAKyB,MAAMlB,iBAAiBE,IAAI,EAAG,OAAOC,OAAO,iBAAiBS,IACxG"}