{"version": 3, "file": "invoicedModal-XEk1BZXP.js", "sources": ["../../src/views/order/info/components/orderdetail/invoicedModal.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"cancel\": \"Cancel\",\r\n    \"confirm\": \"confirm\",\r\n    \"close\": \"Close\",\r\n    \"remark\": \"remark\",\r\n    \"invoiceType\": \"Invoice Type\",\r\n    \"pleaseSelectInvoiceType\": \"Please Select Invoice Type\",\r\n    \"invoiceTypeObj\": {\r\n      \"VATSpecial\": \"VAT Special Invoice\",\r\n      \"VATGeneral\": \"VAT General Invoice\",\r\n      \"individualVATGeneral\": \"Individual VAT General Invoice\"\r\n    },\r\n    \"invoiceForm\": \"Invoice Form\",\r\n    \"pleaseSelectInvoiceForm\": \"Please Select Invoice Form\",\r\n    \"invoiceFormObj\": {\r\n      \"paperInvoice\": \"Paper Invoice\",\r\n      \"electronicInvoice\": \"Electronic Invoice\",\r\n      \"DElectronicInvoice\": \"Digital Electronic Invoice\"\r\n    },\r\n    \"invoiceNumber\": \"Invoice number\",\r\n    \"pleaseInputInvoiceNumber\": \"Please input Invoice number\",\r\n    \"invoiceTitle\": \"Invoice title\",\r\n    \"invoiceCode\": \"Invoice code\",\r\n    \"unitTaxCode\": \"unit tax code\",\r\n    \"registeredAddress\": \"Registered address\",\r\n    \"registeredPhone\": \"Registered phone\",\r\n    \"openingBank\": \"Opening Bank\",\r\n    \"bankAccount\": \"bank account\",\r\n    \"invoiceAmount\": \"Invoice amount\",\r\n    \"pleaseInputInvoiceAmount\": \"Please input Invoice amount\",\r\n    \"invoiceTaxAmount\": \"Invoice tax amount\",\r\n    \"invoiceTime\":\"Invoice Time\",\r\n    \"state\":\"State\",\r\n    \"modify\": \"modify\",\r\n    \"normal\": \"Normal\",\r\n    \"cancel1\": \"Cancel\",\r\n    \"guest\": \"guest\",\r\n    \"invoiceRecords\": \"Invoice Records\",\r\n    \"issueAnInvoice\": \"issue an invoice\",\r\n    \"invoiceSuccess\":\"invoice success\",\r\n    \"operations\":\"Operations\",\r\n    \"confirm2\":\"Are you sure to invalidate this invoice record?\",\r\n    \"amountInvoiced\":\"Amount Invoiced\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"cancel\": \"取消\",\r\n    \"confirm\": \"确认\",\r\n    \"close\": \"关闭\",\r\n    \"remark\": \"备注\",\r\n    \"invoiceType\": \"发票类别\",\r\n    \"pleaseSelectInvoiceType\": \"请选择发票类别\",\r\n    \"invoiceTypeObj\": {\r\n      \"VATSpecial\": \"公司专票\",\r\n      \"VATGeneral\": \"公司普票\",\r\n      \"individualVATGeneral\": \"个人普票\"\r\n    },\r\n    \"invoiceForm\": \"发票形式\",\r\n    \"pleaseSelectInvoiceForm\": \"请选择发票类别\",\r\n    \"invoiceFormObj\": {\r\n      \"paperInvoice\": \"纸质发票\",\r\n      \"electronicInvoice\": \"电子发票\",\r\n      \"DElectronicInvoice\": \"数电发票\"\r\n    },\r\n    \"invoiceNumber\": \"发票号码\",\r\n    \"pleaseInputInvoiceNumber\": \"请输入发票号码\",\r\n    \"invoiceTitle\": \"发票抬头\",\r\n    \"invoiceCode\": \"发票代码\",\r\n    \"unitTaxCode\": \"纳税人识别号\",\r\n    \"registeredAddress\": \"注册地址\",\r\n    \"registeredPhone\": \"注册电话\",\r\n    \"openingBank\": \"开户银行\",\r\n    \"bankAccount\": \"银行账号\",\r\n    \"invoiceAmount\": \"发票金额\",\r\n    \"pleaseInputInvoiceAmount\": \"请输入发票金额\",\r\n    \"invoiceTaxAmount\": \"发票税额\",\r\n    \"invoiceTime\": \"开票时间\",\r\n    \"state\": \"状态\",\r\n    \"modify\": \"修改\",\r\n    \"normal\": \"正常\",\r\n    \"cancel1\": \"作废\",\r\n    \"guest\": \"住客\",\r\n    \"invoiceRecords\": \"开票记录\",\r\n    \"issueAnInvoice\": \"开票\",\r\n    \"invoiceSuccess\": \"发票成功\",\r\n    \"operations\":\"操作\",\r\n    \"confirm2\":\"确定要作废此开票记录吗？\",\r\n    \"amountInvoiced\":\"已开票金额\"\r\n  },\r\n  \"km\": {\r\n    \"cancel\": \"បោះបង់\",\r\n    \"confirm\": \"បញ្ជាក់\",\r\n    \"close\": \"បិទ\",\r\n    \"remark\": \"សម្គាល់\",\r\n    \"invoiceType\": \"ប្រភេទវិក័យប័ត្រ\",\r\n    \"pleaseSelectInvoiceType\": \"សូមជ្រើសរើសប្រភេទវិក័យប័ត្រ\",\r\n    \"invoiceTypeObj\": {\r\n      \"VATSpecial\": \"វិក័យប័ត្រពិសេស VAT\",\r\n      \"VATGeneral\": \"វិក័យប័ត្រទូទៅ VAT\",\r\n      \"individualVATGeneral\": \"វិក័យប័ត្រទូទៅ VAT បុគ្គល\"\r\n    },\r\n    \"invoiceForm\": \"ទម្រង់វិក័យប័ត្រ\",\r\n    \"pleaseSelectInvoiceForm\": \"សូមជ្រើសរើសទម្រង់វិក័យប័ត្រ\",\r\n    \"invoiceFormObj\": {\r\n      \"paperInvoice\": \"វិក័យប័ត្រក្រដាស\",\r\n      \"electronicInvoice\": \"វិក័យប័ត្រអេឡិចត្រូនិច\",\r\n      \"DElectronicInvoice\": \"វិក័យប័ត្រអេឡិចត្រូនិចឌីជីថល\"\r\n    },\r\n    \"invoiceNumber\": \"លេខវិក័យប័ត្រ\",\r\n    \"pleaseInputInvoiceNumber\": \"សូមបញ្ចូលលេខវិក័យប័ត្រ\",\r\n    \"invoiceTitle\": \"ចំណងជើងវិក័យប័ត្រ\",\r\n    \"invoiceCode\": \"កូដវិក័យប័ត្រ\",\r\n    \"unitTaxCode\": \"លេខសម្គាល់អ្នកបង់ពន្ធ\",\r\n    \"registeredAddress\": \"អាសយដ្ឋានចុះបញ្ជី\",\r\n    \"registeredPhone\": \"ទូរស័ព្ធចុះបញ្ជី\",\r\n    \"openingBank\": \"ធនាគារបើកគណនី\",\r\n    \"bankAccount\": \"លេខគណនីធនាគារ\",\r\n    \"invoiceAmount\": \"ចំនួនទឹកប្រាក់វិក័យប័ត្រ\",\r\n    \"pleaseInputInvoiceAmount\": \"សូមបញ្ចូលចំនួនទឹកប្រាក់វិក័យប័ត្រ\",\r\n    \"invoiceTaxAmount\": \"ចំនួនពន្ធវិក័យប័ត្រ\",\r\n    \"invoiceTime\": \"ពេលវេលាចេញវិក័យប័ត្រ\",\r\n    \"state\": \"ស្ថានភាព\",\r\n    \"modify\": \"កែប្រែ\",\r\n    \"normal\": \"ធម្មតា\",\r\n    \"cancel1\": \"បោះបង់\",\r\n    \"guest\": \"ភ្ញៀវ\",\r\n    \"invoiceRecords\": \"កំណត់ត្រាចេញវិក័យប័ត្រ\",\r\n    \"issueAnInvoice\": \"ចេញវិក័យប័ត្រ\",\r\n    \"invoiceSuccess\": \"ចេញវិក័យប័ត្របានជោគជ័យ\",\r\n    \"operations\": \"ប្រតិបត្តិការ\",\r\n    \"confirm2\": \"តើអ្នកប្រាកដថាចង់បោះបង់កំណត់ត្រាចេញវិក័យប័ត្រនេះទេ?\",\r\n    \"amountInvoiced\": \"ចំនួនទឹកប្រាក់ដែលបានចេញវិក័យប័ត្រ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type types from './account'\r\nimport { orderApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { ElButton, ElTag, type TabsPaneContext } from 'element-plus'\r\n\r\ndefineOptions({\r\n  name: 'Invoiced', // 弹窗开发票\r\n})\r\n/**\r\n * 传入的props变量\r\n */\r\nconst props = defineProps({\r\n  /** 订单号 */\r\n  orderNo: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  /** 默认宾客信息 */\r\n  togetherCode: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n})\r\n/**\r\n * @param 发射给父组件的方法\r\n * @param 用于子组件给父组件传值或调用父组件方法\r\n */\r\nconst emits = defineEmits(['submit', 'close'])\r\nconst { t } = useI18n()\r\n/** 获取缓存数据 */\r\nconst userStore = useUserStore()\r\n/** 开发票tabs */\r\nconst activeName = ref('1')\r\n/** 获取开发票form(新增) */\r\nconst easyFormRefAdd = ref()\r\n/** 获取开发票form(更新) */\r\nconst easyFormRefUpdate = ref()\r\n/** 更新发票 */\r\nconst easyDialogRef = ref()\r\n/** 查询条件 */\r\nconst queryParams = reactive<types.queryParams>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  orderNo: props.orderNo,\r\n})\r\n/** 后台获取到的数据对象 */\r\nconst tableData = ref<types.invoicedForm[]>([])\r\n/** 发票记录表格配置 */\r\nconst tableColumns = ref<Table.Column<types.invoicedForm>[]>([\r\n  { label: t('guest'), prop: 'name', width: 120 },\r\n  {\r\n    label: t('invoiceType'),\r\n    width: 100,\r\n    render: ({ row }) => h('span', invoiceTypeType(row.invoiceTitleType)),\r\n  },\r\n  {\r\n    label: t('invoiceForm'),\r\n    width: 100,\r\n    render: ({ row }) => h('span', invoiceFormType(row.invoiceType)),\r\n  },\r\n  { label: t('invoiceNumber'), prop: 'invoiceNo', width: 180 },\r\n  { label: t('invoiceAmount'), prop: 'invoiceMoney', width: 100 },\r\n  { label: t('invoiceTaxAmount'), prop: 'tax', width: 100 },\r\n  { label: t('invoiceTime'), prop: 'makeTime', width: 180 },\r\n  {\r\n    label: t('state'),\r\n    render: ({ row }) =>\r\n      h(\r\n        ElTag,\r\n        {\r\n          type: row.state === 1 ? 'success' : 'danger',\r\n        },\r\n        { default: () => stateType(row.state) }\r\n      ),\r\n  },\r\n  { label: t('remark'), prop: 'remark', width: 180 },\r\n  {\r\n    label: t('operations'),\r\n    width: 140,\r\n    slot: 'operations',\r\n    fixed: 'right',\r\n  },\r\n])\r\n/** 表格加载状态 */\r\nconst loading = ref(false)\r\n/** form提交禁用按钮 */\r\nconst btLoading = ref(false)\r\n/** 获取发票总计金额 */\r\nconst totalInvoiceMoney = ref()\r\n/** 弹窗内容（初始化） */\r\nconst _modelForm = ref<types.invoicedForm>({\r\n  togetherCode: props.togetherCode,\r\n  invoiceTitleType: '0',\r\n  invoiceType: '0',\r\n  invoiceNo: '',\r\n  invoiceMoney: 0,\r\n  invoiceCode: '',\r\n  tax: null,\r\n  invoiceTitle: '',\r\n  taxpayerId: '',\r\n  regAddress: '',\r\n  regPhone: '',\r\n  bank: '',\r\n  bankNo: '',\r\n  remark: '',\r\n  invoicePerson: userStore.username,\r\n  orderNo: props.orderNo,\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n/** 弹窗内容 */\r\nconst modelForm = ref<Record<string, any>>({ ..._modelForm.value, id: '' })\r\n/** 宾客信息下拉框 */\r\nconst togetherOptions = reactive<Record<string, any>[]>([])\r\n/** 发票类别 */\r\nconst invoiceType = reactive([\r\n  { label: t('invoiceTypeObj.VATSpecial'), value: '0' },\r\n  { label: t('invoiceTypeObj.VATGeneral'), value: '1' },\r\n  { label: t('invoiceTypeObj.individualVATGeneral'), value: '2' },\r\n])\r\n/** 发票形式 */\r\nconst invoiceForm = reactive([\r\n  { label: t('invoiceFormObj.paperInvoice'), value: '0' },\r\n  { label: t('invoiceFormObj.electronicInvoice'), value: '1' },\r\n  { label: t('invoiceFormObj.DElectronicInvoice'), value: '2' },\r\n])\r\n/** 发票类别过滤 */\r\nfunction invoiceTypeType(id: string) {\r\n  return invoiceType.filter((e) => id === e.value)[0].label\r\n}\r\n/** 发票形式过滤 */\r\nfunction invoiceFormType(id: string) {\r\n  return invoiceForm.filter((e) => id === e.value)[0].label\r\n}\r\n/** 发票状态 */\r\nfunction stateType(id: string) {\r\n  let txt = ''\r\n  switch (id) {\r\n    case '0':\r\n      txt = t('cancel1')\r\n      break\r\n    case '1':\r\n      txt = t('normal')\r\n      break\r\n  }\r\n  return txt\r\n}\r\n/** 判断开票金额不能小于或等于0 */\r\nfunction checkInvoiceMoney(rule: any, value: any, callback: any) {\r\n  if (value <= '0') {\r\n    callback(new Error(t('pleaseInputInvoiceAmount')))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n/** form表单 */\r\nconst ruleFieldList = reactive<Form.FieldItem[]>([\r\n  {\r\n    label: t('guest'),\r\n    field: 'togetherCode',\r\n    type: 'select',\r\n    options: {\r\n      data: togetherOptions,\r\n    },\r\n  },\r\n  {\r\n    label: t('invoiceType'),\r\n    field: 'invoiceTitleType',\r\n    type: 'radio', // 类型\r\n    rules: [\r\n      {\r\n        required: true,\r\n        message: t('pleaseSelectInvoiceType'),\r\n        trigger: ['change', 'blue'],\r\n      },\r\n    ],\r\n    options: {\r\n      data: invoiceType,\r\n    },\r\n  },\r\n  {\r\n    label: t('invoiceForm'),\r\n    field: 'invoiceType',\r\n    type: 'radio', // 类型\r\n    rules: [\r\n      {\r\n        required: true,\r\n        message: t('pleaseSelectInvoiceForm'),\r\n        trigger: ['change', 'blue'],\r\n      },\r\n    ],\r\n    options: {\r\n      data: invoiceForm,\r\n    },\r\n  },\r\n  {\r\n    label: t('invoiceNumber'),\r\n    field: 'invoiceNo',\r\n    rules: [\r\n      {\r\n        required: true,\r\n        message: t('pleaseInputInvoiceNumber'),\r\n        trigger: ['change', 'blue'],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    label: t('invoiceAmount'),\r\n    field: 'invoiceMoney',\r\n    type: 'number',\r\n    precision: 2,\r\n    rules: [\r\n      {\r\n        required: true,\r\n        trigger: ['change', 'blue'],\r\n        validator: checkInvoiceMoney,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    label: t('invoiceCode'),\r\n    field: 'invoiceCode',\r\n  },\r\n  {\r\n    label: t('invoiceTaxAmount'),\r\n    field: 'tax',\r\n  },\r\n  {\r\n    label: t('invoiceTitle'),\r\n    field: 'invoiceTitle',\r\n  },\r\n  {\r\n    label: t('unitTaxCode'),\r\n    field: 'taxpayerId',\r\n  },\r\n  {\r\n    label: t('registeredAddress'),\r\n    field: 'regAddress',\r\n  },\r\n  {\r\n    label: t('registeredPhone'),\r\n    field: 'regPhone',\r\n  },\r\n  {\r\n    label: t('openingBank'),\r\n    field: 'bank',\r\n  },\r\n  {\r\n    label: t('bankAccount'),\r\n    field: 'bankNo',\r\n  },\r\n  {\r\n    label: t('remark'),\r\n    field: 'remark',\r\n  },\r\n])\r\n/** 开发票提交form */\r\nfunction formSubmit() {\r\n  let apiVal = ''\r\n  if (activeName.value === '1' && !easyFormRefAdd.value.formRef) {\r\n    return\r\n  }\r\n  if (activeName.value === '2' && !easyFormRefUpdate.value.formRef) {\r\n    return\r\n  }\r\n  if (activeName.value === '1') {\r\n    apiVal = 'invoiceCreate'\r\n  }\r\n  if (activeName.value === '2') {\r\n    apiVal = 'getInvoiceUpdate'\r\n  }\r\n  // 1 开票  2 开票记录\r\n  if (activeName.value === '1' && modelForm.value.id) {\r\n    delete modelForm.value.id\r\n  }\r\n  if (activeName.value === '2') {\r\n    easyDialogRef.value.loading = true\r\n  }\r\n  /** 过滤没有填写的字段 */\r\n  const params = Object.keys(modelForm.value).reduce((acc, key) => {\r\n    const value = modelForm.value[key]\r\n    if (value !== null && value !== undefined && value !== '' && value !== false) {\r\n      acc[key] = value\r\n    }\r\n    return acc\r\n  }, {})\r\n  easyFormRefAdd.value.formRef.validate(async (valid: boolean) => {\r\n    if (valid) {\r\n      btLoading.value = true\r\n      await orderApi[apiVal](params)\r\n      btLoading.value = false\r\n      ElMessage.success(t('invoiceSuccess'))\r\n      if (activeName.value === '2') {\r\n        getList()\r\n        easyDialogRef.value.close()\r\n      } else {\r\n        emits('submit')\r\n        emits('close')\r\n      }\r\n    }\r\n  })\r\n}\r\n/** 获取宾客的信息 */\r\nasync function getGuestList() {\r\n  togetherOptions.length = 0\r\n  const { data } = await orderApi.getGuestList(queryParams)\r\n  data.forEach((item) => {\r\n    togetherOptions.push({ label: item.name, value: item.togetherCode })\r\n  })\r\n}\r\n/** 标签切换 */\r\nfunction handleClick(tab: TabsPaneContext, event: Event) {\r\n  resetForm()\r\n  if (tab.props.name === '1') {\r\n    getGuestList()\r\n  }\r\n  if (tab.props.name === '2') {\r\n    getList()\r\n  }\r\n}\r\n/** 获取发票记录 */\r\nasync function getList() {\r\n  loading.value = true\r\n  const { data } = await orderApi.getInvoiceList(queryParams)\r\n  loading.value = false\r\n  tableData.value = data.list\r\n  totalInvoiceMoney.value = data.totalInvoiceMoney\r\n}\r\n/** form表单重置 */\r\nfunction resetForm() {\r\n  if (easyFormRefAdd.value) {\r\n    easyFormRefAdd.value.formRef.resetFields()\r\n  }\r\n  if (easyFormRefUpdate.value) {\r\n    easyFormRefUpdate.value.formRef.resetFields()\r\n  }\r\n  /** 重新赋值 */\r\n  for (const key in _modelForm.value) {\r\n    modelForm.value[key] = _modelForm.value[key]\r\n  }\r\n}\r\n/** 这里的类型是表格配置文件里定义的类型 */\r\nasync function handleAction(command: Table.Command, row: any) {\r\n  /** 修改发票 */\r\n  if (command === 'modify') {\r\n    for (const key in modelForm.value) {\r\n      modelForm.value[key] = row[key]\r\n    }\r\n    modelForm.value.id = row.id\r\n    easyDialogRef.value.show()\r\n  }\r\n  /** 作废发票 */\r\n  if (command === 'delete') {\r\n    await orderApi.getInvoiceUpdateState({ id: row.id })\r\n    getList()\r\n  }\r\n}\r\n/**\r\n * limit或者currentPage改变触发\r\n * @param page 当前页码\r\n * @param limit 当前条数\r\n */\r\nfunction handlePaginationChange(page: number, limit: number) {\r\n  queryParams.pageNo = page\r\n  queryParams.pageSize = limit\r\n  getList()\r\n}\r\nonMounted(() => {\r\n  getGuestList()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"invoiced\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n      <el-tab-pane :label=\"t('issueAnInvoice')\" name=\"1\">\r\n        <easyForm\r\n          ref=\"easyFormRefAdd\"\r\n          class=\"invoiced-form\"\r\n          :field-list=\"ruleFieldList\"\r\n          :model=\"modelForm\"\r\n          :options=\"{\r\n            labelSuffix: '：',\r\n            labelWidth: 160,\r\n          }\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane :label=\"t('invoiceRecords')\" name=\"2\">\r\n        <EasyTable v-loading=\"loading\" :columns=\"tableColumns\" :table-data=\"tableData\" @pagination-change=\"handlePaginationChange\">\r\n          <template #operations=\"{ row }\">\r\n            <ElButton type=\"primary\" link @click=\"handleAction('modify', row)\">\r\n              {{ t('modify') }}\r\n            </ElButton>\r\n            <el-popconfirm :title=\"t('confirm2')\" @confirm=\"handleAction('delete', row)\">\r\n              <template #reference>\r\n                <ElButton link type=\"danger\">\r\n                  {{ t('cancel1') }}\r\n                </ElButton>\r\n              </template>\r\n            </el-popconfirm>\r\n          </template>\r\n        </EasyTable>\r\n        <div class=\"invoiced-money\">\r\n          {{ `${t('amountInvoiced')}：￥${totalInvoiceMoney}` }}\r\n        </div>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <div class=\"invoiced-button\">\r\n      <ElButton @click=\"emits('close')\">\r\n        {{ activeName === '1' ? t('cancel') : t('close') }}\r\n      </ElButton>\r\n      <ElButton v-if=\"activeName === '1'\" :loading=\"btLoading\" type=\"primary\" @click=\"formSubmit\">\r\n        {{ t('confirm') }}\r\n      </ElButton>\r\n    </div>\r\n\r\n    <EasyDialog ref=\"easyDialogRef\" :title=\"t('modify')\" is-body show-cancel-button show-confirm-button dialog-width=\"800\" @submit=\"formSubmit()\" @close=\"resetForm()\">\r\n      <easyForm\r\n        ref=\"easyFormRefUpdate\"\r\n        class=\"invoiced-form\"\r\n        :field-list=\"ruleFieldList\"\r\n        :model=\"modelForm\"\r\n        :options=\"{\r\n          labelSuffix: '：',\r\n          labelWidth: 160,\r\n        }\"\r\n      />\r\n    </EasyDialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.invoiced {\r\n  .invoiced-button {\r\n    text-align: right;\r\n    padding-top: var(--el-dialog-padding-primary);\r\n  }\r\n  .invoiced-money {\r\n    @apply mt-[15px] px-[15px] py-[10px];\r\n    background: rgb(244, 246, 247);\r\n    border: 1px solid var(--el-border-color-light);\r\n  }\r\n}\r\n.invoiced-form {\r\n  :deep(.el-form-item:nth-of-type(n + 4)) {\r\n    width: 50% !important;\r\n    display: inline-flex;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "activeName", "ref", "easyFormRefAdd", "easyFormRefUpdate", "easyDialogRef", "queryParams", "reactive", "gcode", "hcode", "orderNo", "tableData", "tableColumns", "label", "prop", "width", "render", "row", "h", "id", "invoiceTitleType", "invoiceType", "filter", "e", "value", "invoiceForm", "ElTag", "type", "state", "default", "txt", "stateType", "slot", "fixed", "loading", "btLoading", "totalInvoiceMoney", "_modelForm", "togetherCode", "invoiceNo", "invoiceMoney", "invoiceCode", "tax", "invoiceTitle", "taxpayerId", "reg<PERSON><PERSON><PERSON>", "regPhone", "bank", "bankNo", "remark", "invoicePerson", "username", "modelForm", "togetherOptions", "ruleFieldList", "field", "options", "data", "rules", "required", "message", "trigger", "precision", "validator", "rule", "callback", "Error", "formSubmit", "apiVal", "formRef", "params", "Object", "keys", "reduce", "acc", "key", "validate", "async", "valid", "orderApi", "ElMessage", "success", "getList", "close", "getGuestList", "length", "for<PERSON>ach", "item", "push", "name", "handleClick", "tab", "event", "resetForm", "getInvoiceList", "list", "resetFields", "handleAction", "command", "show", "getInvoiceUpdateState", "handlePaginationChange", "page", "limit", "pageNo", "pageSize", "onMounted"], "mappings": "+uDAqJA,MAAMA,EAAQC,EAgBRC,EAAQC,GACRC,EAAEA,GAAMC,IAERC,EAAYC,IAEZC,EAAaC,EAAI,KAEjBC,EAAiBD,IAEjBE,EAAoBF,IAEpBG,EAAgBH,IAEhBI,EAAcC,EAA4B,CAC9CC,MAAOT,EAAUS,MACjBC,MAAOV,EAAUU,MACjBC,QAASjB,EAAMiB,UAGXC,EAAYT,EAA0B,IAEtCU,EAAeV,EAAwC,CAC3D,CAAEW,MAAOhB,EAAE,SAAUiB,KAAM,OAAQC,MAAO,KAC1C,CACEF,MAAOhB,EAAE,eACTkB,MAAO,IACPC,OAAQ,EAAGC,UAAUC,SAAE,QA0EFC,EA1E0BF,EAAIG,iBA2E9CC,EAAYC,QAAQC,GAAMJ,IAAOI,EAAEC,QAAO,GAAGX,QADtD,IAAyBM,CAzEvB,GACA,CACEN,MAAOhB,EAAE,eACTkB,MAAO,IACPC,OAAQ,EAAGC,UAAUC,SAAE,QAyEFC,EAzE0BF,EAAII,YA0E9CI,EAAYH,QAAQC,GAAMJ,IAAOI,EAAEC,QAAO,GAAGX,QADtD,IAAyBM,CAxEvB,GACA,CAAEN,MAAOhB,EAAE,iBAAkBiB,KAAM,YAAaC,MAAO,KACvD,CAAEF,MAAOhB,EAAE,iBAAkBiB,KAAM,eAAgBC,MAAO,KAC1D,CAAEF,MAAOhB,EAAE,oBAAqBiB,KAAM,MAAOC,MAAO,KACpD,CAAEF,MAAOhB,EAAE,eAAgBiB,KAAM,WAAYC,MAAO,KACpD,CACEF,MAAOhB,EAAE,SACTmB,OAAQ,EAAGC,SACTC,EACEQ,EACA,CACEC,KAAoB,IAAdV,EAAIW,MAAc,UAAY,UAEtC,CAAEC,QAAS,IA+DnB,SAAmBV,GACjB,IAAIW,EAAM,GACV,OAAQX,GACN,IAAK,IACHW,EAAMjC,EAAE,WACR,MACF,IAAK,IACHiC,EAAMjC,EAAE,UAGL,OAAAiC,CAAA,CAzEgBC,CAAUd,EAAIW,UAGrC,CAAEf,MAAOhB,EAAE,UAAWiB,KAAM,SAAUC,MAAO,KAC7C,CACEF,MAAOhB,EAAE,cACTkB,MAAO,IACPiB,KAAM,aACNC,MAAO,WAILC,EAAUhC,GAAI,GAEdiC,EAAYjC,GAAI,GAEhBkC,EAAoBlC,IAEpBmC,EAAanC,EAAwB,CACzCoC,aAAc7C,EAAM6C,aACpBlB,iBAAkB,IAClBC,YAAa,IACbkB,UAAW,GACXC,aAAc,EACdC,YAAa,GACbC,IAAK,KACLC,aAAc,GACdC,WAAY,GACZC,WAAY,GACZC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,OAAQ,GACRC,cAAenD,EAAUoD,SACzBzC,QAASjB,EAAMiB,QACfF,MAAOT,EAAUS,MACjBC,MAAOV,EAAUU,QAGb2C,EAAYlD,EAAyB,IAAKmC,EAAWb,MAAOL,GAAI,KAEhEkC,EAAkB9C,EAAgC,IAElDc,EAAcd,EAAS,CAC3B,CAAEM,MAAOhB,EAAE,6BAA8B2B,MAAO,KAChD,CAAEX,MAAOhB,EAAE,6BAA8B2B,MAAO,KAChD,CAAEX,MAAOhB,EAAE,uCAAwC2B,MAAO,OAGtDC,EAAclB,EAAS,CAC3B,CAAEM,MAAOhB,EAAE,+BAAgC2B,MAAO,KAClD,CAAEX,MAAOhB,EAAE,oCAAqC2B,MAAO,KACvD,CAAEX,MAAOhB,EAAE,qCAAsC2B,MAAO,OAgC1D,MAAM8B,EAAgB/C,EAA2B,CAC/C,CACEM,MAAOhB,EAAE,SACT0D,MAAO,eACP5B,KAAM,SACN6B,QAAS,CACPC,KAAMJ,IAGV,CACExC,MAAOhB,EAAE,eACT0D,MAAO,mBACP5B,KAAM,QACN+B,MAAO,CACL,CACEC,UAAU,EACVC,QAAS/D,EAAE,2BACXgE,QAAS,CAAC,SAAU,UAGxBL,QAAS,CACPC,KAAMpC,IAGV,CACER,MAAOhB,EAAE,eACT0D,MAAO,cACP5B,KAAM,QACN+B,MAAO,CACL,CACEC,UAAU,EACVC,QAAS/D,EAAE,2BACXgE,QAAS,CAAC,SAAU,UAGxBL,QAAS,CACPC,KAAMhC,IAGV,CACEZ,MAAOhB,EAAE,iBACT0D,MAAO,YACPG,MAAO,CACL,CACEC,UAAU,EACVC,QAAS/D,EAAE,4BACXgE,QAAS,CAAC,SAAU,WAI1B,CACEhD,MAAOhB,EAAE,iBACT0D,MAAO,eACP5B,KAAM,SACNmC,UAAW,EACXJ,MAAO,CACL,CACEC,UAAU,EACVE,QAAS,CAAC,SAAU,QACpBE,UAnEC,SAAkBC,EAAWxC,EAAYyC,GAC5CzC,GAAS,IACXyC,EAAS,IAAIC,MAAMrE,EAAE,8BAEZoE,GACX,KAkEA,CACEpD,MAAOhB,EAAE,eACT0D,MAAO,eAET,CACE1C,MAAOhB,EAAE,oBACT0D,MAAO,OAET,CACE1C,MAAOhB,EAAE,gBACT0D,MAAO,gBAET,CACE1C,MAAOhB,EAAE,eACT0D,MAAO,cAET,CACE1C,MAAOhB,EAAE,qBACT0D,MAAO,cAET,CACE1C,MAAOhB,EAAE,mBACT0D,MAAO,YAET,CACE1C,MAAOhB,EAAE,eACT0D,MAAO,QAET,CACE1C,MAAOhB,EAAE,eACT0D,MAAO,UAET,CACE1C,MAAOhB,EAAE,UACT0D,MAAO,YAIX,SAASY,KACP,IAAIC,EAAS,GACb,GAAyB,MAArBnE,EAAWuB,QAAkBrB,EAAeqB,MAAM6C,QACpD,OAEF,GAAyB,MAArBpE,EAAWuB,QAAkBpB,EAAkBoB,MAAM6C,QACvD,OAEuB,MAArBpE,EAAWuB,QACJ4C,EAAA,iBAEc,MAArBnE,EAAWuB,QACJ4C,EAAA,oBAGc,MAArBnE,EAAWuB,OAAiB4B,EAAU5B,MAAML,WACvCiC,EAAU5B,MAAML,GAEA,MAArBlB,EAAWuB,QACbnB,EAAcmB,MAAMU,SAAU,GAG1B,MAAAoC,EAASC,OAAOC,KAAKpB,EAAU5B,OAAOiD,QAAO,CAACC,EAAKC,KACjD,MAAAnD,EAAQ4B,EAAU5B,MAAMmD,GAIvB,OAHHnD,SAAmD,KAAVA,IAA0B,IAAVA,IAC3DkD,EAAIC,GAAOnD,GAENkD,CAAA,GACN,IACHvE,EAAeqB,MAAM6C,QAAQO,UAASC,MAAOC,IACvCA,IACF3C,EAAUX,OAAQ,QACZuD,EAASX,GAAQE,GACvBnC,EAAUX,OAAQ,EACRwD,EAAAC,QAAQpF,EAAE,mBACK,MAArBI,EAAWuB,OACL0D,KACR7E,EAAcmB,MAAM2D,UAEpBxF,EAAM,UACNA,EAAM,UACR,GAEH,CAGHkF,eAAeO,KACb/B,EAAgBgC,OAAS,EACzB,MAAM5B,KAAEA,SAAesB,EAASK,aAAa9E,GACxCmD,EAAA6B,SAASC,IACIlC,EAAAmC,KAAK,CAAE3E,MAAO0E,EAAKE,KAAMjE,MAAO+D,EAAKjD,cAAc,GACpE,CAGM,SAAAoD,GAAYC,EAAsBC,GAC/BC,KACa,MAAnBF,EAAIlG,MAAMgG,MACCL,KAEQ,MAAnBO,EAAIlG,MAAMgG,MACJP,IACV,CAGFL,eAAeK,KACbhD,EAAQV,OAAQ,EAChB,MAAMiC,KAAEA,SAAesB,EAASe,eAAexF,GAC/C4B,EAAQV,OAAQ,EAChBb,EAAUa,MAAQiC,EAAKsC,KACvB3D,EAAkBZ,MAAQiC,EAAKrB,iBAAA,CAGjC,SAASyD,KACH1F,EAAeqB,OACFrB,EAAAqB,MAAM6C,QAAQ2B,cAE3B5F,EAAkBoB,OACFpB,EAAAoB,MAAM6C,QAAQ2B,cAGvB,IAAA,MAAArB,KAAOtC,EAAWb,MAC3B4B,EAAU5B,MAAMmD,GAAOtC,EAAWb,MAAMmD,EAC1C,CAGaE,eAAAoB,GAAaC,EAAwBjF,GAElD,GAAgB,WAAZiF,EAAsB,CACb,IAAA,MAAAvB,KAAOvB,EAAU5B,MAC1B4B,EAAU5B,MAAMmD,GAAO1D,EAAI0D,GAEnBvB,EAAA5B,MAAML,GAAKF,EAAIE,GACzBd,EAAcmB,MAAM2E,MAAK,CAGX,WAAZD,UACInB,EAASqB,sBAAsB,CAAEjF,GAAIF,EAAIE,KACvC+D,KACV,CAOO,SAAAmB,GAAuBC,EAAcC,GAC5CjG,EAAYkG,OAASF,EACrBhG,EAAYmG,SAAWF,EACfrB,IAAA,QAEVwB,GAAU,KACKtB,IAAA"}