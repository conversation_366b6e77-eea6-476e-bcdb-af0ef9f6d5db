{"version": 3, "file": "rightSide.vue_vue_type_script_setup_true_lang-DsMBvNXs.js", "sources": ["../../src/layouts/components/Topbar/Toolbar/rightSide.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport useApi from '@/api/modules/user.api'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\nimport eventBus from '@/utils/eventBus'\r\n\r\nimport Tools from './tools.vue'\r\n\r\ndefineOptions({\r\n  name: 'ToolbarRightSide',\r\n})\r\n\r\nconst router = useRouter()\r\n\r\nconst settingsStore = useSettingsStore()\r\nconst userStore = useUserStore()\r\n\r\nconst { t } = useI18n()\r\nconst { generateI18nTitle } = useMenu()\r\n\r\nconst avatarError = ref(false)\r\nwatch(\r\n  () => userStore.avatar,\r\n  () => {\r\n    if (avatarError.value) {\r\n      avatarError.value = false\r\n    }\r\n  },\r\n)\r\n\r\nfunction exit() {\r\n  useApi.logout().then((res: any) => {\r\n    if (res.code === 0) {\r\n      userStore.logout()\r\n    }\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"flex items-center\">\r\n    <Tools mode=\"right-side\" />\r\n    <HDropdownMenu\r\n      :items=\"[\r\n        [\r\n          {\r\n            label: generateI18nTitle(settingsStore.settings.home.title),\r\n            handle: () =>\r\n              router.push({ path: settingsStore.settings.home.fullPath }),\r\n            hide: !settingsStore.settings.home.enable,\r\n          },\r\n          {\r\n            label: t('app.profile'),\r\n            handle: () => router.push({ name: 'personalSetting' }),\r\n          },\r\n          {\r\n            label: t('app.preferences'),\r\n            handle: () => eventBus.emit('global-preferences-toggle'),\r\n            hide: !settingsStore.settings.userPreferences.enable,\r\n          },\r\n        ],\r\n        [\r\n          {\r\n            label: t('app.hotkeys'),\r\n            handle: () => eventBus.emit('global-hotkeys-intro-toggle'),\r\n            hide: settingsStore.mode !== 'pc',\r\n          },\r\n        ],\r\n        [{ label: t('app.logout'), handle: () => exit() }],\r\n      ]\"\r\n      class=\"flex-center cursor-pointer px-2\"\r\n    >\r\n      <div class=\"flex-center gap-1\">\r\n        <img\r\n          v-if=\"userStore.avatar && !avatarError\"\r\n          :src=\"userStore.avatar\"\r\n          :onerror=\"() => (avatarError = true)\"\r\n          class=\"h-[24px] w-[24px] rounded-full\"\r\n        >\r\n        <SvgIcon\r\n          v-else\r\n          name=\"i-carbon:user-avatar-filled-alt\"\r\n          :size=\"24\"\r\n          class=\"text-gray-400\"\r\n        />\r\n        {{ userStore.account }}\r\n        <SvgIcon name=\"i-ep:caret-bottom\" />\r\n      </div>\r\n    </HDropdownMenu>\r\n  </div>\r\n</template>\r\n"], "names": ["router", "useRouter", "settingsStore", "useSettingsStore", "userStore", "useUserStore", "t", "useI18n", "generateI18nTitle", "useMenu", "avatar<PERSON><PERSON><PERSON>", "ref", "watch", "avatar", "value", "useApi", "logout", "then", "res", "code"], "mappings": "4dAYA,MAAMA,EAASC,IAETC,EAAgBC,IAChBC,EAAYC,KAEZC,EAAEA,GAAMC,KACRC,kBAAEA,GAAsBC,IAExBC,EAAcC,GAAI,UACxBC,GACE,IAAMR,EAAUS,SAChB,KACMH,EAAYI,QACdJ,EAAYI,OAAQ,EAAA,8iBAMxBC,EAAOC,SAASC,MAAMC,IACH,IAAbA,EAAIC,MACNf,EAAUY,QAAO"}