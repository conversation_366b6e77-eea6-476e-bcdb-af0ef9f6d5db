import{d as e,b as a,b9 as t,ba as l,bb as n,B as s,bc as r,af as u,y as o,D as i,z as d,A as v,bd as c,be as p,F as b,bf as f,ae as m,ap as x,bg as g,bh as h,bi as y,bj as I,bk as k,bl as w,bm as S,a1 as T,a2 as A,o as P,e as F,w as O,f as E,u as D,c as j,ag as L,g as B,a7 as _,R as q,h as C,Y as G,_ as z}from"./index-CkEhI1Zk.js";import{s as M}from"./use-resolve-button-type-CUES3UUR.js";let N=e({props:{onFocus:{type:Function,required:!0}},setup(e){let s=a(!0);return()=>s.value?t(n,{as:"button",type:"button",features:l.Focusable,onFocus(a){a.preventDefault();let t,l=50;t=requestAnimationFrame((function a(){var n;if(!(l--<=0))return null!=(n=e.onFocus)&&n.call(e)?(s.value=!1,void cancelAnimationFrame(t)):void(t=requestAnimationFrame(a));t&&cancelAnimationFrame(t)}))}}):null}});var W,R,V=((R=V||{})[R.Forwards=0]="Forwards",R[R.Backwards=1]="Backwards",R),$=((W=$||{})[W.Less=-1]="Less",W[W.Equal=0]="Equal",W[W.Greater=1]="Greater",W);let H=Symbol("TabsContext");function U(e){let a=x(H,null);if(null===a){let a=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,U),a}return a}let K=Symbol("TabsSSRContext"),Y=e({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:l,attrs:n,emit:f}){var m;let x=a(null!=(m=e.selectedIndex)?m:e.defaultIndex),h=a([]),y=a([]),I=s((()=>null!==e.selectedIndex)),k=s((()=>I.value?e.selectedIndex:x.value));function w(e){var a;let t=r(S.tabs.value,v),l=r(S.panels.value,v),n=t.filter((e=>{var a;return!(null!=(a=v(e))&&a.hasAttribute("disabled"))}));if(e<0||e>t.length-1){let a=g(null===x.value?0:Math.sign(e-x.value),{[-1]:()=>1,0:()=>g(Math.sign(e),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0}),s=g(a,{0:()=>t.indexOf(n[0]),1:()=>t.indexOf(n[n.length-1])});-1!==s&&(x.value=s),S.tabs.value=t,S.panels.value=l}else{let s=t.slice(0,e),r=[...t.slice(e),...s].find((e=>n.includes(e)));if(!r)return;let u=null!=(a=t.indexOf(r))?a:S.selectedIndex.value;-1===u&&(u=S.selectedIndex.value),x.value=u,S.tabs.value=t,S.panels.value=l}}let S={selectedIndex:s((()=>{var a,t;return null!=(t=null!=(a=x.value)?a:e.defaultIndex)?t:null})),orientation:s((()=>e.vertical?"vertical":"horizontal")),activation:s((()=>e.manual?"manual":"auto")),tabs:h,panels:y,setSelectedIndex(e){k.value!==e&&f("change",e),I.value||w(e)},registerTab(e){var a;if(h.value.includes(e))return;let t=h.value[x.value];if(h.value.push(e),h.value=r(h.value,v),!I.value){let e=null!=(a=h.value.indexOf(t))?a:x.value;-1!==e&&(x.value=e)}},unregisterTab(e){let a=h.value.indexOf(e);-1!==a&&h.value.splice(a,1)},registerPanel(e){y.value.includes(e)||(y.value.push(e),y.value=r(y.value,v))},unregisterPanel(e){let a=y.value.indexOf(e);-1!==a&&y.value.splice(a,1)}};u(H,S);let T=a({tabs:[],panels:[]}),A=a(!1);o((()=>{A.value=!0})),u(K,s((()=>A.value?null:T.value)));let P=s((()=>e.selectedIndex));return o((()=>{i([P],(()=>{var a;return w(null!=(a=e.selectedIndex)?a:e.defaultIndex)}),{immediate:!0})})),d((()=>{if(!I.value||null==k.value||S.tabs.value.length<=0)return;let e=r(S.tabs.value,v);e.some(((e,a)=>v(S.tabs.value[a])!==v(e)))&&S.setSelectedIndex(e.findIndex((e=>v(e)===v(S.tabs.value[k.value]))))})),()=>{let a={selectedIndex:x.value};return t(b,[h.value.length<=0&&t(N,{onFocus:()=>{for(let e of h.value){let a=v(e);if(0===(null==a?void 0:a.tabIndex))return a.focus(),!0}return!1}}),c({theirProps:{...n,...p(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])},ourProps:{},slot:a,slots:l,attrs:n,name:"TabGroup"})])}}}),J=e({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:a,slots:t}){let l=U("TabList");return()=>{let n={selectedIndex:l.selectedIndex.value},s={role:"tablist","aria-orientation":l.orientation.value};return c({ourProps:s,theirProps:e,slot:n,attrs:a,slots:t,name:"TabList"})}}}),Q=e({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var r;let u=null!=(r=e.id)?r:`headlessui-tabs-tab-${f()}`,i=U("Tab"),d=a(null);n({el:d,$el:d}),o((()=>i.registerTab(d))),m((()=>i.unregisterTab(d)));let p=x(K),b=s((()=>{if(p.value){let e=p.value.tabs.indexOf(u);return-1===e?p.value.tabs.push(u)-1:e}return-1})),T=s((()=>{let e=i.tabs.value.indexOf(d);return-1===e?b.value:e})),A=s((()=>T.value===i.selectedIndex.value));function P(e){var a;let t=e();if(t===y.Success&&"auto"===i.activation.value){let e=null==(a=k(d))?void 0:a.activeElement,t=i.tabs.value.findIndex((a=>v(a)===e));-1!==t&&i.setSelectedIndex(t)}return t}function F(e){let a=i.tabs.value.map((e=>v(e))).filter(Boolean);if(e.key===h.Space||e.key===h.Enter)return e.preventDefault(),e.stopPropagation(),void i.setSelectedIndex(T.value);switch(e.key){case h.Home:case h.PageUp:return e.preventDefault(),e.stopPropagation(),P((()=>w(a,S.First)));case h.End:case h.PageDown:return e.preventDefault(),e.stopPropagation(),P((()=>w(a,S.Last)))}return P((()=>g(i.orientation.value,{vertical:()=>e.key===h.ArrowUp?w(a,S.Previous|S.WrapAround):e.key===h.ArrowDown?w(a,S.Next|S.WrapAround):y.Error,horizontal:()=>e.key===h.ArrowLeft?w(a,S.Previous|S.WrapAround):e.key===h.ArrowRight?w(a,S.Next|S.WrapAround):y.Error})))===y.Success?e.preventDefault():void 0}let O=a(!1);function E(){var a;O.value||(O.value=!0,!e.disabled&&(null==(a=v(d))||a.focus({preventScroll:!0}),i.setSelectedIndex(T.value),I((()=>{O.value=!1}))))}function D(e){e.preventDefault()}let j=M(s((()=>({as:e.as,type:t.type}))),d);return()=>{var a,n;let s={selected:A.value,disabled:null!=(a=e.disabled)&&a},{...r}=e,o={ref:d,onKeydown:F,onMousedown:D,onClick:E,id:u,role:"tab",type:j.value,"aria-controls":null==(n=v(i.panels.value[T.value]))?void 0:n.id,"aria-selected":A.value,tabIndex:A.value?0:-1,disabled:!!e.disabled||void 0};return c({ourProps:o,theirProps:r,slot:s,attrs:t,slots:l,name:"Tab"})}}});const X=e({__name:"HTabList",props:T({options:{}},{modelValue:{},modelModifiers:{}}),emits:T(["change"],["update:modelValue"]),setup(e,{emit:a}){const t=e,l=a,n=A(e,"modelValue"),r=s({get:()=>t.options.findIndex((e=>e.value===n.value)),set(e){n.value=t.options[e].value}});function u(e){n.value=t.options[e].value}return i(n,(e=>{e&&l("change",e)})),(e,a)=>{const t=z;return P(),F(D(Y),{"selected-index":D(r),onChange:u},{default:O((()=>[E(D(J),{class:"inline-flex select-none items-center justify-center rounded-md bg-stone-1 p-1 ring-1 ring-stone-2 dark-bg-stone-9 dark-ring-stone-8"},{default:O((()=>[(P(!0),j(b,null,L(e.options,((e,a)=>(P(),F(D(Q),{key:a,as:"template"},{default:O((({selected:a})=>[B("button",{class:_(["w-full inline-flex items-center justify-center gap-1 whitespace-nowrap border-size-0 rounded-md bg-inherit px-2 py-1.5 text-sm text-dark ring-stone-2 ring-inset dark-text-white focus-outline-none focus-ring-2 dark-ring-stone-8",{"cursor-default bg-white dark-bg-dark-9":a,"cursor-pointer opacity-50 hover-opacity-100":!a}])},[e.icon?(P(),F(t,{key:0,name:e.icon,class:"flex-shrink-0"},null,8,["name"])):q("",!0),C(" "+G(e.label),1)],2)])),_:2},1024)))),128))])),_:1})])),_:1},8,["selected-index"])}}});export{X as _};
//# sourceMappingURL=HTabList.vue_vue_type_script_setup_true_lang-CHW-QDXA.js.map
