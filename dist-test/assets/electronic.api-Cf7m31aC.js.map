{"version": 3, "file": "electronic.api-Cf7m31aC.js", "sources": ["../../src/api/modules/pms/sell/house/electronic.api.ts"], "sourcesContent": ["import api from '@/api/index'\r\n\r\nconst BASE_PATH = 'admin-api/pms/price-panel'\r\n/**\r\n * 获得房价牌背景图片列表\r\n */\r\nexport function getPricePanelBackgroundList(params: hgCode) {\r\n  return api.get(`admin-api/pms/price-panel-background/list`, {\r\n    params: params,\r\n  })\r\n}\r\n/** 获得房价牌基础信息 */\r\nexport function getPricePanel(params: hgCode) {\r\n  return api.get(`${BASE_PATH}/get`, {\r\n    params: params,\r\n  })\r\n}\r\n/** 创建(更新)房价牌基础信息 */\r\nexport function createOrUpdateBase(params: any) {\r\n  return api.post(`${BASE_PATH}/create-or-update-base`, params)\r\n}\r\n/** 获得房价牌房价 */\r\nexport function getPriceList(params: any) {\r\n  return api.get(`${BASE_PATH}/get-price`, {\r\n    params: params,\r\n  })\r\n}\r\n/** 创建(更新)房价牌房价设置 */\r\nexport function updatePricePanel(params: any) {\r\n  return api.post(`${BASE_PATH}/update`, params)\r\n}\r\n/** 获得房价牌(自定义房型房价)默认房价 */\r\nexport function getPricePanelCustom(params: any) {\r\n  return api.get(`${BASE_PATH}/get-price-panel-custom`, {\r\n    params: params,\r\n  })\r\n}\r\n/** 创建房价牌(自定义房型房价) */\r\nexport function createPricePanelCustom(params: any) {\r\n  return api.post(`${BASE_PATH}/create-price-panel-custom`, params)\r\n}\r\n/** 修改房价牌(自定义房型房价) */\r\nexport function updatePricePanelCustom(params: any) {\r\n  return api.post(`${BASE_PATH}/update-price-panel-custom`, params)\r\n}\r\n/** 删除房价牌(自定义房型房价) */\r\nexport function deletePricePanelCustom(params: any) {\r\n  return api.delete(`${BASE_PATH}/delete-price-panel-custom`, {\r\n    params: params,\r\n  })\r\n}\r\n"], "names": ["BASE_PATH", "getPricePanelBackgroundList", "params", "api", "get", "getPricePanel", "createOrUpdateBase", "post", "getPriceList", "updatePricePanel", "getPricePanelCustom", "createPricePanelCustom", "updatePricePanelCustom", "deletePricePanelCustom", "delete"], "mappings": "wCAEA,MAAMA,EAAY,4BAIX,SAASC,EAA4BC,GACnC,OAAAC,EAAIC,IAAI,4CAA6C,CAC1DF,UAEJ,CAEO,SAASG,EAAcH,GAC5B,OAAOC,EAAIC,IAAI,GAAGJ,QAAiB,CACjCE,UAEJ,CAEO,SAASI,EAAmBJ,GACjC,OAAOC,EAAII,KAAK,GAAGP,0BAAmCE,EACxD,CAEO,SAASM,EAAaN,GAC3B,OAAOC,EAAIC,IAAI,GAAGJ,cAAuB,CACvCE,UAEJ,CAEO,SAASO,EAAiBP,GAC/B,OAAOC,EAAII,KAAK,GAAGP,WAAoBE,EACzC,CAEO,SAASQ,EAAoBR,GAClC,OAAOC,EAAIC,IAAI,GAAGJ,2BAAoC,CACpDE,UAEJ,CAEO,SAASS,EAAuBT,GACrC,OAAOC,EAAII,KAAK,GAAGP,8BAAuCE,EAC5D,CAEO,SAASU,EAAuBV,GACrC,OAAOC,EAAII,KAAK,GAAGP,8BAAuCE,EAC5D,CAEO,SAASW,EAAuBX,GACrC,OAAOC,EAAIW,OAAO,GAAGd,8BAAuC,CAC1DE,UAEJ"}