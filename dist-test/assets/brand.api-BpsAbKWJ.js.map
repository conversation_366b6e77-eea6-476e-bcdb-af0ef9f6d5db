{"version": 3, "file": "brand.api-BpsAbKWJ.js", "sources": ["../../src/api/modules/system/group/brand.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/system/group-brand'\r\n/**\r\n * 品牌接口\r\n */\r\nexport default {\r\n  /**\r\n   * 品牌信息\r\n   * @param gcode\r\n   */\r\n  getGroupBrands: (gcode: string) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: {\r\n        gcode,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 品牌详情\r\n   * @param brandCode\r\n   */\r\n  getGroupBrand: (brandCode: string) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        brandCode,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 删除集团品牌\r\n   * @param brandCode\r\n   */\r\n  deleteGroupBrand: (brandCode: string) =>\r\n    api.delete(`${BASE_PATH}/delete`, {\r\n      params: { brandCode },\r\n    }),\r\n\r\n  /**\r\n   * 创建品牌\r\n   * @param data\r\n   */\r\n  createGroupBrand: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n\r\n  /**\r\n   * 修改品牌\r\n   * @param data\r\n   */\r\n  updateGroupBrand: (data: any) => api.put(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 修改品牌状态\r\n   * @param data\r\n   */\r\n  updateGroupBrandStatus: (data: {\r\n    brandCode: string\r\n    isEnable: string\r\n  }) => api.put(`${BASE_PATH}/update-status`, data, {}),\r\n}\r\n"], "names": ["BASE_PATH", "brandApi", "getGroupBrands", "gcode", "api", "get", "params", "getGroupBrand", "brandCode", "deleteGroupBrand", "delete", "createGroupBrand", "data", "post", "updateGroupBrand", "put", "updateGroupBrandStatus"], "mappings": "mCAEA,MAAMA,EAAY,+BAIHC,EAAA,CAKbC,eAAiBC,GACfC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQ,CACNH,WAQNI,cAAgBC,GACdJ,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CACNE,eAQNC,iBAAmBD,GACjBJ,EAAIM,OAAO,GAAGV,WAAoB,CAChCM,OAAQ,CAAEE,eAOdG,iBAAmBC,GAAcR,EAAIS,KAAK,GAAGb,WAAoBY,EAAM,IAMvEE,iBAAmBF,GAAcR,EAAIW,IAAI,GAAGf,WAAoBY,EAAM,IAMtEI,uBAAyBJ,GAGnBR,EAAIW,IAAI,GAAGf,kBAA2BY,EAAM,CAAE"}