import{d as l,r as e,b as a,o as t,e as o,w as i,f as d,u,g as n,h as s,i as r,c as m,F as p,E as c,j as f,k as b,l as y,m as g,n as v,p as V,q as h,s as _,t as k,v as x,x as j}from"./index-CkEhI1Zk.js";/* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css               *//* empty css                 *//* empty css                        *//* empty css                  *//* empty css                          */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                  *//* empty css                 */const w={style:{display:"flex"}},C={style:{display:"flex"}},U={style:{width:"600px","margin-left":"150px","text-align":"right"}},E={style:{display:"flex","align-items":"center","justify-content":"space-between"}},z={style:{width:"600px","margin-left":"150px","text-align":"right"}},D=l({__name:"whole",setup(l){const D=e({name:"",region:"",date1:"",date2:"",delivery:!1,type:[],resource:"",desc:""}),I=a(1);function R(){console.log("submit!")}const T=a([{Id:0x5bb9c7fccc001,DebitTypeValue:"D1000",VatRate:"30%"},{Id:0x5bb9c7fccc002,DebitTypeValue:"D1001",VatRate:"40%"}]);return(l,e)=>{const a=c,q=f,F=b,A=y,B=g,G=v,H=V,J=h,K=_,L=k,M=x,N=j;return t(),o(N,{model:u(D),"label-width":"auto"},{default:i((()=>[d(B,{label:"会员信息共享范围"},{default:i((()=>[d(A,{modelValue:u(D).resource,"onUpdate:modelValue":e[1]||(e[1]=l=>u(D).resource=l)},{default:i((()=>[n("div",null,[n("div",null,[d(a,{value:"1不共享"})]),n("div",null,[d(a,{value:"2共享给所有门店"})]),n("div",w,[d(a,{value:"3共享给所有组织门店"}),d(F,{modelValue:u(D).region,"onUpdate:modelValue":e[0]||(e[0]=l=>u(D).region=l),style:{width:"150px"},placeholder:"选择"},{default:i((()=>[d(q,{label:"组织1",value:"shanghai"}),d(q,{label:"组织2",value:"beijing"})])),_:1},8,["modelValue"])])])])),_:1},8,["modelValue"])])),_:1}),d(B,{label:"会员余额积分共享范围"},{default:i((()=>[d(A,{modelValue:u(D).resource,"onUpdate:modelValue":e[4]||(e[4]=l=>u(D).resource=l)},{default:i((()=>[n("div",null,[n("div",null,[d(a,{value:"1不共享"})]),n("div",null,[d(a,{value:"2共享给所有门店"}),d(H,{modelValue:u(D).type,"onUpdate:modelValue":e[2]||(e[2]=l=>u(D).type=l)},{default:i((()=>[d(G,{label:"直营",name:"type"}),d(G,{label:"加盟",name:"type"}),d(G,{label:"托管",name:"type"})])),_:1},8,["modelValue"])]),n("div",C,[d(a,{value:"3共享给所有组织门店"}),d(F,{modelValue:u(D).region,"onUpdate:modelValue":e[3]||(e[3]=l=>u(D).region=l),style:{width:"150px"},placeholder:"选择"},{default:i((()=>[d(q,{label:"组织1",value:"shanghai"}),d(q,{label:"组织2",value:"beijing"})])),_:1},8,["modelValue"])])])])),_:1},8,["modelValue"])])),_:1}),d(B,{label:"会员储值金额入账"},{default:i((()=>[d(A,{modelValue:u(D).resource,"onUpdate:modelValue":e[5]||(e[5]=l=>u(D).resource=l)},{default:i((()=>[n("div",null,[n("div",null,[d(a,{value:"入账到门店"})]),n("div",null,[d(a,{value:"入账到集团"})])])])),_:1},8,["modelValue"])])),_:1}),d(B,{label:"会员储值提成和报表"}),d(B,{label:"提成模式"},{default:i((()=>[n("div",null,[n("div",null,[d(A,{modelValue:u(D).resource,"onUpdate:modelValue":e[6]||(e[6]=l=>u(D).resource=l)},{default:i((()=>[d(a,{label:"按储值金额比例提成"}),d(a,{label:"按储值金额范围提成"})])),_:1},8,["modelValue"])]),e[13]||(e[13]=n("div",null,[s("报表：门店-会员储值明细报表 "),n("span",null,"报表号 mdhy00000012")],-1))])])),_:1}),n("div",U,[d(B,null,{default:i((()=>[d(J,{type:"danger",onClick:R},{default:i((()=>e[14]||(e[14]=[s(" 新增 ")]))),_:1})])),_:1})]),d(M,{"header-cell-style":{background:"#f5f7fa",color:"#606266"},data:u(T),height:"100%",style:{width:"800px","margin-left":"150px"}},{default:i((()=>[d(L,{label:"储值规则",align:"left",width:"350"},{default:i((l=>[n("div",E,[d(K,{modelValue:u(I),"onUpdate:modelValue":e[7]||(e[7]=l=>r(I)?I.value=l:null),min:1,max:10,controls:!1,placeholder:"请输入金额"},null,8,["modelValue"]),e[15]||(e[15]=s(" 至 ")),d(K,{modelValue:u(I),"onUpdate:modelValue":e[8]||(e[8]=l=>r(I)?I.value=l:null),min:1,max:10,controls:!1,placeholder:"请输入金额"},null,8,["modelValue"])])])),_:1}),d(L,{label:"储值提成",align:"left"},{default:i((l=>[n("div",null,[e[16]||(e[16]=s(" 提成 ")),d(K,{modelValue:u(I),"onUpdate:modelValue":e[9]||(e[9]=l=>r(I)?I.value=l:null),min:1,max:10,controls:!1,placeholder:"请输入金额"},null,8,["modelValue"]),e[17]||(e[17]=s("% "))])])),_:1}),d(L,{label:"操作",align:"left",fixed:"right"},{default:i((a=>[a.row.isEdit?(t(),m(p,{key:0},[d(J,{type:"primary",plain:"",size:"small",onClick:e=>l.onEdit(a.row)},{default:i((()=>e[18]||(e[18]=[s(" 修改 ")]))),_:2},1032,["onClick"]),d(J,{plain:"",size:"small",onClick:l=>a.row.isEdit=!1},{default:i((()=>e[19]||(e[19]=[s(" 取消 ")]))),_:2},1032,["onClick"])],64)):(t(),m(p,{key:1},[d(J,{type:"primary",plain:"",size:"small",onClick:l=>a.row.isEdit=!0},{default:i((()=>e[20]||(e[20]=[s(" 编辑 ")]))),_:2},1032,["onClick"]),d(J,{type:"primary",plain:"",size:"small",onClick:l=>a.row.isEdit=!0},{default:i((()=>e[21]||(e[21]=[s(" 删除 ")]))),_:2},1032,["onClick"])],64))])),_:1})])),_:1},8,["data"]),d(B,{label:"会员办卡提成规则"}),d(B,{label:"提成模式"},{default:i((()=>[n("div",null,[n("div",null,[d(A,{modelValue:u(D).resource,"onUpdate:modelValue":e[10]||(e[10]=l=>u(D).resource=l)},{default:i((()=>[d(a,{label:"按储值金额比例提成"}),d(a,{label:"按储值金额范围提成"})])),_:1},8,["modelValue"])]),e[22]||(e[22]=n("div",null,[s("报表：门店-会员储值明细报表 "),n("span",null,"报表号 mdhy00000012")],-1))])])),_:1}),n("div",z,[d(B,null,{default:i((()=>[d(J,{type:"danger",onClick:R},{default:i((()=>e[23]||(e[23]=[s(" 新增 ")]))),_:1})])),_:1})]),d(M,{"header-cell-style":{background:"#f5f7fa",color:"#606266"},data:u(T),height:"100%",style:{width:"800px","margin-left":"150px"}},{default:i((()=>[d(L,{label:"会员等级",align:"left",width:"350"},{default:i((l=>[d(F,{modelValue:u(D).region,"onUpdate:modelValue":e[11]||(e[11]=l=>u(D).region=l),placeholder:"选择"},{default:i((()=>[d(q,{label:"金卡",value:"shanghai"}),d(q,{label:"钻石卡",value:"beijing"}),d(q,{label:"黑金卡",value:"shanghai"})])),_:1},8,["modelValue"])])),_:1}),d(L,{label:"提成金额",align:"left"},{default:i((l=>[n("div",null,[e[24]||(e[24]=s(" 提成 ")),d(K,{modelValue:u(I),"onUpdate:modelValue":e[12]||(e[12]=l=>r(I)?I.value=l:null),min:1,max:10,controls:!1,placeholder:"请输入金额"},null,8,["modelValue"]),e[25]||(e[25]=s("% "))])])),_:1}),d(L,{label:"操作",align:"left",fixed:"right"},{default:i((a=>[a.row.isEdit?(t(),m(p,{key:0},[d(J,{type:"primary",plain:"",size:"small",onClick:e=>l.onEdit(a.row)},{default:i((()=>e[26]||(e[26]=[s(" 修改 ")]))),_:2},1032,["onClick"]),d(J,{plain:"",size:"small",onClick:l=>a.row.isEdit=!1},{default:i((()=>e[27]||(e[27]=[s(" 取消 ")]))),_:2},1032,["onClick"])],64)):(t(),m(p,{key:1},[d(J,{type:"primary",plain:"",size:"small",onClick:l=>a.row.isEdit=!0},{default:i((()=>e[28]||(e[28]=[s(" 编辑 ")]))),_:2},1032,["onClick"]),d(J,{type:"primary",plain:"",size:"small",onClick:l=>a.row.isEdit=!0},{default:i((()=>e[29]||(e[29]=[s(" 删除 ")]))),_:2},1032,["onClick"])],64))])),_:1})])),_:1},8,["data"])])),_:1},8,["model"])}}});export{D as _};
//# sourceMappingURL=whole.vue_vue_type_script_setup_true_lang-o4hAF7dQ.js.map
