import{a}from"./index-CkEhI1Zk.js";const e="admin-api/pms/hotel-param-config",t={createHotelParamConfig:t=>a.post(`${e}/create`,t),updateHotelParamConfig:t=>a.put(`${e}/update`,t),getHotelParamConfigFront:t=>a.get(`${e}/get/front`,{params:t}),getHotelParamConfigDeposit:t=>a.get(`${e}/get/deposit`,{params:t}),getHotelParamConfigShiftmode:t=>a.get(`${e}/get/shiftmode`,{params:t}),getHotelParamConfigBreakfastTicket:t=>a.get(`${e}/get/breakfast-ticket`,{params:t}),edit:t=>a.put(`${e}/update`,t),nightAudit:e=>a.get("/admin-api/pms/group-param-config/get/night-audit",{params:e}),getWarehouse:e=>a.get("/admin-api/pms/general-config/get-warehouse",{params:e}),updateWarehouse:e=>a.put("/admin-api/pms/general-config/update-warehouse",e)};export{t as h};
//# sourceMappingURL=hotelParamConfig.api-CbdvhUfn.js.map
