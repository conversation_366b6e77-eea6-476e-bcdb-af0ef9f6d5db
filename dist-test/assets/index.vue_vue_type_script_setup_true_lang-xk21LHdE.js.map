{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-xk21LHdE.js", "sources": ["../../src/api/modules/marketing/touch/platformConfig.api.ts", "../../src/views/marketing/touch/platform/components/FormMode/index.vue"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n/**\r\n * @description: 碰一碰平台配置API\r\n */\r\nexport default {\r\n  /**\r\n   * @description: 创建平台配置信息\r\n   */\r\n  create: (data: any) => api.post('/admin-api/touch/platform-config/create', data),\r\n\r\n  /**\r\n   * @description: 获取平台配置信息\r\n   */\r\n  getConfig: (id: number) => api.get('/admin-api/touch/platform-config/select', { params: { id } }),\r\n\r\n  /**\r\n   * @description: 获取全部配置（包括未启用）\r\n   */\r\n  getAllConfig: (hcode: string) => api.get('/admin-api/touch/platform-config/get-all-config', { params: { hcode } }),\r\n\r\n  /**\r\n   * @description: 修改配置信息\r\n   */\r\n  updateData: (data: any) => api.post('/admin-api/touch/platform-config/updateData', data),\r\n\r\n  /**\r\n   * @description: 修改状态\r\n   */\r\n  updateState: (id: number) => api.get('/admin-api/touch/platform-config/updateState', { params: { id } }),\r\n\r\n  /**\r\n   * @description: 删除平台配置信息\r\n   */\r\n  // delete: (id: number) => api.delete('/admin-api/touch/platform-config/delete', { params: { id } }),\r\n\r\n  /**\r\n   * @description: 分页查询平台配置\r\n   */\r\n  // page: (data: any) => api.get('/admin-api/touch/platform-config/page', { params: data })\r\n}\r\n", "<script setup lang=\"ts\">\r\nimport type { PlatGenConfigReqVO, PlatGenConfigRespVO } from '@/models/marketing/touch'\r\nimport { touchPlatformConfigApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport DynamicConfigForm from './DynamicConfigForm.vue'\r\n\r\ninterface Props {\r\n  visible: boolean\r\n  handle: 'create' | 'detail'\r\n  configId: number | null\r\n  rowData?: PlatGenConfigRespVO | null\r\n}\r\n\r\nconst props = withDefaults(defineProps<Props>(), {\r\n  visible: false,\r\n  handle: 'create',\r\n  configId: null,\r\n  rowData: null,\r\n})\r\n\r\nconst emit = defineEmits<{\r\n  'update:visible': [visible: boolean]\r\n  'success': []\r\n}>()\r\n\r\nconst userStore = useUserStore()\r\n\r\nconst formRef = ref()\r\nconst loading = ref(false)\r\n// 添加编辑模式状态\r\nconst isEditing = ref(false)\r\n\r\nconst platformOptions = ref([\r\n  { label: '小红书', value: 'READ_BOOK' },\r\n  { label: '抖音', value: 'TIK_TOK' },\r\n  { label: '系统', value: 'SYSTEM' },\r\n  { label: '携程', value: 'CTRIP' },\r\n  { label: '美团', value: 'MEI_TUAN' },\r\n])\r\n\r\nconst configTypeOptions = ref([\r\n  { label: '个人主页', value: 'PERSONAL_URL' },\r\n  { label: '笔记分享', value: 'SHARE_NORMAL' },\r\n  { label: '视频分享', value: 'SHARE_VIDEO' },\r\n  { label: '店铺主页', value: 'SHOP_URL' },\r\n  { label: 'WIFI', value: 'WIFI' },\r\n\t{ label: '用户评价', value: 'USER_REVIEWS' }\r\n])\r\n\r\nconst formData = ref<PlatGenConfigReqVO>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  platform: '',\r\n  code: '',\r\n  name: '',\r\n  type: '',\r\n  value: '',\r\n  state: true,\r\n})\r\n\r\nconst rules = {\r\n  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],\r\n  code: [{ required: true, message: '请输入配置代码', trigger: 'blur' }],\r\n  type: [{ required: true, message: '请选择配置类型', trigger: 'change' }],\r\n  name: [{ required: true, message: '请输入备注名称', trigger: 'change' }],\r\n}\r\n\r\nconst DynamicList = ['SHARE_NORMAL', 'SHARE_VIDEO', 'WIFI','USER_REVIEWS']\r\n\r\nconst title = computed(() => {\r\n  if (props.handle === 'create') return '新增配置'\r\n  if (props.handle === 'detail') {\r\n    return isEditing.value ? '编辑配置' : '配置详情'\r\n  }\r\n})\r\n\r\nconst dialogVisible = computed({\r\n  get: () => props.visible,\r\n  set: (value) => emit('update:visible', value)\r\n})\r\n\r\nfunction resetForm() {\r\n  formData.value = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    platform: '',\r\n    code: '',\r\n    name: '',\r\n    type: '',\r\n    value: '',\r\n    state: true,\r\n  }\r\n  \r\n  // 设置固定的平台和类型\r\n  if (props.handle === 'create' && props.rowData) {\r\n    formData.value.platform = props.rowData.platform\r\n    formData.value.type = props.rowData.type\r\n  }\r\n  \r\n  formRef.value?.clearValidate()\r\n}\r\n\r\nfunction getConfigDetail() {\r\n  if (!props.configId) return\r\n\r\n  loading.value = true\r\n  touchPlatformConfigApi.getConfig(props.configId).then((res: any) => {\r\n    loading.value = false\r\n    if (res.code === 0) {\r\n      formData.value = { ...res.data }\r\n    }\r\n  }).catch(() => {\r\n    loading.value = false\r\n  })\r\n}\r\n\r\nfunction handleFormData() {\r\n  if (props.handle === 'detail' && props.rowData) {\r\n    formData.value = { ...props.rowData }\r\n  }\r\n  else if (props.handle === 'create') {\r\n    resetForm()\r\n  }\r\n  else if (props.configId && !props.rowData) {\r\n    getConfigDetail()\r\n  }\r\n  else {\r\n    resetForm()\r\n  }\r\n}\r\n\r\n// 添加编辑相关函数\r\nfunction handleEdit() {\r\n  isEditing.value = true\r\n}\r\n\r\nfunction handleCancelEdit() {\r\n  isEditing.value = false\r\n  // 重新加载原始数据\r\n  if (props.rowData) {\r\n    formData.value = { ...props.rowData }\r\n  } else if (props.configId) {\r\n    getConfigDetail()\r\n  }\r\n}\r\n\r\nfunction handleUpdate() {\r\n  formRef.value?.validate((valid: boolean) => {\r\n    if (valid) {\r\n      loading.value = true\r\n      // 调用updateData函数\r\n      touchPlatformConfigApi.updateData({ ...formData.value, id: props.configId })\r\n        .then((res: any) => {\r\n          loading.value = false\r\n          if (res.code === 0) {\r\n            ElMessage.success({\r\n              message: '更新成功',\r\n              center: true,\r\n            })\r\n            isEditing.value = false\r\n            emit('success')\r\n          } else {\r\n            ElMessage.error({\r\n              message: res.msg,\r\n              center: true,\r\n            })\r\n          }\r\n        })\r\n        .catch(() => {\r\n          loading.value = false\r\n        })\r\n    }\r\n  })\r\n}\r\n\r\nfunction handleSubmit() {\r\n  if (props.handle === 'detail' && isEditing.value) {\r\n    handleUpdate()\r\n    return\r\n  }\r\n  \r\n  formRef.value?.validate((valid: boolean) => {\r\n    if (valid) {\r\n      loading.value = true\r\n      const apiCall = props.handle === 'create'\r\n        ? touchPlatformConfigApi.create(formData.value)\r\n        : touchPlatformConfigApi.update({ ...formData.value, id: props.configId })\r\n\r\n      apiCall.then((res: any) => {\r\n        loading.value = false\r\n        if (res.code === 0) {\r\n          ElMessage.success({\r\n            message: props.handle === 'create' ? '创建成功' : '更新成功',\r\n            center: true,\r\n          })\r\n          dialogVisible.value = false\r\n          emit('success')\r\n        }\r\n        else {\r\n          ElMessage.error({\r\n            message: res.msg,\r\n            center: true,\r\n          })\r\n        }\r\n      }).catch(() => {\r\n        loading.value = false\r\n      })\r\n    }\r\n  })\r\n}\r\n\r\nfunction handleCancel() {\r\n  if (props.handle === 'detail' && isEditing.value) {\r\n    handleCancelEdit()\r\n    return\r\n  }\r\n  dialogVisible.value = false\r\n}\r\n// 判断是否需要动态配置表单 - 移动到script setup内的正确位置\r\nconst needsDynamicConfig = computed(() => {\r\n  return DynamicList.includes(formData.value.type) // 添加 'WIFI'\r\n})\r\n// 监听props变化\r\nwatch(\r\n  [() => props.rowData, () => props.visible, () => props.handle],\r\n  ([, newVisible]) => {\r\n    if (newVisible) {\r\n      isEditing.value = false // 重置编辑状态\r\n      handleFormData()\r\n    }\r\n  },\r\n  { deep: true, immediate: true }\r\n)\r\n</script>\r\n\r\n<template>\r\n  <el-dialog\r\n    v-model=\"dialogVisible\"\r\n    :title=\"title\"\r\n    width=\"600px\"\r\n    :close-on-click-modal=\"false\"\r\n  >\r\n    <el-form\r\n      ref=\"formRef\"\r\n      v-loading=\"loading\"\r\n      :model=\"formData\"\r\n      :rules=\"rules\"\r\n      label-width=\"120px\"\r\n    >\r\n      <!-- 平台字段改为只读显示 -->\r\n      <el-form-item label=\"平台\">\r\n        <el-input\r\n          :value=\"platformOptions.find(item => item.value === formData.platform)?.label || formData.platform\"\r\n          disabled\r\n          placeholder=\"平台\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 配置类型字段改为只读显示 -->\r\n      <el-form-item label=\"配置类型\">\r\n        <el-input\r\n          :value=\"configTypeOptions.find(item => item.value === formData.type)?.label || formData.type\"\r\n          disabled\r\n          placeholder=\"配置类型\"\r\n        />\r\n      </el-form-item>\r\n      \r\n      <!-- 动态配置表单 -->\r\n      <template v-if=\"needsDynamicConfig\">\r\n        <DynamicConfigForm\r\n          :config-type=\"formData.type\"\r\n          v-model=\"formData.code\"\r\n        />\r\n      </template>\r\n      \r\n      <!-- 传统配置信息输入 -->\r\n      <template v-else>\r\n        <el-form-item label=\"商家链接\" prop=\"code\">\r\n          <el-input\r\n            v-model=\"formData.code\"\r\n            placeholder=\"请输入商家链接\"\r\n            :disabled=\"props.handle === 'detail' && !isEditing\"\r\n          />\r\n        </el-form-item>\r\n      </template>\r\n\r\n      <el-form-item label=\"备注\" prop=\"name\">\r\n        <el-input\r\n          v-model=\"formData.name\"\r\n          placeholder=\"请输入备注名称\"\r\n          :disabled=\"props.handle === 'detail' && !isEditing\"\r\n        />\r\n      </el-form-item>\r\n    </el-form>\r\n    \r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button @click=\"handleCancel\">\r\n          {{ (props.handle === 'detail' && isEditing) ? '取消编辑' : '取消' }}\r\n        </el-button>\r\n        \r\n        <!-- 详情模式下的编辑按钮 -->\r\n        <el-button\r\n          v-if=\"props.handle === 'detail' && !isEditing\"\r\n          type=\"primary\"\r\n          @click=\"handleEdit\"\r\n        >\r\n          编辑\r\n        </el-button>\r\n        \r\n        <!-- 创建模式或编辑模式下的确定按钮 -->\r\n        <el-button\r\n          v-if=\"props.handle === 'create' || (props.handle === 'detail' && isEditing)\"\r\n          type=\"primary\"\r\n          :loading=\"loading\"\r\n          @click=\"handleSubmit\"\r\n        >\r\n          {{ props.handle === 'create' ? '确定' : '保存' }}\r\n        </el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n\r\n"], "names": ["touchPlatformConfigApi", "create", "data", "api", "post", "getConfig", "id", "get", "params", "getAllConfig", "hcode", "updateData", "updateState", "props", "__props", "emit", "__emit", "userStore", "useUserStore", "formRef", "ref", "loading", "isEditing", "platformOptions", "label", "value", "configTypeOptions", "formData", "gcode", "platform", "code", "name", "type", "state", "rules", "required", "message", "trigger", "DynamicList", "title", "computed", "handle", "dialogVisible", "visible", "set", "resetForm", "rowData", "_a", "clearValidate", "getConfigDetail", "configId", "then", "res", "catch", "handleEdit", "handleSubmit", "validate", "valid", "ElMessage", "success", "center", "error", "msg", "handleUpdate", "update", "handleCancel", "needsDynamicConfig", "includes", "watch", "newVisible", "deep", "immediate"], "mappings": "geAKA,MAAeA,EAAA,CAIbC,OAASC,GAAcC,EAAIC,KAAK,0CAA2CF,GAK3EG,UAAYC,GAAeH,EAAII,IAAI,0CAA2C,CAAEC,OAAQ,CAAEF,QAK1FG,aAAeC,GAAkBP,EAAII,IAAI,kDAAmD,CAAEC,OAAQ,CAAEE,WAKxGC,WAAaT,GAAcC,EAAIC,KAAK,8CAA+CF,GAKnFU,YAAcN,GAAeH,EAAII,IAAI,+CAAgD,CAAEC,OAAQ,CAAEF,+NChBnG,MAAMO,EAAQC,EAORC,EAAOC,EAKPC,EAAYC,IAEZC,EAAUC,IACVC,EAAUD,GAAI,GAEdE,EAAYF,GAAI,GAEhBG,EAAkBH,EAAI,CAC1B,CAAEI,MAAO,MAAOC,MAAO,aACvB,CAAED,MAAO,KAAMC,MAAO,WACtB,CAAED,MAAO,KAAMC,MAAO,UACtB,CAAED,MAAO,KAAMC,MAAO,SACtB,CAAED,MAAO,KAAMC,MAAO,cAGlBC,EAAoBN,EAAI,CAC5B,CAAEI,MAAO,OAAQC,MAAO,gBACxB,CAAED,MAAO,OAAQC,MAAO,gBACxB,CAAED,MAAO,OAAQC,MAAO,eACxB,CAAED,MAAO,OAAQC,MAAO,YACxB,CAAED,MAAO,OAAQC,MAAO,QACzB,CAAED,MAAO,OAAQC,MAAO,kBAGnBE,EAAWP,EAAwB,CACvCQ,MAAOX,EAAUW,MACjBlB,MAAOO,EAAUP,MACjBmB,SAAU,GACVC,KAAM,GACNC,KAAM,GACNC,KAAM,GACNP,MAAO,GACPQ,OAAO,IAGHC,EAAQ,CACZL,SAAU,CAAC,CAAEM,UAAU,EAAMC,QAAS,QAASC,QAAS,WACxDP,KAAM,CAAC,CAAEK,UAAU,EAAMC,QAAS,UAAWC,QAAS,SACtDL,KAAM,CAAC,CAAEG,UAAU,EAAMC,QAAS,UAAWC,QAAS,WACtDN,KAAM,CAAC,CAAEI,UAAU,EAAMC,QAAS,UAAWC,QAAS,YAGlDC,EAAc,CAAC,eAAgB,cAAe,OAAO,gBAErDC,EAAQC,GAAS,IACA,WAAjB3B,EAAM4B,OAA4B,OACjB,WAAjB5B,EAAM4B,OACDnB,EAAUG,MAAQ,OAAS,YADhC,IAKAiB,EAAgBF,EAAS,CAC7BjC,IAAK,IAAMM,EAAM8B,QACjBC,IAAMnB,GAAUV,EAAK,iBAAkBU,KAGzC,SAASoB,UACPlB,EAASF,MAAQ,CACfG,MAAOX,EAAUW,MACjBlB,MAAOO,EAAUP,MACjBmB,SAAU,GACVC,KAAM,GACNC,KAAM,GACNC,KAAM,GACNP,MAAO,GACPQ,OAAO,GAIY,WAAjBpB,EAAM4B,QAAuB5B,EAAMiC,UAC5BnB,EAAAF,MAAMI,SAAWhB,EAAMiC,QAAQjB,SAC/BF,EAAAF,MAAMO,KAAOnB,EAAMiC,QAAQd,MAGtC,OAAAe,EAAA5B,EAAQM,QAAOsB,EAAAC,eAAc,CAG/B,SAASC,IACFpC,EAAMqC,WAEX7B,EAAQI,OAAQ,EAChBzB,EAAuBK,UAAUQ,EAAMqC,UAAUC,MAAMC,IACrD/B,EAAQI,OAAQ,EACC,IAAb2B,EAAItB,OACNH,EAASF,MAAQ,IAAK2B,EAAIlD,MAAK,IAEhCmD,OAAM,KACPhC,EAAQI,OAAQ,CAAA,IACjB,CAmBH,SAAS6B,IACPhC,EAAUG,OAAQ,CAAA,CA0CpB,SAAS8B,UACc,WAAjB1C,EAAM4B,QAAuBnB,EAAUG,MA9B7C,iBACU,OAAAsB,EAAA5B,EAAAM,QAAAsB,EAAOS,UAAUC,IACnBA,IACFpC,EAAQI,OAAQ,EAEhBzB,EAAuBW,WAAW,IAAKgB,EAASF,MAAOnB,GAAIO,EAAMqC,WAC9DC,MAAMC,IACL/B,EAAQI,OAAQ,EACC,IAAb2B,EAAItB,MACN4B,EAAUC,QAAQ,CAChBvB,QAAS,OACTwB,QAAQ,IAEVtC,EAAUG,OAAQ,EAClBV,EAAK,YAEL2C,EAAUG,MAAM,CACdzB,QAASgB,EAAIU,IACbF,QAAQ,GACT,IAGJP,OAAM,KACLhC,EAAQI,OAAQ,CAAA,IACjB,GAEN,CAKcsC,GAIP,OAAAhB,EAAA5B,EAAAM,QAAAsB,EAAOS,UAAUC,IACvB,GAAIA,EAAO,CACTpC,EAAQI,OAAQ,GACiB,WAAjBZ,EAAM4B,OAClBzC,EAAuBC,OAAO0B,EAASF,OACvCzB,EAAuBgE,OAAO,IAAKrC,EAASF,MAAOnB,GAAIO,EAAMqC,YAEzDC,MAAMC,IACZ/B,EAAQI,OAAQ,EACC,IAAb2B,EAAItB,MACN4B,EAAUC,QAAQ,CAChBvB,QAA0B,WAAjBvB,EAAM4B,OAAsB,OAAS,OAC9CmB,QAAQ,IAEVlB,EAAcjB,OAAQ,EACtBV,EAAK,YAGL2C,EAAUG,MAAM,CACdzB,QAASgB,EAAIU,IACbF,QAAQ,GACT,IAEFP,OAAM,KACPhC,EAAQI,OAAQ,CAAA,GACjB,IAEJ,CAGH,SAASwC,IACP,GAAqB,WAAjBpD,EAAM4B,QAAuBnB,EAAUG,MAEzC,OA7EFH,EAAUG,OAAQ,OAEdZ,EAAMiC,QACRnB,EAASF,MAAQ,IAAKZ,EAAMiC,SACnBjC,EAAMqC,UACCD,KA0ElBP,EAAcjB,OAAQ,CAAA,CAGlB,MAAAyC,EAAqB1B,GAAS,IAC3BF,EAAY6B,SAASxC,EAASF,MAAMO,eAG7CoC,EACE,CAAC,IAAMvD,EAAMiC,QAAS,IAAMjC,EAAM8B,QAAS,IAAM9B,EAAM4B,SACvD,EAAC,CAAG4B,MACEA,IACF/C,EAAUG,OAAQ,EA9GD,WAAjBZ,EAAM4B,QAAuB5B,EAAMiC,QACrCnB,EAASF,MAAQ,IAAKZ,EAAMiC,SAEJ,WAAjBjC,EAAM4B,OACHI,IAEHhC,EAAMqC,WAAarC,EAAMiC,QAChBG,IAGNJ,IAqGO,GAGnB,CAAEyB,MAAM,EAAMC,WAAW"}