{"version": 3, "file": "cleanDirty--Y1_-3mS.js", "sources": ["../../src/views/room/realtime/components/cleanDirty.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"selectedRooms\": \"Selected Rooms\",\r\n    \"selectAll\": \"Select All\",\r\n    \"clear\": \"Clear\",\r\n    \"pleaseSelectAtLeastOneRoom\": \"Please select at least one room\",\r\n    \"pleaseSelectCleaner\": \"Please select a cleaner\",\r\n    \"batchOperationSuccess\": \"Batch operation successful!\",\r\n    \"batchOperationFailed\": \"Batch operation failed!\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"selectedRooms\": \"已选房间\",\r\n    \"selectAll\": \"全选\",\r\n    \"clear\": \"清空\",\r\n    \"pleaseSelectAtLeastOneRoom\": \"请至少勾选一个房间\",\r\n    \"pleaseSelectCleaner\": \"请选择保洁员\",\r\n    \"batchOperationSuccess\": \"批量操作成功！\",\r\n    \"batchOperationFailed\": \"批量操作失败！\"\r\n  },\r\n  \"km\": {\r\n    \"selectedRooms\": \"បន្ទប់ដែលបានជ្រើសរើស\",\r\n    \"selectAll\": \"ជ្រើសរើសទាំងអស់\",\r\n    \"clear\": \"សម្អាត\",\r\n    \"pleaseSelectAtLeastOneRoom\": \"សូមជ្រើសរើសយ៉ាងហោចណាស់មួយបន្ទប់\",\r\n    \"pleaseSelectCleaner\": \"សូមជ្រើសរើសអ្នកសម្អាត\",\r\n    \"batchOperationSuccess\": \"ប្រតិបត្តិការជាក្រុមជោគជ័យ!\",\r\n    \"batchOperationFailed\": \"ប្រតិបត្តិការជាក្រុមបរាជ័យ!\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { roomApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<{ type: string }>(), {\r\n  type: '',\r\n})\r\nconst emits = defineEmits<{\r\n  success: [value: []]\r\n}>()\r\nconst { t } = useI18n()\r\nconst data = ref<any>([\r\n  {\r\n    checkAll: false,\r\n    checkAllName: '',\r\n    checkedCities: [],\r\n    cities: [],\r\n  },\r\n])\r\nconst checkRoomNum = computed(() => {\r\n  let num = 0\r\n  data.value.forEach((item: { checkedCities: string | any[] }) => {\r\n    num += item.checkedCities.length\r\n  })\r\n  return num\r\n})\r\nconst userStore = useUserStore()\r\nconst checkedAll = ref(false)\r\n\r\nfunction handleCheckAllChange(value: { checkedCities: any; checkAll: any; cities: any }) {\r\n  value.checkedCities = value.checkAll ? value.cities.map((i: { rCode: any }) => i.rCode) : []\r\n  getIsAll()\r\n}\r\nfunction handleCheckedCitiesChange(value: any) {\r\n  const checkedCount = value.checkedCities.length\r\n  value.checkAll = checkedCount === value.cities.length\r\n  // checkedAll.value = checkedCount === value.cities.length\r\n  getIsAll()\r\n}\r\n\r\n// 总全选\r\nfunction onAllClick() {\r\n  data.value = data.value.map((item: { cities: { rCode: any }[] }) => {\r\n    return {\r\n      ...item,\r\n      checkAll: !!checkedAll.value,\r\n      checkedCities: checkedAll.value ? item.cities.map((i: { rCode: any }) => i.rCode) : [],\r\n    }\r\n  })\r\n}\r\nfunction getIsAll() {\r\n  if (\r\n    data.value.every((item: { checkedCities: string | any[]; cities: string | any[] }) => {\r\n      return item.checkedCities.length === item.cities.length\r\n    })\r\n  ) {\r\n    checkedAll.value = true\r\n  } else {\r\n    checkedAll.value = false\r\n  }\r\n}\r\nasync function getData() {\r\n  try {\r\n    const res = await roomApi.getBatchRoomsClean({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      state: props.type === 'clean' ? 1 : 0,\r\n    })\r\n    if (res && res.data) {\r\n      data.value = res.data.map((_item: any) => {\r\n        return {\r\n          checkAll: false,\r\n          checkAllName: `${_item.buildingName || ''}${_item.floorName || ''}`,\r\n          checkedCities: [],\r\n          cities: _item.rooms,\r\n        }\r\n      })\r\n    }\r\n  } catch (error) {\r\n    // ElMessage.error()\r\n  }\r\n}\r\nfunction submit() {\r\n  let list: string | any[] = []\r\n  data.value.forEach((element: { checkedCities: any }) => {\r\n    list = [...list, ...element.checkedCities]\r\n  })\r\n  return list\r\n}\r\nonMounted(() => {\r\n  getData()\r\n})\r\ndefineExpose({\r\n  submit,\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <span>{{ t('selectedRooms') }}：&nbsp;&nbsp;{{ checkRoomNum }}</span\r\n    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\r\n\r\n    <el-checkbox v-model=\"checkedAll\" @change=\"onAllClick\">\r\n      <span style=\"margin: 0 3px\">{{ t('selectAll') }}</span>\r\n    </el-checkbox>\r\n\r\n    <!-- &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\r\n    <span class=\"close\">\r\n      <el-icon>\r\n        <Delete />\r\n      </el-icon>\r\n      <span style=\"margin: 0 3px;\">清空</span>\r\n    </span> -->\r\n    <div>\r\n      <el-row v-for=\"item in data\" :key=\"item\" class=\"row\">\r\n        <el-col :md=\"4\">\r\n          <el-checkbox v-model=\"item.checkAll\" @change=\"handleCheckAllChange(item)\">\r\n            {{ item.checkAllName }}\r\n          </el-checkbox>\r\n        </el-col>\r\n        <el-col :md=\"20\">\r\n          <el-checkbox-group v-model=\"item.checkedCities\" @change=\"handleCheckedCitiesChange(item)\">\r\n            <el-checkbox v-for=\"city in item.cities\" :key=\"city.rCode\" :label=\"city.rNo\" :value=\"city.rCode\" />\r\n          </el-checkbox-group>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.close {\r\n  line-height: 10px;\r\n  color: #0488fb;\r\n  cursor: pointer;\r\n}\r\n\r\n.row {\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid #efefef;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "data", "ref", "checkAll", "checkAllName", "checkedCities", "cities", "checkRoomNum", "computed", "num", "value", "for<PERSON>ach", "item", "length", "userStore", "useUserStore", "checkedAll", "onAllClick", "map", "i", "rCode", "getIsAll", "every", "onMounted", "async", "res", "roomApi", "getBatchRoomsClean", "gcode", "hcode", "state", "type", "_item", "buildingName", "floorName", "rooms", "error", "getData", "__expose", "submit", "list", "element", "checkedCount"], "mappings": "uiBAoCA,MAAMA,EAAQC,GAMRC,EAAEA,GAAMC,IACRC,EAAOC,EAAS,CACpB,CACEC,UAAU,EACVC,aAAc,GACdC,cAAe,GACfC,OAAQ,MAGNC,EAAeC,GAAS,KAC5B,IAAIC,EAAM,EAIH,OAHFR,EAAAS,MAAMC,SAASC,IAClBH,GAAOG,EAAKP,cAAcQ,MAAA,IAErBJ,CAAA,IAEHK,EAAYC,IACZC,EAAad,GAAI,GAcvB,SAASe,IACPhB,EAAKS,MAAQT,EAAKS,MAAMQ,KAAKN,IACpB,IACFA,EACHT,WAAYa,EAAWN,MACvBL,cAAeW,EAAWN,MAAQE,EAAKN,OAAOY,KAAKC,GAAsBA,EAAEC,QAAS,MAEvF,CAEH,SAASC,IAELpB,EAAKS,MAAMY,OAAOV,GACTA,EAAKP,cAAcQ,SAAWD,EAAKN,OAAOO,SAGnDG,EAAWN,OAAQ,EAEnBM,EAAWN,OAAQ,CACrB,QA8BFa,GAAU,MA5BVC,iBACM,IACI,MAAAC,QAAYC,EAAQC,mBAAmB,CAC3CC,MAAOd,EAAUc,MACjBC,MAAOf,EAAUe,MACjBC,MAAsB,UAAfjC,EAAMkC,KAAmB,EAAI,IAElCN,GAAOA,EAAIxB,OACbA,EAAKS,MAAQe,EAAIxB,KAAKiB,KAAKc,IAClB,CACL7B,UAAU,EACVC,aAAc,GAAG4B,EAAMC,cAAgB,KAAKD,EAAME,WAAa,KAC/D7B,cAAe,GACfC,OAAQ0B,EAAMG,iBAIbC,GAAO,CAEhB,CAUQC,EAAA,IAEGC,EAAA,CACXC,OAXF,WACE,IAAIC,EAAuB,GAIpB,OAHFvC,EAAAS,MAAMC,SAAS8B,IAClBD,EAAO,IAAIA,KAASC,EAAQpC,cAAa,IAEpCmC,CAAA,6fA1DqB9B,KACtBL,cAAgBK,EAAMP,SAAWO,EAAMJ,OAAOY,KAAKC,GAAsBA,EAAEC,QAAS,QACjFC,IAFX,IAA8BX,4OAI9B,SAAmCA,GAC3B,MAAAgC,EAAehC,EAAML,cAAcQ,OACnCH,EAAAP,SAAWuC,IAAiBhC,EAAMJ,OAAOO,OAEtCQ,GAAA"}