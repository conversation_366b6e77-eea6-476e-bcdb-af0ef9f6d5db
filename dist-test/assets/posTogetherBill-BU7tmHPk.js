import{d as l,y as t,B as a,b as n,o as e,c as i,g as s,Y as r,F as u,ag as o}from"./index-CkEhI1Zk.js";import{_ as d}from"./_plugin-vue_export-helper-BCo6x5W8.js";const c={id:"pos-bill-content",class:"bill-content"},p={class:"pos-bill-container"},h={class:"hotel-title"},b={class:"bill-info"},g={class:"info-table"},m={colspan:"3"},D={colspan:"3"},v={colspan:"3"},f={class:"detail-table"},y={colspan:"3"},$={class:"project-name"},S={align:"right"},T={align:"right"},k={class:"total-row"},N={align:"right"},_={colspan:"3"},F={class:"project-name"},j={align:"right"},w={align:"right"},x={class:"total-row"},B={align:"right"},E={class:"remark"},M={class:"footer"},O=d(l({__name:"posTogetherBill",props:{billData:{type:Object,required:!0}},setup(l){const d=l;t((()=>{}));const O=a((()=>({name:d.billData.hname,phone:d.billData.frontPhone||"",address:d.billData.address||""}))),Y=n(d.billData.printDate||(new Date).toLocaleString()),q=a((()=>d.billData.consumeTotalFee||0)),H=a((()=>d.billData.payTotalFee||0)),I=a((()=>{var l;const t={};return(null==(l=d.billData)?void 0:l.consumptionDetails)&&d.billData.consumptionDetails.length>0&&d.billData.consumptionDetails.forEach((l=>{const a=l.createTime.split(" ")[0];t[a]||(t[a]=[]),t[a].push(l)})),t})),L=a((()=>{var l;const t={};return(null==(l=d.billData)?void 0:l.paymentDetails)&&d.billData.paymentDetails.length>0&&d.billData.paymentDetails.forEach((l=>{const a=l.createTime.split(" ")[0];t[a]||(t[a]=[]),t[a].push(l)})),t}));function P(l){if(!l)return"";try{const t=new Date(l);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`}catch{return l}}function z(l){const t=l.reduce(((l,t)=>l+t.fee),0);return Number.parseFloat(t.toFixed(2))}return(l,t)=>(e(),i("div",c,[s("div",p,[s("div",h,r(O.value.name),1),t[14]||(t[14]=s("div",{class:"form-title"},"宾客账单",-1)),s("div",b,[s("div",null,"NO:"+r(d.billData.orderNo),1),s("div",null,"打印时间:"+r(Y.value),1)]),s("table",g,[s("tbody",null,[s("tr",null,[t[0]||(t[0]=s("th",null,"客人姓名",-1)),s("td",null,r(d.billData.name),1),t[1]||(t[1]=s("th",null,"房号",-1)),s("td",null,r(d.billData.rNo),1)]),s("tr",null,[t[2]||(t[2]=s("th",null,"房型",-1)),s("td",m,r(d.billData.rtName),1)]),s("tr",null,[t[3]||(t[3]=s("th",null,"确认金额",-1)),s("td",null,r(d.billData.balance||""),1),t[4]||(t[4]=s("th",null,"积分余额",-1)),s("td",null,r(d.billData.point||""),1)]),s("tr",null,[t[5]||(t[5]=s("th",null,"入住时间",-1)),s("td",D,r(P(d.billData.checkinTime)),1)]),s("tr",null,[t[6]||(t[6]=s("th",null,"离店时间",-1)),s("td",v,r(P(d.billData.checkoutTime)),1)])])]),s("table",f,[t[12]||(t[12]=s("thead",null,[s("tr",null,[s("th",null,"项目"),s("th",null,"时间"),s("th",null,"金额")])],-1)),s("tbody",null,[(e(!0),i(u,null,o(I.value,((l,a,n)=>(e(),i(u,{key:`consume-date-${n}`},[s("tr",null,[s("td",y,r(a),1)]),(e(!0),i(u,null,o(l,((l,t)=>(e(),i("tr",{key:`consume-${n}-${t}`},[s("td",$,r((null==l?void 0:l.subName)||""),1),s("td",null,r(l.createTime),1),s("td",S,r(l.fee),1)])))),128)),s("tr",null,[t[7]||(t[7]=s("td",{colspan:"2",align:"right"},"小计:",-1)),s("td",T,r(z(l)),1)])],64)))),128)),s("tr",k,[t[8]||(t[8]=s("td",{colspan:"2",align:"right"},"消费合计:",-1)),s("td",N,r(q.value),1)]),t[11]||(t[11]=s("tr",null,[s("th",null,"项目"),s("th",null,"时间"),s("th",null,"金额")],-1)),(e(!0),i(u,null,o(L.value,((l,a,n)=>(e(),i(u,{key:`pay-date-${n}`},[s("tr",null,[s("td",_,r(a),1)]),(e(!0),i(u,null,o(l,((l,t)=>(e(),i("tr",{key:`pay-${n}-${t}`},[s("td",F,r((null==l?void 0:l.subName)||""),1),s("td",null,r(l.createTime),1),s("td",j,r(l.fee),1)])))),128)),s("tr",null,[t[9]||(t[9]=s("td",{colspan:"2",align:"right",class:"subtotal-row"},"小计:",-1)),s("td",w,r(z(l)),1)])],64)))),128)),s("tr",x,[t[10]||(t[10]=s("td",{colspan:"2",align:"right"},"付款合计:",-1)),s("td",B,r(H.value),1)])])]),s("div",E,[t[13]||(t[13]=s("div",null,"备注:",-1)),s("div",null,r(d.billData.info||""),1)]),s("div",M,[s("div",null,"酒店电话: "+r(O.value.phone),1),s("div",null,"酒店地址: "+r(O.value.address),1)])])]))}}),[["__scopeId","data-v-67e09fd9"]]);export{O as default};
//# sourceMappingURL=posTogetherBill-BU7tmHPk.js.map
