{"version": 3, "file": "material.api-B6h8YE6r.js", "sources": ["../../src/api/modules/marketing/touch/material.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/touch/material'\r\n\r\n/**\r\n * 素材库管理\r\n */\r\nexport default {\r\n  /**\r\n   * 创建素材\r\n   * @param data 素材数据\r\n   */\r\n  createMaterial(data: any) {\r\n    return api.post(`${BASE_PATH}/create`, data)\r\n  },\r\n\r\n  /**\r\n   * 更新素材\r\n   * @param data 素材数据\r\n   */\r\n  updateMaterial(data: any) {\r\n    return api.post(`${BASE_PATH}/update`, data)\r\n  },\r\n\r\n  /**\r\n   * 删除素材\r\n   * @param id 素材ID\r\n   */\r\n  deleteMaterial(id: String) {\r\n    return api.get(`${BASE_PATH}/delete`, {\r\n      params: { id }\r\n    })\r\n  },\r\n\r\n  /**\r\n   * 获取素材详情\r\n   * @param id 素材ID\r\n   */\r\n  getMaterial(id: String) {\r\n    return api.get(`${BASE_PATH}/get`, {\r\n      params: { id }\r\n    })\r\n  },\r\n\r\n  /**\r\n   * 获取素材分页列表\r\n   * @param data 查询参数\r\n   */\r\n  getMaterialPage(data: any) {\r\n    return api.post(`${BASE_PATH}/page`, data)\r\n  },\r\n\r\n  /**\r\n   * 获取一个素材的所有平台\r\n   * @param data 素材请求参数\r\n   */\r\n  getMaterialAllPlatform(data: any) {\r\n    return api.post(`${BASE_PATH}/get-all-platform`, data)\r\n  },\r\n}\r\n"], "names": ["BASE_PATH", "materialApi", "createMaterial", "data", "api", "post", "updateMaterial", "deleteMaterial", "id", "get", "params", "getMaterial", "getMaterialPage", "getMaterialAllPlatform"], "mappings": "mCAEA,MAAMA,EAAY,2BAKHC,EAAA,CAKbC,eAAeC,GACNC,EAAIC,KAAK,GAAGL,WAAoBG,GAOzCG,eAAeH,GACNC,EAAIC,KAAK,GAAGL,WAAoBG,GAOzCI,eAAeC,GACNJ,EAAIK,IAAI,GAAGT,WAAoB,CACpCU,OAAQ,CAAEF,QAQdG,YAAYH,GACHJ,EAAIK,IAAI,GAAGT,QAAiB,CACjCU,OAAQ,CAAEF,QAQdI,gBAAgBT,GACPC,EAAIC,KAAK,GAAGL,SAAkBG,GAOvCU,uBAAuBV,GACdC,EAAIC,KAAK,GAAGL,qBAA8BG"}