{"version": 3, "file": "rentCreate-DgokBdtt.js", "sources": ["../../src/views/room/goods/rent/components/DetailForm/rentCreate.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"renter\": \"Renter\",\r\n    \"rentItem\": \"Rent Item\",\r\n    \"rent\": \"Rent\",\r\n    \"yuan\": \"Yuan\",\r\n    \"rentVoucherNumber\": \"Rent Voucher No\",\r\n    \"rentRemark\": \"Rent Remark\",\r\n    \"pleaseSelectRenter\": \"Please select renter\",\r\n    \"pleaseSelectRentItem\": \"Please select rent item\",\r\n    \"pleaseEnterRentVoucherNumber\": \"Please enter rent voucher number\",\r\n    \"pleaseEnterRentItemFeatures\": \"Please enter key features of the rental item, such as color, size, quantity, etc.\",\r\n    \"registrationSuccess\": \"Registration successful\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"renter\": \"租借人\",\r\n    \"rentItem\": \"租借物品\",\r\n    \"rent\": \"租金\",\r\n    \"yuan\": \"元\",\r\n    \"rentVoucherNumber\": \"租借凭证号\",\r\n    \"rentRemark\": \"租借备注\",\r\n    \"pleaseSelectRenter\": \"请选择租借人\",\r\n    \"pleaseSelectRentItem\": \"请选择租借物品\",\r\n    \"pleaseEnterRentVoucherNumber\": \"请输入租借凭证号\",\r\n    \"pleaseEnterRentItemFeatures\": \"请输入租借物品的关键特征，如颜色、大小、数量等\",\r\n    \"registrationSuccess\": \"登记成功\"\r\n  },\r\n  \"km\": {\r\n    \"renter\": \"អ្នកជួល\",\r\n    \"rentItem\": \"វត្ថុជួល\",\r\n    \"rent\": \"តម្លៃជួល\",\r\n    \"yuan\": \"យួន\",\r\n    \"rentVoucherNumber\": \"លេខប័ណ្ណជួល\",\r\n    \"rentRemark\": \"ចំណាំអំពីការជួល\",\r\n    \"pleaseSelectRenter\": \"សូមជ្រើសរើសអ្នកជួល\",\r\n    \"pleaseSelectRentItem\": \"សូមជ្រើសរើសវត្ថុជួល\",\r\n    \"pleaseEnterRentVoucherNumber\": \"សូមបញ្ចូលលេខប័ណ្ណជួល\",\r\n    \"pleaseEnterRentItemFeatures\": \"សូមបញ្ចូលលក្ខណៈសំខាន់នៃវត្ថុជួល ដូចជាពណ៌ ទំហំ បរិមាណ ជាដើម\",\r\n    \"registrationSuccess\": \"បានចុះឈ្មោះដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { Bill, RentGoodsResp } from '../../types'\r\nimport { orderApi, rentApi, rentGoodsApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    orderNo?: string\r\n  }>(),\r\n  {\r\n    orderNo: '',\r\n  }\r\n)\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n  /** 租借凭证号 物品上的码 */\r\n  rentNo: '',\r\n  /** 租借物品代码 */\r\n  rentCode: '',\r\n  /** 租借物品名称 */\r\n  rentName: '',\r\n  /** 订单号 */\r\n  orderNo: '',\r\n  /** 房号 */\r\n  rNo: '',\r\n  togetherCode: '',\r\n  // 租金\r\n  rentPrice: 0,\r\n  // 损失需要赔偿的金额\r\n  indemnityPrice: 0,\r\n  /** 客人姓名 */\r\n  name: '',\r\n  /** 状态 0：未归还 1：已归还 2：已赔偿 */\r\n  state: '0',\r\n  remark: '',\r\n})\r\nconst formRules = ref<FormRules>({\r\n  togetherCode: [{ required: true, message: t('pleaseSelectRenter'), trigger: 'blur' }],\r\n  rentCode: [{ required: true, message: t('pleaseSelectRentItem'), trigger: 'blur' }],\r\n})\r\n\r\nconst rentGoods = ref<RentGoodsResp[]>([])\r\nconst bills = ref<Bill[]>([])\r\nonMounted(() => {\r\n  getCanTransferBill()\r\n  getRentGoodsList()\r\n})\r\n\r\nfunction getCanTransferBill() {\r\n  orderApi\r\n    .getCanTransferBill({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        // 如果props.orderNo存在，只保留no==props.orderNo的数据\r\n        if (props.orderNo) {\r\n          bills.value = res.data.filter((item: Bill) => item.no === props.orderNo)\r\n          // 如果过滤后有数据，默认选中第一个\r\n          if (bills.value.length > 0) {\r\n            form.value.togetherCode = bills.value[0].togetherCode\r\n            handleTogetherCodeChange(bills.value[0].togetherCode)\r\n          }\r\n        } else {\r\n          bills.value = res.data\r\n        }\r\n      }\r\n    })\r\n}\r\n\r\nfunction getRentGoodsList() {\r\n  rentApi\r\n    .list({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        rentGoods.value = res.data\r\n      }\r\n    })\r\n}\r\n\r\nfunction handleTogetherCodeChange(togetherCode: string) {\r\n  const selectedBill = bills.value.find((item) => item.togetherCode === togetherCode)\r\n  if (selectedBill) {\r\n    // 将选中的租借人信息赋值给表单\r\n    form.value.rNo = selectedBill.rNo\r\n    form.value.name = selectedBill.name\r\n    form.value.orderNo = selectedBill.no // 假设订单号对应bill的no字段\r\n  } else {\r\n    // 如果没有找到匹配项，清空相关字段\r\n    form.value.rNo = ''\r\n    form.value.name = ''\r\n    form.value.orderNo = ''\r\n  }\r\n}\r\n\r\nfunction handleRentGoodsChange(rentCode: string) {\r\n  const selectedRentGoods = rentGoods.value.find((item) => item.rentCode === rentCode)\r\n  if (selectedRentGoods) {\r\n    form.value.rentPrice = selectedRentGoods.rentPrice\r\n    form.value.indemnityPrice = selectedRentGoods.indemnityPrice\r\n    form.value.rentName = selectedRentGoods.rentName\r\n  } else {\r\n    form.value.rentPrice = 0\r\n    form.value.indemnityPrice = 0\r\n    form.value.rentName = ''\r\n  }\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            rentGoodsApi.createRentGoods(form.value).then(() => {\r\n              ElMessage({\r\n                type: 'success',\r\n                message: t('registrationSuccess'),\r\n                center: true,\r\n              })\r\n              resolve()\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"150px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('renter')\" prop=\"togetherCode\">\r\n        <el-select v-model=\"form.togetherCode\" :placeholder=\"t('pleaseSelectRenter')\" :filterable=\"true\" @change=\"handleTogetherCodeChange\">\r\n          <el-option v-for=\"item in bills\" :key=\"item.togetherCode\" :label=\"`${item.rNo}-${item.name}`\" :value=\"item.togetherCode\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rentItem')\" prop=\"rentCode\">\r\n        <el-select v-model=\"form.rentCode\" :placeholder=\"t('pleaseSelectRentItem')\" :filterable=\"true\" @change=\"handleRentGoodsChange\">\r\n          <el-option v-for=\"item in rentGoods\" :key=\"item.rentCode\" :label=\"`${item.rentName}-${t('rent')}${item.rentPrice}${t('yuan')}`\" :value=\"item.rentCode\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rent')\"> {{ form.rentPrice }} {{ t('yuan') }} </el-form-item>\r\n      <el-form-item :label=\"t('rentVoucherNumber')\" prop=\"rentNo\">\r\n        <el-input v-model=\"form.rentNo\" :placeholder=\"t('pleaseEnterRentVoucherNumber')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rentRemark')\">\r\n        <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"5\" maxlength=\"200\" :placeholder=\"t('pleaseEnterRentItemFeatures')\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "hcode", "rentNo", "rentCode", "rentName", "orderNo", "rNo", "togetherCode", "rentPrice", "indemnityPrice", "name", "state", "remark", "formRules", "required", "message", "trigger", "rentGoods", "bills", "handleTogetherCodeChange", "selected<PERSON><PERSON>", "value", "find", "item", "no", "handleRentGoodsChange", "selectedRentGoods", "onMounted", "orderApi", "getCanTransferBill", "then", "res", "code", "data", "filter", "length", "rentApi", "list", "__expose", "submit", "Promise", "resolve", "validate", "valid", "rentGoodsApi", "createRentGoods", "ElMessage", "type", "center"], "mappings": "6tBAmDA,MAAMA,EAAQC,GAQRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,MAAON,EAAUM,MAEjBC,MAAOP,EAAUO,MAEjBC,OAAQ,GAERC,SAAU,GAEVC,SAAU,GAEVC,QAAS,GAETC,IAAK,GACLC,aAAc,GAEdC,UAAW,EAEXC,eAAgB,EAEhBC,KAAM,GAENC,MAAO,IACPC,OAAQ,KAEJC,EAAYhB,EAAe,CAC/BU,aAAc,CAAC,CAAEO,UAAU,EAAMC,QAASvB,EAAE,sBAAuBwB,QAAS,SAC5Eb,SAAU,CAAC,CAAEW,UAAU,EAAMC,QAASvB,EAAE,wBAAyBwB,QAAS,WAGtEC,EAAYpB,EAAqB,IACjCqB,EAAQrB,EAAY,IA0C1B,SAASsB,EAAyBZ,GAC1B,MAAAa,EAAeF,EAAMG,MAAMC,MAAMC,GAASA,EAAKhB,eAAiBA,IAClEa,GAEGrB,EAAAsB,MAAMf,IAAMc,EAAad,IACzBP,EAAAsB,MAAMX,KAAOU,EAAaV,KAC1BX,EAAAsB,MAAMhB,QAAUe,EAAaI,KAGlCzB,EAAKsB,MAAMf,IAAM,GACjBP,EAAKsB,MAAMX,KAAO,GAClBX,EAAKsB,MAAMhB,QAAU,GACvB,CAGF,SAASoB,EAAsBtB,GACvB,MAAAuB,EAAoBT,EAAUI,MAAMC,MAAMC,GAASA,EAAKpB,WAAaA,IACvEuB,GACG3B,EAAAsB,MAAMb,UAAYkB,EAAkBlB,UACpCT,EAAAsB,MAAMZ,eAAiBiB,EAAkBjB,eACzCV,EAAAsB,MAAMjB,SAAWsB,EAAkBtB,WAExCL,EAAKsB,MAAMb,UAAY,EACvBT,EAAKsB,MAAMZ,eAAiB,EAC5BV,EAAKsB,MAAMjB,SAAW,GACxB,QAlEFuB,GAAU,KAMRC,EACGC,mBAAmB,CAClB7B,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,QAElB6B,MAAMC,IACY,IAAbA,EAAIC,OAEF1C,EAAMe,SACFa,EAAAG,MAAQU,EAAIE,KAAKC,QAAQX,GAAeA,EAAKC,KAAOlC,EAAMe,UAE5Da,EAAMG,MAAMc,OAAS,IACvBpC,EAAKsB,MAAMd,aAAeW,EAAMG,MAAM,GAAGd,aACzCY,EAAyBD,EAAMG,MAAM,GAAGd,gBAG1CW,EAAMG,MAAQU,EAAIE,KACpB,IAMNG,EACGC,KAAK,CACJrC,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,QAElB6B,MAAMC,IACY,IAAbA,EAAIC,OACNf,EAAUI,MAAQU,EAAIE,KAAA,GAlCX,IAmENK,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB3C,EAAQuB,OACNvB,EAAQuB,MAAMqB,UAAUC,IAClBA,GACFC,EAAaC,gBAAgB9C,EAAKsB,OAAOS,MAAK,KAClCgB,EAAA,CACRC,KAAM,UACNhC,QAASvB,EAAE,uBACXwD,QAAQ,IAEFP,GAAA,GACT,GAEJ"}