{"version": 3, "file": "groupParamConfig.api-R6b4sMh1.js", "sources": ["../../src/api/modules/pms/config/groupParamConfig.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/group-param-config'\r\n/** 集团参数设置 */\r\nexport default {\r\n  /**\r\n   * 获得集团参数设置-订单配置\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigOrder: (gcode: string) => api.get(`${BASE_PATH}/get/order`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得集团参数设置-夜审业务逻辑\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigNightAudit: (gcode: string) => api.get(`${BASE_PATH}/get/night-audit`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得集团参数设置-财务设置\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigFinance: (gcode: string) => api.get(`${BASE_PATH}/get/finance`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得集团参数设置-会员配置\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigMember: (gcode: string) => api.get(`${BASE_PATH}/get/member`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得集团参数设置-会员积分规则配置\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigMemberRule: (gcode: string) => api.get(`${BASE_PATH}/get/member-rule`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得集团参数设置-房态盘颜色配置\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigRoomColor: (gcode: string) => api.get(`${BASE_PATH}/get/color`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得集团参数设置-间夜数设置\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigNightNum: (gcode: string) => api.get(`${BASE_PATH}/get/night-num`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得集团参数设置-钟点房占房配置\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  getGroupParamConfigHour: (gcode: string) => api.get(`${BASE_PATH}/get/hour`, { params: { gcode } }),\r\n\r\n  /**\r\n   * 更新集团参数设置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGroupParamConfig: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n}\r\n"], "names": ["BASE_PATH", "groupParamConfigApi", "getGroupParamConfigOrder", "gcode", "api", "get", "params", "getGroupParamConfigNightAudit", "getGroupParamConfigFinance", "getGroupParamConfigMember", "getGroupParamConfigMemberRule", "getGroupParamConfigRoomColor", "getGroupParamConfigNightNum", "getGroupParamConfigHour", "updateGroupParamConfig", "data", "put"], "mappings": "wCAEA,MAAMA,EAAY,mCAEHC,EAAA,CAMbC,yBAA2BC,GAAkBC,EAAIC,IAAI,GAAGL,cAAuB,CAAEM,OAAQ,CAAEH,WAO3FI,8BAAgCJ,GAAkBC,EAAIC,IAAI,GAAGL,oBAA6B,CAAEM,OAAQ,CAAEH,WAOtGK,2BAA6BL,GAAkBC,EAAIC,IAAI,GAAGL,gBAAyB,CAAEM,OAAQ,CAAEH,WAO/FM,0BAA4BN,GAAkBC,EAAIC,IAAI,GAAGL,eAAwB,CAAEM,OAAQ,CAAEH,WAO7FO,8BAAgCP,GAAkBC,EAAIC,IAAI,GAAGL,oBAA6B,CAAEM,OAAQ,CAAEH,WAOtGQ,6BAA+BR,GAAkBC,EAAIC,IAAI,GAAGL,cAAuB,CAAEM,OAAQ,CAAEH,WAO/FS,4BAA8BT,GAAkBC,EAAIC,IAAI,GAAGL,kBAA2B,CAAEM,OAAQ,CAAEH,WAOlGU,wBAA0BV,GAAkBC,EAAIC,IAAI,GAAGL,aAAsB,CAAEM,OAAQ,CAAEH,WAOzFW,uBAAyBC,GAAcX,EAAIY,IAAI,GAAGhB,WAAoBe"}