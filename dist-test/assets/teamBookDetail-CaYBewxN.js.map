{"version": 3, "file": "teamBookDetail-CaYBewxN.js", "sources": ["../../src/views/order/info/components/orderdetail/teamBookDetail.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"zh-cn\": {\r\n      \"checkinInfo\": \"入住信息\",\r\n      \"bookingNo\": \"预订单号\",\r\n      \"modifyOrder\": \"修改订单\",\r\n      \"cancel\": \"取消\",\r\n      \"saveChanges\": \"保存修改\",\r\n      \"teamName\": \"团队名称\",\r\n      \"checkinType\": \"入住类型\",\r\n      \"contractNo\": \"合同编号\",\r\n      \"salesperson\": \"销售员\",\r\n      \"guestSourceType\": \"客源类型\",\r\n      \"guaranteeMethod\": \"担保方式\",\r\n      \"contact\": \"联系人\",\r\n      \"contactPhone\": \"联系电话\",\r\n      \"remark\": \"备注\",\r\n      \"roomGuestInfo\": \"房间/住客信息\",\r\n      \"addRoom\": \"添加房间\",\r\n      \"batch\": \"批次\",\r\n      \"delete\": \"删除\",\r\n      \"confirmDeleteBatch\": \"确定删除该批次吗？\",\r\n      \"autoArrangeRooms\": \"AI自动排房\",\r\n      \"batchCheckin\": \"批量入住\",\r\n      \"arrangeRoom\": \"排房\",\r\n      \"upgrade\": \"升级\",\r\n      \"changePrice\": \"改价\",\r\n      \"name\": \"姓名\",\r\n      \"phone\": \"手机号\",\r\n      \"idType\": \"证件类型\",\r\n      \"idNumber\": \"证件号码\",\r\n      \"readIdCard\": \"读身份证\",\r\n      \"makeNewCard\": \"制新卡\",\r\n      \"checkin\": \"入住\",\r\n      \"addRoommate\": \"添加同住\",\r\n      \"removeRoom\": \"移除房间\",\r\n      \"confirmRemoveRoom\": \"确定移除该房间吗？\",\r\n      \"viewOrder\": \"查看订单\",\r\n      \"makeSameRoomCard\": \"制同住卡\",\r\n      \"removeGuest\": \"移除客人\",\r\n      \"roomUpgrade\": \"房型升级\",\r\n      \"currentRoomType\": \"当前房型\",\r\n      \"upgradeRoomType\": \"升级房型\",\r\n      \"freeUpgrade\": \"免费升级\",\r\n      \"selectUpgradeRoomType\": \"请选择升级房型\",\r\n      \"availableRooms\": \"可售数\",\r\n      \"overBookingRooms\": \"可超数\",\r\n      \"confirm\": \"确定\",\r\n      \"travelGroup\": \"旅行团\",\r\n      \"meetingGroup\": \"会议团\",\r\n      \"member\": \"会员\",\r\n      \"agent\": \"中介\",\r\n      \"selectAgent\": \"请选择中介\",\r\n      \"protocolUnit\": \"协议单位\",\r\n      \"selectProtocolUnit\": \"请选择协议单位\",\r\n      \"selectSalesperson\": \"请选择销售员\",\r\n      \"portions\": \"份\",\r\n      \"noIdInfo\": \"无证件信息\",\r\n      \"roomAlreadyArranged\": \"已排房，需先取消排房\",\r\n      \"canUpgradeToHigherRoom\": \"可以升级到更高级的房型\",\r\n      \"clickToUpgradeRoomType\": \"点击升级房型\",\r\n      \"orderAlreadyArrangedCannotUpgrade\": \"订单已排房，无法升级，请取消排房后再操作\",\r\n      \"messages\": {\r\n        \"modifySuccess\": \"修改成功\",\r\n        \"deleteSuccess\": \"删除成功\",\r\n        \"onlyOneBatchCannotDelete\": \"当前预订单只有一个批次，不允许删除\",\r\n        \"arrangeRoomSuccess\": \"排房成功\",\r\n        \"checkinSuccess\": \"入住成功\",\r\n        \"batchCheckinSuccess\": \"批量入住成功\",\r\n        \"addRoomSuccess\": \"添加房间成功\",\r\n        \"removeRoomSuccess\": \"移除房间成功\",\r\n        \"roomUpgradeSuccess\": \"房型升级成功！\",\r\n        \"getSystemTimeFailed\": \"获取系统时间失败\",\r\n        \"currentTimeNotInBatchRange\": \"当前时间不在批次时间范围内，不允许入住\",\r\n        \"noRoomsToCheckin\": \"没有可入住的房间，请先排房\",\r\n        \"guestNameMissing\": \"有入住人姓名缺失，请先填写入住人信息\",\r\n        \"idNumberMissing\": \"有入住人身份证号码为空\",\r\n        \"continueQuestion\": \"是否继续？\",\r\n        \"makeCardBeforeArrangeRoom\": \"制卡前请先排房\",\r\n        \"hotelNoLockConfig\": \"酒店没有配置门锁，请到房锁配置里选择酒店所用门锁型号，如果没有匹配的门锁型号，请联系服务商。\",\r\n        \"makeCardSuccess\": \"房制卡成功\",\r\n        \"checkinBeforeArrangeRoom\": \"入住前请先排房\",\r\n        \"atLeastOneRoomRequired\": \"移除失败：预订单中至少需要保留一间房\",\r\n        \"multipleGuestsCannotCollapse\": \"该房间有多位住客，无法折叠\",\r\n        \"orderPastCheckoutTime\": \"订单已过最晚预离时间，请修改订单预离时间\",\r\n        \"currentOrderArrangedCannotUpgrade\": \"当前订单已经有排房，如需升级房型，请先取消排房\",\r\n        \"downloadClientForCardReading\": \"读取房卡只能在Hotel-Agent中操作，请下载Hotel-Agent客户端。\",\r\n        \"cardInfo\": \"房卡信息\",\r\n        \"guestNameMissingInRoom\": \"房号 {roomNo} 有 {count} 位入住人姓名缺失，请先填写入住人信息\",\r\n        \"idNumberMissingInRoom\": \"房号 {roomNo} 有 {count} 位入住人身份证号码为空\",\r\n        \"idNumberMissingConfirm\": \"房号 {roomNo} 有 {count} 位入住人身份证号码为空，是否继续？\",\r\n        \"batchGuestNameMissing\": \"以下房间有入住人姓名缺失：{rooms}，请先填写入住人信息\",\r\n        \"batchIdNumberMissing\": \"以下房间有入住人身份证号码为空：{rooms}\",\r\n        \"batchIdNumberMissingConfirm\": \"以下房间有入住人身份证号码为空：{rooms}，是否继续？\",\r\n        \"userCancelOperation\": \"用户取消操作\",\r\n        \"tip\": \"提示\",\r\n        \"continue\": \"继续\",\r\n        \"cancel\": \"取消\"\r\n      },\r\n      \"validation\": {\r\n        \"teamNameRequired\": \"团队名称不能为空\",\r\n        \"currentRoomTypeRequired\": \"当前房型不能为空！\",\r\n        \"upgradeRoomTypeRequired\": \"升级房型不能为空！\"\r\n      },\r\n      \"placeholders\": {\r\n        \"enterName\": \"姓名\",\r\n        \"enterPhone\": \"手机号\",\r\n        \"enterIdNumber\": \"证件号码\",\r\n        \"phoneNumber\": \"手机号\"\r\n      },\r\n      \"tableColumns\": {\r\n      \"roomNoRoomType\": \"房号/房型\",\r\n      \"discountPrice\": \"优惠价\",\r\n      \"namePhone\": \"姓名/电话\",\r\n      \"idTypeIdNumber\": \"证件类型/证件号\",\r\n      \"breakfastPortions\": \"赠早/份\",\r\n      \"operations\": \"操作\"\r\n      },\r\n      \"guestSrcValidation\": {\r\n        \"memberRequired\": \"会员不能为空\",\r\n        \"agentRequired\": \"中介不能为空\",\r\n        \"protocolRequired\": \"协议单位不能为空\",\r\n        \"selectOrInputRequired\": \"请选择或输入客源代码\",\r\n      },\r\n      \"teamNameRequired\": \"请输入团队名称\"\r\n    },\r\n    \"en\": {\r\n      \"checkinInfo\": \"Check-in Information\",\r\n      \"bookingNo\": \"Booking No\",\r\n      \"modifyOrder\": \"Modify Order\",\r\n      \"cancel\": \"Cancel\",\r\n      \"saveChanges\": \"Save Changes\",\r\n      \"teamName\": \"Team Name\",\r\n      \"checkinType\": \"Check-in Type\",\r\n      \"contractNo\": \"Contract No\",\r\n      \"salesperson\": \"Sales\",\r\n      \"guestSourceType\": \"Guest Source Type\",\r\n      \"guaranteeMethod\": \"Guarantee Method\",\r\n      \"contact\": \"Contact\",\r\n      \"contactPhone\": \"Contact Phone\",\r\n      \"remark\": \"Remark\",\r\n      \"roomGuestInfo\": \"Room/Guest Information\",\r\n      \"addRoom\": \"Add Room\",\r\n      \"batch\": \"Batch\",\r\n      \"delete\": \"Delete\",\r\n      \"confirmDeleteBatch\": \"Are you sure to delete this batch?\",\r\n      \"autoArrangeRooms\": \"AI Auto Arrange Rooms\",\r\n      \"batchCheckin\": \"Batch Check-in\",\r\n      \"arrangeRoom\": \"Arrange Room\",\r\n      \"upgrade\": \"Upgrade\",\r\n      \"changePrice\": \"Change Price\",\r\n      \"name\": \"Name\",\r\n      \"phone\": \"Phone\",\r\n      \"idType\": \"ID Type\",\r\n      \"idNumber\": \"ID Number\",\r\n      \"readIdCard\": \"Read ID Card\",\r\n      \"makeNewCard\": \"Make New Card\",\r\n      \"checkin\": \"Check-in\",\r\n      \"addRoommate\": \"Add Roommate\",\r\n      \"removeRoom\": \"Remove Room\",\r\n      \"confirmRemoveRoom\": \"Are you sure to remove this room?\",\r\n      \"viewOrder\": \"View Order\",\r\n      \"makeSameRoomCard\": \"Make Same Room Card\",\r\n      \"removeGuest\": \"Remove Guest\",\r\n      \"roomUpgrade\": \"Room Upgrade\",\r\n      \"currentRoomType\": \"Current Room Type\",\r\n      \"upgradeRoomType\": \"Upgrade Room Type\",\r\n      \"freeUpgrade\": \"Free Upgrade\",\r\n      \"selectUpgradeRoomType\": \"Please select upgrade room type\",\r\n      \"availableRooms\": \"Available\",\r\n      \"overBookingRooms\": \"Overbooking\",\r\n      \"confirm\": \"Confirm\",\r\n      \"travelGroup\": \"Travel Group\",\r\n      \"meetingGroup\": \"Meeting Group\",\r\n      \"member\": \"Member\",\r\n      \"agent\": \"Agent\",\r\n      \"selectAgent\": \"Please select agent\",\r\n      \"protocolUnit\": \"Protocol Unit\",\r\n      \"selectProtocolUnit\": \"Please select protocol unit\",\r\n      \"selectSalesperson\": \"Please select salesperson\",\r\n      \"portions\": \"portions\",\r\n      \"noIdInfo\": \"No ID information\",\r\n      \"roomAlreadyArranged\": \"Room already arranged, need to cancel arrangement first\",\r\n      \"canUpgradeToHigherRoom\": \"Can upgrade to higher room type\",\r\n      \"clickToUpgradeRoomType\": \"Click to upgrade room type\",\r\n      \"orderAlreadyArrangedCannotUpgrade\": \"Order already arranged, cannot upgrade, please cancel arrangement first\",\r\n      \"messages\": {\r\n        \"modifySuccess\": \"Modified successfully\",\r\n        \"deleteSuccess\": \"Deleted successfully\",\r\n        \"onlyOneBatchCannotDelete\": \"Only one batch exists, cannot delete\",\r\n        \"arrangeRoomSuccess\": \"Room arranged successfully\",\r\n        \"checkinSuccess\": \"Check-in successful\",\r\n        \"batchCheckinSuccess\": \"Batch check-in successful\",\r\n        \"addRoomSuccess\": \"Room added successfully\",\r\n        \"removeRoomSuccess\": \"Room removed successfully\",\r\n        \"roomUpgradeSuccess\": \"Room upgrade successful!\",\r\n        \"getSystemTimeFailed\": \"Failed to get system time\",\r\n        \"currentTimeNotInBatchRange\": \"Current time is not within batch time range, check-in not allowed\",\r\n        \"noRoomsToCheckin\": \"No rooms available for check-in, please arrange rooms first\",\r\n        \"guestNameMissing\": \"Guest name missing, please fill in guest information first\",\r\n        \"idNumberMissing\": \"Guest ID number is empty\",\r\n        \"continueQuestion\": \"Continue?\",\r\n        \"makeCardBeforeArrangeRoom\": \"Please arrange room before making card\",\r\n        \"hotelNoLockConfig\": \"Hotel has no lock configuration, please go to 'Lock Configuration' to select the lock model used by the hotel. If there is no matching lock model, please contact the service provider.\",\r\n        \"makeCardSuccess\": \"Card made successfully\",\r\n        \"checkinBeforeArrangeRoom\": \"Please arrange room before check-in\",\r\n        \"atLeastOneRoomRequired\": \"Removal failed: At least one room must be kept in the booking\",\r\n        \"multipleGuestsCannotCollapse\": \"This room has multiple guests and cannot be collapsed\",\r\n        \"orderPastCheckoutTime\": \"Order has passed the latest checkout time, please modify the order checkout time\",\r\n        \"currentOrderArrangedCannotUpgrade\": \"Current order already has room arrangement, please cancel arrangement first if you need to upgrade room type\",\r\n        \"downloadClientForCardReading\": \"Card reading can only be operated in Hotel-Agent, please download Hotel-Agent client.\",\r\n        \"cardInfo\": \"Card Information\",\r\n        \"guestNameMissingInRoom\": \"Room {roomNo} has {count} guests with missing names, please fill in guest information first\",\r\n        \"idNumberMissingInRoom\": \"Room {roomNo} has {count} guests with empty ID numbers\",\r\n        \"idNumberMissingConfirm\": \"Room {roomNo} has {count} guests with empty ID numbers, continue?\",\r\n        \"batchGuestNameMissing\": \"The following rooms have guests with missing names: {rooms}, please fill in guest information first\",\r\n        \"batchIdNumberMissing\": \"The following rooms have guests with empty ID numbers: {rooms}\",\r\n        \"batchIdNumberMissingConfirm\": \"The following rooms have guests with empty ID numbers: {rooms}, continue?\",\r\n        \"userCancelOperation\": \"User cancelled operation\",\r\n        \"tip\": \"Tip\",\r\n        \"continue\": \"Continue\",\r\n        \"cancel\": \"Cancel\"\r\n      },\r\n      \"validation\": {\r\n        \"teamNameRequired\": \"Team name is required\",\r\n        \"currentRoomTypeRequired\": \"Current room type is required!\",\r\n        \"upgradeRoomTypeRequired\": \"Upgrade room type is required!\"\r\n      },\r\n      \"placeholders\": {\r\n        \"enterName\": \"Name\",\r\n        \"enterPhone\": \"Phone\",\r\n        \"enterIdNumber\": \"ID Number\",\r\n        \"phoneNumber\": \"Phone Number\"\r\n      },\r\n      \"tableColumns\": {\r\n      \"roomNoRoomType\": \"Room No/Room Type\",\r\n      \"discountPrice\": \"Discount Price\",\r\n      \"namePhone\": \"Name/Phone\",\r\n      \"idTypeIdNumber\": \"ID Type/ID Number\",\r\n      \"breakfastPortions\": \"Breakfast/Portions\",\r\n      \"operations\": \"Operations\"\r\n      },\r\n      \"guestSrcValidation\": {\r\n        \"memberRequired\": \"Member cannot be empty\",\r\n        \"agentRequired\": \"Agent cannot be empty\",\r\n        \"protocolRequired\": \"Protocol unit cannot be empty\",\r\n        \"selectOrInputRequired\": \"Please select or enter guest source code\",\r\n      },\r\n      \"teamNameRequired\": \"Please enter team name\"\r\n    },\r\n    \"km\": {\r\n      \"checkinInfo\": \"ព័ត៌មានចូលស្នាក់នៅ\",\r\n      \"bookingNo\": \"លេខកម្មង់\",\r\n      \"modifyOrder\": \"កែប្រែកម្មង់\",\r\n      \"cancel\": \"បោះបង់\",\r\n      \"saveChanges\": \"រក្សាទុកការផ្លាស់ប្តូរ\",\r\n      \"teamName\": \"ឈ្មោះក្រុម\",\r\n      \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n      \"contractNo\": \"លេខកិច្ចសន្យា\",\r\n      \"salesperson\": \"អ្នកលក់\",\r\n      \"guestSourceType\": \"ប្រភេទប្រភពភ្ញៀវ\",\r\n      \"guaranteeMethod\": \"វិធីធានា\",\r\n      \"contact\": \"ទំនាក់ទំនង\",\r\n      \"contactPhone\": \"លេខទូរស័ព្ទទំនាក់ទំនង\",\r\n      \"remark\": \"ចំណាំ\",\r\n      \"roomGuestInfo\": \"ព័ត៌មានបន្ទប់/ភ្ញៀវ\",\r\n      \"addRoom\": \"បន្ថែមបន្ទប់\",\r\n      \"batch\": \"ជំនាន់\",\r\n      \"delete\": \"លុប\",\r\n      \"confirmDeleteBatch\": \"តើអ្នកប្រាកដថាចង់លុបជំនាន់នេះមែនទេ?\",\r\n      \"autoArrangeRooms\": \"AI ចាត់ចងបន្ទប់ស្វ័យប្រវត្តិ\",\r\n      \"batchCheckin\": \"ចូលស្នាក់នៅជាបាច់\",\r\n      \"arrangeRoom\": \"ចាត់ចងបន្ទប់\",\r\n      \"upgrade\": \"ដំឡើងកំណែ\",\r\n      \"changePrice\": \"ផ្លាស់ប្តូរតម្លៃ\",\r\n      \"name\": \"ឈ្មោះ\",\r\n      \"phone\": \"លេខទូរស័ព្ទ\",\r\n      \"idType\": \"ប្រភេទអត្តសញ្ញាណប័ណ្ណ\",\r\n      \"idNumber\": \"លេខអត្តសញ្ញាណប័ណ្ណ\",\r\n      \"readIdCard\": \"អានអត្តសញ្ញាណប័ណ្ណ\",\r\n      \"makeNewCard\": \"ធ្វើកាតថ្មី\",\r\n      \"checkin\": \"ចូលស្នាក់នៅ\",\r\n      \"addRoommate\": \"បន្ថែមដៃគូបន្ទប់\",\r\n      \"removeRoom\": \"ដកបន្ទប់ចេញ\",\r\n      \"confirmRemoveRoom\": \"តើអ្នកប្រាកដថាចង់ដកបន្ទប់នេះចេញមែនទេ?\",\r\n      \"viewOrder\": \"មើលកម្មង់\",\r\n      \"makeSameRoomCard\": \"ធ្វើកាតបន្ទប់ដូចគ្នា\",\r\n      \"removeGuest\": \"ដកភ្ញៀវចេញ\",\r\n      \"roomUpgrade\": \"ដំឡើងកំណែបន្ទប់\",\r\n      \"currentRoomType\": \"ប្រភេទបន្ទប់បច្ចុប្បន្ន\",\r\n      \"upgradeRoomType\": \"ប្រភេទបន្ទប់ដំឡើងកំណែ\",\r\n      \"freeUpgrade\": \"ដំឡើងកំណែឥតគិតថ្លៃ\",\r\n      \"selectUpgradeRoomType\": \"សូមជ្រើសរើសប្រភេទបន្ទប់ដំឡើងកំណែ\",\r\n      \"availableRooms\": \"អាចលក់បាន\",\r\n      \"overBookingRooms\": \"អាចលើសបាន\",\r\n      \"confirm\": \"បញ្ជាក់\",\r\n      \"travelGroup\": \"ក្រុមទេសចរណ៍\",\r\n      \"meetingGroup\": \"ក្រុមប្រជុំ\",\r\n      \"member\": \"សមាជិក\",\r\n      \"agent\": \"ភ្នាក់ងារ\",\r\n      \"selectAgent\": \"សូមជ្រើសរើសភ្នាក់ងារ\",\r\n      \"protocolUnit\": \"ឯកតាពិធីការ\",\r\n      \"selectProtocolUnit\": \"សូមជ្រើសរើសឯកតាពិធីការ\",\r\n      \"selectSalesperson\": \"សូមជ្រើសរើសអ្នកលក់\",\r\n      \"portions\": \"ចំណែក\",\r\n      \"noIdInfo\": \"គ្មានព័ត៌មានអត្តសញ្ញាណប័ណ្ណ\",\r\n      \"roomAlreadyArranged\": \"បានចាត់ចងបន្ទប់រួចហើយ ត្រូវបោះបង់ការចាត់ចងជាមុនសិន\",\r\n      \"canUpgradeToHigherRoom\": \"អាចដំឡើងកំណែទៅប្រភេទបន្ទប់ខ្ពស់ជាងនេះ\",\r\n      \"clickToUpgradeRoomType\": \"ចុចដើម្បីដំឡើងកំណែប្រភេទបន្ទប់\",\r\n      \"orderAlreadyArrangedCannotUpgrade\": \"កម្មង់បានចាត់ចងរួចហើយ មិនអាចដំឡើងកំណែបាន សូមបោះបង់ការចាត់ចងជាមុនសិន\",\r\n      \"messages\": {\r\n        \"modifySuccess\": \"កែប្រែបានជោគជ័យ\",\r\n        \"deleteSuccess\": \"លុបបានជោគជ័យ\",\r\n        \"onlyOneBatchCannotDelete\": \"កម្មង់បម្រុងនេះមានតែជំនាន់មួយ មិនអនុញ្ញាតឱ្យលុបទេ\",\r\n        \"arrangeRoomSuccess\": \"ចាត់ចងបន្ទប់បានជោគជ័យ\",\r\n        \"checkinSuccess\": \"ចូលស្នាក់នៅបានជោគជ័យ\",\r\n        \"batchCheckinSuccess\": \"ចូលស្នាក់នៅជាបាច់បានជោគជ័យ\",\r\n        \"addRoomSuccess\": \"បន្ថែមបន្ទប់បានជោគជ័យ\",\r\n        \"removeRoomSuccess\": \"ដកបន្ទប់ចេញបានជោគជ័យ\",\r\n        \"roomUpgradeSuccess\": \"ដំឡើងកំណែប្រភេទបន្ទប់បានជោគជ័យ!\",\r\n        \"getSystemTimeFailed\": \"បរាជ័យក្នុងការទទួលបានពេលវេលាប្រព័ន្ធ\",\r\n        \"currentTimeNotInBatchRange\": \"ពេលវេលាបច្ចុប្បន្នមិននៅក្នុងចន្លោះពេលជំនាន់ទេ មិនអនុញ្ញាតឱ្យចូលស្នាក់នៅ\",\r\n        \"noRoomsToCheckin\": \"គ្មានបន្ទប់សម្រាប់ចូលស្នាក់នៅ សូមចាត់ចងបន្ទប់ជាមុនសិន\",\r\n        \"guestNameMissing\": \"មានឈ្មោះអ្នកចូលស្នាក់នៅបាត់ សូមបំពេញព័ត៌មានអ្នកចូលស្នាក់នៅជាមុនសិន\",\r\n        \"idNumberMissing\": \"មានលេខអត្តសញ្ញាណប័ណ្ណអ្នកចូលស្នាក់នៅទទេ\",\r\n        \"continueQuestion\": \"តើបន្តទេ?\",\r\n        \"makeCardBeforeArrangeRoom\": \"ធ្វើកាតមុនសូមចាត់ចងបន្ទប់ជាមុនសិន\",\r\n        \"hotelNoLockConfig\": \"សណ្ឋាគារមិនបានកំណត់រចនាសម្ព័ន្ធសោ សូមទៅកាន់ការកំណត់រចនាសម្ព័ន្ធសោបន្ទប់ដើម្បីជ្រើសរើសម៉ូដែលសោដែលសណ្ឋាគារប្រើ ប្រសិនបើគ្មានម៉ូដែលសោដែលត្រូវគ្នា សូមទាក់ទងអ្នកផ្តល់សេវាកម្ម។\",\r\n        \"makeCardSuccess\": \"ធ្វើកាតបន្ទប់បានជោគជ័យ\",\r\n        \"checkinBeforeArrangeRoom\": \"ចូលស្នាក់នៅមុនសូមចាត់ចងបន្ទប់ជាមុនសិន\",\r\n        \"atLeastOneRoomRequired\": \"ដកចេញបរាជ័យ៖ កម្មង់បម្រុងត្រូវតែរក្សាបន្ទប់យ៉ាងហោចណាស់មួយ\",\r\n        \"multipleGuestsCannotCollapse\": \"បន្ទប់នេះមានភ្ញៀវច្រើននាក់ មិនអាចបត់បានទេ\",\r\n        \"orderPastCheckoutTime\": \"កម្មង់បានកន្លងផុតពេលចេញចុងក្រោយ សូមកែប្រែពេលចេញកម្មង់\",\r\n        \"currentOrderArrangedCannotUpgrade\": \"កម្មង់បច្ចុប្បន្នមានការចាត់ចងបន្ទប់រួចហើយ ប្រសិនបើត្រូវការដំឡើងកំណែប្រភេទបន្ទប់ សូមបោះបង់ការចាត់ចងជាមុនសិន\",\r\n        \"downloadClientForCardReading\": \"ការអានកាតបន្ទប់អាចធ្វើបានតែក្នុង Hotel-Agent ប៉ុណ្ណោះ សូមទាញយក Hotel-Agent client។\",\r\n        \"cardInfo\": \"ព័ត៌មានកាត\",\r\n        \"guestNameMissingInRoom\": \"បន្ទប់លេខ {roomNo} មានអ្នកចូលស្នាក់នៅ {count} នាក់ដែលបាត់ឈ្មោះ សូមបំពេញព័ត៌មានអ្នកចូលស្នាក់នៅជាមុនសិន\",\r\n        \"idNumberMissingInRoom\": \"បន្ទប់លេខ {roomNo} មានអ្នកចូលស្នាក់នៅ {count} នាក់ដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ\",\r\n        \"idNumberMissingConfirm\": \"បន្ទប់លេខ {roomNo} មានអ្នកចូលស្នាក់នៅ {count} នាក់ដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ តើបន្តទេ?\",\r\n        \"batchGuestNameMissing\": \"បន្ទប់ខាងក្រោមមានអ្នកចូលស្នាក់នៅដែលបាត់ឈ្មោះ៖ {rooms} សូមបំពេញព័ត៌មានអ្នកចូលស្នាក់នៅជាមុនសិន\",\r\n        \"batchIdNumberMissing\": \"បន្ទប់ខាងក្រោមមានអ្នកចូលស្នាក់នៅដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ៖ {rooms}\",\r\n        \"batchIdNumberMissingConfirm\": \"បន្ទប់ខាងក្រោមមានអ្នកចូលស្នាក់នៅដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ៖ {rooms} តើបន្តទេ?\",\r\n        \"userCancelOperation\": \"អ្នកប្រើបានបោះបង់ប្រតិបត្តិការ\",\r\n        \"tip\": \"ជំនួយ\",\r\n        \"continue\": \"បន្ត\",\r\n        \"cancel\": \"បោះបង់\"\r\n      },\r\n      \"validation\": {\r\n        \"teamNameRequired\": \"ឈ្មោះក្រុមមិនអាចទទេបានទេ\",\r\n        \"currentRoomTypeRequired\": \"ប្រភេទបន្ទប់បច្ចុប្បន្នមិនអាចទទេបានទេ!\",\r\n        \"upgradeRoomTypeRequired\": \"ប្រភេទបន្ទប់ដំឡើងកំណែមិនអាចទទេបានទេ!\"\r\n      },\r\n      \"placeholders\": {\r\n        \"enterName\": \"ឈ្មោះ\",\r\n        \"enterPhone\": \"លេខទូរស័ព្ទ\",\r\n        \"enterIdNumber\": \"លេខអត្តសញ្ញាណប័ណ្ណ\",\r\n        \"phoneNumber\": \"លេខទូរស័ព្ទ\"\r\n      },\r\n      \"tableColumns\": {\r\n      \"roomNoRoomType\": \"លេខបន្ទប់/ប្រភេទបន្ទប់\",\r\n      \"discountPrice\": \"តម្លៃបញ្ចុះតម្លៃ\",\r\n      \"namePhone\": \"ឈ្មោះ/ទូរស័ព្ទ\",\r\n      \"idTypeIdNumber\": \"ប្រភេទអត្តសញ្ញាណប័ណ្ណ/លេខអត្តសញ្ញាណប័ណ្ណ\",\r\n      \"breakfastPortions\": \"អាហារពេលព្រឹក/ចំណែក\",\r\n      \"operations\": \"ប្រតិបត្តិការ\"\r\n      },\r\n      \"guestSrcValidation\": {\r\n        \"memberRequired\": \"សមាជិកមិនអាចទទេបានទេ\",\r\n        \"agentRequired\": \"ភ្នាក់ងារមិនអាចទទេបានទេ\",\r\n        \"protocolRequired\": \"ឯកតាពិធីការមិនអាចទទេបានទេ\",\r\n        \"selectOrInputRequired\": \"សូមជ្រើសរើស ឬបញ្ចូលកូដប្រភពភ្ញៀវ\",\r\n      },\r\n      \"teamNameRequired\": \"សូមបញ្ចូលឈ្មោះក្រុម\"\r\n    }\r\n  }\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { HotelDeviceSetRespVO } from '@/api/modules/pms/device/device.api.ts'\r\nimport type { DictDataModel } from '@/models'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { bookApi, customerApi, deviceApi, dictDataApi, hotelParamConfigApi, memberApi, orderApi, serverTimeApi, userApi } from '@/api/modules/index'\r\nimport roomCardLogApi from '@/api/modules/pms/room/roomCardLog.api'\r\nimport { BooleanEnum, CheckinType, ClientMethodEnum, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_ID_TYPE, GuestSrcType, IdType, NoType, OrderState } from '@/models'\r\n\r\nimport { SexEnum } from '@/models/dict/constants.ts'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { CardReader } from '@/store/websocket/CardReader.ts'\r\nimport { deduplicateByProperty, removePropertyFromArray } from '@/utils'\r\nimport { getGenderFromIdCard } from '@/utils/myStringUtil'\r\n\r\nimport { generateCardInfoHtml } from '@/utils/roomCardUtil.ts'\r\nimport ArrangeRoomsDialog from '@/views/room/components/arrangeRooms/arrangeRooms.vue'\r\nimport ArranGeRts from '@/views/room/components/arrangeRts/arrangeRts.vue'\r\nimport RoomEditPrice from '@/views/room/components/roomEditPrice/index.vue'\r\nimport { validateTipPhone } from '@/views/room/realtime/components/utils'\r\nimport { CreditCard } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\nimport { ElInput, ElMessage, ElMessageBox, ElNotification } from 'element-plus'\r\nimport { ref } from 'vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    bookNo: string[] // 预订单号\r\n    state: string // 状态\r\n    isEntryAccount: string // 是否可以入账操作\r\n  }>(),\r\n  {\r\n    bookNo: () => [],\r\n    state: '',\r\n    isEntryAccount: '0',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n  refresh: []\r\n  reload: []\r\n  seeDetailed: [value: object]\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n})\r\ninterface OrderPrice {\r\n  bkNum?: number // 赠早数\r\n  roomBkNum?: number // 房包早数\r\n  priceStrategyCode?: string // 价格策略代码\r\n  priceType?: string // 价格类型\r\n  price?: number // 价格（单位：分）\r\n  vipPrice?: number // 优惠价（单位：分）\r\n  priceDate?: string // 价格日期\r\n  week?: number // 星期几\r\n}\r\n\r\ninterface Together {\r\n  togetherCode?: string\r\n  rCode?: string // 房间代码\r\n  rNo?: string // 房号\r\n  name?: string // 客人姓名\r\n  phone?: string // 客人电话\r\n  idType?: string // 证件类型\r\n  idNo?: string // 证件号码\r\n  sex?: string // 性别\r\n  nation?: string // 民族\r\n  address?: string\r\n  isMadeCard?: string // 是否已制卡\r\n  isMain?: string // 是否主宾客\r\n  state?: string // 状态\r\n}\r\n\r\ninterface BookRoom {\r\n  orderNo?: string // 订单号\r\n  rtCode?: string // 房型代码\r\n  rtName?: string // 房型名称\r\n  planCheckinTime?: string // 预抵时间\r\n  planCheckoutTime?: string // 预离时间\r\n  priceType?: string // 价格类型\r\n  rCode?: string // 房间代码\r\n  rNo?: string // 房号\r\n  lockNo?: string // 锁号\r\n  mac?: string // 锁mac地址\r\n  buildNo?: string // 楼栋号\r\n  floorNo?: string // 楼层号\r\n  state?: string // 订单状态\r\n  days?: number // 天数\r\n  togethers: Together[] // 同住人列表\r\n  orderPrices: OrderPrice[] // 每日房价\r\n}\r\n\r\ninterface Batch {\r\n  batchNo?: string // 批次号\r\n  days?: number // 天数\r\n  planCheckinTime?: string // 预低时间\r\n  planCheckoutTime?: string // 预离时间\r\n  bookRooms: BookRoom[] // 房间列表\r\n}\r\n\r\ninterface TeamBookAllDataRespVO {\r\n  id?: number // id\r\n  gcode?: string // 集团代码\r\n  hcode?: string // 门店代码\r\n  bookNo?: string // 预订单号\r\n  channelCode?: string // 渠道代码\r\n  orderSource?: string // 订单来源\r\n  orderSourceName?: string // 订单来源名称\r\n  guestSrcType?: string // 客源类型\r\n  guestSrcTypeName?: string // 客源类型名称\r\n  bookType?: string // 预订类型\r\n  teamCode?: string // 团队代码\r\n  teamName?: string // 团队名称\r\n  checkinType?: string // 入住类型\r\n  checkinTypeName?: string // 入住类型名称\r\n  guestCode?: string // 客人代码\r\n  guestName?: string // 客人名称\r\n  contractNo?: string // 合同号\r\n  contact?: string // 预订人\r\n  phone?: string // 预订人电话\r\n  guarantyStyle?: string // 担保方式\r\n  guarantyStyleName?: string // 担保方式名称\r\n  state?: string // 状态\r\n  isSendSms?: string // 是否发短信\r\n  seller?: string // 销售员\r\n  outOrderNo?: string // 外部订单号\r\n  remark?: string // 订单备注\r\n  createTime?: string // 创建时间\r\n  batches: Batch[] // 批次列表\r\n}\r\nconst formRef = ref<FormInstance>()\r\n// 初始化 Vue 响应式对象\r\nconst dt = ref<TeamBookAllDataRespVO>({\r\n  batches: [], // 初始化必需的batches属性为空数组\r\n})\r\n/** 列表查询条件 */\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n\r\n/** 证件类型列表 */\r\nconst idTypes = ref<DictDataModel[]>([])\r\n/** 入住类型列表 */\r\nconst checkinTypes = ref<DictDataModel[]>([])\r\n/** 客源类型列表 */\r\nconst guestSrcTypes = ref<DictDataModel[]>([])\r\n\r\n/** 房间 */\r\nconst rooms = ref([])\r\n/** 是否排房 */\r\nconst isAlone = ref(false)\r\n/** 服务器时间 */\r\nconst serverTime = ref('')\r\n/** 办理入住表格加载状态 */\r\nconst bookRoomsLoading = ref(false)\r\n/** 展开办理入住行的 keys 数组 */\r\nconst expandedRows = ref([])\r\n/** 升级房型弹窗 */\r\nconst easyDialogRef = ref()\r\n/** 获取form表格ref */\r\nconst easyFormRef = ref()\r\n// 添加房间\r\nconst addRtsVisible = ref(false)\r\n/** 弹窗内容(初始化) */\r\nconst _modelForm = reactive({\r\n  ...queryParams,\r\n  /** 升级方式 */\r\n  upgradeMethod: 0,\r\n  /** 预订单号 */\r\n  bookNo: props.bookNo[0],\r\n  /** 房型代码 */\r\n  rtCode: '',\r\n  /** 升级房型代码 */\r\n  upgradeRtCode: '',\r\n  /** 需要升级的订单号 */\r\n  orderNo: '',\r\n})\r\n/** 房间信息表格列表 */\r\nconst columns = [\r\n  {\r\n    prop: 'room',\r\n    label: t('tableColumns.roomNoRoomType'),\r\n    width: '250px',\r\n    align: 'left',\r\n  },\r\n  {\r\n    prop: 'price',\r\n    label: t('tableColumns.discountPrice'),\r\n    width: '120px',\r\n    align: 'left',\r\n  },\r\n  {\r\n    prop: 'namePhone',\r\n    label: t('tableColumns.namePhone'),\r\n    width: '240px',\r\n    align: 'left',\r\n  },\r\n  {\r\n    prop: 'CardNumber',\r\n    label: t('tableColumns.idTypeIdNumber'),\r\n    width: '440px',\r\n    align: 'left',\r\n  },\r\n  {\r\n    prop: 'bk',\r\n    label: t('tableColumns.breakfastPortions'),\r\n    width: '150px',\r\n    align: 'center',\r\n  },\r\n  {\r\n    prop: 'cz',\r\n    label: t('tableColumns.operations'),\r\n    width: '240px',\r\n    align: 'center',\r\n  },\r\n]\r\n/** 弹窗内容 */\r\nconst modelForm = ref({ ..._modelForm })\r\n/** 当前房型 */\r\nconst roomOptions = ref<any[]>([])\r\ninterface updateRoomTypes {\r\n  /** 可超数 */\r\n  canOverNum?: number\r\n  /** 可售数 */\r\n  canSellNum?: number\r\n  /** 房型代码 */\r\n  rtCode?: string\r\n  /** 房型名称 */\r\n  rtName?: string\r\n}\r\n\r\n// 计算属性\r\nconst guestSrcTypeValidationMessage = computed(() => {\r\n  if (dt.value.guestSrcType === GuestSrcType.MEMBER) {\r\n    return t('guestSrcValidation.memberRequired')\r\n  } else if (dt.value.guestSrcType === GuestSrcType.AGENT) {\r\n    return t('guestSrcValidation.agentRequired')\r\n  } else if (dt.value.guestSrcType === GuestSrcType.PROTOCOL) {\r\n    return t('guestSrcValidation.protocolRequired')\r\n  } else {\r\n    return t('guestSrcValidation.selectOrInputRequired')\r\n  }\r\n})\r\n\r\n// 计算属性\r\nconst isGuestSrcTypeNotIndividual = computed(() => {\r\n  return dt.value.guestSrcType !== GuestSrcType.WALK_IN // GuestSrcType.WALK_IN 表示散客\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  teamName: [{ required: true, message: t('teamNameRequired'), trigger: 'blur' }],\r\n  guestCode: [\r\n    {\r\n      required: isGuestSrcTypeNotIndividual.value,\r\n      message: guestSrcTypeValidationMessage.value,\r\n      trigger: 'blur',\r\n    },\r\n  ],\r\n})\r\nonMounted(() => {\r\n  initConstants()\r\n  getTeamBook()\r\n  getSeller()\r\n  getServerTime()\r\n  // 获取门锁配置信息\r\n  // getHotelDoorConfig()\r\n  getFrontConfig()\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n})\r\nconst popoverContent = ref()\r\nonUnmounted(() => {\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n  if (popoverContent.value && popoverContent.value.length > 0) {\r\n    popoverContent.value.removeEventListener('scroll', handleScroll)\r\n  }\r\n})\r\n// 不知道这个是干啥的\r\nconst scrollTop = ref<number | null>(null)\r\nfunction handleScroll() {\r\n  scrollTop.value = document.documentElement.scrollTop\r\n}\r\n/** 中介列表和 协议单位列表 */\r\nconst agents = ref<{ paCode: string; paName: string; sellLevel: string }[]>([])\r\n/** 将常量初始化 */\r\nfunction initConstants() {\r\n  dictDataApi.getDictDataBatch([DICT_TYPE_ID_TYPE, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE]).then((res: any) => {\r\n    idTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_ID_TYPE)\r\n    checkinTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n    guestSrcTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE).filter((item: { code: string }) => item.code !== '0' && item.code !== '0')\r\n  })\r\n}\r\n\r\nconst frontConfig = ref({\r\n  dirtyAlert: '', // 脏房入住提醒 1是 0否\r\n  idOneRoom: '', // 一证件只能办理一间房\r\n  needId: '', // 入住身份证必填\r\n  orderNotice: '', // 订单声音提醒\r\n})\r\n\r\nfunction getFrontConfig() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  hotelParamConfigApi.getHotelParamConfigFront(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      frontConfig.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取服务器时间 */\r\nasync function getServerTime() {\r\n  const res = await serverTimeApi.serverTime(userStore.gcode, '0')\r\n  serverTime.value = res.data\r\n}\r\n/** 获取中介和协议单位下拉数据 */\r\nfunction getAgents(type: number) {\r\n  customerApi\r\n    .simpleList({\r\n      gcode: userStore.gcode,\r\n      belongHcode: userStore.hcode,\r\n      paType: type,\r\n      isEnable: BooleanEnum.YES,\r\n    })\r\n    .then((res: any) => {\r\n      agents.value = res.data\r\n    })\r\n}\r\n\r\nconst sellers = ref<{ username: string; nickname: string }[]>([])\r\nfunction getSeller() {\r\n  userApi.listSeller(queryParams).then((res: any) => {\r\n    if (res.code === 0) {\r\n      sellers.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 预订单详情 */\r\nfunction getTeamBook() {\r\n  bookRoomsLoading.value = true\r\n  orderApi.getTeamBook({ bookNo: props.bookNo[0] }).then((res: any) => {\r\n    bookRoomsLoading.value = false\r\n    if (res.code === 0) {\r\n      dt.value = res.data\r\n      // 循环dt.value.batches.bookRooms,如果togeters为空，则初始化一个togeter对象\r\n      dt.value.batches?.forEach((batch: any) => {\r\n        batch.bookRooms.forEach((bookRoom: any) => {\r\n          if (!bookRoom.togethers || bookRoom.togethers.length === 0) {\r\n            bookRoom.togethers = [\r\n              {\r\n                togetherCode: '', // 新创建的客人不设置togetherCode，让后端生成\r\n                rCode: '',\r\n                rNo: '',\r\n                sex: SexEnum.SECRECY.toString(),\r\n                nation: '',\r\n                address: '',\r\n                name: '',\r\n                phone: '',\r\n                idType: IdType.IDCERT,\r\n                idNo: '',\r\n                isMadeCard: '0',\r\n                isMain: '1',\r\n                state: '',\r\n              },\r\n            ]\r\n          }\r\n\r\n          // 初始化原始数据副本，用于变更检测\r\n          bookRoom.togethers.forEach((together: any, index: number) => {\r\n            // 为新创建的客人生成唯一的临时key\r\n            let togetherKey: string\r\n            if (together.togetherCode) {\r\n              togetherKey = `${bookRoom.orderNo}_${together.togetherCode}`\r\n            } else {\r\n              // 新创建的客人使用临时key，包含索引以确保唯一性\r\n              togetherKey = `${bookRoom.orderNo}_new_${index}`\r\n            }\r\n\r\n            originalData.value[togetherKey] = {\r\n              name: together.name || '',\r\n              phone: together.phone || '',\r\n              idNo: together.idNo || '',\r\n              idType: together.idType || '',\r\n            }\r\n          })\r\n\r\n          // 如果房间住客超过1人，自动展开该行\r\n          if (bookRoom.togethers && bookRoom.togethers.length > 1) {\r\n            if (!expandedRows.value.includes(bookRoom.orderNo)) {\r\n              expandedRows.value.push(bookRoom.orderNo)\r\n            }\r\n          }\r\n        })\r\n      })\r\n    }\r\n  })\r\n}\r\nconst editTeamInfo = ref(false)\r\nfunction editTeamInfoFun() {\r\n  editTeamInfo.value = true\r\n}\r\nfunction save() {\r\n  if (formRef.value) {\r\n    formRef.value.validate((valid: any) => {\r\n      if (valid) {\r\n        const params = {\r\n          bookNo: dt.value.bookNo,\r\n          channelCode: dt.value.channelCode,\r\n          teamName: dt.value.teamName,\r\n          guestSrcType: dt.value.guestSrcType,\r\n          checkinType: dt.value.checkinType,\r\n          guestCode: dt.value.guestCode,\r\n          guestName: dt.value.guestName,\r\n          teamCode: dt.value.teamCode,\r\n          contractNo: dt.value.contractNo,\r\n          contact: dt.value.contact,\r\n          phone: dt.value.phone,\r\n          guarantyStyle: dt.value.guarantyStyle,\r\n          seller: dt.value.seller,\r\n          remark: dt.value.remark,\r\n        }\r\n        bookApi.updateBaseTeamBookInfo(params).then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success({\r\n              message: t('messages.modifySuccess'),\r\n              type: 'success',\r\n              center: true,\r\n            })\r\n            editTeamInfo.value = false\r\n          }\r\n        })\r\n      }\r\n    })\r\n  }\r\n}\r\n\r\n// 添加房间按钮触发\r\nfunction handleAddRoom() {\r\n  addRtsVisible.value = true\r\n}\r\n\r\n// 批次删除房间\r\nfunction removeBatch(batch: Batch) {\r\n  // 如果当前预订单只有一个批次，则不允许删除\r\n  const isSingleBatch = dt.value.batches.length === 1\r\n  if (isSingleBatch) {\r\n    ElMessage.warning(t('messages.onlyOneBatchCannotDelete'))\r\n    return\r\n  }\r\n  bookApi\r\n    .batchDeleteBookRoom({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      bookNo: props.bookNo[0],\r\n      batchNo: batch.batchNo,\r\n      teamCode: dt.value.teamCode,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        getTeamBook()\r\n        emits('refresh')\r\n        ElMessage.success(t('messages.deleteSuccess'))\r\n      }\r\n    })\r\n}\r\n\r\n// 批量排房\r\nfunction batchArrangeRooms(batch: any) {}\r\n\r\n// 批量自动排房\r\nfunction batchAutoArrangeRooms(batch: any) {\r\n  bookApi\r\n    .autoArrangeRooms({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      bookNo: props.bookNo[0],\r\n      batchNo: batch.batchNo,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('messages.arrangeRoomSuccess'))\r\n        getTeamBook()\r\n      }\r\n    })\r\n}\r\n\r\nfunction batchWriteLockCard(batch: any) {\r\n  // 实现批量制卡逻辑\r\n}\r\nfunction batchChangePrice(batch: any) {\r\n  // 实现批量改价逻辑\r\n}\r\n\r\n// 批量入住逻辑\r\nasync function batchCheckIn(batch: any) {\r\n  // 获取当前服务器时间\r\n  const sysTimeString = await getSysTime()\r\n  if (!sysTimeString) {\r\n    ElMessage.error(t('messages.getSystemTimeFailed'))\r\n    return\r\n  }\r\n\r\n  // 将服务器时间转换为dayjs对象\r\n  const currentTime = dayjs(Number(sysTimeString))\r\n  // 获取批次的预抵和预离时间\r\n  const planCheckinTime = dayjs(batch.planCheckinTime).startOf('day')\r\n  const planCheckoutTime = dayjs(batch.planCheckoutTime).endOf('day')\r\n  // 判断当前时间是否在批次时间范围内\r\n  if (currentTime.isBefore(planCheckinTime) || currentTime.isAfter(planCheckoutTime)) {\r\n    ElMessage.warning(t('messages.currentTimeNotInBatchRange'))\r\n    return\r\n  }\r\n\r\n  // 过滤出已排房的房间\r\n  const roomsWithRNo = batch.bookRooms.filter((room) => room.rNo && room.state === OrderState.IN_BOOKING)\r\n\r\n  if (roomsWithRNo.length === 0) {\r\n    ElMessage.warning(t('messages.noRoomsToCheckin'))\r\n    return\r\n  }\r\n  // 检查所有房间是否都有入住人信息\r\n  let hasMissingName = false\r\n  let hasMissingIdNo = false\r\n  const missingNameRooms: string[] = []\r\n  const missingIdNoRooms: string[] = []\r\n\r\n  for (const room of roomsWithRNo) {\r\n    // 检查入住人姓名\r\n    const missingNameGuests = room.togethers.filter((together) => !together.name)\r\n    if (missingNameGuests.length > 0) {\r\n      hasMissingName = true\r\n      missingNameRooms.push(room.rNo)\r\n      continue\r\n    }\r\n\r\n    // 检查身份证号码\r\n    const missingIdNoGuests = room.togethers.filter((together) => together.idType === IdType.IDCERT && !together.idNo)\r\n    if (missingIdNoGuests.length > 0) {\r\n      hasMissingIdNo = true\r\n      missingIdNoRooms.push(room.rNo)\r\n    }\r\n  }\r\n\r\n  // 统一提示入住人姓名缺失\r\n  if (hasMissingName) {\r\n    ElMessage.warning(t('messages.batchGuestNameMissing', { rooms: missingNameRooms.join('，') }))\r\n    return\r\n  }\r\n\r\n  // 统一提示身份证号码缺失\r\n  if (hasMissingIdNo) {\r\n    if (frontConfig.value.needId === '1') {\r\n      ElMessage.warning(t('messages.batchIdNumberMissing', { rooms: missingIdNoRooms.join('，') }))\r\n      return\r\n    } else {\r\n      // 加个提示，只提示一次\r\n      await ElMessageBox.confirm(t('messages.batchIdNumberMissingConfirm', { rooms: missingIdNoRooms.join('，') }), t('messages.tip'), {\r\n        confirmButtonText: t('messages.continue'),\r\n        cancelButtonText: t('messages.cancel'),\r\n        type: 'warning',\r\n      }).catch(() => {\r\n        throw new Error(t('messages.userCancelOperation'))\r\n      })\r\n    }\r\n  }\r\n\r\n  // 构建请求参数\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    bookNo: props.bookNo[0],\r\n    batchNo: batch.batchNo,\r\n    isSendSms: BooleanEnum.NO,\r\n    orders: batch.bookRooms.map((bookRoom: BookRoom) => ({\r\n      orderNo: bookRoom.orderNo,\r\n      bkNum: bookRoom.orderPrices[0].bkNum,\r\n      persons: bookRoom.togethers.map((together: Together) => {\r\n        const person: any = {\r\n          name: together.name,\r\n          sex: together.sex || SexEnum.SECRECY.toString(), // 默认为未知性别\r\n          nation: together.nation || '',\r\n          address: together.address || '',\r\n          phone: together.phone || '',\r\n          idType: together.idType || IdType.OTHER,\r\n          idNo: together.idNo || '',\r\n          isMadeCard: together.isMadeCard || '0',\r\n        }\r\n        // togetherCode有就传，没有就不传\r\n        if (together.togetherCode) {\r\n          person.togetherCode = together.togetherCode\r\n        }\r\n        return person\r\n      }),\r\n    })),\r\n  }\r\n\r\n  // 调用后端接口进行批量入住\r\n  orderApi.bookCheckIn(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success(t('messages.batchCheckinSuccess'))\r\n      getTeamBook()\r\n      emits('refresh')\r\n    }\r\n  })\r\n}\r\n\r\n/**\r\n * 获取酒店的门锁配置\r\n */\r\nconst hotelDoorConfig = ref<HotelDeviceSetRespVO>()\r\n/**\r\n * 获取酒店的门锁配置\r\n */\r\nfunction getHotelDoorConfig(lockVersion?: string): Promise<void> {\r\n  const params = {\r\n    ...queryParams,\r\n    ...(lockVersion && { lockVersion }),\r\n  }\r\n  return deviceApi.getHotelDoor(params).then((res: any) => {\r\n    if (res.code === 0 && res.data) {\r\n      hotelDoorConfig.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取服务器的系统时间戳 */\r\nfunction getSysTime(): Promise<string> {\r\n  return serverTimeApi.serverTime(userStore.gcode, '3').then((res: any) => {\r\n    if (res.code === 0) {\r\n      return res.data // 返回字符串类型的时间戳\r\n    }\r\n    return ''\r\n  })\r\n}\r\n/** 制房卡 */\r\nasync function writeLockCard(row: any, newCard: boolean) {\r\n  // 判断是否已经排房，如果没有排房，则不能制卡\r\n  if (!row.rNo) {\r\n    ElMessage.warning(t('messages.makeCardBeforeArrangeRoom'))\r\n    return\r\n  }\r\n  if (!isInClient()) {\r\n    return\r\n  }\r\n\r\n  if (!row.lockVersion) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('messages.hotelNoLockConfig'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n\r\n  await getHotelDoorConfig(row.lockVersion)\r\n\r\n  // 如果没有门锁的配置，则提示\r\n  if (!hotelDoorConfig.value) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('messages.hotelNoLockConfig'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n  // 每次点击都要先关闭websocket\r\n  if (CardReader.isConnected) {\r\n    CardReader.closeSocket()\r\n    // 等待WebSocket完全关闭\r\n    await new Promise((resolve) => setTimeout(resolve, 300))\r\n  }\r\n\r\n  CardReader.initCardReader((message: string) => {\r\n    const data = JSON.parse(message)\r\n    if (data && data.succeed === true && data.method === ClientMethodEnum.WRITELOCKCARD) {\r\n      if (data.cardInfo) {\r\n        ElNotification({\r\n          title: t('messages.cardInfo'),\r\n          dangerouslyUseHTMLString: true,\r\n          message: generateCardInfoHtml(data),\r\n          position: 'bottom-left',\r\n          type: 'success',\r\n        })\r\n\r\n        // 异步记录制卡日志，不影响主业务\r\n        roomCardLogApi\r\n          .createRoomCardLog({\r\n            gcode: userStore.gcode,\r\n            hcode: userStore.hcode,\r\n            name: row.guestName,\r\n            rNo: row.rNo,\r\n            cardNo: data.cardInfo.cardNo,\r\n            type: newCard ? '0' : '1',\r\n            periodTime: dayjs(Number(data.cardInfo.expire) * 1000).format('YYYY-MM-DD HH:mm:ss'),\r\n          })\r\n          .then((res: any) => {\r\n            if (res.code === 0) {\r\n              console.log('制卡日志记录成功')\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            // 日志记录失败不影响主业务，只记录错误\r\n            console.error('制卡日志记录失败:', error)\r\n          })\r\n      }\r\n      ElMessage({\r\n        message: `${row.rNo}${t('messages.makeCardSuccess')}`,\r\n        type: 'success',\r\n      })\r\n    }\r\n  })\r\n  // 获取系统时间戳\r\n  const sysTimeString = await getSysTime() // 调用异步方法\r\n  if (!sysTimeString) {\r\n    console.error('Failed to get system time')\r\n    return\r\n  }\r\n  // 单位秒\r\n  const timeStampInSeconds = Math.floor(Number.parseInt(sysTimeString, 10) / 1000)\r\n  // 构建 json 对象\r\n  const json = {\r\n    method: ClientMethodEnum.WRITELOCKCARD, // 写门锁卡\r\n    lockVer: hotelDoorConfig.value.version, // 门锁类型\r\n    cardInfo: {\r\n      roomNo: row.rNo, // 房间号，字符串\r\n      checkin: timeStampInSeconds, // 入住时间戳，单位秒，整型\r\n      expire: Math.floor(dayjs(row.planCheckoutTime).valueOf() / 1000), // 到期时间戳，单位秒，整型\r\n      allowLockOut: hotelDoorConfig.value?.allowLockOut === BooleanEnum.YES, // 是否允许开反锁，布尔值\r\n      replaceCard: newCard, // 新卡替换旧卡，布尔值\r\n      checkTime: hotelDoorConfig.value?.checkTime === BooleanEnum.YES, // 检查入住时间，布尔值\r\n      lockNo: row.lockNo, // 门锁号，字符串\r\n      mac: row.mac, //  门锁mac地址\r\n      buildNo: row.buildNo, // 楼栋号\r\n      floorNo: row.floorNo, // 楼层号\r\n    },\r\n  }\r\n\r\n  // 检查 confParameter 是否有值\r\n  if (hotelDoorConfig.value.conf && hotelDoorConfig.value.conf.length > 0) {\r\n    // 将 confParameter 转换为 cardInfo 对象\r\n    hotelDoorConfig.value.conf.forEach((param) => {\r\n      json.cardInfo[param.parameterCode] = param.parameterContent\r\n    })\r\n  }\r\n\r\n  // 将 json 对象转换为字符串\r\n  const jsonString = JSON.stringify(json)\r\n  console.log('jsonString', jsonString)\r\n  const timer = setInterval(() => {\r\n    if (CardReader.isConnected) {\r\n      CardReader.handleLockCard(jsonString)\r\n      clearInterval(timer)\r\n    }\r\n  }, 200)\r\n}\r\n\r\nfunction isInClient(): boolean {\r\n  if (typeof (window as any).CallBridge === 'undefined' && typeof (window as any).__RUNNING_IN_PMS_AGENT__ === 'undefined') {\r\n    // 提示没有下载客户端不能读取房卡，并提示下载客户端的路径\r\n    const downloadLink = `<a href=\"${import.meta.env.VITE_APP_CLIENT_DOWNLOAD_URL}\" target=\"_blank\">下载Hotel-Agent客户端</a>`\r\n    ElMessage({\r\n      showClose: true,\r\n      message: `读取房卡只能在Hotel-Agent中操作，请${downloadLink}。`,\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/** 读取身份证信息 */\r\nfunction readIdNo(scope: any, props?: any) {\r\n  if (!isInClient()) {\r\n    return\r\n  }\r\n  // 每次点击都要先关闭websocket\r\n  if (CardReader.isConnected) {\r\n    CardReader.closeSocket()\r\n  }\r\n  // 处理读取结果并更新每个住客的信息\r\n  CardReader.initCardReader((message: string) => {\r\n    const data = JSON.parse(message) // 假设 message 格式为 { idNo: \"身份证号\", name: \"姓名\" }\r\n    if (data) {\r\n      const idNo = data.peopleIDCode\r\n      const guestName = data.peopleName\r\n      const address = data.peopleAddress\r\n      let sex = SexEnum.SECRECY.toString()\r\n      // 优先使用返回的性别信息，如果没有则根据身份证号码判断\r\n      if (data.peopleSex) {\r\n        sex = data.peopleSex === '男' ? SexEnum.MALE.toString() : SexEnum.FEMALE.toString()\r\n      } else if (data.peopleIDCode) {\r\n        sex = getGenderFromIdCard(data.peopleIDCode).toString()\r\n      } else {\r\n        sex = SexEnum.SECRECY.toString()\r\n      }\r\n      const nation = data.peopleNation.indexOf('族') ? data.peopleNation : `${data.peopleNation}族`\r\n      // 同住人赋值\r\n      if (props) {\r\n        // 循环dt的批次，循环bookRooms，循环togethers，找到togetherCode==scope.row.togetherCode的together,然后赋值\r\n        for (let i = 0; i < dt.value.batches.length; i++) {\r\n          for (let j = 0; j < dt.value.batches[i].bookRooms.length; j++) {\r\n            for (let k = 0; k < dt.value.batches[i].bookRooms[j].togethers.length; k++) {\r\n              if (dt.value.batches[i].bookRooms[j].togethers[k].togetherCode === scope.row.togetherCode) {\r\n                dt.value.batches[i].bookRooms[j].togethers[k].idNo = idNo\r\n                dt.value.batches[i].bookRooms[j].togethers[k].name = guestName\r\n                dt.value.batches[i].bookRooms[j].togethers[k].address = address\r\n                dt.value.batches[i].bookRooms[j].togethers[k].sex = sex\r\n                dt.value.batches[i].bookRooms[j].togethers[k].nation = nation\r\n              }\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        // 循环dt的批次，循环bookRooms，找到orderNo==scope.row.orderNo的bookRoom,然后获取togethers，然后给第一个together赋值\r\n        for (let i = 0; i < dt.value.batches.length; i++) {\r\n          for (let j = 0; j < dt.value.batches[i].bookRooms.length; j++) {\r\n            if (dt.value.batches[i].bookRooms[j].orderNo === scope.row.orderNo) {\r\n              dt.value.batches[i].bookRooms[j].togethers[0].idNo = idNo\r\n              dt.value.batches[i].bookRooms[j].togethers[0].name = guestName\r\n              dt.value.batches[i].bookRooms[j].togethers[0].address = address\r\n              dt.value.batches[i].bookRooms[j].togethers[0].sex = sex\r\n              dt.value.batches[i].bookRooms[j].togethers[0].nation = nation\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  })\r\n  // 触发读取身份证信息\r\n  const timer = setInterval(() => {\r\n    if (CardReader.isConnected) {\r\n      CardReader.readIdCard()\r\n      clearInterval(timer)\r\n    }\r\n  }, 200)\r\n}\r\n\r\n// 移除客人\r\nfunction removeGuest(row: any) {\r\n  // 显示确认对话框\r\n  ElMessageBox.confirm(`确定要移除客人\"${row.name || '未命名客人'}\"吗？`, '确认移除', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning',\r\n  })\r\n    .then(() => {\r\n      // 用户确认后执行移除操作\r\n      performRemoveGuest(row)\r\n    })\r\n    .catch(() => {\r\n      // 用户取消，不执行任何操作\r\n      console.log('用户取消移除客人操作')\r\n    })\r\n}\r\n\r\n/** 执行移除客人操作 */\r\nfunction performRemoveGuest(row: any) {\r\n  // 如果客人有宾客代码，则调用后端接口移除\r\n  if (row.togetherCode && row.togetherCode.trim() !== '') {\r\n    console.log('移除客人，宾客代码:', row.togetherCode)\r\n\r\n    const requestData = {\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      togetherCode: row.togetherCode,\r\n    }\r\n\r\n    console.log('发送移除客人请求，参数:', requestData)\r\n\r\n    orderApi\r\n      .removeTogether(requestData)\r\n      .then((res: any) => {\r\n        if (res.code === 0) {\r\n          ElMessage.success('移除客人成功')\r\n\r\n          // 后端移除成功后，从前端数据中移除\r\n          removeGuestFromFrontend(row)\r\n\r\n          // 清理原始数据中的相关记录\r\n          cleanupOriginalData(row.togetherCode)\r\n        } else {\r\n          ElMessage.error(res.msg || '移除客人失败')\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error('移除客人失败:', error)\r\n        console.error('错误详情:', error.response || error.message || error)\r\n        ElMessage.error(`移除客人失败: ${error.response?.data?.msg || error.message || '未知错误'}`)\r\n      })\r\n  } else {\r\n    // 如果没有宾客代码，只从前端移除（新创建但未保存的客人）\r\n    console.log('移除未保存的客人')\r\n    removeGuestFromFrontend(row)\r\n\r\n    // 清理原始数据中的相关记录\r\n    cleanupOriginalData('')\r\n\r\n    ElMessage.success('移除客人成功')\r\n  }\r\n}\r\n\r\n/** 从前端数据中移除客人 */\r\nfunction removeGuestFromFrontend(targetGuest: any) {\r\n  for (let i = 0; i < dt.value.batches.length; i++) {\r\n    for (let j = 0; j < dt.value.batches[i].bookRooms.length; j++) {\r\n      for (let k = 0; k < dt.value.batches[i].bookRooms[j].togethers.length; k++) {\r\n        const together = dt.value.batches[i].bookRooms[j].togethers[k]\r\n\r\n        // 如果有togetherCode，按togetherCode匹配\r\n        if (targetGuest.togetherCode && together.togetherCode === targetGuest.togetherCode) {\r\n          dt.value.batches[i].bookRooms[j].togethers.splice(k, 1)\r\n          console.log('已从前端移除客人，宾客代码:', targetGuest.togetherCode)\r\n          return\r\n        }\r\n\r\n        // 如果没有togetherCode，按对象引用匹配（新创建的客人）\r\n        if (!targetGuest.togetherCode && together === targetGuest) {\r\n          dt.value.batches[i].bookRooms[j].togethers.splice(k, 1)\r\n          console.log('已从前端移除未保存的客人')\r\n          return\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/** 清理原始数据中的相关记录 */\r\nfunction cleanupOriginalData(togetherCode: string) {\r\n  let keysToDelete: string[] = []\r\n\r\n  if (togetherCode && togetherCode.trim() !== '') {\r\n    // 有宾客代码的情况，删除包含该代码的记录\r\n    keysToDelete = Object.keys(originalData.value).filter((key) => key.includes(togetherCode))\r\n  } else {\r\n    // 没有宾客代码的情况，删除临时记录（new_开头的）\r\n    keysToDelete = Object.keys(originalData.value).filter((key) => key.includes('_new_'))\r\n  }\r\n\r\n  keysToDelete.forEach((key) => {\r\n    delete originalData.value[key]\r\n  })\r\n\r\n  console.log('已清理原始数据记录:', keysToDelete)\r\n}\r\n\r\n// 存储原始数据的副本，用于变更检测\r\nconst originalData = ref<any>({})\r\n\r\n/** 身份证号码校验 */\r\nfunction validateIdCard(idNo: string): { isValid: boolean; message: string } {\r\n  if (!idNo || idNo.trim() === '') {\r\n    return { isValid: true, message: '' } // 空值不校验\r\n  }\r\n\r\n  const idCard = idNo.trim().toUpperCase()\r\n\r\n  // 18位身份证号码校验\r\n  if (idCard.length === 18) {\r\n    // 格式校验：前17位数字，最后一位数字或X\r\n    const reg = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9X]$/i\r\n    if (!reg.test(idCard)) {\r\n      return { isValid: false, message: '身份证号码格式不正确' }\r\n    }\r\n\r\n    // 校验码验证\r\n    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]\r\n    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']\r\n\r\n    let sum = 0\r\n    for (let i = 0; i < 17; i++) {\r\n      sum += Number.parseInt(idCard[i]) * weights[i]\r\n    }\r\n\r\n    const checkCode = checkCodes[sum % 11]\r\n    if (idCard[17] !== checkCode) {\r\n      return { isValid: false, message: '身份证号码校验位不正确' }\r\n    }\r\n\r\n    // 日期校验\r\n    const year = Number.parseInt(idCard.substring(6, 10))\r\n    const month = Number.parseInt(idCard.substring(10, 12))\r\n    const day = Number.parseInt(idCard.substring(12, 14))\r\n\r\n    const currentYear = new Date().getFullYear()\r\n    if (year < 1900 || year > currentYear) {\r\n      return { isValid: false, message: '身份证号码中的年份不正确' }\r\n    }\r\n\r\n    if (month < 1 || month > 12) {\r\n      return { isValid: false, message: '身份证号码中的月份不正确' }\r\n    }\r\n\r\n    if (day < 1 || day > 31) {\r\n      return { isValid: false, message: '身份证号码中的日期不正确' }\r\n    }\r\n\r\n    // 检查日期是否真实存在\r\n    const date = new Date(year, month - 1, day)\r\n    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {\r\n      return { isValid: false, message: '身份证号码中的日期不存在' }\r\n    }\r\n\r\n    return { isValid: true, message: '身份证号码格式正确' }\r\n  }\r\n\r\n  // 15位身份证号码校验（老版本）\r\n  else if (idCard.length === 15) {\r\n    const reg = /^[1-9]\\d{7}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}$/\r\n    if (!reg.test(idCard)) {\r\n      return { isValid: false, message: '身份证号码格式不正确' }\r\n    }\r\n\r\n    // 日期校验（15位身份证是19xx年）\r\n    const year = 1900 + Number.parseInt(idCard.substring(6, 8))\r\n    const month = Number.parseInt(idCard.substring(8, 10))\r\n    const day = Number.parseInt(idCard.substring(10, 12))\r\n\r\n    if (year < 1900 || year > 1999) {\r\n      return { isValid: false, message: '身份证号码中的年份不正确' }\r\n    }\r\n\r\n    if (month < 1 || month > 12) {\r\n      return { isValid: false, message: '身份证号码中的月份不正确' }\r\n    }\r\n\r\n    if (day < 1 || day > 31) {\r\n      return { isValid: false, message: '身份证号码中的日期不正确' }\r\n    }\r\n\r\n    return { isValid: true, message: '身份证号码格式正确' }\r\n  } else {\r\n    return { isValid: false, message: '身份证号码长度不正确，应为15位或18位' }\r\n  }\r\n}\r\n\r\n/** 护照号码校验 */\r\nfunction validatePassport(idNo: string): { isValid: boolean; message: string } {\r\n  if (!idNo || idNo.trim() === '') {\r\n    return { isValid: true, message: '' }\r\n  }\r\n\r\n  const passport = idNo.trim().toUpperCase()\r\n\r\n  // 护照号码一般为8-9位，包含字母和数字\r\n  const reg = /^[A-Z0-9]{8,9}$/\r\n  if (!reg.test(passport)) {\r\n    return { isValid: false, message: '护照号码格式不正确，应为8-9位字母和数字组合' }\r\n  }\r\n\r\n  return { isValid: true, message: '护照号码格式正确' }\r\n}\r\n\r\n/** 其他证件号码校验 */\r\nfunction validateOtherIdCard(idNo: string): { isValid: boolean; message: string } {\r\n  if (!idNo || idNo.trim() === '') {\r\n    return { isValid: true, message: '' }\r\n  }\r\n\r\n  const id = idNo.trim()\r\n\r\n  // 基本格式校验：长度在6-20位之间，包含字母、数字、括号、横线\r\n  const reg = /^[A-Z0-9\\-()]{1,20}$/i\r\n  if (!reg.test(id)) {\r\n    return { isValid: false, message: '证件号码格式不正确' }\r\n  }\r\n\r\n  return { isValid: true, message: '证件号码格式正确' }\r\n}\r\n\r\n/** 根据证件类型校验证件号码 */\r\nfunction validateIdNumber(idType: string, idNo: string): { isValid: boolean; message: string } {\r\n  switch (idType) {\r\n    case IdType.IDCERT:\r\n      return validateIdCard(idNo)\r\n    case IdType.PASSPORT:\r\n      return validatePassport(idNo)\r\n    case IdType.OTHER:\r\n    default:\r\n      return validateOtherIdCard(idNo)\r\n  }\r\n}\r\n\r\n/** 实时校验证件号码输入 */\r\nfunction validateIdNumberInput(together: Together) {\r\n  if (!together.idNo || together.idNo.trim() === '') {\r\n    return // 空值不显示提示\r\n  }\r\n\r\n  const validation = validateIdNumber(together.idType || IdType.IDCERT, together.idNo)\r\n\r\n  // 使用防抖，避免频繁提示\r\n  clearTimeout((together as any)._validationTimer)\r\n  ;(together as any)._validationTimer = setTimeout(() => {\r\n    if (!validation.isValid) {\r\n      // 只在输入框失焦时显示错误，避免输入过程中频繁提示\r\n      // ElMessage.warning(validation.message)\r\n    }\r\n  }, 1000)\r\n}\r\n\r\n/** 获取证件号码校验样式 */\r\nfunction getIdNumberValidationClass(together: Together): string {\r\n  if (!together.idNo || together.idNo.trim() === '') {\r\n    return '' // 空值不显示样式\r\n  }\r\n\r\n  const validation = validateIdNumber(together.idType || IdType.IDCERT, together.idNo)\r\n\r\n  if (validation.isValid) {\r\n    return 'id-number-valid'\r\n  } else {\r\n    return 'id-number-invalid'\r\n  }\r\n}\r\n\r\n/** 处理证件类型变更 */\r\nfunction handleIdTypeChange(together: Together, bookRoom: BookRoom, batch?: Batch) {\r\n  // 重新校验证件号码\r\n  if (together.idNo && together.idNo.trim() !== '') {\r\n    validateIdNumberInput(together)\r\n  }\r\n\r\n  // 保存信息\r\n  saveGuestInfo(together, bookRoom, batch)\r\n}\r\n\r\n// 键盘导航功能\r\nconst inputRefs = ref<any>({})\r\n\r\n/** 键盘导航处理函数 */\r\nfunction handleKeyNavigation(event: KeyboardEvent, currentInputId: string) {\r\n  // 只处理方向键\r\n  if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {\r\n    return\r\n  }\r\n\r\n  const currentInput = inputRefs.value[currentInputId]\r\n  if (!currentInput || !currentInput.$el) {\r\n    return\r\n  }\r\n\r\n  const inputElement = currentInput.$el.querySelector('input') || currentInput.$el\r\n  const value = inputElement.value || ''\r\n  const selectionStart = inputElement.selectionStart || 0\r\n  const selectionEnd = inputElement.selectionEnd || 0\r\n\r\n  // 检查是否应该优先处理文字内的光标移动\r\n  const shouldNavigateBetweenInputs = () => {\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        // 只有当光标在文字开头时才跳转到其他输入框\r\n        return selectionStart === 0 && selectionEnd === 0\r\n      case 'ArrowRight':\r\n        // 只有当光标在文字末尾时才跳转到其他输入框\r\n        return selectionStart === value.length && selectionEnd === value.length\r\n      case 'ArrowUp':\r\n      case 'ArrowDown':\r\n        // 上下箭头总是跳转到其他输入框（因为单行输入框内上下移动没有意义）\r\n        return true\r\n      default:\r\n        return false\r\n    }\r\n  }\r\n\r\n  // 如果不应该在输入框间导航，让浏览器处理默认的文字光标移动\r\n  if (!shouldNavigateBetweenInputs()) {\r\n    return\r\n  }\r\n\r\n  // 阻止默认行为，执行输入框间导航\r\n  event.preventDefault()\r\n\r\n  // 解析当前输入框的位置信息\r\n  const [, batchIndex, roomIndex, togetherIndex, fieldType] = currentInputId.split('_')\r\n  const currentBatch = Number.parseInt(batchIndex)\r\n  const currentRoom = Number.parseInt(roomIndex)\r\n  const currentTogether = Number.parseInt(togetherIndex)\r\n\r\n  // 定义字段顺序：name -> phone -> idNo\r\n  const fieldOrder = ['name', 'phone', 'idNo']\r\n  const currentFieldIndex = fieldOrder.indexOf(fieldType)\r\n\r\n  let targetInputId = ''\r\n\r\n  switch (event.key) {\r\n    case 'ArrowLeft':\r\n      // 左箭头：同一行向左移动\r\n      if (currentFieldIndex > 0) {\r\n        // 移动到同一行的前一个字段\r\n        const prevField = fieldOrder[currentFieldIndex - 1]\r\n        targetInputId = generateInputId(currentBatch, currentRoom, currentTogether, prevField)\r\n      } else {\r\n        // 已经是第一个字段，移动到上一行的最后一个字段\r\n        targetInputId = findPreviousRowLastField(currentBatch, currentRoom, currentTogether)\r\n      }\r\n      break\r\n\r\n    case 'ArrowRight':\r\n      // 右箭头：同一行向右移动\r\n      if (currentFieldIndex < fieldOrder.length - 1) {\r\n        // 移动到同一行的下一个字段\r\n        const nextField = fieldOrder[currentFieldIndex + 1]\r\n        targetInputId = generateInputId(currentBatch, currentRoom, currentTogether, nextField)\r\n      } else {\r\n        // 已经是最后一个字段，移动到下一行的第一个字段\r\n        targetInputId = findNextRowFirstField(currentBatch, currentRoom, currentTogether)\r\n      }\r\n      break\r\n\r\n    case 'ArrowUp':\r\n      // 上箭头：移动到上一行的同一列\r\n      targetInputId = findSameColumnPreviousRow(currentBatch, currentRoom, currentTogether, fieldType)\r\n      break\r\n\r\n    case 'ArrowDown':\r\n      // 下箭头：移动到下一行的同一列\r\n      targetInputId = findSameColumnNextRow(currentBatch, currentRoom, currentTogether, fieldType)\r\n      break\r\n  }\r\n\r\n  // 如果找到了目标输入框，就跳转过去\r\n  if (targetInputId && inputRefs.value[targetInputId]) {\r\n    const targetInput = inputRefs.value[targetInputId]\r\n    if (targetInput && targetInput.focus) {\r\n      targetInput.focus()\r\n    }\r\n  }\r\n}\r\n\r\n/** 生成输入框ID */\r\nfunction generateInputId(batchIndex: number, roomIndex: number, togetherIndex: number, fieldType: string) {\r\n  return `input_${batchIndex}_${roomIndex}_${togetherIndex}_${fieldType}`\r\n}\r\n\r\n/** 查找上一行的最后一个字段 */\r\nfunction findPreviousRowLastField(currentBatch: number, currentRoom: number, currentTogether: number): string {\r\n  // 获取所有可用的输入框ID\r\n  const allInputIds = Object.keys(inputRefs.value).filter((id) => {\r\n    const input = inputRefs.value[id]\r\n    return input && input.$el && !input.disabled\r\n  })\r\n\r\n  // 按位置排序，找到当前位置之前的最后一个\r\n  const sortedInputs = allInputIds.sort((a, b) => {\r\n    const [, aBatch, aRoom, aTogether, aField] = a.split('_')\r\n    const [, bBatch, bRoom, bTogether, bField] = b.split('_')\r\n\r\n    const aBatchNum = Number.parseInt(aBatch)\r\n    const bBatchNum = Number.parseInt(bBatch)\r\n    const aRoomNum = Number.parseInt(aRoom)\r\n    const bRoomNum = Number.parseInt(bRoom)\r\n    const aTogetherNum = Number.parseInt(aTogether)\r\n    const bTogetherNum = Number.parseInt(bTogether)\r\n\r\n    if (aBatchNum !== bBatchNum) {\r\n      return aBatchNum - bBatchNum\r\n    }\r\n    if (aRoomNum !== bRoomNum) {\r\n      return aRoomNum - bRoomNum\r\n    }\r\n    if (aTogetherNum !== bTogetherNum) {\r\n      return aTogetherNum - bTogetherNum\r\n    }\r\n\r\n    const fieldOrder = ['name', 'phone', 'idNo']\r\n    return fieldOrder.indexOf(aField) - fieldOrder.indexOf(bField)\r\n  })\r\n\r\n  const currentId = generateInputId(currentBatch, currentRoom, currentTogether, 'name')\r\n  const currentIndex = sortedInputs.indexOf(currentId)\r\n\r\n  if (currentIndex > 0) {\r\n    return sortedInputs[currentIndex - 1]\r\n  }\r\n\r\n  // 如果没有找到，返回最后一个\r\n  return sortedInputs[sortedInputs.length - 1]\r\n}\r\n\r\n/** 查找下一行的第一个字段 */\r\nfunction findNextRowFirstField(currentBatch: number, currentRoom: number, currentTogether: number): string {\r\n  // 获取所有可用的输入框ID\r\n  const allInputIds = Object.keys(inputRefs.value).filter((id) => {\r\n    const input = inputRefs.value[id]\r\n    return input && input.$el && !input.disabled\r\n  })\r\n\r\n  // 按位置排序，找到当前位置之后的第一个\r\n  const sortedInputs = allInputIds.sort((a, b) => {\r\n    const [, aBatch, aRoom, aTogether, aField] = a.split('_')\r\n    const [, bBatch, bRoom, bTogether, bField] = b.split('_')\r\n\r\n    const aBatchNum = Number.parseInt(aBatch)\r\n    const bBatchNum = Number.parseInt(bBatch)\r\n    const aRoomNum = Number.parseInt(aRoom)\r\n    const bRoomNum = Number.parseInt(bRoom)\r\n    const aTogetherNum = Number.parseInt(aTogether)\r\n    const bTogetherNum = Number.parseInt(bTogether)\r\n\r\n    if (aBatchNum !== bBatchNum) {\r\n      return aBatchNum - bBatchNum\r\n    }\r\n    if (aRoomNum !== bRoomNum) {\r\n      return aRoomNum - bRoomNum\r\n    }\r\n    if (aTogetherNum !== bTogetherNum) {\r\n      return aTogetherNum - bTogetherNum\r\n    }\r\n\r\n    const fieldOrder = ['name', 'phone', 'idNo']\r\n    return fieldOrder.indexOf(aField) - fieldOrder.indexOf(bField)\r\n  })\r\n\r\n  const currentId = generateInputId(currentBatch, currentRoom, currentTogether, 'idNo')\r\n  const currentIndex = sortedInputs.indexOf(currentId)\r\n\r\n  if (currentIndex < sortedInputs.length - 1) {\r\n    return sortedInputs[currentIndex + 1]\r\n  }\r\n\r\n  // 如果没有找到，返回第一个\r\n  return sortedInputs[0]\r\n}\r\n\r\n/** 查找同一列的上一行 */\r\nfunction findSameColumnPreviousRow(currentBatch: number, currentRoom: number, currentTogether: number, fieldType: string): string {\r\n  // 先尝试同一房间的上一个客人\r\n  if (currentTogether > 0) {\r\n    const targetId = generateInputId(currentBatch, currentRoom, currentTogether - 1, fieldType)\r\n    if (inputRefs.value[targetId]) {\r\n      return targetId\r\n    }\r\n  }\r\n\r\n  // 再尝试上一个房间的主客\r\n  if (currentRoom > 0) {\r\n    const targetId = generateInputId(currentBatch, currentRoom - 1, 0, fieldType)\r\n    if (inputRefs.value[targetId]) {\r\n      return targetId\r\n    }\r\n  }\r\n\r\n  // 最后尝试上一个批次\r\n  if (currentBatch > 0) {\r\n    // 找到上一个批次的最后一个房间的主客\r\n    const allInputIds = Object.keys(inputRefs.value)\r\n    const prevBatchInputs = allInputIds.filter((id) => id.startsWith(`input_${currentBatch - 1}_`))\r\n    if (prevBatchInputs.length > 0) {\r\n      const targetId = generateInputId(currentBatch - 1, 0, 0, fieldType)\r\n      if (inputRefs.value[targetId]) {\r\n        return targetId\r\n      }\r\n    }\r\n  }\r\n\r\n  return ''\r\n}\r\n\r\n/** 查找同一列的下一行 */\r\nfunction findSameColumnNextRow(currentBatch: number, currentRoom: number, currentTogether: number, fieldType: string): string {\r\n  // 先尝试同一房间的下一个客人\r\n  const nextTogetherTargetId = generateInputId(currentBatch, currentRoom, currentTogether + 1, fieldType)\r\n  if (inputRefs.value[nextTogetherTargetId]) {\r\n    return nextTogetherTargetId\r\n  }\r\n\r\n  // 再尝试下一个房间的主客\r\n  const nextRoomTargetId = generateInputId(currentBatch, currentRoom + 1, 0, fieldType)\r\n  if (inputRefs.value[nextRoomTargetId]) {\r\n    return nextRoomTargetId\r\n  }\r\n\r\n  // 最后尝试下一个批次的第一个房间的主客\r\n  const nextBatchTargetId = generateInputId(currentBatch + 1, 0, 0, fieldType)\r\n  if (inputRefs.value[nextBatchTargetId]) {\r\n    return nextBatchTargetId\r\n  }\r\n\r\n  return ''\r\n}\r\n\r\n/** 保存客人信息 */\r\nfunction saveGuestInfo(together: Together, bookRoom: BookRoom, batch?: Batch) {\r\n  // 检查是否有需要保存的信息 - 更严格的验证\r\n  const isValidValue = (value: any): boolean => {\r\n    return value !== null && value !== undefined && value !== '' && String(value).trim() !== ''\r\n  }\r\n\r\n  const hasName = isValidValue(together.name)\r\n  const hasPhone = isValidValue(together.phone)\r\n  const hasIdNo = isValidValue(together.idNo)\r\n\r\n  // 如果姓名、手机号、身份证号都为空或无效值，则不调用保存接口\r\n  if (!hasName && !hasPhone && !hasIdNo) {\r\n    return\r\n  }\r\n\r\n  // 证件号码校验\r\n  if (hasIdNo) {\r\n    const validation = validateIdNumber(together.idType || IdType.IDCERT, together.idNo || '')\r\n    if (!validation.isValid) {\r\n      ElMessage.error(validation.message)\r\n      return\r\n    }\r\n  }\r\n\r\n  // 检查是否有内容变更\r\n  let togetherKey: string\r\n  if (together.togetherCode) {\r\n    togetherKey = `${bookRoom.orderNo}_${together.togetherCode}`\r\n  } else {\r\n    // 对于新创建的客人，尝试找到对应的临时key\r\n    const tempKeys = Object.keys(originalData.value).filter((key) => key.startsWith(`${bookRoom.orderNo}_new_`))\r\n    // 使用第一个匹配的临时key，或者创建一个新的\r\n    togetherKey = tempKeys.length > 0 ? tempKeys[0] : `${bookRoom.orderNo}_new_0`\r\n  }\r\n\r\n  const originalTogether = originalData.value[togetherKey]\r\n\r\n  if (originalTogether) {\r\n    // 比较当前值与原始值\r\n    const nameChanged = (together.name || '') !== (originalTogether.name || '')\r\n    const phoneChanged = (together.phone || '') !== (originalTogether.phone || '')\r\n    const idNoChanged = (together.idNo || '') !== (originalTogether.idNo || '')\r\n    const idTypeChanged = (together.idType || '') !== (originalTogether.idType || '')\r\n\r\n    // 如果没有任何变更，则不调用保存接口\r\n    if (!nameChanged && !phoneChanged && !idNoChanged && !idTypeChanged) {\r\n      return\r\n    }\r\n  }\r\n\r\n  // 如果没有传入batch，则通过orderNo查找对应的batch\r\n  let currentBatch = batch\r\n  if (!currentBatch) {\r\n    for (const b of dt.value.batches) {\r\n      const foundRoom = b.bookRooms.find((room) => room.orderNo === bookRoom.orderNo)\r\n      if (foundRoom) {\r\n        currentBatch = b\r\n        break\r\n      }\r\n    }\r\n  }\r\n\r\n  // 构建请求参数\r\n  const params: any = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    orderNo: bookRoom.orderNo,\r\n    bookNo: props.bookNo[0],\r\n    planCheckinTime: bookRoom.planCheckinTime,\r\n    planCheckoutTime: bookRoom.planCheckoutTime,\r\n    rtCode: bookRoom.rtCode,\r\n    rCode: bookRoom.rCode || '',\r\n    rNo: bookRoom.rNo || '',\r\n    name: together.name || '',\r\n    phone: together.phone || '',\r\n    idType: together.idType || IdType.IDCERT,\r\n    idNo: together.idNo || '',\r\n    isMain: together.isMain || '0',\r\n  }\r\n\r\n  // togetherCode有就传，没有就不传\r\n  if (together.togetherCode) {\r\n    params.togetherCode = together.togetherCode\r\n  }\r\n\r\n  // 调用保存接口\r\n  orderApi\r\n    .bookGuestInfoSave(params)\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success('修改成功')\r\n\r\n        // 如果返回了新的宾客代码，更新到前端数据中\r\n        if (res.data && typeof res.data === 'string' && !together.togetherCode) {\r\n          console.log('接收到新的宾客代码:', res.data)\r\n          together.togetherCode = res.data\r\n\r\n          // 查找并删除所有相关的临时key\r\n          const tempKeys = Object.keys(originalData.value).filter((key) => key.startsWith(`${bookRoom.orderNo}_new_`))\r\n          tempKeys.forEach((key) => {\r\n            delete originalData.value[key]\r\n          })\r\n\r\n          // 使用新的togetherCode创建key\r\n          const newTogetherKey = `${bookRoom.orderNo}_${res.data}`\r\n          originalData.value[newTogetherKey] = {\r\n            name: together.name || '',\r\n            phone: together.phone || '',\r\n            idNo: together.idNo || '',\r\n            idType: together.idType || '',\r\n          }\r\n\r\n          console.log('已更新宾客代码，新key:', newTogetherKey)\r\n        } else {\r\n          // 保存成功后，更新原始数据副本\r\n          let updateKey: string\r\n          if (together.togetherCode) {\r\n            updateKey = `${bookRoom.orderNo}_${together.togetherCode}`\r\n          } else {\r\n            // 对于仍然没有togetherCode的情况，使用当前的togetherKey\r\n            updateKey = togetherKey\r\n          }\r\n\r\n          originalData.value[updateKey] = {\r\n            name: together.name || '',\r\n            phone: together.phone || '',\r\n            idNo: together.idNo || '',\r\n            idType: together.idType || '',\r\n          }\r\n        }\r\n      } else {\r\n        ElMessage.error(res.msg || '保存失败')\r\n      }\r\n    })\r\n    .catch((error) => {\r\n      console.error('保存客人信息失败:', error)\r\n      ElMessage.error('保存失败')\r\n    })\r\n}\r\n\r\ninterface ParameterMaps {\r\n  bkNum: number\r\n  price: number\r\n  priceDate: string\r\n  priceStrategyCode: string\r\n  roomBkNum: number\r\n  vipPrice: number\r\n  week: number\r\n}\r\nconst roomPriceProps = ref({\r\n  visible: false,\r\n  rtCode: '',\r\n  orderNo: '',\r\n  orderType: '',\r\n  initialPriceList: [\r\n    {\r\n      bkNum: 0,\r\n      price: 0,\r\n      priceDate: '',\r\n      priceStrategyCode: '',\r\n      roomBkNum: 0,\r\n      vipPrice: 0,\r\n      week: 0,\r\n    },\r\n  ] as ParameterMaps[],\r\n})\r\n\r\n// 改价\r\nfunction checkPrice(row: any) {\r\n  roomPriceProps.value.rtCode = row.rtCode\r\n  roomPriceProps.value.orderNo = row.orderNo\r\n  roomPriceProps.value.orderType = NoType.ORDER\r\n  roomPriceProps.value.initialPriceList = row.orderPrices\r\n  roomPriceProps.value.visible = true\r\n}\r\n\r\n// 改价成功返回\r\nfunction echoList(value: ParameterMaps[], echoId: string) {\r\n  getTeamBook()\r\n}\r\n\r\n// 入住操作\r\n// 入住操作\r\nasync function checkIn(scope: any) {\r\n  if (!scope.row.rNo) {\r\n    ElMessage.warning(t('messages.checkinBeforeArrangeRoom'))\r\n    return\r\n  }\r\n\r\n  // 获取当前服务器时间\r\n  const sysTimeString = await getSysTime()\r\n  if (!sysTimeString) {\r\n    ElMessage.error(t('messages.getSystemTimeFailed'))\r\n    return\r\n  }\r\n\r\n  // 将服务器时间转换为dayjs对象\r\n  const currentTime = dayjs(Number(sysTimeString))\r\n  // 获取批次的预抵和预离时间\r\n  const planCheckinTime = dayjs(scope.row.planCheckinTime).startOf('day')\r\n  const planCheckoutTime = dayjs(scope.row.planCheckoutTime).endOf('day')\r\n  // 判断当前时间是否在批次时间范围内\r\n  if (currentTime.isBefore(planCheckinTime) || currentTime.isAfter(planCheckoutTime)) {\r\n    ElMessage.warning(t('messages.currentTimeNotInBatchRange'))\r\n    return\r\n  }\r\n\r\n  // 检查入住人姓名\r\n  const missingNameGuests = scope.row.togethers.filter((together) => !together.name)\r\n  if (missingNameGuests.length > 0) {\r\n    ElMessage.warning(t('messages.guestNameMissingInRoom', { roomNo: scope.row.rNo, count: missingNameGuests.length }))\r\n    return\r\n  }\r\n\r\n  // 检查证件类型为身份证时是否输入了身份证号码\r\n  const missingIdNoGuests = scope.row.togethers.filter((together) => together.idType === IdType.IDCERT && !together.idNo)\r\n\r\n  if (missingIdNoGuests.length > 0) {\r\n    if (frontConfig.value.needId === '1') {\r\n      ElMessage.warning(t('messages.idNumberMissingInRoom', { roomNo: scope.row.rNo, count: missingIdNoGuests.length }))\r\n      return\r\n    } else {\r\n      // 加个提示，只提示一次\r\n      try {\r\n        await ElMessageBox.confirm(t('messages.idNumberMissingConfirm', { roomNo: scope.row.rNo, count: missingIdNoGuests.length }), t('messages.tip'), {\r\n          confirmButtonText: t('messages.continue'),\r\n          cancelButtonText: t('messages.cancel'),\r\n          type: 'warning',\r\n        })\r\n      } catch (error) {\r\n        // 用户取消操作\r\n        return\r\n      }\r\n    }\r\n  }\r\n\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    bookNo: props.bookNo[0],\r\n    batchNo: `${dayjs(scope.row.planCheckinTime).format('YYYY-MM-DD')}/${dayjs(scope.row.planCheckoutTime).format('YYYY-MM-DD')}`,\r\n    isSendSms: BooleanEnum.NO,\r\n    orders: [\r\n      {\r\n        orderNo: scope.row.orderNo,\r\n        bkNum: scope.row.orderPrices[0].bkNum,\r\n        persons: scope.row.togethers.map((together: Together) => {\r\n          const person: any = {\r\n            name: together.name,\r\n            sex: together.sex || SexEnum.SECRECY.toString(), // 默认为未知性别\r\n            nation: together.nation || '',\r\n            address: together.address || '',\r\n            phone: together.phone || '',\r\n            idType: together.idType || IdType.IDCERT,\r\n            idNo: together.idNo || '',\r\n            isMadeCard: together.isMadeCard || '0',\r\n          }\r\n          // togetherCode有就传，没有就不传\r\n          if (together.togetherCode) {\r\n            person.togetherCode = together.togetherCode\r\n          }\r\n          return person\r\n        }),\r\n      },\r\n    ],\r\n  }\r\n  orderApi.bookCheckIn(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success(t('messages.checkinSuccess'))\r\n      getTeamBook()\r\n      emits('refresh')\r\n    }\r\n  })\r\n}\r\n\r\n// 添加同住人\r\nfunction addGuest(scope: any) {\r\n  // 循环dt,根据scope.row.orderNo找到对应的bookRoom，然后在该bookRoom中的togethers添加一个together对象\r\n  for (let i = 0; i < dt.value.batches.length; i++) {\r\n    const batch = dt.value.batches[i]\r\n    for (let j = 0; j < batch.bookRooms.length; j++) {\r\n      const bookRoom = batch.bookRooms[j]\r\n      if (bookRoom.orderNo === scope.row.orderNo) {\r\n        // 在该bookRoom中的togethers添加一个together对象\r\n        const newTogether = {\r\n          togetherCode: '', // 新添加的同住人不设置togetherCode，让后端生成\r\n          rCode: '',\r\n          rNo: '',\r\n          sex: SexEnum.SECRECY.toString(),\r\n          nation: '',\r\n          address: '',\r\n          name: '',\r\n          phone: '',\r\n          idType: IdType.IDCERT,\r\n          idNo: '',\r\n          isMadeCard: '0',\r\n          isMain: '0',\r\n          state: '',\r\n        }\r\n        bookRoom.togethers.push(newTogether)\r\n\r\n        // 为新添加的客人初始化原始数据副本\r\n        const togetherIndex = bookRoom.togethers.length - 1 // 新添加的客人的索引\r\n        const togetherKey = `${bookRoom.orderNo}_new_${togetherIndex}`\r\n        originalData.value[togetherKey] = {\r\n          name: '',\r\n          phone: '',\r\n          idNo: '',\r\n          idType: IdType.IDCERT,\r\n        }\r\n\r\n        // 确保当前行被展开\r\n        if (!expandedRows.value.includes(bookRoom.orderNo)) {\r\n          expandedRows.value.push(bookRoom.orderNo)\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 移除单个房间\r\nfunction removeRoom(row: any) {\r\n  // 统计所有房间的数量\r\n  let totalRooms = 0\r\n  dt.value.batches.forEach((batch: Batch) => {\r\n    totalRooms += batch.bookRooms.length\r\n  })\r\n\r\n  // 如果房间总数为1，则不允许移除该房间\r\n  if (totalRooms === 1) {\r\n    ElMessage.warning(t('messages.atLeastOneRoomRequired'))\r\n    return\r\n  }\r\n  bookApi\r\n    .deleteBookRoom({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      orderNo: row.orderNo,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('messages.removeRoomSuccess'))\r\n        getTeamBook()\r\n        emits('refresh')\r\n      }\r\n    })\r\n}\r\n\r\n/** 会员类型 */\r\nconst members = ref<\r\n  {\r\n    mcode: string\r\n    name: string\r\n    phone: string\r\n    idNo: string\r\n    level: string\r\n    storeCardBalance: string\r\n    usedPoint: string\r\n  }[]\r\n>([])\r\n\r\n/** 获取未脱敏会员详情 */\r\nasync function getInfo(query: string) {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    phone: query.length > 11 ? '' : query,\r\n    idNo: query.length > 11 ? query : '',\r\n    state: BooleanEnum.YES,\r\n  }\r\n  // TODO:没有实现脱敏，用的是未脱敏数据\r\n  const res = await memberApi.getMemberNoPage(params)\r\n  if (res && (res.data as any)) {\r\n    return res.data.list\r\n  }\r\n}\r\n\r\n/** 远程查询会员信息 */\r\nfunction remoteQueryMember(query: string) {\r\n  // 拦截判断一下身份证或者手机号格式是否正确？\r\n  const fage = validateTipPhone(query)\r\n  if (!fage) {\r\n    members.value = []\r\n    data.value.loading = true\r\n    setTimeout(async () => {\r\n      data.value.loading = false\r\n      const res = await getInfo(query)\r\n      if (res) {\r\n        members.value = res\r\n      }\r\n    }, 200)\r\n  } else {\r\n    // ElMessage.warning(\"身份证或手机号格式错误\");\r\n    // members.value = [];\r\n  }\r\n}\r\n\r\n/** 会员选中 */\r\nfunction vipChange(value) {\r\n  const data = members.value.filter((item: any) => item.mcode === value)[0]\r\n  dt.value.contact = data.name\r\n  dt.value.phone = data.phone\r\n}\r\n\r\ninterface vipDAtatype {\r\n  mcode: string\r\n  name: string\r\n  phone: string\r\n  idNo: string\r\n  mtCode?: string\r\n}\r\nconst vipData = ref<vipDAtatype | undefined>()\r\n\r\n/** 客源点击切换 */\r\nfunction onChange(value: any) {\r\n  if (value.target._value === 'agent') {\r\n    getAgents(1)\r\n  }\r\n  if (value.target._value === 'protocol') {\r\n    getAgents(0)\r\n  }\r\n  vipData.value = undefined\r\n  members.value = []\r\n  dt.value.guestName = ''\r\n  dt.value.guestCode = ''\r\n}\r\n\r\n// 计算属性\r\nconst guestSrcTypeName = computed(() => {\r\n  const guestSrcTypeItem = guestSrcTypes.value.find((item) => item.code === dt.value.guestSrcType)\r\n  return guestSrcTypeItem ? guestSrcTypeItem.label : ''\r\n})\r\n/** 返回选中的房间 */\r\nasync function selectRooms(rooms: any) {\r\n  // 预低预离时间\r\n  const planCheckinTime = dayjs(rooms.planCheckinTime).format('YYYY-MM-DD HH:mm')\r\n  const planCheckoutTime = dayjs(rooms.planCheckoutTime).format('YYYY-MM-DD HH:mm')\r\n\r\n  // 过滤出 roomNum > 0 的房间类型\r\n  const filteredDataList = rooms.dataList.filter((item) => item.roomNum > 0)\r\n  // 构建 roomTypes 参数\r\n  let bookRooms = []\r\n  const roomTypes = filteredDataList.map((item: any) => {\r\n    if (item.bookRooms && item.bookRooms.length > 0) {\r\n      bookRooms = item.bookRooms.map((bookRoom: any) => ({\r\n        rCode: bookRoom.rCode,\r\n        rNo: bookRoom.rNo,\r\n        mac: bookRoom.mac,\r\n        lockVersion: bookRoom.lockVersion,\r\n      }))\r\n    }\r\n\r\n    const dayPrices = item.dayPrices.map((dayPrice: any) => ({\r\n      priceDate: dayjs(dayPrice.priceDate).format('YYYY-MM-DD'),\r\n      week: dayPrice.week,\r\n      bkNum: dayPrice.bkNum,\r\n      roomBkNum: dayPrice.roomBkNum,\r\n      price: dayPrice.price,\r\n      vipPrice: dayPrice.vipPrice,\r\n      priceType: dayPrice.priceType,\r\n      priceStrategyCode: dayPrice.priceStrategyCode,\r\n    }))\r\n\r\n    return {\r\n      rtCode: item.rtCode,\r\n      roomNum: item.roomNum,\r\n      isMeetingRoom: item.isMeetingRoom || '0', // 默认为客房\r\n      bookRooms,\r\n      dayPrices,\r\n    }\r\n  })\r\n\r\n  // 构建请求参数\r\n  const requestParams = {\r\n    gcode: queryParams.gcode,\r\n    hcode: queryParams.hcode,\r\n    channelCode: dt.value.channelCode,\r\n    bookNo: dt.value.bookNo,\r\n    days: rooms.days,\r\n    planCheckinTime,\r\n    planCheckoutTime,\r\n    batchNo: `${rooms.planCheckinTime.slice(0, 10)}/${rooms.planCheckoutTime.slice(0, 10)}`,\r\n    roomTypes,\r\n  }\r\n\r\n  // 调用后端接口添加房间\r\n  await bookApi.addTeamRoom(requestParams).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success(t('messages.addRoomSuccess'))\r\n      getTeamBook()\r\n      emits('refresh')\r\n    }\r\n  })\r\n}\r\n\r\nconst arrangeRoomsProps = ref({\r\n  visible: false,\r\n  /** 预订单号 */\r\n  bookNo: '',\r\n  /** 批次号 */\r\n  batchNo: '',\r\n  /** 订单号 */\r\n  orderNo: '',\r\n  /** 房间类型 */\r\n  rtCode: '',\r\n  /** 房间类型名称 */\r\n  rtName: '',\r\n  /** 房间状态 */\r\n  rtState: '',\r\n  /** 房间号列表 */\r\n  rNos: [] as string[],\r\n  /** 预抵时间 */\r\n  planCheckinTime: '',\r\n  /** 预离时间 */\r\n  planCheckoutTime: '',\r\n  roomNum: 0,\r\n})\r\n/** 排房 */\r\nfunction arrangeRoom(row: any) {\r\n  isAlone.value = true\r\n  arrangeRoomsProps.value.rNos = []\r\n  arrangeRoomsProps.value.rtCode = row.rtCode\r\n  arrangeRoomsProps.value.rtName = row.rtName\r\n  arrangeRoomsProps.value.orderNo = row.orderNo\r\n  arrangeRoomsProps.value.batchNo = `${dayjs(row.planCheckinTime).format('YYYY-MM-DD')}/${dayjs(row.planCheckoutTime).format('YYYY-MM-DD')}`\r\n  arrangeRoomsProps.value.roomNum = 1\r\n  arrangeRoomsProps.value.planCheckinTime = dayjs(row.planCheckinTime).format('YYYY-MM-DD HH:mm')\r\n  arrangeRoomsProps.value.planCheckoutTime = dayjs(row.planCheckoutTime).format('YYYY-MM-DD HH:mm')\r\n  if (row.rNo) {\r\n    arrangeRoomsProps.value.rNos.push(row.rNo)\r\n  }\r\n  arrangeRoomsProps.value.visible = true\r\n}\r\n\r\n/** 排房返回数据 */\r\nfunction arrangeRoomConfirm(data: any) {\r\n  getTeamBook()\r\n  emits('refresh')\r\n}\r\n\r\n// 通过证件类型代码转换为证件类型名称\r\nfunction convertIdType(idType: string): string {\r\n  const item = idTypes.value.find((item: any) => item.code === idType)\r\n  return item ? item.label : ''\r\n}\r\n\r\n/** 查看详单 */\r\nfunction viewDetailed(row: any) {\r\n  // 获取房间中的主客代码\r\n  const together = row.togethers.find((item: any) => item.isMain === BooleanEnum.YES)\r\n  emits('seeDetailed', {\r\n    orderNo: row.orderNo,\r\n    state: row.state,\r\n    togetherCode: together.togetherCode,\r\n  })\r\n}\r\n\r\n/** 户型升级 */\r\nfunction roomUpgradeClick(row: any) {\r\n  // 如果当前订单已经有排房，则不允许升级，需要先取消排房\r\n  if (row.rNo) {\r\n    // roomUpgradePopoverRef.value.popperRef?.delayHide?.()\r\n    ElMessage.warning(t('messages.currentOrderArrangedCannotUpgrade'))\r\n    return\r\n  }\r\n  const specificTime = dayjs(row.planCheckoutTime) // 包含时间的字符串格式\r\n  const currentTime = dayjs(serverTime.value)\r\n  if (specificTime.isBefore(currentTime)) {\r\n    return ElMessage.warning(t('messages.orderPastCheckoutTime'))\r\n  } else {\r\n    easyDialogRef.value.show()\r\n    const dedupArray = deduplicateByProperty([row], 'rtCode').filter((item) => !item.rNo) || []\r\n    roomOptions.value = removePropertyFromArray(dedupArray, 'orderPrices')\r\n    modelForm.value.rtCode = row.rtCode\r\n    modelForm.value.orderNo = row.orderNo\r\n    getRoomtype()\r\n  }\r\n}\r\n\r\n// 批量升级房型\r\nfunction batchUpdateRoomTypes(batch: any) {\r\n  //\r\n}\r\n\r\n/** 房型升级提交表单 */\r\nfunction formSubmit() {\r\n  if (!easyFormRef.value.formRef) {\r\n    return\r\n  }\r\n  easyFormRef.value.formRef.validate(async (valid: boolean) => {\r\n    if (valid) {\r\n      const res = await bookApi.putUpgrade(modelForm.value)\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('messages.roomUpgradeSuccess'))\r\n        getTeamBook()\r\n        formClose()\r\n      }\r\n    }\r\n  })\r\n}\r\n/** 取消弹窗 */\r\nfunction formClose() {\r\n  // 清空校验\r\n  if (easyFormRef.value) {\r\n    easyFormRef.value.formRef.resetFields()\r\n  }\r\n  // 赋值给弹窗的值\r\n  for (const key in modelForm.value) {\r\n    modelForm.value[key] = _modelForm[key]\r\n  }\r\n  easyDialogRef.value.loading = false\r\n  easyDialogRef.value.visible = false\r\n}\r\n\r\n/** 升级房型 */\r\nconst updateRoomOptions = ref<updateRoomTypes[]>([])\r\n/** 加载当前房型数据 */\r\nfunction getRoomtype() {\r\n  const params = { ...modelForm.value }\r\n  delete params.upgradeRtCode\r\n  bookApi.getRoomtype(params).then(({ data }) => {\r\n    updateRoomOptions.value = data\r\n    modelForm.value.upgradeRtCode = ''\r\n  })\r\n}\r\n/** form表单 */\r\nconst ruleFieldList = reactive<Form.FieldItem[]>([\r\n  {\r\n    label: t('roomUpgrade'),\r\n    type: 'radio',\r\n    field: 'upgradeMethod',\r\n    options: {\r\n      data: [\r\n        { value: 0, label: t('freeUpgrade') },\r\n        // { value: 1, label: '补差价' },\r\n      ],\r\n      radioOptions: {\r\n        'onUpdate:modelValue': (newValue) => {\r\n          getRoomtype()\r\n        },\r\n      },\r\n    },\r\n  },\r\n  {\r\n    label: t('currentRoomType'),\r\n    field: 'rtCode',\r\n    type: 'select',\r\n    rules: [{ required: true, message: t('validation.currentRoomTypeRequired') }],\r\n    options: {\r\n      data: roomOptions,\r\n      valueKey: 'rtCode',\r\n      labelKey: 'rtName',\r\n      selectOptions: {\r\n        'onUpdate:modelValue': (newValue) => {\r\n          getRoomtype()\r\n        },\r\n      },\r\n    },\r\n  },\r\n  {\r\n    label: t('upgradeRoomType'),\r\n    field: 'upgradeRtCode',\r\n    slot: 'upgradeRtCode',\r\n  },\r\n])\r\n\r\n/** 处理行展开/折叠事件 */\r\nfunction handleExpandChange(row: any, expanded: any) {\r\n  if (expanded) {\r\n    // 如果展开，将该行的orderNo添加到expandedRows中\r\n    if (!expandedRows.value.includes(row.orderNo)) {\r\n      expandedRows.value.push(row.orderNo)\r\n    }\r\n  } else {\r\n    // 如果折叠，从expandedRows中移除该行的orderNo\r\n    // 但如果住客超过1人，不允许折叠\r\n    if (row.togethers && row.togethers.length > 1) {\r\n      // 保持展开状态\r\n      if (!expandedRows.value.includes(row.orderNo)) {\r\n        expandedRows.value.push(row.orderNo)\r\n      }\r\n      // 可以添加一个提示\r\n      ElMessage.info(t('messages.multipleGuestsCannotCollapse'))\r\n    } else {\r\n      const index = expandedRows.value.indexOf(row.orderNo)\r\n      if (index !== -1) {\r\n        expandedRows.value.splice(index, 1)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-card shadow=\"never\">\r\n      <template #header>\r\n        <div class=\"card-header\" style=\"display: flex; justify-content: space-between; align-items: center\">\r\n          <span>{{ t('checkinInfo') }}（{{ t('bookingNo') }}:{{ dt.bookNo }}）</span>\r\n          <el-button v-if=\"!editTeamInfo\" type=\"primary\" @click=\"editTeamInfoFun\">\r\n            {{ t('modifyOrder') }}\r\n          </el-button>\r\n          <div v-if=\"editTeamInfo\">\r\n            <el-button type=\"info\" @click=\"editTeamInfo = false\">\r\n              {{ t('cancel') }}\r\n            </el-button>\r\n            <el-button type=\"primary\" @click=\"save\">\r\n              {{ t('saveChanges') }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <el-form v-if=\"!editTeamInfo\" label-width=\"180px\" label-position=\"right\" label-suffix=\"：\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('teamName')\">\r\n              {{ dt.teamName }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('checkinType')\">\r\n              {{ dt.checkinTypeName }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('contractNo')\">\r\n              {{ dt.contractNo }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('salesperson')\">\r\n              {{ dt.seller }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('guestSourceType')\">\r\n              {{ guestSrcTypeName }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('guaranteeMethod')\">\r\n              {{ dt.guarantyStyleName }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('contact')\">\r\n              {{ dt.contact }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('remark')\">\r\n              {{ dt.remark }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-form v-if=\"editTeamInfo\" ref=\"formRef\" :model=\"dt\" :rules=\"formRules\" label-width=\"180px\" label-position=\"right\" label-suffix=\"：\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('teamName')\" prop=\"teamName\">\r\n              <ElInput v-model=\"dt.teamName\" maxlength=\"30\" style=\"width: 200px\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('checkinType')\">\r\n              <el-radio-group v-model=\"dt.checkinType\">\r\n                <el-radio :value=\"CheckinType.TRAVEL_GROUP\">\r\n                  {{ t('travelGroup') }}\r\n                </el-radio>\r\n                <el-radio :value=\"CheckinType.MEETING_GROUP\">\r\n                  {{ t('meetingGroup') }}\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('guestSourceType')\">\r\n              <el-radio-group v-model=\"dt.guestSrcType\">\r\n                <el-radio-button v-for=\"item in guestSrcTypes\" :key=\"item.code\" :value=\"item.code\" :label=\"item.label\" @change=\"onChange\" />\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item v-if=\"dt.guestSrcType === GuestSrcType.MEMBER\" prop=\"guestCode\" :label=\"t('member')\">\r\n              <el-select v-model=\"dt.guestCode\" filterable remote reserve-keyword :placeholder=\"t('phone')\" :remote-method=\"remoteQueryMember\" :loading=\"data.loading\" style=\"width: 200px\" @change=\"vipChange\">\r\n                <el-option v-for=\"item in members\" :key=\"item.mcode\" :label=\"item.name\" :value=\"item.mcode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item v-if=\"dt.guestSrcType === GuestSrcType.AGENT\" :label=\"t('agent')\" prop=\"guestCode\">\r\n              <el-select v-model=\"dt.guestCode\" :placeholder=\"t('selectAgent')\" filterable style=\"width: 200px\">\r\n                <el-option v-for=\"item in agents\" :key=\"item.paCode\" :label=\"item.paName\" :value=\"item.paCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item v-if=\"dt.guestSrcType === GuestSrcType.PROTOCOL\" :label=\"t('protocolUnit')\" prop=\"guestCode\">\r\n              <el-select v-model=\"dt.guestCode\" filterable style=\"width: 200px\" :placeholder=\"t('selectProtocolUnit')\">\r\n                <el-option v-for=\"item in agents\" :key=\"item.paCode\" :label=\"item.paName\" :value=\"item.paCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('contact')\">\r\n              <ElInput v-model=\"dt.contact\" maxlength=\"30\" style=\"width: 200px\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('contactPhone')\">\r\n              <ElInput v-model=\"dt.phone\" maxlength=\"30\" style=\"width: 200px\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('salesperson')\">\r\n              <el-select v-model=\"dt.seller\" :placeholder=\"t('selectSalesperson')\" style=\"width: 200px\">\r\n                <el-option v-for=\"item in sellers\" :key=\"item.username\" :label=\"item.nickname\" :value=\"item.username\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item :label=\"t('contractNo')\">\r\n              <ElInput v-model=\"dt.contractNo\" maxlength=\"30\" style=\"width: 200px\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('remark')\">\r\n              <ElInput v-model=\"dt.remark\" type=\"textarea\" :rows=\"3\" maxlength=\"250\" style=\"width: 723px\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n    </el-card>\r\n    <br />\r\n    <!-- 房间/住客信息模块 -->\r\n    <el-card shadow=\"never\">\r\n      <template #header>\r\n        <div class=\"card-header\" style=\"display: flex; justify-content: space-between; align-items: center\">\r\n          <span>{{ t('roomGuestInfo') }}</span>\r\n          <el-button type=\"primary\" @click=\"handleAddRoom\">\r\n            {{ t('addRoom') }}\r\n          </el-button>\r\n        </div>\r\n      </template>\r\n      <div v-for=\"(batch, batchIndex) in dt.batches\" :key=\"batchIndex\">\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px\">\r\n          <div>\r\n            <el-tag type=\"primary\" size=\"small\"> {{ t('batch') }}{{ index + 1 }} </el-tag>\r\n            <span style=\"margin-left: 10px; margin-right: 10px\">{{ dayjs(batch.planCheckinTime).format('YYYY-MM-DD') }}/{{ dayjs(batch.planCheckoutTime).format('YYYY-MM-DD') }}</span>\r\n            <el-popconfirm width=\"200\" :confirm-button-text=\"t('confirm')\" :cancel-button-text=\"t('cancel')\" :title=\"t('confirmDeleteBatch')\" @confirm=\"removeBatch(batch)\">\r\n              <template #reference>\r\n                <el-button type=\"danger\" size=\"small\">\r\n                  {{ t('delete') }}\r\n                </el-button>\r\n              </template>\r\n            </el-popconfirm>\r\n          </div>\r\n          <div>\r\n            <el-button type=\"warning\" @click=\"batchAutoArrangeRooms(batch)\">\r\n              {{ t('autoArrangeRooms') }}\r\n            </el-button>\r\n            <!--            <el-button type=\"info\" @click=\"batchWriteLockCard(batch)\"> -->\r\n            <!--              批量制卡 -->\r\n            <!--            </el-button> -->\r\n            <!--            <el-button type=\"info\" @click=\"batchChangePrice(batch)\"> -->\r\n            <!--              批量改价 -->\r\n            <!--            </el-button> -->\r\n            <el-button type=\"primary\" @click=\"batchCheckIn(batch)\">\r\n              {{ t('batchCheckin') }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        <div class=\"table-wrapper\">\r\n          <el-table :data=\"batch.bookRooms\" :expand-row-keys=\"expandedRows\" row-key=\"orderNo\" style=\"margin-bottom: 10px\" @expand-change=\"handleExpandChange\">\r\n            <el-table-column width=\"48px\" type=\"expand\">\r\n              <template #default=\"expandProps\">\r\n                <el-table v-if=\"expandProps.row.togethers && expandProps.row.togethers.length > 1\" :data=\"expandProps.row.togethers.slice(1)\" :show-header=\"false\">\r\n                  <el-table-column width=\"48px\" />\r\n                  <el-table-column min-width=\"120px\" />\r\n                  <el-table-column min-width=\"250px\" />\r\n                  <el-table-column align=\"left\" min-width=\"240px\">\r\n                    <template #default=\"scope\">\r\n                      <template v-if=\"expandProps.row.state === OrderState.IN_BOOKING\">\r\n                        <div style=\"display: flex; align-items: center\">\r\n                          <ElInput\r\n                            :ref=\"(el) => (inputRefs[generateInputId(batchIndex, expandProps.$index, scope.$index + 1, 'name')] = el)\"\r\n                            v-model=\"scope.row.name\"\r\n                            style=\"width: 100px; margin-right: 8px\"\r\n                            :placeholder=\"t('name')\"\r\n                            @blur=\"saveGuestInfo(scope.row, expandProps.row, batch)\"\r\n                            @keydown=\"handleKeyNavigation($event, generateInputId(batchIndex, expandProps.$index, scope.$index + 1, 'name'))\"\r\n                          />\r\n                          <ElInput\r\n                            :ref=\"(el) => (inputRefs[generateInputId(batchIndex, expandProps.$index, scope.$index + 1, 'phone')] = el)\"\r\n                            v-model=\"scope.row.phone\"\r\n                            style=\"width: 120px\"\r\n                            :placeholder=\"t('phone')\"\r\n                            @blur=\"saveGuestInfo(scope.row, expandProps.row, batch)\"\r\n                            @keydown=\"handleKeyNavigation($event, generateInputId(batchIndex, expandProps.$index, scope.$index + 1, 'phone'))\"\r\n                          />\r\n                        </div>\r\n                      </template>\r\n                      <template v-else>\r\n                        {{ scope.row.name }}/{{ scope.row.phone }}\r\n                        <div class=\"w-[60px] text-right\" />\r\n                      </template>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"left\" min-width=\"440px\">\r\n                    <template #default=\"scope\">\r\n                      <template v-if=\"expandProps.row.state === OrderState.IN_BOOKING\">\r\n                        <div style=\"display: flex; align-items: center\">\r\n                          <el-select v-model=\"scope.row.idType\" style=\"width: 120px; margin-right: 8px\" @change=\"handleIdTypeChange(scope.row, expandProps.row, batch)\">\r\n                            <el-option v-for=\"item in idTypes\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n                          </el-select>\r\n                          <ElInput\r\n                            :ref=\"(el) => (inputRefs[generateInputId(batchIndex, expandProps.$index, scope.$index + 1, 'idNo')] = el)\"\r\n                            v-model=\"scope.row.idNo\"\r\n                            :class=\"getIdNumberValidationClass(scope.row)\"\r\n                            style=\"width: 200px; margin-right: 8px\"\r\n                            :placeholder=\"t('idNumber')\"\r\n                            @blur=\"saveGuestInfo(scope.row, expandProps.row, batch)\"\r\n                            @keydown=\"handleKeyNavigation($event, generateInputId(batchIndex, expandProps.$index, scope.$index + 1, 'idNo'))\"\r\n                            @input=\"validateIdNumberInput(scope.row)\"\r\n                          />\r\n                          <el-button v-if=\"scope.row.idType === IdType.IDCERT\" type=\"primary\" :icon=\"CreditCard\" plain @click=\"readIdNo(scope, props)\">\r\n                            {{ t('readIdCard') }}\r\n                          </el-button>\r\n                        </div>\r\n                      </template>\r\n                      <template v-else>\r\n                        <span>{{ convertIdType(scope.row.idType) }} / {{ scope.row.idNo }}</span>\r\n                      </template>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column min-width=\"150px\" />\r\n                  <el-table-column align=\"center\" min-width=\"240px\">\r\n                    <template #default=\"scope\">\r\n                      <template v-if=\"expandProps.row.state === OrderState.IN_BOOKING\">\r\n                        <el-button type=\"primary\" link @click=\"writeLockCard(expandProps.row, false)\">\r\n                          {{ t('makeSameRoomCard') }}\r\n                        </el-button>\r\n                        <el-button v-if=\"expandProps.row.togethers && expandProps.row.togethers.length > 1\" type=\"danger\" link @click=\"removeGuest(scope.row)\">\r\n                          {{ t('removeGuest') }}\r\n                        </el-button>\r\n                      </template>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column v-for=\"column in columns\" :key=\"column.prop\" :label=\"column.label\" :prop=\"column.prop\" :min-width=\"column.width\" :align=\"column.align\">\r\n              <template #default=\"scope\">\r\n                <template v-if=\"column.prop === 'room'\">\r\n                  <template v-if=\"scope.row.rNo\">\r\n                    <el-link v-if=\"scope.row.state === OrderState.IN_BOOKING\" type=\"primary\" :underline=\"false\" @click=\"arrangeRoom(scope.row)\">\r\n                      {{ scope.row.rNo }}\r\n                    </el-link>\r\n                    <template v-else>\r\n                      {{ scope.row.rNo }}\r\n                    </template>\r\n                  </template>\r\n                  <template v-else>\r\n                    <el-link v-if=\"scope.row.state === OrderState.IN_BOOKING\" type=\"primary\" :underline=\"false\" @click=\"arrangeRoom(scope.row)\">\r\n                      {{ t('arrangeRoom') }}\r\n                    </el-link>\r\n                  </template>\r\n                  <el-tag type=\"warning\" style=\"margin-right: 5px; margin-left: 5px\">\r\n                    {{ scope.row.rtName }}\r\n                  </el-tag>\r\n                  <el-popover v-if=\"scope.row.state === OrderState.IN_BOOKING\" placement=\"right\" :width=\"200\" trigger=\"hover\">\r\n                    <template #reference>\r\n                      <el-link type=\"primary\" :underline=\"false\" @click=\"roomUpgradeClick(scope.row)\">\r\n                        {{ t('upgrade') }}\r\n                      </el-link>\r\n                    </template>\r\n                    <div>\r\n                      <p>{{ t('clickToUpgradeRoomType') }}</p>\r\n                      <p v-if=\"scope.row.rNo\" style=\"color: #f56c6c\">\r\n                        {{ t('roomAlreadyArranged') }}\r\n                      </p>\r\n                      <p v-else>\r\n                        {{ t('canUpgradeToHigherRoom') }}\r\n                      </p>\r\n                    </div>\r\n                  </el-popover>\r\n                </template>\r\n                <template v-if=\"column.prop === 'price'\">\r\n                  <div style=\"display: flex; align-items: center\">\r\n                    <span style=\"margin-right: 5px\">￥{{ scope.row.orderPrices[0].vipPrice }}</span>\r\n                    <el-link v-if=\"scope.row.state === OrderState.IN_BOOKING\" type=\"primary\" :underline=\"false\" @click=\"checkPrice(scope.row)\">\r\n                      {{ t('changePrice') }}\r\n                    </el-link>\r\n                  </div>\r\n                </template>\r\n                <!-- 主表格中的姓名/电话列 -->\r\n                <template v-if=\"column.prop === 'namePhone'\">\r\n                  <div class=\"flex-around\">\r\n                    <div class=\"w-[240px]\" style=\"display: flex; align-items: center\">\r\n                      <template v-if=\"scope.row.state === OrderState.IN_BOOKING\">\r\n                        <ElInput\r\n                          :ref=\"(el) => (inputRefs[generateInputId(batchIndex, scope.$index, 0, 'name')] = el)\"\r\n                          v-model=\"scope.row.togethers[0].name\"\r\n                          style=\"width: 100px; margin-right: 8px\"\r\n                          :placeholder=\"t('name')\"\r\n                          @blur=\"saveGuestInfo(scope.row.togethers[0], scope.row, batch)\"\r\n                          @keydown=\"handleKeyNavigation($event, generateInputId(batchIndex, scope.$index, 0, 'name'))\"\r\n                        />\r\n                        <ElInput\r\n                          :ref=\"(el) => (inputRefs[generateInputId(batchIndex, scope.$index, 0, 'phone')] = el)\"\r\n                          v-model=\"scope.row.togethers[0].phone\"\r\n                          style=\"width: 120px\"\r\n                          :placeholder=\"t('phone')\"\r\n                          @blur=\"saveGuestInfo(scope.row.togethers[0], scope.row, batch)\"\r\n                          @keydown=\"handleKeyNavigation($event, generateInputId(batchIndex, scope.$index, 0, 'phone'))\"\r\n                        />\r\n                      </template>\r\n                      <template v-else>\r\n                        {{ scope.row.togethers[0].name }} /\r\n                        {{ scope.row.togethers[0].phone }}\r\n                      </template>\r\n                    </div>\r\n                    <div class=\"w-[60px] text-right\" />\r\n                  </div>\r\n                </template>\r\n                <!-- 证件类型/证件号列 -->\r\n                <template v-if=\"column.prop === 'CardNumber'\">\r\n                  <template v-if=\"scope.row.state === OrderState.IN_BOOKING\">\r\n                    <div style=\"display: flex; align-items: center\">\r\n                      <el-select v-model=\"scope.row.togethers[0].idType\" style=\"width: 120px; margin-right: 8px\" @change=\"handleIdTypeChange(scope.row.togethers[0], scope.row, batch)\">\r\n                        <el-option v-for=\"idType in idTypes\" :key=\"idType.code\" :label=\"idType.label\" :value=\"idType.code\" />\r\n                      </el-select>\r\n                      <ElInput\r\n                        :ref=\"(el) => (inputRefs[generateInputId(batchIndex, scope.$index, 0, 'idNo')] = el)\"\r\n                        v-model=\"scope.row.togethers[0].idNo\"\r\n                        :class=\"getIdNumberValidationClass(scope.row.togethers[0])\"\r\n                        style=\"width: 200px; margin-right: 8px\"\r\n                        :placeholder=\"t('idNumber')\"\r\n                        @blur=\"saveGuestInfo(scope.row.togethers[0], scope.row, batch)\"\r\n                        @keydown=\"handleKeyNavigation($event, generateInputId(batchIndex, scope.$index, 0, 'idNo'))\"\r\n                        @input=\"validateIdNumberInput(scope.row.togethers[0])\"\r\n                      />\r\n                      <el-button v-if=\"scope.row.togethers[0].idType === IdType.IDCERT\" type=\"primary\" :icon=\"CreditCard\" plain @click=\"readIdNo(scope)\">\r\n                        {{ t('readIdCard') }}\r\n                      </el-button>\r\n                    </div>\r\n                  </template>\r\n                  <template v-else>\r\n                    <span v-if=\"scope.row.togethers && scope.row.togethers.length > 0\">\r\n                      <template v-if=\"scope.row.togethers[0].idNo === ''\"> {{ t('noIdInfo') }} </template>\r\n                      <template v-else>\r\n                        <span>{{ convertIdType(scope.row.togethers[0].idType) }} / {{ scope.row.togethers[0].idNo }}</span>\r\n                      </template>\r\n                    </span>\r\n                  </template>\r\n                </template>\r\n                <template v-if=\"column.prop === 'bk'\">\r\n                  <template v-if=\"scope.row.state === OrderState.IN_BOOKING\">\r\n                    <el-input-number v-model=\"scope.row.orderPrices[0].bkNum\" style=\"width: 120px\" :min=\"0\" :max=\"100\" :step=\"1\" step-strictly :value-on-clear=\"0\" />\r\n                  </template>\r\n                  <template v-else> {{ scope.row.orderPrices[0].bkNum }} 份 </template>\r\n                </template>\r\n                <!-- 操作列 -->\r\n                <template v-if=\"column.prop === 'cz'\">\r\n                  <div class=\"flex-center\">\r\n                    <template v-if=\"scope.row.state === OrderState.IN_BOOKING\">\r\n                      <el-button type=\"primary\" link @click=\"writeLockCard(scope.row, true)\">\r\n                        {{ t('makeNewCard') }}\r\n                      </el-button>\r\n                      <el-button type=\"primary\" link @click=\"checkIn(scope)\">\r\n                        {{ t('checkin') }}\r\n                      </el-button>\r\n                      <el-button type=\"primary\" link @click=\"addGuest(scope)\">\r\n                        {{ t('addRoommate') }}\r\n                      </el-button>\r\n                      <el-popconfirm width=\"200\" :confirm-button-text=\"t('confirm')\" :cancel-button-text=\"t('cancel')\" :title=\"t('confirmRemoveRoom')\" @confirm=\"removeRoom(scope.row)\">\r\n                        <template #reference>\r\n                          <el-button type=\"danger\" link>\r\n                            {{ t('removeRoom') }}\r\n                          </el-button>\r\n                        </template>\r\n                      </el-popconfirm>\r\n                    </template>\r\n                    <template v-else>\r\n                      <el-button type=\"primary\" text @click=\"viewDetailed(scope.row)\">\r\n                        {{ t('viewOrder') }}\r\n                      </el-button>\r\n                    </template>\r\n                  </div>\r\n                </template>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n  <ArranGeRts v-if=\"addRtsVisible\" v-model=\"addRtsVisible\" :channel-code=\"dt.channelCode\" :checkin-type=\"dt.checkinType\" :guest-src-type=\"dt.guestSrcType\" :guest-code=\"dt.guestCode\" @success=\"selectRooms\" />\r\n  <!-- 改价 -->\r\n  <RoomEditPrice\r\n    v-if=\"roomPriceProps.visible\"\r\n    v-model=\"roomPriceProps.visible\"\r\n    :room-list-price=\"roomPriceProps.initialPriceList\"\r\n    :rt-code=\"roomPriceProps.rtCode\"\r\n    :order-no=\"roomPriceProps.orderNo\"\r\n    :order-type=\"roomPriceProps.orderType\"\r\n    @success=\"echoList\"\r\n  />\r\n  <!-- 排房 -->\r\n  <!--  排房弹窗 -->\r\n  <ArrangeRoomsDialog v-if=\"arrangeRoomsProps.visible\" v-model=\"arrangeRoomsProps.visible\" v-bind=\"arrangeRoomsProps\" :is-alone=\"isAlone\" :book-no=\"dt.bookNo\" :rooms=\"rooms\" @success=\"selectRooms\" @reload=\"arrangeRoomConfirm\" />\r\n  <!-- 升级房型 -->\r\n  <EasyDialog ref=\"easyDialogRef\" :title=\"t('roomUpgrade')\" is-body dialog-width=\"500\" show-cancel-button :show-confirm-button=\"roomOptions.length > 0\" @submit=\"formSubmit()\" @close=\"formClose()\">\r\n    <EasyForm\r\n      v-if=\"roomOptions.length > 0\"\r\n      ref=\"easyFormRef\"\r\n      :field-list=\"ruleFieldList\"\r\n      :model=\"modelForm\"\r\n      :options=\"{\r\n        labelSuffix: '：',\r\n      }\"\r\n    >\r\n      <template #upgradeRtCode>\r\n        <el-form-item :label=\"t('upgradeRoomType')\" prop=\"upgradeRtCode\" :rules=\"[{ required: true, message: t('validation.upgradeRoomTypeRequired') }]\">\r\n          <el-select v-model=\"modelForm.upgradeRtCode\" :placeholder=\"t('selectUpgradeRoomType')\">\r\n            <el-option v-for=\"item in updateRoomOptions\" :key=\"item.rtCode\" :label=\"item.rtName\" :disabled=\"item.canSellNum === 0 && item.canOverNum === 0\" :value=\"item.rtCode\">\r\n              <div class=\"flex-between\">\r\n                <div>{{ item.rtName }}</div>\r\n                <div class=\"flex-center\" style=\"color: var(--el-text-color-secondary); font-size: 13px\">\r\n                  <span class=\"w-[75px]\">\r\n                    {{ t('availableRooms') }}：<span :class=\"item.canSellNum > 0 ? 'text-[var(--el-color-primary)]' : ''\">{{ item.canSellNum }}</span>\r\n                  </span>\r\n                  <span class=\"w-[75px]\">\r\n                    {{ t('overBookingRooms') }}：<span :class=\"item.canOverNum > 0 ? 'text-[var(--el-color-primary)]' : ''\">{{ item.canOverNum }}</span>\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </template>\r\n    </EasyForm>\r\n    <el-empty v-else :description=\"t('orderAlreadyArrangedCannotUpgrade')\" />\r\n  </EasyDialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 证件号码校验样式\r\n.id-number-valid {\r\n  :deep(.el-input__wrapper) {\r\n    border-color: #67c23a !important;\r\n    box-shadow: 0 0 0 1px #67c23a inset !important;\r\n  }\r\n}\r\n\r\n.id-number-invalid {\r\n  :deep(.el-input__wrapper) {\r\n    border-color: #f56c6c !important;\r\n    box-shadow: 0 0 0 1px #f56c6c inset !important;\r\n  }\r\n}\r\n\r\n.blance {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80px;\r\n  height: 60px;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-align: center;\r\n  background-color: red;\r\n  border: 1px solid transparent;\r\n  border-radius: 4px;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.custom-width {\r\n  width: 50px !important;\r\n}\r\n\r\n.el-form-item {\r\n  // margin-right: 0;\r\n  margin-top: 5px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.prices {\r\n  padding: 5px;\r\n  border: 1px solid rgb(228 224 224);\r\n  border-radius: 4px;\r\n  transition: border-color 0.3s ease;\r\n\r\n  .center-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.dropdown-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.dropdown-container .spaced-dropdown:not(:last-child) {\r\n  margin-right: 5px;\r\n}\r\n\r\n:deep(.el-card__body) {\r\n  padding: 12px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "data", "ref", "loading", "tableAutoHeight", "formRef", "dt", "batches", "queryParams", "reactive", "gcode", "hcode", "idTypes", "checkinTypes", "guestSrcTypes", "rooms", "isAlone", "serverTime", "bookRoomsLoading", "expandedRows", "easyDialogRef", "easyFormRef", "addRtsVisible", "_modelForm", "upgradeMethod", "bookNo", "rtCode", "upgradeRtCode", "orderNo", "columns", "prop", "label", "width", "align", "modelForm", "roomOptions", "guestSrcTypeValidationMessage", "computed", "value", "guestSrcType", "GuestSrcType", "MEMBER", "AGENT", "PROTOCOL", "isGuestSrcTypeNotIndividual", "WALK_IN", "formRules", "teamName", "required", "message", "trigger", "guest<PERSON><PERSON>", "onMounted", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "DICT_TYPE_ID_TYPE", "DICT_TYPE_CHECKIN_TYPE", "DICT_TYPE_GUEST_SRC_TYPE", "then", "res", "filter", "item", "dictType", "code", "getTeamBook", "userApi", "listSeller", "sellers", "async", "serverTimeApi", "getServerTime", "params", "hotelParamConfigApi", "getHotelParamConfigFront", "frontConfig", "getFrontConfig", "window", "CallBridge", "__RUNNING_IN_PMS_AGENT__", "<PERSON><PERSON><PERSON><PERSON>", "closeSocket", "popoverContent", "onUnmounted", "length", "removeEventListener", "handleScroll", "scrollTop", "document", "documentElement", "agents", "<PERSON><PERSON><PERSON><PERSON>", "idOneRoom", "needId", "orderNotice", "getAgents", "type", "customerApi", "simpleList", "belongHcode", "paType", "isEnable", "BooleanEnum", "YES", "orderApi", "_a", "for<PERSON>ach", "batch", "bookRooms", "bookRoom", "togethers", "togetherCode", "rCode", "rNo", "sex", "SexEnum", "SECRECY", "toString", "nation", "address", "name", "phone", "idType", "IdType", "IDCERT", "idNo", "isMadeCard", "is<PERSON><PERSON>", "state", "together", "index", "<PERSON><PERSON><PERSON>", "originalData", "includes", "push", "editTeamInfo", "editTeamInfoFun", "save", "validate", "valid", "channelCode", "checkinType", "<PERSON><PERSON><PERSON>", "teamCode", "contractNo", "contact", "guarantyStyle", "seller", "remark", "bookApi", "updateBaseTeamBookInfo", "ElMessage", "success", "center", "handleAddRoom", "hotelDoorConfig", "getSysTime", "writeLockCard", "row", "newCard", "warning", "isInClient", "lockVersion", "showClose", "dangerouslyUseHTMLString", "deviceApi", "getHotelDoor", "getHotelDoorConfig", "isConnected", "Promise", "resolve", "setTimeout", "initCardReader", "JSON", "parse", "succeed", "method", "ClientMethodEnum", "WRITELOCKCARD", "cardInfo", "ElNotification", "title", "generateCardInfoHtml", "position", "roomCardLogApi", "createRoomCardLog", "cardNo", "periodTime", "dayjs", "Number", "expire", "format", "console", "log", "catch", "error", "sysTimeString", "timeStampInSeconds", "Math", "floor", "parseInt", "json", "lock<PERSON>er", "version", "roomNo", "checkin", "planCheckoutTime", "valueOf", "allowLockOut", "replaceCard", "checkTime", "_b", "lockNo", "mac", "buildNo", "floorNo", "conf", "param", "parameterCode", "parameterContent", "jsonString", "stringify", "timer", "setInterval", "handleLockCard", "clearInterval", "readIdNo", "scope", "peopleIDCode", "peopleName", "peopleAddress", "peopleSex", "MALE", "FEMALE", "getGenderFromIdCard", "peopleNation", "indexOf", "i", "j", "k", "readIdCard", "removeGuest", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "trim", "requestData", "removeTogether", "removeGuestFromFrontend", "cleanupOriginalData", "msg", "response", "performRemoveGuest", "targetGuest", "splice", "keysToDelete", "Object", "keys", "key", "validateIdNumber", "<PERSON><PERSON><PERSON><PERSON>", "idCard", "toUpperCase", "test", "weights", "checkCodes", "sum", "checkCode", "year", "substring", "month", "day", "currentYear", "Date", "getFullYear", "date", "getMonth", "getDate", "validateIdCard", "PASSPORT", "passport", "validatePassport", "OTHER", "id", "validateOtherIdCard", "validateIdNumberInput", "validation", "clearTimeout", "_validationTimer", "getIdNumberValidationClass", "handleIdTypeChange", "saveGuestInfo", "inputRefs", "handleKeyNavigation", "event", "currentInputId", "currentInput", "$el", "inputElement", "querySelector", "selectionStart", "selectionEnd", "shouldNavigateBetweenInputs", "preventDefault", "batchIndex", "roomIndex", "togetherIndex", "fieldType", "split", "currentBatch", "currentRoom", "currentTogether", "fieldOrder", "currentFieldIndex", "targetInputId", "generateInputId", "sortedInputs", "input", "disabled", "sort", "a", "b", "aBatch", "aRoom", "aTogether", "aField", "bB<PERSON>", "bRoom", "b<PERSON><PERSON><PERSON><PERSON>", "b<PERSON><PERSON>", "aBatchNum", "bBatchNum", "aRoomNum", "bRoomNum", "aTogetherNum", "bTogetherNum", "currentId", "currentIndex", "findPreviousRowLastField", "findNextRowFirstField", "targetId", "startsWith", "findSameColumnPreviousRow", "nextTogetherTargetId", "nextRoomTargetId", "nextBatchTargetId", "findSameColumnNextRow", "targetInput", "focus", "isValidValue", "String", "<PERSON><PERSON><PERSON>", "hasPhone", "hasIdNo", "tempKeys", "originalTogether", "nameChanged", "phoneChanged", "idNoChanged", "idTypeChanged", "find", "room", "planCheckinTime", "bookGuestInfoSave", "newTogetherKey", "updateKey", "roomPriceProps", "visible", "orderType", "initialPriceList", "bkNum", "price", "priceDate", "priceStrategyCode", "roomBkNum", "vipPrice", "week", "echoList", "echoId", "members", "remoteQueryMember", "query", "validateTipPhone", "memberApi", "getMemberNoPage", "list", "getInfo", "vipChange", "mcode", "vipData", "onChange", "target", "_value", "guestSrcTypeName", "guestSrcTypeItem", "selectRooms", "filteredDataList", "dataList", "roomNum", "roomTypes", "map", "dayPrices", "dayPrice", "priceType", "isMeetingRoom", "requestParams", "days", "batchNo", "slice", "addTeamRoom", "arrangeRoomsProps", "rtName", "rtState", "rNos", "arrangeRoom", "arrangeRoomConfirm", "convertIdType", "formClose", "resetFields", "updateRoomOptions", "getRoomtype", "ruleFieldList", "field", "options", "radioOptions", "newValue", "rules", "valueKey", "labelKey", "selectOptions", "slot", "handleExpandChange", "expanded", "info", "batchDeleteBookRoom", "autoArrangeRooms", "currentTime", "startOf", "endOf", "isBefore", "isAfter", "roomsWithRNo", "OrderState", "IN_BOOKING", "hasMissingName", "hasMissingIdNo", "missingNameRooms", "missingIdNoRooms", "join", "Error", "isSendSms", "NO", "orders", "orderPrices", "persons", "person", "bookCheckIn", "specificTime", "show", "dedu<PERSON><PERSON><PERSON><PERSON>", "deduplicateByProperty", "removePropertyFromArray", "NoType", "ORDER", "missingNameGuests", "count", "missingIdNoGuests", "newTogether", "totalRooms", "deleteBookRoom", "putUpgrade"], "mappings": "mxHAiZA,MAAMA,GAAQC,EAaRC,GAAQC,IAQRC,EAAEA,IAAMC,IAERC,GAAYC,IACZC,GAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,IAsFbC,GAAUH,IAEVI,GAAKJ,EAA2B,CACpCK,QAAS,KAGLC,GAAcC,EAAiB,CACnCC,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,QAIbC,GAAUV,EAAqB,IAE/BW,GAAeX,EAAqB,IAEpCY,GAAgBZ,EAAqB,IAGrCa,GAAQb,EAAI,IAEZc,GAAUd,GAAI,GAEde,GAAaf,EAAI,IAEjBgB,GAAmBhB,GAAI,GAEvBiB,GAAejB,EAAI,IAEnBkB,GAAgBlB,IAEhBmB,GAAcnB,IAEdoB,GAAgBpB,GAAI,GAEpBqB,GAAad,EAAS,IACvBD,GAEHgB,cAAe,EAEfC,OAAQhC,GAAMgC,OAAO,GAErBC,OAAQ,GAERC,cAAe,GAEfC,QAAS,KAGLC,GAAU,CACd,CACEC,KAAM,OACNC,MAAOlC,GAAE,+BACTmC,MAAO,QACPC,MAAO,QAET,CACEH,KAAM,QACNC,MAAOlC,GAAE,8BACTmC,MAAO,QACPC,MAAO,QAET,CACEH,KAAM,YACNC,MAAOlC,GAAE,0BACTmC,MAAO,QACPC,MAAO,QAET,CACEH,KAAM,aACNC,MAAOlC,GAAE,+BACTmC,MAAO,QACPC,MAAO,QAET,CACEH,KAAM,KACNC,MAAOlC,GAAE,kCACTmC,MAAO,QACPC,MAAO,UAET,CACEH,KAAM,KACNC,MAAOlC,GAAE,2BACTmC,MAAO,QACPC,MAAO,WAILC,GAAYhC,EAAI,IAAKqB,KAErBY,GAAcjC,EAAW,IAazBkC,GAAgCC,GAAS,IACzC/B,GAAGgC,MAAMC,eAAiBC,GAAaC,OAClC5C,GAAE,qCACAS,GAAGgC,MAAMC,eAAiBC,GAAaE,MACzC7C,GAAE,oCACAS,GAAGgC,MAAMC,eAAiBC,GAAaG,SACzC9C,GAAE,uCAEFA,GAAE,8CAKP+C,GAA8BP,GAAS,IACpC/B,GAAGgC,MAAMC,eAAiBC,GAAaK,UAG1CC,GAAY5C,EAAe,CAC/B6C,SAAU,CAAC,CAAEC,UAAU,EAAMC,QAASpD,GAAE,oBAAqBqD,QAAS,SACtEC,UAAW,CACT,CACEH,SAAUJ,GAA4BN,MACtCW,QAASb,GAA8BE,MACvCY,QAAS,WAIfE,GAAU,KA8BIC,GAAAC,iBAAiB,CAACC,GAAmBC,GAAwBC,KAA2BC,MAAMC,IAChG/C,GAAA0B,MAAQqB,EAAI1D,KAAK2D,QAAQC,GAAcA,EAAKC,WAAaP,KACpD1C,GAAAyB,MAAQqB,EAAI1D,KAAK2D,QAAQC,GAAcA,EAAKC,WAAaN,KACtE1C,GAAcwB,MAAQqB,EAAI1D,KAAK2D,QAAQC,GAAcA,EAAKC,WAAaL,KAA0BG,QAAQC,GAAyC,MAAdA,EAAKE,MAA8B,MAAdF,EAAKE,MAAY,IA/BhKC,KA2EZC,GAAQC,WAAW1D,IAAakD,MAAMC,IACnB,IAAbA,EAAII,OACNI,GAAQ7B,MAAQqB,EAAI1D,KAAA,IAtB1BmE,iBACE,MAAMT,QAAYU,GAAcpD,WAAWlB,GAAUW,MAAO,KAC5DO,GAAWqB,MAAQqB,EAAI1D,IAAA,CAvDTqE,GAwChB,WACE,MAAMC,EAAS,CACb7D,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,OAEnB6D,EAAoBC,yBAAyBF,GAAQb,MAAMC,IACxC,IAAbA,EAAII,OACNW,GAAYpC,MAAQqB,EAAI1D,KAAA,GAE3B,CA9Cc0E,QAC2B,IAA9BC,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,GAAWC,aAAY,IAG3B,MAAMC,GAAiB/E,IACvBgF,GAAY,UACgC,IAA9BN,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,GAAWC,cAETC,GAAe3C,OAAS2C,GAAe3C,MAAM6C,OAAS,GACzCF,GAAA3C,MAAM8C,oBAAoB,SAAUC,GAAY,IAI7D,MAAAC,GAAYpF,EAAmB,MACrC,SAASmF,KACGC,GAAAhD,MAAQiD,SAASC,gBAAgBF,SAAA,CAGvC,MAAAG,GAASvF,EAA6D,IAU5E,MAAMwE,GAAcxE,EAAI,CACtBwF,WAAY,GACZC,UAAW,GACXC,OAAQ,GACRC,YAAa,KAqBf,SAASC,GAAUC,GACjBC,EACGC,WAAW,CACVvF,MAAOX,GAAUW,MACjBwF,YAAanG,GAAUY,MACvBwF,OAAQJ,EACRK,SAAUC,GAAYC,MAEvB5C,MAAMC,IACL8B,GAAOnD,MAAQqB,EAAI1D,IAAA,GACpB,CAGC,MAAAkE,GAAUjE,EAA8C,IAU9D,SAAS8D,KACP9C,GAAiBoB,OAAQ,EAChBiE,GAAAvC,YAAY,CAAEvC,OAAQhC,GAAMgC,OAAO,KAAMiC,MAAMC,UACtDzC,GAAiBoB,OAAQ,EACR,IAAbqB,EAAII,OACNzD,GAAGgC,MAAQqB,EAAI1D,KAEf,OAAAuG,EAAAlG,GAAGgC,MAAM/B,UAASiG,EAAAC,SAASC,IACnBA,EAAAC,UAAUF,SAASG,IAClBA,EAASC,WAA2C,IAA9BD,EAASC,UAAU1B,SAC5CyB,EAASC,UAAY,CACnB,CACEC,aAAc,GACdC,MAAO,GACPC,IAAK,GACLC,IAAKC,GAAQC,QAAQC,WACrBC,OAAQ,GACRC,QAAS,GACTC,KAAM,GACNC,MAAO,GACPC,OAAQC,GAAOC,OACfC,KAAM,GACNC,WAAY,IACZC,OAAQ,IACRC,MAAO,MAMbnB,EAASC,UAAUJ,SAAQ,CAACuB,EAAeC,KAErC,IAAAC,EAEFA,EADEF,EAASlB,aACG,GAAGF,EAAShF,WAAWoG,EAASlB,eAGhC,GAAGF,EAAShF,eAAeqG,IAG9BE,GAAA7F,MAAM4F,GAAe,CAChCX,KAAMS,EAAST,MAAQ,GACvBC,MAAOQ,EAASR,OAAS,GACzBI,KAAMI,EAASJ,MAAQ,GACvBH,OAAQO,EAASP,QAAU,GAC7B,IAIEb,EAASC,WAAaD,EAASC,UAAU1B,OAAS,IAC/ChE,GAAamB,MAAM8F,SAASxB,EAAShF,UAC3BT,GAAAmB,MAAM+F,KAAKzB,EAAShF,SACnC,GAEH,IACF,GAEJ,CAEG,MAAA0G,GAAepI,GAAI,GACzB,SAASqI,KACPD,GAAahG,OAAQ,CAAA,CAEvB,SAASkG,KACHnI,GAAQiC,OACFjC,GAAAiC,MAAMmG,UAAUC,IACtB,GAAIA,EAAO,CACT,MAAMnE,EAAS,CACb9C,OAAQnB,GAAGgC,MAAMb,OACjBkH,YAAarI,GAAGgC,MAAMqG,YACtB5F,SAAUzC,GAAGgC,MAAMS,SACnBR,aAAcjC,GAAGgC,MAAMC,aACvBqG,YAAatI,GAAGgC,MAAMsG,YACtBzF,UAAW7C,GAAGgC,MAAMa,UACpB0F,UAAWvI,GAAGgC,MAAMuG,UACpBC,SAAUxI,GAAGgC,MAAMwG,SACnBC,WAAYzI,GAAGgC,MAAMyG,WACrBC,QAAS1I,GAAGgC,MAAM0G,QAClBxB,MAAOlH,GAAGgC,MAAMkF,MAChByB,cAAe3I,GAAGgC,MAAM2G,cACxBC,OAAQ5I,GAAGgC,MAAM4G,OACjBC,OAAQ7I,GAAGgC,MAAM6G,QAEnBC,GAAQC,uBAAuB9E,GAAQb,MAAMC,IAC1B,IAAbA,EAAII,OACNuF,EAAUC,QAAQ,CAChBtG,QAASpD,GAAE,0BACXkG,KAAM,UACNyD,QAAQ,IAEVlB,GAAahG,OAAQ,EAAA,GAExB,IAGP,CAIF,SAASmH,KACPnI,GAAcgB,OAAQ,CAAA,CA2KxB,MAAMoH,GAAkBxJ,IAiBxB,SAASyJ,KACA,OAAAtF,GAAcpD,WAAWlB,GAAUW,MAAO,KAAKgD,MAAMC,GACzC,IAAbA,EAAII,KACCJ,EAAI1D,KAEN,IACR,CAGYmE,eAAAwF,GAAcC,EAAUC,WAEjC,IAACD,EAAI7C,IAEP,YADUsC,EAAAS,QAAQlK,GAAE,uCAGlB,IAACmK,KACH,OAGE,IAACH,EAAII,YAOP,YANUX,EAAA,CACRY,WAAW,EACXjH,QAASpD,GAAE,8BACXkG,KAAM,UACNoE,0BAA0B,IAQ1B,SA7CN,SAA4BF,GAC1B,MAAM1F,EAAS,IACV/D,MACCyJ,GAAe,CAAEA,gBAEvB,OAAOG,GAAUC,aAAa9F,GAAQb,MAAMC,IACzB,IAAbA,EAAII,MAAcJ,EAAI1D,OACxByJ,GAAgBpH,MAAQqB,EAAI1D,KAAA,GAE/B,CAiCKqK,CAAmBT,EAAII,cAGxBP,GAAgBpH,MAOnB,YANUgH,EAAA,CACRY,WAAW,EACXjH,QAASpD,GAAE,8BACXkG,KAAM,UACNoE,0BAA0B,IAK1BpF,GAAWwF,cACbxF,GAAWC,oBAEL,IAAIwF,SAASC,GAAYC,WAAWD,EAAS,QAG1C1F,GAAA4F,gBAAgB1H,IACnBhD,MAAAA,EAAO2K,KAAKC,MAAM5H,GACpBhD,IAAyB,IAAjBA,EAAK6K,SAAoB7K,EAAK8K,SAAWC,GAAiBC,gBAChEhL,EAAKiL,WACQC,EAAA,CACbC,MAAOvL,GAAE,qBACTsK,0BAA0B,EAC1BlH,QAASoI,GAAqBpL,GAC9BqL,SAAU,cACVvF,KAAM,YAIRwF,GACGC,kBAAkB,CACjB9K,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjB4G,KAAMsC,EAAIhB,UACV7B,IAAK6C,EAAI7C,IACTyE,OAAQxL,EAAKiL,SAASO,OACtB1F,KAAM+D,EAAU,IAAM,IACtB4B,WAAYC,EAAqC,IAA/BC,OAAO3L,EAAKiL,SAASW,SAAgBC,OAAO,yBAE/DpI,MAAMC,IACY,IAAbA,EAAII,MACNgI,QAAQC,IAAI,WAAU,IAGzBC,OAAOC,IAEEH,QAAAG,MAAM,YAAaA,EAAK,KAG5B5C,EAAA,CACRrG,QAAS,GAAG4G,EAAI7C,MAAMnH,GAAE,8BACxBkG,KAAM,YACP,IAIC,MAAAoG,QAAsBxC,KAC5B,IAAKwC,EAEH,YADAJ,QAAQG,MAAM,6BAIV,MAAAE,EAAqBC,KAAKC,MAAMV,OAAOW,SAASJ,EAAe,IAAM,KAErEK,EAAO,CACXzB,OAAQC,GAAiBC,cACzBwB,QAAS/C,GAAgBpH,MAAMoK,QAC/BxB,SAAU,CACRyB,OAAQ9C,EAAI7C,IACZ4F,QAASR,EACTP,OAAQQ,KAAKC,MAAMX,EAAM9B,EAAIgD,kBAAkBC,UAAY,KAC3DC,cAAc,OAAAvG,EAAAkD,GAAgBpH,YAAhB,EAAAkE,EAAuBuG,gBAAiB1G,GAAYC,IAClE0G,YAAalD,EACbmD,WAAW,OAAAC,EAAAxD,GAAgBpH,YAAhB,EAAA4K,EAAuBD,aAAc5G,GAAYC,IAC5D6G,OAAQtD,EAAIsD,OACZC,IAAKvD,EAAIuD,IACTC,QAASxD,EAAIwD,QACbC,QAASzD,EAAIyD,UAKb5D,GAAgBpH,MAAMiL,MAAQ7D,GAAgBpH,MAAMiL,KAAKpI,OAAS,GAEpEuE,GAAgBpH,MAAMiL,KAAK9G,SAAS+G,IAClChB,EAAKtB,SAASsC,EAAMC,eAAiBD,EAAME,gBAAA,IAKzC,MAAAC,EAAa/C,KAAKgD,UAAUpB,GAC1BT,QAAAC,IAAI,aAAc2B,GACpB,MAAAE,EAAQC,aAAY,KACpB/I,GAAWwF,cACbxF,GAAWgJ,eAAeJ,GAC1BK,cAAcH,GAAK,GAEpB,IAAG,CAGR,SAAS7D,KACP,QAA0C,IAA9BpF,OAAeC,iBAAkF,IAA5CD,OAAeE,yBAA0C,CASjH,OANGwE,EAAA,CACRY,WAAW,EACXjH,QAAS,0BAHU,+IAInB8C,KAAM,UACNoE,0BAA0B,KAErB,CAAA,CAEF,OAAA,CAAA,CAIA,SAAA8D,GAASC,EAAYzO,GACxB,IAACuK,KACH,OAGEjF,GAAWwF,aACbxF,GAAWC,cAGFD,GAAA4F,gBAAgB1H,IACnBhD,MAAAA,EAAO2K,KAAKC,MAAM5H,GACxB,GAAIhD,EAAM,CACR,MAAM2H,EAAO3H,EAAKkO,aACZtF,EAAY5I,EAAKmO,WACjB9G,EAAUrH,EAAKoO,cACjB,IAAApH,EAAMC,GAAQC,QAAQC,WAGlBnH,EADJA,EAAKqO,UACkB,MAAnBrO,EAAKqO,UAAoBpH,GAAQqH,KAAKnH,WAAaF,GAAQsH,OAAOpH,WAC/DnH,EAAKkO,aACRM,GAAoBxO,EAAKkO,cAAc/G,WAEvCF,GAAQC,QAAQC,WAElB,MAAAC,EAASpH,EAAKyO,aAAaC,QAAQ,KAAO1O,EAAKyO,aAAe,GAAGzO,EAAKyO,gBAE5E,GAAIjP,EAEF,IAAA,IAASmP,EAAI,EAAGA,EAAItO,GAAGgC,MAAM/B,QAAQ4E,OAAQyJ,IAClC,IAAA,IAAAC,EAAI,EAAGA,EAAIvO,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUxB,OAAQ0J,IACxD,IAAA,IAASC,EAAI,EAAGA,EAAIxO,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAU1B,OAAQ2J,IACjExO,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiI,GAAGhI,eAAiBoH,EAAMrE,IAAI/C,eACxExG,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiI,GAAGlH,KAAOA,EAClDtH,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiI,GAAGvH,KAAOsB,EAClDvI,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiI,GAAGxH,QAAUA,EACrDhH,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiI,GAAG7H,IAAMA,EACjD3G,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiI,GAAGzH,OAASA,QAO/D,IAAA,IAASuH,EAAI,EAAGA,EAAItO,GAAGgC,MAAM/B,QAAQ4E,OAAQyJ,IAClC,IAAA,IAAAC,EAAI,EAAGA,EAAIvO,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUxB,OAAQ0J,IACpDvO,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGjN,UAAYsM,EAAMrE,IAAIjI,UACtDtB,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAU,GAAGe,KAAOA,EAClDtH,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAU,GAAGU,KAAOsB,EAClDvI,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAU,GAAGS,QAAUA,EACrDhH,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAU,GAAGI,IAAMA,EACjD3G,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAU,GAAGQ,OAASA,EAI/D,KAIE,MAAAwG,EAAQC,aAAY,KACpB/I,GAAWwF,cACbxF,GAAWgK,aACXf,cAAcH,GAAK,GAEpB,IAAG,CAIR,SAASmB,GAAYnF,GAEnBoF,EAAaC,QAAQ,WAAWrF,EAAItC,MAAQ,aAAc,OAAQ,CAChE4H,kBAAmB,KACnBC,iBAAkB,KAClBrJ,KAAM,YAELrC,MAAK,MAWV,SAA4BmG,GAE1B,GAAIA,EAAI/C,cAA4C,KAA5B+C,EAAI/C,aAAauI,OAAe,CAC9CtD,QAAAC,IAAI,aAAcnC,EAAI/C,cAE9B,MAAMwI,EAAc,CAClB5O,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjBmG,aAAc+C,EAAI/C,cAGZiF,QAAAC,IAAI,eAAgBsD,GAE5B/I,GACGgJ,eAAeD,GACf5L,MAAMC,IACY,IAAbA,EAAII,MACNuF,EAAUC,QAAQ,UAGlBiG,GAAwB3F,GAGxB4F,GAAoB5F,EAAI/C,eAEdwC,EAAA4C,MAAMvI,EAAI+L,KAAO,SAAQ,IAGtCzD,OAAOC,YACEH,QAAAG,MAAM,UAAWA,GACzBH,QAAQG,MAAM,QAASA,EAAMyD,UAAYzD,EAAMjJ,SAAWiJ,GAChD5C,EAAA4C,MAAM,YAAW,OAAAgB,EAAA,OAAA1G,EAAA0F,EAAMyD,eAAN,EAAAnJ,EAAgBvG,WAAhB,EAAAiN,EAAsBwC,MAAOxD,EAAMjJ,SAAW,SAAQ,GAClF,MAGH8I,QAAQC,IAAI,YACZwD,GAAwB3F,GAGxB4F,GAAoB,IAEpBnG,EAAUC,QAAQ,SACpB,CAnDIqG,CAAmB/F,EAAG,IAEvBoC,OAAM,KAELF,QAAQC,IAAI,aAAY,GACzB,CAkDL,SAASwD,GAAwBK,GAC/B,IAAA,IAASjB,EAAI,EAAGA,EAAItO,GAAGgC,MAAM/B,QAAQ4E,OAAQyJ,IAClC,IAAA,IAAAC,EAAI,EAAGA,EAAIvO,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUxB,OAAQ0J,IACxD,IAAA,IAASC,EAAI,EAAGA,EAAIxO,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAU1B,OAAQ2J,IAAK,CACpE,MAAA9G,EAAW1H,GAAGgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiI,GAG5D,GAAIe,EAAY/I,cAAgBkB,EAASlB,eAAiB+I,EAAY/I,aAGpE,OAFGxG,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiJ,OAAOhB,EAAG,QAC7C/C,QAAAC,IAAI,iBAAkB6D,EAAY/I,cAK5C,IAAK+I,EAAY/I,cAAgBkB,IAAa6H,EAG5C,OAFGvP,GAAAgC,MAAM/B,QAAQqO,GAAGjI,UAAUkI,GAAGhI,UAAUiJ,OAAOhB,EAAG,QACrD/C,QAAQC,IAAI,eAEd,CAGN,CAIF,SAASyD,GAAoB3I,GAC3B,IAAIiJ,EAAyB,GAIZA,EAFbjJ,GAAwC,KAAxBA,EAAauI,OAEhBW,OAAOC,KAAK9H,GAAa7F,OAAOsB,QAAQsM,GAAQA,EAAI9H,SAAStB,KAG7DkJ,OAAOC,KAAK9H,GAAa7F,OAAOsB,QAAQsM,GAAQA,EAAI9H,SAAS,WAGjE2H,EAAAtJ,SAASyJ,WACb/H,GAAa7F,MAAM4N,EAAG,IAGvBnE,QAAAC,IAAI,aAAc+D,EAAY,CAIlC,MAAA5H,GAAejI,EAAS,IA4HrB,SAAAiQ,GAAiB1I,EAAgBG,GACxC,OAAQH,GACN,KAAKC,GAAOC,OACV,OA5HN,SAAwBC,GACtB,IAAKA,GAAwB,KAAhBA,EAAKyH,OAChB,MAAO,CAAEe,SAAS,EAAMnN,QAAS,IAGnC,MAAMoN,EAASzI,EAAKyH,OAAOiB,cAGvB,GAAkB,KAAlBD,EAAOlL,OAAe,CAGxB,IADY,uFACHoL,KAAKF,GACZ,MAAO,CAAED,SAAS,EAAOnN,QAAS,cAIpC,MAAMuN,EAAU,CAAC,EAAG,EAAG,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,EAAG,EAAG,GAC7DC,EAAa,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAEtE,IAAIC,EAAM,EACV,IAAA,IAAS9B,EAAI,EAAGA,EAAI,GAAIA,IACtB8B,GAAO9E,OAAOW,SAAS8D,EAAOzB,IAAM4B,EAAQ5B,GAGxC,MAAA+B,EAAYF,EAAWC,EAAM,IAC/B,GAAAL,EAAO,MAAQM,EACjB,MAAO,CAAEP,SAAS,EAAOnN,QAAS,eAIpC,MAAM2N,EAAOhF,OAAOW,SAAS8D,EAAOQ,UAAU,EAAG,KAC3CC,EAAQlF,OAAOW,SAAS8D,EAAOQ,UAAU,GAAI,KAC7CE,EAAMnF,OAAOW,SAAS8D,EAAOQ,UAAU,GAAI,KAE3CG,GAAc,IAAIC,MAAOC,cAC3B,GAAAN,EAAO,MAAQA,EAAOI,EACxB,MAAO,CAAEZ,SAAS,EAAOnN,QAAS,gBAGhC,GAAA6N,EAAQ,GAAKA,EAAQ,GACvB,MAAO,CAAEV,SAAS,EAAOnN,QAAS,gBAGhC,GAAA8N,EAAM,GAAKA,EAAM,GACnB,MAAO,CAAEX,SAAS,EAAOnN,QAAS,gBAIpC,MAAMkO,EAAO,IAAIF,KAAKL,EAAME,EAAQ,EAAGC,GACvC,OAAII,EAAKD,gBAAkBN,GAAQO,EAAKC,aAAeN,EAAQ,GAAKK,EAAKE,YAAcN,EAC9E,CAAEX,SAAS,EAAOnN,QAAS,gBAG7B,CAAEmN,SAAS,EAAMnN,QAAS,YAAY,CAAA,GAIpB,KAAlBoN,EAAOlL,OAAe,CAE7B,IADY,iEACHoL,KAAKF,GACZ,MAAO,CAAED,SAAS,EAAOnN,QAAS,cAI9B,MAAA2N,EAAO,KAAOhF,OAAOW,SAAS8D,EAAOQ,UAAU,EAAG,IAClDC,EAAQlF,OAAOW,SAAS8D,EAAOQ,UAAU,EAAG,KAC5CE,EAAMnF,OAAOW,SAAS8D,EAAOQ,UAAU,GAAI,KAE7C,OAAAD,EAAO,MAAQA,EAAO,KACjB,CAAER,SAAS,EAAOnN,QAAS,gBAGhC6N,EAAQ,GAAKA,EAAQ,GAChB,CAAEV,SAAS,EAAOnN,QAAS,gBAGhC8N,EAAM,GAAKA,EAAM,GACZ,CAAEX,SAAS,EAAOnN,QAAS,gBAG7B,CAAEmN,SAAS,EAAMnN,QAAS,YAAY,CAE7C,MAAO,CAAEmN,SAAS,EAAOnN,QAAS,uBACpC,CAyCWqO,CAAe1J,GACxB,KAAKF,GAAO6J,SACV,OAvCN,SAA0B3J,GACxB,IAAKA,GAAwB,KAAhBA,EAAKyH,OAChB,MAAO,CAAEe,SAAS,EAAMnN,QAAS,IAGnC,MAAMuO,EAAW5J,EAAKyH,OAAOiB,cAI7B,MADY,kBACHC,KAAKiB,GAIP,CAAEpB,SAAS,EAAMnN,QAAS,YAHxB,CAAEmN,SAAS,EAAOnN,QAAS,0BAGQ,CA0BjCwO,CAAiB7J,GAC1B,KAAKF,GAAOgK,MACZ,QACE,OAzBN,SAA6B9J,GAC3B,IAAKA,GAAwB,KAAhBA,EAAKyH,OAChB,MAAO,CAAEe,SAAS,EAAMnN,QAAS,IAG7B,MAAA0O,EAAK/J,EAAKyH,OAIhB,MADY,wBACHkB,KAAKoB,GAIP,CAAEvB,SAAS,EAAMnN,QAAS,YAHxB,CAAEmN,SAAS,EAAOnN,QAAS,YAGQ,CAYjC2O,CAAoBhK,GAC/B,CAIF,SAASiK,GAAsB7J,GAC7B,IAAKA,EAASJ,MAAiC,KAAzBI,EAASJ,KAAKyH,OAClC,OAGF,MAAMyC,EAAa3B,GAAiBnI,EAASP,QAAUC,GAAOC,OAAQK,EAASJ,MAG/EmK,aAAc/J,EAAiBgK,kBACZhK,EAAAgK,iBAAmBtH,YAAW,KAC1CoH,EAAW1B,OAAS,GAIxB,IAAI,CAIT,SAAS6B,GAA2BjK,GAClC,IAAKA,EAASJ,MAAiC,KAAzBI,EAASJ,KAAKyH,OAC3B,MAAA,GAKT,OAFmBc,GAAiBnI,EAASP,QAAUC,GAAOC,OAAQK,EAASJ,MAEhEwI,QACN,kBAEA,mBACT,CAIO,SAAA8B,GAAmBlK,EAAoBpB,EAAoBF,GAE9DsB,EAASJ,MAAiC,KAAzBI,EAASJ,KAAKyH,QACjCwC,GAAsB7J,GAIVmK,GAAAnK,EAAUpB,EAAUF,EAAK,CAInC,MAAA0L,GAAYlS,EAAS,IAGlB,SAAAmS,GAAoBC,EAAsBC,GAE7C,IAAC,CAAC,UAAW,YAAa,YAAa,cAAcnK,SAASkK,EAAMpC,KACtE,OAGI,MAAAsC,EAAeJ,GAAU9P,MAAMiQ,GACrC,IAAKC,IAAiBA,EAAaC,IACjC,OAGF,MAAMC,EAAeF,EAAaC,IAAIE,cAAc,UAAYH,EAAaC,IACvEnQ,EAAQoQ,EAAapQ,OAAS,GAC9BsQ,EAAiBF,EAAaE,gBAAkB,EAChDC,EAAeH,EAAaG,cAAgB,EAqB9C,IAlBgC,MAClC,OAAQP,EAAMpC,KACZ,IAAK,YAEI,OAAmB,IAAnB0C,GAAyC,IAAjBC,EACjC,IAAK,aAEH,OAAOD,IAAmBtQ,EAAM6C,QAAU0N,IAAiBvQ,EAAM6C,OACnE,IAAK,UACL,IAAK,YAEI,OAAA,EACT,QACS,OAAA,EAAA,EAKR2N,GACH,OAIFR,EAAMS,iBAGA,MAAG,CAAAC,EAAYC,EAAWC,EAAeC,GAAaZ,EAAea,MAAM,KAC3EC,EAAezH,OAAOW,SAASyG,GAC/BM,EAAc1H,OAAOW,SAAS0G,GAC9BM,EAAkB3H,OAAOW,SAAS2G,GAGlCM,EAAa,CAAC,OAAQ,QAAS,QAC/BC,EAAoBD,EAAW7E,QAAQwE,GAE7C,IAAIO,EAAgB,GAEpB,OAAQpB,EAAMpC,KACZ,IAAK,YAEH,GAAIuD,EAAoB,EAAG,CAGzBC,EAAgBC,GAAgBN,EAAcC,EAAaC,EADzCC,EAAWC,EAAoB,GACoC,MAGrEC,EA0Cf,SAAyBL,EAAsBC,EAAqBC,GAErE,MAMAK,EANc5D,OAAOC,KAAKmC,GAAU9P,OAAOsB,QAAQ+N,IACjD,MAAAkC,EAAQzB,GAAU9P,MAAMqP,GAC9B,OAAOkC,GAASA,EAAMpB,MAAQoB,EAAMC,QAAA,IAILC,MAAK,CAACC,EAAGC,KAClC,MAAG,CAAAC,EAAQC,EAAOC,EAAWC,GAAUL,EAAEZ,MAAM,MAC5C,CAAAkB,EAAQC,EAAOC,EAAWC,GAAUR,EAAEb,MAAM,KAE/CsB,EAAY9I,OAAOW,SAAS2H,GAC5BS,EAAY/I,OAAOW,SAAS+H,GAC5BM,EAAWhJ,OAAOW,SAAS4H,GAC3BU,EAAWjJ,OAAOW,SAASgI,GAC3BO,EAAelJ,OAAOW,SAAS6H,GAC/BW,EAAenJ,OAAOW,SAASiI,GAErC,GAAIE,IAAcC,EAChB,OAAOD,EAAYC,EAErB,GAAIC,IAAaC,EACf,OAAOD,EAAWC,EAEpB,GAAIC,IAAiBC,EACnB,OAAOD,EAAeC,EAGxB,MAAMvB,EAAa,CAAC,OAAQ,QAAS,QACrC,OAAOA,EAAW7E,QAAQ0F,GAAUb,EAAW7E,QAAQ8F,EAAM,IAGzDO,EAAYrB,GAAgBN,EAAcC,EAAaC,EAAiB,QACxE0B,EAAerB,EAAajF,QAAQqG,GAE1C,GAAIC,EAAe,EACV,OAAArB,EAAaqB,EAAe,GAI9B,OAAArB,EAAaA,EAAazO,OAAS,EAAC,CAnFrB+P,CAAyB7B,EAAcC,EAAaC,GAEtE,MAEF,IAAK,aAEC,GAAAE,EAAoBD,EAAWrO,OAAS,EAAG,CAG7CuO,EAAgBC,GAAgBN,EAAcC,EAAaC,EADzCC,EAAWC,EAAoB,GACoC,MAGrEC,EA2Ef,SAAsBL,EAAsBC,EAAqBC,GAElE,MAMAK,EANc5D,OAAOC,KAAKmC,GAAU9P,OAAOsB,QAAQ+N,IACjD,MAAAkC,EAAQzB,GAAU9P,MAAMqP,GAC9B,OAAOkC,GAASA,EAAMpB,MAAQoB,EAAMC,QAAA,IAILC,MAAK,CAACC,EAAGC,KAClC,MAAG,CAAAC,EAAQC,EAAOC,EAAWC,GAAUL,EAAEZ,MAAM,MAC5C,CAAAkB,EAAQC,EAAOC,EAAWC,GAAUR,EAAEb,MAAM,KAE/CsB,EAAY9I,OAAOW,SAAS2H,GAC5BS,EAAY/I,OAAOW,SAAS+H,GAC5BM,EAAWhJ,OAAOW,SAAS4H,GAC3BU,EAAWjJ,OAAOW,SAASgI,GAC3BO,EAAelJ,OAAOW,SAAS6H,GAC/BW,EAAenJ,OAAOW,SAASiI,GAErC,GAAIE,IAAcC,EAChB,OAAOD,EAAYC,EAErB,GAAIC,IAAaC,EACf,OAAOD,EAAWC,EAEpB,GAAIC,IAAiBC,EACnB,OAAOD,EAAeC,EAGxB,MAAMvB,EAAa,CAAC,OAAQ,QAAS,QACrC,OAAOA,EAAW7E,QAAQ0F,GAAUb,EAAW7E,QAAQ8F,EAAM,IAGzDO,EAAYrB,GAAgBN,EAAcC,EAAaC,EAAiB,QACxE0B,EAAerB,EAAajF,QAAQqG,GAEtC,GAAAC,EAAerB,EAAazO,OAAS,EAChC,OAAAyO,EAAaqB,EAAe,GAIrC,OAAOrB,EAAa,EAAC,CApHCuB,CAAsB9B,EAAcC,EAAaC,GAEnE,MAEF,IAAK,UAEHG,EAkHN,SAAmCL,EAAsBC,EAAqBC,EAAyBJ,GAErG,GAAII,EAAkB,EAAG,CACvB,MAAM6B,EAAWzB,GAAgBN,EAAcC,EAAaC,EAAkB,EAAGJ,GAC7E,GAAAf,GAAU9P,MAAM8S,GACX,OAAAA,CACT,CAIF,GAAI9B,EAAc,EAAG,CACnB,MAAM8B,EAAWzB,GAAgBN,EAAcC,EAAc,EAAG,EAAGH,GAC/D,GAAAf,GAAU9P,MAAM8S,GACX,OAAAA,CACT,CAIF,GAAI/B,EAAe,EAAG,CAIhB,GAFgBrD,OAAOC,KAAKmC,GAAU9P,OACNsB,QAAQ+N,GAAOA,EAAG0D,WAAW,SAAShC,EAAe,QACrElO,OAAS,EAAG,CAC9B,MAAMiQ,EAAWzB,GAAgBN,EAAe,EAAG,EAAG,EAAGF,GACrD,GAAAf,GAAU9P,MAAM8S,GACX,OAAAA,CACT,CACF,CAGK,MAAA,EAAA,CAhJaE,CAA0BjC,EAAcC,EAAaC,EAAiBJ,GACtF,MAEF,IAAK,YAEHO,EA+IN,SAA+BL,EAAsBC,EAAqBC,EAAyBJ,GAEjG,MAAMoC,EAAuB5B,GAAgBN,EAAcC,EAAaC,EAAkB,EAAGJ,GACzF,GAAAf,GAAU9P,MAAMiT,GACX,OAAAA,EAIT,MAAMC,EAAmB7B,GAAgBN,EAAcC,EAAc,EAAG,EAAGH,GACvE,GAAAf,GAAU9P,MAAMkT,GACX,OAAAA,EAIT,MAAMC,EAAoB9B,GAAgBN,EAAe,EAAG,EAAG,EAAGF,GAC9D,GAAAf,GAAU9P,MAAMmT,GACX,OAAAA,EAGF,MAAA,EAAA,CAlKaC,CAAsBrC,EAAcC,EAAaC,EAAiBJ,GAKtF,GAAIO,GAAiBtB,GAAU9P,MAAMoR,GAAgB,CAC7C,MAAAiC,EAAcvD,GAAU9P,MAAMoR,GAChCiC,GAAeA,EAAYC,OAC7BD,EAAYC,OACd,CACF,CAIF,SAASjC,GAAgBX,EAAoBC,EAAmBC,EAAuBC,GACrF,MAAO,SAASH,KAAcC,KAAaC,KAAiBC,GAAS,CAuJ9D,SAAAhB,GAAcnK,EAAoBpB,EAAoBF,GAEvD,MAAAmP,EAAgBvT,GACbA,SAAmD,KAAVA,GAAyC,KAAzBwT,OAAOxT,GAAO+M,OAG1E0G,EAAUF,EAAa7N,EAAST,MAChCyO,EAAWH,EAAa7N,EAASR,OACjCyO,EAAUJ,EAAa7N,EAASJ,MAGtC,IAAKmO,IAAYC,IAAaC,EAC5B,OAIF,GAAIA,EAAS,CACL,MAAAnE,EAAa3B,GAAiBnI,EAASP,QAAUC,GAAOC,OAAQK,EAASJ,MAAQ,IACnF,IAACkK,EAAW1B,QAEd,YADU9G,EAAA4C,MAAM4F,EAAW7O,QAE7B,CAIE,IAAAiF,EACJ,GAAIF,EAASlB,aACXoB,EAAc,GAAGtB,EAAShF,WAAWoG,EAASlB,mBACzC,CAEL,MAAMoP,EAAWlG,OAAOC,KAAK9H,GAAa7F,OAAOsB,QAAQsM,GAAQA,EAAImF,WAAW,GAAGzO,EAAShF,kBAE9EsG,EAAAgO,EAAS/Q,OAAS,EAAI+Q,EAAS,GAAK,GAAGtP,EAAShF,eAAO,CAGjE,MAAAuU,EAAmBhO,GAAa7F,MAAM4F,GAE5C,GAAIiO,EAAkB,CAEpB,MAAMC,GAAepO,EAAST,MAAQ,OAAS4O,EAAiB5O,MAAQ,IAClE8O,GAAgBrO,EAASR,OAAS,OAAS2O,EAAiB3O,OAAS,IACrE8O,GAAetO,EAASJ,MAAQ,OAASuO,EAAiBvO,MAAQ,IAClE2O,GAAiBvO,EAASP,QAAU,OAAS0O,EAAiB1O,QAAU,IAG9E,KAAK2O,GAAgBC,GAAiBC,GAAgBC,GACpD,MACF,CAIF,IAAIlD,EAAe3M,EACnB,IAAK2M,EACQ,IAAA,MAAAY,KAAK3T,GAAGgC,MAAM/B,QAAS,CAEhC,GADkB0T,EAAEtN,UAAU6P,MAAMC,GAASA,EAAK7U,UAAYgF,EAAShF,UACxD,CACEyR,EAAAY,EACf,KAAA,CACF,CAKJ,MAAM1P,EAAc,CAClB7D,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjBiB,QAASgF,EAAShF,QAClBH,OAAQhC,GAAMgC,OAAO,GACrBiV,gBAAiB9P,EAAS8P,gBAC1B7J,iBAAkBjG,EAASiG,iBAC3BnL,OAAQkF,EAASlF,OACjBqF,MAAOH,EAASG,OAAS,GACzBC,IAAKJ,EAASI,KAAO,GACrBO,KAAMS,EAAST,MAAQ,GACvBC,MAAOQ,EAASR,OAAS,GACzBC,OAAQO,EAASP,QAAUC,GAAOC,OAClCC,KAAMI,EAASJ,MAAQ,GACvBE,OAAQE,EAASF,QAAU,KAIzBE,EAASlB,eACXvC,EAAOuC,aAAekB,EAASlB,cAIjCP,GACGoQ,kBAAkBpS,GAClBb,MAAMC,IACD,GAAa,IAAbA,EAAII,KAIF,GAHJuF,EAAUC,QAAQ,QAGd5F,EAAI1D,MAA4B,iBAAb0D,EAAI1D,OAAsB+H,EAASlB,aAAc,CAC9DiF,QAAAC,IAAI,aAAcrI,EAAI1D,MAC9B+H,EAASlB,aAAenD,EAAI1D,KAGX+P,OAAOC,KAAK9H,GAAa7F,OAAOsB,QAAQsM,GAAQA,EAAImF,WAAW,GAAGzO,EAAShF,kBACnF6E,SAASyJ,WACT/H,GAAa7F,MAAM4N,EAAG,IAI/B,MAAM0G,EAAiB,GAAGhQ,EAAShF,WAAW+B,EAAI1D,OACrCkI,GAAA7F,MAAMsU,GAAkB,CACnCrP,KAAMS,EAAST,MAAQ,GACvBC,MAAOQ,EAASR,OAAS,GACzBI,KAAMI,EAASJ,MAAQ,GACvBH,OAAQO,EAASP,QAAU,IAGrBsE,QAAAC,IAAI,gBAAiB4K,EAAc,KACtC,CAED,IAAAC,EAEFA,EADE7O,EAASlB,aACC,GAAGF,EAAShF,WAAWoG,EAASlB,eAGhCoB,EAGDC,GAAA7F,MAAMuU,GAAa,CAC9BtP,KAAMS,EAAST,MAAQ,GACvBC,MAAOQ,EAASR,OAAS,GACzBI,KAAMI,EAASJ,MAAQ,GACvBH,OAAQO,EAASP,QAAU,GAC7B,MAGQ6B,EAAA4C,MAAMvI,EAAI+L,KAAO,OAAM,IAGpCzD,OAAOC,IACEH,QAAAG,MAAM,YAAaA,GAC3B5C,EAAU4C,MAAM,OAAM,GACvB,CAYL,MAAM4K,GAAiB5W,EAAI,CACzB6W,SAAS,EACTrV,OAAQ,GACRE,QAAS,GACToV,UAAW,GACXC,iBAAkB,CAChB,CACEC,MAAO,EACPC,MAAO,EACPC,UAAW,GACXC,kBAAmB,GACnBC,UAAW,EACXC,SAAU,EACVC,KAAM,MAeH,SAAAC,GAASnV,EAAwBoV,GAC5B1T,IAAA,CA2KR,MAAA2T,GAAUzX,EAUd,IAkBF,SAAS0X,GAAkBC,GAEZC,GAAiBD,KAE5BF,GAAQrV,MAAQ,GAChBrC,GAAKqC,MAAMnC,SAAU,EACrBuK,YAAWtG,UACTnE,GAAKqC,MAAMnC,SAAU,EACf,MAAAwD,QAvBZS,eAAuByT,GACrB,MAAMtT,EAAS,CACb7D,MAAOX,GAAUW,MACjB8G,MAAOqQ,EAAM1S,OAAS,GAAK,GAAK0S,EAChCjQ,KAAMiQ,EAAM1S,OAAS,GAAK0S,EAAQ,GAClC9P,MAAO1B,GAAYC,KAGf3C,QAAYoU,EAAUC,gBAAgBzT,GACxC,GAAAZ,GAAQA,EAAI1D,KACd,OAAO0D,EAAI1D,KAAKgY,IAClB,CAYsBC,CAAQL,GACtBlU,IACFgU,GAAQrV,MAAQqB,EAAA,GAEjB,KAIL,CAIF,SAASwU,GAAU7V,GACXrC,MAAAA,EAAO0X,GAAQrV,MAAMsB,QAAQC,GAAcA,EAAKuU,QAAU9V,IAAO,GACpEhC,GAAAgC,MAAM0G,QAAU/I,EAAKsH,KACrBjH,GAAAgC,MAAMkF,MAAQvH,EAAKuH,KAAA,CAUxB,MAAM6Q,GAAUnY,IAGhB,SAASoY,GAAShW,GACY,UAAxBA,EAAMiW,OAAOC,QACf1S,GAAU,GAEgB,aAAxBxD,EAAMiW,OAAOC,QACf1S,GAAU,GAEZuS,GAAQ/V,WAAQ,EAChBqV,GAAQrV,MAAQ,GAChBhC,GAAGgC,MAAMuG,UAAY,GACrBvI,GAAGgC,MAAMa,UAAY,EAAA,CAIjB,MAAAsV,GAAmBpW,GAAS,KAC1B,MAAAqW,EAAmB5X,GAAcwB,MAAMkU,MAAM3S,GAASA,EAAKE,OAASzD,GAAGgC,MAAMC,eAC5E,OAAAmW,EAAmBA,EAAiB3W,MAAQ,EAAA,IAGrDqC,eAAeuU,GAAY5X,GAEzB,MAAM2V,EAAkB/K,EAAM5K,EAAM2V,iBAAiB5K,OAAO,oBACtDe,EAAmBlB,EAAM5K,EAAM8L,kBAAkBf,OAAO,oBAGxD8M,EAAmB7X,EAAM8X,SAASjV,QAAQC,GAASA,EAAKiV,QAAU,IAExE,IAAInS,EAAY,GAChB,MAAMoS,EAAYH,EAAiBI,KAAKnV,IAClCA,EAAK8C,WAAa9C,EAAK8C,UAAUxB,OAAS,IAC5CwB,EAAY9C,EAAK8C,UAAUqS,KAAKpS,IAAmB,CACjDG,MAAOH,EAASG,MAChBC,IAAKJ,EAASI,IACdoG,IAAKxG,EAASwG,IACdnD,YAAarD,EAASqD,iBAI1B,MAAMgP,EAAYpV,EAAKoV,UAAUD,KAAKE,IAAmB,CACvD9B,UAAWzL,EAAMuN,EAAS9B,WAAWtL,OAAO,cAC5C0L,KAAM0B,EAAS1B,KACfN,MAAOgC,EAAShC,MAChBI,UAAW4B,EAAS5B,UACpBH,MAAO+B,EAAS/B,MAChBI,SAAU2B,EAAS3B,SACnB4B,UAAWD,EAASC,UACpB9B,kBAAmB6B,EAAS7B,sBAGvB,MAAA,CACL3V,OAAQmC,EAAKnC,OACboX,QAASjV,EAAKiV,QACdM,cAAevV,EAAKuV,eAAiB,IACrCzS,YACAsS,YACF,IAIII,EAAgB,CACpB3Y,MAAOF,GAAYE,MACnBC,MAAOH,GAAYG,MACnBgI,YAAarI,GAAGgC,MAAMqG,YACtBlH,OAAQnB,GAAGgC,MAAMb,OACjB6X,KAAMvY,EAAMuY,KACZ5C,kBACA7J,mBACA0M,QAAS,GAAGxY,EAAM2V,gBAAgB8C,MAAM,EAAG,OAAOzY,EAAM8L,iBAAiB2M,MAAM,EAAG,MAClFT,mBAII3P,GAAQqQ,YAAYJ,GAAe3V,MAAMC,IAC5B,IAAbA,EAAII,OACIuF,EAAAC,QAAQ1J,GAAE,4BACRmE,KACZrE,GAAM,WAAS,GAElB,CAGH,MAAM+Z,GAAoBxZ,EAAI,CAC5B6W,SAAS,EAETtV,OAAQ,GAER8X,QAAS,GAET3X,QAAS,GAETF,OAAQ,GAERiY,OAAQ,GAERC,QAAS,GAETC,KAAM,GAENnD,gBAAiB,GAEjB7J,iBAAkB,GAClBiM,QAAS,IAGX,SAASgB,GAAYjQ,GACnB7I,GAAQsB,OAAQ,EACEoX,GAAApX,MAAMuX,KAAO,GACbH,GAAApX,MAAMZ,OAASmI,EAAInI,OACnBgY,GAAApX,MAAMqX,OAAS9P,EAAI8P,OACnBD,GAAApX,MAAMV,QAAUiI,EAAIjI,QACtC8X,GAAkBpX,MAAMiX,QAAU,GAAG5N,EAAM9B,EAAI6M,iBAAiB5K,OAAO,iBAAiBH,EAAM9B,EAAIgD,kBAAkBf,OAAO,gBAC3H4N,GAAkBpX,MAAMwW,QAAU,EAClCY,GAAkBpX,MAAMoU,gBAAkB/K,EAAM9B,EAAI6M,iBAAiB5K,OAAO,oBAC5E4N,GAAkBpX,MAAMuK,iBAAmBlB,EAAM9B,EAAIgD,kBAAkBf,OAAO,oBAC1EjC,EAAI7C,KACN0S,GAAkBpX,MAAMuX,KAAKxR,KAAKwB,EAAI7C,KAExC0S,GAAkBpX,MAAMyU,SAAU,CAAA,CAIpC,SAASgD,GAAmB9Z,GACd+D,KACZrE,GAAM,UAAS,CAIjB,SAASqa,GAAcvS,GACf,MAAA5D,EAAOjD,GAAQ0B,MAAMkU,MAAM3S,GAAcA,EAAKE,OAAS0D,IACtD,OAAA5D,EAAOA,EAAK9B,MAAQ,EAAA,CA0D7B,SAASkY,KAEH5Y,GAAYiB,OACFjB,GAAAiB,MAAMjC,QAAQ6Z,cAGjB,IAAA,MAAAhK,KAAOhO,GAAUI,MAC1BJ,GAAUI,MAAM4N,GAAO3O,GAAW2O,GAEpC9O,GAAckB,MAAMnC,SAAU,EAC9BiB,GAAckB,MAAMyU,SAAU,CAAA,CAI1B,MAAAoD,GAAoBja,EAAuB,IAEjD,SAASka,KACP,MAAM7V,EAAS,IAAKrC,GAAUI,cACvBiC,EAAO5C,cACNyH,GAAAgR,YAAY7V,GAAQb,MAAK,EAAGzD,KAAAA,MAClCka,GAAkB7X,MAAQrC,EAC1BiC,GAAUI,MAAMX,cAAgB,EAAA,GACjC,CAGH,MAAM0Y,GAAgB5Z,EAA2B,CAC/C,CACEsB,MAAOlC,GAAE,eACTkG,KAAM,QACNuU,MAAO,gBACPC,QAAS,CACPta,KAAM,CACJ,CAAEqC,MAAO,EAAGP,MAAOlC,GAAE,iBAGvB2a,aAAc,CACZ,sBAAwBC,IACVL,IAAA,KAKpB,CACErY,MAAOlC,GAAE,mBACTya,MAAO,SACPvU,KAAM,SACN2U,MAAO,CAAC,CAAE1X,UAAU,EAAMC,QAASpD,GAAE,wCACrC0a,QAAS,CACPta,KAAMkC,GACNwY,SAAU,SACVC,SAAU,SACVC,cAAe,CACb,sBAAwBJ,IACVL,IAAA,KAKpB,CACErY,MAAOlC,GAAE,mBACTya,MAAO,gBACPQ,KAAM,mBAKD,SAAAC,GAAmBlR,EAAUmR,GACpC,GAAIA,EAEG7Z,GAAamB,MAAM8F,SAASyB,EAAIjI,UACtBT,GAAAmB,MAAM+F,KAAKwB,EAAIjI,cAK9B,GAAIiI,EAAIhD,WAAagD,EAAIhD,UAAU1B,OAAS,EAErChE,GAAamB,MAAM8F,SAASyB,EAAIjI,UACtBT,GAAAmB,MAAM+F,KAAKwB,EAAIjI,SAGpB0H,EAAA2R,KAAKpb,GAAE,8CACZ,CACL,MAAMoI,EAAQ9G,GAAamB,MAAMqM,QAAQ9E,EAAIjI,UAC3B,IAAdqG,GACW9G,GAAAmB,MAAMwN,OAAO7H,EAAO,EACnC,CAEJ,yhOA/lDF,SAAqBvB,GAE+B,IAA5BpG,GAAGgC,MAAM/B,QAAQ4E,OAE3BmE,EAAAS,QAAQlK,GAAE,sCAGtBuJ,GACG8R,oBAAoB,CACnBxa,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjBc,OAAQhC,GAAMgC,OAAO,GACrB8X,QAAS7S,EAAM6S,QACfzQ,SAAUxI,GAAGgC,MAAMwG,WAEpBpF,MAAMC,IACY,IAAbA,EAAII,OACMC,KACZrE,GAAM,WACI2J,EAAAC,QAAQ1J,GAAE,2BAAyB,GAEhD,0OAOL,SAA+B6G,GAC7B0C,GACG+R,iBAAiB,CAChBza,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjBc,OAAQhC,GAAMgC,OAAO,GACrB8X,QAAS7S,EAAM6S,UAEhB7V,MAAMC,IACY,IAAbA,EAAII,OACIuF,EAAAC,QAAQ1J,GAAE,gCACRmE,KAAA,GAEf,kHAWLI,eAA4BsC,GAEpB,MAAAyF,QAAsBxC,KAC5B,IAAKwC,EAEH,YADU7C,EAAA4C,MAAMrM,GAAE,iCAKpB,MAAMub,EAAczP,EAAMC,OAAOO,IAE3BuK,EAAkB/K,EAAMjF,EAAMgQ,iBAAiB2E,QAAQ,OACvDxO,EAAmBlB,EAAMjF,EAAMmG,kBAAkByO,MAAM,OAE7D,GAAIF,EAAYG,SAAS7E,IAAoB0E,EAAYI,QAAQ3O,GAE/D,YADUvD,EAAAS,QAAQlK,GAAE,wCAKhB,MAAA4b,EAAe/U,EAAMC,UAAU/C,QAAQ6S,GAASA,EAAKzP,KAAOyP,EAAK1O,QAAU2T,GAAWC,aAExF,GAAwB,IAAxBF,EAAatW,OAEf,YADUmE,EAAAS,QAAQlK,GAAE,8BAItB,IAAI+b,GAAiB,EACjBC,GAAiB,EACrB,MAAMC,EAA6B,GAC7BC,EAA6B,GAEnC,IAAA,MAAWtF,KAAQgF,EAEShF,EAAK5P,UAAUjD,QAAQoE,IAAcA,EAAST,OAClDpC,OAAS,GACZyW,GAAA,EACAE,EAAAzT,KAAKoO,EAAKzP,MAKHyP,EAAK5P,UAAUjD,QAAQoE,GAAaA,EAASP,SAAWC,GAAOC,SAAWK,EAASJ,OACvFzC,OAAS,IACZ0W,GAAA,EACAE,EAAA1T,KAAKoO,EAAKzP,MAK/B,GAAI4U,EAEF,YADUtS,EAAAS,QAAQlK,GAAE,iCAAkC,CAAEkB,MAAO+a,EAAiBE,KAAK,QAKvF,GAAIH,EAAgB,CACd,GAA6B,MAA7BnX,GAAYpC,MAAMsD,OAEpB,YADU0D,EAAAS,QAAQlK,GAAE,gCAAiC,CAAEkB,MAAOgb,EAAiBC,KAAK,cAI9E/M,EAAaC,QAAQrP,GAAE,uCAAwC,CAAEkB,MAAOgb,EAAiBC,KAAK,OAASnc,GAAE,gBAAiB,CAC9HsP,kBAAmBtP,GAAE,qBACrBuP,iBAAkBvP,GAAE,mBACpBkG,KAAM,YACLkG,OAAM,KACP,MAAM,IAAIgQ,MAAMpc,GAAE,gCAA+B,GAErD,CAIF,MAAM0E,EAAS,CACb7D,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjBc,OAAQhC,GAAMgC,OAAO,GACrB8X,QAAS7S,EAAM6S,QACf2C,UAAW7V,GAAY8V,GACvBC,OAAQ1V,EAAMC,UAAUqS,KAAKpS,IAAwB,CACnDhF,QAASgF,EAAShF,QAClBsV,MAAOtQ,EAASyV,YAAY,GAAGnF,MAC/BoF,QAAS1V,EAASC,UAAUmS,KAAKhR,IAC/B,MAAMuU,EAAc,CAClBhV,KAAMS,EAAST,KACfN,IAAKe,EAASf,KAAOC,GAAQC,QAAQC,WACrCC,OAAQW,EAASX,QAAU,GAC3BC,QAASU,EAASV,SAAW,GAC7BE,MAAOQ,EAASR,OAAS,GACzBC,OAAQO,EAASP,QAAUC,GAAOgK,MAClC9J,KAAMI,EAASJ,MAAQ,GACvBC,WAAYG,EAASH,YAAc,KAM9B,OAHHG,EAASlB,eACXyV,EAAOzV,aAAekB,EAASlB,cAE1ByV,CAAA,SAMbhW,GAASiW,YAAYjY,GAAQb,MAAMC,IAChB,IAAbA,EAAII,OACIuF,EAAAC,QAAQ1J,GAAE,iCACRmE,KACZrE,GAAM,WAAS,GAElB,23HA2zCH,SAA0BkK,GAExB,GAAIA,EAAI7C,IAGN,YADUsC,EAAAS,QAAQlK,GAAE,+CAGhB,MAAA4c,EAAe9Q,EAAM9B,EAAIgD,kBACzBuO,EAAczP,EAAM1K,GAAWqB,OACjC,GAAAma,EAAalB,SAASH,GACxB,OAAO9R,EAAUS,QAAQlK,GAAE,mCACtB,CACLuB,GAAckB,MAAMoa,OACpB,MAAMC,EAAaC,EAAsB,CAAC/S,GAAM,UAAUjG,QAAQC,IAAUA,EAAKmD,OAAQ,GAC7E7E,GAAAG,MAAQua,EAAwBF,EAAY,eAC9Cza,GAAAI,MAAMZ,OAASmI,EAAInI,OACnBQ,GAAAI,MAAMV,QAAUiI,EAAIjI,QAClBwY,IAAA,CACd,+eAvZkBvQ,QACHiN,GAAAxU,MAAMZ,OAASmI,EAAInI,OACnBoV,GAAAxU,MAAMV,QAAUiI,EAAIjI,QACpBkV,GAAAxU,MAAM0U,UAAY8F,GAAOC,MACzBjG,GAAAxU,MAAM2U,iBAAmBpN,EAAIwS,iBAC5CvF,GAAexU,MAAMyU,SAAU,GALjC,IAAoBlN,glGAepBzF,eAAuB8J,GACjB,IAACA,EAAMrE,IAAI7C,IAEb,YADUsC,EAAAS,QAAQlK,GAAE,sCAKhB,MAAAsM,QAAsBxC,KAC5B,IAAKwC,EAEH,YADU7C,EAAA4C,MAAMrM,GAAE,iCAKpB,MAAMub,EAAczP,EAAMC,OAAOO,IAE3BuK,EAAkB/K,EAAMuC,EAAMrE,IAAI6M,iBAAiB2E,QAAQ,OAC3DxO,EAAmBlB,EAAMuC,EAAMrE,IAAIgD,kBAAkByO,MAAM,OAEjE,GAAIF,EAAYG,SAAS7E,IAAoB0E,EAAYI,QAAQ3O,GAE/D,YADUvD,EAAAS,QAAQlK,GAAE,wCAKhB,MAAAmd,EAAoB9O,EAAMrE,IAAIhD,UAAUjD,QAAQoE,IAAcA,EAAST,OACzE,GAAAyV,EAAkB7X,OAAS,EAE7B,YADAmE,EAAUS,QAAQlK,GAAE,kCAAmC,CAAE8M,OAAQuB,EAAMrE,IAAI7C,IAAKiW,MAAOD,EAAkB7X,UAK3G,MAAM+X,EAAoBhP,EAAMrE,IAAIhD,UAAUjD,QAAQoE,GAAaA,EAASP,SAAWC,GAAOC,SAAWK,EAASJ,OAE9G,GAAAsV,EAAkB/X,OAAS,EAAG,CAC5B,GAA6B,MAA7BT,GAAYpC,MAAMsD,OAEpB,YADA0D,EAAUS,QAAQlK,GAAE,iCAAkC,CAAE8M,OAAQuB,EAAMrE,IAAI7C,IAAKiW,MAAOC,EAAkB/X,UAIpG,UACI8J,EAAaC,QAAQrP,GAAE,kCAAmC,CAAE8M,OAAQuB,EAAMrE,IAAI7C,IAAKiW,MAAOC,EAAkB/X,SAAWtF,GAAE,gBAAiB,CAC9IsP,kBAAmBtP,GAAE,qBACrBuP,iBAAkBvP,GAAE,mBACpBkG,KAAM,kBAEDmG,GAEP,MAAA,CAEJ,CAGF,MAAM3H,EAAS,CACb7D,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjBc,OAAQhC,GAAMgC,OAAO,GACrB8X,QAAS,GAAG5N,EAAMuC,EAAMrE,IAAI6M,iBAAiB5K,OAAO,iBAAiBH,EAAMuC,EAAMrE,IAAIgD,kBAAkBf,OAAO,gBAC9GoQ,UAAW7V,GAAY8V,GACvBC,OAAQ,CACN,CACExa,QAASsM,EAAMrE,IAAIjI,QACnBsV,MAAOhJ,EAAMrE,IAAIwS,YAAY,GAAGnF,MAChCoF,QAASpO,EAAMrE,IAAIhD,UAAUmS,KAAKhR,IAChC,MAAMuU,EAAc,CAClBhV,KAAMS,EAAST,KACfN,IAAKe,EAASf,KAAOC,GAAQC,QAAQC,WACrCC,OAAQW,EAASX,QAAU,GAC3BC,QAASU,EAASV,SAAW,GAC7BE,MAAOQ,EAASR,OAAS,GACzBC,OAAQO,EAASP,QAAUC,GAAOC,OAClCC,KAAMI,EAASJ,MAAQ,GACvBC,WAAYG,EAASH,YAAc,KAM9B,OAHHG,EAASlB,eACXyV,EAAOzV,aAAekB,EAASlB,cAE1ByV,CAAA,OAKfhW,GAASiW,YAAYjY,GAAQb,MAAMC,IAChB,IAAbA,EAAII,OACIuF,EAAAC,QAAQ1J,GAAE,4BACRmE,KACZrE,GAAM,WAAS,GAElB,iHAIH,SAAkBuO,GAEhB,IAAA,IAASU,EAAI,EAAGA,EAAItO,GAAGgC,MAAM/B,QAAQ4E,OAAQyJ,IAAK,CAChD,MAAMlI,EAAQpG,GAAGgC,MAAM/B,QAAQqO,GAC/B,IAAA,IAASC,EAAI,EAAGA,EAAInI,EAAMC,UAAUxB,OAAQ0J,IAAK,CACzC,MAAAjI,EAAWF,EAAMC,UAAUkI,GACjC,GAAIjI,EAAShF,UAAYsM,EAAMrE,IAAIjI,QAAS,CAE1C,MAAMub,EAAc,CAClBrW,aAAc,GACdC,MAAO,GACPC,IAAK,GACLC,IAAKC,GAAQC,QAAQC,WACrBC,OAAQ,GACRC,QAAS,GACTC,KAAM,GACNC,MAAO,GACPC,OAAQC,GAAOC,OACfC,KAAM,GACNC,WAAY,IACZC,OAAQ,IACRC,MAAO,IAEAnB,EAAAC,UAAUwB,KAAK8U,GAGlB,MAAAjK,EAAgBtM,EAASC,UAAU1B,OAAS,EAC5C+C,EAAc,GAAGtB,EAAShF,eAAesR,IAClC/K,GAAA7F,MAAM4F,GAAe,CAChCX,KAAM,GACNC,MAAO,GACPI,KAAM,GACNH,OAAQC,GAAOC,QAIZxG,GAAamB,MAAM8F,SAASxB,EAAShF,UAC3BT,GAAAmB,MAAM+F,KAAKzB,EAAShF,QACnC,CACF,CACF,CACF,0NAIF,SAAoBiI,GAElB,IAAIuT,EAAa,EACjB9c,GAAGgC,MAAM/B,QAAQkG,SAASC,IACxB0W,GAAc1W,EAAMC,UAAUxB,MAAA,IAIb,IAAfiY,EAIJhU,GACGiU,eAAe,CACd3c,MAAOX,GAAUW,MACjBC,MAAOZ,GAAUY,MACjBiB,QAASiI,EAAIjI,UAEd8B,MAAMC,IACY,IAAbA,EAAII,OACIuF,EAAAC,QAAQ1J,GAAE,+BACRmE,KACZrE,GAAM,WAAS,IAbT2J,EAAAS,QAAQlK,GAAE,mCAenB,sPAyML,SAAsBgK,GAEd,MAAA7B,EAAW6B,EAAIhD,UAAU2P,MAAM3S,GAAcA,EAAKiE,SAAWzB,GAAYC,MAC/E3G,GAAM,cAAe,CACnBiC,QAASiI,EAAIjI,QACbmG,MAAO8B,EAAI9B,MACXjB,aAAckB,EAASlB,cACxB,22CAgCIzF,GAAYiB,MAAMjC,SAGvBgB,GAAYiB,MAAMjC,QAAQoI,UAASrE,MAAOsE,IACpCA,GAEe,WADCU,GAAQkU,WAAWpb,GAAUI,QACvCyB,OACIuF,EAAAC,QAAQ1J,GAAE,gCACRmE,KACFiW,KACZ"}