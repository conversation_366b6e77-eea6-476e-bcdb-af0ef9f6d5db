import{a as i}from"./index-CkEhI1Zk.js";const t={list:t=>i.get("/admin-api/pms/indemnity-goods/list",{params:t}),changeStatus:t=>i.put("/admin-api/pms/indemnity-goods/update-status",t),create:t=>i.post("/admin-api/pms/indemnity-goods/create",t),edit:t=>i.put("/admin-api/pms/indemnity-goods/update",t),detail:t=>i.get("/admin-api/pms/indemnity-goods/get",{params:t}),delete:t=>i.delete("/admin-api/pms/indemnity-goods/delete",{params:t})};export{t as i};
//# sourceMappingURL=indemnityGoods.api-BzuE6zcC.js.map
