import{d as e,ai as a,aj as t,B as l,b as s,y as i,o as n,e as o,w as r,g as d,f as c,u,h as m,Y as h,R as p,i as b,c as f,F as v,ag as g,al as k,aR as y,aS as _,m as w,x as V,b7 as j,b8 as I,n as E,t as P,p as A,v as R,q as x,ay as S}from"./index-CkEhI1Zk.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css                          *//* empty css               *//* empty css                    *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{_ as C}from"./_plugin-vue_export-helper-BCo6x5W8.js";const D={class:"dialog-footer"},N=e({__name:"permissionMode",props:{modelValue:{type:Boolean,default:!1},roleId:{default:0},name:{default:""},type:{default:""}},emits:["update:modelValue","success"],setup(e,{emit:C}){const N=e,U=C,q=a(),{t:B}=t(),M=l({get:()=>N.modelValue,set(e){U("update:modelValue",e)}}),T=s(),z=s({name:"",remark:"",code:"",sort:0}),F=s({name:[{required:!0,message:B("pleaseEnterRoleName"),trigger:"blur"}]}),O=s([]),Y=s(""),G=s([]),H=s([]);async function J(e){e.authData&&(e.checked=e.checkAll?e.authData.map((e=>e.id)):[]),e.isIndeterminate=!1,e.children&&await K(e.children,e.checkAll),L(H.value)}function K(e,a){e.forEach((async e=>{e.children&&e.children.length>0&&await K(e.children,a),e.checkAll=a,e.isIndeterminate=!1,e.authData&&(e.checked=e.checkAll?e.authData.map((e=>e.id)):[])}))}function L(e){e.forEach((e=>{if(e.children&&Array.isArray(e.children)&&e.children.length>0&&L(e.children),[1,2].includes(e.type))if(e.authData)e.checkAll=e.checked.length===e.authData.length,e.isIndeterminate=e.checked.length>0&&e.checked.length<e.authData.length;else if(Array.isArray(e.children)&&e.children.length>0){const a=e.children.every((e=>e.checkAll)),t=e.children.some((e=>e.isIndeterminate||e.checkAll));e.checkAll=a,e.isIndeterminate=!a&&t}}))}function Q(){M.value=!1}const W=s(!1);function X(){O.value=[],G.value.forEach((e=>{e.children&&(L(e.children),Z(e.children))})),"0"===N.type?k.updatePermission({roleId:N.roleId,menuIds:O.value}).then((()=>{y.success(B("editSuccess")),Q(),U("success")})):"1"===N.type&&T.value&&T.value.validate((e=>{e&&(W.value=!0,k.createRole({...z.value,gcode:q.gcode}).then((e=>{k.updatePermission({roleId:e.data,menuIds:O.value}).then((()=>{W.value=!1,y.success(B("copySuccess")),Q(),U("success")}))})))}))}function Z(e){e.forEach((e=>{e.children&&e.children.length>0&&Z(e.children),e.authData&&(e.checked.length>0||e.isIndeterminate)?O.value=[...new Set([...O.value,...e.checked,e.id])]:e.children&&(e.children.some((e=>e.checkAll))||e.children.some((e=>e.isIndeterminate)))&&(O.value=[...new Set([...O.value,e.id])])}))}function $(){const e=G.value.find((e=>e.id===Y.value));e&&L(e.children);const a=G.value.find((e=>e.id===Y.value));a&&(H.value=a.children,L(H.value))}async function ee(){await k.permissionTree({status:"0",roleId:N.roleId}).then((e=>{0===e.code&&(G.value=e.data,ae(G.value),G.value.forEach((e=>{e.children&&L(e.children)})),Y.value=G.value[0].id,H.value=G.value[0].children)}))}function ae(e){e.forEach((e=>{e.children&&e.checked&&(e.authData=e.children,e.children=null),e.children&&e.children.length>0&&ae(e.children)}))}return i((()=>{ee(),"0"===N.type&&(z.value.name=N.name)})),(e,a)=>{const t=_,l=w,s=V,i=j,k=I,y=E,C=P,U=A,q=R,O=x,K=S;return n(),o(K,{modelValue:u(M),"onUpdate:modelValue":a[3]||(a[3]=e=>b(M)?M.value=e:null),title:"0"===N.type?u(B)("configPermissions"):u(B)("copyPermissions"),width:"70vw","align-center":""},{footer:r((()=>[d("div",D,[c(O,{loading:u(W),onClick:Q},{default:r((()=>[m(h(u(B)("cancle")),1)])),_:1},8,["loading"]),c(O,{loading:u(W),type:"primary",onClick:X},{default:r((()=>[m(h(u(B)("save")),1)])),_:1},8,["loading"])])])),default:r((()=>[d("div",null,[c(s,{ref_key:"formRef",ref:T,inline:!1,model:u(z),rules:u(F),"label-width":"100px","inline-message":!0},{default:r((()=>[c(l,{label:u(B)("roleName"),prop:"name",style:{width:"50%"}},{default:r((()=>[c(t,{modelValue:u(z).name,"onUpdate:modelValue":a[0]||(a[0]=e=>u(z).name=e),disabled:"0"===N.type,placeholder:u(B)("pleaseEnterRoleName")},null,8,["modelValue","disabled","placeholder"])])),_:1},8,["label"]),"1"===N.type?(n(),o(l,{key:0,label:u(B)("remark"),style:{width:"50%"}},{default:r((()=>[c(t,{modelValue:u(z).remark,"onUpdate:modelValue":a[1]||(a[1]=e=>u(z).remark=e),type:"textarea",rows:3,maxlength:"250",placeholder:u(B)("pleaseEnterRemark")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])):p("",!0)])),_:1},8,["model","rules"]),c(k,{modelValue:u(Y),"onUpdate:modelValue":a[2]||(a[2]=e=>b(Y)?Y.value=e:null),class:"demo-tabs",onTabChange:$},{default:r((()=>[(n(!0),f(v,null,g(u(G),(e=>(n(),o(i,{key:e.id,label:e.name,name:e.id},null,8,["label","name"])))),128))])),_:1},8,["modelValue"]),c(q,{data:u(H),style:{width:"100%","margin-bottom":"20px"},"row-key":"id","max-height":"68vh","default-expand-all":"",border:""},{default:r((()=>[c(C,{prop:"date",width:"300px",label:u(B)("module")},{default:r((e=>[c(y,{modelValue:e.row.checkAll,"onUpdate:modelValue":a=>e.row.checkAll=a,indeterminate:e.row.isIndeterminate,onChange:a=>J(e.row)},{default:r((()=>[m(h(e.row.name),1)])),_:2},1032,["modelValue","onUpdate:modelValue","indeterminate","onChange"])])),_:1},8,["label"]),c(C,{prop:"name",label:u(B)("permissions")},{default:r((e=>[c(U,{modelValue:e.row.checked,"onUpdate:modelValue":a=>e.row.checked=a},{default:r((()=>[(n(!0),f(v,null,g(e.row.authData,((a,t)=>(n(),o(y,{key:t,value:a.id,onChange:a=>(e.row,void L(H.value))},{default:r((()=>[m(h(a.name),1)])),_:2},1032,["value","onChange"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1},8,["label"])])),_:1},8,["data"])])])),_:1},8,["modelValue","title"])}}});function U(e){const a=e;a.__i18n=a.__i18n||[],a.__i18n.push({locale:"",resource:{"zh-cn":{configPermissions:{t:0,b:{t:2,i:[{t:3}],s:"配置权限"}},copyPermissions:{t:0,b:{t:2,i:[{t:3}],s:"复制权限"}},roleName:{t:0,b:{t:2,i:[{t:3}],s:"角色名称"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},pleaseEnterRoleName:{t:0,b:{t:2,i:[{t:3}],s:"请输入角色名称"}},pleaseEnterRemark:{t:0,b:{t:2,i:[{t:3}],s:"请输入备注"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"编辑成功"}},copySuccess:{t:0,b:{t:2,i:[{t:3}],s:"复制成功"}},module:{t:0,b:{t:2,i:[{t:3}],s:"模块"}},permissions:{t:0,b:{t:2,i:[{t:3}],s:"权限"}},cancle:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},save:{t:0,b:{t:2,i:[{t:3}],s:"确定"}}},en:{configPermissions:{t:0,b:{t:2,i:[{t:3}],s:"Configure Permissions"}},copyPermissions:{t:0,b:{t:2,i:[{t:3}],s:"Copy Permissions"}},roleName:{t:0,b:{t:2,i:[{t:3}],s:"Role Name"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},pleaseEnterRoleName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter role name"}},pleaseEnterRemark:{t:0,b:{t:2,i:[{t:3}],s:"Please enter remark"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Edit successful"}},copySuccess:{t:0,b:{t:2,i:[{t:3}],s:"Copy successful"}},module:{t:0,b:{t:2,i:[{t:3}],s:"Module"}},permissions:{t:0,b:{t:2,i:[{t:3}],s:"Permissions"}},cancle:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}}},km:{configPermissions:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់រចនាសម្ព័ន្ធសិទ្ធិ"}},copyPermissions:{t:0,b:{t:2,i:[{t:3}],s:"ចម្លងសិទ្ធិ"}},roleName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះតួនាទី"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់សម្គាល់"}},pleaseEnterRoleName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះតួនាទី"}},pleaseEnterRemark:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលកំណត់សម្គាល់"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"កែប្រែដោយជោគជ័យ"}},copySuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចម្លងដោយជោគជ័យ"}},module:{t:0,b:{t:2,i:[{t:3}],s:"ម៉ូឌុល"}},permissions:{t:0,b:{t:2,i:[{t:3}],s:"សិទ្ធិ"}},cancle:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},save:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ជាក់"}}}}})}U(N);const q=C(N,[["__scopeId","data-v-d5667af6"]]);export{q as default};
//# sourceMappingURL=permissionMode-CTUYl1L_.js.map
