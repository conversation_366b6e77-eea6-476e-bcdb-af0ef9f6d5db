{"version": 3, "file": "createUnitAgent-s5xsgvmO.js", "sources": ["../../src/views/customer-manage/unit-agent/components/DetailForm/createUnitAgent.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"pleaseEnterName\": \"Please enter name\",\r\n    \"pleaseSelectCustomerLevel\": \"Please select client level\",\r\n    \"addedSuccessfully\": \"Added successfully\",\r\n    \"type\": \"Type\",\r\n    \"channel\": \"Channel\",\r\n    \"selectChannel\": \"Select channel\",\r\n    \"ownershipType\": \"Belonging Type\",\r\n    \"belongingStore\": \"Belonging Hotel\",\r\n    \"selectStore\": \"Select hotel\",\r\n    \"allowCredit\": \"Allow Credit\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"hideRoomPriceOnRegistration\": \"Hide Price\",\r\n    \"companyInformation\": \"Company Info\",\r\n    \"agreementUnit\": \"Protocol Unit\",\r\n    \"agency\": \"Agent\",\r\n    \"singleStoreApplicable\": \"Single hotel\",\r\n    \"groupShare\": \"Group Share\",\r\n    \"name\": \"Name\",\r\n    \"shortName\": \"Hotel Name\",\r\n    \"legalPerson\": \"Legal Person\",\r\n    \"telephone\": \"Telephone\",\r\n    \"address\": \"Address\",\r\n    \"startDate\": \"Start Date\",\r\n    \"selectDate\": \"Select date\",\r\n    \"endDate\": \"End Date\",\r\n    \"salesperson\": \"Sales Person\",\r\n    \"status\": \"Status\",\r\n    \"valid\": \"Valid\",\r\n    \"invalid\": \"Invalid\",\r\n    \"customerLevel\": \"Customer Level\",\r\n    \"commissionLevel\": \"Commission Level\",\r\n    \"remark\": \"Remark\",\r\n    \"contactPerson\": \"Contact Person\",\r\n    \"email\": \"Email\",\r\n    \"creditSettings\": \"Credit Settings\",\r\n    \"accountSetName\": \"AR Name\",\r\n    \"companyName\": \"Company Name\",\r\n    \"accountType\": \"Account Type\",\r\n    \"creditAccount\": \"Credit Account\",\r\n    \"prepaidAccount\": \"Prepaid Account\",\r\n    \"settlementPeriod\": \"Settlement Period\",\r\n    \"permanentPeriod\": \"Permanent Period\",\r\n    \"fixedPeriod\": \"Fixed Period\",\r\n    \"pleaseEnterFixedPeriod\": \"Please enter fixed period\",\r\n    \"maximumLimit\": \"Maximum Limit\",\r\n    \"unlimitedLimit\": \"Unlimited Limit\",\r\n    \"limitedLimit\": \"Limited Limit\",\r\n    \"pleaseEnterMaximumLimit\": \"Please enter maximum limit\",\r\n    \"validityPeriod\": \"Validity Period\",\r\n    \"permanentlyValid\": \"Permanently Valid\",\r\n    \"fixedValidity\": \"Fixed Validity\",\r\n    \"to\": \"To\",\r\n    \"allowNegativeBalance\": \"Allow Negative\",\r\n    \"hotelScope\": \"Hotel Scope\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"pleaseEnterName\": \"请输入名称\",\r\n    \"pleaseSelectCustomerLevel\": \"请选择客户级别\",\r\n    \"addedSuccessfully\": \"新增成功\",\r\n    \"type\": \"类型\",\r\n    \"channel\": \"渠道\",\r\n    \"selectChannel\": \"选择渠道\",\r\n    \"ownershipType\": \"所属类型\",\r\n    \"belongingStore\": \"所属门店\",\r\n    \"selectStore\": \"选择门店\",\r\n    \"allowCredit\": \"允许挂账\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"hideRoomPriceOnRegistration\": \"登记单隐藏房价\",\r\n    \"companyInformation\": \"公司信息\",\r\n    \"agreementUnit\": \"协议单位\",\r\n    \"agency\": \"中介\",\r\n    \"singleStoreApplicable\": \"单店适用\",\r\n    \"groupShare\": \"集团共享\",\r\n    \"name\": \"名称\",\r\n    \"shortName\": \"简称\",\r\n    \"legalPerson\": \"法人\",\r\n    \"telephone\": \"电话\",\r\n    \"address\": \"地址\",\r\n    \"startDate\": \"开始日期\",\r\n    \"selectDate\": \"选择日期\",\r\n    \"endDate\": \"结束日期\",\r\n    \"salesperson\": \"销售人员\",\r\n    \"status\": \"状态\",\r\n    \"valid\": \"有效\",\r\n    \"invalid\": \"无效\",\r\n    \"customerLevel\": \"客户级别\",\r\n    \"commissionLevel\": \"佣金等级\",\r\n    \"remark\": \"备注\",\r\n    \"contactPerson\": \"联系人\",\r\n    \"email\": \"Email\",\r\n    \"creditSettings\": \"挂账设置\",\r\n    \"accountSetName\": \"账套名称\",\r\n    \"companyName\": \"公司名称\",\r\n    \"accountType\": \"账户类型\",\r\n    \"creditAccount\": \"信用账户\",\r\n    \"prepaidAccount\": \"预付账户\",\r\n    \"settlementPeriod\": \"结算账期\",\r\n    \"permanentPeriod\": \"永久账期\",\r\n    \"fixedPeriod\": \"固定账期\",\r\n    \"pleaseEnterFixedPeriod\": \"请输入固定账期\",\r\n    \"maximumLimit\": \"最大额度\",\r\n    \"unlimitedLimit\": \"不限额度\",\r\n    \"limitedLimit\": \"限制额度\",\r\n    \"pleaseEnterMaximumLimit\": \"输入最大限额\",\r\n    \"validityPeriod\": \"有效时间\",\r\n    \"permanentlyValid\": \"永久有效\",\r\n    \"fixedValidity\": \"固定有效\",\r\n    \"to\": \"至\",\r\n    \"allowNegativeBalance\": \"允许负账\",\r\n    \"hotelScope\": \"酒店范围\"\r\n  },\r\n  \"km\": {\r\n  \"pleaseEnterName\": \"សូមបញ្ចូលឈ្មោះ\",\r\n  \"pleaseSelectCustomerLevel\": \"សូមជ្រើសរើសកម្រិតអតិថិជន\",\r\n  \"addedSuccessfully\": \"បានបន្ថែមដោយជោគជ័យ\",\r\n  \"type\": \"ប្រភេទ\",\r\n  \"channel\": \"ឆានែល\",\r\n  \"selectChannel\": \"ជ្រើសរើសឆានែល\",\r\n  \"ownershipType\": \"ប្រភេទកម្មសិទ្ធិ\",\r\n  \"belongingStore\": \"សណ្ឋាគារដែលជាកម្មសិទ្ធិ\",\r\n  \"selectStore\": \"ជ្រើសរើសសណ្ឋាគារ\",\r\n  \"allowCredit\": \"អនុញ្ញាតឱ្យឥណទាន\",\r\n  \"yes\": \"បាទ/ចាស\",\r\n  \"no\": \"ទេ\",\r\n  \"hideRoomPriceOnRegistration\": \"លាក់តម្លៃបន្ទប់\",\r\n  \"companyInformation\": \"ព័ត៌មានក្រុមហ៊ុន\",\r\n  \"agreementUnit\": \"អង្គភាពកិច្ចព្រមព្រៀង\",\r\n  \"agency\": \"ភ្នាក់ងារ\",\r\n  \"singleStoreApplicable\": \"សណ្ឋាគារតែមួយ\",\r\n  \"groupShare\": \"ការចែករំលែកក្រុម\",\r\n  \"name\": \"ឈ្មោះ\",\r\n  \"shortName\": \"ឈ្មោះសណ្ឋាគារ\",\r\n  \"legalPerson\": \"ជនផ្លូវការ\",\r\n  \"telephone\": \"ទូរស័ព្ទ\",\r\n  \"address\": \"អាសយដ្ឋាន\",\r\n  \"startDate\": \"កាលបរិច្ឆេទចាប់ផ្តើម\",\r\n  \"selectDate\": \"ជ្រើសរើសកាលបរិច្ឆេទ\",\r\n  \"endDate\": \"កាលបរិច្ឆេទបញ្ចប់\",\r\n  \"salesperson\": \"អ្នកលក់\",\r\n  \"status\": \"ស្ថានភាព\",\r\n  \"valid\": \"មានសុពលភាព\",\r\n  \"invalid\": \"គ្មានសុពលភាព\",\r\n  \"customerLevel\": \"កម្រិតអតិថិជន\",\r\n  \"commissionLevel\": \"កម្រិតគណនី\",\r\n  \"remark\": \"ចំណាំ\",\r\n  \"contactPerson\": \"អ្នកទំនាក់ទំនង\",\r\n  \"email\": \"អ៊ីមែល\",\r\n  \"creditSettings\": \"ការកំណត់ឥណទាន\",\r\n  \"accountSetName\": \"ឈ្មោះគណនី\",\r\n  \"companyName\": \"ឈ្មោះក្រុមហ៊ុន\",\r\n  \"accountType\": \"ប្រភេទគណនី\",\r\n  \"creditAccount\": \"គណនីឥណទាន\",\r\n  \"prepaidAccount\": \"គណនីបង់មុន\",\r\n  \"settlementPeriod\": \"រយៈពេលសំណង\",\r\n  \"permanentPeriod\": \"រយៈពេលអចិន្ត្រៃយ៍\",\r\n  \"fixedPeriod\": \"រយៈពេលថេរ\",\r\n  \"pleaseEnterFixedPeriod\": \"សូមបញ្ចូលរយៈពេលថេរ\",\r\n  \"maximumLimit\": \"ដែនកំណត់អតិបរមា\",\r\n  \"unlimitedLimit\": \"គ្មានដែនកំណត់\",\r\n  \"limitedLimit\": \"ដែនកំណត់\",\r\n  \"pleaseEnterMaximumLimit\": \"សូមបញ្ចូលដែនកំណត់អតិបរមា\",\r\n  \"validityPeriod\": \"រយៈពេលសុពលភាព\",\r\n  \"permanentlyValid\": \"មានសុពលភាពអចិន្ត្រៃយ៍\",\r\n  \"fixedValidity\": \"សុពលភាពថេរ\",\r\n  \"to\": \"ដល់\",\r\n  \"allowNegativeBalance\": \"អនុញ្ញាតឱ្យអវិជ្ជមាន\",\r\n  \"hotelScope\": \"វិសាលភាពសណ្ឋាគារ\"\r\n}\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { MerchantModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { channelApi, customerApi, generalConfigApi, merchantApi, userApi } from '@/api/modules/index'\r\nimport { BooleanEnum, DictTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport ymdate from '@/utils/timeutils'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  paCode: '',\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 所属酒店代码 */\r\n  belongHcode: '',\r\n  /** 所属酒店名称 */\r\n  belongHname: '',\r\n  /** 中介/协议单位代码 */\r\n  paCode: '',\r\n  /** 中介/协议单位名称 */\r\n  paName: '',\r\n  /** 简称 */\r\n  shortName: '',\r\n  /** 法人 */\r\n  legalPerson: '',\r\n  /** 电话 */\r\n  telephone: '',\r\n  /** 地址 */\r\n  address: '',\r\n  /** 类型 */\r\n  paType: '1',\r\n  /** 渠道分类 */\r\n  channelType: 'ZJ',\r\n  /** 渠道 */\r\n  channel: '',\r\n  /** 是否共享 */\r\n  isShare: '0',\r\n  /** 联系人姓名 */\r\n  contact: '',\r\n  /** 联系人电话 */\r\n  phone: '',\r\n  /** 联系人email */\r\n  email: '',\r\n  /** 销售员 */\r\n  seller: '',\r\n  /** 开始日期 */\r\n  startDate: new Date().toString(),\r\n  /** 结束日期 */\r\n  endDate: dayjs().add(1, 'year').toString(),\r\n  /** 佣金策略 */\r\n  commissionStrategy: '',\r\n  /** 状态 0：无效 1：有效 */\r\n  isEnable: '1',\r\n  /** 登记单隐藏房价 0：否 1：是 */\r\n  isHidePrice: BooleanEnum.NO,\r\n  /** 是否允许挂账 0：否 1：是 */\r\n  isCredit: BooleanEnum.NO,\r\n  /** 销售等级 */\r\n  sellLevel: '',\r\n  /**  佣金等级 */\r\n  commissionLevel: '',\r\n  /** 备注 */\r\n  remark: '',\r\n  arSet: {\r\n    /** id */\r\n    id: '',\r\n    /** 集团代码 */\r\n    gcode: userStore.gcode,\r\n    /** 账套代码 */\r\n    arSetCode: '',\r\n    /** 账套名称 */\r\n    arSetName: '',\r\n    /** 单位或中介代码、团队/旅行社代码 */\r\n    unitCode: '',\r\n    /** 单位或中介、团队/旅行社名称 */\r\n    unitName: '',\r\n    /** 挂账账户名称 */\r\n    creditAccName: '',\r\n    /** 挂账账户类型;0：信用账户 1：预付账户 */\r\n    creditAccType: '0',\r\n    /** 挂账:结算账期;0：固定账期 1：永久账期；credit_acc_type=0需要设置； */\r\n    creditPayDays: '1',\r\n    /** 挂账:固定账期值;credit_acc_type=0需要设置；credit_pay_days=0，该字段有值 */\r\n    creditPayFix: 0,\r\n    /** 挂账最大限额类型;credit_acc_type=0需要设置；0：不限额度 1：限制额度 */\r\n    creditQuotaType: BooleanEnum.NO,\r\n    /** 挂账:最大限额;credit_acc_type=0需要设置；credit_quota_type=1，该字段需要设置最大额度 */\r\n    creditQuota: 0,\r\n    /** 挂账:有效时间类型;credit_acc_type=0需要设置；0：永久有效 1：固定有效 */\r\n    creditValidType: BooleanEnum.NO,\r\n    /** 挂账：有效时间开始;credit_acc_type=0需要设置；credit_valid_type=1,需要设置 */\r\n    creditStartDate: '',\r\n    /** 挂账：有效时间结束;credit_acc_type=0需要设置；credit_valid_type=1,需要设置 */\r\n    creditEndDate: '',\r\n    /** 挂账：允许负账;0：不允许 1：允许 */\r\n    creditMinusAcc: BooleanEnum.NO,\r\n    /** 状态;0: 无效 1：有效 */\r\n    isEnable: BooleanEnum.YES,\r\n    /** 应收账套关联门店列表 */\r\n    arSetMerchants: [],\r\n    /** 手动添加账套; 0 :手动 1: 自动 */\r\n    isManual: '',\r\n  },\r\n})\r\n\r\n/** 渠道列表 */\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\n/** 门店列表 */\r\nconst merchants = ref<MerchantModel[]>([])\r\n\r\nconst formRules = ref<FormRules>({\r\n  paName: [{ required: true, message: t('pleaseEnterName'), trigger: 'blur' }],\r\n  sellLevel: [\r\n    {\r\n      required: true,\r\n      message: t('pleaseSelectCustomerLevel'),\r\n      trigger: 'blur',\r\n    },\r\n  ],\r\n})\r\n\r\nonMounted(() => {\r\n  getChannels()\r\n  getMerchants()\r\n  getSeller()\r\n  getLevel()\r\n  getBrokerageLevel()\r\n})\r\n\r\n// 获取客户级别\r\nconst levelList = ref<{ code: string; name: string }[]>([])\r\nconst typeLevel = ref('')\r\nfunction getLevel() {\r\n  typeLevel.value = form.value.paType === '0' ? DictTypeEnum.PROTOCOL_LEVEL : DictTypeEnum.AGENT_LEVEL\r\n  generalConfigApi.list({ gcode: userStore.gcode, type: typeLevel.value }).then((res: any) => {\r\n    levelList.value = res.data\r\n  })\r\n}\r\n// 获取等级\r\nconst brokerageLevelList = ref<{ code: string; name: string }[]>([])\r\nfunction getBrokerageLevel() {\r\n  typeLevel.value = DictTypeEnum.BROKERAGE_LEVEL\r\n  generalConfigApi.list({ gcode: userStore.gcode, type: typeLevel.value }).then((res: any) => {\r\n    brokerageLevelList.value = res.data\r\n  })\r\n}\r\n\r\n// 获取销售员列表\r\nconst sellers = ref<{ username: string; nickname: string }[]>([])\r\n\r\nfunction getSeller() {\r\n  userApi.listSeller({ gcode: userStore.gcode }).then((res: any) => {\r\n    sellers.value = res.data\r\n  })\r\n}\r\n\r\n/** 获取渠道列表 */\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    isEnable: BooleanEnum.YES,\r\n    isG: BooleanEnum.YES,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取门店列表 */\r\nfunction getMerchants() {\r\n  merchantApi.getSimpleUnitList(userStore.gcode, userStore.hcode).then((res: any) => {\r\n    if (res.code === 0) {\r\n      merchants.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            form.value.startDate = ymdate(form.value.startDate)\r\n            form.value.endDate = ymdate(form.value.endDate)\r\n            form.value.arSet.creditStartDate = form.value.arSet.creditStartDate ? ymdate(form.value.arSet.creditStartDate) : ''\r\n            form.value.arSet.creditEndDate = form.value.arSet.creditEndDate ? ymdate(form.value.arSet.creditEndDate) : ''\r\n            customerApi.create(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('addedSuccessfully'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n\r\nfunction paTypeChange() {\r\n  form.value.sellLevel = ''\r\n  getLevel()\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\" style=\"height: 650px\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"180px\" label-suffix=\"：\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('type')\">\r\n            <el-radio-group v-model=\"form.paType\" @change=\"paTypeChange\">\r\n              <el-radio value=\"0\">\r\n                {{ t('agreementUnit') }}\r\n              </el-radio>\r\n              <el-radio value=\"1\">\r\n                {{ t('agency') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item v-if=\"form.paType === '1'\" :label=\"t('channel')\">\r\n            <el-select v-model=\"form.channel\" :placeholder=\"t('selectChannel')\">\r\n              <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('ownershipType')\">\r\n            <el-radio-group v-model=\"form.isShare\">\r\n              <el-radio value=\"0\">\r\n                {{ t('singleStoreApplicable') }}\r\n              </el-radio>\r\n              <el-radio value=\"1\">\r\n                {{ t('groupShare') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('belongingStore')\">\r\n            <el-select v-model=\"form.belongHcode\" :placeholder=\"t('selectStore')\">\r\n              <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.hname\" :value=\"item.hcode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('allowCredit')\">\r\n            <el-switch v-model=\"form.isCredit\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('hideRoomPriceOnRegistration')\">\r\n            <el-switch v-model=\"form.isHidePrice\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :md=\"24\">\r\n          <el-tabs class=\"demo-tabs\" model-value=\"first\">\r\n            <el-tab-pane :label=\"t('companyInformation')\" name=\"first\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('name')\" prop=\"paName\">\r\n                    <el-input v-model=\"form.paName\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('shortName')\">\r\n                    <el-input v-model=\"form.shortName\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('legalPerson')\">\r\n                    <el-input v-model=\"form.legalPerson\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('telephone')\">\r\n                    <el-input v-model=\"form.telephone\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('address')\">\r\n                    <el-input v-model=\"form.address\" maxlength=\"255\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('startDate')\">\r\n                    <el-date-picker v-model=\"form.startDate\" type=\"date\" :placeholder=\"t('selectDate')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('endDate')\">\r\n                    <el-date-picker v-model=\"form.endDate\" type=\"date\" :placeholder=\"t('selectDate')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('salesperson')\">\r\n                    <el-select v-model=\"form.seller\">\r\n                      <el-option v-for=\"i in sellers\" :key=\"i.username\" :label=\"i.nickname\" :value=\"i.username\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('status')\">\r\n                    <el-switch v-model=\"form.isEnable\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('valid')\" :inactive-text=\"t('invalid')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('customerLevel')\" prop=\"sellLevel\">\r\n                    <el-select v-model=\"form.sellLevel\">\r\n                      <el-option v-for=\"item in levelList\" :key=\"item.code\" :label=\"item.name\" :value=\"item.code\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('commissionLevel')\">\r\n                    <el-select v-model=\"form.commissionLevel\">\r\n                      <el-option v-for=\"item in brokerageLevelList\" :key=\"item.code\" :label=\"item.name\" :value=\"item.code\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('remark')\">\r\n                    <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"4\" maxlength=\"250\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-tab-pane>\r\n            <el-tab-pane :label=\"t('contactPerson')\" name=\"second\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('name')\">\r\n                    <el-input v-model=\"form.contact\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('telephone')\">\r\n                    <el-input v-model=\"form.phone\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('email')\">\r\n                    <el-input v-model=\"form.email\" maxlength=\"50\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-if=\"form.isCredit === '1'\" :label=\"t('creditSettings')\" name=\"third\">\r\n              <el-row :gutter=\"24\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('accountSetName')\">\r\n                    <el-input v-model=\"form.arSet.arSetName\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('companyName')\">\r\n                    {{ form.paName }}\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('accountType')\">\r\n                    <el-radio-group v-model=\"form.arSet.creditAccType\" class=\"ml-4\">\r\n                      <el-radio value=\"0\" size=\"large\">\r\n                        {{ t('creditAccount') }}\r\n                      </el-radio>\r\n                      <el-radio value=\"1\" size=\"large\">\r\n                        {{ t('prepaidAccount') }}\r\n                      </el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <div v-if=\"form.arSet.creditAccType === '0'\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :md=\"24\">\r\n                    <el-form-item :label=\"t('settlementPeriod')\">\r\n                      <el-radio-group v-model=\"form.arSet.creditPayDays\" class=\"ml-4\">\r\n                        <el-radio value=\"1\" size=\"large\">\r\n                          {{ t('permanentPeriod') }}\r\n                        </el-radio>\r\n                        <el-radio value=\"0\" size=\"large\">\r\n                          {{ t('fixedPeriod') }}\r\n                        </el-radio>\r\n                        <el-input-number\r\n                          v-if=\"form.arSet.creditPayDays === '0'\"\r\n                          v-model=\"form.arSet.creditPayFix\"\r\n                          class=\"mx-2\"\r\n                          :min=\"0\"\r\n                          :placeholder=\"t('pleaseEnterFixedPeriod')\"\r\n                          controls-position=\"right\"\r\n                          :precision=\"0\"\r\n                          style=\"width: 200px; margin: 0 8px\"\r\n                        />\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :md=\"24\">\r\n                    <el-form-item :label=\"t('maximumLimit')\">\r\n                      <el-radio-group v-model=\"form.arSet.creditQuotaType\" class=\"ml-4\">\r\n                        <el-radio value=\"0\" size=\"large\">\r\n                          {{ t('unlimitedLimit') }}\r\n                        </el-radio>\r\n                        <el-radio value=\"1\" size=\"large\">\r\n                          {{ t('limitedLimit') }}\r\n                        </el-radio>\r\n                        <el-input-number\r\n                          v-if=\"form.arSet.creditQuotaType === '1'\"\r\n                          v-model=\"form.arSet.creditQuota\"\r\n                          class=\"mx-4\"\r\n                          :min=\"0\"\r\n                          :placeholder=\"t('pleaseEnterMaximumLimit')\"\r\n                          controls-position=\"right\"\r\n                          :precision=\"2\"\r\n                          style=\"width: 200px; margin: 0 8px\"\r\n                        />\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :md=\"24\">\r\n                    <el-form-item :label=\"t('validityPeriod')\">\r\n                      <el-radio-group v-model=\"form.arSet.creditValidType\" class=\"ml-4\">\r\n                        <el-radio value=\"0\" size=\"large\">\r\n                          {{ t('permanentlyValid') }}\r\n                        </el-radio>\r\n                        <el-radio value=\"1\" size=\"large\">\r\n                          {{ t('fixedValidity') }}\r\n                        </el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item v-if=\"form.arSet.creditValidType === '1'\" label=\"\">\r\n                      <el-date-picker v-model=\"form.arSet.creditStartDate\" type=\"date\" :placeholder=\"t('startDate')\" />\r\n                      <span style=\"margin: 0 5px\">{{ t('to') }}</span>\r\n                      <el-date-picker v-model=\"form.arSet.creditEndDate\" type=\"date\" :placeholder=\"t('endDate')\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"20\">\r\n                  <el-form-item :label=\"t('allowNegativeBalance')\">\r\n                    <el-switch v-model=\"form.arSet.creditMinusAcc\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('hotelScope')\">\r\n                    <el-select v-model=\"form.arSet.arSetMerchants\" multiple collapse-tags clearable :placeholder=\"t('selectStore')\">\r\n                      <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.hname\" :value=\"item.hcode\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vertical-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  text-align: left;\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "belongHcode", "belongHname", "paCode", "paName", "shortName", "legal<PERSON>erson", "telephone", "address", "paType", "channelType", "channel", "isShare", "contact", "phone", "email", "seller", "startDate", "Date", "toString", "endDate", "dayjs", "add", "commissionStrategy", "isEnable", "isHidePrice", "BooleanEnum", "NO", "isCredit", "sellLevel", "commissionLevel", "remark", "arSet", "id", "arSetCode", "arSetName", "unitCode", "unitName", "creditAccName", "creditAccType", "creditPayDays", "creditPayFix", "creditQuotaType", "creditQuota", "creditValidType", "creditStartDate", "creditEndDate", "creditMinusAcc", "YES", "arSetMerchants", "<PERSON><PERSON><PERSON><PERSON>", "channels", "merchants", "formRules", "required", "message", "trigger", "onMounted", "params", "isG", "channelApi", "getChannelSimpleList", "then", "res", "code", "value", "data", "getChannels", "merchantApi", "getSimpleUnitList", "hcode", "userApi", "listSeller", "sellers", "getLevel", "typeLevel", "DictTypeEnum", "BROKERAGE_LEVEL", "generalConfigApi", "list", "type", "brokerageLevelList", "levelList", "PROTOCOL_LEVEL", "AGENT_LEVEL", "paTypeChange", "__expose", "submit", "Promise", "resolve", "validate", "valid", "ymdate", "customerApi", "create", "ElMessage", "success", "center", "error", "msg"], "mappings": "66CA6LM,MAAAA,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,MAAON,EAAUM,MAEjBC,YAAa,GAEbC,YAAa,GAEbC,OAAQ,GAERC,OAAQ,GAERC,UAAW,GAEXC,YAAa,GAEbC,UAAW,GAEXC,QAAS,GAETC,OAAQ,IAERC,YAAa,KAEbC,QAAS,GAETC,QAAS,IAETC,QAAS,GAETC,MAAO,GAEPC,MAAO,GAEPC,OAAQ,GAERC,WAAW,IAAIC,MAAOC,WAEtBC,QAASC,IAAQC,IAAI,EAAG,QAAQH,WAEhCI,mBAAoB,GAEpBC,SAAU,IAEVC,YAAaC,EAAYC,GAEzBC,SAAUF,EAAYC,GAEtBE,UAAW,GAEXC,gBAAiB,GAEjBC,OAAQ,GACRC,MAAO,CAELC,GAAI,GAEJjC,MAAON,EAAUM,MAEjBkC,UAAW,GAEXC,UAAW,GAEXC,SAAU,GAEVC,SAAU,GAEVC,cAAe,GAEfC,cAAe,IAEfC,cAAe,IAEfC,aAAc,EAEdC,gBAAiBhB,EAAYC,GAE7BgB,YAAa,EAEbC,gBAAiBlB,EAAYC,GAE7BkB,gBAAiB,GAEjBC,cAAe,GAEfC,eAAgBrB,EAAYC,GAE5BH,SAAUE,EAAYsB,IAEtBC,eAAgB,GAEhBC,SAAU,MAKRC,EAAWtD,EAAoD,IAE/DuD,EAAYvD,EAAqB,IAEjCwD,GAAYxD,EAAe,CAC/BO,OAAQ,CAAC,CAAEkD,UAAU,EAAMC,QAAS/D,EAAE,mBAAoBgE,QAAS,SACnE3B,UAAW,CACT,CACEyB,UAAU,EACVC,QAAS/D,EAAE,6BACXgE,QAAS,WAKfC,GAAU,MAoCV,WACE,MAAMC,EAAS,CACb1D,MAAON,EAAUM,MACjBwB,SAAUE,EAAYsB,IACtBW,IAAKjC,EAAYsB,KAEnBY,EAAWC,qBAAqBH,GAAQI,MAAMC,IAC3B,IAAbA,EAAIC,OACNb,EAASc,MAAQF,EAAIG,KAAA,GAExB,CA7CWC,GAkDAC,EAAAC,kBAAkB3E,EAAUM,MAAON,EAAU4E,OAAOR,MAAMC,IACnD,IAAbA,EAAIC,OACNZ,EAAUa,MAAQF,EAAIG,KAAA,IAvBlBK,EAAAC,WAAW,CAAExE,MAAON,EAAUM,QAAS8D,MAAMC,IACnDU,GAAQR,MAAQF,EAAIG,IAAA,IA3BbQ,KAgBTC,GAAUV,MAAQW,EAAaC,gBAC/BC,EAAiBC,KAAK,CAAE/E,MAAON,EAAUM,MAAOgF,KAAML,GAAUV,QAASH,MAAMC,IAC7EkB,GAAmBhB,MAAQF,EAAIG,IAAA,GAjBf,IAId,MAAAgB,GAAYrF,EAAsC,IAClD8E,GAAY9E,EAAI,IACtB,SAAS6E,KACPC,GAAUV,MAA8B,MAAtBlE,EAAKkE,MAAMxD,OAAiBmE,EAAaO,eAAiBP,EAAaQ,YACzFN,EAAiBC,KAAK,CAAE/E,MAAON,EAAUM,MAAOgF,KAAML,GAAUV,QAASH,MAAMC,IAC7EmB,GAAUjB,MAAQF,EAAIG,IAAA,GACvB,CAGG,MAAAe,GAAqBpF,EAAsC,IAS3D,MAAA4E,GAAU5E,EAA8C,IA6D9D,SAASwF,KACPtF,EAAKkE,MAAMpC,UAAY,GACd6C,IAAA,QAhCEY,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB3F,EAAQmE,OACNnE,EAAQmE,MAAMyB,UAAUC,IAClBA,IACF5F,EAAKkE,MAAMhD,UAAY2E,EAAO7F,EAAKkE,MAAMhD,WACzClB,EAAKkE,MAAM7C,QAAUwE,EAAO7F,EAAKkE,MAAM7C,SACvCrB,EAAKkE,MAAMjC,MAAMa,gBAAkB9C,EAAKkE,MAAMjC,MAAMa,gBAAkB+C,EAAO7F,EAAKkE,MAAMjC,MAAMa,iBAAmB,GACjH9C,EAAKkE,MAAMjC,MAAMc,cAAgB/C,EAAKkE,MAAMjC,MAAMc,cAAgB8C,EAAO7F,EAAKkE,MAAMjC,MAAMc,eAAiB,GAC3G+C,EAAYC,OAAO/F,EAAKkE,OAAOH,MAAMC,IAClB,IAAbA,EAAIC,MACN+B,EAAUC,QAAQ,CAChBzC,QAAS/D,EAAE,qBACXyG,QAAQ,IAEFR,KAERM,EAAUG,MAAM,CACd3C,QAASQ,EAAIoC,IACbF,QAAQ,GACT,IAEJ,GAEJ"}