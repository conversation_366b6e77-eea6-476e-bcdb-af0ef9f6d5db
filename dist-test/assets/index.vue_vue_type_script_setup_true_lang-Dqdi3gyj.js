import{d as e,o as s,c as o,g as t,ab as n,f as a,R as r,_ as c}from"./index-CkEhI1Zk.js";const l={class:"chip inline-block rounded-999 bg-stone-1 px-3 vertical-mid transition-background-color dark-bg-stone-8"},i={class:"content h-8 flex items-center gap-2 text-xs"},d=e({name:"Chip",__name:"index",props:{closable:{type:Boolean}},emits:["close"],setup(e,{emit:d}){const p=d;return(e,d)=>{const b=c;return s(),o("div",l,[t("div",i,[n(e.$slots,"default"),e.closable?(s(),o("span",{key:0,class:"closable h-6 w-6 flex-center cursor-pointer rounded-1/2 bg-stone-2 text-sm text-initial transition-background-color -mr-1.5 dark-bg-stone-9 hover-op-70",onClick:d[0]||(d[0]=e=>p("close"))},[a(b,{name:"i-ep:close-bold"})])):r("",!0)])])}}});export{d as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-Dqdi3gyj.js.map
