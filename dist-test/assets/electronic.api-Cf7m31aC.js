import{a as e}from"./index-CkEhI1Zk.js";const t="admin-api/pms/price-panel";function a(t){return e.get("admin-api/pms/price-panel-background/list",{params:t})}function n(a){return e.get(`${t}/get`,{params:a})}function r(a){return e.post(`${t}/create-or-update-base`,a)}function p(a){return e.get(`${t}/get-price`,{params:a})}function s(a){return e.post(`${t}/update`,a)}function u(a){return e.get(`${t}/get-price-panel-custom`,{params:a})}function c(a){return e.post(`${t}/create-price-panel-custom`,a)}function i(a){return e.post(`${t}/update-price-panel-custom`,a)}function o(a){return e.delete(`${t}/delete-price-panel-custom`,{params:a})}export{p as a,a as b,n as c,o as d,c as e,r as f,u as g,s as h,i as u};
//# sourceMappingURL=electronic.api-Cf7m31aC.js.map
