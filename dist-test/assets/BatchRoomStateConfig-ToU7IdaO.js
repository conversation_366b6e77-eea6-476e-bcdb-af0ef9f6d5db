import{d as e,y as t,ai as a,aj as l,b as s,o as c,c as o,f as n,w as i,g as r,u,h as b,Y as p,i as m,F as d,ag as h,R as f,aR as y,b7 as v,b8 as C,k as _,q as g,aA as j,e as S,j as k}from"./index-CkEhI1Zk.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                    */import{r as D}from"./room.api-BPOzPQjk.js";import{u as x}from"./user.api-BYl7ypOS.js";import O from"./cleanDirty--Y1_-3mS.js";import{_ as R}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css               *//* empty css                          *//* empty css               *//* empty css                    */const V={key:0},w=e({__name:"BatchRoomStateConfig",emits:["success"],setup(e,{expose:R,emit:w}){const B=w;t((()=>{}));const z=a(),{t:A}=l(),L=s(),P=s(),U=s(!1),Z=s(!1),q=s("clean"),F=s([]),I=s(void 0);function T(){}async function Y(){if("clean"===q.value&&!I.value)return y.warning(A("pleaseSelectCleaner"));const e="clean"===q.value?L.value.submit():P.value.submit();if(e.length<1)return y.warning(A("pleaseSelectAtLeastOneRoom"));U.value=!0,await D.updateBatchClean({gcode:z.gcode,hcode:z.hcode,rCodes:e,state:"clean"===q.value?"1":"0",cleaner:I.value}).then((e=>{0===e.code?(y.success(A("batchOperationSuccess")),Z.value=!1,q.value="clean",B("success")):y.error(e.msg)})).finally((()=>{U.value=!1}))}function E(){Z.value=!1}return R({open:async function(){Z.value=!0,async function(){await x.getCleanerList({gcode:z.gcode,hcode:z.hcode}).then((e=>{0===e.code?F.value=e.data:y.error(e.msg)}))}()}}),(e,t)=>{const a=v,l=C,s=k,y=_,D=g,x=j;return c(),o("div",null,[n(x,{modelValue:u(Z),"onUpdate:modelValue":t[2]||(t[2]=e=>m(Z)?Z.value=e:null),"z-index":2e3,title:u(A)("batchCleanDirty"),size:"600px","close-on-click-modal":!0,"destroy-on-close":"",modal:!1},{footer:i((()=>[r("span",null,["clean"===u(q)?(c(),o("span",V,[b(p(u(A)("selectCleaner"))+" ",1),n(y,{modelValue:u(I),"onUpdate:modelValue":t[1]||(t[1]=e=>m(I)?I.value=e:null),placeholder:u(A)("pleaseSelectCleaner"),style:{width:"240px"}},{default:i((()=>[(c(!0),o(d,null,h(u(F),(e=>(c(),S(s,{key:e.username,label:e.nickname,value:e.username},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])):f("",!0)]),r("span",null,[n(D,{size:"large",onClick:E},{default:i((()=>[b(p(u(A)("close")),1)])),_:1}),n(D,{type:"primary",size:"large",loading:u(U),onClick:Y},{default:i((()=>[b(p(u(A)("save")),1)])),_:1},8,["loading"])])])),default:i((()=>[n(l,{modelValue:u(q),"onUpdate:modelValue":t[0]||(t[0]=e=>m(q)?q.value=e:null),class:"demo-tabs",onTabClick:T},{default:i((()=>[n(a,{label:u(A)("batchClean"),name:"clean"},{default:i((()=>[n(O,{ref_key:"cleanRef",ref:L,type:"clean"},null,512)])),_:1},8,["label"]),n(a,{label:u(A)("batchDirty"),name:"dirty"},{default:i((()=>[n(O,{ref_key:"dirtyRef",ref:P,type:"dirty"},null,512)])),_:1},8,["label"])])),_:1},8,["modelValue"])])),_:1},8,["modelValue","title"])])}}});function B(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{pleaseSelectCleaner:{t:0,b:{t:2,i:[{t:3}],s:"Please select a cleaner"}},pleaseSelectAtLeastOneRoom:{t:0,b:{t:2,i:[{t:3}],s:"Please select at least one room"}},batchOperationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Batch operation successful!"}},batchCleanDirty:{t:0,b:{t:2,i:[{t:3}],s:"Batch Clean/Dirty"}},batchClean:{t:0,b:{t:2,i:[{t:3}],s:"Batch Clean"}},batchDirty:{t:0,b:{t:2,i:[{t:3}],s:"Batch Dirty"}},selectCleaner:{t:0,b:{t:2,i:[{t:3}],s:"Select Cleaner"}},close:{t:0,b:{t:2,i:[{t:3}],s:"Close"}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}}},"zh-cn":{pleaseSelectCleaner:{t:0,b:{t:2,i:[{t:3}],s:"请选择保洁员"}},pleaseSelectAtLeastOneRoom:{t:0,b:{t:2,i:[{t:3}],s:"请至少选择一间房"}},batchOperationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"批量操作成功！"}},batchCleanDirty:{t:0,b:{t:2,i:[{t:3}],s:"批量置净置脏"}},batchClean:{t:0,b:{t:2,i:[{t:3}],s:"批量置净"}},batchDirty:{t:0,b:{t:2,i:[{t:3}],s:"批量置脏"}},selectCleaner:{t:0,b:{t:2,i:[{t:3}],s:"选择保洁员"}},close:{t:0,b:{t:2,i:[{t:3}],s:"关闭"}},save:{t:0,b:{t:2,i:[{t:3}],s:"保存"}}},km:{pleaseSelectCleaner:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសអ្នកសម្អាត"}},pleaseSelectAtLeastOneRoom:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសយ៉ាងហោចណាស់មួយបន្ទប់"}},batchOperationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ប្រតិបត្តិការជាក្រុមជោគជ័យ!"}},batchCleanDirty:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមស្អាត/ក្រុមខ្វះ"}},batchClean:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមស្អាត"}},batchDirty:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមខ្វះ"}},selectCleaner:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសអ្នកសម្អាត"}},close:{t:0,b:{t:2,i:[{t:3}],s:"បិទ"}},save:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}}}}})}B(w);const z=R(w,[["__scopeId","data-v-1e03cc02"]]);export{z as default};
//# sourceMappingURL=BatchRoomStateConfig-ToU7IdaO.js.map
