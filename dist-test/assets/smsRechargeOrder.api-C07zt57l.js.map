{"version": 3, "file": "smsRechargeOrder.api-C07zt57l.js", "sources": ["../../src/api/modules/marketing/sms/smsRechargeOrder.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n/**\r\n * 短信充值记录\r\n */\r\nexport default {\r\n  /**\r\n   * 短信充值订单\r\n   * @param dateStart\r\n   * @param dateEnd\r\n   * @returns\r\n   */\r\n  list: (date: {\r\n    gcode: string\r\n    dateStart?: string\r\n    dateEnd?: string\r\n    pageNo: number\r\n    pageSize: number\r\n  }) =>\r\n    api.get('marketing/sms/order/list', {\r\n      params: date,\r\n    }),\r\n\r\n  /**\r\n   * 创建短信充值订单\r\n   * @param data {activeCode:活动代码}\r\n   * @returns\r\n   */\r\n  create: (data: { gcode: string, hcode: string, activeCode: string }) =>\r\n    api.post('marketing/sms/order/create', data, {}),\r\n\r\n  /**\r\n   * 修改短信充值订单\r\n   * @param data\r\n   * @returns\r\n   */\r\n  edit: (data: {\r\n    gcode: string\r\n    hcode: string\r\n    orderNo: string\r\n    activeCode: string\r\n  }) => api.post('marketing/sms/order/edit', data, {}),\r\n\r\n  // 消息发送记录（分页查询）\r\n  pageList: (data: any) => api.post('admin-api/marketing/sms-send-record/pageList', data),\r\n  // 发送统计\r\n  statistics: (data: any) => api.post('admin-api/marketing/sms-send-record/statistics', data),\r\n  // 获得短信默认模板分页\r\n  templatePageList: (data: any) => api.post('admin-api/marketing/sms-default-template/page', data),\r\n  // 启用/停用短信默认模板\r\n  templateEnabled: (data: any) => api.put('admin-api/marketing/sms-send-record/pageList', data),\r\n  // 公共业务启用/停用短信默认模板\r\n  templateUpdate: (data: any) => api.put('admin-api/marketing/sms-default-template/update-enabled', data),\r\n}\r\n"], "names": ["smsRechargeOrderApi", "list", "date", "api", "get", "params", "create", "data", "post", "edit", "pageList", "statistics", "templatePageList", "templateEnabled", "put", "templateUpdate"], "mappings": "wCAKA,MAAeA,EAAA,CAObC,KAAOC,GAOLC,EAAIC,IAAI,2BAA4B,CAClCC,OAAQH,IAQZI,OAASC,GACPJ,EAAIK,KAAK,6BAA8BD,EAAM,IAO/CE,KAAOF,GAKDJ,EAAIK,KAAK,2BAA4BD,EAAM,IAGjDG,SAAWH,GAAcJ,EAAIK,KAAK,+CAAgDD,GAElFI,WAAaJ,GAAcJ,EAAIK,KAAK,iDAAkDD,GAEtFK,iBAAmBL,GAAcJ,EAAIK,KAAK,gDAAiDD,GAE3FM,gBAAkBN,GAAcJ,EAAIW,IAAI,+CAAgDP,GAExFQ,eAAiBR,GAAcJ,EAAIW,IAAI,0DAA2DP"}