{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-CB3L42GY.js", "sources": ["../../src/layouts/components/Topbar/Toolbar/NavSearch/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport eventBus from '@/utils/eventBus'\r\n\r\ndefineOptions({\r\n  name: 'NavSearch',\r\n})\r\n\r\nconst settingsStore = useSettingsStore()\r\n\r\nconst { t } = useI18n()\r\n</script>\r\n\r\n<template>\r\n  <span\r\n    class=\"flex-center cursor-pointer\"\r\n    :class=\"settingsStore.mode === 'mobile' ? 'p-2' : 'px-2'\"\r\n    @click=\"eventBus.emit('global-search-toggle')\"\r\n  >\r\n    <SvgIcon v-if=\"settingsStore.mode === 'mobile'\" name=\"i-ri:search-line\" />\r\n    <span\r\n      v-else\r\n      class=\"group inline-flex cursor-pointer items-center gap-1 whitespace-nowrap rounded-2 bg-stone-1 px-2 py-1.5 text-dark ring-stone-3 ring-inset transition dark-bg-stone-9 dark-text-white hover-ring-1 dark-ring-stone-7\"\r\n    >\r\n      <SvgIcon name=\"i-ri:search-line\" />\r\n      <span\r\n        class=\"text-sm text-stone-5 transition group-hover-text-dark dark-group-hover-text-white\"\r\n        >{{ t('app.search.text') }}</span\r\n      >\r\n      <HKbd v-if=\"settingsStore.settings.navSearch.enableHotkeys\" class=\"ms-2\"\r\n        >{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }} S</HKbd\r\n      >\r\n    </span>\r\n  </span>\r\n</template>\r\n"], "names": ["settingsStore", "useSettingsStore", "t", "useI18n"], "mappings": "4iBAQA,MAAMA,EAAgBC,KAEhBC,EAAEA,GAAMC"}