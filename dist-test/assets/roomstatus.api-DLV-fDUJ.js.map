{"version": 3, "file": "roomstatus.api-DLV-fDUJ.js", "sources": ["../../src/api/modules/pms/roomstatus/roomstatus.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/room-status'\r\n\r\nexport default {\r\n  /**\r\n   * 获得房间列表\r\n   * @param data\r\n   */\r\n  getRoomStatus: (data: any) => api.get(`${BASE_PATH}/list`, { params: data }),\r\n\r\n  /**\r\n   * 远期房态\r\n   * @param data gcode:集团代码 hcode:酒店代码 startDate:开始日期\r\n   */\r\n  getFutureRoomStatus: (data: any) => api.get(`${BASE_PATH}/future-room-status`, { params: data }),\r\n\r\n  /**\r\n   * 获取实时出租率\r\n   * @param data\r\n   */\r\n  getOCC: (data: any) => api.get(`${BASE_PATH}/get-occ`, { params: data }),\r\n\r\n  /**\r\n   * 获得OTA_api列表\r\n   * @param data\r\n   */\r\n  getOtaApiList: (data: any) => api.get(`admin-api/ota-sync/ota-api/list`, { params: data }),\r\n  /**\r\n   * 根据外部订单号查询是否存在OTA订单\r\n   * @param data\r\n   */\r\n  getExistOtaOrder: (data: any) => api.get(`admin-api/pms/order/exist-ota-order`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "roomStatusApi", "getRoomStatus", "data", "api", "get", "params", "getFutureRoomStatus", "getOCC", "getOtaApiList", "getExistOtaOrder"], "mappings": "wCAEA,MAAMA,EAAY,4BAEHC,EAAA,CAKbC,cAAgBC,GAAcC,EAAIC,IAAI,GAAGL,SAAkB,CAAEM,OAAQH,IAMrEI,oBAAsBJ,GAAcC,EAAIC,IAAI,GAAGL,uBAAgC,CAAEM,OAAQH,IAMzFK,OAASL,GAAcC,EAAIC,IAAI,GAAGL,YAAqB,CAAEM,OAAQH,IAMjEM,cAAgBN,GAAcC,EAAIC,IAAI,kCAAmC,CAAEC,OAAQH,IAKnFO,iBAAmBP,GAAcC,EAAIC,IAAI,sCAAuC,CAAEC,OAAQH"}