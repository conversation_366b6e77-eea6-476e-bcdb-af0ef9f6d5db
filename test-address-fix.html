<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址脱敏功能修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 600;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
        }
        
        .demo-item {
            background: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .original {
            color: #67c23a;
        }
        
        .masked {
            color: #e6a23c;
        }
        
        .test-result {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .pass {
            color: #059669;
        }
        
        .fail {
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🏠 地址脱敏功能修复测试</h1>
        
        <div class="demo-section">
            <div class="demo-title">各种地址格式脱敏测试</div>
            
            <div class="demo-item">
                <strong>省市县格式（用户提到的问题）：</strong><br>
                <span class="original">原始：湖南省益阳市南县中鱼口乡小小村</span><br>
                <span class="masked" id="test1">脱敏：</span>
            </div>
            
            <div class="demo-item">
                <strong>省市区格式：</strong><br>
                <span class="original">原始：广东省深圳市南山区科技园南区深南大道10000号</span><br>
                <span class="masked" id="test2">脱敏：</span>
            </div>
            
            <div class="demo-item">
                <strong>直辖市区格式：</strong><br>
                <span class="original">原始：北京市朝阳区建国门外大街1号</span><br>
                <span class="masked" id="test3">脱敏：</span>
            </div>
            
            <div class="demo-item">
                <strong>直辖市县格式：</strong><br>
                <span class="original">原始：重庆市巫山县大昌镇人民路123号</span><br>
                <span class="masked" id="test4">脱敏：</span>
            </div>
            
            <div class="demo-item">
                <strong>自治区格式：</strong><br>
                <span class="original">原始：新疆维吾尔自治区乌鲁木齐市天山区人民路100号</span><br>
                <span class="masked" id="test5">脱敏：</span>
            </div>
            
            <div class="demo-item">
                <strong>省直辖县格式：</strong><br>
                <span class="original">原始：河南省济源市天坛路123号</span><br>
                <span class="masked" id="test6">脱敏：</span>
            </div>
            
            <div class="demo-item">
                <strong>自治州格式：</strong><br>
                <span class="original">原始：四川省甘孜藏族自治州康定市向阳街道</span><br>
                <span class="masked" id="test7">脱敏：</span>
            </div>
            
            <div class="demo-item">
                <strong>内蒙古盟旗格式：</strong><br>
                <span class="original">原始：内蒙古自治区锡林郭勒盟正蓝旗上都镇</span><br>
                <span class="masked" id="test8">脱敏：</span>
            </div>
        </div>
        
        <div class="test-result" id="testResult">
            <strong>测试结果：</strong><br>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // 修复后的地址脱敏函数
        function maskAddress(address) {
            if (!address) return '';
            
            // 常见省市区/县模式匹配，按优先级排序
            const patterns = [
                // 完整格式：XX省XX市XX区/县（包括市辖区和县级市）
                /^(.{2,3}省.{2,6}市.{2,6}[区县市])/,
                // 直辖市格式：XX市XX区/县
                /^(.{2,3}市.{2,6}[区县])/,
                // 自治区格式：XX自治区XX市XX区/县
                /^(.{2,8}自治区.{2,6}市.{2,6}[区县市])/,
                // 特别行政区格式：XX特别行政区XX区
                /^(.{2,8}特别行政区.{2,6}区)/,
                // 省直辖县级行政区：XX省XX县
                /^(.{2,3}省.{2,6}县)/,
                // 地级市下辖县：XX省XX市XX县
                /^(.{2,3}省.{2,6}市.{2,6}县)/,
                // 自治州格式：XX省XX自治州XX县
                /^(.{2,3}省.{2,8}自治州.{2,6}[县市])/,
                // 盟格式（内蒙古）：XX自治区XX盟XX旗/县
                /^(.{2,8}自治区.{2,6}盟.{2,6}[旗县])/,
                // 简化格式：至少保留前6个字符作为省市区
                /^(.{6})/
            ];
            
            for (const pattern of patterns) {
                const match = address.match(pattern);
                if (match) {
                    const regionPart = match[1];
                    const remainingLength = address.length - regionPart.length;
                    if (remainingLength > 0) {
                        return regionPart + '*'.repeat(remainingLength);
                    } else {
                        return regionPart;
                    }
                }
            }
            
            // 如果都不匹配，保留前3个字符
            if (address.length <= 3) {
                return address;
            }
            return address.substr(0, 3) + '*'.repeat(address.length - 3);
        }
        
        // 测试用例
        const testCases = [
            {
                id: 'test1',
                original: '湖南省益阳市南县中鱼口乡小小村',
                expected: '湖南省益阳市南县*******'
            },
            {
                id: 'test2',
                original: '广东省深圳市南山区科技园南区深南大道10000号',
                expected: '广东省深圳市南山区*****************'
            },
            {
                id: 'test3', 
                original: '北京市朝阳区建国门外大街1号',
                expected: '北京市朝阳区**********'
            },
            {
                id: 'test4',
                original: '重庆市巫山县大昌镇人民路123号',
                expected: '重庆市巫山县***********'
            },
            {
                id: 'test5',
                original: '新疆维吾尔自治区乌鲁木齐市天山区人民路100号',
                expected: '新疆维吾尔自治区乌鲁木齐市天山区***********'
            },
            {
                id: 'test6',
                original: '河南省济源市天坛路123号',
                expected: '河南省济源市*******'
            },
            {
                id: 'test7',
                original: '四川省甘孜藏族自治州康定市向阳街道',
                expected: '四川省甘孜藏族自治州康定市******'
            },
            {
                id: 'test8',
                original: '内蒙古自治区锡林郭勒盟正蓝旗上都镇',
                expected: '内蒙古自治区锡林郭勒盟正蓝旗****'
            }
        ];
        
        // 执行测试
        let passCount = 0;
        let totalCount = testCases.length;
        let results = [];
        
        testCases.forEach(testCase => {
            const masked = maskAddress(testCase.original);
            const element = document.getElementById(testCase.id);
            element.textContent = `脱敏：${masked}`;
            
            const passed = masked === testCase.expected;
            if (passed) {
                passCount++;
                element.classList.add('pass');
                results.push(`✅ ${testCase.id}: 通过`);
            } else {
                element.classList.add('fail');
                results.push(`❌ ${testCase.id}: 失败 (期望: ${testCase.expected}, 实际: ${masked})`);
            }
        });
        
        // 显示测试结果
        const resultElement = document.getElementById('results');
        resultElement.innerHTML = results.join('<br>') + 
            `<br><br><strong>总计：${passCount}/${totalCount} 通过</strong>`;
        
        if (passCount === totalCount) {
            resultElement.innerHTML += '<br><span class="pass">🎉 所有测试通过！地址脱敏功能修复成功！</span>';
        } else {
            resultElement.innerHTML += '<br><span class="fail">⚠️ 部分测试失败，需要进一步调整</span>';
        }
    </script>
</body>
</html>
