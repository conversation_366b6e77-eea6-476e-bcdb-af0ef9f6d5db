{"version": 3, "file": "HInput.vue_vue_type_script_setup_true_lang-Deq2hR5F.js", "sources": ["../../src/layouts/ui-kit/HInput.vue"], "sourcesContent": ["<script setup lang=\"ts\" generic=\"T extends string | number\">\r\nwithDefaults(\r\n  defineProps<{\r\n    placeholder?: string\r\n    disabled?: boolean\r\n  }>(),\r\n  {\r\n    disabled: false,\r\n  },\r\n)\r\n\r\nconst value = defineModel<T>()\r\n\r\nconst inputRef = useTemplateRef('inputRef')\r\n\r\ndefineExpose({\r\n  ref: inputRef,\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"relative w-full lg-w-48\">\r\n    <input ref=\"inputRef\" v-model=\"value\" type=\"text\" :placeholder=\"placeholder\" :disabled=\"disabled\" class=\"relative block w-full border-0 rounded-md bg-white px-2.5 py-1.5 text-sm shadow-sm ring-1 ring-stone-2 ring-inset disabled-cursor-not-allowed dark-bg-dark disabled-opacity-50 focus-outline-none focus-ring-2 dark-ring-stone-8 focus-ring-ui-primary placeholder-stone-4 dark-placeholder-stone-5\">\r\n  </div>\r\n</template>\r\n"], "names": ["value", "_useModel", "inputRef", "useTemplateRef", "__expose", "ref"], "mappings": "gVAWM,MAAAA,EAAQC,kBAERC,EAAWC,EAAe,mBAEnBC,EAAA,CACXC,IAAKH"}