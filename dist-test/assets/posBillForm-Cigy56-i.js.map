{"version": 3, "file": "posBillForm-Cigy56-i.js", "sources": ["../../src/views/print/posBillForm.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport useUserStore from '@/store/modules/user'\r\nimport { computed, onMounted, ref } from 'vue'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst props = defineProps({\r\n  formData: {\r\n    type: Object,\r\n    required: true,\r\n  },\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nonMounted(() => {})\r\n\r\n// 酒店信息\r\nconst hotelInfo = computed(() => {\r\n  return {\r\n    name: props.formData.hname,\r\n    phone: props.formData.frontPhone || '',\r\n    address: props.formData.address || '',\r\n  }\r\n})\r\n\r\n// 当前时间\r\nconst currentTime = ref(props.formData.printDate || new Date().toLocaleString())\r\n\r\n// 计算总计\r\nconst totalConsume = computed(() => {\r\n  return props.formData.consumeTotalFee || 0\r\n})\r\n\r\nconst totalPay = computed(() => {\r\n  return props.formData.payTotalFee || 0\r\n})\r\n\r\n// 格式化日期时间，去掉秒\r\nfunction formatDateTime(dateStr) {\r\n  if (!dateStr) {\r\n    return ''\r\n  }\r\n  try {\r\n    // 尝试将日期格式化为 yyyy-MM-dd HH:mm 格式\r\n    const date = new Date(dateStr)\r\n    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\r\n  } catch (e) {\r\n    // 如果格式化失败，返回原始字符串\r\n    return dateStr\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"pos-bill-container\">\r\n    <div class=\"hotel-title\">\r\n      {{ hotelInfo.name }}\r\n    </div>\r\n    <div class=\"form-title\">结账明细单</div>\r\n    <div class=\"bill-info\">\r\n      <div>NO:{{ props.formData.orderNo }}</div>\r\n      <div>打印时间:{{ currentTime }}</div>\r\n    </div>\r\n\r\n    <table class=\"info-table\">\r\n      <tbody>\r\n        <tr>\r\n          <th>客人姓名</th>\r\n          <td colspan=\"3\">\r\n            {{ props.formData.name }}\r\n          </td>\r\n        </tr>\r\n        <tr>\r\n          <th>房型</th>\r\n          <td colspan=\"3\">\r\n            {{ props.formData.rtName }}\r\n          </td>\r\n        </tr>\r\n        <tr>\r\n          <th>房号</th>\r\n          <td colspan=\"3\">\r\n            {{ props.formData.rNo }}\r\n          </td>\r\n        </tr>\r\n        <tr>\r\n          <th>确认金额</th>\r\n          <td>{{ props.formData.balance }}</td>\r\n          <th>积分金额</th>\r\n          <td>{{ props.formData.point }}</td>\r\n        </tr>\r\n        <tr>\r\n          <th>入住时间</th>\r\n          <td colspan=\"3\">\r\n            {{ formatDateTime(props.formData.checkinTime) }}\r\n          </td>\r\n        </tr>\r\n        <tr>\r\n          <th>离店时间</th>\r\n          <td colspan=\"3\">\r\n            {{ formatDateTime(props.formData.checkoutTime) }}\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 消费明细 -->\r\n    <table class=\"detail-table\">\r\n      <thead>\r\n        <tr>\r\n          <th class=\"center-text\">项目</th>\r\n          <th class=\"center-text\">消费</th>\r\n          <th class=\"center-text\">付款</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <!-- 消费明细 -->\r\n        <tr v-for=\"(item, index) in props.formData.consumptionDetails\" :key=\"`consume-${index}`\">\r\n          <td class=\"center-text\">\r\n            {{ item.subName }}\r\n          </td>\r\n          <td class=\"center-text\">\r\n            {{ item.fee }}\r\n          </td>\r\n          <td class=\"center-text\">-</td>\r\n        </tr>\r\n        <!-- 付款明细 -->\r\n        <tr v-for=\"(item, index) in props.formData.paymentDetails\" :key=\"`pay-${index}`\">\r\n          <td class=\"center-text\">\r\n            {{ item.subName }}\r\n          </td>\r\n          <td class=\"center-text\">-</td>\r\n          <td class=\"center-text\">\r\n            {{ item.fee }}\r\n          </td>\r\n        </tr>\r\n        <!-- 合计 -->\r\n        <tr class=\"total-row\">\r\n          <td style=\"text-align: right\">合计:</td>\r\n          <td class=\"center-text\">\r\n            {{ totalConsume }}\r\n          </td>\r\n          <td class=\"center-text\">\r\n            {{ totalPay }}\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 备注 -->\r\n    <div class=\"remark\">\r\n    \t<div>订单备注:</div>\r\n      <div>{{ props.formData.orderRemark || '' }}</div>\r\n      <div>备注:</div>\r\n      <div>{{ props.formData.info || '' }}</div>\r\n    </div>\r\n\r\n    <div class=\"footer\">\r\n      <div>责任签名:</div>\r\n      <div>酒店电话: {{ hotelInfo.phone }}</div>\r\n      <div>酒店地址: {{ hotelInfo.address }}</div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.pos-bill-container {\r\n  width: 76mm;\r\n  margin: 0 auto;\r\n  padding: 5mm;\r\n  font-size: 14px;\r\n  border: 1px solid #000;\r\n  color: black;\r\n}\r\n\r\n.hotel-title {\r\n  text-align: center;\r\n  font-size: 30px;\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.form-title {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.bill-info {\r\n  margin-bottom: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.info-table,\r\n.detail-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  margin-bottom: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.info-table th,\r\n.info-table td,\r\n.detail-table th,\r\n.detail-table td {\r\n  border: 1px solid #000;\r\n  padding: 1px 2px;\r\n}\r\n\r\n.info-table th {\r\n  width: 25%;\r\n  text-align: left;\r\n}\r\n\r\n.center-text {\r\n  text-align: center;\r\n}\r\n\r\n.total-row {\r\n  font-weight: bold;\r\n}\r\n\r\n.remark {\r\n  border: 1px solid #000;\r\n  padding: 2px;\r\n  margin-bottom: 4px;\r\n  font-size: 10px;\r\n}\r\n\r\n.footer {\r\n  width: 95%;\r\n  margin-top: 10px;\r\n  font-size: 12px;\r\n}\r\n\r\n.footer > div {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "useI18n", "useUserStore", "onMounted", "hotelInfo", "computed", "name", "formData", "hname", "phone", "frontPhone", "address", "currentTime", "ref", "printDate", "Date", "toLocaleString", "totalConsume", "consumeTotalFee", "totalPay", "payTotalFee", "formatDateTime", "dateStr", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "e"], "mappings": "mqBAKA,MAAMA,EAAQC,EAMQC,IACSC,IAE/BC,GAAU,SAGJ,MAAAC,EAAYC,GAAS,KAClB,CACLC,KAAMP,EAAMQ,SAASC,MACrBC,MAAOV,EAAMQ,SAASG,YAAc,GACpCC,QAASZ,EAAMQ,SAASI,SAAW,OAKjCC,EAAcC,EAAId,EAAMQ,SAASO,gBAAiBC,MAAOC,kBAGzDC,EAAeZ,GAAS,IACrBN,EAAMQ,SAASW,iBAAmB,IAGrCC,EAAWd,GAAS,IACjBN,EAAMQ,SAASa,aAAe,IAIvC,SAASC,EAAeC,GACtB,IAAKA,EACI,MAAA,GAEL,IAEI,MAAAC,EAAO,IAAIR,KAAKO,GACf,MAAA,GAAGC,EAAKC,iBAAiBC,OAAOF,EAAKG,WAAa,GAAGC,SAAS,EAAG,QAAQF,OAAOF,EAAKK,WAAWD,SAAS,EAAG,QAAQF,OAAOF,EAAKM,YAAYF,SAAS,EAAG,QAAQF,OAAOF,EAAKO,cAAcH,SAAS,EAAG,aACtMI,GAEA,OAAAT,CAAA,CACT"}