# 优惠券房型判断功能实现完成

## 功能说明
根据后端返回的数据，新增了 `rtCodes` 字段来判断优惠券是否适用于当前房型。

## 已完成的修改

### 1. ✅ 更新 Coupon 接口定义
- 在 `CouponDialog.vue` 第22行添加了 `rtCodes?: string[]` 字段来存储适用的房型代码列表

### 2. ✅ 修改数据转换逻辑
- 在 `fetchCoupons` 函数中第706行，将后端返回的 `rtCodes` 字段保存到前端数据结构中：
  ```typescript
  rtCodes: item.rtCodes || [], // 添加适用的房型代码列表
  ```

### 3. ✅ 更新可用性判断逻辑
- 在 `isAvailable` 函数中第248-252行添加房型判断：
  ```typescript
  // 检查房型是否适用
  if (props.rtCode && coupon.rtCodes && coupon.rtCodes.length > 0) {
    if (!coupon.rtCodes.includes(props.rtCode)) {
      return false
    }
  }
  ```
- 在 `isAvailableForFee` 函数中第283-287行添加相同的房型判断逻辑

### 4. ✅ 添加不可用原因提示
- 新增 `getUnavailableReason` 函数（第303-346行），用于获取优惠券不可用的具体原因
- 当房型不匹配时，返回 "该优惠券不适用该房型" 提示

### 5. ✅ 更新模板和样式
- 为首日优惠模式的优惠券项添加 tooltip 提示（第1138-1189行）
- 为多天优惠模式的优惠券选项添加 tooltip 提示（第1065-1104行）
- 为不可用的优惠券选项添加置灰样式（第1347-1356行）

### 6. ✅ 父组件集成确认
- 确认 `leavePrice/index.vue` 已正确传递 `rtCode` 参数（第1939行）：
  ```vue
  <CouponDialog
    v-model="couponDialogVisible"
    :rt-code="getRtCode()"
    ...
  />
  ```

## 实现效果

### 场景1：房型匹配 ✅
- 当前房型代码在优惠券的 `rtCodes` 列表中
- 优惠券可以正常选择和使用

### 场景2：房型不匹配 ✅
- 当前房型代码不在优惠券的 `rtCodes` 列表中
- 优惠券被置灰且不可选择
- 鼠标悬停时显示 "该优惠券不适用该房型" 提示

### 场景3：无房型限制 ✅
- 优惠券的 `rtCodes` 为空或未定义
- 优惠券对所有房型都可用

## 后端数据示例
```json
{
  "id": 158,
  "templateName": "订单蚁来优惠券",
  "couponType": "voucher",
  "rtCodes": [
    "1868570690343190528",
    "1868570661209554944",
    "1831598419748958208"
  ]
}
```

## 技术实现细节

### 房型判断逻辑
```typescript
// 检查房型是否适用
if (props.rtCode && coupon.rtCodes && coupon.rtCodes.length > 0) {
  if (!coupon.rtCodes.includes(props.rtCode)) {
    return false // 房型不匹配，优惠券不可用
  }
}
```

### Tooltip 提示实现
- 首日优惠模式：使用 `el-tooltip` 包装整个优惠券项
- 多天优惠模式：在 `el-option` 中使用条件渲染的 `el-tooltip`

### 样式处理
- 不可用的优惠券选项添加 `disabled` 类，设置透明度为 0.5
- 优惠券状态文本显示具体的不可用原因

## 状态：✅ 实现完成
所有功能已按需求实现，包括房型判断、置灰显示、tooltip 提示等。

---

# 国际化功能实现完成

## 新增功能说明
参考 leavePrice/index.vue 的国际化实现，为 CouponDialog.vue 组件添加了完整的国际化支持。

## 已完成的国际化修改

### 1. ✅ 添加国际化配置
- 在文件顶部添加了 `<i18n>` 标签，包含英文、中文、柬埔寨语三种语言
- 涵盖了所有界面文本，包括：
  - 对话框标题和按钮
  - 优惠模式选择
  - 表格列标题
  - 提示信息和错误消息
  - 优惠券状态和类型显示

### 2. ✅ 导入和使用 useI18n
- 导入 `useI18n` 函数
- 在组件中使用 `const { t } = useI18n()`

### 3. ✅ 更新所有硬编码文本
- **对话框标题**：`title="选择优惠券"` → `:title="t('selectCoupon')"`
- **优惠模式**：`首日优惠` → `{{ t('firstDayDiscount') }}`
- **搜索框**：`placeholder="输入优惠券名称搜索"` → `:placeholder="t('searchCouponPlaceholder')"`
- **表格列标题**：`label="日期"` → `:label="t('date')"`
- **按钮文本**：`取消` → `{{ t('cancel') }}`

### 4. ✅ 更新动态文本和提示
- **不可用原因**：`getUnavailableReason()` 函数中的所有提示文本
- **Tooltip 内容**：使用 `t()` 函数和参数插值
- **空数据提示**：列表为空时的说明文本
- **优惠券信息**：券代码、券类型、有效期等标签

### 5. ✅ 支持参数插值
- **最低金额**：`t('minAmount', { amount: coupon.minAmount })`
- **已使用日期**：`t('couponAlreadyUsedOn', { date: getDateUsingCoupon(coupon.id) })`

## 国际化文本对照表

| 中文 | 英文 | 柬埔寨语 | 键名 |
|------|------|----------|------|
| 选择优惠券 | Select Coupon | ជ្រើសរើសគូប៉ុង | selectCoupon |
| 首日优惠 | First Day Discount | ការបញ្ចុះតម្លៃថ្ងៃដំបូង | firstDayDiscount |
| 连住多天优惠 | Multi-day Stay Discount | ការបញ្ចុះតម្លៃសម្រាប់ស្នាក់នៅច្រើនថ្ងៃ | multiDayDiscount |
| 该优惠券不适用该房型 | This coupon is not applicable to this room type | គូប៉ុងនេះមិនអាចអនុវត្តចំពោះប្រភេទបន្ទប់នេះបានទេ | couponNotApplicableToRoomType |
| 免房 | Free Room | បន្ទប់ឥតគិតថ្លៃ | freeRoom |
| 折 | Discount | បញ្ចុះតម្លៃ | discountUnit |

## 实现效果

### 多语言支持 ✅
- 支持中文、英文、柬埔寨语三种语言
- 所有界面文本都已国际化
- 动态文本支持参数插值

### 一致性 ✅
- 与父组件 leavePrice/index.vue 的国际化实现保持一致
- 使用相同的 `useI18n()` 和 `t()` 函数
- 遵循相同的国际化文本组织结构

### 完整性 ✅
- 涵盖了所有用户可见的文本
- 包括错误提示、状态信息、操作按钮等
- 支持复杂的文本插值和条件显示

## 技术实现细节

### 国际化配置结构
```vue
<i18n>
{
  "en": { "selectCoupon": "Select Coupon", ... },
  "zh-cn": { "selectCoupon": "选择优惠券", ... },
  "km": { "selectCoupon": "ជ្រើសរើសគូប៉ុង", ... }
}
</i18n>
```

### 使用方式
```vue
<script setup>
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>

<template>
  <el-dialog :title="t('selectCoupon')">
    <el-button>{{ t('cancel') }}</el-button>
  </template>
```

## 状态：✅ 国际化实现完成
CouponDialog.vue 组件已完全国际化，支持多语言切换，与系统其他组件保持一致。
