{"version": 3, "file": "planCheckinList-BFggSPK_.js", "sources": ["../../src/views/order/inhand/planCheckinList.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"strategyBoard\": \"Strategy Board\",\r\n    \"strategyList\": \"Strategy List\",\r\n    \"priceStrategy\": \"Price Strategy\",\r\n    \"storeSetPriceStrategy\": \"Store sets its own price strategy.\",\r\n    \"channel\": \"Channel\",\r\n    \"search\": \"Precise Search\",\r\n    \"guestSrcType\": \"Guest Type\",\r\n    \"roomType\": \"Room Type\",\r\n    \"roomTypePrice\": \"Room Type Price\",\r\n    \"checkinType\": \"Check-in\",\r\n    \"seller\": \"Sales\",\r\n    \"query\": \"Filter\",\r\n    \"guestPhone\": \"Guest/Phone\",\r\n    \"orderNo\": \"Order No\",\r\n    \"price\": \"Price\",\r\n    \"roomNo\": \"Room No\",\r\n    \"plannedArrivalDeparture\": \"Planned Arrival/Departure\",\r\n    \"checkinTypeName\": \"Check-in Type\",\r\n    \"guestSrcTypeName\": \"Guest Source Type\",\r\n    \"channelName\": \"Channel\",\r\n    \"retainTime\": \"Retain Time\",\r\n    \"remark\": \"Remark\",\r\n    \"operation\": \"Actions\",\r\n    \"view\": \"View\",\r\n    \"checkIn\": \"Check-In\",\r\n    \"cancelOrder\": \"Cancel\",\r\n    \"select\": \"select\",\r\n    \"ID/phone\": \"Order/ID/Phone\",\r\n    \"all\": \"All\",\r\n    \"roomPrice\": \"Room Price\",\r\n    \"guestSourceType\": \"Guest Source\",\r\n    \"waitingForRoom\": \"Waiting For Room\",\r\n    \"guest\": \"Guest:\",\r\n    \"phone\": \"Phone:\",\r\n    \"orderNumber\": \"Order No:\",\r\n    \"externalOrderNumber\": \"External Order No:\",\r\n    \"rooms\": \"R\",\r\n    \"notAssigned\": \"Not Assigned\",\r\n    \"plannedArrival\": \"Arrival:\",\r\n    \"plannedDeparture\": \"Departure:\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"strategyBoard\": \"策略看板\",\r\n    \"strategyList\": \"策略列表\",\r\n    \"priceStrategy\": \"房价策略\",\r\n    \"storeSetPriceStrategy\": \"门店设置自己房价策略。\",\r\n    \"channel\": \"渠道\",\r\n    \"search\": \"精准搜索\",\r\n    \"guestSrcType\": \"客源类型\",\r\n    \"roomType\": \"房型\",\r\n    \"roomTypePrice\": \"房型/间数/房价\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"seller\": \"销售员\",\r\n    \"query\": \"查询\",\r\n    \"guestPhone\": \"客人/电话\",\r\n    \"orderNo\": \"订单号/外部订单号\",\r\n    \"price\": \"房价\",\r\n    \"roomNo\": \"房号\",\r\n    \"plannedArrivalDeparture\": \"预抵/预离\",\r\n    \"checkinTypeName\": \"入住类型\",\r\n    \"guestSrcTypeName\": \"客源类型\",\r\n    \"channelName\": \"渠道\",\r\n    \"retainTime\": \"保留时间\",\r\n    \"remark\": \"备注\",\r\n    \"operation\": \"操作\",\r\n    \"view\": \"查看\",\r\n    \"checkIn\": \"入住\",\r\n    \"cancelOrder\": \"取消订单\",\r\n    \"select\": \"选择\",\r\n    \"ID/phone\": \"订单号、外部订单号、姓名、手机号\",\r\n    \"all\": \"全部\",\r\n    \"roomPrice\": \"房费\",\r\n    \"guestSourceType\": \"客户来源\",\r\n    \"waitingForRoom\": \"排房\",\r\n    \"guest\": \"客人：\",\r\n    \"phone\": \"电话：\",\r\n    \"orderNumber\": \"订单号：\",\r\n    \"externalOrderNumber\": \"外部订单号：\",\r\n    \"rooms\": \"间\",\r\n    \"notAssigned\": \"未排房\",\r\n    \"plannedArrival\": \"预抵：\",\r\n    \"plannedDeparture\": \"预离：\"\r\n  },\r\n  \"km\": {\r\n    \"strategyBoard\": \"ផ្ទាំងយុទ្ធសាស្ត្រ\",\r\n    \"strategyList\": \"បញ្ជីយុទ្ធសាស្ត្រ\",\r\n    \"priceStrategy\": \"យុទ្ធសាស្ត្រតម្លៃ\",\r\n    \"storeSetPriceStrategy\": \"ហាងកំណត់យុទ្ធសាស្ត្រតម្លៃផ្ទាល់ខ្លួន។\",\r\n    \"channel\": \"ឆានែល\",\r\n    \"search\": \"ស្វែងរកច្បាស់លាស់\",\r\n    \"guestSrcType\": \"ប្រភេទភ្ញៀវ\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"roomTypePrice\": \"ប្រភេទបន្ទប់/ចំនួន/តម្លៃ\",\r\n    \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n    \"seller\": \"អ្នកលក់\",\r\n    \"query\": \"ស្វែងរក\",\r\n    \"guestPhone\": \"ភ្ញៀវ/ទូរស័ព្ទ\",\r\n    \"orderNo\": \"លេខកម្មងែ/លេខកម្មងែខាងក្រៅ\",\r\n    \"price\": \"តម្លៃ\",\r\n    \"roomNo\": \"លេខបន្ទប់\",\r\n    \"plannedArrivalDeparture\": \"គ្រោងមក/គ្រោងចេញ\",\r\n    \"checkinTypeName\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n    \"guestSrcTypeName\": \"ប្រភេទប្រភពភ្ញៀវ\",\r\n    \"channelName\": \"ឆានែល\",\r\n    \"retainTime\": \"ពេលវេលារក្សាទុក\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"view\": \"មើល\",\r\n    \"checkIn\": \"ចូលស្នាក់នៅ\",\r\n    \"cancelOrder\": \"បោះបង់កម្មងែ\",\r\n    \"select\": \"ជ្រើសរើស\",\r\n    \"ID/phone\": \"លេខកម្មងែ/លេខសម្គាល់/ទូរស័ព្ទ\",\r\n    \"all\": \"ទាំងអស់\",\r\n    \"roomPrice\": \"តម្លៃបន្ទប់\",\r\n    \"guestSourceType\": \"ប្រភពភ្ញៀវ\",\r\n    \"waitingForRoom\": \"រៀបចំបន្ទប់\",\r\n    \"guest\": \"ភ្ញៀវ៖\",\r\n    \"phone\": \"ទូរស័ព្ទ៖\",\r\n    \"orderNumber\": \"លេខកម្មងែ៖\",\r\n    \"externalOrderNumber\": \"លេខកម្មងែខាងក្រៅ៖\",\r\n    \"rooms\": \"បន្ទប់\",\r\n    \"notAssigned\": \"មិនទាន់រៀបចំ\",\r\n    \"plannedArrival\": \"គ្រោងមក៖\",\r\n    \"plannedDeparture\": \"គ្រោងចេញ៖\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { bookApi, channelApi, dictDataApi, rtApi, userApi } from '@/api/modules/index'\r\nimport { BooleanEnum, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE, NoType, OrderType } from '@/models'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nimport CancelPopUP from '@/views/order/component/cancelPopUP.vue'\r\nimport OrderDialog from '@/views/order/info/order.vue'\r\nimport { InfoFilled, Search } from '@element-plus/icons-vue'\r\n\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  tableAutoHeight: false,\r\n  formMode: 'dialog' as 'router' | 'dialog' | 'drawer',\r\n  formModeProps: {\r\n    visible: false,\r\n    bookNo: '',\r\n    bookType: 'book',\r\n  },\r\n  search: {\r\n    channelCode: '-1',\r\n    rtCode: '',\r\n    guestSrcType: '',\r\n    checkinType: '',\r\n    seller: '',\r\n    searchType: '0',\r\n    searchContent: '',\r\n  },\r\n  dataList: [],\r\n})\r\n\r\nonMounted(() => {\r\n  getChannels()\r\n  getRts()\r\n  getConstants()\r\n  getSeller()\r\n  getDataList()\r\n})\r\n\r\nconst noType = ref('book')\r\n\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst rts = ref<{ rtCode: string; rtName: string }[]>([])\r\nfunction getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isEnable: BooleanEnum.YES,\r\n    isVirtual: BooleanEnum.NO,\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst dictTypes = [DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_CHECKIN_TYPE]\r\nconst srcTypeList = ref<{ code: string; label: string }[]>([])\r\nconst checkinTypeList = ref<{ code: string; label: string }[]>([])\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    srcTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n    checkinTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n  })\r\n}\r\n\r\nconst sellers = ref<{ username: string; nickname: string }[]>([])\r\nfunction getSeller() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  userApi.listSeller(params).then((res: any) => {\r\n    sellers.value = res.data\r\n  })\r\n}\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    keyWords: data.value.search.searchContent,\r\n    channelCode: data.value.search.channelCode === '-1' ? '' : data.value.search.channelCode,\r\n    guestSrcType: data.value.search.guestSrcType,\r\n    rtCode: data.value.search.rtCode,\r\n    checkinType: data.value.search.checkinType,\r\n    seller: data.value.search.seller,\r\n  }\r\n  bookApi.todayArriveList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    if (res.data.list) {\r\n      data.value.dataList = res.data.list\r\n      pagination.value.total = res.data.total\r\n    }\r\n  })\r\n}\r\n\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nconst routerName = ref('detail')\r\nconst typeName = ref('individual')\r\nfunction onEdit(row: any) {\r\n  data.value.formModeProps.bookNo = row.bookNo\r\n\r\n  if (row.bookType === OrderType.GROUP) {\r\n    noType.value = NoType.TEAM\r\n  } else {\r\n    noType.value = NoType.BOOK\r\n  }\r\n\r\n  data.value.formModeProps.visible = true\r\n}\r\nconst upVisible = ref(false)\r\nfunction orderCancel(row: any) {\r\n  data.value.formModeProps.bookNo = row.bookNo\r\n  upVisible.value = true\r\n}\r\n\r\nfunction firstNonNullRNo(value: any) {\r\n  const room = value.find((room: any) => room.rNo !== null)\r\n  return room ? room.rNo : ''\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <search-bar :show-toggle=\"false\">\r\n        <el-form :model=\"data.search\" size=\"default\" label-width=\"80px\" inline-message inline class=\"search-form\">\r\n          <div class=\"filter-row\">\r\n            <el-form-item :label=\"t('channel')\">\r\n              <el-select v-model=\"data.search.channelCode\" clearable class=\"filter-select\">\r\n                <el-option :label=\"t('all')\" value=\"-1\" />\r\n                <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('guestSrcType')\">\r\n              <el-select v-model=\"data.search.guestSrcType\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in srcTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('roomType')\">\r\n              <el-select v-model=\"data.search.rtCode\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"filter-row\">\r\n            <el-form-item :label=\"t('checkinType')\">\r\n              <el-select v-model=\"data.search.checkinType\" clearable class=\"filter-select\" :placeholder=\"t('select')\">\r\n                <el-option v-for=\"item in checkinTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('seller')\">\r\n              <el-select v-model=\"data.search.seller\" clearable class=\"filter-select\" :placeholder=\"t('select')\">\r\n                <el-option v-for=\"i in sellers\" :key=\"i.username\" :label=\"i.nickname\" :value=\"i.username\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('search')\" class=\"search-input-item\">\r\n              <el-input v-model=\"data.search.searchContent\" class=\"filter-select w-350px!\" clearable :placeholder=\"t('ID/phone')\">\r\n                <template #append>\r\n                  <el-button :icon=\"Search\" @click=\"currentChange()\" />\r\n                </template>\r\n              </el-input>\r\n              <el-button type=\"primary\" class=\"query-button\" @click=\"getDataList\">\r\n                {{ t('query') }}\r\n              </el-button>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </search-bar>\r\n      <el-table v-loading=\"data.loading\" class=\"list-table\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\" @sort-change=\"sortChange\">\r\n        <el-table-column :label=\"t('guestPhone')\" min-width=\"155\">\r\n          <template #default=\"scope\">\r\n            <div class=\"guest-info\">\r\n              <div>{{ scope.row.contact }}</div>\r\n              <div v-if=\"scope.row.phone\">\r\n                {{ scope.row.phone }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('orderNo')\" min-width=\"190\">\r\n          <template #default=\"scope\">\r\n            <div class=\"order-info\">\r\n              <div>\r\n                <span class=\"label\">{{ t('orderNumber') }}</span\r\n                >{{ scope.row.bookNo }}\r\n              </div>\r\n              <div v-if=\"scope.row.outOrderNo\">\r\n                <span class=\"label\">{{ t('externalOrderNumber') }}</span\r\n                >{{ scope.row.outOrderNo }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- 房型价格列 -->\r\n        <el-table-column :label=\"t('roomTypePrice')\" min-width=\"200\">\r\n          <template #default=\"scope\">\r\n            <div v-for=\"item in scope.row.roomTypeClass\" :key=\"item.rtCode\" class=\"room-info\">\r\n              <span class=\"room-type-tag\">{{ item.rtName }} {{ item.num }}{{ t('rooms') }}</span>\r\n              <span class=\"price\">￥{{ item.price ?? '-' }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- 房号列 -->\r\n        <el-table-column :label=\"t('roomNo')\" min-width=\"110\">\r\n          <template #default=\"scope\">\r\n            <el-popover placement=\"bottom\" width=\"300px\" trigger=\"hover\">\r\n              <template #reference>\r\n                <div class=\"room-no-cell\">\r\n                  <span>{{ firstNonNullRNo(scope.row.roomTypes) || t('notAssigned') }}</span>\r\n                  <el-icon class=\"info-icon\">\r\n                    <InfoFilled />\r\n                  </el-icon>\r\n                </div>\r\n              </template>\r\n              <el-table :data=\"scope.row.roomTypes\">\r\n                <el-table-column property=\"rtName\" :label=\"t('roomType')\" />\r\n                <el-table-column :label=\"t('roomNo')\">\r\n                  <template #default=\"scope\">\r\n                    <span>{{ scope.row.rNo || t('notAssigned') }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-popover>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- 预抵预离时间列 -->\r\n        <el-table-column :label=\"t('plannedArrivalDeparture')\" min-width=\"170\">\r\n          <template #default=\"scope\">\r\n            <div class=\"time-info\">\r\n              <span class=\"label\">{{ t('plannedArrival') }}</span\r\n              >{{ scope.row.planCheckinTime }}\r\n            </div>\r\n            <div class=\"time-info\">\r\n              <span class=\"label\">{{ t('plannedDeparture') }}</span\r\n              >{{ scope.row.planCheckoutTime }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"checkinTypeName\" :label=\"t('checkinType')\" min-width=\"80\" />\r\n        <el-table-column prop=\"guestSrcTypeName\" :label=\"t('guestSourceType')\" min-width=\"110\" />\r\n        <el-table-column prop=\"channelName\" :label=\"t('channel')\" min-width=\"90\" />\r\n        <el-table-column prop=\"remark\" :label=\"t('remark')\" min-width=\"220\" />\r\n        <el-table-column :label=\"t('operation')\" align=\"center\" fixed=\"right\" min-width=\"150\">\r\n          <template #default=\"scope\">\r\n            <el-link v-auth=\"'pms:order:query:get-order-detail'\" type=\"primary\" @click=\"onEdit(scope.row)\">\r\n              {{ t('view') }}\r\n            </el-link>\r\n            <el-link v-auth=\"'pms:order:create:check-in'\" type=\"primary\" @click=\"onEdit(scope.row)\">\r\n              {{ t('checkIn') }}\r\n            </el-link>\r\n            <el-link v-if=\"scope.row.state !== 'cancel'\" v-auth=\"'pms:book:update:cancel'\" type=\"danger\" @click=\"orderCancel(scope.row)\">\r\n              {{ t('cancelOrder') }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n      <OrderDialog v-if=\"data.formModeProps.visible\" v-model=\"data.formModeProps.visible\" :no=\"data.formModeProps.bookNo\" :no-type=\"noType\" :tab-name=\"routerName\" :tab-type=\"typeName\" @reload=\"getDataList\" />\r\n      <CancelPopUP v-if=\"upVisible\" v-model=\"upVisible\" :book-no=\"data.formModeProps.bookNo\" @success=\"getDataList\" />\r\n    </page-main>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-link {\r\n  margin: 0 10px;\r\n}\r\n\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    .filter-row {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      margin-bottom: 8px;\r\n      align-items: center;\r\n\r\n      .el-form-item {\r\n        margin-right: 12px;\r\n        margin-bottom: 8px;\r\n\r\n        // 增加标签宽度以适应英文文本\r\n        :deep(.el-form-item__label) {\r\n          padding-bottom: 0;\r\n          line-height: 28px;\r\n          min-width: 100px; // 增加最小宽度\r\n          white-space: nowrap; // 防止标签换行\r\n        }\r\n\r\n        .filter-select {\r\n          width: 220px; // 增加选择框宽度\r\n          min-width: 180px; // 设置最小宽度\r\n        }\r\n\r\n        :deep(.el-form-item__content) {\r\n          line-height: 28px;\r\n        }\r\n\r\n        &.search-input-item {\r\n          flex-grow: 1;\r\n          min-width: 300px; // 调整最小宽度以适应200px输入框\r\n\r\n          :deep(.el-form-item__content) {\r\n            display: flex;\r\n            align-items: center;\r\n            flex-wrap: wrap; // 允许换行\r\n            gap: 8px; // 添加间距\r\n          }\r\n\r\n          .filter-select {\r\n            width: 200px; // 设置为200px\r\n            min-width: 200px; // 最小宽度也设置为200px\r\n          }\r\n\r\n          .query-button {\r\n            margin-left: 0; // 移除左边距\r\n            min-width: 80px;\r\n            flex-shrink: 0; // 防止按钮被压缩\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.flex-form {\r\n  display: flex;\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "t", "useI18n", "data", "ref", "loading", "tableAutoHeight", "formMode", "formModeProps", "visible", "bookNo", "bookType", "search", "channelCode", "rtCode", "guestSrcType", "checkinType", "seller", "searchType", "searchContent", "dataList", "onMounted", "params", "gcode", "hcode", "isEnable", "BooleanEnum", "YES", "channelApi", "getChannelSimpleList", "then", "res", "code", "channels", "value", "getChannels", "isVirtual", "NO", "rtApi", "getRoomTypeSimpleList", "rts", "getRts", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "srcTypeList", "filter", "item", "dictType", "DICT_TYPE_GUEST_SRC_TYPE", "checkinTypeList", "DICT_TYPE_CHECKIN_TYPE", "userApi", "listSeller", "sellers", "getSeller", "getDataList", "noType", "key<PERSON>ords", "bookApi", "todayArriveList", "list", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "routerName", "typeName", "onEdit", "row", "OrderType", "GROUP", "NoType", "TEAM", "BOOK", "upVisible", "firstNonNullRNo", "room", "find", "rNo"], "mappings": "u6KA4IA,MAAMA,EAAYC,KACZC,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,mBAAcC,GAAiBC,aAAAA,IAAiBC,KAEzEC,EAAEA,IAAMC,IAERC,GAAOC,EAAI,CACfC,SAAS,EACTC,iBAAiB,EACjBC,SAAU,SACVC,cAAe,CACbC,SAAS,EACTC,OAAQ,GACRC,SAAU,QAEZC,OAAQ,CACNC,YAAa,KACbC,OAAQ,GACRC,aAAc,GACdC,YAAa,GACbC,OAAQ,GACRC,WAAY,IACZC,cAAe,IAEjBC,SAAU,KAGZC,GAAU,MAWV,WACE,MAAMC,EAAS,CACbC,MAAO9B,EAAU8B,MACjBC,MAAO/B,EAAU+B,MACjBC,SAAUC,EAAYC,KAExBC,EAAWC,qBAAqBP,GAAQQ,MAAMC,IAC3B,IAAbA,EAAIC,OACNC,GAASC,MAAQH,EAAI5B,KAAA,GAExB,CApBWgC,GAwBd,WACE,MAAMb,EAAS,CACbC,MAAO9B,EAAU8B,MACjBC,MAAO/B,EAAU+B,MACjBC,SAAUC,EAAYC,IACtBS,UAAWV,EAAYW,IAEzBC,EAAMC,sBAAsBjB,GAAQQ,MAAMC,IACvB,IAAbA,EAAIC,OACNQ,GAAIN,MAAQH,EAAI5B,KAAA,GAEnB,CAlCMsC,GAyCPC,EAAYC,iBAAiBC,IAAWd,MAAMC,IAChCc,GAAAX,MAAQH,EAAI5B,KAAK2C,QAAQC,GAAcA,EAAKC,WAAaC,IACrDC,GAAAhB,MAAQH,EAAI5B,KAAK2C,QAAQC,GAAcA,EAAKC,WAAaG,GAAsB,IAKnG,WACE,MAAM7B,EAAS,CACbC,MAAO9B,EAAU8B,MACjBC,MAAO/B,EAAU+B,OAEnB4B,EAAQC,WAAW/B,GAAQQ,MAAMC,IAC/BuB,GAAQpB,MAAQH,EAAI5B,IAAA,GACrB,CArDSoD,GACEC,IAAA,IAGR,MAAAC,GAASrD,EAAI,QAEb6B,GAAW7B,EAAoD,IAc/D,MAAAoC,GAAMpC,EAA0C,IAehD,MAAAwC,GAAY,CAACK,EAA0BE,GACvCN,GAAczC,EAAuC,IACrD8C,GAAkB9C,EAAuC,IAQzD,MAAAkD,GAAUlD,EAA8C,IAW9D,SAASoD,KACPrD,GAAK+B,MAAM7B,SAAU,EACrB,MAAMiB,EAAS,IACV1B,KACH2B,MAAO9B,EAAU8B,MACjBC,MAAO/B,EAAU+B,MACjBkC,SAAUvD,GAAK+B,MAAMtB,OAAOO,cAC5BN,YAA+C,OAAlCV,GAAK+B,MAAMtB,OAAOC,YAAuB,GAAKV,GAAK+B,MAAMtB,OAAOC,YAC7EE,aAAcZ,GAAK+B,MAAMtB,OAAOG,aAChCD,OAAQX,GAAK+B,MAAMtB,OAAOE,OAC1BE,YAAab,GAAK+B,MAAMtB,OAAOI,YAC/BC,OAAQd,GAAK+B,MAAMtB,OAAOK,QAE5B0C,EAAQC,gBAAgBtC,GAAQQ,MAAMC,IACpC5B,GAAK+B,MAAM7B,SAAU,EACjB0B,EAAI5B,KAAK0D,OACN1D,GAAA+B,MAAMd,SAAWW,EAAI5B,KAAK0D,KACpBlE,GAAAuC,MAAM4B,MAAQ/B,EAAI5B,KAAK2D,MAAA,GAErC,CAGH,SAASC,GAAWC,GAClBnE,GAAamE,GAAMlC,MAAK,IAAM0B,MAAa,CAGpC,SAAAS,GAAcC,EAAO,GAC5BpE,GAAgBoE,GAAMpC,MAAK,IAAM0B,MAAa,CAGhD,SAASW,IAAWC,KAAEA,EAAMC,MAAAA,IAC1BtE,GAAaqE,EAAMC,GAAOvC,MAAK,IAAM0B,MAAa,CAG9C,MAAAc,GAAalE,EAAI,UACjBmE,GAAWnE,EAAI,cACrB,SAASoE,GAAOC,GACTtE,GAAA+B,MAAM1B,cAAcE,OAAS+D,EAAI/D,OAElC+D,EAAI9D,WAAa+D,EAAUC,MAC7BlB,GAAOvB,MAAQ0C,EAAOC,KAEtBpB,GAAOvB,MAAQ0C,EAAOE,KAGnB3E,GAAA+B,MAAM1B,cAAcC,SAAU,CAAA,CAE/B,MAAAsE,GAAY3E,GAAI,GAMtB,SAAS4E,GAAgB9C,GACvB,MAAM+C,EAAO/C,EAAMgD,MAAMD,GAA2B,OAAbA,EAAKE,MACrC,OAAAF,EAAOA,EAAKE,IAAM,EAAA,+6KAPNV,QACdtE,GAAA+B,MAAM1B,cAAcE,OAAS+D,EAAI/D,YACtCqE,GAAU7C,OAAQ,GAFpB,IAAqBuC"}