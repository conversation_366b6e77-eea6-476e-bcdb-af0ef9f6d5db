{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-kAbxK0Ex.js", "sources": ["../../src/layouts/components/Topbar/Toolbar/Fullscreen/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport { useFullscreen } from '@vueuse/core'\r\n\r\ndefineOptions({\r\n  name: 'Fullscreen',\r\n})\r\n\r\nconst settingsStore = useSettingsStore()\r\n\r\nconst { isFullscreen, toggle } = useFullscreen()\r\n</script>\r\n\r\n<template>\r\n  <span v-if=\"settingsStore.mode === 'pc'\" class=\"flex-center cursor-pointer p-2\" @click=\"toggle\">\r\n    <SvgIcon :name=\"isFullscreen ? 'i-ri:fullscreen-exit-line' : 'i-ri:fullscreen-line'\" />\r\n  </span>\r\n</template>\r\n"], "names": ["settingsStore", "useSettingsStore", "isFullscreen", "toggle", "useFullscreen"], "mappings": "sLAQA,MAAMA,EAAgBC,KAEhBC,aAAEA,EAAAC,OAAcA,GAAWC"}