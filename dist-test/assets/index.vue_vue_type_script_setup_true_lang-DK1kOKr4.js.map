{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-DK1kOKr4.js", "sources": ["../../src/components/PageMain/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\ndefineOptions({\r\n  name: 'PageMain',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    title?: string\r\n    collaspe?: boolean\r\n    height?: string\r\n  }>(),\r\n  {\r\n    title: '',\r\n    collaspe: false,\r\n    height: '',\r\n  }\r\n)\r\n\r\nconst titleSlot = !!useSlots().title\r\n\r\nconst isCollaspe = ref(props.collaspe)\r\nfunction unCollaspe() {\r\n  isCollaspe.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div\r\n    class=\"page-main relative m-4 flex flex-col bg-[var(--g-container-bg)] transition-background-color-300\"\r\n    :class=\"{\r\n      'of-hidden': isCollaspe,\r\n    }\"\r\n    :style=\"{\r\n      height: isCollaspe ? height : '',\r\n    }\"\r\n  >\r\n    <div\r\n      v-if=\"titleSlot || title\"\r\n      class=\"title-container border-b-1 border-b-[var(--g-bg)] border-b-solid px-5 py-4 transition-border-color-300\"\r\n    >\r\n      <slot name=\"title\">\r\n        {{ title }}\r\n      </slot>\r\n    </div>\r\n    <div class=\"main-container p-5\">\r\n      <slot />\r\n    </div>\r\n    <div\r\n      v-if=\"isCollaspe\"\r\n      class=\"collaspe absolute bottom-0 w-full cursor-pointer from-transparent to-[var(--g-container-bg)] bg-gradient-to-b pb-2 pt-10 text-center\"\r\n      @click=\"unCollaspe\"\r\n    >\r\n      <SvgIcon\r\n        name=\"i-ep:arrow-down\"\r\n        class=\"text-xl op-30 transition-opacity hover-op-100\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "titleSlot", "useSlots", "title", "is<PERSON><PERSON><PERSON><PERSON>", "ref", "collas<PERSON>", "unCollaspe", "value"], "mappings": "saAKA,MAAMA,EAAQC,EAaRC,IAAcC,IAAWC,MAEzBC,EAAaC,EAAIN,EAAMO,UAC7B,SAASC,IACPH,EAAWI,OAAQ,CAAA"}