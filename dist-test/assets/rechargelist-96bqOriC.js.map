{"version": 3, "file": "rechargelist-96bqOriC.js", "sources": ["../../src/api/modules/pms/arset/accOutIn.api.ts", "../../src/views/merchant-finance/ar/acc-verify/rechargelist.vue"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/acc-out-in'\r\n/**\r\n * 单位中介出入账记录\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 创建单位中介出入账记录\r\n   * @param data\r\n   */\r\n  createAccOutIn(data: any) {\r\n    return api.post(`${BASE_PATH}/create`, data)\r\n  },\r\n\r\n  /**\r\n   * 更新单位中介出入账记录\r\n   * @param data\r\n   */\r\n  updateAccOutIn(data: any) {\r\n    return api.put(`${BASE_PATH}/update`, data)\r\n  },\r\n  /**\r\n   * 获得单位中介出入账记录分页\r\n   * @param data\r\n   */\r\n  // getAccOutInPage(data: any) {\r\n  //     return api.get(`${BASE_PATH}/page`, data);\r\n  // },\r\n  getAccOutInPage: (data: any) => api.get('/admin-api/pms/acc-out-in/page', { params: data }),\r\n}\r\n", "<i18n>\r\n{\r\n  \"en\": {\r\n    \"subject\": \"Subject\",\r\n    \"receiptAmount\": \"Receipt Amount\",\r\n    \"receiptTime\": \"Receipt Time\",\r\n    \"remark\": \"Remark\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"subject\": \"科目\",\r\n    \"receiptAmount\": \"收款金额\",\r\n    \"receiptTime\": \"收款时间\",\r\n    \"remark\": \"备注\"\r\n  },\r\n  \"km\": {\r\n    \"subject\": \"ប្រធានបទ\",\r\n    \"receiptAmount\": \"ចំនួនទឹកប្រាក់ទទួល\",\r\n    \"receiptTime\": \"ពេលវេលាទទួល\",\r\n    \"remark\": \"ចំណាំ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { accOutInApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst p = defineProps<{\r\n  arSetCode: string\r\n}>()\r\n\r\nconst { t } = useI18n()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n  dataList: [],\r\n})\r\nonMounted(() => {\r\n  getDataList()\r\n})\r\n\r\ndefineExpose({\r\n  getDataList,\r\n})\r\n\r\nfunction getDataList() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    arSetCode: p.arSetCode,\r\n    accType: '1',\r\n    ...getParams(),\r\n  }\r\n  accOutInApi.getAccOutInPage(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      data.value.dataList = res.data.list\r\n      pagination.value.total = res.data.total\r\n    }\r\n  })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" highlight-current-row height=\"100%\">\r\n      <el-table-column prop=\"subName\" :label=\"t('subject')\" />\r\n      <el-table-column :label=\"t('receiptAmount')\" align=\"right\">\r\n        <template #default=\"{ row }\">\r\n          <span>￥{{ row.fee }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('receiptTime')\">\r\n        <template #default=\"{ row }\">\r\n          <span>{{ row.creator }}</span>\r\n          <br />\r\n          <span>{{ row.createTime }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"remark\" :label=\"t('remark')\" />\r\n    </el-table>\r\n    <el-pagination\r\n      v-if=\"pagination.total > 10\"\r\n      :current-page=\"pagination.pageNo\"\r\n      :total=\"pagination.total\"\r\n      :page-size=\"pagination.pageSize\"\r\n      :page-sizes=\"pagination.sizes\"\r\n      :layout=\"pagination.layout\"\r\n      :hide-on-single-page=\"false\"\r\n      class=\"pagination\"\r\n      background\r\n      @size-change=\"sizeChange\"\r\n      @current-change=\"currentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["BASE_PATH", "accOutInApi", "createAccOutIn", "data", "api", "post", "updateAccOutIn", "put", "getAccOutInPage", "get", "params", "p", "__props", "t", "useI18n", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "userStore", "useUserStore", "ref", "loading", "tableAutoHeight", "dataList", "getDataList", "gcode", "hcode", "arSetCode", "accType", "then", "res", "code", "value", "list", "total", "sizeChange", "size", "currentChange", "page", "onMounted", "__expose"], "mappings": "2mBAEA,MAAMA,EAAY,2BAIHC,EAAA,CAMbC,eAAeC,GACNC,EAAIC,KAAK,GAAGL,WAAoBG,GAOzCG,eAAeH,GACNC,EAAIG,IAAI,GAAGP,WAAoBG,GASxCK,gBAAkBL,GAAcC,EAAIK,IAAI,iCAAkC,CAAEC,OAAQP,yECHtF,MAAMQ,EAAIC,GAIJC,EAAEA,GAAMC,KACRC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IACzEC,EAAYC,IACZnB,EAAOoB,EAAI,CACfC,SAAS,EAETC,iBAAiB,EACjBC,SAAU,KAUZ,SAASC,IACP,MAAMjB,EAAS,CACbkB,MAAOP,EAAUO,MACjBC,MAAOR,EAAUQ,MACjBC,UAAWnB,EAAEmB,UACbC,QAAS,OACNf,KAELf,EAAYO,gBAAgBE,GAAQsB,MAAMC,IACvB,IAAbA,EAAIC,OACD/B,EAAAgC,MAAMT,SAAWO,EAAI9B,KAAKiC,KACpBrB,EAAAoB,MAAME,MAAQJ,EAAI9B,KAAKkC,MAAA,GAErC,CAIH,SAASC,EAAWC,GAClBtB,EAAasB,GAAMP,MAAK,IAAML,KAAa,CAIpC,SAAAa,EAAcC,EAAO,GAC5BvB,EAAgBuB,GAAMT,MAAK,IAAML,KAAa,QA/BhDe,GAAU,KACIf,GAAA,IAGDgB,EAAA,CACXhB"}