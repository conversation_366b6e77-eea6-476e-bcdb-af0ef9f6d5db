{"version": 3, "file": "arrangeRts-C83hWsgy.js", "sources": ["../../src/views/room/components/arrangeRts/arrangeRts.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"addRoom\": \"Add Room\",\r\n      \"expectedArrival\": \"Arrival\",\r\n      \"expectedDeparture\": \"Departure\",\r\n      \"days\": \"Days\",\r\n      \"checkinDate\": \"Check-in Date\",\r\n      \"checkoutDate\": \"Check-out Date\",\r\n      \"daysPlaceholder\": \"Days\",\r\n      \"roomType\": \"Room Type\",\r\n      \"discountPrice\": \"Discount Price\",\r\n      \"sellingPrice\": \"Selling Price\",\r\n      \"changePrice\": \"Change Price\",\r\n      \"availableRooms\": \"Available Rooms\",\r\n      \"overbookingLimit\": \"Overbooking Limit\",\r\n      \"bookedRooms\": \"Booked Rooms\",\r\n      \"actions\": \"Actions\",\r\n      \"arrangeRoom\": \"Arrange Room\",\r\n      \"cancel\": \"Cancel\",\r\n      \"confirm\": \"Confirm\",\r\n      \"notArranged\": \"Not Arranged\",\r\n      \"getRoomListFailed\": \"Failed to get room list\",\r\n      \"setTimeFirst\": \"Please set the expected arrival and departure times first, and make sure the departure time is later than the arrival time.\",\r\n      \"selectRoomCountFirst\": \"Please select the number of rooms to book for the current room type first\",\r\n      \"selectBookedRooms\": \"Please select the number of booked rooms\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"addRoom\": \"添加房间\",\r\n      \"expectedArrival\": \"预抵\",\r\n      \"expectedDeparture\": \"预离\",\r\n      \"days\": \"天数\",\r\n      \"checkinDate\": \"入住日期\",\r\n      \"checkoutDate\": \"离店日期\",\r\n      \"daysPlaceholder\": \"天数\",\r\n      \"roomType\": \"房型\",\r\n      \"discountPrice\": \"优惠价\",\r\n      \"sellingPrice\": \"售价\",\r\n      \"changePrice\": \"改价\",\r\n      \"availableRooms\": \"可售数\",\r\n      \"overbookingLimit\": \"可超数\",\r\n      \"bookedRooms\": \"预订间数\",\r\n      \"actions\": \"操作\",\r\n      \"arrangeRoom\": \"排房\",\r\n      \"cancel\": \"取消\",\r\n      \"confirm\": \"确定\",\r\n      \"notArranged\": \"未排房\",\r\n      \"getRoomListFailed\": \"获取房间列表失败\",\r\n      \"setTimeFirst\": \"请先设置预抵和预离时间，并使预离时间晚于预抵时间.\",\r\n      \"selectRoomCountFirst\": \"请先选择当前房型预订间数\",\r\n      \"selectBookedRooms\": \"请选择预订间数\"\r\n    },\r\n  \"km\": {\r\n    \"addRoom\": \"បន្ថែមបន្ទប់\",\r\n    \"expectedArrival\": \"ពេលមកដល់\",\r\n    \"expectedDeparture\": \"ពេលចាកចេញ\",\r\n    \"days\": \"ថ្ងៃ\",\r\n    \"checkinDate\": \"កាលបរិច្ឆេទចូលស្នាក់នៅ\",\r\n    \"checkoutDate\": \"កាលបរិច្ឆេទចាកចេញ\",\r\n    \"daysPlaceholder\": \"ថ្ងៃ\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"discountPrice\": \"តម្លៃបញ្ចុះតម្លៃ\",\r\n    \"sellingPrice\": \"តម្លៃលក់\",\r\n    \"changePrice\": \"ផ្លាស់ប្តូរតម្លៃ\",\r\n    \"availableRooms\": \"បន្ទប់ដែលអាចលក់បាន\",\r\n    \"overbookingLimit\": \"ដែនកំណត់កក់លើស\",\r\n    \"bookedRooms\": \"ចំនួនបន្ទប់ដែលបានកក់\",\r\n    \"actions\": \"សកម្មភាព\",\r\n    \"arrangeRoom\": \"រៀបចំបន្ទប់\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"confirm\": \"បញ្ជាក់\",\r\n    \"notArranged\": \"មិនបានរៀបចំ\",\r\n    \"getRoomListFailed\": \"មិនអាចទាញយកបញ្ជីបន្ទប់បាន\",\r\n    \"setTimeFirst\": \"សូមកំណត់ពេលមកដល់ និងពេលចាកចេញជាមុនសិន ហើយធ្វើឱ្យពេលចាកចេញក្រោយពេលមកដល់។\",\r\n    \"selectRoomCountFirst\": \"សូមជ្រើសរើសចំនួនបន្ទប់ដែលត្រូវកក់សម្រាប់ប្រភេទបន្ទប់បច្ចុប្បន្នជាមុនសិន\",\r\n    \"selectBookedRooms\": \"សូមជ្រើសរើសចំនួនបន្ទប់ដែលបានកក់\"\r\n  }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { BookRoomsModel } from '@/models'\r\nimport type individual from '@/views/room/booking/individual'\r\nimport { bookApi, generalConfigApi, serverTimeApi } from '@/api/modules/index'\r\nimport { ChannelEnum, GeneralConfigTypeEnum } from '@/models'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nimport ymdate from '@/utils/timeutils.ts'\r\nimport ArrangeRoomsDialog from '@/views/room/components/arrangeRooms/arrangeRooms.vue'\r\nimport RoomEditPrice from '@/views/room/components/roomEditPrice/index.vue'\r\nimport dayjs from 'dayjs'\r\n\r\ndefineOptions({\r\n  name: 'ArrangeRtsList',\r\n})\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    channelCode?: string\r\n    checkinType?: string\r\n    guestSrcType?: string\r\n    guestCode?: string\r\n    mtCode?: string\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    channelCode: 'lobby',\r\n    checkinType: '',\r\n    guestSrcType: '',\r\n    guestCode: '',\r\n    mtCode: '',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst myStartHour = ref('')\r\nconst myEndHour = ref('')\r\n/** 公用参数 */\r\nconst queryParams = reactive({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n})\r\n/** 服务器时间 */\r\nconst serverTime = ref()\r\n/** 设置的时间 */\r\nconst setTime = ref()\r\nconst earliestCheckinTime = ref('')\r\nconst form = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n  /** 客源类型 */\r\n  guestSrcType: props.guestSrcType,\r\n  /** 会员代码、协议单位、中介代码、团队代码 */\r\n  guestCode: props.guestCode,\r\n  /** 订单来源 */\r\n  orderSource: ChannelEnum.LOBBY,\r\n  planCheckinTime: dayjs().format('YYYY-MM-DD HH:mm'),\r\n  planCheckoutTime: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm'),\r\n  days: 1,\r\n  channelCode: props.channelCode,\r\n  dataList: [] as BookRoomsModel[],\r\n})\r\nconst controls = ref(false)\r\nconst rooms = ref([])\r\nonMounted(async () => {\r\n  await fetchTime()\r\n  await getTime()\r\n})\r\n\r\n/** 最晚退房时间 */\r\nasync function getTime() {\r\n  const { data } = await generalConfigApi.getCheckOutTime(queryParams)\r\n  myEndHour.value = data.value\r\n  form.value.planCheckoutTime = `${dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')} ${myEndHour.value}`\r\n  bookingRoomsList()\r\n}\r\n\r\n/** 获取服务器进行比较作为当前时间 */\r\nasync function fetchTime() {\r\n  const { data } = await serverTimeApi.serverTime(userStore.gcode, '0')\r\n  const res = await generalConfigApi.getGeneralConfig({\r\n    ...queryParams,\r\n    code: GeneralConfigTypeEnum.CHECKIN_TIME,\r\n    type: GeneralConfigTypeEnum.CHECKIN_TIME,\r\n  })\r\n  serverTime.value = data\r\n  // 获取服务器时分\r\n  const now = dayjs(serverTime.value).format('HH:mm')\r\n  // 设置的时分\r\n  setTime.value = res.data.value\r\n  // 判断是否大于\r\n  if (now > setTime.value) {\r\n    form.value.planCheckinTime = dayjs(serverTime.value).format('YYYY-MM-DD HH:mm')\r\n  } else {\r\n    form.value.planCheckinTime = `${dayjs(serverTime.value).format('YYYY-MM-DD')} ${setTime.value}`\r\n  }\r\n  myStartHour.value = dayjs(form.value.planCheckinTime).format('HH:mm')\r\n}\r\n/** 选择预抵时间 */\r\nfunction changeCheckinTime(val: Date) {\r\n  // 选择当前的时间\r\n  const specificDate = dayjs(val).format('YYYY-MM-DD')\r\n  // 今天日期\r\n  const today = dayjs(serverTime.value)\r\n  // 是否今天\r\n  const isToday = today.isSame(specificDate, 'day')\r\n  if (isToday) {\r\n    // 获取服务器时分\r\n    const now = today.format('HH:mm')\r\n    // 判断是否大于\r\n    if (now > setTime.value) {\r\n      form.value.planCheckinTime = today.format('YYYY-MM-DD HH:mm')\r\n    } else {\r\n      form.value.planCheckinTime = `${today.format('YYYY-MM-DD')} ${setTime.value}`\r\n    }\r\n    myStartHour.value = dayjs(form.value.planCheckinTime).format('HH:mm')\r\n  } else {\r\n    form.value.planCheckinTime = `${specificDate} ${setTime.value}`\r\n  }\r\n  form.value.planCheckoutTime = `${dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')} ${myEndHour.value}`\r\n  if (dayjs(form.value.planCheckinTime).format('YYYY-MM-DD') >= dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')) {\r\n    form.value.planCheckoutTime = `${dayjs(form.value.planCheckinTime).add(1, 'day').format('YYYY-MM-DD')} ${myEndHour.value}`\r\n  }\r\n\r\n  // 计算天数\r\n  const checkInDate = dayjs(ymdate(form.value.planCheckinTime))\r\n  const checkOutDate = dayjs(ymdate(form.value.planCheckoutTime))\r\n  // 如果预抵时间点小于最早入住时间，则天数加1\r\n  if (myStartHour.value < earliestCheckinTime.value) {\r\n    form.value.days = Math.abs(checkOutDate.diff(checkInDate, 'days')) + 1\r\n  } else {\r\n    form.value.days = Math.abs(checkOutDate.diff(checkInDate, 'days'))\r\n  }\r\n\r\n  bookingRoomsList()\r\n}\r\n\r\n/** 选择预离时间 */\r\nfunction changeCheckOutTime() {\r\n  form.value.planCheckoutTime = `${dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')} ${myEndHour.value}`\r\n\r\n  // 计算天数\r\n  const checkInDate = dayjs(ymdate(form.value.planCheckinTime))\r\n  const checkOutDate = dayjs(ymdate(form.value.planCheckoutTime))\r\n  // 如果预抵时间点小于最早入住时间，则天数加1\r\n  if (myStartHour.value < earliestCheckinTime.value) {\r\n    form.value.days = Math.abs(checkOutDate.diff(checkInDate, 'days')) + 1\r\n  } else {\r\n    form.value.days = Math.abs(checkOutDate.diff(checkInDate, 'days'))\r\n  }\r\n\r\n  bookingRoomsList()\r\n}\r\n\r\nfunction bookingRoomsList() {\r\n  form.value.loading = true\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    channelCode: props.channelCode,\r\n    checkinType: props.checkinType,\r\n    guestSrcType: props.guestSrcType,\r\n    guestCode: props.guestCode,\r\n    delayMinute: 0,\r\n    orderSource: ChannelEnum.LOBBY,\r\n    planCheckinTime: form.value.planCheckinTime,\r\n    planCheckoutTime: form.value.planCheckoutTime,\r\n  }\r\n  bookApi.roomtypeList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      form.value.loading = false\r\n      form.value.dataList = res.data.map((_item: any) => {\r\n        return {\r\n          ..._item,\r\n          vipPrice: _item.dayPrices[0].vipPrice,\r\n        }\r\n      })\r\n      // 更新展开行\r\n      // getExpanded()\r\n    } else {\r\n      ElMessage.error(t('getRoomListFailed'))\r\n    }\r\n  })\r\n}\r\nfunction handleClose(row: any, index: number) {\r\n  const x = form.value.dataList.findIndex((item) => item.rtCode === row.rtCode)\r\n  if (x !== -1) {\r\n    form.value.dataList[x].bookRooms?.splice(index, 1) // 直接使用 index 删除\r\n  }\r\n}\r\n\r\nconst arrangeRoomsProps = ref({\r\n  visible: false,\r\n  rtCode: '',\r\n  rtName: '',\r\n  rtState: '',\r\n  rNos: [] as string[],\r\n  roomNum: 0,\r\n  planCheckinTime: '',\r\n  planCheckoutTime: '',\r\n})\r\n\r\nfunction addRoom(row: any) {\r\n  arrangeRoomsProps.value.rNos = []\r\n  if (form.value.planCheckinTime === '' || form.value.planCheckoutTime === '' || form.value.days <= 0) {\r\n    ElMessage({\r\n      message: t('setTimeFirst'),\r\n      type: 'warning',\r\n    })\r\n    return\r\n  }\r\n  if (row.roomNum <= 0) {\r\n    ElMessage({\r\n      message: t('selectRoomCountFirst'),\r\n      type: 'warning',\r\n    })\r\n    return\r\n  }\r\n  arrangeRoomsProps.value.rtCode = row.rtCode\r\n  arrangeRoomsProps.value.rtName = row.rtName\r\n  // arrangeRoomsProps.value.rtState = props.rtState\r\n  arrangeRoomsProps.value.roomNum = row.roomNum\r\n  arrangeRoomsProps.value.planCheckinTime = form.value.planCheckinTime\r\n  arrangeRoomsProps.value.planCheckoutTime = form.value.planCheckoutTime\r\n  if (row.bookRooms) {\r\n    row.bookRooms.forEach((ls: any) => {\r\n      arrangeRoomsProps.value.rNos.push(ls.rNo)\r\n    })\r\n  }\r\n  rooms.value = row.bookRooms\r\n  arrangeRoomsProps.value.visible = true\r\n}\r\n\r\nconst expandedRows = ref([])\r\nfunction getExpanded() {\r\n  const list = [] as any\r\n  form.value.dataList.forEach((item) => {\r\n    if (item.bookRooms?.length > 0) {\r\n      list.push(item.rtCode)\r\n    }\r\n  })\r\n  expandedRows.value = list\r\n}\r\nfunction selectRooms(data: any) {\r\n  form.value.dataList.forEach((item: any) => {\r\n    if (item.rtCode === data.rtCode) {\r\n      item.bookRooms = data.bookRooms\r\n    }\r\n  })\r\n}\r\n\r\nfunction updateDay() {\r\n  if (form.value.days > 0) {\r\n    if (myStartHour.value < earliestCheckinTime.value) {\r\n      form.value.planCheckoutTime = dayjs(form.value.planCheckinTime)\r\n        .add(form.value.days - 1, 'day')\r\n        .format(`YYYY-MM-DD ${myEndHour.value}`)\r\n    } else {\r\n      form.value.planCheckoutTime = dayjs(form.value.planCheckinTime).add(form.value.days, 'day').format(`YYYY-MM-DD ${myEndHour.value}`)\r\n    }\r\n    bookingRoomsList()\r\n  }\r\n}\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nfunction onSubmit() {\r\n  // 检查是否有选择预订间数\r\n  const hasSelectedRooms = form.value.dataList.some((item) => item.roomNum > 0)\r\n  if (!hasSelectedRooms) {\r\n    ElMessage.warning(t('selectBookedRooms'))\r\n    return\r\n  }\r\n\r\n  // 过滤出 roomNum > 0 的房间类型\r\n  const filteredDataList = form.value.dataList.filter((item) => item.roomNum > 0)\r\n\r\n  // 创建一个新的 form 对象，包含过滤后的 dataList\r\n  const filteredForm = {\r\n    ...form.value,\r\n    dataList: filteredDataList,\r\n  }\r\n\r\n  // 发射 success 事件，传递过滤后的 form 对象\r\n  emits('success', filteredForm)\r\n  // 关闭对话框\r\n  onCancel()\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nconst roomPriceProps = ref({\r\n  visible: false,\r\n  rtCode: '',\r\n  initialPriceList: [] as individual.ParameterMaps[],\r\n})\r\n// 改价\r\nfunction changePrice(row: any) {\r\n  roomPriceProps.value.rtCode = row.rtCode\r\n  roomPriceProps.value.initialPriceList = row.dayPrices\r\n  roomPriceProps.value.visible = true\r\n}\r\n// 改价成功后\r\nfunction echoList(value: individual.ParameterMaps[], rtCode: string) {\r\n  form.value.dataList.forEach((item) => {\r\n    if (item.rtCode === rtCode) {\r\n      item.dayPrices = value\r\n      item.vipPrice = value[0].vipPrice\r\n    }\r\n  })\r\n}\r\n\r\nfunction disablePastDates(time: Date) {\r\n  // 获取当前服务器日期（不包含时间）\r\n  const today = dayjs(serverTime.value).startOf('day')\r\n  // 禁用今天之前的所有日期\r\n  return dayjs(time).isBefore(today, 'day')\r\n}\r\n\r\n// 添加一个新函数用于禁用预离日期\r\nfunction disableCheckoutDates(time: Date) {\r\n  // 获取预抵日期（不包含时间）\r\n  const checkinDate = dayjs(form.value.planCheckinTime).startOf('day')\r\n  // 禁用预抵日期及之前的所有日期\r\n  return dayjs(time).isSameOrBefore(checkinDate, 'day')\r\n}\r\n\r\n// 更新优惠价，同步修改 dayPrices 中的所有价格\r\nfunction updateVipPrice(row: any, newPrice: number) {\r\n  if (row.dayPrices && row.dayPrices.length > 0) {\r\n    row.dayPrices.forEach((dayPrice: any) => {\r\n      dayPrice.vipPrice = newPrice\r\n    })\r\n  }\r\n  row.vipPrice = newPrice\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': form.tableAutoHeight }\" style=\"margin-top: -20px\">\r\n    <el-dialog v-model=\"myVisible\" :title=\"t('addRoom')\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close top=\"5vh\" :fullscreen=\"false\">\r\n      <search-bar :show-toggle=\"false\">\r\n        <el-form :model=\"form\" size=\"default\" label-width=\"140px\" inline-message inline class=\"search-form\">\r\n          <el-row>\r\n            <el-col :span=\"5\">\r\n              <el-form-item :label=\"t('expectedArrival')\">\r\n                <el-date-picker v-model=\"form.planCheckinTime\" type=\"date\" :clearable=\"false\" :placeholder=\"t('checkinDate')\" style=\"width: 150px\" :disabled-date=\"disablePastDates\" @change=\"changeCheckinTime\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item :label=\"t('expectedDeparture')\">\r\n                <el-date-picker v-model=\"form.planCheckoutTime\" type=\"date\" :clearable=\"false\" :placeholder=\"t('checkoutDate')\" style=\"width: 150px\" :disabled-date=\"disableCheckoutDates\" @change=\"changeCheckOutTime\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item :label=\"t('days')\">\r\n                <el-input-number v-model=\"form.days\" :min=\"1\" :precision=\"0\" :step=\"1\" :value-on-clear=\"1\" :placeholder=\"t('daysPlaceholder')\" style=\"width: 120px\" @change=\"updateDay\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </search-bar>\r\n      <el-table v-loading=\"form.loading\" :data=\"form.dataList\" height=\"600px\">\r\n        <el-table-column type=\"expand\">\r\n          <template #default=\"scope\">\r\n            <template v-if=\"scope.row.bookRooms && scope.row.bookRooms.length > 0\">\r\n              <el-tag v-for=\"(iem, index) in scope.row.bookRooms\" :key=\"iem.rNo\" style=\"margin-right: 5px\" closable @close=\"handleClose(scope.row, index)\">\r\n                {{ iem.rNo }}\r\n              </el-tag>\r\n            </template>\r\n            <span v-else>{{ t('notArranged') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"rtName\" :label=\"t('roomType')\" width=\"150\" />\r\n        <el-table-column width=\"150\" :label=\"t('discountPrice')\" align=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-input-number v-model=\"scope.row.vipPrice\" :min=\"0\" :precision=\"2\" :controls=\"controls\" style=\"width: 80px; margin-right: 5px\" @change=\"(val) => updateVipPrice(scope.row, val)\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('sellingPrice')\" align=\"left\">\r\n          <template #default=\"scope\">\r\n            <span>{{ scope.row.price }}</span>\r\n            <el-button type=\"primary\" text @click=\"changePrice(scope.row)\">\r\n              {{ t('changePrice') }}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('availableRooms')\" prop=\"canSellNum\" align=\"right\" />\r\n        <el-table-column :label=\"t('overbookingLimit')\" prop=\"canOverNum\" align=\"right\" />\r\n        <el-table-column :label=\"t('bookedRooms')\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-input-number v-model=\"scope.row.roomNum\" style=\"width: 120px\" :min=\"0\" :max=\"scope.row.canSellNum + scope.row.canOverNum\" :disabled=\"scope.row.canSellNum + scope.row.canOverNum <= 0\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('actions')\" align=\"center\" width=\"110\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addRoom(scope.row)\">\r\n              {{ t('arrangeRoom') }}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          {{ t('confirm') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n  <!--    改价窗口 -->\r\n  <RoomEditPrice v-if=\"roomPriceProps.visible\" v-model=\"roomPriceProps.visible\" :room-list-price=\"roomPriceProps.initialPriceList\" :rt-code=\"roomPriceProps.rtCode\" @success=\"echoList\" />\r\n  <ArrangeRoomsDialog\r\n    v-if=\"arrangeRoomsProps.visible\"\r\n    v-model=\"arrangeRoomsProps.visible\"\r\n    :rt-code=\"arrangeRoomsProps.rtCode\"\r\n    :r-nos=\"arrangeRoomsProps.rNos\"\r\n    :rooms=\"rooms\"\r\n    :plan-checkin-time=\"arrangeRoomsProps.planCheckinTime\"\r\n    :plan-checkout-time=\"arrangeRoomsProps.planCheckoutTime\"\r\n    :rt-name=\"arrangeRoomsProps.rtName\"\r\n    :room-num=\"arrangeRoomsProps.roomNum\"\r\n    :rt-state=\"arrangeRoomsProps.rtState\"\r\n    @success=\"selectRooms\"\r\n  />\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "myStartHour", "ref", "myEndHour", "queryParams", "reactive", "gcode", "hcode", "serverTime", "setTime", "earliestCheckinTime", "form", "loading", "tableAutoHeight", "guestSrcType", "guest<PERSON><PERSON>", "orderSource", "ChannelEnum", "LOBBY", "planCheckinTime", "dayjs", "format", "planCheckoutTime", "add", "days", "channelCode", "dataList", "controls", "rooms", "changeCheckinTime", "val", "specificDate", "today", "value", "isSame", "checkInDate", "ymdate", "checkOutDate", "Math", "abs", "diff", "bookingRoomsList", "changeCheckOutTime", "params", "checkinType", "delayMinute", "bookApi", "roomtypeList", "then", "res", "code", "data", "map", "_item", "vipPrice", "dayPrices", "ElMessage", "error", "onMounted", "async", "serverTimeApi", "generalConfigApi", "getGeneralConfig", "GeneralConfigTypeEnum", "CHECKIN_TIME", "type", "now", "fetchTime", "getCheckOutTime", "getTime", "arrangeRoomsProps", "visible", "rtCode", "rtName", "rtState", "rNos", "roomNum", "selectRooms", "for<PERSON>ach", "item", "bookRooms", "updateDay", "myVisible", "computed", "get", "modelValue", "set", "onSubmit", "some", "warning", "filteredDataList", "filter", "filteredForm", "onCancel", "roomPriceProps", "initialPriceList", "echoList", "disablePastDates", "time", "startOf", "isBefore", "disableCheckoutDates", "checkinDate", "isSameOrBefore", "row", "index", "x", "findIndex", "_a", "splice", "newPrice", "length", "dayPrice", "message", "ls", "push", "rNo"], "mappings": "qyDA+FA,MAAMA,EAAQC,EAkBRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAcC,EAAI,IAClBC,EAAYD,EAAI,IAEhBE,EAAcC,EAAS,CAE3BC,MAAOP,EAAUO,MAEjBC,MAAOR,EAAUQ,QAGbC,GAAaN,IAEbO,GAAUP,IACVQ,GAAsBR,EAAI,IAC1BS,GAAOT,EAAI,CACfU,SAAS,EAETC,iBAAiB,EAEjBC,aAAcrB,EAAMqB,aAEpBC,UAAWtB,EAAMsB,UAEjBC,YAAaC,EAAYC,MACzBC,gBAAiBC,IAAQC,OAAO,oBAChCC,iBAAkBF,IAAQG,IAAI,EAAG,OAAOF,OAAO,oBAC/CG,KAAM,EACNC,YAAahC,EAAMgC,YACnBC,SAAU,KAENC,GAAWzB,GAAI,GACf0B,GAAQ1B,EAAI,IAoClB,SAAS2B,GAAkBC,GAEzB,MAAMC,EAAeX,EAAMU,GAAKT,OAAO,cAEjCW,EAAQZ,EAAMZ,GAAWyB,OAG/B,GADgBD,EAAME,OAAOH,EAAc,OAC9B,CAECC,EAAMX,OAAO,SAEfZ,GAAQwB,MAChBtB,GAAKsB,MAAMd,gBAAkBa,EAAMX,OAAO,oBAErCV,GAAAsB,MAAMd,gBAAkB,GAAGa,EAAMX,OAAO,iBAAiBZ,GAAQwB,QAExEhC,EAAYgC,MAAQb,EAAMT,GAAKsB,MAAMd,iBAAiBE,OAAO,QAAO,MAEpEV,GAAKsB,MAAMd,gBAAkB,GAAGY,KAAgBtB,GAAQwB,QAE1DtB,GAAKsB,MAAMX,iBAAmB,GAAGF,EAAMT,GAAKsB,MAAMX,kBAAkBD,OAAO,iBAAiBlB,EAAU8B,QAClGb,EAAMT,GAAKsB,MAAMd,iBAAiBE,OAAO,eAAiBD,EAAMT,GAAKsB,MAAMX,kBAAkBD,OAAO,gBACtGV,GAAKsB,MAAMX,iBAAmB,GAAGF,EAAMT,GAAKsB,MAAMd,iBAAiBI,IAAI,EAAG,OAAOF,OAAO,iBAAiBlB,EAAU8B,SAIrH,MAAME,EAAcf,EAAMgB,EAAOzB,GAAKsB,MAAMd,kBACtCkB,EAAejB,EAAMgB,EAAOzB,GAAKsB,MAAMX,mBAEzCrB,EAAYgC,MAAQvB,GAAoBuB,MACrCtB,GAAAsB,MAAMT,KAAOc,KAAKC,IAAIF,EAAaG,KAAKL,EAAa,SAAW,EAEhExB,GAAAsB,MAAMT,KAAOc,KAAKC,IAAIF,EAAaG,KAAKL,EAAa,SAG3CM,IAAA,CAInB,SAASC,KACP/B,GAAKsB,MAAMX,iBAAmB,GAAGF,EAAMT,GAAKsB,MAAMX,kBAAkBD,OAAO,iBAAiBlB,EAAU8B,QAGtG,MAAME,EAAcf,EAAMgB,EAAOzB,GAAKsB,MAAMd,kBACtCkB,EAAejB,EAAMgB,EAAOzB,GAAKsB,MAAMX,mBAEzCrB,EAAYgC,MAAQvB,GAAoBuB,MACrCtB,GAAAsB,MAAMT,KAAOc,KAAKC,IAAIF,EAAaG,KAAKL,EAAa,SAAW,EAEhExB,GAAAsB,MAAMT,KAAOc,KAAKC,IAAIF,EAAaG,KAAKL,EAAa,SAG3CM,IAAA,CAGnB,SAASA,KACP9B,GAAKsB,MAAMrB,SAAU,EACrB,MAAM+B,EAAS,CACbrC,MAAOP,EAAUO,MACjBC,MAAOR,EAAUQ,MACjBkB,YAAahC,EAAMgC,YACnBmB,YAAanD,EAAMmD,YACnB9B,aAAcrB,EAAMqB,aACpBC,UAAWtB,EAAMsB,UACjB8B,YAAa,EACb7B,YAAaC,EAAYC,MACzBC,gBAAiBR,GAAKsB,MAAMd,gBAC5BG,iBAAkBX,GAAKsB,MAAMX,kBAE/BwB,EAAQC,aAAaJ,GAAQK,MAAMC,IAChB,IAAbA,EAAIC,MACNvC,GAAKsB,MAAMrB,SAAU,EACrBD,GAAKsB,MAAMP,SAAWuB,EAAIE,KAAKC,KAAKC,IAC3B,IACFA,EACHC,SAAUD,EAAME,UAAU,GAAGD,cAMvBE,EAAAC,MAAM5D,EAAE,qBAAoB,GAEzC,CAtHH6D,GAAUC,gBAcVA,iBACQ,MAAAR,KAAEA,SAAeS,EAAcpD,WAAWT,EAAUO,MAAO,KAC3D2C,QAAYY,EAAiBC,iBAAiB,IAC/C1D,EACH8C,KAAMa,EAAsBC,aAC5BC,KAAMF,EAAsBC,eAE9BxD,GAAWyB,MAAQkB,EAEnB,MAAMe,EAAM9C,EAAMZ,GAAWyB,OAAOZ,OAAO,SAEnCZ,GAAAwB,MAAQgB,EAAIE,KAAKlB,MAErBiC,EAAMzD,GAAQwB,MAChBtB,GAAKsB,MAAMd,gBAAkBC,EAAMZ,GAAWyB,OAAOZ,OAAO,oBAE5DV,GAAKsB,MAAMd,gBAAkB,GAAGC,EAAMZ,GAAWyB,OAAOZ,OAAO,iBAAiBZ,GAAQwB,QAE1FhC,EAAYgC,MAAQb,EAAMT,GAAKsB,MAAMd,iBAAiBE,OAAO,QAAO,CA/B9D8C,SAKRR,iBACE,MAAMR,KAAEA,SAAeU,EAAiBO,gBAAgBhE,GACxDD,EAAU8B,MAAQkB,EAAKlB,MACvBtB,GAAKsB,MAAMX,iBAAmB,GAAGF,EAAMT,GAAKsB,MAAMX,kBAAkBD,OAAO,iBAAiBlB,EAAU8B,QACrFQ,IAAA,CARX4B,EAAQ,IA6HhB,MAAMC,GAAoBpE,EAAI,CAC5BqE,SAAS,EACTC,OAAQ,GACRC,OAAQ,GACRC,QAAS,GACTC,KAAM,GACNC,QAAS,EACTzD,gBAAiB,GACjBG,iBAAkB,KA4CpB,SAASuD,GAAY1B,GACnBxC,GAAKsB,MAAMP,SAASoD,SAASC,IACvBA,EAAKP,SAAWrB,EAAKqB,SACvBO,EAAKC,UAAY7B,EAAK6B,UAAA,GAEzB,CAGH,SAASC,KACHtE,GAAKsB,MAAMT,KAAO,IAChBvB,EAAYgC,MAAQvB,GAAoBuB,MAC1CtB,GAAKsB,MAAMX,iBAAmBF,EAAMT,GAAKsB,MAAMd,iBAC5CI,IAAIZ,GAAKsB,MAAMT,KAAO,EAAG,OACzBH,OAAO,cAAclB,EAAU8B,SAElCtB,GAAKsB,MAAMX,iBAAmBF,EAAMT,GAAKsB,MAAMd,iBAAiBI,IAAIZ,GAAKsB,MAAMT,KAAM,OAAOH,OAAO,cAAclB,EAAU8B,SAE5GQ,KACnB,CA5BmBvC,EAAI,IA+BzB,MAAMgF,GAAYC,EAAS,CACzBC,IAAM,IACG3F,EAAM4F,WAEf,GAAAC,CAAIxD,GACFnC,EAAM,oBAAqBmC,EAAG,IAIlC,SAASyD,KAGP,IADyB5E,GAAKsB,MAAMP,SAAS8D,MAAMT,GAASA,EAAKH,QAAU,IAGzE,YADUpB,EAAAiC,QAAQ5F,EAAE,sBAKhB,MAAA6F,EAAmB/E,GAAKsB,MAAMP,SAASiE,QAAQZ,GAASA,EAAKH,QAAU,IAGvEgB,EAAe,IAChBjF,GAAKsB,MACRP,SAAUgE,GAIZ/F,EAAM,UAAWiG,GAERC,IAAA,CAGX,SAASA,KACPX,GAAUjD,OAAQ,CAAA,CAGpB,MAAM6D,GAAiB5F,EAAI,CACzBqE,SAAS,EACTC,OAAQ,GACRuB,iBAAkB,KASX,SAAAC,GAAS/D,EAAmCuC,GACnD7D,GAAKsB,MAAMP,SAASoD,SAASC,IACvBA,EAAKP,SAAWA,IAClBO,EAAKxB,UAAYtB,EACZ8C,EAAAzB,SAAWrB,EAAM,GAAGqB,SAAA,GAE5B,CAGH,SAAS2C,GAAiBC,GAExB,MAAMlE,EAAQZ,EAAMZ,GAAWyB,OAAOkE,QAAQ,OAE9C,OAAO/E,EAAM8E,GAAME,SAASpE,EAAO,MAAK,CAI1C,SAASqE,GAAqBH,GAE5B,MAAMI,EAAclF,EAAMT,GAAKsB,MAAMd,iBAAiBgF,QAAQ,OAE9D,OAAO/E,EAAM8E,GAAMK,eAAeD,EAAa,MAAK,2mEArJ7C,SAAYE,EAAUC,SACvB,MAAAC,EAAI/F,GAAKsB,MAAMP,SAASiF,WAAW5B,GAASA,EAAKP,SAAWgC,EAAIhC,UACxD,IAAVkC,IACF,OAAAE,EAAAjG,GAAKsB,MAAMP,SAASgF,GAAG1B,YAAvB4B,EAAkCC,OAAOJ,EAAO,GAClD,+bAqJsBD,QAAUM,IAC5BN,EAAIjD,WAAaiD,EAAIjD,UAAUwD,OAAS,GACtCP,EAAAjD,UAAUuB,SAASkC,IACrBA,EAAS1D,SAAWwD,CAAA,SAGxBN,EAAIlD,SAAWwD,GANR,IAAeN,EAAUM,yOA/BbN,QACJV,GAAA7D,MAAMuC,OAASgC,EAAIhC,OACnBsB,GAAA7D,MAAM8D,iBAAmBS,EAAIjD,eAC5CuC,GAAe7D,MAAMsC,SAAU,GAHjC,IAAqBiC,itBAxGJA,QACGlC,GAAArC,MAAM0C,KAAO,QACI,KAA/BhE,GAAKsB,MAAMd,iBAA0D,KAAhCR,GAAKsB,MAAMX,kBAA2BX,GAAKsB,MAAMT,MAAQ,EACtFgC,EAAA,CACRyD,QAASpH,EAAE,gBACXoE,KAAM,YAINuC,EAAI5B,SAAW,EACPpB,EAAA,CACRyD,QAASpH,EAAE,wBACXoE,KAAM,aAIQK,GAAArC,MAAMuC,OAASgC,EAAIhC,OACnBF,GAAArC,MAAMwC,OAAS+B,EAAI/B,OAEnBH,GAAArC,MAAM2C,QAAU4B,EAAI5B,QACpBN,GAAArC,MAAMd,gBAAkBR,GAAKsB,MAAMd,gBACnCmD,GAAArC,MAAMX,iBAAmBX,GAAKsB,MAAMX,iBAClDkF,EAAIxB,WACFwB,EAAAxB,UAAUF,SAASoC,IACrB5C,GAAkBrC,MAAM0C,KAAKwC,KAAKD,EAAGE,IAAG,IAG5CxF,GAAMK,MAAQuE,EAAIxB,UAClBV,GAAkBrC,MAAMsC,SAAU,IA5BpC,IAAiBiC"}