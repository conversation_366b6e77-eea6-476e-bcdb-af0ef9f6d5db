{"version": 3, "file": "createUnitAgent-Ds1yYwDV.js", "sources": ["../../src/views/customer/company/customer-list/components/DetailForm/createUnitAgent.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"type\": \"Type\",\r\n    \"protocolUnit\": \"Protocol Unit\",\r\n    \"agent\": \"Agent\",\r\n    \"channel\": \"Channel\",\r\n    \"selectChannel\": \"Select Channel\",\r\n    \"belongType\": \"Belonging Type\",\r\n    \"singleStoreApplicable\": \"Single Hotel\",\r\n    \"belongingStore\": \"Belonging Hotel\",\r\n    \"allowCredit\": \"Allow Credit\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"hidePrice\": \"Hide Price\",\r\n    \"registrationFormHidePrice\": \"Registration Form Hide Price\",\r\n    \"companyInfo\": \"Company Info\",\r\n    \"name\": \"Name\",\r\n    \"shortName\": \"Short Name\",\r\n    \"legalPerson\": \"Legal Person\",\r\n    \"telephone\": \"Telephone\",\r\n    \"address\": \"Address\",\r\n    \"startDate\": \"Start Date\",\r\n    \"endDate\": \"End Date\",\r\n    \"salesPerson\": \"Sales Person\",\r\n    \"status\": \"Status\",\r\n    \"valid\": \"Valid\",\r\n    \"invalid\": \"Invalid\",\r\n    \"customerLevel\": \"Customer Level\",\r\n    \"commissionStrategy\": \"Strategy Level\",\r\n    \"remarks\": \"Remark\",\r\n    \"contactPerson\": \"Contact Person\",\r\n    \"contactName\": \"Contact Name\",\r\n    \"contactPhone\": \"Contact Phone\",\r\n    \"email\": \"Email\",\r\n    \"creditSetting\": \"Credit Setting\",\r\n    \"accountSetName\": \"AR Name\",\r\n    \"companyName\": \"Client Name\",\r\n    \"accountType\": \"Account Type\",\r\n    \"creditAccount\": \"Credit Account\",\r\n    \"prepaidAccount\": \"Prepaid Account\",\r\n    \"settlementPeriod\": \"Settle Period\",\r\n    \"permanent\": \"Permanent\",\r\n    \"fixedPeriod\": \"Fixed Period\",\r\n    \"maximumLimit\": \"Maximum Limit\",\r\n    \"unlimited\": \"Unlimited\",\r\n    \"limited\": \"Limited\",\r\n    \"validityPeriod\": \"Validity Period\",\r\n    \"permanentlyValid\": \"Permanently Valid\",\r\n    \"fixedValid\": \"Fixed Valid\",\r\n    \"allowNegativeAccount\": \"Allow Negative\",\r\n    \"hotelScope\": \"Hotel Scope\",\r\n    \"nameRequired\": \"Please enter the name\",\r\n    \"selectCustomerLevel\": \"Please select the customer level\",\r\n    \"selectStartDate\": \"Please select the start date\",\r\n    \"selectEndDate\": \"Please select the end date\",\r\n    \"selectCommissionStrategy\": \"Please select the commission strategy\",\r\n    \"accountSetNameRequired\": \"Please enter the account set name\",\r\n    \"startAndEndDateRequired\": \"Start date and end date cannot be empty\",\r\n    \"selectChannelWhenAgent\": \"Channel cannot be empty when selecting agent type\",\r\n    \"editSuccess\": \"Edit successful\",\r\n    \"companyInfoIncomplete\": \"Company information and accounts settings are incomplete\",\r\n    \"selectDate\": \"Select Date\",\r\n    \"to\": \"To\",\r\n    \"pleaseEnterFixedPeriod\": \"Please enter fixed period\",\r\n    \"inputMaxLimit\": \"Please enter max limit\",\r\n    \"dateRange\": \"Date range\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"type\": \"类型\",\r\n    \"protocolUnit\": \"协议单位\",\r\n    \"agent\": \"中介\",\r\n    \"channel\": \"渠道\",\r\n    \"selectChannel\": \"选择渠道\",\r\n    \"belongType\": \"所属类型\",\r\n    \"singleStoreApplicable\": \"单店适用\",\r\n    \"belongingStore\": \"所属门店\",\r\n    \"allowCredit\": \"允许挂账\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"hidePrice\": \"登记单隐藏房价\",\r\n    \"registrationFormHidePrice\": \"登记单隐藏房价\",\r\n    \"companyInfo\": \"公司信息\",\r\n    \"name\": \"名称\",\r\n    \"shortName\": \"简称\",\r\n    \"legalPerson\": \"法人\",\r\n    \"telephone\": \"电话\",\r\n    \"address\": \"地址\",\r\n    \"startDate\": \"开始日期\",\r\n    \"endDate\": \"结束日期\",\r\n    \"salesPerson\": \"销售人员\",\r\n    \"status\": \"状态\",\r\n    \"valid\": \"有效\",\r\n    \"invalid\": \"无效\",\r\n    \"customerLevel\": \"客户级别\",\r\n    \"commissionStrategy\": \"佣金策略级别\",\r\n    \"remarks\": \"备注\",\r\n    \"contactPerson\": \"联系人\",\r\n    \"contactName\": \"姓名\",\r\n    \"contactPhone\": \"电话\",\r\n    \"email\": \"Email\",\r\n    \"creditSetting\": \"挂账设置\",\r\n    \"accountSetName\": \"账套名称\",\r\n    \"companyName\": \"公司名称\",\r\n    \"accountType\": \"账户类型\",\r\n    \"creditAccount\": \"信用账户\",\r\n    \"prepaidAccount\": \"预付账户\",\r\n    \"settlementPeriod\": \"结算账期\",\r\n    \"permanent\": \"永久账期\",\r\n    \"fixedPeriod\": \"固定账期\",\r\n    \"maximumLimit\": \"最大额度\",\r\n    \"unlimited\": \"不限额度\",\r\n    \"limited\": \"限制额度\",\r\n    \"validityPeriod\": \"有效时间\",\r\n    \"permanentlyValid\": \"永久有效\",\r\n    \"fixedValid\": \"固定有效\",\r\n    \"allowNegativeAccount\": \"允许负账\",\r\n    \"hotelScope\": \"酒店范围\",\r\n    \"nameRequired\": \"请输入名称\",\r\n    \"selectCustomerLevel\": \"请选择客户级别\",\r\n    \"selectStartDate\": \"请选择开始日期\",\r\n    \"selectEndDate\": \"请选择结束日期\",\r\n    \"selectCommissionStrategy\": \"请选择佣金策略\",\r\n    \"accountSetNameRequired\": \"请输入账套名称\",\r\n    \"startAndEndDateRequired\": \"开始日期和结束日期不能为空\",\r\n    \"selectChannelWhenAgent\": \"选择中介类型时渠道不能为空\",\r\n    \"editSuccess\": \"编辑成功\",\r\n    \"companyInfoIncomplete\": \"公司信息、挂账设置模块有信息未填写\",\r\n    \"to\": \"至\",\r\n    \"selectDate\": \"选择日期\",\r\n    \"pleaseEnterFixedPeriod\": \"请输入固定账期\",\r\n    \"inputMaxLimit\": \"请输入最大限额\",\r\n    \"dateRange\": \"日期范围\"\r\n  },\r\n  \"km\": {\r\n    \"type\": \"ប្រភេទ\",\r\n    \"protocolUnit\": \"អង្គភាពព្រមាន\",\r\n    \"agent\": \"ភ្នាក់ងារ\",\r\n    \"channel\": \"ឆានែល\",\r\n    \"selectChannel\": \"ជ្រើសរើសឆានែល\",\r\n    \"belongType\": \"ប្រភេទជាកម្មសិទ្ធ\",\r\n    \"singleStoreApplicable\": \"សណ្ឋាគារតែមួយ\",\r\n    \"belongingStore\": \"សណ្ឋាគារជាកម្មសិទ្ធ\",\r\n    \"allowCredit\": \"អនុញ្ញាតឱ្យខ្ចីប្រាក់\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"hidePrice\": \"លាក់តម្លៃនៅលើទម្រង់ចុះឈ្មោះ\",\r\n    \"registrationFormHidePrice\": \"លាក់តម្លៃនៅលើទម្រង់ចុះឈ្មោះ\",\r\n    \"companyInfo\": \"ព័ត៌មានក្រុមហ៊ុន\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"shortName\": \"ឈ្មោះខ្លី\",\r\n    \"legalPerson\": \"មន្ត្រីច្បាប់\",\r\n    \"telephone\": \"ទូរស័ព្ទ\",\r\n    \"address\": \"អាសយដ្ឋាន\",\r\n    \"startDate\": \"កាលបរិច្ឆេទចាប់ផ្តើម\",\r\n    \"endDate\": \"កាលបរិច្ឆេទបញ្ចប់\",\r\n    \"salesPerson\": \"អ្នកលក់\",\r\n    \"status\": \"ស្ថានភាព\",\r\n    \"valid\": \"មានសុពលភាព\",\r\n    \"invalid\": \"គ្មានសុពលភាព\",\r\n    \"customerLevel\": \"កម្រិតអតិថិជន\",\r\n    \"commissionStrategy\": \"កម្រិតយុទ្ធសាស្ត្រកំរៃ\",\r\n    \"remarks\": \"ចំណាំ\",\r\n    \"contactPerson\": \"អ្នកទំនាក់ទំនង\",\r\n    \"contactName\": \"ឈ្មោះ\",\r\n    \"contactPhone\": \"ទូរស័ព្ទ\",\r\n    \"email\": \"អ៊ីមែល\",\r\n    \"creditSetting\": \"ការកំណត់ឥណទាន\",\r\n    \"accountSetName\": \"ឈ្មោះគណនី\",\r\n    \"companyName\": \"ឈ្មោះក្រុមហ៊ុន\",\r\n    \"accountType\": \"ប្រភេទគណនី\",\r\n    \"creditAccount\": \"គណនីឥណទាន\",\r\n    \"prepaidAccount\": \"គណនីបង់មុន\",\r\n    \"settlementPeriod\": \"រយៈពេលសំណង\",\r\n    \"permanent\": \"រយៈពេលអចិន្ត្រៃយ៍\",\r\n    \"fixedPeriod\": \"រយៈពេលថេរ\",\r\n    \"maximumLimit\": \"ដែនកំណត់អតិបរមា\",\r\n    \"unlimited\": \"គ្មានដែនកំណត់\",\r\n    \"limited\": \"ដែនកំណត់\",\r\n    \"validityPeriod\": \"រយៈពេលសុពលភាព\",\r\n    \"permanentlyValid\": \"មានសុពលភាពអចិន្ត្រៃយ៍\",\r\n    \"fixedValid\": \"សុពលភាពថេរ\",\r\n    \"allowNegativeAccount\": \"អនុញ្ញាតឱ្យគណនីអវិជ្ជមាន\",\r\n    \"hotelScope\": \"វិសាលភាពសណ្ឋាគារ\",\r\n    \"nameRequired\": \"សូមបញ្ចូលឈ្មោះ\",\r\n    \"selectCustomerLevel\": \"សូមជ្រើសរើសកម្រិតអតិថិជន\",\r\n    \"selectStartDate\": \"សូមជ្រើសរើសកាលបរិច្ឆេទចាប់ផ្តើម\",\r\n    \"selectEndDate\": \"សូមជ្រើសរើសកាលបរិច្ឆេទបញ្ចប់\",\r\n    \"selectCommissionStrategy\": \"សូមជ្រើសរើសយុទ្ធសាស្ត្រកំរៃ\",\r\n    \"accountSetNameRequired\": \"សូមបញ្ចូលឈ្មោះគណនី\",\r\n    \"startAndEndDateRequired\": \"កាលបរិច្ឆេទចាប់ផ្តើមនិងបញ្ចប់មិនអាចទទេបានទេ\",\r\n    \"selectChannelWhenAgent\": \"ឆានែលមិនអាចទទេបានទេនៅពេលជ្រើសរើសប្រភេទភ្នាក់ងារ\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\",\r\n    \"companyInfoIncomplete\": \"ព័ត៌មានក្រុមហ៊ុន និងការកំណត់គណនីមានព័ត៌មានមិនពេញលេញ\",\r\n    \"to\": \"ដល់\",\r\n    \"selectDate\": \"ជ្រើសរើសកាលបរិច្ឆេទ\",\r\n    \"pleaseEnterFixedPeriod\": \"សូមបញ្ចូលរយៈពេលថេរ\",\r\n    \"inputMaxLimit\": \"សូមបញ្ចូលដែនកំណត់អតិបរមា\",\r\n    \"dateRange\": \"ចន្លោះកាលបរិច្ឆេទ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { MerchantModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { channelApi, customerApi, generalConfigApi, memberApi, merchantApi, userApi } from '@/api/modules/index'\r\n\r\nimport { DictTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport ymdate from '@/utils/timeutils'\r\nimport dayjs from 'dayjs'\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 所属酒店代码 */\r\n  belongHcode: userStore.hcode,\r\n  /** 所属酒店名称 */\r\n  belongHname: userStore.hname,\r\n  /** 中介/协议单位代码 */\r\n  paCode: '',\r\n  /** 中介/协议单位名称 */\r\n  paName: '',\r\n  /** 简称 */\r\n  shortName: '',\r\n  /** 法人 */\r\n  legalPerson: '',\r\n  /** 电话 */\r\n  telephone: '',\r\n  /** 地址 */\r\n  address: '',\r\n  /** 类型 0：协议单位 1：中介 */\r\n  paType: '1',\r\n  /** 渠道 */\r\n  channel: '',\r\n  /** 是否共享 */\r\n  isShare: '0',\r\n  /** 联系人姓名 */\r\n  contact: '',\r\n  /** 联系人电话 */\r\n  phone: '',\r\n  /** 联系人email */\r\n  email: '',\r\n  /** 销售员 */\r\n  seller: '',\r\n  /** 开始日期 */\r\n  startDate: new Date().toString(),\r\n  /** 结束日期 */\r\n  endDate: dayjs().add(1, 'year').toString(),\r\n  /** 状态 0：无效 1：有效 */\r\n  isEnable: '1',\r\n  /** 登记单隐藏房价 0：否 1：是 */\r\n  isHidePrice: '0',\r\n  /** 是否允许挂账 0：否 1：是 */\r\n  isCredit: '0',\r\n  /** 销售等级 */\r\n  sellLevel: '',\r\n  /**  佣金等级 */\r\n  commissionLevel: '',\r\n  /** 备注 */\r\n  remark: '',\r\n  arSet: {\r\n    /** id */\r\n    id: '',\r\n    /** 集团代码 */\r\n    gcode: userStore.gcode,\r\n    /** 账套代码 */\r\n    arSetCode: '',\r\n    /** 账套名称 */\r\n    arSetName: '',\r\n    /** 单位或中介代码、团队/旅行社代码 */\r\n    unitCode: '',\r\n    /** 单位或中介、团队/旅行社名称 */\r\n    unitName: '',\r\n    /** 挂账账户名称 */\r\n    creditAccName: '',\r\n    /** 挂账账户类型;0：信用账户 1：预付账户 */\r\n    creditAccType: '0',\r\n    /** 挂账:结算账期;0：固定账期 1：永久账期；credit_acc_type=0需要设置； */\r\n    creditPayDays: '1',\r\n    /** 挂账:固定账期值;credit_acc_type=0需要设置；credit_pay_days=0，该字段有值 */\r\n    creditPayFix: 0,\r\n    /** 挂账最大限额类型;credit_acc_type=0需要设置；0：不限额度 1：限制额度 */\r\n    creditQuotaType: '0',\r\n    /** 挂账:最大限额;credit_acc_type=0需要设置；credit_quota_type=1，该字段需要设置最大额度 */\r\n    creditQuota: 0,\r\n    /** 挂账:有效时间类型;credit_acc_type=0需要设置；0：永久有效 1：固定有效 */\r\n    creditValidType: '0',\r\n    /** 挂账：有效时间开始;credit_acc_type=0需要设置；credit_valid_type=1,需要设置 */\r\n    creditStartDate: '',\r\n    /** 挂账：有效时间结束;credit_acc_type=0需要设置；credit_valid_type=1,需要设置 */\r\n    creditEndDate: '',\r\n    /** 挂账：允许负账;0：不允许 1：允许 */\r\n    creditMinusAcc: '0',\r\n    /** 状态;0: 无效 1：有效 */\r\n    isEnable: '1',\r\n    /** 应收账套关联门店列表 */\r\n    arSetMerchants: [userStore.hcode],\r\n    /** 手动添加账套; 0 :手动 1: 自动 */\r\n    isManual: '0',\r\n  },\r\n})\r\n\r\n/** 渠道列表 */\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\n/** 门店列表 */\r\nconst merchants = ref<MerchantModel[]>([])\r\n\r\nconst formRules = ref<FormRules>({\r\n  paName: [{ required: true, message: t('nameRequired'), trigger: 'blur' }],\r\n  sellLevel: [{ required: true, message: t('selectCustomerLevel'), trigger: 'blur' }],\r\n  startDate: [{ required: true, message: t('selectStartDate'), trigger: 'blur' }],\r\n  endDate: [{ required: true, message: t('selectEndDate'), trigger: 'blur' }],\r\n  commissionLevel: [{ required: true, message: t('selectCommissionStrategy'), trigger: 'blur' }],\r\n  'arSet.arSetName': [{ required: true, message: t('accountSetNameRequired'), trigger: 'blur' }],\r\n  creditDateRange: [{ validator: validateDateRange, trigger: 'blur' }],\r\n})\r\n\r\nfunction validateDateRange(rule, value, callback) {\r\n  const { creditStartDate, creditEndDate } = form.value.arSet\r\n  if (creditStartDate && creditEndDate) {\r\n    callback()\r\n  } else {\r\n    callback(new Error(t('startAndEndDateRequired')))\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  getChannels()\r\n  getMerchants()\r\n  getMts()\r\n  getSeller()\r\n  getLevel()\r\n  getBrokerage()\r\n})\r\n\r\n// 获取客户级别\r\nconst levelList = ref<{ code: string; name: string }[]>([])\r\nconst typeLevel = ref('')\r\nfunction getLevel() {\r\n  typeLevel.value = form.value.paType === '0' ? DictTypeEnum.PROTOCOL_LEVEL : DictTypeEnum.AGENT_LEVEL\r\n  generalConfigApi.list({ gcode: userStore.gcode, type: typeLevel.value }).then((res: any) => {\r\n    levelList.value = res.data\r\n  })\r\n}\r\n// 获取佣金策略\r\nconst brokerageList = ref<{ code: string; name: string }[]>([])\r\nfunction getBrokerage() {\r\n  typeLevel.value = DictTypeEnum.BROKERAGE_LEVEL\r\n  generalConfigApi.list({ gcode: userStore.gcode, type: typeLevel.value }).then((res: any) => {\r\n    brokerageList.value = res.data\r\n  })\r\n}\r\n\r\n// 获取销售员列表\r\nconst sellers = ref<{ username: string; nickname: string }[]>()\r\nfunction getSeller() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  userApi.listSeller(params).then((res: any) => {\r\n    sellers.value = res.data\r\n  })\r\n}\r\n\r\n/** 获取渠道列表 */\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isEnable: 1,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取门店列表 */\r\nfunction getMerchants() {\r\n  merchantApi.getSimpleList(userStore.gcode).then((res: any) => {\r\n    merchants.value = res.data\r\n  })\r\n}\r\n\r\n/** 获取会员类型列表 */\r\nconst mts = ref<{ mtCode: string; mtName: string }[]>([])\r\n\r\nfunction getMts() {\r\n  memberApi.getTypeSimpleList(userStore.gcode, '1').then((res: any) => {\r\n    if (res.code === 0) {\r\n      mts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            if (form.value.paType === '1' && !form.value.channel) {\r\n              return ElMessage.error(t('selectChannelWhenAgent'))\r\n            }\r\n            form.value.startDate = ymdate(form.value.startDate)\r\n            form.value.endDate = ymdate(form.value.endDate)\r\n            form.value.arSet.creditStartDate = form.value.arSet.creditStartDate ? ymdate(form.value.arSet.creditStartDate) : ''\r\n            form.value.arSet.creditEndDate = form.value.arSet.creditEndDate ? ymdate(form.value.arSet.creditEndDate) : ''\r\n            customerApi.create(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('editSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          } else {\r\n            ElMessage.error(t('companyInfoIncomplete'))\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n\r\nfunction paTypeChange() {\r\n  form.value.sellLevel = ''\r\n  getLevel()\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\" style=\"height: 650px\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"140px\" label-suffix=\"：\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('type')\">\r\n            <el-radio-group v-model=\"form.paType\" @change=\"paTypeChange\">\r\n              <el-radio value=\"0\">\r\n                {{ t('protocolUnit') }}\r\n              </el-radio>\r\n              <el-radio value=\"1\">\r\n                {{ t('agent') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item v-if=\"form.paType === '1'\" :label=\"t('channel')\" style=\"position: relative\">\r\n            <span style=\"position: absolute; left: -66px; color: red\">*</span>\r\n            <el-select v-model=\"form.channel\" :placeholder=\"t('selectChannel')\">\r\n              <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('belongType')\">\r\n            <el-radio-group v-model=\"form.isShare\">\r\n              <el-radio value=\"0\">\r\n                {{ t('singleStoreApplicable') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('belongingStore')\">\r\n            {{ userStore.hname }}\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('allowCredit')\">\r\n            <el-switch v-model=\"form.isCredit\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('hidePrice')\">\r\n            <el-switch v-model=\"form.isHidePrice\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :md=\"24\">\r\n          <el-tabs class=\"demo-tabs\" model-value=\"first\">\r\n            <el-tab-pane :label=\"t('companyInfo')\" name=\"first\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('name')\" prop=\"paName\">\r\n                    <el-input v-model=\"form.paName\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('shortName')\">\r\n                    <el-input v-model=\"form.shortName\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('legalPerson')\">\r\n                    <el-input v-model=\"form.legalPerson\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('telephone')\">\r\n                    <el-input v-model=\"form.telephone\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('address')\">\r\n                    <el-input v-model=\"form.address\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('startDate')\" prop=\"startDate\">\r\n                    <el-date-picker v-model=\"form.startDate\" type=\"date\" :placeholder=\"t('selectDate')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('endDate')\" prop=\"endDate\">\r\n                    <el-date-picker v-model=\"form.endDate\" type=\"date\" :placeholder=\"t('selectDate')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('salesPerson')\">\r\n                    <el-select v-model=\"form.seller\">\r\n                      <el-option v-for=\"i in sellers\" :key=\"i.username\" :label=\"i.nickname\" :value=\"i.username\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('status')\">\r\n                    <el-switch v-model=\"form.isEnable\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('valid')\" :inactive-text=\"t('invalid')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('customerLevel')\" prop=\"sellLevel\">\r\n                    <el-select v-model=\"form.sellLevel\">\r\n                      <el-option v-for=\"item in levelList\" :key=\"item.code\" :label=\"item.name\" :value=\"item.code\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('commissionStrategy')\">\r\n                    <el-select v-model=\"form.commissionLevel\" prop=\"commissionLevel\">\r\n                      <el-option v-for=\"item in brokerageList\" :key=\"item.code\" :label=\"item.name\" :value=\"item.code\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('remarks')\">\r\n                    <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"5\" maxlength=\"250\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-tab-pane>\r\n            <el-tab-pane :label=\"t('contactPerson')\" name=\"second\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('contactName')\">\r\n                    <el-input v-model=\"form.contact\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('contactPhone')\">\r\n                    <el-input v-model=\"form.phone\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('email')\">\r\n                    <el-input v-model=\"form.email\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-tab-pane>\r\n            <el-tab-pane v-if=\"form.isCredit === '1'\" :label=\"t('creditSetting')\" name=\"third\">\r\n              <el-row :gutter=\"24\">\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('accountSetName')\" prop=\"arSet.arSetName\">\r\n                    <el-input v-model=\"form.arSet.arSetName\" maxlength=\"30\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :md=\"12\">\r\n                  <el-form-item :label=\"t('companyName')\">\r\n                    {{ form.paName }}\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"24\">\r\n                  <el-form-item :label=\"t('accountType')\">\r\n                    <el-radio-group v-model=\"form.arSet.creditAccType\" class=\"ml-4\">\r\n                      <el-radio value=\"0\" size=\"large\">\r\n                        {{ t('creditAccount') }}\r\n                      </el-radio>\r\n                      <el-radio value=\"1\" size=\"large\">\r\n                        {{ t('prepaidAccount') }}\r\n                      </el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <div v-if=\"form.arSet.creditAccType === '0'\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :md=\"24\">\r\n                    <el-form-item :label=\"t('settlementPeriod')\">\r\n                      <el-radio-group v-model=\"form.arSet.creditPayDays\" class=\"ml-4\">\r\n                        <el-radio value=\"1\" size=\"large\">\r\n                          {{ t('permanent') }}\r\n                        </el-radio>\r\n                        <el-radio value=\"0\" size=\"large\">\r\n                          {{ t('fixedPeriod') }}\r\n                        </el-radio>\r\n                        <el-input-number\r\n                          v-if=\"form.arSet.creditPayDays === '0'\"\r\n                          v-model=\"form.arSet.creditPayFix\"\r\n                          class=\"mx-4\"\r\n                          :min=\"0\"\r\n                          :placeholder=\"t('pleaseEnterFixedPeriod')\"\r\n                          controls-position=\"right\"\r\n                          :precision=\"0\"\r\n                          style=\"width: 200px; margin: 0 8px\"\r\n                        />\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :md=\"24\">\r\n                    <el-form-item :label=\"t('maximumLimit')\">\r\n                      <el-radio-group v-model=\"form.arSet.creditQuotaType\" class=\"ml-4\">\r\n                        <el-radio value=\"0\" size=\"large\">\r\n                          {{ t('unlimited') }}\r\n                        </el-radio>\r\n                        <el-radio value=\"1\" size=\"large\">\r\n                          {{ t('limited') }}\r\n                        </el-radio>\r\n                        <el-input-number\r\n                          v-if=\"form.arSet.creditQuotaType === '1'\"\r\n                          v-model=\"form.arSet.creditQuota\"\r\n                          class=\"mx-4\"\r\n                          :min=\"0\"\r\n                          :placeholder=\"t('inputMaxLimit')\"\r\n                          controls-position=\"right\"\r\n                          :precision=\"2\"\r\n                          style=\"width: 200px; margin: 0 8px\"\r\n                        />\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :md=\"24\">\r\n                    <el-form-item :label=\"t('validityPeriod')\">\r\n                      <el-radio-group v-model=\"form.arSet.creditValidType\" class=\"ml-4\">\r\n                        <el-radio value=\"0\" size=\"large\">\r\n                          {{ t('permanentlyValid') }}\r\n                        </el-radio>\r\n                        <el-radio value=\"1\" size=\"large\">\r\n                          {{ t('fixedValid') }}\r\n                        </el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item v-if=\"form.arSet.creditValidType === '1'\" :label=\"t('dateRange')\" prop=\"creditDateRange\">\r\n                      <el-date-picker v-model=\"form.arSet.creditStartDate\" type=\"date\" :placeholder=\"t('startDate')\" />\r\n                      <span style=\"margin: 0 5px\">{{ t('to') }}</span>\r\n                      <el-date-picker v-model=\"form.arSet.creditEndDate\" type=\"date\" :placeholder=\"t('endDate')\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"20\">\r\n                  <el-form-item :label=\"t('allowNegativeAccount')\">\r\n                    <el-switch v-model=\"form.arSet.creditMinusAcc\" inline-prompt active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :md=\"20\">\r\n                  <el-form-item :label=\"t('hotelScope')\">\r\n                    {{ userStore.hname }}\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vertical-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  text-align: left;\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "belongHcode", "hcode", "belongHname", "hname", "paCode", "paName", "shortName", "legal<PERSON>erson", "telephone", "address", "paType", "channel", "isShare", "contact", "phone", "email", "seller", "startDate", "Date", "toString", "endDate", "dayjs", "add", "isEnable", "isHidePrice", "isCredit", "sellLevel", "commissionLevel", "remark", "arSet", "id", "arSetCode", "arSetName", "unitCode", "unitName", "creditAccName", "creditAccType", "creditPayDays", "creditPayFix", "creditQuotaType", "creditQuota", "creditValidType", "creditStartDate", "creditEndDate", "creditMinusAcc", "arSetMerchants", "<PERSON><PERSON><PERSON><PERSON>", "channels", "merchants", "formRules", "required", "message", "trigger", "creditDateRange", "validator", "rule", "value", "callback", "Error", "onMounted", "params", "channelApi", "getChannelSimpleList", "then", "res", "code", "data", "getChannels", "merchantApi", "getSimpleList", "memberApi", "getTypeSimpleList", "mts", "userApi", "listSeller", "sellers", "getSeller", "getLevel", "typeLevel", "DictTypeEnum", "BROKERAGE_LEVEL", "generalConfigApi", "list", "type", "brokerageList", "levelList", "PROTOCOL_LEVEL", "AGENT_LEVEL", "paTypeChange", "__expose", "submit", "Promise", "resolve", "validate", "valid", "ElMessage", "error", "ymdate", "customerApi", "create", "success", "center", "msg"], "mappings": "u3CAqNM,MAAAA,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,MAAON,EAAUM,MAEjBC,YAAaP,EAAUQ,MAEvBC,YAAaT,EAAUU,MAEvBC,OAAQ,GAERC,OAAQ,GAERC,UAAW,GAEXC,YAAa,GAEbC,UAAW,GAEXC,QAAS,GAETC,OAAQ,IAERC,QAAS,GAETC,QAAS,IAETC,QAAS,GAETC,MAAO,GAEPC,MAAO,GAEPC,OAAQ,GAERC,WAAW,IAAIC,MAAOC,WAEtBC,QAASC,IAAQC,IAAI,EAAG,QAAQH,WAEhCI,SAAU,IAEVC,YAAa,IAEbC,SAAU,IAEVC,UAAW,GAEXC,gBAAiB,GAEjBC,OAAQ,GACRC,MAAO,CAELC,GAAI,GAEJ/B,MAAON,EAAUM,MAEjBgC,UAAW,GAEXC,UAAW,GAEXC,SAAU,GAEVC,SAAU,GAEVC,cAAe,GAEfC,cAAe,IAEfC,cAAe,IAEfC,aAAc,EAEdC,gBAAiB,IAEjBC,YAAa,EAEbC,gBAAiB,IAEjBC,gBAAiB,GAEjBC,cAAe,GAEfC,eAAgB,IAEhBrB,SAAU,IAEVsB,eAAgB,CAACpD,EAAUQ,OAE3B6C,SAAU,OAKRC,EAAWnD,EAAoD,IAE/DoD,EAAYpD,EAAqB,IAEjCqD,GAAYrD,EAAe,CAC/BS,OAAQ,CAAC,CAAE6C,UAAU,EAAMC,QAAS5D,EAAE,gBAAiB6D,QAAS,SAChE1B,UAAW,CAAC,CAAEwB,UAAU,EAAMC,QAAS5D,EAAE,uBAAwB6D,QAAS,SAC1EnC,UAAW,CAAC,CAAEiC,UAAU,EAAMC,QAAS5D,EAAE,mBAAoB6D,QAAS,SACtEhC,QAAS,CAAC,CAAE8B,UAAU,EAAMC,QAAS5D,EAAE,iBAAkB6D,QAAS,SAClEzB,gBAAiB,CAAC,CAAEuB,UAAU,EAAMC,QAAS5D,EAAE,4BAA6B6D,QAAS,SACrF,kBAAmB,CAAC,CAAEF,UAAU,EAAMC,QAAS5D,EAAE,0BAA2B6D,QAAS,SACrFC,gBAAiB,CAAC,CAAEC,UAGb,SAAkBC,EAAMC,EAAOC,GACtC,MAAMf,gBAAEA,EAAAC,cAAiBA,GAAkB7C,EAAK0D,MAAM3B,MAClDa,GAAmBC,EACZc,IAETA,EAAS,IAAIC,MAAMnE,EAAE,4BACvB,EATkD6D,QAAS,WAY7DO,GAAU,MAwCV,WACE,MAAMC,EAAS,CACb7D,MAAON,EAAUM,MACjBE,MAAOR,EAAUQ,MACjBsB,SAAU,GAEZsC,EAAWC,qBAAqBF,GAAQG,MAAMC,IAC3B,IAAbA,EAAIC,OACNlB,EAASS,MAAQQ,EAAIE,KAAA,GAExB,CAjDWC,GAsDZC,EAAYC,cAAc5E,EAAUM,OAAOgE,MAAMC,IAC/ChB,EAAUQ,MAAQQ,EAAIE,IAAA,IAQxBI,EAAUC,kBAAkB9E,EAAUM,MAAO,KAAKgE,MAAMC,IACrC,IAAbA,EAAIC,OACNO,GAAIhB,MAAQQ,EAAIE,KAAA,IArCtB,WACE,MAAMN,EAAS,CACb7D,MAAON,EAAUM,MACjBE,MAAOR,EAAUQ,OAEnBwE,EAAQC,WAAWd,GAAQG,MAAMC,IAC/BW,GAAQnB,MAAQQ,EAAIE,IAAA,GACrB,CAhCSU,GACDC,KAgBTC,GAAUtB,MAAQuB,EAAaC,gBAC/BC,EAAiBC,KAAK,CAAEnF,MAAON,EAAUM,MAAOoF,KAAML,GAAUtB,QAASO,MAAMC,IAC7EoB,GAAc5B,MAAQQ,EAAIE,IAAA,GAjBf,IAIT,MAAAmB,GAAYzF,EAAsC,IAClDkF,GAAYlF,EAAI,IACtB,SAASiF,KACPC,GAAUtB,MAA8B,MAAtB1D,EAAK0D,MAAM9C,OAAiBqE,EAAaO,eAAiBP,EAAaQ,YACzFN,EAAiBC,KAAK,CAAEnF,MAAON,EAAUM,MAAOoF,KAAML,GAAUtB,QAASO,MAAMC,IAC7EqB,GAAU7B,MAAQQ,EAAIE,IAAA,GACvB,CAGG,MAAAkB,GAAgBxF,EAAsC,IAS5D,MAAM+E,GAAU/E,IAiCV,MAAA4E,GAAM5E,EAA0C,IA6CtD,SAAS4F,KACP1F,EAAK0D,MAAM9B,UAAY,GACdmD,IAAA,QArCEY,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB/F,EAAQ2D,OACN3D,EAAQ2D,MAAMqC,UAAUC,IACtB,GAAIA,EAAO,CACT,GAA0B,MAAtBhG,EAAK0D,MAAM9C,SAAmBZ,EAAK0D,MAAM7C,QAC3C,OAAOoF,EAAUC,MAAMzG,EAAE,2BAE3BO,EAAK0D,MAAMvC,UAAYgF,EAAOnG,EAAK0D,MAAMvC,WACzCnB,EAAK0D,MAAMpC,QAAU6E,EAAOnG,EAAK0D,MAAMpC,SACvCtB,EAAK0D,MAAM3B,MAAMa,gBAAkB5C,EAAK0D,MAAM3B,MAAMa,gBAAkBuD,EAAOnG,EAAK0D,MAAM3B,MAAMa,iBAAmB,GACjH5C,EAAK0D,MAAM3B,MAAMc,cAAgB7C,EAAK0D,MAAM3B,MAAMc,cAAgBsD,EAAOnG,EAAK0D,MAAM3B,MAAMc,eAAiB,GAC3GuD,EAAYC,OAAOrG,EAAK0D,OAAOO,MAAMC,IAClB,IAAbA,EAAIC,MACN8B,EAAUK,QAAQ,CAChBjD,QAAS5D,EAAE,eACX8G,QAAQ,IAEFT,KAERG,EAAUC,MAAM,CACd7C,QAASa,EAAIsC,IACbD,QAAQ,GACT,GAEJ,MAESN,EAAAC,MAAMzG,EAAE,yBAAwB,GAE7C"}