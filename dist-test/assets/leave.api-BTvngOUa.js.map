{"version": 3, "file": "leave.api-BTvngOUa.js", "sources": ["../../src/api/modules/pms/goods/leave.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/leave'\r\n/**\r\n * @description: 遗留物品\r\n */\r\nexport default {\r\n  /**\r\n   * 遗留物品列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  list: (data: {\r\n    gcode: string\r\n    hcode: string\r\n    pickTimeStart?: string\r\n    pickTimeEnd?: string\r\n    claimTimeStart?: string\r\n    claimTimeEnd?: string\r\n    state?: string // 状态\r\n    goodsName?: string // 物品名称\r\n    pageNo: number\r\n    pageSize: number\r\n  }) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 遗留物品明细\r\n   * @param id\r\n   * @returns\r\n   */\r\n  detail: (id: number) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        id,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 编辑遗留物品\r\n   * @param data\r\n   * @returns\r\n   */\r\n  edit: (data: any) => api.put(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 创建遗留物品\r\n   * @param data\r\n   * @returns\r\n   */\r\n  create: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n\r\n  /**\r\n   * 删除遗留物品\r\n   * @param id\r\n   * @returns\r\n   */\r\n  delete: (id: number) =>\r\n    api.delete(`${BASE_PATH}/delete`, {\r\n      params: {\r\n        id,\r\n      },\r\n    }),\r\n}\r\n"], "names": ["BASE_PATH", "leaveApi", "list", "data", "api", "get", "params", "detail", "id", "edit", "put", "create", "post", "delete"], "mappings": "wCAEA,MAAMA,EAAY,sBAIHC,EAAA,CAMbC,KAAOC,GAYLC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAQZI,OAASC,GACPJ,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CACNE,QASNC,KAAON,GAAcC,EAAIM,IAAI,GAAGV,WAAoBG,EAAM,IAO1DQ,OAASR,GAAcC,EAAIQ,KAAK,GAAGZ,WAAoBG,EAAM,IAO7DU,OAASL,GACPJ,EAAIS,OAAO,GAAGb,WAAoB,CAChCM,OAAQ,CACNE"}