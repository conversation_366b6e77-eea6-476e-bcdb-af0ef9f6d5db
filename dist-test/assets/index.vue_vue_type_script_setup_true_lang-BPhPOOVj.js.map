{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-BPhPOOVj.js", "sources": ["../../src/layouts/components/Menu/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { MenuInjection, MenuProps } from './types'\r\nimport Item from './item.vue'\r\nimport SubMenu from './sub.vue'\r\nimport { rootMenuInjectionKey } from './types'\r\n\r\ndefineOptions({\r\n  name: 'MainMenu',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<MenuProps>(),\r\n  {\r\n    accordion: true,\r\n    defaultOpeneds: () => [],\r\n    alwaysOpeneds: () => [],\r\n    mode: 'vertical',\r\n    collapse: false,\r\n    showCollapseName: false,\r\n    rounded: false,\r\n    direction: 'ltr',\r\n  },\r\n)\r\n\r\nconst activeIndex = ref<MenuInjection['activeIndex']>(props.value)\r\nconst items = ref<MenuInjection['items']>({})\r\nconst subMenus = ref<MenuInjection['subMenus']>({})\r\n// 合并 alwaysOpeneds 和 defaultOpeneds 并去重\r\nconst openedMenus = ref<MenuInjection['openedMenus']>(Array.from(new Set([...props.alwaysOpeneds.slice(0), ...props.defaultOpeneds.slice(0)])))\r\nconst alwaysOpenedsMenus = ref<MenuInjection['alwaysOpenedsMenus']>(props.alwaysOpeneds.slice(0))\r\nconst mouseInMenu = ref<MenuInjection['mouseInMenu']>([])\r\nconst isMenuPopup = computed<MenuInjection['isMenuPopup']>(() => {\r\n  return props.mode === 'horizontal' || (props.mode === 'vertical' && props.collapse)\r\n})\r\n\r\n// 解析传入的 menu 数据，并保存到 items 和 subMenus 对象中\r\nfunction initItems(menu: MenuProps['menu'], parentPaths: string[] = []) {\r\n  menu.forEach((item) => {\r\n    const index = item.path ?? JSON.stringify(item)\r\n    if (item.children) {\r\n      const indexPath = [...parentPaths, index]\r\n      subMenus.value[index] = {\r\n        index,\r\n        indexPath,\r\n        active: false,\r\n      }\r\n      initItems(item.children, indexPath)\r\n    }\r\n    else {\r\n      items.value[index] = {\r\n        index,\r\n        indexPath: parentPaths,\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\nconst openMenu: MenuInjection['openMenu'] = (index, indexPath) => {\r\n  if (openedMenus.value.includes(index)) {\r\n    return\r\n  }\r\n  if (props.accordion) {\r\n    openedMenus.value = openedMenus.value.filter(key => indexPath.includes(key) || alwaysOpenedsMenus.value.includes(key))\r\n  }\r\n  openedMenus.value.push(index)\r\n}\r\nconst closeMenu: MenuInjection['closeMenu'] = (index) => {\r\n  if (Array.isArray(index)) {\r\n    nextTick(() => {\r\n      closeMenu(index.at(-1)!)\r\n      if (index.length > 1) {\r\n        closeMenu(index.slice(0, -1))\r\n      }\r\n    })\r\n    return\r\n  }\r\n  Object.keys(subMenus.value).forEach((item) => {\r\n    if (subMenus.value[item].indexPath.includes(index)) {\r\n      openedMenus.value = openedMenus.value.filter(item => item !== index)\r\n    }\r\n  })\r\n}\r\n\r\nfunction setSubMenusActive(index: string) {\r\n  for (const key in subMenus.value) {\r\n    subMenus.value[key].active = false\r\n  }\r\n  subMenus.value[index]?.indexPath.forEach((idx) => {\r\n    subMenus.value[idx].active = true\r\n  })\r\n  items.value[index]?.indexPath.forEach((idx) => {\r\n    subMenus.value[idx].active = true\r\n  })\r\n}\r\n\r\nconst handleMenuItemClick: MenuInjection['handleMenuItemClick'] = (index) => {\r\n  if (props.mode === 'horizontal' || props.collapse) {\r\n    openedMenus.value = []\r\n  }\r\n  setSubMenusActive(index)\r\n}\r\nconst handleSubMenuClick: MenuInjection['handleSubMenuClick'] = (index, indexPath) => {\r\n  if (openedMenus.value.includes(index)) {\r\n    closeMenu(index)\r\n  }\r\n  else {\r\n    openMenu(index, indexPath)\r\n  }\r\n}\r\n\r\nfunction initMenu() {\r\n  const activeItem = activeIndex.value && items.value[activeIndex.value]\r\n  setSubMenusActive(activeIndex.value)\r\n  if (!activeItem || isMenuPopup.value || props.collapse) {\r\n    return\r\n  }\r\n  // 展开该菜单项的路径上所有子菜单\r\n  activeItem.indexPath.forEach((index) => {\r\n    const subMenu = subMenus.value[index]\r\n    subMenu && openMenu(index, subMenu.indexPath)\r\n  })\r\n}\r\n\r\nwatch(() => props.menu, (val) => {\r\n  initItems(val)\r\n  initMenu()\r\n}, {\r\n  deep: true,\r\n  immediate: true,\r\n})\r\n\r\nwatch(() => props.value, (currentValue) => {\r\n  if (!items.value[currentValue]) {\r\n    activeIndex.value = ''\r\n  }\r\n  const item = items.value[currentValue] || (activeIndex.value && items.value[activeIndex.value]) || items.value[props.value]\r\n  if (item) {\r\n    activeIndex.value = item.index\r\n  }\r\n  else {\r\n    activeIndex.value = currentValue\r\n  }\r\n  initMenu()\r\n})\r\n\r\nwatch(() => props.collapse, (value) => {\r\n  if (value) {\r\n    openedMenus.value = []\r\n  }\r\n  else {\r\n    openedMenus.value = props.alwaysOpeneds.slice(0)\r\n  }\r\n  initMenu()\r\n})\r\n\r\nprovide(rootMenuInjectionKey, reactive({\r\n  props,\r\n  items,\r\n  subMenus,\r\n  activeIndex,\r\n  openedMenus,\r\n  alwaysOpenedsMenus,\r\n  mouseInMenu,\r\n  isMenuPopup,\r\n  openMenu,\r\n  closeMenu,\r\n  handleMenuItemClick,\r\n  handleSubMenuClick,\r\n}))\r\n</script>\r\n\r\n<template>\r\n  <div\r\n    class=\"h-full w-full flex flex-col of-hidden transition-all\" :class=\"{\r\n      'flex-row! w-auto!': isMenuPopup && props.mode === 'horizontal',\r\n      'py-1': props.mode === 'vertical',\r\n    }\"\r\n  >\r\n    <template v-for=\"item in menu\" :key=\"item.path ?? JSON.stringify(item)\">\r\n      <template v-if=\"item.meta?.menu !== false\">\r\n        <SubMenu v-if=\"item.children?.length\" :menu=\"item\" :unique-key=\"[item.path ?? (item.children.every(item => item.meta?.menu === false) ? item.children[0].path! : JSON.stringify(item))]\" />\r\n        <Item v-else :item=\"item\" :unique-key=\"[item.path ?? (item.children?.every(item => item.meta?.menu === false) ? item.children[0].path! : JSON.stringify(item))]\" @click=\"handleMenuItemClick(item.path ?? (item.children?.every(item => item.meta?.menu === false) ? item.children[0].path! : JSON.stringify(item)))\" />\r\n      </template>\r\n    </template>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "activeIndex", "ref", "value", "items", "subMenus", "openedMenus", "Array", "from", "Set", "alwaysOpeneds", "slice", "defaultOpeneds", "alwaysOpenedsMenus", "mouseInMenu", "isMenuPopup", "computed", "mode", "collapse", "initItems", "menu", "parentPaths", "for<PERSON>ach", "item", "index", "path", "JSON", "stringify", "children", "indexPath", "active", "openMenu", "includes", "accordion", "filter", "key", "push", "closeMenu", "isArray", "nextTick", "at", "length", "Object", "keys", "setSubMenusActive", "_a", "idx", "_b", "handleMenuItemClick", "initMenu", "activeItem", "subMenu", "watch", "val", "deep", "immediate", "currentValue", "provide", "rootMenuInjectionKey", "reactive", "handleSubMenuClick"], "mappings": "ylBAUA,MAAMA,EAAQC,EAcRC,EAAcC,EAAkCH,EAAMI,OACtDC,EAAQF,EAA4B,IACpCG,EAAWH,EAA+B,IAE1CI,EAAcJ,EAAkCK,MAAMC,SAASC,IAAI,IAAIV,EAAMW,cAAcC,MAAM,MAAOZ,EAAMa,eAAeD,MAAM,OACnIE,EAAqBX,EAAyCH,EAAMW,cAAcC,MAAM,IACxFG,EAAcZ,EAAkC,IAChDa,EAAcC,GAAuC,IACnC,eAAfjB,EAAMkB,MAAyC,aAAflB,EAAMkB,MAAuBlB,EAAMmB,WAI5E,SAASC,EAAUC,EAAyBC,EAAwB,IAC7DD,EAAAE,SAASC,IACZ,MAAMC,EAAQD,EAAKE,MAAQC,KAAKC,UAAUJ,GAC1C,GAAIA,EAAKK,SAAU,CACjB,MAAMC,EAAY,IAAIR,EAAaG,GAC1BnB,EAAAF,MAAMqB,GAAS,CACtBA,QACAK,YACAC,QAAQ,GAEAX,EAAAI,EAAKK,SAAUC,EAAS,MAG5BzB,EAAAD,MAAMqB,GAAS,CACnBA,QACAK,UAAWR,EACb,GAEH,CAGG,MAAAU,EAAsC,CAACP,EAAOK,KAC9CvB,EAAYH,MAAM6B,SAASR,KAG3BzB,EAAMkC,YACR3B,EAAYH,MAAQG,EAAYH,MAAM+B,QAAcC,GAAAN,EAAUG,SAASG,IAAQtB,EAAmBV,MAAM6B,SAASG,MAEvG7B,EAAAH,MAAMiC,KAAKZ,GAAK,EAExBa,EAAyCb,IACzCjB,MAAM+B,QAAQd,GAChBe,GAAS,KACGF,EAAAb,EAAMgB,IAAG,IACfhB,EAAMiB,OAAS,GACjBJ,EAAUb,EAAMb,MAAM,GAAG,GAAG,IAKlC+B,OAAOC,KAAKtC,EAASF,OAAOmB,SAASC,IAC/BlB,EAASF,MAAMoB,GAAMM,UAAUG,SAASR,KAC1ClB,EAAYH,MAAQG,EAAYH,MAAM+B,QAAOX,GAAQA,IAASC,IAAK,GAEtE,EAGH,SAASoB,EAAkBpB,WACd,IAAA,MAAAW,KAAO9B,EAASF,MAChBE,EAAAF,MAAMgC,GAAKL,QAAS,EAE/B,OAAAe,EAAAxC,EAASF,MAAMqB,OAAQK,UAAUP,SAASwB,IAC/BzC,EAAAF,MAAM2C,GAAKhB,QAAS,CAAA,IAE/B,OAAAiB,EAAA3C,EAAMD,MAAMqB,OAAQK,UAAUP,SAASwB,IAC5BzC,EAAAF,MAAM2C,GAAKhB,QAAS,CAAA,GAC9B,CAGG,MAAAkB,EAA6DxB,KAC9C,eAAfzB,EAAMkB,MAAyBlB,EAAMmB,YACvCZ,EAAYH,MAAQ,IAEtByC,EAAkBpB,EAAK,EAWzB,SAASyB,IACP,MAAMC,EAAajD,EAAYE,OAASC,EAAMD,MAAMF,EAAYE,OAChEyC,EAAkB3C,EAAYE,QACzB+C,GAAcnC,EAAYZ,OAASJ,EAAMmB,UAInCgC,EAAArB,UAAUP,SAASE,IACtB,MAAA2B,EAAU9C,EAASF,MAAMqB,GACpB2B,GAAApB,EAASP,EAAO2B,EAAQtB,UAAS,GAC7C,QAGHuB,GAAM,IAAMrD,EAAMqB,OAAOiC,IACvBlC,EAAUkC,GACDJ,GAAA,GACR,CACDK,MAAM,EACNC,WAAW,IAGbH,GAAM,IAAMrD,EAAMI,QAAQqD,IACnBpD,EAAMD,MAAMqD,KACfvD,EAAYE,MAAQ,IAEtB,MAAMoB,EAAOnB,EAAMD,MAAMqD,IAAkBvD,EAAYE,OAASC,EAAMD,MAAMF,EAAYE,QAAWC,EAAMD,MAAMJ,EAAMI,OAEnHF,EAAYE,MADVoB,EACkBA,EAAKC,MAGLgC,EAEbP,GAAA,IAGXG,GAAM,IAAMrD,EAAMmB,WAAWf,IAEzBG,EAAYH,MADVA,EACkB,GAGAJ,EAAMW,cAAcC,MAAM,GAEvCsC,GAAA,IAGXQ,EAAQC,EAAsBC,EAAS,CACrC5D,QACAK,QACAC,WACAJ,cACAK,cACAO,qBACAC,cACAC,cACAgB,WACAM,YACAW,sBACAY,mBAlE8D,CAACpC,EAAOK,KAClEvB,EAAYH,MAAM6B,SAASR,GAC7Ba,EAAUb,GAGVO,EAASP,EAAOK,EAAS"}