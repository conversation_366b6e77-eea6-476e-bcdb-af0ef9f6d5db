import e from"./index-CrrhLRii.js";import{_ as t}from"./index.vue_vue_type_script_setup_true_lang-BoxF5NeN.js";import{_ as o}from"./index.vue_vue_type_script_setup_true_lang-BhI9YChL.js";import{_ as s}from"./index.vue_vue_type_script_setup_true_lang-kAbxK0Ex.js";import n from"./index-CmmArCWT.js";import{_ as r}from"./index.vue_vue_type_script_setup_true_lang-CB3L42GY.js";import i from"./index-Cux34mqh.js";import u from"./index-CgC-1VP8.js";import{d as a,W as p,B as c,o as _,c as d,ag as l,u as f,F as m,e as v,aP as x,R as g}from"./index-CkEhI1Zk.js";const h=/\d/,y=["-","_","/","."];function j(e=""){if(!h.test(e))return e!==e.toLowerCase()}function b(e,t){return e?(Array.isArray(e)?e:function(e){const t=y,o=[];if(!e||"string"!=typeof e)return o;let s,n,r="";for(const i of e){const e=t.includes(i);if(!0===e){o.push(r),r="",s=void 0;continue}const u=j(i);if(!1===n){if(!1===s&&!0===u){o.push(r),r=i,s=u;continue}if(!0===s&&!1===u&&r.length>1){const e=r.at(-1);o.push(r.slice(0,Math.max(0,r.length-1))),r=e+i,s=u;continue}}r+=i,s=u,n=e}return o.push(r),o}(e)).map((e=>function(e){return e?e[0].toUpperCase()+e.slice(1):""}((null==t?void 0:t.normalize)?e.toLowerCase():e))).join(""):""}const C=a({name:"ToolbarTools",__name:"tools",props:{mode:{}},setup(a){const h=a,y=Object.assign({"./Breadcrumb/index.vue":e,"./ColorScheme/index.vue":t,"./Favorites/index.vue":o,"./Fullscreen/index.vue":s,"./I18n/index.vue":n,"./NavSearch/index.vue":r,"./Notification/index.vue":i,"./PageReload/index.vue":u}),j=p(),C=c((()=>{const e=j.settings.toolbar.layout.findIndex((e=>"->"===e)),t=[];return"left-side"===h.mode?j.settings.toolbar.layout.forEach(((o,s)=>{s<e&&"->"!==o&&t.push(o)})):j.settings.toolbar.layout.forEach(((o,s)=>{s>e&&"->"!==o&&t.push(o)})),t}));return(e,t)=>(_(!0),d(m,null,l(f(C),(e=>(_(),d(m,{key:e},[f(j).settings.toolbar[e]?(_(),v(x(f(y)[`./${f(b)(e)}/index.vue`]),{key:0})):g("",!0)],64)))),128))}});export{C as _};
//# sourceMappingURL=tools.vue_vue_type_script_setup_true_lang-CiXRxFW5.js.map
