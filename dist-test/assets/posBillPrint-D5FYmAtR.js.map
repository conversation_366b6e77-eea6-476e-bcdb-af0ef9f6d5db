{"version": 3, "file": "posBillPrint-D5FYmAtR.js", "sources": ["../../src/views/cash/debit/components/posBillPrint.vue"], "sourcesContent": ["<script setup>\r\nimport { computed, ref } from 'vue'\r\n\r\nconst props = defineProps({\r\n  printData: {\r\n    type: Array,\r\n    required: true,\r\n  },\r\n  hotelName: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  operator: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  remark: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n})\r\n\r\n// 获取当前时间作为打印时间\r\nconst printTime = ref(\r\n  new Date()\r\n    .toLocaleString('zh-CN', {\r\n      year: 'numeric',\r\n      month: '2-digit',\r\n      day: '2-digit',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n      second: '2-digit',\r\n      hour12: false,\r\n    })\r\n    .replace(/\\//g, '-')\r\n)\r\n\r\n// 消费科目项目\r\nconst consumeItems = computed(() => {\r\n  return props.printData.filter((item) => item.subType === 'consume_account' || !item.subType)\r\n})\r\n\r\n// 付款科目项目\r\nconst paymentItems = computed(() => {\r\n  return props.printData.filter((item) => item.subType === 'pay_account')\r\n})\r\n\r\n// 消费总额\r\nconst consumeTotal = computed(() => {\r\n  return consumeItems.value.reduce((sum, item) => sum + (item.fee || 0), 0)\r\n})\r\n\r\n// 付款总额\r\nconst paymentTotal = computed(() => {\r\n  return paymentItems.value.reduce((sum, item) => sum + (item.fee || 0), 0)\r\n})\r\n\r\n// 格式化金额\r\nfunction formatAmount(amount) {\r\n  return amount.toFixed(2)\r\n}\r\n\r\n// 格式化时间 - 移除T字符\r\nfunction formatTime(time) {\r\n  if (!time) return ''\r\n  return time.replace('T', ' ').substring(5, 16)\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"print-container\" style=\"width: 76mm; margin: 0 auto; padding: 5mm; font-size: 12px; display: flex; flex-direction: column; color: black; font-family: SimSun, '宋体', sans-serif\">\r\n    <div class=\"hotel-title\" style=\"text-align: center; font-size: 20pt; font-weight: bold; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 10px\">{{ hotelName }}</div>\r\n    <div class=\"bill-title\" style=\"text-align: center; font-size: 14px; font-weight: bold; margin-bottom: 10px\">入账单打印</div>\r\n    <div class=\"print-time\" style=\"margin-bottom: 10px; font-size: 12px\">打印时间: {{ printTime }}</div>\r\n\r\n    <table class=\"info-table\" style=\"width: 100%; border-collapse: collapse; margin-bottom: 10px\">\r\n      <thead>\r\n        <tr>\r\n          <th style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px; background-color: #f0f0f0; font-weight: bold\">账目</th>\r\n          <th style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px; background-color: #f0f0f0; font-weight: bold; text-align: center\">金额</th>\r\n          <th style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px; background-color: #f0f0f0; font-weight: bold; text-align: center\">时间</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr>\r\n          <td colspan=\"3\" style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px; font-weight: bold\">消费科目</td>\r\n        </tr>\r\n        <template v-for=\"(item, index) in consumeItems\" :key=\"index\">\r\n          <tr>\r\n            <td style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px\">{{ item.subName }}</td>\r\n            <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; text-align: center\">{{ formatAmount(item.fee) }}</td>\r\n            <td style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px; text-align: center\">{{ formatTime(item.createTime) }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.remark\">\r\n            <td colspan=\"3\" style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 11px; font-style: italic\">摘要: {{ item.remark }}</td>\r\n          </tr>\r\n        </template>\r\n        <tr>\r\n          <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; font-weight: bold\">合计</td>\r\n          <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; font-weight: bold; text-align: center\">{{ formatAmount(consumeTotal) }}</td>\r\n          <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; font-weight: bold\"></td>\r\n        </tr>\r\n\r\n        <tr>\r\n          <td colspan=\"3\" style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px; font-weight: bold\">付款科目</td>\r\n        </tr>\r\n        <template v-for=\"(item, index) in paymentItems\" :key=\"index\">\r\n          <tr>\r\n            <td style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px\">{{ item.subName }}</td>\r\n            <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; text-align: center\">{{ formatAmount(item.fee) }}</td>\r\n            <td style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 12px; text-align: center\">{{ formatTime(item.createTime) }}</td>\r\n          </tr>\r\n          <tr v-if=\"item.remark\">\r\n            <td colspan=\"3\" style=\"border: 1px solid #000; padding: 4px; text-align: left; font-size: 11px; font-style: italic\">摘要: {{ item.remark }}</td>\r\n          </tr>\r\n        </template>\r\n        <tr>\r\n          <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; font-weight: bold\">合计</td>\r\n          <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; font-weight: bold; text-align: center\">{{ formatAmount(paymentTotal) }}</td>\r\n          <td style=\"border: 1px solid #000; padding: 4px; text-align: right; font-size: 12px; font-weight: bold\"></td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <div class=\"operator\" style=\"margin-top: 10px; width: 100%; font-size: 12px\">操作员: {{ operator }}</div>\r\n\r\n    <div class=\"signature\" style=\"margin-top: 20px; width: 100%; font-size: 12px\">住客签名:</div>\r\n  </div>\r\n</template>\r\n\r\n<style>\r\n@media print {\r\n  @page {\r\n    size: 80mm auto;\r\n    margin: 0;\r\n  }\r\n  body {\r\n    margin: 5mm;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "printTime", "ref", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "consumeItems", "computed", "printData", "filter", "item", "subType", "paymentItems", "consumeTotal", "value", "reduce", "sum", "fee", "paymentTotal", "formatAmount", "amount", "toFixed", "formatTime", "time", "substring"], "mappings": "24DAGA,MAAMA,EAAQC,EAoBRC,EAAYC,GAAA,IACZC,MACDC,eAAe,QAAS,CACvBC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,QAAQ,IAETC,QAAQ,MAAO,MAIdC,EAAeC,GAAS,IACrBf,EAAMgB,UAAUC,QAAQC,GAA0B,oBAAjBA,EAAKC,UAAkCD,EAAKC,YAIhFC,EAAeL,GAAS,IACrBf,EAAMgB,UAAUC,QAAQC,GAA0B,gBAAjBA,EAAKC,YAIzCE,EAAeN,GAAS,IACrBD,EAAaQ,MAAMC,QAAO,CAACC,EAAKN,IAASM,GAAON,EAAKO,KAAO,IAAI,KAInEC,EAAeX,GAAS,IACrBK,EAAaE,MAAMC,QAAO,CAACC,EAAKN,IAASM,GAAON,EAAKO,KAAO,IAAI,KAIzE,SAASE,EAAaC,GACb,OAAAA,EAAOC,QAAQ,EACxB,CAGA,SAASC,EAAWC,GACd,OAACA,EACEA,EAAKlB,QAAQ,IAAK,KAAKmB,UAAU,EAAG,IADzB,EAEpB"}