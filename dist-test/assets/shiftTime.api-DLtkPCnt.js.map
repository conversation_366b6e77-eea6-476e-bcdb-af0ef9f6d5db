{"version": 3, "file": "shiftTime.api-DLtkPCnt.js", "sources": ["../../src/api/modules/pms/shift/shiftTime.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nexport default {\r\n\r\n  // 获得班次设置列表\r\n  getShiftTimeList: (data: any) => api.get('/admin-api/pms/shift-time/list', { params: data }),\r\n\r\n  /**\r\n   * 获得班次设置\r\n   * @param data\r\n   */\r\n  getShiftTime: (data: {\r\n    gcode: string\r\n    hcode: string\r\n    shiftCode: string\r\n  }) => api.get('/admin-api/pms/shift-time/get', { params: data }),\r\n\r\n  // 更新班次设置\r\n  updateShiftTime: (data: any) => api.put('/admin-api/pms/shift-time/update', data),\r\n\r\n  // 获得交班报表\r\n  handoverReport: (data: any) => api.get('/admin-api/pms/account/handover-report', { params: data }),\r\n\r\n\t// 获得交班报表(收付实现制)\r\n\thandoverReportCashRealization: (data: any) => api.get('/admin-api/pms/account/handover-report-cash-realization', { params: data }),\r\n\r\n\t/**\r\n\t * 获得可交互的班次\r\n\t * @param data\r\n\t */\r\n\tgetChangeShiftTime: (data: {\r\n\t\tgcode: string\r\n\t\thcode: string\r\n\t}) => api.get('/admin-api/pms/shift-time/get-change-shift', { params: data }),\r\n\r\n\t/**\r\n\t * 交班\r\n\t * @param data\r\n\t */\r\n\thandover: (data: {\r\n\t\tgcode: string\r\n\t\thcode: string\r\n\t\tshiftCode: string\r\n\t}) => api.post('/admin-api/pms/shift-time/handover', data),\r\n\r\n\t/**\r\n\t * 交换班次\r\n\t * @param data\r\n\t * @param config - 可选的请求配置，包括请求头\r\n\t */\r\n\tgetChangeShiftByShiftSet: (data: {\r\n\t\tgcode: string\r\n\t\thcode: string\r\n\t}, config?: any) => api.get('/admin-api/pms/shift-time/get-change-shift-by-shift-set', { params: data, ...config }),\r\n}\r\n"], "names": ["shiftTimeApi", "getShiftTimeList", "data", "api", "get", "params", "getShiftTime", "updateShiftTime", "put", "handoverReport", "handoverReportCashRealization", "getChangeShiftTime", "handover", "post", "getChangeShiftByShiftSet", "config"], "mappings": "wCAEA,MAAeA,EAAA,CAGbC,iBAAmBC,GAAcC,EAAIC,IAAI,iCAAkC,CAAEC,OAAQH,IAMrFI,aAAeJ,GAITC,EAAIC,IAAI,gCAAiC,CAAEC,OAAQH,IAGzDK,gBAAkBL,GAAcC,EAAIK,IAAI,mCAAoCN,GAG5EO,eAAiBP,GAAcC,EAAIC,IAAI,yCAA0C,CAAEC,OAAQH,IAG5FQ,8BAAgCR,GAAcC,EAAIC,IAAI,0DAA2D,CAAEC,OAAQH,IAM3HS,mBAAqBT,GAGfC,EAAIC,IAAI,6CAA8C,CAAEC,OAAQH,IAMtEU,SAAWV,GAILC,EAAIU,KAAK,qCAAsCX,GAOrDY,yBAA0B,CAACZ,EAGxBa,IAAiBZ,EAAIC,IAAI,0DAA2D,CAAEC,OAAQH,KAASa"}