{"version": 3, "file": "bookDetails-LczAhzor.js", "sources": ["../../src/views/room/booking/bookDetails.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\":{\r\n    \"roomTypeOccupyDetails\": \"Room Type Occupancy Details\",\r\n    \"roomTypeName\": \"Room Type Name\",\r\n    \"roomNumber\": \"Room Number\",\r\n    \"guestPhone\": \"Guest / Phone\",\r\n    \"planCheckinTime\": \"Planned Check-in Time\",\r\n    \"planCheckoutTime\": \"Planned Check-out Time\",\r\n\t\t\"repairStartTime\": \"Repair Start Time\",\r\n\t\t\"repairEndTime\": \"Repair End Time\",\r\n\t\t\"checkinTime\": \"Checkin Time\",\r\n    \"guestSource\": \"Guest Source\",\r\n    \"orderSource\": \"Order Source\",\r\n    \"repairReason\": \"Repair Reason\",\r\n    \"availableRoomsDesc\": \"Available rooms = Total rooms - Occupied - Booked + Checked-out - Under repair\",\r\n    \"lockedRoomsDesc\": \"Locked rooms do not occupy inventory\",\r\n    \"startTime\": \"Start Time\",\r\n    \"endTime\": \"End Time\",\r\n    \"staying\": \"Staying\",\r\n    \"booked\": \"Booked\",\r\n    \"preCheckout\": \"preCheckout\",\r\n    \"underRepair\": \"Repair\",\r\n    \"blockRooms\": \"Block rooms\",\r\n    \"state\": \"state\",\r\n    \"reservationType\":\"Reservation Type\",\r\n    \"roomsBooked\":\"Rooms Booked\",\r\n    \"checkinTime1\": \"Check-in Time\",\r\n    \"checkoutTime\": \"Check-out Time\",\r\n    \"orderNumber\": \"Order Number\",\r\n    \"actions\": \"Actions\",\r\n    \"view\": \"View\"\r\n  },\r\n  \"zh-cn\":{\r\n    \"roomTypeOccupyDetails\": \"房型占用详情\",\r\n    \"roomTypeName\": \"房型名称\",\r\n    \"roomNumber\": \"房号\",\r\n    \"guestPhone\": \"客人/电话\",\r\n    \"planCheckinTime\": \"预抵时间\",\r\n    \"planCheckoutTime\": \"预离时间\",\r\n\t\t\"repairStartTime\": \"维修开始时间\",\r\n\t\t\"repairEndTime\": \"维修结束时间\",\r\n\t\t\"checkinTime\": \"入住时间\",\r\n    \"guestSource\": \"客源\",\r\n    \"orderSource\": \"订单来源\",\r\n    \"repairReason\": \"维修原因\",\r\n    \"availableRoomsDesc\": \"可售数 = 总房数 - 在住 - 预订 + 预离 - 维修\",\r\n    \"lockedRoomsDesc\": \"锁房不占用库存\",\r\n    \"startTime\": \"开始时间\",\r\n    \"endTime\": \"结束时间\",\r\n    \"staying\": \"在住\",\r\n    \"booked\": \"预订\",\r\n    \"preCheckout\": \"预离\",\r\n    \"underRepair\": \"维修\",\r\n    \"blockRooms\": \"占房\",\r\n    \"state\": \"状态\",\r\n    \"reservationType\": \"入住类型\",\r\n    \"roomsBooked\": \"占用间数\",\r\n    \"checkinTime1\": \"起始时间\",\r\n    \"checkoutTime\": \"结束时间\",\r\n    \"orderNumber\": \"订单号\",\r\n    \"actions\": \"操作\",\r\n    \"view\": \"查看\"\r\n  },\r\n  \"km\": {\r\n    \"roomTypeOccupyDetails\": \"ព័ត៌មានលម្អិតអំពីការប្រើប្រាស់ប្រភេទបន្ទប់\",\r\n    \"roomTypeName\": \"ឈ្មោះប្រភេទបន្ទប់\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"guestPhone\": \"ភ្ញៀវ/ទូរស័ព្ទ\",\r\n    \"planCheckinTime\": \"ពេលវេលាមកដល់ដែលបានគ្រោងទុក\",\r\n    \"planCheckoutTime\": \"ពេលវេលាចេញដែលបានគ្រោងទុក\",\r\n    \"repairStartTime\": \"ពេលវេលាចាប់ផ្តើមជួសជុល\",\r\n    \"repairEndTime\": \"ពេលវេលាបញ្ចប់ជួសជុល\",\r\n    \"checkinTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n    \"guestSource\": \"ប្រភពភ្ញៀវ\",\r\n    \"orderSource\": \"ប្រភពលំដាប់\",\r\n    \"repairReason\": \"មូលហេតុជួសជុល\",\r\n    \"availableRoomsDesc\": \"បន្ទប់ដែលអាចលក់បាន = ចំនួនបន្ទប់សរុប - កំពុងស្នាក់នៅ - បានកក់ + កំពុងចេញ - កំពុងជួសជុល\",\r\n    \"lockedRoomsDesc\": \"បន្ទប់ដែលបានចាក់សោមិនប្រើប្រាស់ស្តុក\",\r\n    \"startTime\": \"ពេលវេលាចាប់ផ្តើម\",\r\n    \"endTime\": \"ពេលវេលាបញ្ចប់\",\r\n    \"staying\": \"កំពុងស្នាក់នៅ\",\r\n    \"booked\": \"បានកក់\",\r\n    \"preCheckout\": \"កំពុងចេញ\",\r\n    \"underRepair\": \"កំពុងជួសជុល\",\r\n    \"blockRooms\": \"កាន់កាប់បន្ទប់\",\r\n    \"state\": \"ស្ថានភាព\",\r\n    \"reservationType\": \"ប្រភេទការស្នាក់នៅ\",\r\n    \"roomsBooked\": \"ចំនួនបន្ទប់ដែលកាន់កាប់\",\r\n    \"checkinTime1\": \"ពេលវេលាចាប់ផ្តើម\",\r\n    \"checkoutTime\": \"ពេលវេលាបញ្ចប់\",\r\n    \"orderNumber\": \"លេខលំដាប់\",\r\n    \"actions\": \"សកម្មភាព\",\r\n    \"view\": \"មើល\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { queryTypes } from './bookDetails.ts'\r\nimport type bookDetails from './bookDetails.ts'\r\nimport { bookApi, orderApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\nimport { ElButton, ElDialog, ElPagination, ElTable, ElTableColumn } from 'element-plus'\r\n/** 传入的props变量 */\r\nconst props = defineProps({\r\n  /** 弹窗是否显示 */\r\n  modelValue: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  /** 开始时间 */\r\n  planCheckinTime: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  /** 结束时间 */\r\n  planCheckoutTime: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  /** 房间代码 */\r\n  deatilRtcode: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  /** 日期 */\r\n  deatilDate: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n  /** 类型 */\r\n  roomType: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n})\r\n/**\r\n * 发射给父组件的方法\r\n * 用于子组件给父组件传值或调用父组件方法\r\n */\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n}>()\r\nconst router = useRouter()\r\nconst { t } = useI18n()\r\n/** 获取缓存数据 */\r\nconst userStore = useUserStore()\r\n/** 弹框显示状态 */\r\nconst dialogVisible = computed({\r\n  get: () => props.modelValue,\r\n  set: (value) => emits('update:modelValue', value),\r\n})\r\n/**\r\n * tabs 名称\r\n * @ {staying} 在住\r\n * @ {booked} 预订\r\n * @ {underRepair} 维修\r\n * @ {preCheckout} 预离\r\n * @ {blockRooms} 占房\r\n */\r\nconst state = ref('staying')\r\n/** 列表查询条件 */\r\nconst queryParams = reactive<bookDetails.queryParams>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  pageNo: 1,\r\n  pageSize: 10,\r\n  startTime: props.planCheckinTime,\r\n  endTime: props.planCheckoutTime,\r\n  rtCode: props.deatilRtcode,\r\n  date: props.deatilDate,\r\n})\r\n/** tabs切换页 */\r\nconst stateList = ref<bookDetails.stateListTypes[]>([])\r\nwatchEffect(() => {\r\n  let stateComponents: bookDetails.stateListTypes[] = [\r\n    { num: 0, label: t('staying'), value: 'staying' },\r\n    { num: 0, label: t('booked'), value: 'booked' },\r\n    { num: 0, label: t('underRepair'), value: 'underRepair' },\r\n  ]\r\n  if (props.roomType != 'roomTypeForecasting') {\r\n    stateComponents = [...stateComponents, { num: 0, label: t('preCheckout'), value: 'preCheckout' }, { num: 0, label: t('blockRooms'), value: 'blockRooms' }]\r\n  }\r\n  stateList.value = stateComponents\r\n})\r\n\r\n/** 分页配置 */\r\nconst pagination = reactive({\r\n  currentPage: 1,\r\n  pageSize: 10,\r\n  total: 0,\r\n  layout: 'prev, pager, next',\r\n})\r\n\r\n/** 加载状态 */\r\nconst loading = ref(false)\r\n/** 后台获取到的数据对象(在住) */\r\nconst tableData1 = ref<bookDetails.orderPager[]>([])\r\n/** 后台获取到的数据对象(预订) */\r\nconst tableData2 = ref<bookDetails.pageBook[]>([])\r\n/** 后台获取到的数据对象(维修) */\r\nconst tableData3 = ref<bookDetails.getRepairRoom[]>([])\r\n/** 后台获取到的数据对象(预离) */\r\nconst tableData4 = ref<bookDetails.todayPlanCheckOut[]>([])\r\n/** 后台获取到的数据对象(占房) */\r\nconst tableData5 = ref<bookDetails.BookConflictRespVO[]>([])\r\n\r\n/** 格式化日期 */\r\nfunction formatDateTime(date: string) {\r\n  if (!date) {\r\n    return '--'\r\n  }\r\n  return dayjs(date).format('MM/DD HH:mm')\r\n}\r\n\r\n/** 在住 */\r\nasync function getCheckin() {\r\n  loading.value = true\r\n  const { data } = await orderApi.orderPagerList({ ...queryParams, timeType: '2' })\r\n  loading.value = false\r\n  stateList.value[0].num = data.total\r\n  tableData1.value = data.list\r\n  pagination.total = data.total\r\n  pagination.currentPage = data.pageNo\r\n  pagination.pageSize = data.pageSize\r\n}\r\n/** 预订 */\r\nasync function getBook() {\r\n  loading.value = true\r\n  const { data } = await bookApi.getPageBook(queryParams)\r\n  loading.value = false\r\n  stateList.value[1].num = data.total\r\n  tableData2.value = data.list\r\n  pagination.total = data.total\r\n  pagination.currentPage = data.pageNo\r\n  pagination.pageSize = data.pageSize\r\n}\r\n/** 维修 */\r\nasync function getRepair() {\r\n  loading.value = true\r\n  const { data } = await orderApi.getRepairRoom(queryParams)\r\n  loading.value = false\r\n  stateList.value[2].num = data.total\r\n  tableData3.value = data.list\r\n  pagination.total = data.total\r\n  pagination.currentPage = data.pageNo\r\n  pagination.pageSize = data.pageSize\r\n}\r\n/** 预离 */\r\nasync function getCheckout() {\r\n  loading.value = true\r\n  const { data } = await orderApi.getCheckoutList(queryParams)\r\n  loading.value = false\r\n  stateList.value[3].num = data.total\r\n  tableData4.value = data.list\r\n  pagination.total = data.total\r\n  pagination.currentPage = data.pageNo\r\n  pagination.pageSize = data.pageSize\r\n}\r\n/** 占用房间 */\r\nasync function getBlockRooms() {\r\n  loading.value = true\r\n  const { data } = await orderApi.getBookConflict(queryParams)\r\n  loading.value = false\r\n  stateList.value[4].num = data.total\r\n  tableData5.value = data.list\r\n  pagination.total = data.total\r\n  pagination.currentPage = data.pageNo\r\n  pagination.pageSize = data.pageSize\r\n}\r\n/** 这里的类型是表格配置文件里定义的类型 */\r\nasync function handleAction(command: string, row: any) {\r\n  switch (command) {\r\n    // 查看\r\n    case 'view':\r\n      const query: queryTypes = {\r\n        fullscreen: 'true',\r\n        modelValue: 'true',\r\n        tabName: 'detail',\r\n        no: '',\r\n        noType: '',\r\n      }\r\n      if (state.value == 'staying' || state.value == 'preCheckout') {\r\n        query.no = row.orderNo\r\n        query.noType = 'order'\r\n      }\r\n      if (state.value == 'booked') {\r\n        query.no = row.bookNo\r\n        query.noType = 'book'\r\n      }\r\n      if (state.value == 'blockRooms' && row.no) {\r\n        query.no = row.no\r\n        query.noType = row.noType\r\n      }\r\n      window.open(router.resolve({ name: 'orderDetails', query: { ...query } }).href, '_blank')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n/**\r\n * 分页改变\r\n * @param page 当前页码\r\n */\r\nfunction handleCurrentChange(page: number) {\r\n  queryParams.pageNo = page\r\n  pagination.currentPage = page\r\n  switch (state.value) {\r\n    case 'staying':\r\n      getCheckin()\r\n      break\r\n    case 'booked':\r\n      getBook()\r\n      break\r\n    case 'preCheckout':\r\n      getCheckout()\r\n      break\r\n    case 'underRepair':\r\n      getRepair()\r\n      break\r\n    case 'blockRooms':\r\n      getBlockRooms()\r\n      break\r\n  }\r\n}\r\n\r\n/** 取消弹框 */\r\nfunction formCancel() {\r\n  dialogVisible.value = false\r\n}\r\n/** 切换类型 */\r\nfunction handleChange(value?: string | number | boolean) {\r\n  queryParams.pageNo = 1\r\n  pagination.currentPage = 1\r\n  switch (value) {\r\n    case 'staying':\r\n      getCheckin()\r\n      break\r\n    case 'booked':\r\n      getBook()\r\n      break\r\n    case 'preCheckout':\r\n      getCheckout()\r\n      break\r\n    case 'underRepair':\r\n      getRepair()\r\n      break\r\n    case 'blockRooms':\r\n      getBlockRooms()\r\n      break\r\n  }\r\n}\r\n\r\nonMounted(async () => {\r\n  if (props.roomType == 'roomTypeForecasting') {\r\n    await Promise.all([getCheckin(), getBook(), getRepair()])\r\n  } else {\r\n    await Promise.all([getCheckin(), getBook(), getCheckout(), getRepair(), getBlockRooms()])\r\n  }\r\n})\r\n</script>\r\n\r\n<template>\r\n  <!-- 弹框 -->\r\n  <ElDialog v-model=\"dialogVisible\" :title=\"t('roomTypeOccupyDetails')\" width=\"1300px\" @close=\"formCancel\">\r\n    <div style=\"min-height: 820px; overflow: auto\">\r\n      <el-radio-group v-model=\"state\" class=\"mb-[16px]\" @change=\"handleChange\">\r\n        <el-radio-button v-for=\"(item, index) in stateList\" :key=\"index\" :value=\"item.value\"> {{ item.label }}({{ item.num }}) </el-radio-button>\r\n      </el-radio-group>\r\n\r\n      <!-- 在住 -->\r\n      <div v-if=\"state === 'staying'\" v-loading=\"loading\">\r\n        <ElTable :data=\"tableData1\" style=\"width: 100%\">\r\n          <ElTableColumn prop=\"rtName\" :label=\"t('roomTypeName')\" />\r\n          <ElTableColumn prop=\"rNo\" :label=\"t('roomNumber')\" />\r\n          <ElTableColumn :label=\"t('guestPhone')\">\r\n            <template #default=\"{ row }\">\r\n              <div>{{ row.name }}</div>\r\n              <div>{{ row.phone }}</div>\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"checkinTime\" :label=\"t('checkinTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.checkinTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"planCheckoutTime\" :label=\"t('planCheckoutTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.planCheckoutTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"guestSrcTypeName\" :label=\"t('guestSource')\" />\r\n          <ElTableColumn prop=\"orderSourceName\" :label=\"t('orderSource')\" />\r\n          <ElTableColumn :label=\"t('actions')\">\r\n            <template #default=\"{ row }\">\r\n              <ElButton link type=\"primary\" @click=\"handleAction('view', row)\"> 查看 </ElButton>\r\n            </template>\r\n          </ElTableColumn>\r\n        </ElTable>\r\n        <ElPagination v-if=\"pagination.total > 0\" v-model:current-page=\"pagination.currentPage\" :page-size=\"pagination.pageSize\" :total=\"pagination.total\" layout=\"prev, pager, next\" class=\"mt-4 justify-center\" @current-change=\"handleCurrentChange\" />\r\n      </div>\r\n\r\n      <!-- 预订 -->\r\n      <div v-if=\"state === 'booked'\" v-loading=\"loading\">\r\n        <ElTable :data=\"tableData2\" style=\"width: 100%\">\r\n          <ElTableColumn prop=\"rtName\" :label=\"t('roomTypeName')\" />\r\n          <ElTableColumn prop=\"rNo\" :label=\"t('roomNumber')\" />\r\n          <ElTableColumn :label=\"t('guestPhone')\">\r\n            <template #default=\"{ row }\">\r\n              <div>{{ row.name }}</div>\r\n              <div>{{ row.phone }}</div>\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"planCheckinTime\" :label=\"t('planCheckinTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.planCheckinTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"planCheckoutTime\" :label=\"t('planCheckoutTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.planCheckoutTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"guestSrcTypeName\" :label=\"t('guestSource')\" />\r\n          <ElTableColumn prop=\"orderSourceName\" :label=\"t('orderSource')\" />\r\n          <ElTableColumn :label=\"t('actions')\">\r\n            <template #default=\"{ row }\">\r\n              <ElButton link type=\"primary\" @click=\"handleAction('view', row)\"> 查看 </ElButton>\r\n            </template>\r\n          </ElTableColumn>\r\n        </ElTable>\r\n        <ElPagination v-if=\"pagination.total > 0\" v-model:current-page=\"pagination.currentPage\" :page-size=\"pagination.pageSize\" :total=\"pagination.total\" layout=\"prev, pager, next\" class=\"mt-4 justify-center\" @current-change=\"handleCurrentChange\" />\r\n      </div>\r\n\r\n      <!-- 维修 -->\r\n      <div v-if=\"state === 'underRepair'\" v-loading=\"loading\">\r\n        <ElTable :data=\"tableData3\" style=\"width: 100%\">\r\n          <ElTableColumn prop=\"rtName\" :label=\"t('roomTypeName')\" />\r\n          <ElTableColumn prop=\"rNo\" :label=\"t('roomNumber')\" />\r\n          <ElTableColumn :label=\"t('guestPhone')\">\r\n            <template #default=\"{ row }\">\r\n              <div>{{ row.name }}</div>\r\n              <div>{{ row.phone }}</div>\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"repairStartTime\" :label=\"t('repairStartTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.repairStartTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"repairEndTime\" :label=\"t('repairEndTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.repairEndTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"guestSrcTypeName\" :label=\"t('guestSource')\" />\r\n          <ElTableColumn prop=\"orderSourceName\" :label=\"t('orderSource')\" />\r\n        </ElTable>\r\n        <ElPagination v-if=\"pagination.total > 0\" v-model:current-page=\"pagination.currentPage\" :page-size=\"pagination.pageSize\" :total=\"pagination.total\" layout=\"prev, pager, next\" class=\"mt-4 justify-center\" @current-change=\"handleCurrentChange\" />\r\n      </div>\r\n\r\n      <!-- 预离 -->\r\n      <div v-if=\"state === 'preCheckout'\" v-loading=\"loading\">\r\n        <ElTable :data=\"tableData4\" style=\"width: 100%\">\r\n          <ElTableColumn prop=\"rtName\" :label=\"t('roomTypeName')\" />\r\n          <ElTableColumn prop=\"rNo\" :label=\"t('roomNumber')\" />\r\n          <ElTableColumn :label=\"t('guestPhone')\">\r\n            <template #default=\"{ row }\">\r\n              <div>{{ row.name }}</div>\r\n              <div>{{ row.phone }}</div>\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"planCheckinTime\" :label=\"t('planCheckinTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.planCheckinTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"planCheckoutTime\" :label=\"t('planCheckoutTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.planCheckoutTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"guestSrcTypeName\" :label=\"t('guestSource')\" />\r\n          <ElTableColumn prop=\"orderSourceName\" :label=\"t('orderSource')\" />\r\n          <ElTableColumn :label=\"t('actions')\">\r\n            <template #default=\"{ row }\">\r\n              <ElButton link type=\"primary\" @click=\"handleAction('view', row)\"> 查看 </ElButton>\r\n            </template>\r\n          </ElTableColumn>\r\n        </ElTable>\r\n        <ElPagination v-if=\"pagination.total > 0\" v-model:current-page=\"pagination.currentPage\" :page-size=\"pagination.pageSize\" :total=\"pagination.total\" layout=\"prev, pager, next\" class=\"mt-4 justify-center\" @current-change=\"handleCurrentChange\" />\r\n      </div>\r\n\r\n      <!-- 占房 -->\r\n      <div v-if=\"state === 'blockRooms'\" v-loading=\"loading\">\r\n        <ElTable :data=\"tableData5\" style=\"width: 100%\">\r\n          <ElTableColumn prop=\"rtName\" :label=\"t('roomTypeName')\" />\r\n          <ElTableColumn prop=\"rNo\" :label=\"t('roomNumber')\" />\r\n          <ElTableColumn prop=\"state\" :label=\"t('state')\" />\r\n          <ElTableColumn :label=\"t('guestPhone')\">\r\n            <template #default=\"{ row }\">\r\n              <div>{{ row.name ? row.name : '--' }}</div>\r\n              <div>{{ row.phone }}</div>\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"checkinTypeName\" :label=\"t('reservationType')\" />\r\n          <ElTableColumn prop=\"num\" :label=\"t('roomsBooked')\" />\r\n          <ElTableColumn prop=\"startTime\" :label=\"t('checkinTime1')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.startTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"endTime\" :label=\"t('checkoutTime')\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDateTime(row.endTime) }}\r\n            </template>\r\n          </ElTableColumn>\r\n          <ElTableColumn prop=\"no\" :label=\"t('orderNumber')\" min-width=\"170\" />\r\n          <ElTableColumn :label=\"t('actions')\">\r\n            <template #default=\"{ row }\">\r\n              <ElButton v-if=\"row.no\" link type=\"primary\" @click=\"handleAction('view', row)\"> 查看 </ElButton>\r\n            </template>\r\n          </ElTableColumn>\r\n        </ElTable>\r\n        <ElPagination v-if=\"pagination.total > 0\" v-model:current-page=\"pagination.currentPage\" :page-size=\"pagination.pageSize\" :total=\"pagination.total\" layout=\"prev, pager, next\" class=\"mt-4 justify-center\" @current-change=\"handleCurrentChange\" />\r\n      </div>\r\n\r\n      <div>\r\n        <div>1、{{ t('availableRoomsDesc') }}</div>\r\n        <div>2、{{ t('lockedRoomsDesc') }}</div>\r\n      </div>\r\n    </div>\r\n  </ElDialog>\r\n</template>\r\n\r\n<style scoped lang=\"scss\"></style>\r\n"], "names": ["props", "__props", "emits", "__emit", "router", "useRouter", "t", "useI18n", "userStore", "useUserStore", "dialogVisible", "computed", "get", "modelValue", "set", "value", "state", "ref", "queryParams", "reactive", "gcode", "hcode", "pageNo", "pageSize", "startTime", "planCheckinTime", "endTime", "planCheckoutTime", "rtCode", "deatilRtcode", "date", "deatilDate", "stateList", "watchEffect", "stateComponents", "num", "label", "roomType", "pagination", "currentPage", "total", "layout", "loading", "tableData1", "tableData2", "tableData3", "tableData4", "tableData5", "formatDateTime", "dayjs", "format", "async", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "orderApi", "orderPagerList", "timeType", "list", "getBook", "bookApi", "getPageBook", "getRepair", "getRepairRoom", "getCheckout", "getCheckoutList", "getBlockRooms", "getBookConflict", "handleAction", "command", "row", "query", "fullscreen", "tabName", "no", "noType", "orderNo", "bookNo", "window", "open", "resolve", "name", "href", "handleCurrentChange", "page", "formCancel", "handleChange", "onMounted", "Promise", "all"], "mappings": "s2BA0GA,MAAMA,EAAQC,EAoCRC,EAAQC,EAGRC,EAASC,KACTC,EAAEA,GAAMC,IAERC,EAAYC,IAEZC,EAAgBC,EAAS,CAC7BC,IAAK,IAAMZ,EAAMa,WACjBC,IAAMC,GAAUb,EAAM,oBAAqBa,KAUvCC,EAAQC,EAAI,WAEZC,EAAcC,EAAkC,CACpDC,MAAOZ,EAAUY,MACjBC,MAAOb,EAAUa,MACjBC,OAAQ,EACRC,SAAU,GACVC,UAAWxB,EAAMyB,gBACjBC,QAAS1B,EAAM2B,iBACfC,OAAQ5B,EAAM6B,aACdC,KAAM9B,EAAM+B,aAGRC,EAAYf,EAAkC,IACpDgB,GAAY,KACV,IAAIC,EAAgD,CAClD,CAAEC,IAAK,EAAGC,MAAO9B,EAAE,WAAYS,MAAO,WACtC,CAAEoB,IAAK,EAAGC,MAAO9B,EAAE,UAAWS,MAAO,UACrC,CAAEoB,IAAK,EAAGC,MAAO9B,EAAE,eAAgBS,MAAO,gBAEtB,uBAAlBf,EAAMqC,WACUH,EAAA,IAAIA,EAAiB,CAAEC,IAAK,EAAGC,MAAO9B,EAAE,eAAgBS,MAAO,eAAiB,CAAEoB,IAAK,EAAGC,MAAO9B,EAAE,cAAeS,MAAO,gBAE7IiB,EAAUjB,MAAQmB,CAAA,IAIpB,MAAMI,EAAanB,EAAS,CAC1BoB,YAAa,EACbhB,SAAU,GACViB,MAAO,EACPC,OAAQ,sBAIJC,EAAUzB,GAAI,GAEd0B,EAAa1B,EAA8B,IAE3C2B,EAAa3B,EAA4B,IAEzC4B,GAAa5B,EAAiC,IAE9C6B,GAAa7B,EAAqC,IAElD8B,GAAa9B,EAAsC,IAGzD,SAAS+B,GAAelB,GACtB,OAAKA,EAGEmB,EAAMnB,GAAMoB,OAAO,eAFjB,IAE8B,CAIzCC,eAAeC,KACbV,EAAQ3B,OAAQ,EACV,MAAAsC,KAAEA,SAAeC,EAASC,eAAe,IAAKrC,EAAasC,SAAU,MAC3Ed,EAAQ3B,OAAQ,EAChBiB,EAAUjB,MAAM,GAAGoB,IAAMkB,EAAKb,MAC9BG,EAAW5B,MAAQsC,EAAKI,KACxBnB,EAAWE,MAAQa,EAAKb,MACxBF,EAAWC,YAAcc,EAAK/B,OAC9BgB,EAAWf,SAAW8B,EAAK9B,QAAA,CAG7B4B,eAAeO,KACbhB,EAAQ3B,OAAQ,EAChB,MAAMsC,KAAEA,SAAeM,EAAQC,YAAY1C,GAC3CwB,EAAQ3B,OAAQ,EAChBiB,EAAUjB,MAAM,GAAGoB,IAAMkB,EAAKb,MAC9BI,EAAW7B,MAAQsC,EAAKI,KACxBnB,EAAWE,MAAQa,EAAKb,MACxBF,EAAWC,YAAcc,EAAK/B,OAC9BgB,EAAWf,SAAW8B,EAAK9B,QAAA,CAG7B4B,eAAeU,KACbnB,EAAQ3B,OAAQ,EAChB,MAAMsC,KAAEA,SAAeC,EAASQ,cAAc5C,GAC9CwB,EAAQ3B,OAAQ,EAChBiB,EAAUjB,MAAM,GAAGoB,IAAMkB,EAAKb,MAC9BK,GAAW9B,MAAQsC,EAAKI,KACxBnB,EAAWE,MAAQa,EAAKb,MACxBF,EAAWC,YAAcc,EAAK/B,OAC9BgB,EAAWf,SAAW8B,EAAK9B,QAAA,CAG7B4B,eAAeY,KACbrB,EAAQ3B,OAAQ,EAChB,MAAMsC,KAAEA,SAAeC,EAASU,gBAAgB9C,GAChDwB,EAAQ3B,OAAQ,EAChBiB,EAAUjB,MAAM,GAAGoB,IAAMkB,EAAKb,MAC9BM,GAAW/B,MAAQsC,EAAKI,KACxBnB,EAAWE,MAAQa,EAAKb,MACxBF,EAAWC,YAAcc,EAAK/B,OAC9BgB,EAAWf,SAAW8B,EAAK9B,QAAA,CAG7B4B,eAAec,KACbvB,EAAQ3B,OAAQ,EAChB,MAAMsC,KAAEA,SAAeC,EAASY,gBAAgBhD,GAChDwB,EAAQ3B,OAAQ,EAChBiB,EAAUjB,MAAM,GAAGoB,IAAMkB,EAAKb,MAC9BO,GAAWhC,MAAQsC,EAAKI,KACxBnB,EAAWE,MAAQa,EAAKb,MACxBF,EAAWC,YAAcc,EAAK/B,OAC9BgB,EAAWf,SAAW8B,EAAK9B,QAAA,CAGd4B,eAAAgB,GAAaC,EAAiBC,GAC3C,GAEO,SAFCD,EAEN,CACE,MAAME,EAAoB,CACxBC,WAAY,OACZ1D,WAAY,OACZ2D,QAAS,SACTC,GAAI,GACJC,OAAQ,IAES,WAAf1D,EAAMD,OAAqC,eAAfC,EAAMD,QACpCuD,EAAMG,GAAKJ,EAAIM,QACfL,EAAMI,OAAS,SAEE,UAAf1D,EAAMD,QACRuD,EAAMG,GAAKJ,EAAIO,OACfN,EAAMI,OAAS,QAEE,cAAf1D,EAAMD,OAAyBsD,EAAII,KACrCH,EAAMG,GAAKJ,EAAII,GACfH,EAAMI,OAASL,EAAIK,QAErBG,OAAOC,KAAK1E,EAAO2E,QAAQ,CAAEC,KAAM,eAAgBV,MAAO,IAAKA,KAAWW,KAAM,SAChF,CAGJ,CAMF,SAASC,GAAoBC,GAG3B,OAFAjE,EAAYI,OAAS6D,EACrB7C,EAAWC,YAAc4C,EACjBnE,EAAMD,OACZ,IAAK,UACQqC,KACX,MACF,IAAK,SACKM,KACR,MACF,IAAK,cACSK,KACZ,MACF,IAAK,cACOF,KACV,MACF,IAAK,aACWI,KAElB,CAIF,SAASmB,KACP1E,EAAcK,OAAQ,CAAA,CAGxB,SAASsE,GAAatE,GAGpB,OAFAG,EAAYI,OAAS,EACrBgB,EAAWC,YAAc,EACjBxB,GACN,IAAK,UACQqC,KACX,MACF,IAAK,SACKM,KACR,MACF,IAAK,cACSK,KACZ,MACF,IAAK,cACOF,KACV,MACF,IAAK,aACWI,KAElB,QAGFqB,GAAUnC,UACc,uBAAlBnD,EAAMqC,eACFkD,QAAQC,IAAI,CAACpC,KAAcM,KAAWG,aAEtC0B,QAAQC,IAAI,CAACpC,KAAcM,KAAWK,KAAeF,KAAaI,MAAgB"}