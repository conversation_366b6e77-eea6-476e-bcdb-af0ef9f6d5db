{"version": 3, "file": "sensitive-la8iBfdn.js", "sources": ["../../src/utils/sensitive.ts"], "sourcesContent": ["/**\r\n * 数据脱敏工具类\r\n */\r\n\r\n/**\r\n * 手机号码脱敏\r\n * @param phone 手机号码\r\n * @returns 脱敏后的手机号码\r\n */\r\nexport function maskPhone(phone: string): string {\r\n  if (!phone) return ''\r\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2')\r\n}\r\n\r\n/**\r\n * 姓名脱敏\r\n * @param name 姓名\r\n * @returns 脱敏后的姓名\r\n */\r\nexport function maskName(name: string): string {\r\n  if (!name) return ''\r\n  if (name.length <= 2) {\r\n    return name.substr(0, 1) + '*'\r\n  }\r\n  const stars = '*'.repeat(name.length - 2)\r\n  return name.substr(0, 1) + stars + name.substr(-1)\r\n}\r\n\r\n/**\r\n * 地址脱敏 - 保留省市区/县，后面全部为*\r\n * @param address 地址\r\n * @returns 脱敏后的地址\r\n */\r\nexport function maskAddress(address: string): string {\r\n  if (!address) return ''\r\n\r\n  // 常见省市区/县模式匹配，按优先级排序\r\n  const patterns = [\r\n    // 完整格式：XX省XX市XX区/县（包括市辖区和县级市）\r\n    /^(.{2,3}省.{2,6}市.{2,6}[区县市])/,\r\n    // 直辖市格式：XX市XX区/县\r\n    /^(.{2,3}市.{2,6}[区县])/,\r\n    // 自治区格式：XX自治区XX市XX区/县\r\n    /^(.{2,8}自治区.{2,6}市.{2,6}[区县市])/,\r\n    // 特别行政区格式：XX特别行政区XX区\r\n    /^(.{2,8}特别行政区.{2,6}区)/,\r\n    // 省直辖县级行政区：XX省XX县\r\n    /^(.{2,3}省.{2,6}县)/,\r\n    // 地级市下辖县：XX省XX市XX县\r\n    /^(.{2,3}省.{2,6}市.{2,6}县)/,\r\n    // 自治州格式：XX省XX自治州XX县\r\n    /^(.{2,3}省.{2,8}自治州.{2,6}[县市])/,\r\n    // 盟格式（内蒙古）：XX自治区XX盟XX旗/县\r\n    /^(.{2,8}自治区.{2,6}盟.{2,6}[旗县])/,\r\n    // 简化格式：至少保留前6个字符作为省市区\r\n    /^(.{6})/\r\n  ]\r\n\r\n  for (const pattern of patterns) {\r\n    const match = address.match(pattern)\r\n    if (match) {\r\n      const regionPart = match[1]\r\n      const remainingLength = address.length - regionPart.length\r\n      if (remainingLength > 0) {\r\n        return regionPart + '*'.repeat(remainingLength)\r\n      } else {\r\n        return regionPart\r\n      }\r\n    }\r\n  }\r\n\r\n  // 如果都不匹配，保留前3个字符\r\n  if (address.length <= 3) {\r\n    return address\r\n  }\r\n  return address.substr(0, 3) + '*'.repeat(address.length - 3)\r\n}\r\n\r\n/**\r\n * 身份证号码脱敏\r\n * @param idNo 身份证号码\r\n * @returns 脱敏后的身份证号码\r\n */\r\nexport function maskIdNo(idNo: string): string {\r\n  if (!idNo) return ''\r\n  return idNo.replace(/^(.{6})(.{8})(.+)$/, '$1********$3')\r\n}\r\n\r\n/**\r\n * 通用脱敏方法\r\n * @param value 需要脱敏的值\r\n * @param type 脱敏类型：phone-手机号, name-姓名, address-地址, idNo-身份证号\r\n * @returns 脱敏后的值\r\n */\r\nexport function maskSensitive(value: string, type: 'phone' | 'name' | 'address' | 'idNo'): string {\r\n  if (!value) return ''\r\n\r\n  switch (type) {\r\n    case 'phone':\r\n      return maskPhone(value)\r\n    case 'name':\r\n      return maskName(value)\r\n    case 'address':\r\n      return maskAddress(value)\r\n    case 'idNo':\r\n      return maskIdNo(value)\r\n    default:\r\n      return value\r\n  }\r\n}\r\n\r\n/**\r\n * 存储原始值的 Map\r\n */\r\nconst originalValueMap = new Map<string, string>()\r\n\r\n/**\r\n * 生成唯一键值\r\n */\r\nfunction generateKey(value: string, type: string): string {\r\n  return `${type}_${value}`\r\n}\r\n\r\n/**\r\n * 保存原始值并返回脱敏后的值\r\n */\r\nexport function saveAndMask(value: string, type: 'phone' | 'name' | 'address' | 'idNo'): string {\r\n  if (!value) return ''\r\n  const key = generateKey(value, type)\r\n  originalValueMap.set(key, value)\r\n  return maskSensitive(value, type)\r\n}\r\n\r\n/**\r\n * 获取原始值\r\n */\r\nexport function getOriginalValue(maskedValue: string, type: 'phone' | 'name' | 'address' | 'idNo'): string {\r\n  if (!maskedValue) return ''\r\n  const key = generateKey(maskedValue, type)\r\n  return originalValueMap.get(key) || maskedValue\r\n}\r\n\r\n/**\r\n * 清除存储的原始值\r\n */\r\nexport function clearOriginalValues(): void {\r\n  originalValueMap.clear()\r\n}\r\n"], "names": ["maskSensitive", "value", "type", "phone", "replace", "name", "length", "substr", "stars", "repeat", "<PERSON><PERSON><PERSON>", "address", "patterns", "pattern", "match", "regionPart", "remaining<PERSON><PERSON>th", "<PERSON><PERSON><PERSON><PERSON>", "idNo", "originalValueMap", "Map", "saveAndMask", "key", "<PERSON><PERSON>ey", "set", "clear<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear"], "mappings": "AA8FgB,SAAAA,EAAcC,EAAeC,GACvC,IAACD,EAAc,MAAA,GAEnB,OAAQC,GACN,IAAK,QACH,OA1FoBC,EA0FHF,GAxFdE,EAAMC,QAAQ,sBAAuB,YADzB,GA0FjB,IAAK,OACH,OAlFC,SAAkBC,GACnB,IAACA,EAAa,MAAA,GACd,GAAAA,EAAKC,QAAU,EACjB,OAAOD,EAAKE,OAAO,EAAG,GAAK,IAE7B,MAAMC,EAAQ,IAAIC,OAAOJ,EAAKC,OAAS,GAChC,OAAAD,EAAKE,OAAO,EAAG,GAAKC,EAAQH,EAAKE,QAAS,EACnD,CA2EaG,CAAST,GAClB,IAAK,UACH,OAtEC,SAAqBU,GACtB,IAACA,EAAgB,MAAA,GAGrB,MAAMC,EAAW,CAEf,+BAEA,uBAEA,iCAEA,wBAEA,oBAEA,2BAEA,gCAEA,gCAEA,WAGF,IAAA,MAAWC,KAAWD,EAAU,CACxB,MAAAE,EAAQH,EAAQG,MAAMD,GAC5B,GAAIC,EAAO,CACH,MAAAC,EAAaD,EAAM,GACnBE,EAAkBL,EAAQL,OAASS,EAAWT,OACpD,OAAIU,EAAkB,EACbD,EAAa,IAAIN,OAAOO,GAExBD,CACT,CACF,CAIE,OAAAJ,EAAQL,QAAU,EACbK,EAEFA,EAAQJ,OAAO,EAAG,GAAK,IAAIE,OAAOE,EAAQL,OAAS,EAC5D,CA2BaW,CAAYhB,GACrB,IAAK,OACH,OAtBmBiB,EAsBHjB,GApBbiB,EAAKd,QAAQ,qBAAsB,gBADxB,GAsBhB,QACS,OAAAH,EAxBN,IAAkBiB,EA1ECf,CAoG1B,CAKA,MAAMgB,MAAuBC,IAYb,SAAAC,EAAYpB,EAAeC,GACrC,IAACD,EAAc,MAAA,GACb,MAAAqB,EATR,SAAqBrB,EAAeC,GAC3B,MAAA,GAAGA,KAAQD,GACpB,CAOcsB,CAAYtB,EAAOC,GAExB,OADUiB,EAAAK,IAAIF,EAAKrB,GACnBD,EAAcC,EAAOC,EAC9B,CAcO,SAASuB,IACdN,EAAiBO,OACnB"}