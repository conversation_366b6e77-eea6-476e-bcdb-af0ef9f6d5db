{"version": 3, "file": "splitAccount-DpqmmImE.js", "sources": ["../../src/views/order/info/components/orderdetail/splitAccount.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"splitAccount\": \"Split Account\",\r\n    \"enterAccount\": \"Please enter the transfer account\",\r\n    \"enterRemark\": \"Please enter a remark\",\r\n    \"splitSuccess\": \"Account split successfully\",\r\n    \"amount\": \"Amount($)\",\r\n    \"remark\": \"Remark\",\r\n    \"alreadySplit\": \"Already split: ${splited}\",\r\n    \"notSplit\": \"Not split: ${noStriped}\",\r\n    \"totalAmount\": \"Total: ${fee}\",\r\n    \"cancel\": \"Cancel\",\r\n    \"confirm\": \"Confirm\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"splitAccount\": \"拆账\",\r\n    \"enterAccount\": \"请输入转入账户\",\r\n    \"enterRemark\": \"请输入备注\",\r\n    \"splitSuccess\": \"拆账成功\",\r\n    \"amount\": \"金额(元)\",\r\n    \"remark\": \"备注\",\r\n    \"alreadySplit\": \"已拆：￥{splited}\",\r\n    \"notSplit\": \"未拆：￥{noStriped}\",\r\n    \"totalAmount\": \"总额：￥{fee}\",\r\n    \"cancel\": \"取消\",\r\n    \"confirm\": \"确定\"\r\n  },\r\n  \"km\": {\r\n    \"splitAccount\": \"បែងចែកគណនី\",\r\n    \"enterAccount\": \"សូមបញ្ចូលគណនីផ្ទេរ\",\r\n    \"enterRemark\": \"សូមបញ្ចូលចំណាំ\",\r\n    \"splitSuccess\": \"បែងចែកគណនីបានជោគជ័យ\",\r\n    \"amount\": \"ចំនួនទឹកប្រាក់($)\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"alreadySplit\": \"បានបែងចែក៖ ${splited}\",\r\n    \"notSplit\": \"មិនបានបែងចែក៖ ${noStriped}\",\r\n    \"totalAmount\": \"សរុប៖ ${fee}\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"confirm\": \"បញ្ជាក់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { accountApi } from '@/api/modules'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { Minus, Plus } from '@element-plus/icons-vue'\r\n\r\ninterface AccountAccNoFeeModel {\r\n  no: string\r\n  accNo: string\r\n  togetherCode: string\r\n  fee: number\r\n}\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    /** 账单号 */\r\n    // accNo: string | number\r\n    account: AccountAccNoFeeModel\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst userStore = useUserStore()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  accNo: props.account.accNo,\r\n  accountList: [] as {\r\n    fee: number\r\n    remark: string\r\n  }[],\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst formRules = ref<FormRules>({\r\n  no: [{ required: true, message: t('enterAccount'), trigger: 'blur' }],\r\n  remark: [{ required: true, message: t('enterRemark'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(async () => {\r\n  const _fee = props.account.fee / 2\r\n  if (_fee > 0) {\r\n    form.value.accountList.push({ fee: _fee, remark: '' })\r\n    form.value.accountList.push({ fee: _fee, remark: '' })\r\n  }\r\n})\r\nconst formRef = ref<FormInstance>()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nfunction add() {\r\n  form.value.accountList.push({ fee: 0, remark: '' })\r\n}\r\n\r\nfunction remove(item: any) {\r\n  form.value.accountList.splice(form.value.accountList.indexOf(item), 1)\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        accountApi.splitAccount(form.value).then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success({\r\n              message: t('splitSuccess'),\r\n              type: 'success',\r\n              center: true,\r\n            })\r\n            onCancel()\r\n            emits('success')\r\n          } else {\r\n            ElMessage.error(res.msg)\r\n          }\r\n        })\r\n      }\r\n    })\r\n}\r\nconst splited = computed(() => {\r\n  return form.value.accountList.reduce((prev, cur) => {\r\n    return prev + cur.fee\r\n  }, 0)\r\n})\r\n\r\nconst noStriped = computed(() => {\r\n  return props.account.fee - splited.value\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" :title=\"t('splitAccount')\" width=\"800px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\">\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"90px\" label-suffix=\"：\">\r\n        <el-row v-for=\"(item, index) in form.accountList\" :key=\"index\" :gutter=\"10\">\r\n          <el-col :span=\"10\">\r\n            <el-form-item :label=\"t('amount')\">\r\n              <el-input-number v-model=\"item.fee\" :min=\"0\" :max=\"props.account.fee\" :step=\"1\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item :label=\"t('remark')\">\r\n              <el-input v-model=\"item.remark\" maxlength=\"200\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-button v-if=\"form.accountList.length > 1\" circle :icon=\"Minus\" type=\"danger\" plain @click=\"remove(item)\" />\r\n            <el-button v-if=\"index === 0\" circle :icon=\"Plus\" type=\"primary\" plain @click=\"add()\" />\r\n          </el-col>\r\n        </el-row>\r\n        <el-row style=\"align-items: center; height: 40px; margin-right: -15px; margin-left: -15px; background-color: rgb(238 235 235)\">\r\n          <el-col :span=\"12\" style=\"padding-left: 40px; text-align: left\">\r\n            <span style=\"margin-right: 10px\">{{ t('alreadySplit', { splited }) }}</span>\r\n            <span>{{ t('notSplit', { noStriped }) }}</span>\r\n          </el-col>\r\n          <el-col :span=\"12\" style=\"padding-right: 40px; text-align: right\">\r\n            {{ t('totalAmount', { fee: account.fee }) }}\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <template #footer>\r\n        <el-button @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"onSubmit\">\r\n          {{ t('confirm') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "form", "ref", "gcode", "hcode", "accNo", "account", "accountList", "t", "useI18n", "formRules", "no", "required", "message", "trigger", "remark", "onMounted", "async", "_fee", "fee", "value", "push", "formRef", "myVisible", "computed", "get", "modelValue", "set", "val", "onCancel", "onSubmit", "validate", "valid", "accountApi", "splitAccount", "then", "res", "code", "ElMessage", "success", "type", "center", "error", "msg", "splited", "reduce", "prev", "cur", "noStriped", "item", "splice", "indexOf"], "mappings": "2zBAyDA,MAAMA,EAAQC,EAWRC,EAAQC,EAIRC,EAAYC,IACZC,EAAOC,EAAI,CACfC,MAAOJ,EAAUI,MACjBC,MAAOL,EAAUK,MACjBC,MAAOV,EAAMW,QAAQD,MACrBE,YAAa,MAMTC,EAAEA,GAAMC,IAERC,EAAYR,EAAe,CAC/BS,GAAI,CAAC,CAAEC,UAAU,EAAMC,QAASL,EAAE,gBAAiBM,QAAS,SAC5DC,OAAQ,CAAC,CAAEH,UAAU,EAAMC,QAASL,EAAE,eAAgBM,QAAS,WAGjEE,GAAUC,UACF,MAAAC,EAAOvB,EAAMW,QAAQa,IAAM,EAC7BD,EAAO,IACJjB,EAAAmB,MAAMb,YAAYc,KAAK,CAAEF,IAAKD,EAAMH,OAAQ,KAC5Cd,EAAAmB,MAAMb,YAAYc,KAAK,CAAEF,IAAKD,EAAMH,OAAQ,KAAI,IAGzD,MAAMO,EAAUpB,IACVqB,EAAYC,EAAS,CACzBC,IAAM,IACG9B,EAAM+B,WAEf,GAAAC,CAAIC,GACF/B,EAAM,oBAAqB+B,EAAG,IAWlC,SAASC,IACPN,EAAUH,OAAQ,CAAA,CAGpB,SAASU,IACPR,EAAQF,OACNE,EAAQF,MAAMW,UAAUC,IAClBA,GACFC,EAAWC,aAAajC,EAAKmB,OAAOe,MAAMC,IACvB,IAAbA,EAAIC,MACNC,EAAUC,QAAQ,CAChB1B,QAASL,EAAE,gBACXgC,KAAM,UACNC,QAAQ,IAEDZ,IACThC,EAAM,YAEIyC,EAAAI,MAAMN,EAAIO,IAAG,GAE1B,GAEJ,CAEC,MAAAC,EAAUpB,GAAS,IAChBvB,EAAKmB,MAAMb,YAAYsC,QAAO,CAACC,EAAMC,IACnCD,EAAOC,EAAI5B,KACjB,KAGC6B,EAAYxB,GAAS,IAClB7B,EAAMW,QAAQa,IAAMyB,EAAQxB,gwCAlCrC,SAAgB6B,GACThD,EAAAmB,MAAMb,YAAY2C,OAAOjD,EAAKmB,MAAMb,YAAY4C,QAAQF,GAAO,EAAC,qIAJhEhD,EAAAmB,MAAMb,YAAYc,KAAK,CAAEF,IAAK,EAAGJ,OAAQ"}