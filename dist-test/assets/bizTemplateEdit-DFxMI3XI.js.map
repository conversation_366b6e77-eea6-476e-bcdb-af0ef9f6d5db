{"version": 3, "file": "bizTemplateEdit-DFxMI3XI.js", "sources": ["../../src/views/marketing/sms/template/components/DetailForm/bizTemplateEdit.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"editSuccess\": \"Edit Successful\",\r\n    \"addSuccess\": \"Add Successful\",\r\n    \"cancel\": \"Cancel\",\r\n    \"submit\": \"Submit\",\r\n    \"editTemplate\": \"Edit Template\",\r\n    \"addTemplate\": \"Add Template\",\r\n    \"templateName\": \"Template Name\",\r\n    \"enterTemplateName\": \"Please enter the template name\",\r\n    \"creator\": \"Creator\",\r\n    \"templateContent\": \"Template Content\",\r\n    \"enterTemplateContent\": \"Please enter the template content\",\r\n    \"isGroupOwned\": \"Is Group Owned\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"associatedStore\": \"Associated Store\",\r\n    \"selectStore\": \"Please select a store\",\r\n    \"sendScene\": \"Sending Scene\",\r\n    \"sendScenePlaceholder\": \"Please enter the sending scene\",\r\n    \"applicationChannel\": \"Application Channel\",\r\n    \"applicationChannelPlaceholder\": \"Please enter the application channel\",\r\n    \"operationFailed\": \"Operation Failed\",\r\n    \"store\": \"Store\",\r\n    \"wechat\": \"WeChat\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"editSuccess\": \"编辑成功\",\r\n    \"addSuccess\": \"新增成功\",\r\n    \"cancel\": \"取消\",\r\n    \"submit\": \"提交\",\r\n    \"editTemplate\": \"编辑模板\",\r\n    \"addTemplate\": \"新增模板\",\r\n    \"templateName\": \"模板名称\",\r\n    \"enterTemplateName\": \"请输入模板名称\",\r\n    \"creator\": \"创建人\",\r\n    \"templateContent\": \"模板内容\",\r\n    \"enterTemplateContent\": \"请输入模板内容\",\r\n    \"isGroupOwned\": \"是否为集团所有\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"associatedStore\": \"所属门店\",\r\n    \"selectStore\": \"请选择门店\",\r\n    \"sendScene\": \"发送场景\",\r\n    \"sendScenePlaceholder\": \"请输入发送场景\",\r\n    \"applicationChannel\": \"应用渠道\",\r\n    \"applicationChannelPlaceholder\": \"请输入应用渠道\",\r\n    \"operationFailed\": \"操作失败\",\r\n    \"store\": \"门店\",\r\n    \"wechat\": \"微信\"\r\n  },\r\n  \"km\": {\r\n    \"editSuccess\": \"កែសម្រួលជោគជ័យ\",\r\n    \"addSuccess\": \"បន្ថែមជោគជ័យ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"submit\": \"ដាក់ស្នើ\",\r\n    \"editTemplate\": \"កែសម្រួលគំរូ\",\r\n    \"addTemplate\": \"បន្ថែមគំរូ\",\r\n    \"templateName\": \"ឈ្មោះគំរូ\",\r\n    \"enterTemplateName\": \"សូមបញ្ចូលឈ្មោះគំរូ\",\r\n    \"creator\": \"អ្នកបង្កើត\",\r\n    \"templateContent\": \"មាតិកាគំរូ\",\r\n    \"enterTemplateContent\": \"សូមបញ្ចូលមាតិកាគំរូ\",\r\n    \"isGroupOwned\": \"ជាកម្មសិទ្ធិរបស់ក្រុមហ៊ុន\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"associatedStore\": \"ហាងដែលភ្ជាប់\",\r\n    \"selectStore\": \"សូមជ្រើសរើសហាង\",\r\n    \"sendScene\": \"ឈុតផ្ញើ\",\r\n    \"sendScenePlaceholder\": \"សូមបញ្ចូលឈុតផ្ញើ\",\r\n    \"applicationChannel\": \"ឆានែលកម្មវិធី\",\r\n    \"applicationChannelPlaceholder\": \"សូមបញ្ចូលឆានែលកម្មវិធី\",\r\n    \"operationFailed\": \"ប្រតិបត្តិការបរាជ័យ\",\r\n    \"store\": \"ហាង\",\r\n    \"wechat\": \"វីឆាត\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\n\r\nimport type { DetailFormProps } from '../../types'\r\nimport moban from '@/api/modules/moban.api'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { transferProperty } from '@/utils/index'\r\n\r\nconst props = withDefaults(\r\n  defineProps<\r\n    {\r\n      modelValue?: boolean\r\n      info?: any\r\n    } & DetailFormProps\r\n  >(),\r\n  {\r\n    modelValue: false,\r\n    signCode: '',\r\n    merchants: [],\r\n    info: {},\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  id: '',\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: '',\r\n  /** 创建人 */\r\n  creator: '',\r\n  /** 是否为集团所有，如果属于集团则只需要传集团编码 0不是，1是 */\r\n  isGroup: true,\r\n  /** 发送场景 URL */\r\n  sendScene: '1',\r\n  /** 模板名称 */\r\n  tplName: '',\r\n  /** 签名内容 */\r\n  tplContent: '{name}你已成功{gcodename}会员,会员名称{memberName}',\r\n  /** 应用渠道 */\r\n  useChannel: 'xc',\r\n})\r\n\r\n// {\r\n//   \"id\": 12,\r\n//   \"cellNumber\": \"13875935213\",\r\n//   \"description\": \"申请签名\",\r\n//   \"gcode\": \"1781282000378556416\",\r\n//   \"hcode\": \"1781282590420660224\",\r\n//   \"isGroup\": 0,\r\n//   \"licenseUrl\": \"http://sms.yiduohua.net/abc.jpg\",\r\n//   \"proveType\": 1,\r\n//   \"sign\": \"一朵花\",\r\n//   \"website\": \"pms.yiduohua.net\"\r\n// }\r\n\r\nconst formRules = ref<FormRules>({\r\n  sign: [{ required: true, message: t('enterTemplateName'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  transferProperty(form.value, props.info)\r\n})\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nconst btnLoading = ref(false)\r\nasync function onSubmit() {\r\n  btnLoading.value = true\r\n  try {\r\n    let data = null as any\r\n    if (props.signCode !== '') {\r\n      data = await moban.update(form.value)\r\n    } else {\r\n      data = await moban.add(form.value)\r\n    }\r\n    if (data.code === 0) {\r\n      ElMessage.success({\r\n        message: props.signCode !== '' ? t('editSuccess') : t('addSuccess'),\r\n        center: true,\r\n      })\r\n      emits('success')\r\n      onCancel()\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error({\r\n      message: error.message || t('operationFailed'),\r\n      center: true,\r\n    })\r\n  } finally {\r\n    btnLoading.value = false\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-dialog v-model=\"myVisible\" :title=\"props.signCode !== '' ? t('editTemplate') : t('addTemplate')\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"180px\" label-suffix=\"：\">\r\n        <el-form-item :label=\"t('templateName')\">\r\n          <el-input v-model=\"form.tplName\" :placeholder=\"t('enterTemplateName')\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('creator')\">\r\n          <el-input v-model=\"form.creator\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('templateContent')\">\r\n          <el-input v-model=\"form.tplContent\" disabled type=\"textarea\" rows=\"4\" />\r\n        </el-form-item>\r\n        <!--\r\n        <el-form-item :label=\"t('applicationDescription')\">\r\n          <el-input v-model=\"form.description\"></el-input>\r\n        </el-form-item>\r\n        {\r\n          \"creator\": \"admin\",\r\n          \"gcode\": \"1781282000378556416\",\r\n          \"hcode\": \"1781282590420660224\",\r\n          \"isGroup\": 0,\r\n          \"sendScene\": \"1\",\r\n          \"tplContent\": \"{name}你已成功{gcodename}会员,会员名称{memberName}\",\r\n          \"tplName\": \"新建模板1\",\r\n          \"useChannel\": 100000001\r\n        }\r\n        -->\r\n        <el-form-item :label=\"t('isGroupOwned')\">\r\n          <el-switch v-model=\"form.isGroup\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" inline-prompt />\r\n        </el-form-item>\r\n        <el-form-item v-if=\"!form.isGroup\" :label=\"t('associatedStore')\">\r\n          <el-select v-model=\"form.hcode\" :placeholder=\"t('selectStore')\">\r\n            <el-option v-for=\"item in props.merchants\" :key=\"item.hcode\" :label=\"item.hname\" :value=\"item.hcode\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('sendScene')\">\r\n          <el-input v-model=\"form.sendScene\" :placeholder=\"t('sendScenePlaceholder')\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('applicationChannel')\">\r\n          <el-input v-model=\"form.useChannel\" :placeholder=\"t('applicationChannelPlaceholder')\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <el-button :loading=\"btnLoading\" size=\"large\" @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button :loading=\"btnLoading\" type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          {{ t('submit') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "id", "gcode", "hcode", "creator", "isGroup", "sendScene", "tplName", "tplContent", "useChannel", "formRules", "sign", "required", "message", "trigger", "onMounted", "transferProperty", "value", "info", "myVisible", "computed", "get", "modelValue", "set", "val", "onCancel", "btnLoading", "async", "onSubmit", "data", "signCode", "moban", "update", "add", "code", "ElMessage", "success", "center", "error"], "mappings": "07BAwFA,MAAMA,EAAQC,EAeRC,EAAQC,GAKRC,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,GAAI,GAEJC,MAAOP,EAAUO,MAEjBC,MAAO,GAEPC,QAAS,GAETC,SAAS,EAETC,UAAW,IAEXC,QAAS,GAETC,WAAY,2CAEZC,WAAY,OAgBRC,EAAYZ,EAAe,CAC/Ba,KAAM,CAAC,CAAEC,UAAU,EAAMC,QAASpB,EAAE,qBAAsBqB,QAAS,WAGrEC,GAAU,KACSC,EAAAhB,EAAKiB,MAAO5B,EAAM6B,KAAI,IAGzC,MAAMC,EAAYC,EAAS,CACzBC,IAAM,IACGhC,EAAMiC,WAEf,GAAAC,CAAIC,GACFjC,EAAM,oBAAqBiC,EAAG,IAIlC,SAASC,IACPN,EAAUF,OAAQ,CAAA,CAGd,MAAAS,EAAa5B,GAAI,GACvB6B,eAAeC,IACbF,EAAWT,OAAQ,EACf,IACF,IAAIY,EAAO,KAETA,EADqB,KAAnBxC,EAAMyC,eACKC,EAAMC,OAAOhC,EAAKiB,aAElBc,EAAME,IAAIjC,EAAKiB,OAEZ,IAAdY,EAAKK,OACPC,EAAUC,QAAQ,CAChBvB,QAA4B,KAAnBxB,EAAMyC,SAAkBrC,EAAE,eAAiBA,EAAE,cACtD4C,QAAQ,IAEV9C,EAAM,WACGkC,WAEJa,GACPH,EAAUG,MAAM,CACdzB,QAASyB,EAAMzB,SAAWpB,EAAE,mBAC5B4C,QAAQ,GACT,CACD,QACAX,EAAWT,OAAQ,CAAA,CACrB"}