{"version": 3, "file": "customerlevel-DVnyH-xD.js", "sources": ["../../src/views/group/system/config/components/customerlevel.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"agentLevel\": \"Agent Level\",\r\n    \"protocolLevel\": \"Protocol Level\",\r\n    \"brokerageLevel\": \"Brokerage Level\",\r\n    \"remark\": \"Remark\",\r\n    \"save\": \"Save\",\r\n    \"cancel\": \"Cancel\",\r\n    \"edit\": \"Edit\",\r\n    \"successMessage\": \"Edit successful\",\r\n    \"errorMessage\": \"Error: {message}\",\r\n    \"operation\": \"operation\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"agentLevel\": \"中介等级\",\r\n    \"protocolLevel\": \"协议单位等级\",\r\n    \"brokerageLevel\": \"佣金等级\",\r\n    \"remark\": \"备注\",\r\n    \"save\": \"保存\",\r\n    \"cancel\": \"取消\",\r\n    \"edit\": \"编辑\",\r\n    \"successMessage\": \"编辑成功\",\r\n    \"errorMessage\": \"错误: {message}\",\r\n    \"operation\": \"操作\"\r\n  },\r\n  \"km\": {\r\n    \"agentLevel\": \"កម្រិតភ្នាក់ងារ\",\r\n    \"protocolLevel\": \"កម្រិតអង្គភាពព្រមព្រៀង\",\r\n    \"brokerageLevel\": \"កម្រិតគណនី\",\r\n    \"remark\": \"កំណត់សម្គាល់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"edit\": \"កែសម្រួល\",\r\n    \"successMessage\": \"កែសម្រួលបានជោគជ័យ\",\r\n    \"errorMessage\": \"កំហុស: {message}\",\r\n    \"operation\": \"ប្រតិបត្តិការ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { GeneralConfigModel } from '@/models/index'\r\nimport { generalConfigApi } from '@/api/modules/index'\r\nimport { BooleanEnum, GeneralConfigTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst loading = ref(false)\r\nconst form = ref({\r\n  id: 0,\r\n  gcode: userStore.gcode,\r\n  code: '',\r\n  name: '',\r\n  value: '',\r\n  type: GeneralConfigTypeEnum.AGENT_LEVEL,\r\n  remark: '',\r\n  isG: BooleanEnum.YES,\r\n})\r\n\r\nconst dataList = ref<GeneralConfigModel[]>([])\r\n\r\nconst type = ref(GeneralConfigTypeEnum.AGENT_LEVEL.toString())\r\n\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  generalConfigApi.list({ gcode: userStore.gcode, isG: BooleanEnum.YES, type: type.value }).then((res: any) => {\r\n    loading.value = false\r\n    dataList.value = res.data\r\n  })\r\n}\r\n\r\nfunction onEdit(item: any) {\r\n  form.value = item\r\n  return new Promise<void>((resolve) => {\r\n    generalConfigApi.updateGeneralConfig(form.value).then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success({\r\n          message: t('successMessage'),\r\n          center: true,\r\n        })\r\n        item.isEdit = false\r\n      } else {\r\n        ElMessage.error({\r\n          message: t('errorMessage', { message: res.msg }),\r\n          center: true,\r\n        })\r\n      }\r\n      resolve()\r\n    })\r\n  })\r\n}\r\n\r\nfunction menuClick(typeCode: string) {\r\n  type.value = typeCode\r\n  getInfo()\r\n}\r\n\r\nconst menuMap = new Map<string, { index: string; menuName: string }>([\r\n  [GeneralConfigTypeEnum.AGENT_LEVEL, { index: '1', menuName: t('agentLevel') }],\r\n  [GeneralConfigTypeEnum.PROTOCOL_LEVEL, { index: '2', menuName: t('protocolLevel') }],\r\n  [GeneralConfigTypeEnum.BROKERAGE_LEVEL, { index: '3', menuName: t('brokerageLevel') }],\r\n])\r\n</script>\r\n\r\n<template>\r\n  <el-container>\r\n    <el-aside style=\"width: 200px\">\r\n      <el-container>\r\n        <el-main class=\"nopadding\">\r\n          <el-menu default-active=\"1\" class=\"el-menu-vertical-demo\">\r\n            <el-menu-item index=\"1\" @click=\"menuClick(GeneralConfigTypeEnum.AGENT_LEVEL.toString())\">\r\n              <span>{{ t('agentLevel') }}</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"2\" @click=\"menuClick(GeneralConfigTypeEnum.PROTOCOL_LEVEL.toString())\">\r\n              <span>{{ t('protocolLevel') }}</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"3\" @click=\"menuClick(GeneralConfigTypeEnum.BROKERAGE_LEVEL.toString())\">\r\n              <span>{{ t('brokerageLevel') }}</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </el-main>\r\n      </el-container>\r\n    </el-aside>\r\n    <el-main>\r\n      <div>\r\n        <el-table v-loading=\"loading\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" class=\"list-table\" :data=\"dataList\" height=\"100%\">\r\n          <el-table-column :label=\"menuMap.get(type)?.menuName\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.name\" maxlength=\"10\" />\r\n              <span v-else>{{ scope.row.name }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('remark')\" align=\"left\">\r\n            <template #default=\"scope\">\r\n              <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.remark\" maxlength=\"50\" />\r\n              <span v-else>{{ scope.row.remark }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('operation')\" align=\"left\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <template v-if=\"scope.row.isEdit\">\r\n                <el-button type=\"primary\" plain @click=\"onEdit(scope.row)\">\r\n                  {{ t('save') }}\r\n                </el-button>\r\n                <el-button plain @click=\"scope.row.isEdit = false\">\r\n                  {{ t('cancel') }}\r\n                </el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"primary\" plain @click=\"scope.row.isEdit = true\">\r\n                  {{ t('edit') }}\r\n                </el-button>\r\n              </template>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-main>\r\n  </el-container>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-main.nopadding {\r\n  padding: 0;\r\n  background: #fff;\r\n}\r\n\r\n:deep(.el-menu-item) {\r\n  &.is-active {\r\n    background-color: var(--el-color-primary-light-9);\r\n  }\r\n  &:hover {\r\n    background-color: var(--el-color-primary-light-9);\r\n    color: var(--el-menu-active-color);\r\n  }\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "form", "id", "gcode", "code", "name", "value", "type", "GeneralConfigTypeEnum", "AGENT_LEVEL", "remark", "isG", "BooleanEnum", "YES", "dataList", "toString", "getInfo", "generalConfigApi", "list", "then", "res", "data", "menuClick", "typeCode", "onMounted", "menuMap", "Map", "index", "menuName", "PROTOCOL_LEVEL", "BROKERAGE_LEVEL", "item", "Promise", "resolve", "updateGeneralConfig", "ElMessage", "success", "message", "center", "isEdit", "error", "msg"], "mappings": "u3BA+CA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IAERC,EAAUC,GAAI,GACdC,EAAOD,EAAI,CACfE,GAAI,EACJC,MAAOR,EAAUQ,MACjBC,KAAM,GACNC,KAAM,GACNC,MAAO,GACPC,KAAMC,EAAsBC,YAC5BC,OAAQ,GACRC,IAAKC,EAAYC,MAGbC,EAAWd,EAA0B,IAErCO,EAAOP,EAAIQ,EAAsBC,YAAYM,YAMnD,SAASC,IACPjB,EAAQO,OAAQ,EAChBW,EAAiBC,KAAK,CAAEf,MAAOR,EAAUQ,MAAOQ,IAAKC,EAAYC,IAAKN,KAAMA,EAAKD,QAASa,MAAMC,IAC9FrB,EAAQO,OAAQ,EAChBQ,EAASR,MAAQc,EAAIC,IAAA,GACtB,CAwBH,SAASC,EAAUC,GACjBhB,EAAKD,MAAQiB,EACLP,GAAA,CAnCVQ,GAAU,KACAR,GAAA,IAqCJ,MAAAS,MAAcC,IAAiD,CACnE,CAAClB,EAAsBC,YAAa,CAAEkB,MAAO,IAAKC,SAAU/B,EAAE,gBAC9D,CAACW,EAAsBqB,eAAgB,CAAEF,MAAO,IAAKC,SAAU/B,EAAE,mBACjE,CAACW,EAAsBsB,gBAAiB,CAAEH,MAAO,IAAKC,SAAU/B,EAAE,6mDA7BpDkC,QACd9B,EAAKK,MAAQyB,EACN,IAAIC,SAAeC,IACxBhB,EAAiBiB,oBAAoBjC,EAAKK,OAAOa,MAAMC,IACpC,IAAbA,EAAIhB,MACN+B,EAAUC,QAAQ,CAChBC,QAASxC,EAAE,kBACXyC,QAAQ,IAEVP,EAAKQ,QAAS,GAEdJ,EAAUK,MAAM,CACdH,QAASxC,EAAE,eAAgB,CAAEwC,QAASjB,EAAIqB,MAC1CH,QAAQ,IAGJL,GAAA,GACT,IAjBL,IAAgBF"}