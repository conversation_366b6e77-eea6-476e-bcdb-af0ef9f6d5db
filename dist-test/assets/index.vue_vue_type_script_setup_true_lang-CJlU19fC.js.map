{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-CJlU19fC.js", "sources": ["../../src/components/Auth/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\ndefineOptions({\r\n  name: 'Auth',\r\n})\r\n\r\nconst props = defineProps<{\r\n  value: string | string[]\r\n  all?: boolean\r\n}>()\r\n\r\nconst auth = useAuth()\r\n\r\nfunction check() {\r\n  return props.all\r\n    ? auth.authAll(typeof props.value === 'string' ? [props.value] : props.value)\r\n    : auth.auth(props.value)\r\n}\r\n</script>\r\n\r\n<template>\r\n  <slot v-if=\"check()\" />\r\n  <slot v-else name=\"no-auth\" />\r\n</template>\r\n"], "names": ["props", "__props", "auth", "useAuth", "all", "authAll", "value"], "mappings": "2IAKA,MAAMA,EAAQC,EAKRC,EAAOC,kBAGJH,EAAMI,IACTF,EAAKG,QAA+B,iBAAhBL,EAAMM,MAAqB,CAACN,EAAMM,OAASN,EAAMM,OACrEJ,EAAKA,KAAKF,EAAMM"}