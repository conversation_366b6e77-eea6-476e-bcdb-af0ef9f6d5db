{"version": 3, "file": "arSet.api-BZHDDSla.js", "sources": ["../../src/api/modules/pms/arset/arSet.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/ar-set'\r\n/**\r\n * @description: 应收账(账套)\r\n */\r\nexport default {\r\n  /**\r\n   * 收款操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  collection: (data: any) => api.post(`${BASE_PATH}/collection`, data),\r\n\r\n  /**\r\n   * 核销操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  verify: (data: any) => api.post(`${BASE_PATH}/verify`, data),\r\n\r\n  /**\r\n   * 撤销操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  revocation: (data: any) => api.post(`${BASE_PATH}/revocation`, data),\r\n\r\n  /**\r\n   * 获得可用的应收账账套列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getArSetList: (data: { gcode: string; hcode?: string }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获取账套分页列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getArSetPage: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    keyword?: string\r\n    isEnable?: string // 状态\r\n    creditAccType?: string\r\n    pageNo: number\r\n    pageSize: number\r\n  }) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获取应收账(账套)详情\r\n   * @returns\r\n   * @param gcode\r\n   * @param arSetCode\r\n   */\r\n  getArSet: (gcode: string, arSetCode: string) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: { gcode, arSetCode },\r\n    }),\r\n\r\n  /**\r\n   * 创建应收账(账套)\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createArSet: (data: any) => api.post(`${BASE_PATH}/create`, data),\r\n\r\n  /**\r\n   * 修改应收账(账套)\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateArSet: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n\r\n  /**\r\n   * 修改应收账(账套)\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateArSetStatus: (data: { id: number; isEnable: string }) => api.put(`${BASE_PATH}/update-status`, data),\r\n\r\n  /**\r\n   * 获取应收帐(账套)下的账务\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getArSetAccSetAccountPage: (data: any) => api.get('/admin-api/pms/ar-set/get-account', { params: data }),\r\n  /**\r\n   * 获得应收账套关联门店列表\r\n   * @param arSetCode 应收账代码\r\n   * @returns\r\n   */\r\n  getArSetMerchantListByArSetCode: (arSetCode: string) =>\r\n    api.get(`${BASE_PATH}/ar-set-merchant/list-by-ar-set-code`, {\r\n      params: { arSetCode },\r\n    }),\r\n\r\n  /**\r\n   * 核销账务列表\r\n   * @param data\r\n   */\r\n  getVerifyRoomAccountList: (data: any) => api.get('/admin-api/pms/ar-set/verify-account', { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "arSetApi", "collection", "data", "api", "post", "verify", "revocation", "getArSetList", "get", "params", "getArSetPage", "getArSet", "gcode", "arSetCode", "createArSet", "updateArSet", "put", "updateArSetStatus", "getArSetAccSetAccountPage", "getArSetMerchantListByArSetCode", "getVerifyRoomAccountList"], "mappings": "wCAEA,MAAMA,EAAY,uBAIHC,EAAA,CAMbC,WAAaC,GAAcC,EAAIC,KAAK,GAAGL,eAAwBG,GAO/DG,OAASH,GAAcC,EAAIC,KAAK,GAAGL,WAAoBG,GAOvDI,WAAaJ,GAAcC,EAAIC,KAAK,GAAGL,eAAwBG,GAO/DK,aAAeL,GACbC,EAAIK,IAAI,GAAGT,SAAkB,CAC3BU,OAAQP,IAQZQ,aAAeR,GASbC,EAAIK,IAAI,GAAGT,SAAkB,CAC3BU,OAAQP,IASZS,SAAU,CAACC,EAAeC,IACxBV,EAAIK,IAAI,GAAGT,QAAiB,CAC1BU,OAAQ,CAAEG,QAAOC,eAQrBC,YAAcZ,GAAcC,EAAIC,KAAK,GAAGL,WAAoBG,GAO5Da,YAAcb,GAAcC,EAAIa,IAAI,GAAGjB,WAAoBG,GAO3De,kBAAoBf,GAA2CC,EAAIa,IAAI,GAAGjB,kBAA2BG,GAOrGgB,0BAA4BhB,GAAcC,EAAIK,IAAI,oCAAqC,CAAEC,OAAQP,IAMjGiB,gCAAkCN,GAChCV,EAAIK,IAAI,GAAGT,wCAAiD,CAC1DU,OAAQ,CAAEI,eAOdO,yBAA2BlB,GAAcC,EAAIK,IAAI,uCAAwC,CAAEC,OAAQP"}