import{d as t,aj as e,ai as s,b as o,y as a,ae as i,r as n,o as r,c as l,g as c,f as d,w as m,u,c8 as b,h as p,Y as f,c9 as v,ca as g,cb as h,F as C,ag as k,R as w,cc as y,cd as _,e as I,ce as R,av as T,aR as N,bL as G,bE as S,bz as L,q as P,b0 as j,bF as A,t as H,v as E,c1 as D}from"./index-CkEhI1Zk.js";/* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css               *//* empty css                 *//* empty css                *//* empty css                  */import{d as M}from"./device.api-BsgckoMw.js";import{o as O}from"./order.api-B-JCVvq6.js";import{s as U}from"./serverTime.api-D89oCqKL.js";import{r as x}from"./roomCardLog.api-pw0J1hl7.js";import{O as F,l as z,B as V}from"./constants-Cg3j_uH4.js";import{C as Y}from"./CardReader-BeR26SIt.js";import{g as q}from"./roomCardUtil-DBQw7z7m.js";import K from"./mergeForm-C0XQeWLX.js";import{_ as B}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                   */import"./print.api-DEqPqKcI.js";import"./printForm.api-BOg76CjP.js";const W={class:"team-reception-container"},J={class:"card-header"},Q={class:"header-title"},X={class:"team-basic-info"},Z={class:"info-row"},$={class:"info-item"},tt={class:"info-label"},et={class:"info-value"},st={class:"info-item"},ot={class:"info-label"},at={class:"info-value"},it={class:"info-item"},nt={class:"info-label"},rt={class:"info-value"},lt={class:"stats-section"},ct={class:"section-title"},dt={class:"stats-cards"},mt={class:"stat-card"},ut={class:"stat-value"},bt={class:"stat-label"},pt={class:"stat-card"},ft={class:"stat-value"},vt={class:"stat-label"},gt={class:"stat-card"},ht={class:"stat-value"},Ct={class:"stat-label"},kt={class:"stat-card"},wt={class:"stat-value"},yt={class:"stat-label"},_t={class:"stat-card"},It={class:"stat-value"},Rt={class:"stat-label"},Tt={class:"stat-card"},Nt={class:"stat-value"},Gt={class:"stat-label"},St={class:"stat-card"},Lt={class:"stat-value"},Pt={class:"stat-label"},jt={class:"room-type-section"},At={class:"section-title"},Ht={class:"room-type-distribution"},Et={class:"room-type-count"},Dt={key:0,class:"remark-section"},Mt={class:"section-title"},Ot={class:"remark-container"},Ut={class:"remark-content"},xt={class:"card-header"},Ft={class:"header-title"},zt={class:"guest-info-container"},Vt={class:"guest-info-header",style:{"margin-bottom":"5px"}},Yt={class:"guest-info-title"},qt={class:"guest-name"},Kt={class:"room-info"},Bt={class:"room-number"},Wt={class:"room-type",style:{"margin-left":"5px"}},Jt={class:"time-info-inline"},Qt={class:"time-item"},Xt={class:"time-item"},Zt={class:"operation-buttons"},$t=t({__name:"teamReception",props:{teamCode:{default:""},bindCode:{default:""},isEntryAccount:{default:"0"}},setup(t){const B=t,{t:$t}=e(),te=s(),ee=o({gcode:"",hcode:"",teamCode:"",teamName:"",teamType:"",contact:"",planCheckinTime:null,roomCount:0,checkInRoomCount:0,personCount:0,creditPersonCount:0,creditRoomCount:0,settlePersonCount:0,settleRoomCount:0,remark:"",teamRooms:[],roomTypeCounts:[]}),se=o(""),oe=o([]),ae=o(!1);a((async()=>{await Promise.all([ne()]),async function(){const t=await U.serverTime(te.gcode,"0");se.value=t.data}(),void 0===window.CallBridge&&void 0===window.__RUNNING_IN_PMS_AGENT__||Y.closeSocket(),ee.value.teamRooms&&ee.value.teamRooms.length>0&&(oe.value=[ee.value.teamRooms[0].orderNo])}));const ie=o();async function ne(){const t=await O.getTeamMainOrder({gcode:te.gcode,hcode:te.hcode,teamCode:B.teamCode});ee.value=t.data}function re(t){const e=["primary","success","warning","danger","info"];return e[t%e.length]}function le(t){return t===F.CHECK_IN?"在住":t===F.CHECK_OUT?"退房":t===F.CREDIT?"挂账":"未知"}function ce(t){return t?T(t).format("MM-DD HH:mm"):"暂无"}i((()=>{void 0===window.CallBridge&&void 0===window.__RUNNING_IN_PMS_AGENT__||Y.closeSocket(),ie.value&&ie.value.length>0&&ie.value.removeEventListener("scroll",handleScroll)}));const de=n({gcode:te.gcode,hcode:te.hcode}),me=o();async function ue(t,e){var s,o;if(!t.rNo)return void N.warning($t("messages.pleaseAssignRoomFirst"));if(!function(){if(void 0===window.CallBridge&&void 0===window.__RUNNING_IN_PMS_AGENT__){return N({showClose:!0,message:$t("messages.clientRequired",{downloadLink:'<a href="https://pms-client-update-1303913307.cos.ap-guangzhou.myqcloud.com/Hotel-Agent-Windows.exe" target="_blank">下载Hotel-Agent客户端</a>'}),type:"warning",dangerouslyUseHTMLString:!0}),!1}return!0}())return;if(!t.lockVersion)return void N({showClose:!0,message:$t("messages.noLockConfigured"),type:"warning",dangerouslyUseHTMLString:!0});if(await function(t){const e={...de,...t&&{lockVersion:t}};return M.getHotelDoor(e).then((t=>{0===t.code&&t.data&&(me.value=t.data)}))}(t.lockVersion),!me.value)return void N({showClose:!0,message:$t("messages.noLockConfigured"),type:"warning",dangerouslyUseHTMLString:!0});Y.initCardReader((s=>{const o=JSON.parse(s);o&&!0===o.succeed&&o.method===z.WRITELOCKCARD&&(o.cardInfo&&(G({title:$t("notifications.cardInfo"),dangerouslyUseHTMLString:!0,message:q(o),position:"bottom-left",type:"success"}),x.createRoomCardLog({gcode:te.gcode,hcode:te.hcode,name:t.name,rNo:t.rNo,cardNo:o.cardInfo.cardNo,orderNo:t.orderNo,type:e?"0":"1",periodTime:T(1e3*Number(o.cardInfo.expire)).format("YYYY-MM-DD HH:mm:ss")}).then((t=>{0===t.code&&console.log("制卡日志记录成功")})).catch((t=>{console.error("制卡日志记录失败:",t)}))),N({message:$t("messages.cardCreatedSuccess",{roomNo:t.rNo}),type:"success"}))}));const a=await U.serverTime(te.gcode,"3").then((t=>0===t.code?t.data:""));if(!a)return void console.error("Failed to get system time");const i=Math.floor(Number.parseInt(a,10)/1e3),n={method:z.WRITELOCKCARD,lockVer:me.value.version,cardInfo:{roomNo:t.rNo,checkin:i,expire:Math.floor(T(t.planCheckoutTime).valueOf()/1e3),allowLockOut:(null==(s=me.value)?void 0:s.allowLockOut)===V.YES,replaceCard:e,checkTime:(null==(o=me.value)?void 0:o.checkTime)===V.YES,lockNo:t.lockNo,mac:t.mac,buildNo:t.buildNo,floorNo:t.floorNo}};me.value.conf&&me.value.conf.length>0&&me.value.conf.forEach((t=>{n.cardInfo[t.parameterCode]=t.parameterContent}));const r=JSON.stringify(n);console.log("jsonString",r);const l=setInterval((()=>{Y.isConnected&&(Y.handleLockCard(r),clearInterval(l))}),200)}return(t,e)=>{const s=S,o=L,a=P,i=j,n=A,T=H,N=E,G=D;return r(),l(C,null,[c("div",W,[d(i,{class:"overview-card",shadow:"hover"},{header:m((()=>[c("div",J,[c("div",Q,[d(s,null,{default:m((()=>[d(u(b))])),_:1}),e[2]||(e[2]=c("span",null,"团队信息",-1)),d(o,{type:"primary",effect:"plain",class:"ml-2"},{default:m((()=>{return[p(f((t=ee.value.teamType,$t("meeting_group"===t?"teamType.meetingGroup":"travel_group"===t?"teamType.travelGroup":"teamType.other"))),1)];var t})),_:1})]),c("div",null,[d(a,{type:"primary",onClick:e[0]||(e[0]=t=>ae.value=!0)},{default:m((()=>[p(f(u($t)("actions.printTeamRegistration")),1)])),_:1})])])])),default:m((()=>[c("div",X,[c("div",Z,[c("div",$,[c("span",tt,f(u($t)("teamInfo.teamName"))+"：",1),c("span",et,f(ee.value.teamName||u($t)("common.none")),1)]),c("div",st,[c("span",ot,f(u($t)("teamInfo.contact"))+"：",1),c("span",at,f(ee.value.contact||u($t)("common.none")),1)]),c("div",it,[c("span",nt,f(u($t)("teamInfo.plannedArrivalTime"))+"：",1),c("span",rt,[d(s,null,{default:m((()=>[d(u(v))])),_:1}),p(" "+f(ce(ee.value.planCheckinTime)),1)])])])]),c("div",lt,[c("div",ct,[d(s,null,{default:m((()=>[d(u(g))])),_:1}),c("span",null,f(u($t)("statistics.roomStatistics")),1)]),c("div",dt,[c("div",mt,[c("div",ut,f(ee.value.roomCount),1),c("div",bt,f(u($t)("statistics.totalRooms")),1)]),c("div",pt,[c("div",ft,f(ee.value.checkInRoomCount),1),c("div",vt,f(u($t)("statistics.checkedInRooms")),1)]),c("div",gt,[c("div",ht,f(ee.value.personCount),1),c("div",Ct,f(u($t)("statistics.checkedInPersons")),1)]),c("div",kt,[c("div",wt,f(ee.value.creditRoomCount),1),c("div",yt,f(u($t)("statistics.creditRooms")),1)]),c("div",_t,[c("div",It,f(ee.value.creditPersonCount),1),c("div",Rt,f(u($t)("statistics.creditPersons")),1)]),c("div",Tt,[c("div",Nt,f(ee.value.settleRoomCount),1),c("div",Gt,f(u($t)("statistics.settledRooms")),1)]),c("div",St,[c("div",Lt,f(ee.value.settlePersonCount),1),c("div",Pt,f(u($t)("statistics.settledPersons")),1)])])]),c("div",jt,[c("div",At,[d(s,null,{default:m((()=>[d(u(h))])),_:1}),c("span",null,f(u($t)("roomType.distribution")),1)]),c("div",Ht,[(r(!0),l(C,null,k(ee.value.roomTypeCounts,((t,e)=>(r(),l("div",{key:e,class:"room-type-item"},[d(o,{type:re(e),effect:"light"},{default:m((()=>[p(f(t.rtName),1)])),_:2},1032,["type"]),c("span",Et,f(t.count)+f(u($t)("roomType.roomUnit")),1)])))),128))])]),ee.value.remark?(r(),l("div",Dt,[c("div",Mt,[d(s,null,{default:m((()=>[d(u(g))])),_:1}),c("span",null,f(u($t)("remark.title")),1)]),c("div",Ot,[c("div",Ut,f(ee.value.remark),1)])])):w("",!0)])),_:1}),d(i,{class:"rooms-card",shadow:"hover"},{header:m((()=>[c("div",xt,[c("div",Ft,[d(s,null,{default:m((()=>[d(u(y))])),_:1}),c("span",null,f(u($t)("roomGuest.title")),1),d(o,{type:"info",effect:"plain",class:"room-count-tag"},{default:m((()=>[p(f(u($t)("roomGuest.totalRooms",{count:ee.value.teamRooms.length})),1)])),_:1})])])])),default:m((()=>[d(N,{data:ee.value.teamRooms,"default-expand-all":"",border:"",class:"room-table"},{default:m((()=>[d(T,{type:"expand"},{default:m((t=>[c("div",zt,[c("div",Vt,[c("div",Yt,[d(s,null,{default:m((()=>[d(u(_))])),_:1}),c("span",null,f(u($t)("guestInfo.title")),1),d(o,{size:"small",type:"info",effect:"plain"},{default:m((()=>{var e;return[p(f(u($t)("guestInfo.totalGuests",{count:(null==(e=t.row.orderTogethers)?void 0:e.length)||0})),1)]})),_:2},1024)])]),t.row.orderTogethers&&0!==t.row.orderTogethers.length?(r(),I(N,{key:1,data:t.row.orderTogethers,class:"guest-table",stripe:"",border:""},{default:m((()=>[d(T,{prop:"name",label:u($t)("guestInfo.name"),"min-width":"120"},{default:m((({row:t})=>[c("div",qt,[c("span",null,f(t.name),1),"1"===t.isMain?(r(),I(o,{key:0,size:"small",type:"success",effect:"plain"},{default:m((()=>[p(f(u($t)("guestInfo.mainGuest")),1)])),_:1})):w("",!0)])])),_:1},8,["label"]),d(T,{label:u($t)("guestInfo.gender"),width:"80",align:"center"},{default:m((({row:t})=>[d(o,{size:"small",type:"1"===t.sex?"primary":"danger",effect:"plain"},{default:m((()=>{return[p(f((e=t.sex,$t("1"===e?"gender.male":"0"===e?"gender.female":"gender.unknown"))),1)];var e})),_:2},1032,["type"])])),_:1},8,["label"]),d(T,{label:u($t)("guestInfo.status"),width:"100",align:"center"},{default:m((({row:t})=>[d(o,{size:"small",type:t.state===u(F).CHECK_IN?"success":t.state===u(F).CREDIT?"warning":"info"},{default:m((()=>[p(f(le(t.state)),1)])),_:2},1032,["type"])])),_:1},8,["label"]),d(T,{label:u($t)("common.actions"),align:"center",width:"285"},{default:m((()=>[d(a,{link:"",type:"primary",size:"small",onClick:e=>ue(t.row,!1)},{default:m((()=>[d(s,null,{default:m((()=>[d(u(h))])),_:1}),p(" "+f(u($t)("actions.createCohabitantCard")),1)])),_:2},1032,["onClick"])])),_:2},1032,["label"])])),_:2},1032,["data"])):(r(),I(n,{key:0,description:u($t)("guestInfo.noGuestInfo")},null,8,["description"]))])])),_:1}),d(T,{label:u($t)("roomInfo.title"),"min-width":"180"},{default:m((t=>{var e;return[c("div",Kt,[c("div",Bt,[d(o,{size:"large",effect:"dark",class:"room-tag"},{default:m((()=>[p(f(t.row.rNo),1)])),_:2},1024),c("span",Wt,f(t.row.rtName),1),d(G,{value:(null==(e=t.row.orderTogethers)?void 0:e.length)||0,type:"primary",style:{"margin-left":"10px"}},{default:m((()=>[d(s,null,{default:m((()=>[d(u(_))])),_:1})])),_:2},1032,["value"])])])]})),_:1},8,["label"]),d(T,{label:u($t)("timeInfo.arrivalDeparture"),width:"280",align:"center"},{default:m((t=>[c("div",Jt,[c("div",Qt,[d(s,null,{default:m((()=>[d(u(v))])),_:1}),c("span",null,f(u($t)("timeInfo.checkin"))+": "+f(ce(t.row.checkinTime)),1)]),e[3]||(e[3]=c("div",{class:"divider"},"|",-1)),c("div",Xt,[d(s,null,{default:m((()=>[d(u(R))])),_:1}),c("span",null,f(u($t)("timeInfo.departure"))+": "+f(ce(t.row.planCheckoutTime)),1)])])])),_:1},8,["label"]),d(T,{label:u($t)("common.status"),align:"center",width:"100"},{default:m((({row:t})=>[d(o,{type:t.state===u(F).CHECK_IN?"success":t.state===u(F).CREDIT?"warning":"info",effect:"dark"},{default:m((()=>[p(f(le(t.state)),1)])),_:2},1032,["type"])])),_:1},8,["label"]),d(T,{label:u($t)("common.actions"),align:"center",width:"300"},{default:m((t=>[c("div",Zt,[d(a,{type:"primary",size:"small",plain:"",onClick:e=>ue(t.row,!0)},{default:m((()=>[d(s,null,{default:m((()=>[d(u(h))])),_:1}),p(" "+f(u($t)("actions.createNewCard")),1)])),_:2},1032,["onClick"])])])),_:1},8,["label"])])),_:1},8,["data"])])),_:1})]),ae.value?(r(),I(K,{key:0,modelValue:ae.value,"onUpdate:modelValue":e[1]||(e[1]=t=>ae.value=t),"bind-code":B.bindCode},null,8,["modelValue","bind-code"])):w("",!0)],64)}}});function te(t){const e=t;e.__i18n=e.__i18n||[],e.__i18n.push({locale:"",resource:{en:{teamInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"Team Information"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"Team Name"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"Contact Person"}},plannedArrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"Planned Arrival Time"}}},teamType:{meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"Meeting Group"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"Travel Group"}},other:{t:0,b:{t:2,i:[{t:3}],s:"Other"}}},statistics:{roomStatistics:{t:0,b:{t:2,i:[{t:3}],s:"Room Statistics"}},totalRooms:{t:0,b:{t:2,i:[{t:3}],s:"Total Rooms"}},checkedInRooms:{t:0,b:{t:2,i:[{t:3}],s:"Checked-in Rooms"}},checkedInPersons:{t:0,b:{t:2,i:[{t:3}],s:"Checked-in Persons"}},creditRooms:{t:0,b:{t:2,i:[{t:3}],s:"Credit Rooms"}},creditPersons:{t:0,b:{t:2,i:[{t:3}],s:"Credit Persons"}},settledRooms:{t:0,b:{t:2,i:[{t:3}],s:"Settled Rooms"}},settledPersons:{t:0,b:{t:2,i:[{t:3}],s:"Settled Persons"}}},roomType:{distribution:{t:0,b:{t:2,i:[{t:3}],s:"Room Type Distribution"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:" rooms"}}},remark:{title:{t:0,b:{t:2,i:[{t:3}],s:"Remarks"}}},roomGuest:{title:{t:0,b:{t:2,i:[{t:3}],s:"Room Guest Information"}},totalRooms:{t:0,b:{t:2,i:[{t:3,v:"Total "},{t:4,k:"count"},{t:3,v:" rooms"}]}}},guestInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"Guest Information"}},totalGuests:{t:0,b:{t:2,i:[{t:3,v:"Total "},{t:4,k:"count"},{t:3,v:" guests"}]}},noGuestInfo:{t:0,b:{t:2,i:[{t:3}],s:"No guest information"}},name:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},gender:{t:0,b:{t:2,i:[{t:3}],s:"Gender"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"Main Guest"}}},roomInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"Room Information"}}},timeInfo:{arrivalDeparture:{t:0,b:{t:2,i:[{t:3}],s:"Arrival/Departure Time"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"Check-in"}},departure:{t:0,b:{t:2,i:[{t:3}],s:"Departure"}}},status:{checkedIn:{t:0,b:{t:2,i:[{t:3}],s:"Checked In"}},checkedOut:{t:0,b:{t:2,i:[{t:3}],s:"Checked Out"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"Credit"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"Unknown"}}},gender:{male:{t:0,b:{t:2,i:[{t:3}],s:"Male"}},female:{t:0,b:{t:2,i:[{t:3}],s:"Female"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"Unknown"}}},actions:{printTeamRegistration:{t:0,b:{t:2,i:[{t:3}],s:"Print Team Registration"}},createNewCard:{t:0,b:{t:2,i:[{t:3}],s:"Create New Card"}},createCohabitantCard:{t:0,b:{t:2,i:[{t:3}],s:"Create Cohabitant Card"}}},messages:{pleaseAssignRoomFirst:{t:0,b:{t:2,i:[{t:3}],s:"Please assign room before creating card"}},noLockConfigured:{t:0,b:{t:2,i:[{t:3}],s:"Hotel has no lock configured. Please go to 'Lock Configuration' to select the lock model used by the hotel. If there is no matching lock model, please contact the service provider."}},cardLogSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Card creation log recorded successfully"}},cardLogFailed:{t:0,b:{t:2,i:[{t:3}],s:"Card creation log recording failed:"}},cardCreatedSuccess:{t:0,b:{t:2,i:[{t:3,v:"Room "},{t:4,k:"roomNo"},{t:3,v:" card created successfully"}]}},downloadClient:{t:0,b:{t:2,i:[{t:3}],s:"Download Hotel-Agent Client"}},clientRequired:{t:0,b:{t:2,i:[{t:3,v:"Reading room cards can only be operated in Hotel-Agent, please "},{t:4,k:"downloadLink"},{t:3,v:"."}]}}},notifications:{cardInfo:{t:0,b:{t:2,i:[{t:3}],s:"Card Information"}}},common:{none:{t:0,b:{t:2,i:[{t:3}],s:"None"}},actions:{t:0,b:{t:2,i:[{t:3}],s:"Actions"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}}}},"zh-cn":{teamInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"团队信息"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"团队名称"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"联系人"}},plannedArrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"预抵时间"}}},teamType:{meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"会议团"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"旅行团"}},other:{t:0,b:{t:2,i:[{t:3}],s:"其他"}}},statistics:{roomStatistics:{t:0,b:{t:2,i:[{t:3}],s:"房间统计"}},totalRooms:{t:0,b:{t:2,i:[{t:3}],s:"总房间数"}},checkedInRooms:{t:0,b:{t:2,i:[{t:3}],s:"已入住房间"}},checkedInPersons:{t:0,b:{t:2,i:[{t:3}],s:"入住人数"}},creditRooms:{t:0,b:{t:2,i:[{t:3}],s:"挂账房间"}},creditPersons:{t:0,b:{t:2,i:[{t:3}],s:"挂账人数"}},settledRooms:{t:0,b:{t:2,i:[{t:3}],s:"已结账房间"}},settledPersons:{t:0,b:{t:2,i:[{t:3}],s:"已结账人数"}}},roomType:{distribution:{t:0,b:{t:2,i:[{t:3}],s:"房型分布"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:"间"}}},remark:{title:{t:0,b:{t:2,i:[{t:3}],s:"备注信息"}}},roomGuest:{title:{t:0,b:{t:2,i:[{t:3}],s:"房间住客信息"}},totalRooms:{t:0,b:{t:2,i:[{t:3,v:"共 "},{t:4,k:"count"},{t:3,v:" 间房"}]}}},guestInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"住客信息"}},totalGuests:{t:0,b:{t:2,i:[{t:3,v:"共 "},{t:4,k:"count"},{t:3,v:" 人"}]}},noGuestInfo:{t:0,b:{t:2,i:[{t:3}],s:"暂无住客信息"}},name:{t:0,b:{t:2,i:[{t:3}],s:"姓名"}},gender:{t:0,b:{t:2,i:[{t:3}],s:"性别"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"主客"}}},roomInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"房间信息"}}},timeInfo:{arrivalDeparture:{t:0,b:{t:2,i:[{t:3}],s:"抵离时间"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"入住"}},departure:{t:0,b:{t:2,i:[{t:3}],s:"离店"}}},status:{checkedIn:{t:0,b:{t:2,i:[{t:3}],s:"在住"}},checkedOut:{t:0,b:{t:2,i:[{t:3}],s:"退房"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"挂账"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"未知"}}},gender:{male:{t:0,b:{t:2,i:[{t:3}],s:"男"}},female:{t:0,b:{t:2,i:[{t:3}],s:"女"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"未知"}}},actions:{printTeamRegistration:{t:0,b:{t:2,i:[{t:3}],s:"打印团队登记单"}},createNewCard:{t:0,b:{t:2,i:[{t:3}],s:"制新卡"}},createCohabitantCard:{t:0,b:{t:2,i:[{t:3}],s:"制同住卡"}}},messages:{pleaseAssignRoomFirst:{t:0,b:{t:2,i:[{t:3}],s:"制卡前请先排房"}},noLockConfigured:{t:0,b:{t:2,i:[{t:3}],s:"酒店没有配置门锁，请到房锁配置里选择酒店所用门锁型号，如果没有匹配的门锁型号，请联系服务商。"}},cardLogSuccess:{t:0,b:{t:2,i:[{t:3}],s:"制卡日志记录成功"}},cardLogFailed:{t:0,b:{t:2,i:[{t:3}],s:"制卡日志记录失败:"}},cardCreatedSuccess:{t:0,b:{t:2,i:[{t:4,k:"roomNo"},{t:3,v:"房制卡成功"}]}},downloadClient:{t:0,b:{t:2,i:[{t:3}],s:"下载Hotel-Agent客户端"}},clientRequired:{t:0,b:{t:2,i:[{t:3,v:"读取房卡只能在Hotel-Agent中操作，请"},{t:4,k:"downloadLink"},{t:3,v:"。"}]}}},notifications:{cardInfo:{t:0,b:{t:2,i:[{t:3}],s:"房卡信息"}}},common:{none:{t:0,b:{t:2,i:[{t:3}],s:"暂无"}},actions:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}}}},km:{teamInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានក្រុម"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះក្រុម"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកទំនាក់ទំនង"}},plannedArrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាមកដល់ដែលបានគ្រោង"}}},teamType:{meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមប្រជុំ"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមទេសចរណ៍"}},other:{t:0,b:{t:2,i:[{t:3}],s:"ផ្សេងទៀត"}}},statistics:{roomStatistics:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថិតិបន្ទប់"}},totalRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់សរុប"}},checkedInRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានចូលស្នាក់"}},checkedInPersons:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនអ្នកចូលស្នាក់"}},creditRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់កាន់កាប់"}},creditPersons:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនអ្នកកាន់កាប់"}},settledRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានទូទាត់"}},settledPersons:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនអ្នកបានទូទាត់"}}},roomType:{distribution:{t:0,b:{t:2,i:[{t:3}],s:"ការចែកចាយប្រភេទបន្ទប់"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់"}}},remark:{title:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានកំណត់ចំណាំ"}}},roomGuest:{title:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានភ្ញៀវបន្ទប់"}},totalRooms:{t:0,b:{t:2,i:[{t:3,v:"សរុប "},{t:4,k:"count"},{t:3,v:" បន្ទប់"}]}}},guestInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានភ្ញៀវ"}},totalGuests:{t:0,b:{t:2,i:[{t:3,v:"សរុប "},{t:4,k:"count"},{t:3,v:" នាក់"}]}},noGuestInfo:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានព័ត៌មានភ្ញៀវ"}},name:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},gender:{t:0,b:{t:2,i:[{t:3}],s:"ភេទ"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវសំខាន់"}}},roomInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានបន្ទប់"}}},timeInfo:{arrivalDeparture:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាមកដល់/ចាកចេញ"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់"}},departure:{t:0,b:{t:2,i:[{t:3}],s:"ចាកចេញ"}}},status:{checkedIn:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងស្នាក់"}},checkedOut:{t:0,b:{t:2,i:[{t:3}],s:"ចេញ"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"កាន់កាប់"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"មិនស្គាល់"}}},gender:{male:{t:0,b:{t:2,i:[{t:3}],s:"ប្រុស"}},female:{t:0,b:{t:2,i:[{t:3}],s:"ស្រី"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"មិនស្គាល់"}}},actions:{printTeamRegistration:{t:0,b:{t:2,i:[{t:3}],s:"បោះពុម្ពបែបបទចុះឈ្មោះក្រុម"}},createNewCard:{t:0,b:{t:2,i:[{t:3}],s:"បង្កើតកាតថ្មី"}},createCohabitantCard:{t:0,b:{t:2,i:[{t:3}],s:"បង្កើតកាតអ្នកស្នាក់រួម"}}},messages:{pleaseAssignRoomFirst:{t:0,b:{t:2,i:[{t:3}],s:"សូមកំណត់បន្ទប់មុនពេលបង្កើតកាត"}},noLockConfigured:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារមិនបានកំណត់រចនាសម្ព័ន្ធសោ។ សូមទៅកាន់ 'ការកំណត់រចនាសម្ព័ន្ធសោ' ដើម្បីជ្រើសរើសម៉ូដែលសោដែលសណ្ឋាគារប្រើ។ ប្រសិនបើមិនមានម៉ូដែលសោដែលត្រូវគ្នា សូមទាក់ទងអ្នកផ្តល់សេវាកម្ម។"}},cardLogSuccess:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់ត្រាបង្កើតកាតបានជោគជ័យ"}},cardLogFailed:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់ត្រាបង្កើតកាតបរាជ័យ:"}},cardCreatedSuccess:{t:0,b:{t:2,i:[{t:3,v:"បន្ទប់ "},{t:4,k:"roomNo"},{t:3,v:" បង្កើតកាតបានជោគជ័យ"}]}},downloadClient:{t:0,b:{t:2,i:[{t:3}],s:"ទាញយក Hotel-Agent Client"}},clientRequired:{t:0,b:{t:2,i:[{t:3,v:"ការអានកាតអាចធ្វើបានតែនៅក្នុង Hotel-Agent ប៉ុណ្ណោះ សូម"},{t:4,k:"downloadLink"},{t:3,v:"។"}]}}},notifications:{cardInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានកាត"}}},common:{none:{t:0,b:{t:2,i:[{t:3}],s:"គ្មាន"}},actions:{t:0,b:{t:2,i:[{t:3}],s:"សកម្មភាព"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}}}}}})}te($t);const ee=B($t,[["__scopeId","data-v-756663de"]]);export{ee as default};
//# sourceMappingURL=teamReception-BVmeD-Jb.js.map
