{"version": 3, "file": "notification-sVKRf-hz.js", "sources": ["../../src/store/modules/notification.ts"], "sourcesContent": ["const useNotificationStore = defineStore(\r\n  // 唯一ID\r\n  'notification',\r\n  () => {\r\n    // 消息\r\n    const message = ref(0)\r\n    // 待办\r\n    const todo = ref(0)\r\n    // 总计\r\n    const total = computed(() => message.value + todo.value)\r\n\r\n    function init() {\r\n      getUnreadMessage()\r\n      getUnreadTodo()\r\n    }\r\n    // 获取未读消息数\r\n    function getUnreadMessage() {\r\n      // 为方便演示，这里直接写死的未读数\r\n      message.value = 9\r\n    }\r\n    // 获取未读待办数\r\n    function getUnreadTodo() {\r\n      // 为方便演示，这里直接写死的未读数\r\n      todo.value = 0\r\n    }\r\n\r\n    return {\r\n      message,\r\n      todo,\r\n      total,\r\n      init,\r\n    }\r\n  },\r\n)\r\n\r\nexport default useNotificationStore\r\n"], "names": ["useNotificationStore", "defineStore", "message", "ref", "todo", "total", "computed", "value", "init"], "mappings": "uDAAA,MAAMA,EAAuBC,EAE3B,gBACA,KAEQ,MAAAC,EAAUC,EAAI,GAEdC,EAAOD,EAAI,GAEXE,EAAQC,GAAS,IAAMJ,EAAQK,MAAQH,EAAKG,QAiB3C,MAAA,CACLL,UACAE,OACAC,QACAG,KAnBF,WAOEN,EAAQK,MAAQ,EAKhBH,EAAKG,MAAQ,CAVC,EAkBhB"}