import{d as t,aj as s,ai as e,b as a,y as i,o,c as n,g as r,f as l,w as c,u as m,c8 as d,Y as u,h as b,c9 as p,ca as v,cb as f,F as h,ag as k,R,e as y,i as C,av as T,bE as g,bz as I,q as _,b0 as P}from"./index-CkEhI1Zk.js";/* empty css                *//* empty css                  *//* empty css               */import{o as j}from"./order.api-B-JCVvq6.js";import w from"./mergeForm-C0XQeWLX.js";import{_ as G}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                   */import"./print.api-DEqPqKcI.js";import"./printForm.api-BOg76CjP.js";import"./constants-Cg3j_uH4.js";const N={class:"team-reception-container"},O={class:"card-header"},A={class:"header-title"},S={class:"team-basic-info"},M={class:"info-row"},U={class:"info-item"},x={class:"info-label"},F={class:"info-value"},H={class:"info-item"},D={class:"info-label"},E={class:"info-value"},V={class:"info-item"},z={class:"info-label"},q={class:"info-value"},B={class:"stats-section"},W={class:"section-title"},Y={class:"stats-cards"},J={class:"stat-card"},K={class:"stat-value"},L={class:"stat-label"},Q={class:"stat-card"},X={class:"stat-value"},Z={class:"stat-label"},$={class:"stat-card"},tt={class:"stat-value"},st={class:"stat-label"},et={class:"stat-card"},at={class:"stat-value"},it={class:"stat-label"},ot={class:"stat-card"},nt={class:"stat-value"},rt={class:"stat-label"},lt={class:"stat-card"},ct={class:"stat-value"},mt={class:"stat-label"},dt={class:"stat-card"},ut={class:"stat-value"},bt={class:"stat-label"},pt={class:"room-type-section"},vt={class:"section-title"},ft={class:"room-type-distribution"},ht={class:"room-type-count"},kt={key:0,class:"remark-section"},Rt={class:"section-title"},yt={class:"remark-container"},Ct={class:"remark-content"},Tt=t({__name:"teamMainOrder",props:{teamCode:{default:""},bindCode:{default:""},isEntryAccount:{default:"0"}},setup(t){const G=t,{t:Tt}=s(),gt=e(),It=a({gcode:"",hcode:"",teamCode:"",teamName:"",teamType:"",contact:"",planCheckinTime:null,roomCount:0,checkInRoomCount:0,personCount:0,creditPersonCount:0,creditRoomCount:0,settlePersonCount:0,settleRoomCount:0,remark:"",teamRooms:[],roomTypeCounts:[]}),_t=a([]),Pt=a(!1);async function jt(){const t=await j.getTeamMainOrder({gcode:gt.gcode,hcode:gt.hcode,teamCode:G.teamCode});It.value=t.data}function wt(t){const s=["primary","success","warning","danger","info"];return s[t%s.length]}return i((async()=>{await Promise.all([jt()]),It.value.teamRooms&&It.value.teamRooms.length>0&&(_t.value=[It.value.teamRooms[0].orderNo])})),a({tableAutoHeight:!0,showWidth:"400px"}),(t,s)=>{const e=g,a=I,i=_,j=P;return o(),n(h,null,[r("div",N,[l(j,{class:"overview-card",shadow:"hover"},{header:c((()=>[r("div",O,[r("div",A,[l(e,null,{default:c((()=>[l(m(d))])),_:1}),r("span",null,u(m(Tt)("teamInfo.title")),1),l(a,{type:"primary",effect:"plain",class:"ml-2"},{default:c((()=>{return[b(u((t=m(It).teamType,Tt("meeting_group"===t?"teamType.meetingGroup":"travel_group"===t?"teamType.travelGroup":"teamType.other"))),1)];var t})),_:1})]),r("div",null,[l(i,{type:"primary",onClick:s[0]||(s[0]=t=>Pt.value=!0)},{default:c((()=>[b(u(m(Tt)("actions.printTeamRegistration")),1)])),_:1})])])])),default:c((()=>{return[r("div",S,[r("div",M,[r("div",U,[r("span",x,u(m(Tt)("teamInfo.teamName"))+"：",1),r("span",F,u(m(It).teamName||m(Tt)("common.none")),1)]),r("div",H,[r("span",D,u(m(Tt)("teamInfo.contact"))+"：",1),r("span",E,u(m(It).contact||m(Tt)("common.none")),1)]),r("div",V,[r("span",z,u(m(Tt)("teamInfo.plannedArrivalTime"))+"：",1),r("span",q,[l(e,null,{default:c((()=>[l(m(p))])),_:1}),b(" "+u((t=m(It).planCheckinTime,t?T(t).format("MM-DD HH:mm"):Tt("common.none"))),1)])])])]),r("div",B,[r("div",W,[l(e,null,{default:c((()=>[l(m(v))])),_:1}),r("span",null,u(m(Tt)("statistics.roomStatistics")),1)]),r("div",Y,[r("div",J,[r("div",K,u(m(It).roomCount),1),r("div",L,u(m(Tt)("statistics.totalRooms")),1)]),r("div",Q,[r("div",X,u(m(It).checkInRoomCount),1),r("div",Z,u(m(Tt)("statistics.checkedInRooms")),1)]),r("div",$,[r("div",tt,u(m(It).personCount),1),r("div",st,u(m(Tt)("statistics.checkedInPersons")),1)]),r("div",et,[r("div",at,u(m(It).creditRoomCount),1),r("div",it,u(m(Tt)("statistics.creditRooms")),1)]),r("div",ot,[r("div",nt,u(m(It).creditPersonCount),1),r("div",rt,u(m(Tt)("statistics.creditPersons")),1)]),r("div",lt,[r("div",ct,u(m(It).settleRoomCount),1),r("div",mt,u(m(Tt)("statistics.settledRooms")),1)]),r("div",dt,[r("div",ut,u(m(It).settlePersonCount),1),r("div",bt,u(m(Tt)("statistics.settledPersons")),1)])])]),r("div",pt,[r("div",vt,[l(e,null,{default:c((()=>[l(m(f))])),_:1}),r("span",null,u(m(Tt)("roomType.distribution")),1)]),r("div",ft,[(o(!0),n(h,null,k(m(It).roomTypeCounts,((t,s)=>(o(),n("div",{key:s,class:"room-type-item"},[l(a,{type:wt(s),effect:"light"},{default:c((()=>[b(u(t.rtName),1)])),_:2},1032,["type"]),r("span",ht,u(t.count)+"间",1)])))),128))])]),m(It).remark?(o(),n("div",kt,[r("div",Rt,[l(e,null,{default:c((()=>[l(m(v))])),_:1}),r("span",null,u(m(Tt)("remark.title")),1)]),r("div",yt,[r("div",Ct,u(m(It).remark),1)])])):R("",!0)];var t})),_:1})]),m(Pt)?(o(),y(w,{key:0,modelValue:m(Pt),"onUpdate:modelValue":s[1]||(s[1]=t=>C(Pt)?Pt.value=t:null),"bind-code":G.bindCode},null,8,["modelValue","bind-code"])):R("",!0)],64)}}});function gt(t){const s=t;s.__i18n=s.__i18n||[],s.__i18n.push({locale:"",resource:{en:{teamInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"Team Information"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"Team Name"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"Contact Person"}},plannedArrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"Planned Arrival Time"}}},teamType:{meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"Meeting Group"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"Travel Group"}},other:{t:0,b:{t:2,i:[{t:3}],s:"Other"}}},statistics:{roomStatistics:{t:0,b:{t:2,i:[{t:3}],s:"Room Statistics"}},totalRooms:{t:0,b:{t:2,i:[{t:3}],s:"Total Rooms"}},checkedInRooms:{t:0,b:{t:2,i:[{t:3}],s:"Checked-in Rooms"}},checkedInPersons:{t:0,b:{t:2,i:[{t:3}],s:"Checked-in Persons"}},creditRooms:{t:0,b:{t:2,i:[{t:3}],s:"Credit Rooms"}},creditPersons:{t:0,b:{t:2,i:[{t:3}],s:"Credit Persons"}},settledRooms:{t:0,b:{t:2,i:[{t:3}],s:"Settled Rooms"}},settledPersons:{t:0,b:{t:2,i:[{t:3}],s:"Settled Persons"}}},roomType:{distribution:{t:0,b:{t:2,i:[{t:3}],s:"Room Type Distribution"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:" rooms"}}},remark:{title:{t:0,b:{t:2,i:[{t:3}],s:"Remarks"}}},status:{checkedIn:{t:0,b:{t:2,i:[{t:3}],s:"Checked In"}},checkedOut:{t:0,b:{t:2,i:[{t:3}],s:"Checked Out"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"Credit"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"Unknown"}}},actions:{printTeamRegistration:{t:0,b:{t:2,i:[{t:3}],s:"Print Team Registration Form"}}},common:{none:{t:0,b:{t:2,i:[{t:3}],s:"None"}}}},"zh-cn":{teamInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"团队信息"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"团队名称"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"联系人"}},plannedArrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"预抵时间"}}},teamType:{meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"会议团"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"旅行团"}},other:{t:0,b:{t:2,i:[{t:3}],s:"其他"}}},statistics:{roomStatistics:{t:0,b:{t:2,i:[{t:3}],s:"房间统计"}},totalRooms:{t:0,b:{t:2,i:[{t:3}],s:"总房间数"}},checkedInRooms:{t:0,b:{t:2,i:[{t:3}],s:"已入住房间"}},checkedInPersons:{t:0,b:{t:2,i:[{t:3}],s:"入住人数"}},creditRooms:{t:0,b:{t:2,i:[{t:3}],s:"挂账房间"}},creditPersons:{t:0,b:{t:2,i:[{t:3}],s:"挂账人数"}},settledRooms:{t:0,b:{t:2,i:[{t:3}],s:"已结账房间"}},settledPersons:{t:0,b:{t:2,i:[{t:3}],s:"已结账人数"}}},roomType:{distribution:{t:0,b:{t:2,i:[{t:3}],s:"房型分布"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:"间"}}},remark:{title:{t:0,b:{t:2,i:[{t:3}],s:"备注信息"}}},status:{checkedIn:{t:0,b:{t:2,i:[{t:3}],s:"在住"}},checkedOut:{t:0,b:{t:2,i:[{t:3}],s:"退房"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"挂账"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"未知"}}},actions:{printTeamRegistration:{t:0,b:{t:2,i:[{t:3}],s:"打印团队登记单"}}},common:{none:{t:0,b:{t:2,i:[{t:3}],s:"暂无"}}}},km:{teamInfo:{title:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានក្រុម"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះក្រុម"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកទំនាក់ទំនង"}},plannedArrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាមកដល់ដែលបានគ្រោង"}}},teamType:{meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមប្រជុំ"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមទេសចរណ៍"}},other:{t:0,b:{t:2,i:[{t:3}],s:"ផ្សេងៗ"}}},statistics:{roomStatistics:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថិតិបន្ទប់"}},totalRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់សរុប"}},checkedInRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានចូលស្នាក់"}},checkedInPersons:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនអ្នកចូលស្នាក់"}},creditRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់កាន់កាប់"}},creditPersons:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនអ្នកកាន់កាប់"}},settledRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានទូទាត់"}},settledPersons:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនអ្នកបានទូទាត់"}}},roomType:{distribution:{t:0,b:{t:2,i:[{t:3}],s:"ការចែកចាយប្រភេទបន្ទប់"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:" បន្ទប់"}}},remark:{title:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានកំណត់ចំណាំ"}}},status:{checkedIn:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងស្នាក់"}},checkedOut:{t:0,b:{t:2,i:[{t:3}],s:"ចេញ"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"កាន់កាប់"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"មិនស្គាល់"}}},actions:{printTeamRegistration:{t:0,b:{t:2,i:[{t:3}],s:"បោះពុម្ពបែបបទចុះឈ្មោះក្រុម"}}},common:{none:{t:0,b:{t:2,i:[{t:3}],s:"គ្មាន"}}}}}})}gt(Tt);const It=G(Tt,[["__scopeId","data-v-b842a066"]]);export{It as default};
//# sourceMappingURL=teamMainOrder-RmJelicD.js.map
