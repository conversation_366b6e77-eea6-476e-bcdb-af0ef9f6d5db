import{d as e,W as a,b as s,aO as t,y as o,ae as l,B as n,o as i,c as r,f as c,w as u,u as h,R as g,a7 as m,g as p,h as v,Y as f,av as d,am as w}from"./index-CkEhI1Zk.js";import x from"./event-bus-CDHrRX6w.js";import b from"./changeHotel-CAzJ7qUQ.js";const D={class:"flex items-center justify-center"},y=["src"],k={key:0,class:"ml-[10px] block truncate"},z={class:"text-[12px]"},L={class:"ml-[10px] !text-[12px]"},j=e({name:"Logo",__name:"index",props:{showLogo:{type:Boolean,default:!0},showTitle:{type:Boolean,default:!0}},setup(e){const j=a(),N=s("/assets/logo-RDCpeKR2.png"),Y=s(t.local.has("shiftName")?t.local.get("shiftName"):""),R=s(t.local.has("bizDate")?t.local.get("bizDate"):"");function V(e){"shiftName"===e.key&&e.newValue&&(Y.value=e.newValue),"bizDate"===e.key&&e.newValue&&(R.value=e.newValue)}o((()=>{window.addEventListener("storage",V);const e=setInterval((()=>{const e=t.local.has("shiftName")?t.local.get("shiftName"):"",a=t.local.has("bizDate")?t.local.get("bizDate"):"";e!==Y.value&&(Y.value=e),a!==R.value&&(R.value=a)}),1e3);l((()=>{clearInterval(e),window.removeEventListener("storage",V)}))})),o((()=>{x.on("bizDateUpdated",(e=>{R.value=e}))}));const _=n((()=>j.settings.home.enable?j.settings.home.fullPath:"")),B=n((()=>({"cursor-pointer":j.settings.home.enable})));return(e,a)=>{const s=w("RouterLink");return i(),r("div",D,[c(s,{to:h(_),class:m(["h-[var(--g-sidebar-logo-height)] w-inherit flex-center gap-2 px-3 text-inherit no-underline",h(B)])},{default:u((()=>[e.showLogo?(i(),r("img",{key:0,src:h(N),class:"logo h-[30px] w-[30px] object-contain"},null,8,y)):g("",!0)])),_:1},8,["to","class"]),e.showTitle?(i(),r("div",k,[c(b),p("div",z,[v(f((t=h(R),t?d(t).format("YYYY/MM/DD"):"")),1),p("span",L,f(h(Y)),1)])])):g("",!0)]);var t}}});export{j as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-CJvwvkTS.js.map
