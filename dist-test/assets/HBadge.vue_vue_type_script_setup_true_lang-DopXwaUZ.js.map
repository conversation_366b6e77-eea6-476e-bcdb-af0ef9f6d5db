{"version": 3, "file": "HBadge.vue_vue_type_script_setup_true_lang-DopXwaUZ.js", "sources": ["../../src/layouts/ui-kit/HBadge.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nconst props = defineProps<{\r\n  value: string | number | boolean\r\n}>()\r\n\r\nconst show = computed(() => {\r\n  switch (typeof props.value) {\r\n    case 'string':\r\n      return props.value.length > 0\r\n    case 'number':\r\n      return props.value > 0\r\n    case 'boolean':\r\n      return props.value\r\n    default:\r\n      return props.value !== undefined && props.value !== null\r\n  }\r\n})\r\n\r\nconst transitionClass = ref({\r\n  enterActiveClass: 'ease-in-out duration-500',\r\n  enterFromClass: 'opacity-0',\r\n  enterToClass: 'opacity-100',\r\n  leaveActiveClass: 'ease-in-out duration-500',\r\n  leaveFromClass: 'opacity-100',\r\n  leaveToClass: 'opacity-0',\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"relative inline-flex\">\r\n    <slot />\r\n    <Transition v-bind=\"transitionClass\">\r\n      <span\r\n        v-if=\"show\"\r\n        class=\"absolute start-[50%] top-0 z-20 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light -translate-y-[50%] dark-ring-dark\"\r\n        :class=\"{ '-indent-9999 w-1.5 h-1.5 px-0! start-[100%]! -translate-x-[50%] rtl:translate-x-[50%] before:content-empty before:block before:bg-ui-primary before:w-full before:h-full before:rounded-full before:absolute before:start-0 before:top-0 before:animate-ping': value === true }\"\r\n      >\r\n        {{ value }}\r\n      </span>\r\n    </Transition>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "show", "computed", "value", "length", "transitionClass", "ref", "enterActiveClass", "enterFromClass", "enterToClass", "leaveActiveClass", "leaveFromClass", "leaveToClass"], "mappings": "yPACA,MAAMA,EAAQC,EAIRC,EAAOC,GAAS,KACZ,cAAOH,EAAMI,OACnB,IAAK,SACI,OAAAJ,EAAMI,MAAMC,OAAS,EAC9B,IAAK,SACH,OAAOL,EAAMI,MAAQ,EACvB,IAAK,UACH,OAAOJ,EAAMI,MACf,QACE,YAAuB,IAAhBJ,EAAMI,OAAuC,OAAhBJ,EAAMI,MAAU,IAIpDE,EAAkBC,EAAI,CAC1BC,iBAAkB,2BAClBC,eAAgB,YAChBC,aAAc,cACdC,iBAAkB,2BAClBC,eAAgB,cAChBC,aAAc"}