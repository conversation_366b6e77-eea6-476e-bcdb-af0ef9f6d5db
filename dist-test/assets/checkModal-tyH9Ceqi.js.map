{"version": 3, "file": "checkModal-tyH9Ceqi.js", "sources": ["../../node_modules/uuid/dist/esm-browser/stringify.js", "../../node_modules/uuid/dist/esm-browser/rng.js", "../../node_modules/uuid/dist/esm-browser/native.js", "../../node_modules/uuid/dist/esm-browser/v4.js", "../../src/views/order/info/components/orderdetail/checkModal.vue"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n", "let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default { randomUUID };\n", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n", "<i18n>\r\n  {\r\n    \"en\": {\r\n      \"transferCheckin\": \"Check-in\",\r\n      \"roomInfo\": \"Room Information\",\r\n      \"roomType\": \"Room Type\",\r\n      \"roomNumber\": \"Room No\",\r\n      \"operation\": \"Actions\",\r\n      \"manualArrange\": \"Manual Arrange\",\r\n      \"autoArrangeRoom\": \"Auto Arrange\",\r\n      \"guestInfo\": \"Guest Information\",\r\n      \"scanId\": \"Scan ID Card\",\r\n      \"manualInput\": \"Manual Input\",\r\n      \"basicInfo\": \"Basic Information\",\r\n      \"name\": \"Name\",\r\n      \"contactPhone\": \"Phone\",\r\n      \"idType\": \"ID Type\",\r\n      \"idNumber\": \"ID Number\",\r\n      \"birthday\": \"Birthday\",\r\n      \"selectDate\": \"Select Date\",\r\n      \"gender\": \"Gender\",\r\n      \"male\": \"Male\",\r\n      \"female\": \"Female\",\r\n      \"confidential\": \"Confidential\",\r\n      \"nation\": \"Nation\",\r\n      \"currentAddress\": \"Address\",\r\n      \"cancel\": \"Cancel\",\r\n      \"confirm\": \"Check In\",\r\n      \"checkinSuccess\": \"Check-in successful\",\r\n      \"checkinFailed\": \"Check-in failed\",\r\n      \"addGuest\": \"Add Guest\",\r\n      \"removeGuest\": \"Remove\",\r\n      \"primaryGuest\": \"Primary Guest\",\r\n      \"additionalGuest\": \"Additional Guest\",\r\n      \"guestCount\": \"Guest {index}\",\r\n      \"inputName\": \"Name is required\",\r\n      \"inputIdNo\": \"ID number is required\",\r\n      \"mustArrangeRoom\": \"Please arrange room first\",\r\n      \"mustHaveGuest\": \"At least one guest is required\",\r\n      \"validationFailed\": \"Please complete all required fields\",\r\n      \"confirmAutoArrange\": \"Confirm auto arrange room?\",\r\n      \"inputAddress\": \"Please input Current Address\",\r\n      \"arrangeSuccess\":  \"Arrange room success\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"transferCheckin\": \"办理入住\",\r\n      \"roomInfo\": \"房间信息\",\r\n      \"roomType\": \"房型\",\r\n      \"roomNumber\": \"房号\",\r\n      \"operation\": \"操作\",\r\n      \"manualArrange\": \"手动排房\",\r\n      \"autoArrangeRoom\": \"自动排房\",\r\n      \"guestInfo\": \"客人信息\",\r\n      \"scanId\": \"刷身份证\",\r\n      \"manualInput\": \"手动输入\",\r\n      \"basicInfo\": \"基本信息\",\r\n      \"name\": \"姓名\",\r\n      \"contactPhone\": \"联系电话\",\r\n      \"idType\": \"证件类型\",\r\n      \"idNumber\": \"证件号码\",\r\n      \"birthday\": \"出生日期\",\r\n      \"selectDate\": \"选择日期\",\r\n      \"gender\": \"性别\",\r\n      \"male\": \"男\",\r\n      \"female\": \"女\",\r\n      \"confidential\": \"保密\",\r\n      \"nation\": \"民族\",\r\n      \"currentAddress\": \"现住址\",\r\n      \"cancel\": \"取消\",\r\n      \"confirm\": \"确定\",\r\n      \"checkinSuccess\": \"入住成功\",\r\n      \"checkinFailed\": \"入住失败\",\r\n      \"addGuest\": \"添加客人\",\r\n      \"removeGuest\": \"移除\",\r\n      \"primaryGuest\": \"主客\",\r\n      \"additionalGuest\": \"同住\",\r\n      \"guestCount\": \"客人 {index}\",\r\n      \"inputName\": \"请输入姓名\",\r\n      \"inputIdNo\": \"请输入证件号码\",\r\n      \"mustArrangeRoom\": \"必须先排房\",\r\n      \"mustHaveGuest\": \"必须至少有一个客人信息\",\r\n      \"validationFailed\": \"请完善所有必填信息\",\r\n      \"confirmAutoArrange\": \"确认自动排房？\",\r\n      \"inputAddress\": \"请输入现住址\",\r\n      \"arrangeSuccess\":  \"排房成功\"\r\n    },\r\n    \"km\": {\r\n      \"transferCheckin\": \"ចុះឈ្មោះចូល\",\r\n      \"roomInfo\": \"ព័ត៌មានបន្ទប់\",\r\n      \"roomType\": \"ប្រភេទបន្ទប់\",\r\n      \"roomNumber\": \"លេខបន្ទប់\",\r\n      \"operation\": \"សកម្មភាព\",\r\n      \"manualArrange\": \"រៀបចំដោយដៃ\",\r\n      \"autoArrangeRoom\": \"រៀបចំដោយស្វ័យប្រវត្តិ\",\r\n      \"guestInfo\": \"ព័ត៌មានភ្ញៀវ\",\r\n      \"scanId\": \"ស្កេនអត្តសញ្ញាណប័ណ្ណ\",\r\n      \"manualInput\": \"បញ្ចូលដោយដៃ\",\r\n      \"basicInfo\": \"ព័ត៌មានមូលដ្ឋាន\",\r\n      \"name\": \"ឈ្មោះ\",\r\n      \"contactPhone\": \"លេខទូរស័ព្ទ\",\r\n      \"idType\": \"ប្រភេទឯកសារ\",\r\n      \"idNumber\": \"លេខឯកសារ\",\r\n      \"birthday\": \"ថ្ងៃខែឆ្នាំកំណើត\",\r\n      \"selectDate\": \"ជ្រើសរើសកាលបរិច្ឆេទ\",\r\n      \"gender\": \"ភេទ\",\r\n      \"male\": \"ប្រុស\",\r\n      \"female\": \"ស្រី\",\r\n      \"confidential\": \"អាថ៌កំបាំង\",\r\n      \"nation\": \"ជាតិសាសន៍\",\r\n      \"currentAddress\": \"អាសយដ្ឋានបច្ចុប្បន្ន\",\r\n      \"cancel\": \"បោះបង់\",\r\n      \"confirm\": \"យល់ព្រម\",\r\n      \"checkinSuccess\": \"ចុះឈ្មោះចូលបានជោគជ័យ\",\r\n      \"checkinFailed\": \"ចុះឈ្មោះចូលបរាជ័យ\",\r\n      \"addGuest\": \"បន្ថែមភ្ញៀវ\",\r\n      \"removeGuest\": \"ដកចេញ\",\r\n      \"primaryGuest\": \"ភ្ញៀវចម្បង\",\r\n      \"additionalGuest\": \"ភ្ញៀវរួម\",\r\n      \"guestCount\": \"ភ្ញៀវ {index}\",\r\n      \"inputName\": \"ត្រូវការឈ្មោះ\",\r\n      \"inputIdNo\": \"ត្រូវការលេខឯកសារ\",\r\n      \"mustArrangeRoom\": \"ត្រូវរៀបចំបន្ទប់មុន\",\r\n      \"mustHaveGuest\": \"ត្រូវមានយ៉ាងហោចណាស់ភ្ញៀវម្នាក់\",\r\n      \"validationFailed\": \"សូមបំពេញព័ត៌មានដែលត្រូវការទាំងអស់\",\r\n      \"confirmAutoArrange\": \"បញ្ជាក់រៀបចំបន្ទប់ដោយស្វ័យប្រវត្តិ?\",\r\n      \"inputAddress\": \"សូមបញ្ចូលអាសយដ្ឋានបច្ចុប្បន្ន\",\r\n      \"arrangeSuccess\": \"រៀបចំបន្ទប់បានជោគជ័យ\"\r\n    }\r\n  }\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel } from '@/models/index'\r\nimport { bookApi, dictDataApi, memberApi, orderApi } from '@/api/modules/index'\r\nimport { SexEnum } from '@/models/dict/constants.ts'\r\nimport { DICT_TYPE_ID_TYPE, DictTypeEnum, IdType } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { CardReader } from '@/store/websocket/CardReader'\r\nimport { getAuth } from '@/utils/index'\r\nimport { getGenderFromIdCard } from '@/utils/myStringUtil'\r\nimport { saveAndMask } from '@/utils/sensitive'\r\nimport ArrangeRoomsDialog from '@/views/room/components/arrangeRooms/arrangeRooms.vue'\r\nimport { ArrowUp, CreditCard, House, Plus, User } from '@element-plus/icons-vue'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst emits = defineEmits<{\r\n  success: []\r\n  reverts: []\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\ninterface GuestInfo {\r\n  id: string\r\n  address: string\r\n  birthday: string\r\n  idNo: string\r\n  idType: string\r\n  isMadeCard: string\r\n  name: string\r\n  nation: string\r\n  phone: string\r\n  sex: string\r\n  isMain: boolean\r\n}\r\n\r\nonMounted(() => {\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n})\r\n\r\nonBeforeUnmount(() => {\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n})\r\n\r\nconst userStore = useUserStore()\r\nconst visible = ref(false)\r\nconst tableData = ref<{ rNo: string }[]>([])\r\nconst guests = ref<GuestInfo[]>([\r\n  {\r\n    id: uuidv4(),\r\n    address: '',\r\n    birthday: '',\r\n    idNo: '',\r\n    idType: IdType.IDCERT,\r\n    isMadeCard: '',\r\n    name: '',\r\n    nation: '01',\r\n    phone: '',\r\n    sex: SexEnum.MALE.toString(),\r\n    isMain: true,\r\n  },\r\n])\r\n\r\n// 查询参数\r\nconst queryParams = reactive({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n\r\n// 客户查询相关变量\r\nconst backQueryString = ref()\r\nconst customerTableData = ref<any[]>([])\r\nlet timeout: ReturnType<typeof setTimeout>\r\n\r\n// 分页查询参数\r\nconst popoverQuery = reactive({\r\n  pageNo: 1,\r\n  pageSize: 10,\r\n})\r\n\r\n// 添加客历信息存储\r\nconst customerHistories = ref<{ [key: string]: any }>({})\r\nconst customerPopovers = ref<{ [key: string]: boolean }>({})\r\n\r\n// 生成唯一行标识的函数\r\nfunction getRowKey(guestIndex: number) {\r\n  return `guest_${guestIndex}`\r\n}\r\n\r\nconst idTypes = ref<DictDataModel[]>([])\r\nconst nationList = ref<DictDataModel[]>([])\r\nconst loading = ref(false)\r\nconst formData = ref<any>({})\r\nconst terracesVisible = ref(false)\r\nconst expandedGuestIndex = ref(-1)\r\n// 获取字典数据\r\nconst dictTypes = [DICT_TYPE_ID_TYPE, DictTypeEnum.DICT_TYPE_NATION]\r\n\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    idTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_ID_TYPE)\r\n    nationList.value = res.data.filter((item: any) => item.dictType === DictTypeEnum.DICT_TYPE_NATION)\r\n  })\r\n}\r\n\r\n// 添加客人\r\nfunction addGuest() {\r\n  guests.value.push({\r\n    id: uuidv4(),\r\n    address: '',\r\n    birthday: '',\r\n    idNo: '',\r\n    idType: IdType.IDCERT,\r\n    isMadeCard: '',\r\n    name: '',\r\n    nation: '01',\r\n    phone: '',\r\n    sex: SexEnum.MALE.toString(),\r\n    isMain: false,\r\n  })\r\n}\r\n\r\n// 移除客人\r\nfunction removeGuest(index: number) {\r\n  if (guests.value.length > 1) {\r\n    guests.value.splice(index, 1)\r\n  }\r\n}\r\n\r\n// 验证表单\r\nfunction validateForm(): boolean {\r\n  // 检查是否已排房\r\n  if (!tableData.value[0]?.rNo) {\r\n    ElMessage.error(t('mustArrangeRoom'))\r\n    return false\r\n  }\r\n\r\n  // 检查是否至少有一个客人\r\n  if (guests.value.length === 0) {\r\n    ElMessage.error(t('mustHaveGuest'))\r\n    return false\r\n  }\r\n\r\n  // 检查每个客人的必填信息\r\n  for (let i = 0; i < guests.value.length; i++) {\r\n    const guest = guests.value[i]\r\n    if (!guest.name.trim()) {\r\n      ElMessage.error(`${t('guestCount', { index: i + 1 })} - ${t('inputName')}`)\r\n      return false\r\n    }\r\n    // if (!guest.idNo.trim()) {\r\n    //   ElMessage.error(`${t('guestCount', { index: i + 1 })} - ${t('inputIdNo')}`)\r\n    //   return false\r\n    // }\r\n  }\r\n\r\n  return true\r\n}\r\n\r\n// 提交表单\r\nasync function onSubmit() {\r\n  if (!validateForm()) {\r\n    return\r\n  }\r\n\r\n  const allPersons = guests.value.map((guest, index) => ({\r\n    name: guest.name,\r\n    sex: guest.sex,\r\n    nation: guest.nation,\r\n    address: guest.address,\r\n    phone: guest.phone,\r\n    idType: guest.idType,\r\n    idNo: guest.idNo,\r\n    isMain: index === 0 ? '1' : '0',\r\n    isMadeCard: '',\r\n  }))\r\n\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    bookNo: formData.value.bookNo,\r\n    batchNo: '',\r\n    orders: [\r\n      {\r\n        orderNo: formData.value.orderNo,\r\n        bkNum: formData.value.bkNum,\r\n        persons: allPersons,\r\n      },\r\n    ],\r\n  }\r\n\r\n  loading.value = true\r\n  try {\r\n    const res = await orderApi.bookCheckIn(params)\r\n    if (res.code === 0) {\r\n      ElMessage.success({\r\n        message: t('checkinSuccess'),\r\n        center: true,\r\n      })\r\n      emits('success')\r\n      onCancel()\r\n    } else {\r\n      ElMessage.error(res.msg)\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error(t('checkinFailed'))\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 取消操作\r\nfunction onCancel() {\r\n  visible.value = false\r\n  guests.value = [\r\n    {\r\n      id: uuidv4(),\r\n      address: '',\r\n      birthday: '',\r\n      idNo: '',\r\n      idType: IdType.IDCERT,\r\n      isMadeCard: '',\r\n      name: '',\r\n      nation: '01',\r\n      phone: '',\r\n      sex: SexEnum.MALE.toString(),\r\n      isMain: true,\r\n    },\r\n  ]\r\n  emits('reverts')\r\n}\r\n\r\n// 刷身份证\r\nasync function scanIdCard(guestIndex: number) {\r\n  const guest = guests.value[guestIndex]\r\n  if (guest.idType === IdType.IDCERT) {\r\n    if (typeof (window as any).CallBridge === 'undefined' && typeof (window as any).__RUNNING_IN_PMS_AGENT__ === 'undefined') {\r\n      const downloadLink = `<a href=\"${import.meta.env.VITE_APP_CLIENT_DOWNLOAD_URL}\" target=\"_blank\">下载Hotel-Agent客户端</a>`\r\n      ElMessage({\r\n        showClose: true,\r\n        message: `读取证件只能在Hotel-Agent中操作，请${downloadLink}。`,\r\n        type: 'warning',\r\n        dangerouslyUseHTMLString: true,\r\n      })\r\n      return\r\n    }\r\n\r\n    if (CardReader.isConnected) {\r\n      CardReader.closeSocket()\r\n      await new Promise((resolve) => setTimeout(resolve, 300))\r\n    }\r\n\r\n    CardReader.initCardReader((message: string) => {\r\n      handleIdCardData(message, guestIndex)\r\n    })\r\n\r\n    const timer = setInterval(() => {\r\n      if (CardReader.isConnected) {\r\n        CardReader.readIdCard()\r\n        clearInterval(timer)\r\n      }\r\n    }, 200)\r\n  }\r\n}\r\n\r\n// 处理身份证数据\r\nasync function handleIdCardData(message: string, guestIndex: number) {\r\n  try {\r\n    const data = JSON.parse(message)\r\n    const guest = guests.value[guestIndex]\r\n    guest.name = data.peopleName\r\n    guest.idNo = data.peopleIDCode\r\n\r\n    // 优先使用返回的性别信息，如果没有则根据身份证号码判断\r\n    if (data.peopleSex) {\r\n      guest.sex = data.peopleSex === '男' ? SexEnum.MALE : SexEnum.FEMALE\r\n    } else if (data.peopleIDCode) {\r\n      guest.sex = getGenderFromIdCard(data.peopleIDCode)\r\n    } else {\r\n      guest.sex = SexEnum.SECRECY\r\n    }\r\n\r\n    guest.address = data.peopleAddress\r\n    guest.birthday = data.peopleBirthday\r\n    guest.nation = data.peopleNation.indexOf('族') ? data.peopleNation : `${data.peopleNation}族`\r\n\r\n    // 查询客历信息\r\n    if (data.peopleIDCode) {\r\n      try {\r\n        const customerRes = await memberApi.getCustomerByIdNo({\r\n          gcode: userStore.gcode,\r\n          idNo: data.peopleIDCode,\r\n        })\r\n        if (customerRes.code === 0 && customerRes.data) {\r\n          // 生成当前行的唯一标识\r\n          const rowKey = getRowKey(guestIndex)\r\n          // 对sex属性进行转换：1代表男，2代表女，其他为保密\r\n          const transformedData = {\r\n            ...customerRes.data,\r\n            sex: customerRes.data.sex === '1' ? '男' : customerRes.data.sex === '0' ? '女' : '保密',\r\n          }\r\n\r\n          // 存储客历信息到对应的行\r\n          customerHistories.value[rowKey] = transformedData\r\n\r\n          // 确保 DOM 更新后再显示对应行的气泡框\r\n          await nextTick()\r\n          customerPopovers.value[rowKey] = true\r\n\r\n          // 填充手机号到对应的输入框\r\n          guest.phone = customerRes.data.phone || ''\r\n        } else {\r\n          console.log('客历查询失败或无数据:', customerRes)\r\n        }\r\n      } catch (error) {\r\n        console.log('查询客历失败:', error)\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('Error parsing message:', error)\r\n  }\r\n}\r\n\r\n/**\r\n * 远程搜索客户信息\r\n */\r\nfunction querySearch(queryString: string, cb: (arg: any) => void) {\r\n  if (!queryString) {\r\n    return cb([])\r\n  }\r\n\r\n  clearTimeout(timeout)\r\n\r\n  // 值没有改变的情况下，不向后端发送请求\r\n  if (queryString !== backQueryString.value) {\r\n    timeout = setTimeout(async () => {\r\n      try {\r\n        const { data } = await memberApi.getCustomerList({\r\n          ...queryParams,\r\n          ...popoverQuery,\r\n          name: queryString,\r\n        })\r\n        backQueryString.value = queryString\r\n        const dataList = data.list || []\r\n\r\n        // 为每个数据项添加value属性，el-autocomplete需要这个属性，并对敏感信息进行脱敏\r\n        const formattedData = dataList.map((item: any) => ({\r\n          ...item,\r\n          value: item.name,\r\n          // 保存原始值\r\n          originalPhone: item.phone,\r\n          originalIdNo: item.idNo,\r\n          // 脱敏显示\r\n          phone: item.phone ? saveAndMask(item.phone, 'phone') : '',\r\n          idNo: item.idNo ? saveAndMask(item.idNo, 'idNo') : '',\r\n        }))\r\n\r\n        customerTableData.value = formattedData\r\n        cb(formattedData)\r\n      } catch (error) {\r\n        console.error('查询客户信息失败:', error)\r\n        cb([])\r\n      }\r\n    }, 1500 * Math.random())\r\n  } else {\r\n    cb(customerTableData.value)\r\n  }\r\n}\r\n\r\n/**\r\n * 选择客户信息\r\n */\r\nfunction handleSelect(item: Record<string, any>, guestIndex: number) {\r\n  const guest = guests.value[guestIndex]\r\n  guest.name = item.name\r\n  // 使用原始值而不是脱敏值\r\n  guest.phone = item.originalPhone || item.phone\r\n  guest.idNo = item.originalIdNo || item.idNo\r\n  guest.idType = item.idType || IdType.IDCERT\r\n}\r\n\r\n// 自动排房\r\nfunction automaticRoom() {\r\n  ElMessageBox.confirm(t('confirmAutoArrange'), {\r\n    confirmButtonText: t('confirm'),\r\n    cancelButtonText: t('cancel'),\r\n    type: 'warning',\r\n  })\r\n    .then(() => {\r\n      bookApi\r\n        .autoArrangeRooms({\r\n          gcode: userStore.gcode,\r\n          hcode: userStore.hcode,\r\n          bookNo: formData.value.bookNo,\r\n          batchNo: formData.value.batchNo,\r\n          orderNo: formData.value.orderNo,\r\n        })\r\n        .then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success(t('arrangeSuccess'))\r\n            tableData.value[0].rNo = res.data[0].rNo\r\n          }\r\n        })\r\n    })\r\n    .catch(() => {})\r\n}\r\n\r\n// 手动排房\r\nfunction onTerraces() {\r\n  terracesVisible.value = true\r\n}\r\n\r\n// 排房回调\r\nfunction onReload(val: any) {\r\n  tableData.value[0].rNo = val[0]\r\n}\r\n\r\nfunction refresh(row: any) {\r\n  tableData.value = row\r\n}\r\n\r\nfunction open(row: any) {\r\n  getConstants()\r\n  tableData.value = row.data\r\n  formData.value = row\r\n  visible.value = true\r\n}\r\n\r\ndefineExpose({\r\n  open,\r\n  refresh,\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"visible\" :title=\"t('transferCheckin')\" width=\"1220px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :before-close=\"onCancel\" class=\"apple-style-dialog\">\r\n      <!-- 房间信息区域 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-header\">\r\n          <h3 class=\"section-title\">\r\n            {{ t('roomInfo') }}\r\n          </h3>\r\n        </div>\r\n        <div class=\"room-info-card\">\r\n          <el-table :data=\"tableData\" class=\"apple-table\">\r\n            <el-table-column :label=\"t('roomType')\" prop=\"rtName\" />\r\n            <el-table-column :label=\"t('roomNumber')\" prop=\"rNo\">\r\n              <template #default=\"scope\">\r\n                <span class=\"room-number\">{{ scope.row.rNo || '未分配' }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- 房间信息区域操作按钮 -->\r\n            <el-table-column :label=\"t('operation')\" width=\"240\">\r\n              <!-- 增加宽度从200到240 -->\r\n              <template #default=\"scope\">\r\n                <div class=\"action-buttons\">\r\n                  <el-button :disabled=\"getAuth('pms:book-room:update:arrange')\" type=\"primary\" plain @click=\"onTerraces\">\r\n                    {{ t('manualArrange') }}\r\n                  </el-button>\r\n                  <el-button :disabled=\"getAuth('pms:book-room:update:arrange')\" type=\"primary\" plain @click=\"automaticRoom\">\r\n                    {{ t('autoArrangeRoom') }}\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 客人信息区域 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-header\">\r\n          <h3 class=\"section-title\">\r\n            {{ t('guestInfo') }}\r\n          </h3>\r\n          <div class=\"action-buttons\">\r\n            <el-button type=\"primary\" link @click=\"addGuest\">\r\n              <el-icon><Plus /></el-icon>\r\n              {{ t('addGuest') }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 客人信息表格 -->\r\n        <div class=\"guests-table-container\">\r\n          <el-table :data=\"guests\" class=\"apple-table guests-table\" row-key=\"id\" :cell-style=\"{ padding: '8px 2px' }\" :header-cell-style=\"{ padding: '12px 2px' }\">\r\n            <el-table-column label=\"类型\" width=\"100\">\r\n              <template #default=\"{ row }\">\r\n                <span class=\"guest-badge\" :class=\"{ primary: row.isMain }\">\r\n                  {{ row.isMain ? t('primaryGuest') : t('additionalGuest') }}\r\n                </span>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column :label=\"t('name')\" width=\"140\">\r\n              <template #default=\"{ row, $index }\">\r\n                <el-autocomplete v-model=\"row.name\" class=\"apple-input table-input\" :placeholder=\"t('inputName')\" size=\"small\" :fetch-suggestions=\"querySearch\" @select=\"(item) => handleSelect(item, $index)\">\r\n                  <template #default=\"{ item }\">\r\n                    <div class=\"flex-around\">\r\n                      <div class=\"w-[100px]\">\r\n                        {{ item.name }}\r\n                      </div>\r\n                      <div class=\"w-[120px]\">\r\n                        {{ item.phone }}\r\n                      </div>\r\n                      <div class=\"w-[60px] text-right\">\r\n                        {{ item.idNo ? item.idNo.slice(-4) : '' }}\r\n                      </div>\r\n                    </div>\r\n                  </template>\r\n                </el-autocomplete>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column :label=\"t('contactPhone')\" width=\"150\">\r\n              <template #default=\"{ row, $index }\">\r\n                <!-- 添加客历信息弹窗 -->\r\n                <el-popover v-model:visible=\"customerPopovers[getRowKey($index)]\" placement=\"top\" :width=\"500\" trigger=\"manual\" popper-class=\"customer-history-popover\">\r\n                  <template #reference>\r\n                    <el-input v-model=\"row.phone\" class=\"apple-input table-input\" size=\"small\" />\r\n                  </template>\r\n                  <div v-if=\"customerHistories[getRowKey($index)]\" class=\"customer-history-content\">\r\n                    <h4 style=\"margin: 0 0 10px 0; color: #409eff\">客历信息</h4>\r\n                    <div class=\"customer-info-grid\">\r\n                      <div class=\"info-item\">\r\n                        <span class=\"label\">姓名：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].name }}</span>\r\n                      </div>\r\n                      <div class=\"info-item\">\r\n                        <span class=\"label\">性别：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].sex }}</span>\r\n                      </div>\r\n                      <div class=\"info-item\">\r\n                        <span class=\"label\">电话：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].phone }}</span>\r\n                      </div>\r\n                      <div class=\"info-item\">\r\n                        <span class=\"label\">入住次数：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].checkinNum }}次</span>\r\n                      </div>\r\n                      <div class=\"info-item\">\r\n                        <span class=\"label\">是否会员：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].isMember === '1' ? '是' : '否' }}</span>\r\n                      </div>\r\n                      <div v-if=\"customerHistories[getRowKey($index)].isMember === '1'\" class=\"info-item\">\r\n                        <span class=\"label\">会员级别：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].mtName }}</span>\r\n                      </div>\r\n                      <div v-if=\"customerHistories[getRowKey($index)].isMember === '1'\" class=\"info-item\">\r\n                        <span class=\"label\">会员卡号：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].mCardNum }}</span>\r\n                      </div>\r\n                      <div class=\"info-item\">\r\n                        <span class=\"label\">最后入住门店：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].lastMerchant }}</span>\r\n                      </div>\r\n                      <div class=\"info-item\">\r\n                        <span class=\"label\">最后入住时间：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].lastCheckinTime }}</span>\r\n                      </div>\r\n                      <div v-if=\"customerHistories[getRowKey($index)].isBlack === '1'\" class=\"info-item\">\r\n                        <span class=\"label\" style=\"color: #f56c6c\">黑名单：</span>\r\n                        <span class=\"value\" style=\"color: #f56c6c\">{{ customerHistories[getRowKey($index)].blackReason }}</span>\r\n                      </div>\r\n                      <div v-if=\"customerHistories[getRowKey($index)].remark\" class=\"info-item\">\r\n                        <span class=\"label\">备注：</span>\r\n                        <span class=\"value\">{{ customerHistories[getRowKey($index)].remark }}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div style=\"text-align: right; margin-top: 15px\">\r\n                      <el-button size=\"small\" @click=\"customerPopovers[getRowKey($index)] = false\"> 关闭 </el-button>\r\n                    </div>\r\n                  </div>\r\n                </el-popover>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column :label=\"t('idType')\" width=\"140\">\r\n              <template #default=\"{ row }\">\r\n                <el-select v-model=\"row.idType\" class=\"apple-select table-select\" size=\"small\">\r\n                  <el-option v-for=\"item in idTypes\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column :label=\"t('idNumber')\" width=\"170\">\r\n              <template #default=\"{ row }\">\r\n                <el-input v-model=\"row.idNo\" class=\"apple-input table-input\" :placeholder=\"t('inputIdNo')\" size=\"small\" />\r\n              </template>\r\n            </el-table-column>\r\n            <!-- 当openPsb为true时显示地址输入框 -->\r\n            <el-table-column :label=\"t('currentAddress')\" width=\"200\">\r\n              <template #default=\"{ row }\">\r\n                <el-input v-model=\"row.address\" class=\"apple-input table-input\" :placeholder=\"t('inputAddress')\" size=\"small\" />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column :label=\"t('gender')\" width=\"100\">\r\n              <template #default=\"{ row }\">\r\n                <el-select v-model=\"row.sex\" class=\"apple-select table-select\" size=\"small\">\r\n                  <el-option :label=\"t('male')\" value=\"1\" />\r\n                  <el-option :label=\"t('female')\" value=\"2\" />\r\n                  <el-option :label=\"t('confidential')\" value=\"3\" />\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <el-table-column :label=\"t('operation')\" fixed=\"right\" style=\"align-items: center\">\r\n              <template #default=\"{ row, $index }\">\r\n                <div class=\"operation-buttons\">\r\n                  <!-- 客人表格中的按钮 -->\r\n                  <el-button v-if=\"row.idType === IdType.IDCERT\" type=\"primary\" :icon=\"CreditCard\" plain @click=\"scanIdCard($index)\">\r\n                    {{ t('scanId') }}\r\n                  </el-button>\r\n                  <el-button v-if=\"!row.isMain && guests.length > 1\" type=\"danger\" link @click=\"removeGuest($index)\">\r\n                    {{ t('removeGuest') }}\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        <!-- 展开的详细信息区域 -->\r\n        <div v-if=\"expandedGuestIndex !== -1\" class=\"guest-detail-form\">\r\n          <div class=\"detail-header\">\r\n            <span class=\"detail-title\">{{ guests[expandedGuestIndex]?.name || `客人 ${expandedGuestIndex + 1}` }} - 详细信息</span>\r\n            <el-button size=\"small\" text @click=\"expandedGuestIndex = -1\">\r\n              <el-icon><ArrowUp /></el-icon>\r\n              收起\r\n            </el-button>\r\n          </div>\r\n\r\n          <el-row :gutter=\"16\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"form-item\">\r\n                <label class=\"form-label\">{{ t('birthday') }}</label>\r\n                <el-date-picker v-model=\"guests[expandedGuestIndex].birthday\" type=\"date\" class=\"apple-date-picker\" :placeholder=\"t('selectDate')\" size=\"small\" />\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"form-item\">\r\n                <label class=\"form-label\">{{ t('nation') }}</label>\r\n                <el-select v-model=\"guests[expandedGuestIndex].nation\" class=\"apple-select\" size=\"small\">\r\n                  <el-option v-for=\"item in nationList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n                </el-select>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"form-item\">\r\n                <label class=\"form-label\">制卡状态</label>\r\n                <el-input v-model=\"guests[expandedGuestIndex].isMadeCard\" class=\"apple-input\" size=\"small\" />\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              <div class=\"form-item\">\r\n                <label class=\"form-label\">{{ t('currentAddress') }}</label>\r\n                <el-input v-model=\"guests[expandedGuestIndex].address\" class=\"apple-input\" type=\"textarea\" :rows=\"2\" size=\"small\" />\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </div>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"onCancel\">\r\n            {{ t('cancel') }}\r\n          </el-button>\r\n          <el-button type=\"primary\" :loading=\"loading\" @click=\"onSubmit\">\r\n            {{ t('confirm') }}\r\n          </el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 排房弹窗 -->\r\n    <ArrangeRoomsDialog v-if=\"terracesVisible\" v-model=\"terracesVisible\" v-bind=\"formData\" :is-alone=\"true\" @reload=\"onReload\" />\r\n  </div>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n// 苹果风格样式\r\n.apple-style-dialog {\r\n  :deep(.el-dialog) {\r\n    border-radius: 12px;\r\n    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\r\n  }\r\n\r\n  :deep(.el-dialog__header) {\r\n    padding: 24px 24px 0;\r\n    border-bottom: none;\r\n\r\n    .el-dialog__title {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: #1d1d1f;\r\n    }\r\n  }\r\n\r\n  :deep(.el-dialog__body) {\r\n    padding: 20px 24px;\r\n  }\r\n\r\n  :deep(.el-dialog__footer) {\r\n    padding: 0 24px 24px;\r\n    border-top: none;\r\n  }\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 32px;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between; /* 确保标题和按钮分别在左右两侧 */\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.section-title {\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.section-icon {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n}\r\n\r\n.section-icon.room-icon {\r\n  color: #409eff;\r\n}\r\n\r\n.section-icon.guest-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.action-buttons {\r\n  margin-left: auto;\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n}\r\n\r\n.room-info-card {\r\n  background: #f8f9fa;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n.apple-table {\r\n  :deep(.el-table) {\r\n    background: transparent;\r\n    border: none;\r\n  }\r\n\r\n  :deep(.el-table__header) {\r\n    background: transparent;\r\n\r\n    th {\r\n      background: transparent;\r\n      border: none;\r\n      color: #6b7280;\r\n      font-weight: 500;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  :deep(.el-table__body) {\r\n    tr {\r\n      background: transparent;\r\n\r\n      td {\r\n        border: none;\r\n        color: #374151;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.room-number {\r\n  font-weight: 600;\r\n  color: #059669;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.guests-table-container {\r\n  background: #ffffff;\r\n  border-radius: 12px;\r\n  border: 1px solid #e5e7eb;\r\n  overflow: hidden;\r\n}\r\n\r\n.guests-table {\r\n  :deep(.el-table) {\r\n    background: transparent;\r\n    border: none;\r\n  }\r\n\r\n  :deep(.el-table__header) {\r\n    background: #f8f9fa;\r\n\r\n    th {\r\n      background: transparent;\r\n      border: none;\r\n      color: #6b7280;\r\n      font-weight: 500;\r\n      font-size: 14px;\r\n      padding: 8px 4px; // 将左右padding从8px改为4px\r\n    }\r\n  }\r\n\r\n  :deep(.el-table__body) {\r\n    tr {\r\n      background: transparent;\r\n\r\n      td {\r\n        border: none;\r\n        color: #374151;\r\n        padding: 8px 4px; // 将左右padding从8px改为4px\r\n      }\r\n\r\n      &:hover {\r\n        background: #f8f9fa;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-input {\r\n  :deep(.el-input__wrapper) {\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 6px;\r\n    box-shadow: none;\r\n\r\n    &:hover {\r\n      border-color: #d1d5db;\r\n    }\r\n\r\n    &.is-focus {\r\n      border-color: #007aff;\r\n    }\r\n  }\r\n}\r\n\r\n.table-select {\r\n  width: 100%;\r\n\r\n  :deep(.el-select__wrapper) {\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 6px;\r\n    box-shadow: none;\r\n\r\n    &:hover {\r\n      border-color: #d1d5db;\r\n    }\r\n\r\n    &.is-focus {\r\n      border-color: #007aff;\r\n    }\r\n  }\r\n}\r\n\r\n.table-actions {\r\n  display: flex;\r\n  gap: 4px;\r\n  justify-content: center;\r\n}\r\n\r\n.guest-detail-form {\r\n  margin-top: 16px;\r\n  padding: 16px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n.detail-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.detail-title {\r\n  font-weight: 600;\r\n  color: #374151;\r\n}\r\n\r\n.guest-badge {\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  font-size: 11px;\r\n  font-weight: 600;\r\n  background: #f3f4f6;\r\n  color: #6b7280;\r\n  display: inline-block;\r\n\r\n  &.primary {\r\n    background: #dbeafe;\r\n    color: #1d4ed8;\r\n  }\r\n}\r\n.operation-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n  flex-wrap: nowrap;\r\n}\r\n.guest-card {\r\n  background: #ffffff;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: #d1d5db;\r\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  &.primary-guest {\r\n    border-color: #3b82f6;\r\n    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);\r\n  }\r\n}\r\n\r\n.guest-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.guest-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.guest-badge {\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  background: #f3f4f6;\r\n  color: #6b7280;\r\n\r\n  &.primary {\r\n    background: #dbeafe;\r\n    color: #1d4ed8;\r\n  }\r\n}\r\n\r\n.guest-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n}\r\n\r\n.guest-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.guest-form {\r\n  .el-row {\r\n    margin-bottom: 16px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #374151;\r\n  margin-bottom: 6px;\r\n\r\n  &.required::after {\r\n    content: ' *';\r\n    color: #ef4444;\r\n  }\r\n}\r\n\r\n// 苹果风格输入框\r\n.apple-input {\r\n  :deep(.el-input__wrapper) {\r\n    border-radius: 8px;\r\n    border: 1px solid #d1d5db;\r\n    box-shadow: none;\r\n    transition: all 0.2s ease;\r\n\r\n    &:hover {\r\n      border-color: #9ca3af;\r\n    }\r\n\r\n    &.is-focus {\r\n      border-color: #007aff;\r\n      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.apple-select {\r\n  width: 100%;\r\n\r\n  :deep(.el-select__wrapper) {\r\n    border-radius: 8px;\r\n    border: 1px solid #d1d5db;\r\n    box-shadow: none;\r\n    transition: all 0.2s ease;\r\n\r\n    &:hover {\r\n      border-color: #9ca3af;\r\n    }\r\n\r\n    &.is-focus {\r\n      border-color: #007aff;\r\n      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.apple-date-picker {\r\n  width: 100%;\r\n\r\n  :deep(.el-input__wrapper) {\r\n    border-radius: 8px;\r\n    border: 1px solid #d1d5db;\r\n    box-shadow: none;\r\n    transition: all 0.2s ease;\r\n\r\n    &:hover {\r\n      border-color: #9ca3af;\r\n    }\r\n\r\n    &.is-focus {\r\n      border-color: #007aff;\r\n      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n// 使用与checkin页面相同的样式\r\n.flex-around {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: flex-start;\r\n}\r\n\r\n// Tailwind CSS 类名支持\r\n.w-\\[100px\\] {\r\n  width: 100px;\r\n}\r\n\r\n.w-\\[120px\\] {\r\n  width: 120px;\r\n}\r\n\r\n.w-\\[60px\\] {\r\n  width: 60px;\r\n}\r\n\r\n.text-right {\r\n  text-align: right;\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .apple-style-dialog {\r\n    :deep(.el-dialog) {\r\n      width: 95% !important;\r\n      margin: 5vh auto;\r\n    }\r\n  }\r\n\r\n  .guest-card {\r\n    padding: 16px;\r\n  }\r\n\r\n  .guest-actions {\r\n    flex-direction: column;\r\n    gap: 4px;\r\n\r\n    .apple-button {\r\n      font-size: 12px;\r\n      padding: 6px 12px;\r\n    }\r\n  }\r\n}\r\n\r\n// 添加客历信息弹窗样式\r\n:deep(.customer-history-popover) {\r\n  .customer-history-content {\r\n    .customer-info-grid {\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      gap: 8px 16px;\r\n\r\n      .info-item {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .label {\r\n          font-weight: 500;\r\n          color: #606266;\r\n          min-width: 80px;\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .value {\r\n          color: #303133;\r\n          word-break: break-all;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["byteToHex", "i", "push", "toString", "slice", "getRandomValues", "rnds8", "Uint8Array", "native", "randomUUID", "crypto", "bind", "v4", "options", "buf", "offset", "rnds", "random", "_a", "rng", "Error", "length", "arr", "toLowerCase", "unsafeStringify", "emits", "__emit", "t", "useI18n", "onMounted", "window", "CallBridge", "__RUNNING_IN_PMS_AGENT__", "<PERSON><PERSON><PERSON><PERSON>", "closeSocket", "onBeforeUnmount", "userStore", "useUserStore", "visible", "ref", "tableData", "guests", "id", "uuidv4", "address", "birthday", "idNo", "idType", "IdType", "IDCERT", "isMadeCard", "name", "nation", "phone", "sex", "SexEnum", "MALE", "is<PERSON><PERSON>", "queryParams", "reactive", "gcode", "hcode", "backQueryString", "customerTableData", "timeout", "popover<PERSON><PERSON><PERSON>", "pageNo", "pageSize", "customerHistories", "customerPopovers", "getRowKey", "guestIndex", "idTypes", "nationList", "loading", "formData", "terracesVisible", "expandedGuestIndex", "dictTypes", "DICT_TYPE_ID_TYPE", "DictTypeEnum", "DICT_TYPE_NATION", "addGuest", "value", "async", "onSubmit", "rNo", "ElMessage", "error", "trim", "index", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "map", "guest", "params", "bookNo", "batchNo", "orders", "orderNo", "bkNum", "persons", "res", "orderApi", "bookCheckIn", "code", "success", "message", "center", "onCancel", "msg", "scanIdCard", "showClose", "type", "dangerouslyUseHTMLString", "isConnected", "Promise", "resolve", "setTimeout", "initCardReader", "data", "JSON", "parse", "peopleName", "peopleIDCode", "peopleSex", "FEMALE", "getGenderFromIdCard", "SECRECY", "peopleAddress", "peopleBirthday", "peopleNation", "indexOf", "customerRes", "memberApi", "getCustomerByIdNo", "<PERSON><PERSON><PERSON>", "transformedData", "nextTick", "console", "log", "handleIdCardData", "timer", "setInterval", "readIdCard", "clearInterval", "querySearch", "queryString", "cb", "clearTimeout", "getCustomerList", "formattedData", "list", "item", "originalPhone", "originalIdNo", "saveAndMask", "Math", "automaticRoom", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "bookApi", "autoArrangeRooms", "catch", "onTerraces", "onReload", "val", "__expose", "open", "row", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "filter", "dictType", "refresh", "splice"], "mappings": "8kDACA,MAAMA,GAAY,GAClB,IAAA,IAASC,GAAI,EAAGA,GAAI,MAAOA,GACbD,GAAAE,MAAMD,GAAI,KAAOE,SAAS,IAAIC,MAAM,ICHlD,IAAIC,GACJ,MAAMC,GAAQ,IAAIC,WAAW,ICD7B,MACeC,GAAA,CAAEC,WADoB,oBAAXC,QAA0BA,OAAOD,YAAcC,OAAOD,WAAWE,KAAKD,SCGhG,SAASE,GAAGC,EAASC,EAAKC,SACtB,GAAIP,GAAOC,aAAeK,IAAQD,EAC9B,OAAOL,GAAOC,aAGlB,MAAMO,GADNH,EAAUA,GAAW,CAAE,GACFI,SAAU,OAAAC,EAAQL,EAAAM,wBFN5B,WACX,IAAKd,GAAiB,CAClB,GAAsB,oBAAXK,SAA2BA,OAAOL,gBACnC,MAAA,IAAIe,MAAM,4GAEFf,GAAAK,OAAOL,gBAAgBM,KAAKD,OACtD,CACI,OAAOL,GAAgBC,GAC3B,CEFsDa,GAC9C,GAAAH,EAAKK,OAAS,GACR,MAAA,IAAID,MAAM,qCAcpB,OAZAJ,EAAK,GAAgB,GAAVA,EAAK,GAAa,GAC7BA,EAAK,GAAgB,GAAVA,EAAK,GAAa,IHR1B,SAAyBM,EAAKP,EAAS,GAClC,OAAAf,GAAUsB,EAAIP,EAAS,IAC3Bf,GAAUsB,EAAIP,EAAS,IACvBf,GAAUsB,EAAIP,EAAS,IACvBf,GAAUsB,EAAIP,EAAS,IACvB,IACAf,GAAUsB,EAAIP,EAAS,IACvBf,GAAUsB,EAAIP,EAAS,IACvB,IACAf,GAAUsB,EAAIP,EAAS,IACvBf,GAAUsB,EAAIP,EAAS,IACvB,IACAf,GAAUsB,EAAIP,EAAS,IACvBf,GAAUsB,EAAIP,EAAS,IACvB,IACAf,GAAUsB,EAAIP,EAAS,KACvBf,GAAUsB,EAAIP,EAAS,KACvBf,GAAUsB,EAAIP,EAAS,KACvBf,GAAUsB,EAAIP,EAAS,KACvBf,GAAUsB,EAAIP,EAAS,KACvBf,GAAUsB,EAAIP,EAAS,MAAMQ,aACrC,CGFWC,CAAgBR,EAC3B,u7CCyHA,MAAMS,GAAQC,IAKRC,EAAEA,IAAMC,IAgBdC,GAAU,UACkC,IAA9BC,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,EAAWC,aAAY,IAI3BC,GAAgB,UAC4B,IAA9BL,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,EAAWC,aAAY,IAI3B,MAAME,GAAYC,IACZC,GAAUC,GAAI,GACdC,GAAYD,EAAuB,IACnCE,GAASF,EAAiB,CAC9B,CACEG,GAAIC,KACJC,QAAS,GACTC,SAAU,GACVC,KAAM,GACNC,OAAQC,EAAOC,OACfC,WAAY,GACZC,KAAM,GACNC,OAAQ,KACRC,MAAO,GACPC,IAAKC,EAAQC,KAAKrD,WAClBsD,QAAQ,KAKNC,GAAcC,EAAS,CAC3BC,MAAOxB,GAAUwB,MACjBC,MAAOzB,GAAUyB,QAIbC,GAAkBvB,IAClBwB,GAAoBxB,EAAW,IACjC,IAAAyB,GAGJ,MAAMC,GAAeN,EAAS,CAC5BO,OAAQ,EACRC,SAAU,KAINC,GAAoB7B,EAA4B,IAChD8B,GAAmB9B,EAAgC,IAGzD,SAAS+B,GAAUC,GACjB,MAAO,SAASA,GAAU,CAGtB,MAAAC,GAAUjC,EAAqB,IAC/BkC,GAAalC,EAAqB,IAClCmC,GAAUnC,GAAI,GACdoC,GAAWpC,EAAS,IACpBqC,GAAkBrC,GAAI,GACtBsC,GAAqBtC,GAAM,GAE3BuC,GAAY,CAACC,EAAmBC,EAAaC,kBAUnD,SAASC,KACPzC,GAAO0C,MAAMjF,KAAK,CAChBwC,GAAIC,KACJC,QAAS,GACTC,SAAU,GACVC,KAAM,GACNC,OAAQC,EAAOC,OACfC,WAAY,GACZC,KAAM,GACNC,OAAQ,KACRC,MAAO,GACPC,IAAKC,EAAQC,KAAKrD,WAClBsD,QAAQ,GACT,CAyCH2B,eAAeC,KACT,IA/BN,iBAEE,KAAK,OAAAnE,EAAUsB,GAAA2C,MAAM,aAAIG,KAEhB,OADGC,EAAAC,MAAM7D,GAAE,qBACX,EAIL,GAAwB,IAAxBc,GAAO0C,MAAM9D,OAER,OADGkE,EAAAC,MAAM7D,GAAE,mBACX,EAIT,IAAA,IAAS1B,EAAI,EAAGA,EAAIwC,GAAO0C,MAAM9D,OAAQpB,IAEvC,IADcwC,GAAO0C,MAAMlF,GAChBkD,KAAKsC,OAEP,OADPF,EAAUC,MAAM,GAAG7D,GAAE,aAAc,CAAE+D,MAAOzF,EAAI,SAAU0B,GAAE,iBACrD,EAQJ,OAAA,CAAA,CAKFgE,GACH,OAGF,MAAMC,EAAanD,GAAO0C,MAAMU,KAAI,CAACC,EAAOJ,KAAW,CACrDvC,KAAM2C,EAAM3C,KACZG,IAAKwC,EAAMxC,IACXF,OAAQ0C,EAAM1C,OACdR,QAASkD,EAAMlD,QACfS,MAAOyC,EAAMzC,MACbN,OAAQ+C,EAAM/C,OACdD,KAAMgD,EAAMhD,KACZW,OAAkB,IAAViC,EAAc,IAAM,IAC5BxC,WAAY,OAGR6C,EAAS,CACbnC,MAAOxB,GAAUwB,MACjBC,MAAOzB,GAAUyB,MACjBmC,OAAQrB,GAASQ,MAAMa,OACvBC,QAAS,GACTC,OAAQ,CACN,CACEC,QAASxB,GAASQ,MAAMgB,QACxBC,MAAOzB,GAASQ,MAAMiB,MACtBC,QAAST,KAKflB,GAAQS,OAAQ,EACZ,IACF,MAAMmB,QAAYC,EAASC,YAAYT,GACtB,IAAbO,EAAIG,MACNlB,EAAUmB,QAAQ,CAChBC,QAAShF,GAAE,kBACXiF,QAAQ,IAEVnF,GAAM,WACGoF,MAECtB,EAAAC,MAAMc,EAAIQ,WAEftB,GACGD,EAAAC,MAAM7D,GAAE,iBAAgB,CAClC,QACA+C,GAAQS,OAAQ,CAAA,CAClB,CAIF,SAAS0B,KACPvE,GAAQ6C,OAAQ,EAChB1C,GAAO0C,MAAQ,CACb,CACEzC,GAAIC,KACJC,QAAS,GACTC,SAAU,GACVC,KAAM,GACNC,OAAQC,EAAOC,OACfC,WAAY,GACZC,KAAM,GACNC,OAAQ,KACRC,MAAO,GACPC,IAAKC,EAAQC,KAAKrD,WAClBsD,QAAQ,IAGZhC,GAAM,UAAS,CAIjB2D,eAAe2B,GAAWxC,GAEpB,GADU9B,GAAO0C,MAAMZ,GACjBxB,SAAWC,EAAOC,OAAQ,CAClC,QAA0C,IAA9BnB,OAAeC,iBAAkF,IAA5CD,OAAeE,yBAA0C,CAQxH,YANUuD,EAAA,CACRyB,WAAW,EACXL,QAAS,0BAHU,+IAInBM,KAAM,UACNC,0BAA0B,GAE5B,CAGEjF,EAAWkF,cACblF,EAAWC,oBACL,IAAIkF,SAASC,GAAYC,WAAWD,EAAS,QAG1CpF,EAAAsF,gBAAgBZ,KAchBvB,eAAiBuB,EAAiBpC,GAC3C,IACI,MAAAiD,EAAOC,KAAKC,MAAMf,GAClBb,EAAQrD,GAAO0C,MAAMZ,GAkB3B,GAjBAuB,EAAM3C,KAAOqE,EAAKG,WAClB7B,EAAMhD,KAAO0E,EAAKI,aAGdJ,EAAKK,UACP/B,EAAMxC,IAAyB,MAAnBkE,EAAKK,UAAoBtE,EAAQC,KAAOD,EAAQuE,OACnDN,EAAKI,aACR9B,EAAAxC,IAAMyE,EAAoBP,EAAKI,cAErC9B,EAAMxC,IAAMC,EAAQyE,QAGtBlC,EAAMlD,QAAU4E,EAAKS,cACrBnC,EAAMjD,SAAW2E,EAAKU,eAChBpC,EAAA1C,OAASoE,EAAKW,aAAaC,QAAQ,KAAOZ,EAAKW,aAAe,GAAGX,EAAKW,gBAGxEX,EAAKI,aACH,IACI,MAAAS,QAAoBC,EAAUC,kBAAkB,CACpD3E,MAAOxB,GAAUwB,MACjBd,KAAM0E,EAAKI,eAEb,GAAyB,IAArBS,EAAY5B,MAAc4B,EAAYb,KAAM,CAExC,MAAAgB,EAASlE,GAAUC,GAEnBkE,EAAkB,IACnBJ,EAAYb,KACflE,IAA8B,MAAzB+E,EAAYb,KAAKlE,IAAc,IAA+B,MAAzB+E,EAAYb,KAAKlE,IAAc,IAAM,MAI/Dc,GAAAe,MAAMqD,GAAUC,QAG5BC,IACWrE,GAAAc,MAAMqD,IAAU,EAG3B1C,EAAAzC,MAAQgF,EAAYb,KAAKnE,OAAS,EAAA,MAEhCsF,QAAAC,IAAI,cAAeP,SAEtB7C,GACCmD,QAAAC,IAAI,UAAWpD,EAAK,QAGzBA,GACCmD,QAAAnD,MAAM,yBAA0BA,EAAK,CAC/C,CAnEIqD,CAAiBlC,EAASpC,EAAU,IAGhC,MAAAuE,EAAQC,aAAY,KACpB9G,EAAWkF,cACblF,EAAW+G,aACXC,cAAcH,GAAK,GAEpB,IAAG,CACR,CAgEO,SAAAI,GAAYC,EAAqBC,GACxC,IAAKD,EACI,OAAAC,EAAG,IAGZC,aAAarF,IAGTmF,IAAgBrF,GAAgBqB,MAClCnB,GAAUsD,YAAWlC,UACf,IACF,MAAMoC,KAAEA,SAAec,EAAUgB,gBAAgB,IAC5C5F,MACAO,GACHd,KAAMgG,IAERrF,GAAgBqB,MAAQgE,EAClB,MAGAI,GAHW/B,EAAKgC,MAAQ,IAGC3D,KAAK4D,IAAe,IAC9CA,EACHtE,MAAOsE,EAAKtG,KAEZuG,cAAeD,EAAKpG,MACpBsG,aAAcF,EAAK3G,KAEnBO,MAAOoG,EAAKpG,MAAQuG,EAAYH,EAAKpG,MAAO,SAAW,GACvDP,KAAM2G,EAAK3G,KAAO8G,EAAYH,EAAK3G,KAAM,QAAU,OAGrDiB,GAAkBoB,MAAQoE,EAC1BH,EAAGG,SACI/D,GACCmD,QAAAnD,MAAM,YAAaA,GAC3B4D,EAAG,GAAE,IAEN,KAAOS,KAAK5I,UAEfmI,EAAGrF,GAAkBoB,MACvB,CAgBF,SAAS2E,KACMC,EAAAC,QAAQrI,GAAE,sBAAuB,CAC5CsI,kBAAmBtI,GAAE,WACrBuI,iBAAkBvI,GAAE,UACpBsF,KAAM,YAELkD,MAAK,KACJC,EACGC,iBAAiB,CAChBzG,MAAOxB,GAAUwB,MACjBC,MAAOzB,GAAUyB,MACjBmC,OAAQrB,GAASQ,MAAMa,OACvBC,QAAStB,GAASQ,MAAMc,QACxBE,QAASxB,GAASQ,MAAMgB,UAEzBgE,MAAM7D,IACY,IAAbA,EAAIG,OACIlB,EAAAmB,QAAQ/E,GAAE,mBACpBa,GAAU2C,MAAM,GAAGG,IAAMgB,EAAIkB,KAAK,GAAGlC,IAAA,GAExC,IAEJgF,OAAM,QAAQ,CAInB,SAASC,KACP3F,GAAgBO,OAAQ,CAAA,CAI1B,SAASqF,GAASC,GAChBjI,GAAU2C,MAAM,GAAGG,IAAMmF,EAAI,EAAC,QAcnBC,GAAA,CACXC,KARF,SAAcC,GAlUZC,EAAYC,iBAAiBhG,IAAWqF,MAAM7D,IACpC9B,GAAAW,MAAQmB,EAAIkB,KAAKuD,QAAQtB,GAAcA,EAAKuB,WAAajG,IACtDN,GAAAU,MAAQmB,EAAIkB,KAAKuD,QAAQtB,GAAcA,EAAKuB,WAAahG,EAAaC,kBAAgB,IAkUnGzC,GAAU2C,MAAQyF,EAAIpD,KACtB7C,GAASQ,MAAQyF,EACjBtI,GAAQ6C,OAAQ,CAAA,EAKhB8F,QAbF,SAAiBL,GACfpI,GAAU2C,MAAQyF,CAAA,skEA9CX,SAAanB,EAA2BlF,GACzC,MAAAuB,EAAQrD,GAAO0C,MAAMZ,GAC3BuB,EAAM3C,KAAOsG,EAAKtG,KAEZ2C,EAAAzC,MAAQoG,EAAKC,eAAiBD,EAAKpG,MACnCyC,EAAAhD,KAAO2G,EAAKE,cAAgBF,EAAK3G,KACjCgD,EAAA/C,OAAS0G,EAAK1G,QAAUC,EAAOC,MAAA,kgIA/PlByC,SACfjD,GAAO0C,MAAM9D,OAAS,GACjBoB,GAAA0C,MAAM+F,OAAOxF,EAAO,IAF/B,IAAqBA", "x_google_ignoreList": [0, 1, 2, 3]}