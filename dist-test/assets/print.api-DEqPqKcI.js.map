{"version": 3, "file": "print.api-DEqPqKcI.js", "sources": ["../../src/api/modules/system/print/print.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/system/print-layout'\r\n\r\n/**\r\n * 打印格式接口\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 批量更新打印格式\r\n   * @param data\r\n   */\r\n  updatePrintLayout: (data: any) => {\r\n    return api.put(`${BASE_PATH}/update`, data, {})\r\n  },\r\n\r\n  /**\r\n   * 获得打印格式\r\n   * @param data\r\n   */\r\n  getPrintLayout: (data: { gcode: string, hcode: string, billCode: string }) => {\r\n    return api.get(`${BASE_PATH}/get`, { params: data })\r\n  },\r\n\r\n  /**\r\n   * 获得打印格式列表\r\n   * @param data\r\n   */\r\n  getPrintLayoutList: (data: { gcode: string, hcode: string, billCode?: string }) => {\r\n    return api.get(`${BASE_PATH}/list`, { params: data })\r\n  },\r\n}\r\n"], "names": ["BASE_PATH", "printApi", "updatePrintLayout", "data", "api", "put", "getPrintLayout", "get", "params", "getPrintLayoutList"], "mappings": "wCAEA,MAAMA,EAAY,gCAKHC,EAAA,CAMbC,kBAAoBC,GACXC,EAAIC,IAAI,GAAGL,WAAoBG,EAAM,IAO9CG,eAAiBH,GACRC,EAAIG,IAAI,GAAGP,QAAiB,CAAEQ,OAAQL,IAO/CM,mBAAqBN,GACZC,EAAIG,IAAI,GAAGP,SAAkB,CAAEQ,OAAQL"}