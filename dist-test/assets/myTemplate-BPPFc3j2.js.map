{"version": 3, "file": "myTemplate-BPPFc3j2.js", "sources": ["../../src/views/marketing/sms/template/myTemplate.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport FormMode from './components/FormMode/index.vue'\r\nimport type { SmsTemplateModel } from '@/models/index'\r\nimport { merchantApi, smsTemplateApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport moban from '@/api/modules/moban.api'\r\n\r\ndefineOptions({\r\n  name: 'MarketingMyTemplateList',\r\n})\r\nconst userStore = useUserStore()\r\nconst router = useRouter()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\nconst tabbar = useTabbar()\r\nconst settingsStore = useSettingsStore()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n  /**\r\n   * 详情展示模式\r\n   * router 路由跳转\r\n   * dialog 对话框\r\n   * drawer 抽屉\r\n   */\r\n  formMode: 'dialog' as 'router' | 'dialog' | 'drawer',\r\n  // 详情\r\n  formModeProps: {\r\n    visible: false,\r\n    templateCode: '',\r\n  },\r\n  // 搜索\r\n  search: {\r\n    /** 模板类型 -1:全部 0：普通模板 1：营销模板 */\r\n    templateType: '-1',\r\n    /** 状态 -1:全部 0:待审核 1:启用 2：停用 3:未通过 */\r\n    state: '1',\r\n    hcode: '',\r\n  },\r\n  // 批量操作\r\n  batch: {\r\n    enable: false,\r\n    selectionDataList: [],\r\n  },\r\n  // 列表数据\r\n  dataList: [] as SmsTemplateModel[],\r\n})\r\n\r\nconst merchants = ref<{ hcode: string, hname: string }[]>([])\r\n\r\nonMounted(() => {\r\n  // getMercharnts()\r\n  // getDataList()\r\n  getTemplateList()\r\n})\r\n\r\nfunction getTemplateList() {\r\n  data.value.loading = true\r\n  const prms = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  moban.list(prms).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data.list\r\n  })\r\n}\r\n\r\nfunction getMercharnts() {\r\n  merchantApi.list({ gcode: userStore.gcode }).then((res: any) => {\r\n    merchants.value = res.data\r\n  })\r\n}\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    ...(data.value.search.templateType !== '-1' && { templateType: data.value.search.templateType }),\r\n    ...(data.value.search.hcode && { hcode: data.value.search.hcode }),\r\n  }\r\n  smsTemplateApi.list(params).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data.list\r\n    pagination.value.total = res.data.total\r\n  })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getTemplateList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string, order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nfunction onCreate() {\r\n  data.value.formModeProps.templateCode = ''\r\n  data.value.formModeProps.visible = true\r\n}\r\n\r\nfunction onEdit(row: any) {\r\n  data.value.formModeProps.templateCode = row.id\r\n  data.value.formModeProps.visible = true\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <search-bar :show-toggle=\"false\">\r\n        <template #default=\"{ fold }\">\r\n          <el-form :model=\"data.search\" size=\"default\" label-width=\"80px\" inline-message inline class=\"search-form\">\r\n            <el-form-item label=\"类型\">\r\n              <el-radio-group v-model=\"data.search.templateType\" @change=\"currentChange()\">\r\n                <el-radio-button value=\"-1\">\r\n                  全部\r\n                </el-radio-button>\r\n                <el-radio-button value=\"0\">\r\n                  普通模板\r\n                </el-radio-button>\r\n                <el-radio-button value=\"1\">\r\n                  营销模板\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"门店\">\r\n              <el-select v-model=\"data.search.hcode\" placeholder=\"请选择门店\" @change=\"currentChange()\">\r\n                <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.hname\" :value=\"item.hcode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item style=\"float: right;\">\r\n              <el-button type=\"primary\" @click=\"onCreate()\">\r\n                <template #icon>\r\n                  <svg-icon name=\"ep:plus\" />\r\n                </template>\r\n                短信模板申请\r\n              </el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </template>\r\n      </search-bar>\r\n      <el-table\r\n        v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" highlight-current-row stripe border\r\n        height=\"100%\" @sort-change=\"sortChange\" @selection-change=\"data.batch.selectionDataList = $event\"\r\n      >\r\n        <el-table-column v-if=\"data.batch.enable\" type=\"selection\" align=\"center\" fixed />\r\n        <!--        <el-table-column label=\"模板类型\" width=\"150\"> -->\r\n        <!--          <template #default=\"scope\"> -->\r\n        <!--            <span v-if=\"scope.row.templateType === '0'\">普通模板</span> -->\r\n        <!--            <span v-else>营销模板</span> -->\r\n        <!--          </template> -->\r\n        <!--        </el-table-column> -->\r\n        <el-table-column prop=\"tplName\" label=\"模板名称\" width=\"150\" />\r\n        <el-table-column prop=\"tplContent\" label=\"模板内容\" />\r\n        <el-table-column label=\"是否集团模板\" width=\"150\">\r\n          <template #default=\"scope\">\r\n            <span v-if=\"scope.row.isGroup === true\">是</span>\r\n            <span v-else>否</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <span v-if=\"scope.row.enabled === true\">是</span>\r\n            <span v-else>否</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"申请时间/门店\" width=\"180\">\r\n          <template #default=\"scope\">\r\n            <span>{{ scope.row.updateTime }}</span>\r\n            <br>\r\n            <span>{{ scope.row.hname }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"100\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-link type=\"primary\" @click=\"onEdit(scope.row)\">\r\n              编辑\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > pagination.pageSize\"\r\n        :current-page=\"pagination.pageNo\" :total=\"pagination.total\" :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\" :layout=\"pagination.layout\" :hide-on-single-page=\"false\" class=\"pagination\"\r\n        background @size-change=\"sizeChange\" @current-change=\"currentChange\"\r\n      />\r\n    </page-main>\r\n    <FormMode\r\n      v-if=\"data.formMode === 'dialog' || data.formMode === 'drawer'\"\r\n      v-model=\"data.formModeProps.visible\" :template-code=\"data.formModeProps.templateCode\" :mode=\"data.formMode\"\r\n      @success=\"getDataList\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "useRouter", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "useTabbar", "useSettingsStore", "data", "ref", "loading", "tableAutoHeight", "formMode", "formModeProps", "visible", "templateCode", "search", "templateType", "state", "hcode", "batch", "enable", "selectionDataList", "dataList", "merchants", "getTemplateList", "value", "prms", "gcode", "moban", "list", "then", "res", "getDataList", "params", "smsTemplateApi", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "onMounted", "row", "id"], "mappings": "q+CAWA,MAAMA,EAAYC,IACOC,IACzB,MAAMC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IACtDC,IACcC,IAEvC,MAAMC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAOjBC,SAAU,SAEVC,cAAe,CACbC,SAAS,EACTC,aAAc,IAGhBC,OAAQ,CAENC,aAAc,KAEdC,MAAO,IACPC,MAAO,IAGTC,MAAO,CACLC,QAAQ,EACRC,kBAAmB,IAGrBC,SAAU,KAGNC,EAAYf,EAAwC,IAQ1D,SAASgB,IACPjB,EAAKkB,MAAMhB,SAAU,EACrB,MAAMiB,EAAO,CACXC,MAAO/B,EAAU+B,MACjBT,MAAOtB,EAAUsB,OAEnBU,EAAMC,KAAKH,GAAMI,MAAMC,IACrBxB,EAAKkB,MAAMhB,SAAU,EAChBF,EAAAkB,MAAMH,SAAWS,EAAIxB,KAAKsB,IAAA,GAChC,CASH,SAASG,IACPzB,EAAKkB,MAAMhB,SAAU,EACrB,MAAMwB,EAAS,IACVjC,IACH2B,MAAO/B,EAAU+B,MACjBT,MAAOtB,EAAUsB,SACsB,OAAnCX,EAAKkB,MAAMV,OAAOC,cAAyB,CAAEA,aAAcT,EAAKkB,MAAMV,OAAOC,iBAC7ET,EAAKkB,MAAMV,OAAOG,OAAS,CAAEA,MAAOX,EAAKkB,MAAMV,OAAOG,QAE5DgB,EAAeL,KAAKI,GAAQH,MAAMC,IAChCxB,EAAKkB,MAAMhB,SAAU,EAChBF,EAAAkB,MAAMH,SAAWS,EAAIxB,KAAKsB,KACpB9B,EAAA0B,MAAMU,MAAQJ,EAAIxB,KAAK4B,KAAA,GACnC,CAIH,SAASC,GAAWC,GAClBpC,EAAaoC,GAAMP,MAAK,IAAME,KAAa,CAIpC,SAAAM,GAAcC,EAAO,GAC5BrC,EAAgBqC,GAAMT,MAAK,IAAMN,KAAiB,CAIpD,SAASgB,IAAWC,KAAEA,EAAMC,MAAAA,IAC1BvC,EAAasC,EAAMC,GAAOZ,MAAK,IAAME,KAAa,QApDpDW,GAAU,KAGQnB,GAAA,yrCAqDXjB,EAAAkB,MAAMb,cAAcE,aAAe,QACnCP,EAAAkB,MAAMb,cAAcC,SAAU,2gCAGrB+B,QACTrC,EAAAkB,MAAMb,cAAcE,aAAe8B,EAAIC,QACvCtC,EAAAkB,MAAMb,cAAcC,SAAU,GAFrC,IAAgB+B"}