import{a as e}from"./index-CkEhI1Zk.js";const t="admin-api/pms",a={getOtaServiceIntegration:a=>e.get(`${t}/service-integration/ota-list`,{params:a}),getPayServiceIntegration:a=>e.get(`${t}/service-integration/pay-list`,{params:a}),getInvoiceServiceIntegration:a=>e.get(`${t}/service-integration/invoice`,{params:a}),getPsbServiceIntegration:a=>e.get(`${t}/service-integration/psb`,{params:a}),getServiceIntegrationList:a=>e.get(`${t}/service-integration/list`,{params:a}),getSimpleList:a=>e.get(`${t}/room-type/simple-list`,{params:a}),getRoomTypeRefList:t=>e.get("admin-api/ota-sync/room-type-ref/list",{params:t}),getHotelRefList:t=>e.get("admin-api/ota-sync/hotel-ref/list",{params:t}),putRoomTypeRefAssociation:t=>e.put("admin-api/ota-sync/room-type-ref/association",t),getRoomTypeRef:t=>e.get("admin-api/ota-sync/room-type-ref/get",{params:t}),getOtaRoomTypeList:t=>e.get("admin-api/ota-sync/room-type-ref/ota-rt-list",{params:t}),deleteRoomTypeRef:t=>e.post("admin-api/ota-sync/room-type-ref/delete",t)};export{a as o};
//# sourceMappingURL=serviceintegration.api-ByMiQtUi.js.map
