{"version": 3, "file": "serviceintegration.api-ByMiQtUi.js", "sources": ["../../src/api/modules/pms/serviceintegration/serviceintegration.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms'\r\n/**\r\n * 酒店开通服务接口\r\n */\r\nexport default {\r\n  /**\r\n   * 获得OTA对接服务列表\r\n   * @param data\r\n   */\r\n  getOtaServiceIntegration: (data: { gcode: string; hcode: string }) => api.get(`${BASE_PATH}/service-integration/ota-list`, { params: data }),\r\n  /**\r\n   * 获得支付对接服务列表\r\n   * @param data\r\n   */\r\n  getPayServiceIntegration: (data: any) => api.get(`${BASE_PATH}/service-integration/pay-list`, { params: data }),\r\n\r\n  /**\r\n   * 获得数电发票服务\r\n   * @param data\r\n   */\r\n  getInvoiceServiceIntegration: (data: any) => api.get(`${BASE_PATH}/service-integration/invoice`, { params: data }),\r\n\r\n  /**\r\n   * 获得PSB服务\r\n   * @param data\r\n   */\r\n  getPsbServiceIntegration: (data: any) => api.get(`${BASE_PATH}/service-integration/psb`, { params: data }),\r\n\r\n  /**\r\n   * 获得门店服务列表\r\n   * @param data\r\n   */\r\n  getServiceIntegrationList: (data: { gcode: string; hcode: string }) => api.get(`${BASE_PATH}/service-integration/list`, { params: data }),\r\n\r\n  /**\r\n   * 获得房型简单列表\r\n   * @param data\r\n   */\r\n  getSimpleList: (data: any) => api.get(`${BASE_PATH}/room-type/simple-list`, { params: data }),\r\n  /**\r\n   * 获得OTA房型关联表;ota房型可以关联多个酒店房型\r\n   * @param data\r\n   */\r\n  getRoomTypeRefList: (data: any) => api.get(`admin-api/ota-sync/room-type-ref/list`, { params: data }),\r\n\r\n  /**\r\n   * 获得OTA酒店关联列表(门店绑定)\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getHotelRefList: (data: any) => api.get(`admin-api/ota-sync/hotel-ref/list`, { params: data }),\r\n\r\n  /**\r\n   * 关联OTA房型,ota房型可以关联多个酒店房型\r\n   * @param data\r\n   * @returns\r\n   */\r\n  putRoomTypeRefAssociation: (data: any) => api.put(`admin-api/ota-sync/room-type-ref/association`, data),\r\n  /**\r\n   * 获得OTA房型关联关系\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getRoomTypeRef: (data: any) => api.get(`admin-api/ota-sync/room-type-ref/get`, { params: data }),\r\n\r\n  /**\r\n   * 获取远程OTA房型列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getOtaRoomTypeList: (data: any) => api.get(`admin-api/ota-sync/room-type-ref/ota-rt-list`, { params: data }),\r\n\r\n  /**\r\n   * 删除关联关系\r\n   * @param data\r\n   * @returns\r\n   */\r\n  deleteRoomTypeRef: (data: any) => api.post(`admin-api/ota-sync/room-type-ref/delete`, data),\r\n}\r\n"], "names": ["BASE_PATH", "otaApi", "getOtaServiceIntegration", "data", "api", "get", "params", "getPayServiceIntegration", "getInvoiceServiceIntegration", "getPsbServiceIntegration", "getServiceIntegrationList", "getSimpleList", "getRoomTypeRefList", "getHotelRefList", "putRoomTypeRefAssociation", "put", "getRoomTypeRef", "getOtaRoomTypeList", "deleteRoomTypeRef", "post"], "mappings": "wCAEA,MAAMA,EAAY,gBAIHC,EAAA,CAKbC,yBAA2BC,GAA2CC,EAAIC,IAAI,GAAGL,iCAA0C,CAAEM,OAAQH,IAKrII,yBAA2BJ,GAAcC,EAAIC,IAAI,GAAGL,iCAA0C,CAAEM,OAAQH,IAMxGK,6BAA+BL,GAAcC,EAAIC,IAAI,GAAGL,gCAAyC,CAAEM,OAAQH,IAM3GM,yBAA2BN,GAAcC,EAAIC,IAAI,GAAGL,4BAAqC,CAAEM,OAAQH,IAMnGO,0BAA4BP,GAA2CC,EAAIC,IAAI,GAAGL,6BAAsC,CAAEM,OAAQH,IAMlIQ,cAAgBR,GAAcC,EAAIC,IAAI,GAAGL,0BAAmC,CAAEM,OAAQH,IAKtFS,mBAAqBT,GAAcC,EAAIC,IAAI,wCAAyC,CAAEC,OAAQH,IAO9FU,gBAAkBV,GAAcC,EAAIC,IAAI,oCAAqC,CAAEC,OAAQH,IAOvFW,0BAA4BX,GAAcC,EAAIW,IAAI,+CAAgDZ,GAMlGa,eAAiBb,GAAcC,EAAIC,IAAI,uCAAwC,CAAEC,OAAQH,IAOzFc,mBAAqBd,GAAcC,EAAIC,IAAI,+CAAgD,CAAEC,OAAQH,IAOrGe,kBAAoBf,GAAcC,EAAIe,KAAK,0CAA2ChB"}