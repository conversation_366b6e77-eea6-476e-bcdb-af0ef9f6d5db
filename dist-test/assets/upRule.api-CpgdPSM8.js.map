{"version": 3, "file": "upRule.api-CpgdPSM8.js", "sources": ["../../src/api/modules/member/rule/upRule.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/member/up-rule'\r\n/**\r\n * 升级规则接口\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 创建会员升级规则\r\n   * @param data\r\n   */\r\n  createUpRule: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n  /**\r\n   * 获得积分规则配置\r\n   * @param data\r\n   */\r\n  getUpRule: (data: { gcode: string, ruleCode: string }) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 更新会员升级规则\r\n   * @param data\r\n   */\r\n  updateUpRule: (data: any) => api.put(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 更新会员升级规则状态\r\n   * @param data\r\n   */\r\n  updateUpRuleStatus: (data: { id: number, isEnable: string }) => api.put(`${BASE_PATH}/update-status`, data, {}),\r\n\r\n  /**\r\n   * 获得会员升级规则列表\r\n   * @param gcode\r\n   */\r\n  getUpRuleList: (gcode: string) => api.get(`${BASE_PATH}/list`, {\r\n    params: {\r\n      gcode,\r\n    },\r\n  }),\r\n  /**\r\n   * 获得会员升级规则列表\r\n   * @param gcode\r\n   */\r\n  simpleList: (data: any) => api.get(`${BASE_PATH}/simple-list`, {\r\n    params: data,\r\n  }),\r\n}\r\n"], "names": ["BASE_PATH", "upRuleApi", "createUpRule", "data", "api", "post", "getUpRule", "get", "params", "updateUpRule", "put", "updateUpRuleStatus", "getUpRuleList", "gcode", "simpleList"], "mappings": "wCAEA,MAAMA,EAAY,2BAIHC,EAAA,CAMbC,aAAeC,GAAcC,EAAIC,KAAK,GAAGL,WAAoBG,EAAM,IAKnEG,UAAYH,GACVC,EAAIG,IAAI,GAAGP,QAAiB,CAC1BQ,OAAQL,IAOZM,aAAeN,GAAcC,EAAIM,IAAI,GAAGV,WAAoBG,EAAM,IAMlEQ,mBAAqBR,GAA2CC,EAAIM,IAAI,GAAGV,kBAA2BG,EAAM,IAM5GS,cAAgBC,GAAkBT,EAAIG,IAAI,GAAGP,SAAkB,CAC7DQ,OAAQ,CACNK,WAOJC,WAAaX,GAAcC,EAAIG,IAAI,GAAGP,gBAAyB,CAC7DQ,OAAQL"}