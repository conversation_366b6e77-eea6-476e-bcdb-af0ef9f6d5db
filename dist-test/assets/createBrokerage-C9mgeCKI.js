import{d as e,ai as t,aj as a,b as s,y as l,aR as o,aq as i,u as r,o as n,c as m,f as d,w as u,h as b,Y as c,F as p,ag as g,e as y,i as f,g as h,R as v,aD as k,E as _,l as T,m as C,b1 as V,b2 as R,aS as S,j as N,k as x,s as j,b5 as U,x as w,aT as L}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 *//* empty css                   */import{b as E}from"./brokerageStrategy.api-C0DFg5AR.js";import{c as P}from"./channel.api-CM6FWEgD.js";import{r as A}from"./rt.api-5a8-At7-.js";import{d as D}from"./dictData.api-DUabpYqy.js";import{b as G}from"./constants-Cg3j_uH4.js";import{_ as I}from"./_plugin-vue_export-helper-BCo6x5W8.js";const B={style:{display:"flex"}},H={key:1,style:{"margin-left":"5px"}},z={key:3,style:{"margin-left":"5px"}},q=e({__name:"createBrokerage",props:{strategyCode:{},handle:{},isEdit:{type:Boolean}},setup(e,{expose:I}){const q=t(),{t:M}=a(),O=s(!1),F=s(),Y=s({gcode:q.gcode,hcode:q.hcode,isG:"0",hotels:[q.hcode],strategyCode:"",strategyName:"",companyType:"0",isGrt:"0",brokerageLevelCode:"",rts:[],brokerageType:"0",brokerageValue:.5,channels:[],isEnable:"1",remark:""}),X=s("1"),$=s({strategyName:[{required:!0,message:M("enterStrategyName"),trigger:"blur"}]});l((()=>{!function(){const e={gcode:q.gcode,hcode:q.hcode,isEnable:1};P.getChannelSimpleList(e).then((e=>{0===e.code&&(Q.value=e.data)}))}(),function(){const e={gcode:q.gcode,hcode:q.hcode,isVirtual:"0",isEnable:"1"};A.getRoomTypeSimpleList(e).then((e=>{0===e.code&&(W.value=e.data)}))}(),D.getDictDataBatch(J).then((e=>{K.value=e.data.filter((e=>e.dictType===G))}))}));const J=[G],K=s([]);const Q=s([]);const W=s([]);function Z(){Y.value.brokerageValue=0}function ee(){Y.value.rts=[];const e={gcode:q.gcode,isVirtual:"0",isEnable:"1"};if("1"===Y.value.isGrt)A.getRoomTypeSimpleList(e).then((e=>{0===e.code&&(W.value=e.data)}));else{const t={...e,hcode:q.hcode};A.getRoomTypeSimpleList(t).then((e=>{0===e.code&&(W.value=e.data)}))}}return I({submit:()=>new Promise((e=>{F.value&&F.value.validate((t=>{t&&E.createBrokerageStrategy(Y.value).then((t=>{0===t.code?(o.success({message:M("addSuccess"),center:!0}),e()):o.error({message:t.msg||M("unknownError"),center:!0})}))}))}))}),(e,t)=>{const a=k,s=_,l=T,o=C,E=V,P=R,A=S,D=N,G=x,I=j,J=U,te=w,ae=L;return i((n(),m("div",null,[d(te,{ref_key:"formRef",ref:F,model:r(Y),rules:r($),"label-width":"180px","label-suffix":"："},{default:u((()=>[d(a,{"content-position":"left"},{default:u((()=>[b(c(r(M)("strategyInformation")),1)])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:24},{default:u((()=>[d(o,{label:r(M)("companyType")},{default:u((()=>[d(l,{modelValue:r(Y).companyType,"onUpdate:modelValue":t[0]||(t[0]=e=>r(Y).companyType=e)},{default:u((()=>[d(s,{value:"0",size:"large"},{default:u((()=>[b(c(r(M)("intermediary")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:12},{default:u((()=>[d(o,{label:r(M)("strategyName"),prop:"strategyName"},{default:u((()=>[d(A,{modelValue:r(Y).strategyName,"onUpdate:modelValue":t[1]||(t[1]=e=>r(Y).strategyName=e),placeholder:r(M)("strategyName"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),d(E,{md:12},{default:u((()=>[d(o,{label:r(M)("brokerageLevel")},{default:u((()=>[d(G,{modelValue:r(Y).brokerageLevelCode,"onUpdate:modelValue":t[2]||(t[2]=e=>r(Y).brokerageLevelCode=e),"collapse-tags":"","collapse-tags-tooltip":"",placeholder:r(M)("selectLevel"),style:{width:"300px"}},{default:u((()=>[(n(!0),m(p,null,g(r(K),(e=>(n(),y(D,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),d(a,{"content-position":"left"},{default:u((()=>[b(c(r(M)("strategyRules")),1)])),_:1}),d(P,{gutter:20},{default:u((()=>[d(E,{md:20},{default:u((()=>[d(o,{label:r(M)("commissionType")},{default:u((()=>[d(l,{modelValue:r(X),"onUpdate:modelValue":t[3]||(t[3]=e=>f(X)?X.value=e:null)},{default:u((()=>[d(s,{value:"1",size:"large"},{default:u((()=>[b(c(r(M)("byNight")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),d(o,null,{default:u((()=>[h("div",B,[d(G,{modelValue:r(Y).brokerageType,"onUpdate:modelValue":t[4]||(t[4]=e=>r(Y).brokerageType=e),style:{width:"230px"},placeholder:r(M)("commissionTypePlaceholder"),onChange:Z},{default:u((()=>[d(D,{label:r(M)("fixedNightCommission"),value:"0"},null,8,["label"]),d(D,{label:r(M)("percentageCommission"),value:"1"},null,8,["label"])])),_:1},8,["modelValue","placeholder"]),"0"===r(Y).brokerageType?(n(),y(I,{key:0,modelValue:r(Y).brokerageValue,"onUpdate:modelValue":t[5]||(t[5]=e=>r(Y).brokerageValue=e),max:1e6,min:0,"controls-position":"right"},null,8,["modelValue"])):v("",!0),"0"===r(Y).brokerageType?(n(),m("span",H,c(r(M)("fixedNightCommissionUnit")),1)):v("",!0),"1"===r(Y).brokerageType?(n(),y(I,{key:2,modelValue:r(Y).brokerageValue,"onUpdate:modelValue":t[6]||(t[6]=e=>r(Y).brokerageValue=e),max:1,min:0,step:.01,precision:2,"controls-position":"right"},null,8,["modelValue"])):v("",!0),"1"===r(Y).brokerageType?(n(),m("span",z,c(r(M)("percentageCommissionUnit")),1)):v("",!0)])])),_:1})])),_:1}),d(a,{"content-position":"left"},{default:u((()=>[b(c(r(M)("strategyApplicationScope")),1)])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:12},{default:u((()=>[d(o,{label:r(M)("appliedChannels")},{default:u((()=>[d(G,{modelValue:r(Y).channels,"onUpdate:modelValue":t[7]||(t[7]=e=>r(Y).channels=e),multiple:"","collapse-tags-tooltip":"",clearable:"",placeholder:r(M)("selectChannels"),style:{width:"240px"}},{default:u((()=>[(n(!0),m(p,null,g(r(Q),(e=>(n(),y(D,{key:e.channelCode,label:e.channelName,value:e.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:24},{default:u((()=>[d(o,{label:r(M)("appliedRoomTypes")},{default:u((()=>[d(l,{modelValue:r(Y).isGrt,"onUpdate:modelValue":t[8]||(t[8]=e=>r(Y).isGrt=e),onChange:ee},{default:u((()=>[d(s,{value:"0",size:"large"},{default:u((()=>[b(c(r(M)("hotelRoomTypes")),1)])),_:1}),d(s,{value:"1",size:"large"},{default:u((()=>[b(c(r(M)("groupRoomTypes")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:24},{default:u((()=>[d(o,null,{default:u((()=>[d(G,{modelValue:r(Y).rts,"onUpdate:modelValue":t[9]||(t[9]=e=>r(Y).rts=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",placeholder:r(M)("selectRoomTypes")},{default:u((()=>[(n(!0),m(p,null,g(r(W),(e=>(n(),y(D,{key:e.rtCode,label:e.rtName,value:e.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1})])),_:1})])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:12},{default:u((()=>[d(o,{label:r(M)("appliedHotel")},{default:u((()=>[h("span",null,c(r(q).hname),1)])),_:1},8,["label"])])),_:1})])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:24},{default:u((()=>[d(o,{label:r(M)("status")},{default:u((()=>[d(J,{modelValue:r(Y).isEnable,"onUpdate:modelValue":t[10]||(t[10]=e=>r(Y).isEnable=e),"inline-prompt":"","active-text":r(M)("valid"),"inactive-text":r(M)("invalid"),"active-value":"1","inactive-value":"0"},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),d(P,{gutter:24},{default:u((()=>[d(E,{md:24},{default:u((()=>[d(o,{label:r(M)("remark")},{default:u((()=>[d(A,{modelValue:r(Y).remark,"onUpdate:modelValue":t[11]||(t[11]=e=>r(Y).remark=e),type:"textarea",rows:3,placeholder:r(M)("enterRemark"),maxlength:"250"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["model","rules"])])),[[ae,r(O)]])}}});function M(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{strategyInformation:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Info"}},companyType:{t:0,b:{t:2,i:[{t:3}],s:"Company Type"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"Protocol Unit"}},intermediary:{t:0,b:{t:2,i:[{t:3}],s:"Agent"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Name"}},enterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the strategy name"}},brokerageLevel:{t:0,b:{t:2,i:[{t:3}],s:"Commission Level"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"Please select a level"}},strategyRules:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Rules"}},commissionType:{t:0,b:{t:2,i:[{t:3}],s:"Commission Type"}},byNight:{t:0,b:{t:2,i:[{t:3}],s:"By Night"}},commissionMethod:{t:0,b:{t:2,i:[{t:3}],s:"Commission Method"}},fixedNightCommission:{t:0,b:{t:2,i:[{t:3}],s:"Fixed Night Commission"}},percentageCommission:{t:0,b:{t:2,i:[{t:3}],s:"Percentage Commission"}},commissionAmount:{t:0,b:{t:2,i:[{t:3}],s:"Commission Amount"}},fixedNightCommissionUnit:{t:0,b:{t:2,i:[{t:3}],s:"Yuan"}},percentageCommissionUnit:{t:0,b:{t:2,i:[{t:3}],s:"%"}},strategyApplicationScope:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Scope"}},appliedChannels:{t:0,b:{t:2,i:[{t:3}],s:"Channels"}},selectChannels:{t:0,b:{t:2,i:[{t:3}],s:"Please select channels"}},appliedRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"Room Types"}},hotelRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"Hotel Room Types"}},groupRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"Group Room Types"}},selectRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"Please select room types"}},appliedHotel:{t:0,b:{t:2,i:[{t:3}],s:"Hotel"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"Valid"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"Invalid"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},enterRemark:{t:0,b:{t:2,i:[{t:3}],s:"Please enter remarks"}},submitSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Submission successful"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Edit successful"}},enableSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Enable successful"}},disableSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Disable successful"}},unknownError:{t:0,b:{t:2,i:[{t:3}],s:"Unknown error"}},networkError:{t:0,b:{t:2,i:[{t:3}],s:"Network error"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"No data available"}},numberOfRoomTypes:{t:0,b:{t:2,i:[{t:4,k:"count"},{t:3,v:" Room Types"}]}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Added successfully"}},commissionTypePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Please select commission type"}},brokerageLevelPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Please select brokerage level"}}},"zh-cn":{strategyInformation:{t:0,b:{t:2,i:[{t:3}],s:"策略信息"}},companyType:{t:0,b:{t:2,i:[{t:3}],s:"公司类型"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}},intermediary:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"策略名称"}},enterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"请输入策略名称"}},brokerageLevel:{t:0,b:{t:2,i:[{t:3}],s:"佣金级别"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"请选择级别"}},strategyRules:{t:0,b:{t:2,i:[{t:3}],s:"策略规则"}},commissionType:{t:0,b:{t:2,i:[{t:3}],s:"返佣类型"}},byNight:{t:0,b:{t:2,i:[{t:3}],s:"按间夜"}},commissionMethod:{t:0,b:{t:2,i:[{t:3}],s:"返佣方式"}},fixedNightCommission:{t:0,b:{t:2,i:[{t:3}],s:"间夜定额返佣"}},percentageCommission:{t:0,b:{t:2,i:[{t:3}],s:"百分比返佣"}},commissionAmount:{t:0,b:{t:2,i:[{t:3}],s:"返佣金额"}},fixedNightCommissionUnit:{t:0,b:{t:2,i:[{t:3}],s:"元"}},percentageCommissionUnit:{t:0,b:{t:2,i:[{t:3}],s:"%"}},strategyApplicationScope:{t:0,b:{t:2,i:[{t:3}],s:"策略应用范围"}},appliedChannels:{t:0,b:{t:2,i:[{t:3}],s:"应用渠道"}},selectChannels:{t:0,b:{t:2,i:[{t:3}],s:"请选择渠道"}},appliedRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"应用房型"}},hotelRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"酒店房型"}},groupRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"集团房型"}},selectRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"请选择房型"}},appliedHotel:{t:0,b:{t:2,i:[{t:3}],s:"应用酒店"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"有效"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"无效"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},enterRemark:{t:0,b:{t:2,i:[{t:3}],s:"请输入备注"}},submitSuccess:{t:0,b:{t:2,i:[{t:3}],s:"提交成功"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"编辑成功"}},enableSuccess:{t:0,b:{t:2,i:[{t:3}],s:"启用成功"}},disableSuccess:{t:0,b:{t:2,i:[{t:3}],s:"停用成功"}},unknownError:{t:0,b:{t:2,i:[{t:3}],s:"未知错误"}},networkError:{t:0,b:{t:2,i:[{t:3}],s:"网络错误"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"暂无数据"}},numberOfRoomTypes:{t:0,b:{t:2,i:[{t:4,k:"count"},{t:3,v:"个房型"}]}},save:{t:0,b:{t:2,i:[{t:3}],s:"保存"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"新增成功"}},commissionTypePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"请选择返佣类型"}},brokerageLevelPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"请选择佣金级别"}}},km:{strategyInformation:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានយុទ្ធសាស្ត្រ"}},companyType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទក្រុមហ៊ុន"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"អង្គភាពកិច្ចព្រមព្រៀង"}},intermediary:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកកាត់ដណ្ដឹង"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះយុទ្ធសាស្ត្រ"}},enterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ"}},brokerageLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតគណនេយ្យ"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកម្រិត"}},strategyRules:{t:0,b:{t:2,i:[{t:3}],s:"ច្បាប់យុទ្ធសាស្ត្រ"}},commissionType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទគណនេយ្យ"}},byNight:{t:0,b:{t:2,i:[{t:3}],s:"តាមយប់"}},commissionMethod:{t:0,b:{t:2,i:[{t:3}],s:"វិធីសាស្ត្រគណនេយ្យ"}},fixedNightCommission:{t:0,b:{t:2,i:[{t:3}],s:"គណនេយ្យថេរក្នុងមួយយប់"}},percentageCommission:{t:0,b:{t:2,i:[{t:3}],s:"គណនេយ្យជាភាគរយ"}},commissionAmount:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនគណនេយ្យ"}},fixedNightCommissionUnit:{t:0,b:{t:2,i:[{t:3}],s:"ដុល្លារ"}},percentageCommissionUnit:{t:0,b:{t:2,i:[{t:3}],s:"%"}},strategyApplicationScope:{t:0,b:{t:2,i:[{t:3}],s:"វិសាលភាពយុទ្ធសាស្ត្រ"}},appliedChannels:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},selectChannels:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឆានែល"}},appliedRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},hotelRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់សណ្ឋាគារ"}},groupRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់ក្រុម"}},selectRoomTypes:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទបន្ទប់"}},appliedHotel:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារ"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"មានសុពលភាព"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានសុពលភាព"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}},enterRemark:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលចំណាំ"}},submitSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានដាក់ស្នើដោយជោគជ័យ"}},editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួលដោយជោគជ័យ"}},enableSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានបើកដោយជោគជ័យ"}},disableSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានបិទដោយជោគជ័យ"}},unknownError:{t:0,b:{t:2,i:[{t:3}],s:"កំហុសមិនស្គាល់"}},networkError:{t:0,b:{t:2,i:[{t:3}],s:"កំហុសបណ្ដាញ"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានទិន្នន័យ"}},numberOfRoomTypes:{t:0,b:{t:2,i:[{t:4,k:"count"},{t:3,v:"ប្រភេទបន្ទប់"}]}},save:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានបន្ថែមដោយជោគជ័យ"}},commissionTypePlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទគណនេយ្យ"}},brokerageLevelPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកម្រិតគណនេយ្យ"}}}}})}M(q);const O=I(q,[["__scopeId","data-v-c8c0bba0"]]);export{O as default};
//# sourceMappingURL=createBrokerage-C9mgeCKI.js.map
