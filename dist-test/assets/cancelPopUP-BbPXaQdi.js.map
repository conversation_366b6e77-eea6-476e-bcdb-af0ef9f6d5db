{"version": 3, "file": "cancelPopUP-BbPXaQdi.js", "sources": ["../../src/views/order/component/cancelPopUP.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"cancelOrderSuccess\": \"Order canceled successfully\",\r\n    \"cancelOrder\": \"Cancel Order\",\r\n    \"cancelReason\": \"Cancel Reason\",\r\n    \"remark\": \"Remark\",\r\n    \"inputRemark\": \"Please enter a remark\",\r\n    \"cancel\": \"Cancel\",\r\n    \"confirm\": \"Confirm\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"cancelOrderSuccess\": \"取消订单成功\",\r\n    \"cancelOrder\": \"取消订单\",\r\n    \"cancelReason\": \"取消原因\",\r\n    \"remark\": \"备注\",\r\n    \"inputRemark\": \"请输入备注\",\r\n    \"cancel\": \"取消\",\r\n    \"confirm\": \"确定\"\r\n  },\r\n  \"km\": {\r\n    \"cancelOrderSuccess\": \"បានលុបការបញ្ជាទិញដោយជោគជ័យ\",\r\n    \"cancelOrder\": \"លុបការបញ្ជាទិញ\",\r\n    \"cancelReason\": \"មូលហេតុលុប\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"inputRemark\": \"សូមបញ្ចូលចំណាំ\",\r\n    \"cancel\": \"លុបចោល\",\r\n    \"confirm\": \"បញ្ជាក់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { bookApi, generalConfigApi } from '@/api/modules/index'\r\nimport { BooleanEnum } from '@/models'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    bookNo: string\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    bookNo: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst loading = ref(false)\r\nconst userStore = useUserStore()\r\nconst reasonList = ref<{ id: number; name: string }[]>([])\r\nconst form = ref({\r\n  reason: '',\r\n  remark: '',\r\n})\r\n\r\nonMounted(() => {\r\n  getSelectData()\r\n})\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nasync function getSelectData() {\r\n  const res = (await generalConfigApi.list({\r\n    gcode: userStore.gcode,\r\n    type: 'cancel_reason',\r\n    isG: BooleanEnum.YES,\r\n  })) as any\r\n  if (res.code === 0) {\r\n    reasonList.value = res.data\r\n    form.value.reason = res.data[0].name\r\n    onChange()\r\n  }\r\n}\r\n\r\nfunction onChange() {\r\n  form.value.remark = form.value.reason\r\n}\r\n\r\nfunction onSubmit() {\r\n  loading.value = true\r\n  bookApi\r\n    .bookCancel({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      bookNo: props.bookNo,\r\n      state: 'cancel',\r\n      remark: form.value.remark,\r\n    })\r\n    .then((res: any) => {\r\n      loading.value = false\r\n      if (res.code === 0) {\r\n        onCancel()\r\n        emits('success')\r\n        ElMessage.success(t('cancelOrderSuccess'))\r\n      }\r\n    })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" width=\"600px\" :title=\"t('cancelOrder')\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\">\r\n      <el-form :model=\"form\" size=\"default\" label-width=\"110px\">\r\n        <el-form-item :label=\"t('cancelReason')\">\r\n          <el-select v-model=\"form.reason\" style=\"width: 200px\" @change=\"onChange\">\r\n            <el-option v-for=\"item in reasonList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.name\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('remark')\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('inputRemark')\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div class=\"center-botton\">\r\n        <el-button @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"onSubmit\">\r\n          {{ t('confirm') }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.table-from {\r\n  padding: 10px 16px;\r\n}\r\n\r\n.subject {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  span {\r\n    display: inline-block;\r\n    margin-right: 5px;\r\n    font-size: 14px;\r\n    color: rgba(85, 77, 214, 1);\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.center-botton {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  width: 565px;\r\n  margin: 0 auto;\r\n  margin-top: 30px;\r\n}\r\n\r\n:deep(.el-input-number .el-input .el-input__inner) {\r\n  text-align: left;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "myVisible", "computed", "get", "modelValue", "set", "val", "loading", "ref", "userStore", "useUserStore", "reasonList", "form", "reason", "remark", "onCancel", "value", "onChange", "onSubmit", "bookApi", "bookCancel", "gcode", "hcode", "bookNo", "state", "then", "res", "code", "ElMessage", "success", "onMounted", "async", "generalConfigApi", "list", "type", "isG", "BooleanEnum", "YES", "data", "name", "getSelectData"], "mappings": "w5BAqCA,MAAMA,EAAQC,EAWRC,EAAQC,GAKRC,EAAEA,GAAMC,IAERC,EAAYC,EAAS,CACzBC,IAAM,IACGR,EAAMS,WAEf,GAAAC,CAAIC,GACFT,EAAM,oBAAqBS,EAAG,IAG5BC,EAAUC,GAAI,GACdC,EAAYC,IACZC,EAAaH,EAAoC,IACjDI,EAAOJ,EAAI,CACfK,OAAQ,GACRC,OAAQ,KAOV,SAASC,IACPd,EAAUe,OAAQ,CAAA,CAgBpB,SAASC,IACFL,EAAAI,MAAMF,OAASF,EAAKI,MAAMH,MAAA,CAGjC,SAASK,IACPX,EAAQS,OAAQ,EAChBG,EACGC,WAAW,CACVC,MAAOZ,EAAUY,MACjBC,MAAOb,EAAUa,MACjBC,OAAQ5B,EAAM4B,OACdC,MAAO,SACPV,OAAQF,EAAKI,MAAMF,SAEpBW,MAAMC,IACLnB,EAAQS,OAAQ,EACC,IAAbU,EAAIC,OACGZ,IACTlB,EAAM,WACI+B,EAAAC,QAAQ9B,EAAE,uBAAqB,GAE5C,QA1CL+B,GAAU,MAQVC,iBACQ,MAAAL,QAAaM,EAAiBC,KAAK,CACvCZ,MAAOZ,EAAUY,MACjBa,KAAM,gBACNC,IAAKC,EAAYC,MAEF,IAAbX,EAAIC,OACNhB,EAAWK,MAAQU,EAAIY,KACvB1B,EAAKI,MAAMH,OAASa,EAAIY,KAAK,GAAGC,KACvBtB,IACX,CAjBcuB,EAAA"}