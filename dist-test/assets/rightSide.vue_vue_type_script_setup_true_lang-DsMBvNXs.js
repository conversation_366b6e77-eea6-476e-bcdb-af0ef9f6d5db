import{_ as e}from"./HDropdownMenu.vue_vue_type_script_setup_true_lang-BvbgENOL.js";import{d as a,W as s,ai as t,aj as l,ak as r,b as o,D as n,o as i,c as p,f as u,w as c,g,u as d,e as h,h as m,Y as _,X as f,a0 as b,al as v,_ as x}from"./index-CkEhI1Zk.js";import{_ as y}from"./tools.vue_vue_type_script_setup_true_lang-CiXRxFW5.js";const k={class:"flex items-center"},j={class:"flex-center gap-1"},w=["src","onerror"],S=a({name:"ToolbarRightSide",__name:"rightSide",setup(a){const S=b(),D=s(),P=t(),{t:T}=l(),{generateI18nTitle:X}=r(),Y=o(!1);return n((()=>P.avatar),(()=>{Y.value&&(Y.value=!1)})),(a,s)=>{const t=x,l=e;return i(),p("div",k,[u(y,{mode:"right-side"}),u(l,{items:[[{label:d(X)(d(D).settings.home.title),handle:()=>d(S).push({path:d(D).settings.home.fullPath}),hide:!d(D).settings.home.enable},{label:d(T)("app.profile"),handle:()=>d(S).push({name:"personalSetting"})},{label:d(T)("app.preferences"),handle:()=>d(f).emit("global-preferences-toggle"),hide:!d(D).settings.userPreferences.enable}],[{label:d(T)("app.hotkeys"),handle:()=>d(f).emit("global-hotkeys-intro-toggle"),hide:"pc"!==d(D).mode}],[{label:d(T)("app.logout"),handle:()=>{v.logout().then((e=>{0===e.code&&P.logout()}))}}]],class:"flex-center cursor-pointer px-2"},{default:c((()=>[g("div",j,[d(P).avatar&&!d(Y)?(i(),p("img",{key:0,src:d(P).avatar,onerror:()=>Y.value=!0,class:"h-[24px] w-[24px] rounded-full"},null,8,w)):(i(),h(t,{key:1,name:"i-carbon:user-avatar-filled-alt",size:24,class:"text-gray-400"})),m(" "+_(d(P).account)+" ",1),u(t,{name:"i-ep:caret-bottom"})])])),_:1},8,["items"])])}}});export{S as _};
//# sourceMappingURL=rightSide.vue_vue_type_script_setup_true_lang-DsMBvNXs.js.map
