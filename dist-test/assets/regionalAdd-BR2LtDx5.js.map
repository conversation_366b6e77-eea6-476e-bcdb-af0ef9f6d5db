{"version": 3, "file": "regionalAdd-BR2LtDx5.js", "sources": ["../../src/views/group/base-info/merchant/components/DetailForm/regionalAdd.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"addDepartment\": \"Add Department\",\r\n    \"newType\": \"New Type\",\r\n    \"parent\": \"Parent\",\r\n    \"departmentParent\": \"Department Parent\",\r\n    \"pleaseSelectParent\": \"Please select parent department\",\r\n    \"departmentName\": \"Name\",\r\n    \"department\": \"Department\",\r\n    \"pleaseEnterName\": \"Please enter name\",\r\n    \"createSuccess\": \"Department created successfully\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"addDepartment\": \"新增部门\",\r\n    \"newType\": \"新增类型\",\r\n    \"parent\": \"上级\",\r\n    \"departmentParent\": \"部门上级\",\r\n    \"pleaseSelectParent\": \"请选择上级部门\",\r\n    \"departmentName\": \"部门名称\",\r\n    \"department\": \"部门\",\r\n    \"pleaseEnterName\": \"请输入名称\",\r\n    \"createSuccess\": \"部门创建成功\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\"\r\n  },\r\n  \"km\": {\r\n    \"addDepartment\": \"បន្ថែមនាយកដ្ឋាន\",\r\n    \"newType\": \"ប្រភេទថ្មី\",\r\n    \"parent\": \"ថ្នាក់លើ\",\r\n    \"departmentParent\": \"នាយកដ្ឋានថ្នាក់លើ\",\r\n    \"pleaseSelectParent\": \"សូមជ្រើសរើសនាយកដ្ឋានថ្នាក់លើ\",\r\n    \"departmentName\": \"ឈ្មោះនាយកដ្ឋាន\",\r\n    \"department\": \"នាយកដ្ឋាន\",\r\n    \"pleaseEnterName\": \"សូមបញ្ចូលឈ្មោះ\",\r\n    \"createSuccess\": \"បង្កើតនាយកដ្ឋានដោយជោគជ័យ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\n\r\nimport type { DetailFormProps } from '../../types'\r\nimport { deptApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { handleTree } from '@/utils/tree'\r\n\r\nconst props = withDefaults(\r\n  defineProps<\r\n    {\r\n      modelValue?: boolean\r\n    } & DetailFormProps\r\n  >(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  name: '',\r\n  parentId: '',\r\n  sort: 1,\r\n  status: 0,\r\n  isMerchant: '0',\r\n})\r\nconst typecode = ref('0')\r\nconst formRules = ref<FormRules>({\r\n  name: [{ required: true, message: t('pleaseEnterName'), trigger: 'blur' }],\r\n  parentId: [{ required: true, message: t('pleaseSelectParent'), trigger: 'blur' }],\r\n})\r\nconst myVisible = ref(props.modelValue)\r\n\r\nonMounted(() => {\r\n  getBuildFloor()\r\n  getTreeList()\r\n})\r\n\r\n// 获取集团\r\nconst filteredData = ref<{ id: number; name: string }[]>([])\r\nfunction getTreeList() {\r\n  deptApi.getSimpleDeptList({ gcode: userStore.gcode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      filteredData.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst myTree = ref<any>([])\r\n\r\nfunction getBuildFloor() {\r\n  deptApi.list({ gcode: userStore.gcode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      const filteredData = res.data.filter((item: any) => item.id !== props.id)\r\n      myTree.value = handleTree(filteredData)\r\n      myTree.value.forEach((item: any) => {\r\n        if (item.children) {\r\n          item.children.forEach((child: any) => {\r\n            child.disabled = true\r\n          })\r\n        }\r\n      })\r\n    }\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        const param = {\r\n          gcode: userStore.gcode,\r\n          name: form.value.name,\r\n          parentId: form.value.parentId,\r\n          sort: form.value.sort,\r\n          status: form.value.status,\r\n          isMerchant: form.value.isMerchant,\r\n        }\r\n        deptApi.create(param).then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success({\r\n              message: t('createSuccess'),\r\n              center: true,\r\n            })\r\n            onCancel()\r\n            emits('success')\r\n          } else {\r\n            ElMessage.error({\r\n              message: res.msg,\r\n              center: true,\r\n            })\r\n          }\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction typeName() {\r\n  form.value.name = form.value.name ? '' : form.value.name\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('addDepartment')\" width=\"500px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close @closed=\"emits('update:modelValue', false)\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"120px\">\r\n      <el-form-item :label=\"t('newType')\">\r\n        <el-radio-group v-model=\"typecode\" @change=\"typeName\">\r\n          <el-radio label=\"0\">\r\n            {{ t('department') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"typecode === '1'\" :label=\"t('parent')\" prop=\"code\">\r\n        <el-cascader\r\n          v-model=\"form.parentId\"\r\n          :options=\"myTree\"\r\n          :props=\"{\r\n            value: 'id',\r\n            label: 'name',\r\n            emitPath: true,\r\n          }\"\r\n          :show-all-levels=\"false\"\r\n          :placeholder=\"t('pleaseSelectParent')\"\r\n          clearable\r\n        />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('parent')\" prop=\"parentId\">\r\n        <el-select v-model=\"form.parentId\" clearable :placeholder=\"t('pleaseSelectParent')\">\r\n          <el-option v-for=\"item in filteredData\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"typecode === '1' ? '区域名称' : t('departmentName')\" prop=\"name\">\r\n        <el-input v-model=\"form.name\" :placeholder=\"t('pleaseEnterName')\" clearable maxlength=\"30\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('save') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-cascader) {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "t", "useI18n", "formRef", "ref", "form", "gcode", "name", "parentId", "sort", "status", "isMerchant", "typecode", "formRules", "required", "message", "trigger", "myVisible", "modelValue", "onMounted", "<PERSON>pt<PERSON><PERSON>", "list", "then", "res", "code", "filteredData", "data", "filter", "item", "id", "myTree", "value", "handleTree", "for<PERSON>ach", "children", "child", "disabled", "getSimpleDeptList", "onSubmit", "validate", "valid", "param", "create", "ElMessage", "success", "center", "onCancel", "error", "msg", "typeName"], "mappings": "kiCAoDA,MAAMA,EAAQC,EAWRC,EAAQC,EAKRC,EAAYC,KACZC,EAAEA,GAAMC,IAERC,EAAUC,IACVC,EAAOD,EAAI,CACfE,MAAOP,EAAUO,MACjBC,KAAM,GACNC,SAAU,GACVC,KAAM,EACNC,OAAQ,EACRC,WAAY,MAERC,EAAWR,EAAI,KACfS,EAAYT,EAAe,CAC/BG,KAAM,CAAC,CAAEO,UAAU,EAAMC,QAASd,EAAE,mBAAoBe,QAAS,SACjER,SAAU,CAAC,CAAEM,UAAU,EAAMC,QAASd,EAAE,sBAAuBe,QAAS,WAEpEC,EAAYb,EAAIT,EAAMuB,YAE5BC,GAAU,KAkBAC,EAAAC,KAAK,CAAEf,MAAOP,EAAUO,QAASgB,MAAMC,IACzC,GAAa,IAAbA,EAAIC,KAAY,CACZC,MAAAA,EAAeF,EAAIG,KAAKC,QAAQC,GAAcA,EAAKC,KAAOlC,EAAMkC,KAC/DC,EAAAC,MAAQC,EAAWP,GACnBK,EAAAC,MAAME,SAASL,IAChBA,EAAKM,UACFN,EAAAM,SAASD,SAASE,IACrBA,EAAMC,UAAW,CAAA,GAClB,GAEJ,KApBGhB,EAAAiB,kBAAkB,CAAE/B,MAAOP,EAAUO,QAASgB,MAAMC,IACzC,IAAbA,EAAIC,OACNC,EAAaM,MAAQR,EAAIG,KAAA,GARjB,IAIR,MAAAD,EAAerB,EAAoC,IASnD,MAAA0B,EAAS1B,EAAS,IAkBxB,SAASkC,IACPnC,EAAQ4B,OACN5B,EAAQ4B,MAAMQ,UAAUC,IACtB,GAAIA,EAAO,CACT,MAAMC,EAAQ,CACZnC,MAAOP,EAAUO,MACjBC,KAAMF,EAAK0B,MAAMxB,KACjBC,SAAUH,EAAK0B,MAAMvB,SACrBC,KAAMJ,EAAK0B,MAAMtB,KACjBC,OAAQL,EAAK0B,MAAMrB,OACnBC,WAAYN,EAAK0B,MAAMpB,YAEzBS,EAAQsB,OAAOD,GAAOnB,MAAMC,IACT,IAAbA,EAAIC,MACNmB,EAAUC,QAAQ,CAChB7B,QAASd,EAAE,iBACX4C,QAAQ,IAEDC,IACTjD,EAAM,YAEN8C,EAAUI,MAAM,CACdhC,QAASQ,EAAIyB,IACbH,QAAQ,GACT,GAEJ,IAEJ,CAGL,SAASC,IACP7B,EAAUc,OAAQ,CAAA,CAGpB,SAASkB,IACP5C,EAAK0B,MAAMxB,KAAOF,EAAK0B,MAAMxB,KAAO,GAAKF,EAAK0B,MAAMxB,IAAA"}