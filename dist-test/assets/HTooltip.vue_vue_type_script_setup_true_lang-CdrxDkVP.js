import{d as e,am as t,o as a,e as s,w as o,ab as p,h as l,Y as r,a6 as d,c as n}from"./index-CkEhI1Zk.js";const u={key:1},f=e({__name:"HTooltip",props:{text:{default:""},enable:{type:Boolean,default:!0}},setup:e=>(e,f)=>{const i=t("VTooltip");return e.enable?(a(),s(i,d({key:0,"popper-triggers":["hover"]},e.$attrs),{popper:o((()=>[p(e.$slots,"text",{},(()=>[l(r(e.text),1)]))])),default:o((()=>[p(e.$slots,"default")])),_:3},16)):(a(),n("div",u,[p(e.$slots,"default")]))}});export{f as _};
//# sourceMappingURL=HTooltip.vue_vue_type_script_setup_true_lang-CdrxDkVP.js.map
