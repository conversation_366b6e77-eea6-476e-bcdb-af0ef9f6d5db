<i18n>
{
  "en": {
    "selectCleaner": "Please select a cleaner",
    "noCleanerFound": "No cleaners found. Please add members in: Store -> Member Roles.",
    "cleanRoomSuccess": "Room \"{rNo}\" cleaned successfully",
    "cleanRoomTitle": "Clean Room: {rNo}",
    "cleanerLabel": "Cleaner",
    "remarkLabel": "Remark",
    "remarkPlaceholder": "Please enter remarks",
    "cancel": "Cancel",
    "submit": "Submit",
    "selectCleanerPlaceholder": "Please select a cleaner",
    "cleanerRequired": "Please select a cleaner"
  },
  "zh-cn": {
    "selectCleaner": "请选择清洁工",
    "noCleanerFound": "没有找到任何清洁工，请先到:门店->成员角色 添加",
    "cleanRoomSuccess": "房间「{rNo}」置干净成功",
    "cleanRoomTitle": "置干净：{rNo}",
    "cleanerLabel": "清洁工",
    "remarkLabel": "备注",
    "remarkPlaceholder": "请输入备注",
    "cancel": "取消",
    "submit": "提交",
    "selectCleanerPlaceholder": "请选择清洁工",
    "cleanerRequired": "请选择清洁工"
  },
  "km": {
    "selectCleaner": "សូមជ្រើសរើសអ្នកសម្អាត",
    "noCleanerFound": "រកមិនឃើញអ្នកសម្អាតទេ។ សូមបន្ថែមសមាជិកនៅ: ហាង -> តួនាទីសមាជិក។",
    "cleanRoomSuccess": "បន្ទប់ \"{rNo}\" ត្រូវបានសម្អាតដោយជោគជ័យ",
    "cleanRoomTitle": "សម្អាតបន្ទប់: {rNo}",
    "cleanerLabel": "អ្នកសម្អាត",
    "remarkLabel": "កំណត់ចំណាំ",
    "remarkPlaceholder": "សូមបញ្ចូលកំណត់ចំណាំ",
    "cancel": "បោះបង់",
    "submit": "ដាក់ស្នើ",
    "selectCleanerPlaceholder": "សូមជ្រើសរើសអ្នកសម្អាត",
    "cleanerRequired": "សូមជ្រើសរើសអ្នកសម្អាត"
  }
}
</i18n>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { roomApi, userApi } from '@/api/modules/index'
import useUserStore from '@/store/modules/user'

const props = withDefaults(
  defineProps<{
    modelValue?: boolean
    rCode: string
    rNo: string
  }>(),
  {
    rCode: '',
    rNo: '',
  }
)

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  success: [data: any]
}>()

const { t } = useI18n()

const userStore = useUserStore()

const formRef = ref<FormInstance>()
const myVisible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  },
})
const form = ref({
  gcode: userStore.gcode,
  hcode: userStore.hcode,
  rCode: props.rCode,
  cleaner: '',
  remark: '',
})
const formRules = ref<FormRules>({
  cleaner: [{ required: true, message: t('cleanerRequired'), trigger: 'blur' }],
})

onMounted(() => {
  // 获取清洁工列表
  getCleanerList()
})
// 清洁工列表
const cleanerList = ref<{ username: string; nickname: string }[]>([])
function getCleanerList() {
  userApi.getCleanerList({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {
    if (res.code === 0) {
      cleanerList.value = res.data
      if (cleanerList.value.length === 0) {
        ElMessage.error(t('noCleanerFound'))
      }
    }
  })
}

function onSubmit() {
  formRef.value &&
    formRef.value.validate((valid) => {
      if (valid) {
        roomApi.turnClean(form.value).then((res: any) => {
          if (res.code !== 0) {
            ElMessage.error(res.msg)
            return
          }
          ElMessage.success({
            message: t('cleanRoomSuccess', { rNo: props.rNo }),
            type: 'success',
            center: true,
          })
          emits('success', {
            rCode: props.rCode,
            rNo: props.rNo,
            state: res.data.state,
          })
          onCancel()
        })
      }
    })
}

function onCancel() {
  myVisible.value = false
}
</script>

<template>
  <el-dialog v-model="myVisible" :title="t('cleanRoomTitle', { rNo: props.rNo })" width="500px" :close-on-click-modal="false" append-to-body destroy-on-close @keydown.ctrl.enter.prevent="onSubmit">
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px" label-suffix="：">
      <el-form-item :label="t('cleanerLabel')">
        <el-select v-model="form.cleaner" :placeholder="t('selectCleanerPlaceholder')">
          <el-option v-for="item in cleanerList" :key="item.username" :label="item.nickname" :value="item.username" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('remarkLabel')">
        <el-input v-model="form.remark" type="textarea" :rows="3" :placeholder="t('remarkPlaceholder')" maxlength="255" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="large" @click="onCancel">
        {{ t('cancel') }}
      </el-button>
      <el-button type="primary" size="large" @click="onSubmit">
        {{ t('submit') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
// scss
</style>
