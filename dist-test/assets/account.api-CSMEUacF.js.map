{"version": 3, "file": "account.api-CSMEUacF.js", "sources": ["../../src/api/modules/pms/account/account.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/account'\r\n/**\r\n * 账务api\r\n */\r\nexport default {\r\n  /**\r\n   * 创建账务\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createAccount: (data: any) => api.post(`${BASE_PATH}/create`, data),\r\n\r\n  /**\r\n   * 更新账务\r\n   * @param data\r\n   */\r\n  updateAccount: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n\r\n  /**\r\n   * 消费操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  consumeAccount: (data: any) => api.post(`${BASE_PATH}/consume`, data),\r\n\r\n  /**\r\n   * 付款预授权操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  payAccount: (data: any) => api.post(`${BASE_PATH}/pay`, data),\r\n\t/**\r\n\t * 批量付款操作(暂时只适用优惠券类型)\r\n\t * @param data\r\n\t * @returns\r\n\t */\r\n\tbatchPayAccount: (data: any) => api.post(`${BASE_PATH}/batch/pay`, data),\r\n  /**\r\n   *  冲账操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  accountRed: (data: any) => api.post(`${BASE_PATH}/red`, data),\r\n\r\n  /**\r\n   *  转账操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  transFer: (data: any) => api.post(`${BASE_PATH}/transfer`, data),\r\n\r\n  /**\r\n   *  获得选中的账务列表(批量冲账、转账)\r\n   * @param data\r\n   * @returns 账务列表\r\n   */\r\n  selectedAccountList: (data: any) => api.get(`${BASE_PATH}/get-selected-list`, { params: data }),\r\n\r\n  /**\r\n   * 根据accNos获取账务列表\r\n   * @param data accNos:账单号列表\r\n   * @returns\r\n   */\r\n  listByAccNos: (data: { gcode: string; hcode: string; accNos: string[] }) => api.post('biz/account/listByAccNos', data),\r\n\r\n  /**\r\n   * 统计多个单号账务(预订单号,联房)\r\n   * @returns\r\n   * @param data\r\n   */\r\n  statAccountByNo: (data: any) => api.get(`${BASE_PATH}/stat-account-by-no`, { params: data }),\r\n\r\n  /**\r\n   * 统计宾客的账务(订单详情账务统计数据)\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getOrderAccountStatByNoType: (data: any) => api.get(`${BASE_PATH}/stat-account-by-notype`, { params: data }),\r\n\r\n  /**\r\n   * 统计在住宾客的账务(订单详情账务统计数据)\r\n   * @returns\r\n   * @param data\r\n   */\r\n  statAccountByTogetherCode: (data: any) => api.get(`${BASE_PATH}/stat-account-by-together-code`, { params: data }),\r\n\r\n  /**\r\n   * 获得账务列表分页(联房)\r\n   * @returns\r\n   * @param data\r\n   */\r\n  pageByNos: (data: any) => api.get(`${BASE_PATH}/page-by-nos`, { params: data }),\r\n\r\n  /**\r\n   * 获得账务列表中科目列表\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getAccountSubList: (data: any) => api.get(`${BASE_PATH}/get-list-for-sub`, { params: data }),\r\n\r\n  /**\r\n   * 获得账务列表分页(宾客)\r\n   * @returns\r\n   * @param data\r\n   */\r\n  pageByTogetherCodes: (data: any) => api.get(`${BASE_PATH}/page`, { params: data }),\r\n\r\n  /**\r\n   * 结账退房-计算房费\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getConfirmRoomFee: (data: any) => api.post(`${BASE_PATH}/get-confirm-room-fee`, data),\r\n\r\n  /**\r\n   * 获得单条账务的金额\r\n   * @param data gcode,hcode,accNo\r\n   */\r\n  getSingleAccountFee: (data: any) => api.get(`${BASE_PATH}/get-single-account-fee`, { params: data }),\r\n\r\n  /**\r\n   * 拆账操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  splitAccount: (data: any) => api.put(`${BASE_PATH}/split`, data),\r\n\r\n  /**\r\n   * 获取账务的备注\r\n   * @param data\r\n   */\r\n  getRemark: (data: any) => api.get(`${BASE_PATH}/get-remark`, { params: data }),\r\n\r\n  /**\r\n   * 修改账务备注\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateRemark: (data: any) => api.put(`${BASE_PATH}/update-remark`, data),\r\n\r\n  /**\r\n   * 结账退房-结账(挂账)账号列表\r\n   * @returns\r\n   * @param data\r\n   */\r\n  checkoutOrderList: (data: any) => api.get(`${BASE_PATH}/get-checkout-order-list`, { params: data }),\r\n\r\n  /**\r\n   * 获取入账账号信息\r\n   * @param data\r\n   */\r\n  getRecordAccountList: (data: any) => api.get(`${BASE_PATH}/get-record-account`, { params: data }),\r\n\r\n  /**\r\n   * 挂账退房\r\n   * @param data\r\n   * @returns\r\n   */\r\n  creditCheckOut: (data: any) => api.post(`${BASE_PATH}/credit-check-out`, data),\r\n\r\n  /**\r\n   * 结账退房\r\n   * @param data\r\n   * @returns\r\n   */\r\n  payCheckOut: (data: any) => api.post(`${BASE_PATH}/pay-check-out`, data),\r\n\r\n  /**\r\n   * 获得撤销的结账列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  closeAccount: (data: any) => api.get(`${BASE_PATH}/get-close-account`, { params: data }),\r\n\r\n  /**\r\n   * 获得结账号的结账列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  byPayno: (data: any) => api.get(`${BASE_PATH}/get-account-by-payno`, { params: data }),\r\n\r\n  /**\r\n   * 撤销操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  undoPay: (data: any) => api.post(`${BASE_PATH}/undo-pay`, data),\r\n\r\n  /**\r\n   * 结账退房-需要确认的预授权列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getPreauth: (data: any) => api.post(`${BASE_PATH}/get-confirm-preauth`, data),\r\n  /**\r\n   * 结账退房-确认预授权（收款金额）\r\n   * @param data\r\n   * @returns\r\n   */\r\n  confirmPreauth: (data: any) => api.post(`${BASE_PATH}/confirm-preauth`, data),\r\n  /**\r\n   * 结账退房-取消预授权\r\n   * @param data\r\n   * @returns\r\n   */\r\n  cancelPreauth: (data: any) => api.put(`${BASE_PATH}/cancel-preauth`, data),\r\n  /**\r\n   * 统计付款金额(完成结账界面需要)\r\n   * @param data\r\n   * @returns\r\n   */\r\n  statAccount: (data: any) => api.post(`${BASE_PATH}/stat-account`, data),\r\n\r\n  /**\r\n   * 判断是否有AR账务可挂(完成结账界面需要)\r\n   * @param data\r\n   * @returns\r\n   */\r\n  judgeARAccount: (data: any) => api.post(`${BASE_PATH}/judge-ar-account`, data),\r\n\r\n  /**\r\n   * 结账退房-确认生成房费\r\n   * @param data\r\n   * @returns\r\n   */\r\n  confirmRoomFee: (data: any) => api.post(`${BASE_PATH}/confirm-room-fee`, data),\r\n\r\n  /**\r\n   * 获得结账号的结账列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getListByAccnos: (data: any) => api.get(`${BASE_PATH}/get-list-by-accnos`, { params: data }),\r\n\r\n  /**\r\n   * 结账、部分结账操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  partCloseAccount: (data: any) => api.post(`${BASE_PATH}/part-close-account`, data),\r\n\r\n  /**\r\n   * 部分结账统计金额\r\n   * @param data\r\n   * @returns\r\n   */\r\n  statCloseAccount: (data: any) => api.post(`${BASE_PATH}/stat-close-account`, data),\r\n\r\n  /**\r\n   * 获取退款详情\r\n   * @param data\r\n   * @returns\r\n   */\r\n  statAccountRefund: (data: any) => api.get(`${BASE_PATH}/stat-account-refund`, { params: data }),\r\n  /**\r\n   * 提交退款\r\n   * @param data\r\n   * @returns\r\n   */\r\n  submitRefund: (data: any) => api.post(`${BASE_PATH}/refund`, data),\r\n  /**\r\n   * 补录查询\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getRecording: (data: any) => api.get(`${BASE_PATH}/get-recording`, { params: data }),\r\n  /**\r\n   * 补录操作\r\n   * @param data\r\n   * @returns\r\n   */\r\n  postRecording: (data: any) => api.post(`${BASE_PATH}/recording`, data),\r\n\t/**\r\n\t * 获得每日房费列表\r\n\t * @param data\r\n\t * @returns\r\n\t */\r\n\taccountRoomFee: (data: any) => api.get(`${BASE_PATH}/room-fee-list`, { params: data }),\r\n\t/**\r\n\t * 获得催账列表(宾客)\r\n\t * @returns\r\n\t * @param data\r\n\t */\r\n\tgetReminderList: (data: any) => api.get(`${BASE_PATH}/get-reminder-list`, { params: data }),\r\n\t/**\r\n  \t * 获得预计欠费数量\r\n  \t * @returns\r\n  \t * @param data\r\n  \t */\r\n  \tgetReminderCount: (data: any) => api.get(`${BASE_PATH}/get-reminder-count`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "accountApi", "createAccount", "data", "api", "post", "updateAccount", "put", "consumeAccount", "payAccount", "batchPayAccount", "accountRed", "transFer", "selectedAccountList", "get", "params", "listByAccNos", "statAccountByNo", "getOrderAccountStatByNoType", "statAccountByTogetherCode", "pageByNos", "getAccountSubList", "pageByTogetherCodes", "getConfirmRoomFee", "getSingleAccountFee", "splitAccount", "getRemark", "updateRemark", "checkoutOrderList", "getRecordAccountList", "creditCheckOut", "payCheckOut", "closeAccount", "by<PERSON><PERSON><PERSON>", "undoPay", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cancelPreauth", "statAccount", "judge<PERSON><PERSON><PERSON>unt", "confirmRoomFee", "getListByAccnos", "partCloseAccount", "statCloseAccount", "statAccountRefund", "submitRefund", "getRecording", "postRecording", "accountRoomFee", "getReminderList", "getReminderCount"], "mappings": "wCAEA,MAAMA,EAAY,wBAIHC,EAAA,CAMbC,cAAgBC,GAAcC,EAAIC,KAAK,GAAGL,WAAoBG,GAM9DG,cAAgBH,GAAcC,EAAIG,IAAI,GAAGP,WAAoBG,GAO7DK,eAAiBL,GAAcC,EAAIC,KAAK,GAAGL,YAAqBG,GAOhEM,WAAaN,GAAcC,EAAIC,KAAK,GAAGL,QAAiBG,GAMzDO,gBAAkBP,GAAcC,EAAIC,KAAK,GAAGL,cAAuBG,GAMlEQ,WAAaR,GAAcC,EAAIC,KAAK,GAAGL,QAAiBG,GAOxDS,SAAWT,GAAcC,EAAIC,KAAK,GAAGL,aAAsBG,GAO3DU,oBAAsBV,GAAcC,EAAIU,IAAI,GAAGd,sBAA+B,CAAEe,OAAQZ,IAOxFa,aAAeb,GAA6DC,EAAIC,KAAK,2BAA4BF,GAOjHc,gBAAkBd,GAAcC,EAAIU,IAAI,GAAGd,uBAAgC,CAAEe,OAAQZ,IAOrFe,4BAA8Bf,GAAcC,EAAIU,IAAI,GAAGd,2BAAoC,CAAEe,OAAQZ,IAOrGgB,0BAA4BhB,GAAcC,EAAIU,IAAI,GAAGd,kCAA2C,CAAEe,OAAQZ,IAO1GiB,UAAYjB,GAAcC,EAAIU,IAAI,GAAGd,gBAAyB,CAAEe,OAAQZ,IAOxEkB,kBAAoBlB,GAAcC,EAAIU,IAAI,GAAGd,qBAA8B,CAAEe,OAAQZ,IAOrFmB,oBAAsBnB,GAAcC,EAAIU,IAAI,GAAGd,SAAkB,CAAEe,OAAQZ,IAO3EoB,kBAAoBpB,GAAcC,EAAIC,KAAK,GAAGL,yBAAkCG,GAMhFqB,oBAAsBrB,GAAcC,EAAIU,IAAI,GAAGd,2BAAoC,CAAEe,OAAQZ,IAO7FsB,aAAetB,GAAcC,EAAIG,IAAI,GAAGP,UAAmBG,GAM3DuB,UAAYvB,GAAcC,EAAIU,IAAI,GAAGd,eAAwB,CAAEe,OAAQZ,IAOvEwB,aAAexB,GAAcC,EAAIG,IAAI,GAAGP,kBAA2BG,GAOnEyB,kBAAoBzB,GAAcC,EAAIU,IAAI,GAAGd,4BAAqC,CAAEe,OAAQZ,IAM5F0B,qBAAuB1B,GAAcC,EAAIU,IAAI,GAAGd,uBAAgC,CAAEe,OAAQZ,IAO1F2B,eAAiB3B,GAAcC,EAAIC,KAAK,GAAGL,qBAA8BG,GAOzE4B,YAAc5B,GAAcC,EAAIC,KAAK,GAAGL,kBAA2BG,GAOnE6B,aAAe7B,GAAcC,EAAIU,IAAI,GAAGd,sBAA+B,CAAEe,OAAQZ,IAOjF8B,QAAU9B,GAAcC,EAAIU,IAAI,GAAGd,yBAAkC,CAAEe,OAAQZ,IAO/E+B,QAAU/B,GAAcC,EAAIC,KAAK,GAAGL,aAAsBG,GAO1DgC,WAAahC,GAAcC,EAAIC,KAAK,GAAGL,wBAAiCG,GAMxEiC,eAAiBjC,GAAcC,EAAIC,KAAK,GAAGL,oBAA6BG,GAMxEkC,cAAgBlC,GAAcC,EAAIG,IAAI,GAAGP,mBAA4BG,GAMrEmC,YAAcnC,GAAcC,EAAIC,KAAK,GAAGL,iBAA0BG,GAOlEoC,eAAiBpC,GAAcC,EAAIC,KAAK,GAAGL,qBAA8BG,GAOzEqC,eAAiBrC,GAAcC,EAAIC,KAAK,GAAGL,qBAA8BG,GAOzEsC,gBAAkBtC,GAAcC,EAAIU,IAAI,GAAGd,uBAAgC,CAAEe,OAAQZ,IAOrFuC,iBAAmBvC,GAAcC,EAAIC,KAAK,GAAGL,uBAAgCG,GAO7EwC,iBAAmBxC,GAAcC,EAAIC,KAAK,GAAGL,uBAAgCG,GAO7EyC,kBAAoBzC,GAAcC,EAAIU,IAAI,GAAGd,wBAAiC,CAAEe,OAAQZ,IAMxF0C,aAAe1C,GAAcC,EAAIC,KAAK,GAAGL,WAAoBG,GAM7D2C,aAAe3C,GAAcC,EAAIU,IAAI,GAAGd,kBAA2B,CAAEe,OAAQZ,IAM7E4C,cAAgB5C,GAAcC,EAAIC,KAAK,GAAGL,cAAuBG,GAMlE6C,eAAiB7C,GAAcC,EAAIU,IAAI,GAAGd,kBAA2B,CAAEe,OAAQZ,IAM/E8C,gBAAkB9C,GAAcC,EAAIU,IAAI,GAAGd,sBAA+B,CAAEe,OAAQZ,IAMlF+C,iBAAmB/C,GAAcC,EAAIU,IAAI,GAAGd,uBAAgC,CAAEe,OAAQZ"}