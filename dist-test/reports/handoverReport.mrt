<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <handoverReport Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>handoverReport</Alias>
        <HeadersString />
        <Key />
        <Name>handoverReport</Name>
        <PathData>D:\code\qizhi\aflower-pms-front\public\reports\handoverReport.json</PathData>
      </handoverReport>
    </Databases>
    <DataSources isList="true" count="15">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="3">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>f90e45b40bf340a6bbec515615073373</Key>
        <Name>root</Name>
        <NameInSource>handoverReport.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="22">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>startDate,System.String</value>
          <value>endDate,System.String</value>
          <value>shiftNo,System.String</value>
          <value>lastSelectTime,System.DateTime</value>
          <value>operator,System.String</value>
          <value>paymentTypeAccounts,System.String</value>
          <value>paymentTypeTotalFee,System.Decimal</value>
          <value>rmbPayTotalFee,System.Decimal</value>
          <value>consumptionTypeAccounts,System.String</value>
          <value>consumptionTypeTotalFee,System.Decimal</value>
          <value>paymentDetails,System.String</value>
          <value>consumptionDetails,System.String</value>
          <value>recorderPaymentDetails,System.String</value>
          <value>memberRechargeAccounts,System.String</value>
          <value>memberRechargeTotalFee,System.Decimal</value>
          <value>mMemberRechargeTotalFee,System.Decimal</value>
          <value>gMemberRechargeTotalFee,System.Decimal</value>
          <value>memberRechargeRmbPayTotalFee,System.Decimal</value>
          <value>arDetails,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>f052f45149f2436a86ab8c408c45936b</Key>
        <Name>root_data</Name>
        <NameInSource>handoverReport.root_data</NameInSource>
      </root_data>
      <root_data_arDetails Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_arDetails</Alias>
        <Columns isList="true" count="4">
          <value>recorder,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>accounts,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>703ca6316d9a47eaa6b85529fd88c022</Key>
        <Name>root_data_arDetails</Name>
        <NameInSource>handoverReport.root_data_arDetails</NameInSource>
      </root_data_arDetails>
      <root_data_arDetails_accounts Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_arDetails_accounts</Alias>
        <Columns isList="true" count="5">
          <value>arSetName,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>51b0cea9627d46b396890083cccb6dd9</Key>
        <Name>root_data_arDetails_accounts</Name>
        <NameInSource>handoverReport.root_data_arDetails_accounts</NameInSource>
      </root_data_arDetails_accounts>
      <root_data_consumptionDetails Ref="7" type="DataTableSource" isKey="true">
        <Alias>root_data_consumptionDetails</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>accounts,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>1ed8f65a1cc64bd587a8b9957098f2c7</Key>
        <Name>root_data_consumptionDetails</Name>
        <NameInSource>handoverReport.root_data_consumptionDetails</NameInSource>
      </root_data_consumptionDetails>
      <root_data_consumptionDetails_accounts Ref="8" type="DataTableSource" isKey="true">
        <Alias>root_data_consumptionDetails_accounts</Alias>
        <Columns isList="true" count="27">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>no,System.String</value>
          <value>accNo,System.String</value>
          <value>accType,System.String</value>
          <value>rCode,System.String</value>
          <value>rNo,System.String</value>
          <value>guestName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>subType,System.String</value>
          <value>payCode,System.String</value>
          <value>accSetName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>accDetail,System.String</value>
          <value>shiftNo,System.String</value>
          <value>shiftName,System.String</value>
          <value>recorder,System.String</value>
          <value>bizDate,System.String</value>
          <value>state,System.String</value>
          <value>isGAcc,System.String</value>
          <value>remark,System.String</value>
          <value>createTime,System.DateTime</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>fd39c858390d4aea889f5f9a83c0723e</Key>
        <Name>root_data_consumptionDetails_accounts</Name>
        <NameInSource>handoverReport.root_data_consumptionDetails_accounts</NameInSource>
      </root_data_consumptionDetails_accounts>
      <root_data_consumptionTypeAccounts Ref="9" type="DataTableSource" isKey="true">
        <Alias>root_data_consumptionTypeAccounts</Alias>
        <Columns isList="true" count="27">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>no,System.String</value>
          <value>accNo,System.String</value>
          <value>accType,System.String</value>
          <value>rCode,System.String</value>
          <value>rNo,System.String</value>
          <value>guestName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>subType,System.String</value>
          <value>payCode,System.String</value>
          <value>accSetName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>accDetail,System.String</value>
          <value>shiftNo,System.String</value>
          <value>shiftName,System.String</value>
          <value>recorder,System.String</value>
          <value>bizDate,System.String</value>
          <value>state,System.String</value>
          <value>isGAcc,System.String</value>
          <value>remark,System.String</value>
          <value>createTime,System.DateTime</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>053b7b3617d04ef4906371ae875d107e</Key>
        <Name>root_data_consumptionTypeAccounts</Name>
        <NameInSource>handoverReport.root_data_consumptionTypeAccounts</NameInSource>
      </root_data_consumptionTypeAccounts>
      <root_data_memberRechargeAccounts Ref="10" type="DataTableSource" isKey="true">
        <Alias>root_data_memberRechargeAccounts</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>accounts,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>43ece0c520b040a4804722f9b8377a61</Key>
        <Name>root_data_memberRechargeAccounts</Name>
        <NameInSource>handoverReport.root_data_memberRechargeAccounts</NameInSource>
      </root_data_memberRechargeAccounts>
      <root_data_memberRechargeAccounts_accounts Ref="11" type="DataTableSource" isKey="true">
        <Alias>root_data_memberRechargeAccounts_accounts</Alias>
        <Columns isList="true" count="27">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>no,System.String</value>
          <value>accNo,System.String</value>
          <value>accType,System.String</value>
          <value>rCode,System.String</value>
          <value>rNo,System.String</value>
          <value>guestName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>subType,System.String</value>
          <value>payCode,System.String</value>
          <value>accSetName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>accDetail,System.String</value>
          <value>shiftNo,System.String</value>
          <value>shiftName,System.String</value>
          <value>recorder,System.String</value>
          <value>bizDate,System.String</value>
          <value>state,System.String</value>
          <value>isGAcc,System.String</value>
          <value>remark,System.String</value>
          <value>createTime,System.DateTime</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>98f4c8032f8d4515be16b32d923612ee</Key>
        <Name>root_data_memberRechargeAccounts_accounts</Name>
        <NameInSource>handoverReport.root_data_memberRechargeAccounts_accounts</NameInSource>
      </root_data_memberRechargeAccounts_accounts>
      <root_data_paymentDetails Ref="12" type="DataTableSource" isKey="true">
        <Alias>root_data_paymentDetails</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>accounts,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>016087601a094657af60772ca4b2dede</Key>
        <Name>root_data_paymentDetails</Name>
        <NameInSource>handoverReport.root_data_paymentDetails</NameInSource>
      </root_data_paymentDetails>
      <root_data_paymentDetails_accounts Ref="13" type="DataTableSource" isKey="true">
        <Alias>root_data_paymentDetails_accounts</Alias>
        <Columns isList="true" count="27">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>no,System.String</value>
          <value>accNo,System.String</value>
          <value>accType,System.String</value>
          <value>rCode,System.String</value>
          <value>rNo,System.String</value>
          <value>guestName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>subType,System.String</value>
          <value>payCode,System.String</value>
          <value>accSetName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>accDetail,System.String</value>
          <value>shiftNo,System.String</value>
          <value>shiftName,System.String</value>
          <value>recorder,System.String</value>
          <value>bizDate,System.String</value>
          <value>state,System.String</value>
          <value>isGAcc,System.String</value>
          <value>remark,System.String</value>
          <value>createTime,System.DateTime</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>6c3db9ace9574b42ba71f1b984e0a3eb</Key>
        <Name>root_data_paymentDetails_accounts</Name>
        <NameInSource>handoverReport.root_data_paymentDetails_accounts</NameInSource>
      </root_data_paymentDetails_accounts>
      <root_data_paymentTypeAccounts Ref="14" type="DataTableSource" isKey="true">
        <Alias>root_data_paymentTypeAccounts</Alias>
        <Columns isList="true" count="27">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>no,System.String</value>
          <value>accNo,System.String</value>
          <value>accType,System.String</value>
          <value>rCode,System.String</value>
          <value>rNo,System.String</value>
          <value>guestName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>subType,System.String</value>
          <value>payCode,System.String</value>
          <value>accSetName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>accDetail,System.String</value>
          <value>shiftNo,System.String</value>
          <value>shiftName,System.String</value>
          <value>recorder,System.String</value>
          <value>bizDate,System.String</value>
          <value>state,System.String</value>
          <value>isGAcc,System.String</value>
          <value>remark,System.String</value>
          <value>createTime,System.DateTime</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>bd34225ec8bc462487d48eafa8d99d4d</Key>
        <Name>root_data_paymentTypeAccounts</Name>
        <NameInSource>handoverReport.root_data_paymentTypeAccounts</NameInSource>
      </root_data_paymentTypeAccounts>
      <root_data_recorderPaymentDetails Ref="15" type="DataTableSource" isKey="true">
        <Alias>root_data_recorderPaymentDetails</Alias>
        <Columns isList="true" count="5">
          <value>recorder,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>memberRechargeRmbPayTotalFee,System.Decimal</value>
          <value>accounts,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>dd8506a0e32941ad912183f8f635b502</Key>
        <Name>root_data_recorderPaymentDetails</Name>
        <NameInSource>handoverReport.root_data_recorderPaymentDetails</NameInSource>
      </root_data_recorderPaymentDetails>
      <root_data_recorderPaymentDetails_accounts Ref="16" type="DataTableSource" isKey="true">
        <Alias>root_data_recorderPaymentDetails_accounts</Alias>
        <Columns isList="true" count="7">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>totalFee,System.Decimal</value>
          <value>classCode,System.String</value>
          <value>className,System.String</value>
          <value>accounts,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>8dbc4cda794c4e72ae46893089615937</Key>
        <Name>root_data_recorderPaymentDetails_accounts</Name>
        <NameInSource>handoverReport.root_data_recorderPaymentDetails_accounts</NameInSource>
      </root_data_recorderPaymentDetails_accounts>
      <root_data_recorderPaymentDetails_accounts_accounts Ref="17" type="DataTableSource" isKey="true">
        <Alias>root_data_recorderPaymentDetails_accounts_accounts</Alias>
        <Columns isList="true" count="27">
          <value>id,System.Decimal</value>
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>no,System.String</value>
          <value>accNo,System.String</value>
          <value>accType,System.String</value>
          <value>rCode,System.String</value>
          <value>rNo,System.String</value>
          <value>guestName,System.String</value>
          <value>guestSrcType,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>subType,System.String</value>
          <value>payCode,System.String</value>
          <value>accSetName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>accDetail,System.String</value>
          <value>shiftNo,System.String</value>
          <value>shiftName,System.String</value>
          <value>recorder,System.String</value>
          <value>bizDate,System.String</value>
          <value>state,System.String</value>
          <value>isGAcc,System.String</value>
          <value>remark,System.String</value>
          <value>createTime,System.DateTime</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>7264222e50df40eab157af1e88ba1a87</Key>
        <Name>root_data_recorderPaymentDetails_accounts_accounts</Name>
        <NameInSource>handoverReport.root_data_recorderPaymentDetails_accounts_accounts</NameInSource>
      </root_data_recorderPaymentDetails_accounts_accounts>
    </DataSources>
    <Relations isList="true" count="14">
      <root Ref="18" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="19" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_arDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>arDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_arDetails Ref="20" type="DataRelation" isKey="true">
        <Alias>root_data_arDetails</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data_arDetails</Name>
        <NameInSource>root_data_arDetails_accounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>accounts</value>
        </ParentColumns>
        <ParentSource isRef="5" />
      </root_data_arDetails>
      <root_data Ref="21" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="7" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_consumptionDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>consumptionDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_consumptionDetails Ref="22" type="DataRelation" isKey="true">
        <Alias>root_data_consumptionDetails</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="8" />
        <Dictionary isRef="1" />
        <Name>root_data_consumptionDetails</Name>
        <NameInSource>root_data_consumptionDetails_accounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>accounts</value>
        </ParentColumns>
        <ParentSource isRef="7" />
      </root_data_consumptionDetails>
      <root_data Ref="23" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="9" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_consumptionTypeAccounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>consumptionTypeAccounts</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="24" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="10" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_memberRechargeAccounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>memberRechargeAccounts</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_memberRechargeAccounts Ref="25" type="DataRelation" isKey="true">
        <Alias>root_data_memberRechargeAccounts</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="11" />
        <Dictionary isRef="1" />
        <Name>root_data_memberRechargeAccounts</Name>
        <NameInSource>root_data_memberRechargeAccounts_accounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>accounts</value>
        </ParentColumns>
        <ParentSource isRef="10" />
      </root_data_memberRechargeAccounts>
      <root_data Ref="26" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="12" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_paymentDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>paymentDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_paymentDetails Ref="27" type="DataRelation" isKey="true">
        <Alias>root_data_paymentDetails</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="13" />
        <Dictionary isRef="1" />
        <Name>root_data_paymentDetails</Name>
        <NameInSource>root_data_paymentDetails_accounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>accounts</value>
        </ParentColumns>
        <ParentSource isRef="12" />
      </root_data_paymentDetails>
      <root_data Ref="28" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="14" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_paymentTypeAccounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>paymentTypeAccounts</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="29" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="15" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_recorderPaymentDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>recorderPaymentDetails</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_recorderPaymentDetails Ref="30" type="DataRelation" isKey="true">
        <Alias>root_data_recorderPaymentDetails</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="16" />
        <Dictionary isRef="1" />
        <Name>root_data_recorderPaymentDetails</Name>
        <NameInSource>root_data_recorderPaymentDetails_accounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>accounts</value>
        </ParentColumns>
        <ParentSource isRef="15" />
      </root_data_recorderPaymentDetails>
      <root_data_recorderPaymentDetails_accounts Ref="31" type="DataRelation" isKey="true">
        <Alias>root_data_recorderPaymentDetails_accounts</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="17" />
        <Dictionary isRef="1" />
        <Name>root_data_recorderPaymentDetails_accounts</Name>
        <NameInSource>root_data_recorderPaymentDetails_accounts_accounts</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>accounts</value>
        </ParentColumns>
        <ParentSource isRef="16" />
      </root_data_recorderPaymentDetails_accounts>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="1">
      <value>Name=test, Alias=test, Type=Json, Content=XxyQoEmRgQaI6LbklQ8KLS1qlI8h2d7HGvmPZXdMzqZ0es047_x002B_l_x002B_NNIY5uU3GUZLIWy5IeMqOmGNn7hcP_x002F_zWs7mY5lr_x002B_kq0HcryNGBJIZpzzvH9BSDqfOvRYLsQzch6JW_x002F_DdwytZlZj7o_x002F_pJfQDBP7_x002B_aMAXLmlnE8dz3EgyGoOogeR627aWHdw3gbo0jbjZH6mb4FAZzrrN1ur00QTB9eS9DOWzPeC9IesOsRVhifQ_x002B_DhUL6QVyTAxREUp525_x002B_wssnynolMQA7G3g4PpidATuk0iuRiZwszj_x002B_iRQB51JO6qt5rcUjerNKdBV_x002B_OQF2SJrqzr9D2y1YcwClLgkEuUip336CBHCWDLGG43YR8TutLCPtvHjoF2O_x002F_E40ltSv6Tnh6ShTxsZ91ZMnJCaCRoy8rhNoZu2hAYYmO_x002B_dcg3VPvgxynrsqv6xsJ0ibu_x002F_Xb6X2qM6W8O_x002F_q00RRqHf9p_x002B_DLGHGuxEiCE2AjnPUHxRRBopxuGxENQtyyCd60nJ5AdJbuSN5Ecn75t5vcMH7MMJ2IcqACD0r1Q3t1biyhyz7SPlS2TourbiKxFv3RVrGOVjggIlPb2CFr1oQuSFGOWk1eFwlEcYkz3XIvFSD_x002B_FCioF3a8mOYr6RT48n_x002B_jgOS0KBNcdCOxmxGTaAZlocA8dzpVRMfuaGxjJ_x002B_iVJslDLIQVc6sATMJxGeURdBKXiKmlNZFolEz8Qv_x002F_ZEulSUWuePYEmQuoWkvziHqfYxqbBjVZXMuJI4_x002B_u51bRkk_x002B_xNwLG6l_x002F_VKMBo8QB_x002B_W8yZST3L7RgtMdBPEDgEDnVsTV9ZN97y5lC5WjUdW4jmCNKEF9l_x002F_b4MEu_x002F_jR6BtIcmuKu3QitbiS1MlbbsuzfrH5oii3a1G1g9WzIJrcudLvZPzTiRAHQvNIY02olfzkrMWzBb63_x002F_d1lgyQl9IA5r7plr0vGOAXuZ7InBqIEpCiTF99Ld7o_x002F_bf48txQ0YGkXnGo2TMdSqWmBGWh6DfXfOaKOeEwQswXxw_x002F_hXbTFGqXLZTYziHzhmOW7LflGebY8r3kijM2T382_x002F__x002B_cDEe8jj7rCKGraq_x002B_2bHkHPVceS7QlDE8nYaHOV6Y9qnKZstj7XKyIcZYpuY2JeN1bq9DDydkVsz1DlMhfHB2Nu2RjSAVCR6_x002B_vlLJ61dgYoFxgkv3ft3hSWAGRsGUW5cD3eLw6W67tCXRd_x002F_h5lcIfHlo9MQpz0il4HrDtQtb7VzEXaX8YyFWw_x002B_VA35YGWdmTX5wEjYn7_x002F_wIgqp7CwBy6tocvU1AkKJwQW0LnbReYGx87d_x002F_BGE2_x002B_MhsM0Xvq_x002B_xmp8ns0VV5fq8s96bBdIvgkMabBJcFEpZ8W8prrvDCgyJRqQ6jvdmdvSmEMQfpFPnfhbMwkLb1Wm6hGuDPmup0hXxuTHjRqLGbO9aY4zNJMi3Y6Qdkt3oyU8SxT6ntJBp_x002F_2HeXnjk9cGTocPR48HkSfhYhZGtOLPoLwGpPQmWS7dOc_x002F_Gq_x002B_LeaGfIQS8bRub_x002F_tc5qJt5nUYbC42EdyTNR5CKYX5nvnJu3t2B_x002F_s8GfgKRSswoSdYjmfcXuVdPXO_x002F_0PcNnRjobXKILfJJ9km1dhr4jj_x002B_wRz66KfbgPx3sp5Y_x002F_J91wtJdHjDlJAVvSDnqg5F07dvywH6pyvZgoIbuQmtHu9Qd5EgMH49i6DiUZ_x002F_yVfgCJ3Z_x002B_h0XLhO_x002F_</value>
    </Resources>
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="1">
      <value>分类</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>56472f412a394d89b25796448fb199b9</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="32" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="35">
        <PageHeaderBand1 Ref="33" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,20,0.2</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </PageHeaderBand1>
        <PageFooterBand1 Ref="34" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,56.6,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text14 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.18,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text14</Name>
              <Page isRef="32" />
              <Parent isRef="34" />
              <Text>{root_data.operator_}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text41 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.5,0.18,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text41</Name>
              <Page isRef="32" />
              <Parent isRef="34" />
              <Text>{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text41>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </PageFooterBand1>
        <Text16 Ref="37" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>-2.43,15.2,1,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>黑体,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text16</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Text>{Line}</Text>
          <TextBrush>Black</TextBrush>
          <Type>SystemVariables</Type>
          <VertAlignment>Center</VertAlignment>
        </Text16>
        <Text58 Ref="38" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>-30.59,2,2.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>黑体,12</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text58</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Text>交班报表</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text58>
        <Text59 Ref="39" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>-32.6,3.2,6.99,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>黑体,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text59</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Text>营业日:{root_data.startTime} - {root_data.endTime}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text59>
        <Text60 Ref="40" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>-32.6,3.8,6.99,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>黑体,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text60</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Text>最后查询时间：{root_data.lastSelectTime}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text60>
        <Text61 Ref="41" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>-30.59,2.6,2.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>黑体,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text61</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Text>{root_data.hname}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text61>
        <ReportTitleBand1 Ref="42" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.4,20,2.4</ClientRectangle>
          <Components isList="true" count="4">
            <Text1 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.21,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="32" />
              <Parent isRef="42" />
              <Text>交班报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text3 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,1.2,10.99,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="32" />
              <Parent isRef="42" />
              <Text>营业日:{root_data.startDate.Substring(0, 10)} - {root_data.endDate.Substring(0, 10)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.8,6.99,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text4</Name>
              <Page isRef="32" />
              <Parent isRef="42" />
              <Text>最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text2 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.21,0.6,4.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text2</Name>
              <Page isRef="32" />
              <Parent isRef="42" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="47" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.6,20,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text22 Ref="48" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,20,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text22</Name>
              <Page isRef="32" />
              <Parent isRef="47" />
              <Text>本班账务发生合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand1>
        <HeaderBand2 Ref="49" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.2,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text23 Ref="50" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text23</Name>
              <Page isRef="32" />
              <Parent isRef="49" />
              <Text>发生金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text26 Ref="51" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text26</Name>
              <Page isRef="32" />
              <Parent isRef="49" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text24 Ref="52" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text24</Name>
              <Page isRef="32" />
              <Parent isRef="49" />
              <Text>消费科目名称</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand2</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand2>
        <DataBand6 Ref="53" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.8,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text27 Ref="54" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text27</Name>
              <Page isRef="32" />
              <Parent isRef="53" />
              <Text>{root_data_consumptionDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="55" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Underline</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.endDate}&amp;subCode={root_data_consumptionDetails.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="32" />
              <Parent isRef="53" />
              <Text>{root_data_consumptionDetails.totalFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="56" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <State>DecimalDigits</State>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text30 Ref="57" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text30</Name>
              <Page isRef="32" />
              <Parent isRef="53" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_consumptionDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand6</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Sort isList="true" count="0" />
        </DataBand6>
        <FooterBand2 Ref="58" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.4,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text31 Ref="59" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Underline</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.endDate}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="32" />
              <Parent isRef="58" />
              <Text>{root_data.consumptionTypeTotalFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="60" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="61" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text32</Name>
              <Page isRef="32" />
              <Parent isRef="58" />
              <Text>消费合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand2</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </FooterBand2>
        <HeaderBand3 Ref="62" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text21 Ref="63" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text21</Name>
              <Page isRef="32" />
              <Parent isRef="62" />
              <Text>发生金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text33 Ref="64" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text33</Name>
              <Page isRef="32" />
              <Parent isRef="62" />
              <Text>收款科目名称</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text35 Ref="65" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text35</Name>
              <Page isRef="32" />
              <Parent isRef="62" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand3</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand3>
        <GroupHeaderBand3 Ref="66" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,12.6,20,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{root_data_paymentDetails.classCode}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand3</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </GroupHeaderBand3>
        <DataBand4 Ref="67" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,13.4,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text10 Ref="68" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text10</Name>
              <Page isRef="32" />
              <Parent isRef="67" />
              <Text>{root_data_paymentDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text40 Ref="69" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Underline</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.endDate}&amp;subCode={root_data_paymentDetails.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="32" />
              <Parent isRef="67" />
              <Text>{root_data_paymentDetails.totalFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="70" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text69 Ref="71" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text69</Name>
              <Page isRef="32" />
              <Parent isRef="67" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_paymentDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand4</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Sort isList="true" count="0" />
        </DataBand4>
        <GroupFooterBand3 Ref="72" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,15,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text38 Ref="73" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Underline</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Interaction Ref="74" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <DrillDownEnabled>True</DrillDownEnabled>
                <DrillDownMode>SinglePage</DrillDownMode>
                <DrillDownParameter1 Ref="75" type="Stimulsoft.Report.Components.StiDrillDownParameter" isKey="true">
                  <Expression>root_data_recorderPaymentDetails_accounts.subCode</Expression>
                  <Name>subCode</Name>
                </DrillDownParameter1>
                <DrillDownParameter2 Ref="76" type="Stimulsoft.Report.Components.StiDrillDownParameter" isKey="true">
                  <Expression>root_data_recorderPaymentDetails_accounts.root_data_recorderPaymentDetails.recorder</Expression>
                  <Name>recorder</Name>
                </DrillDownParameter2>
                <DrillDownReport>C:\Users\<USER>\OneDrive\桌面\Report03.mrt</DrillDownReport>
              </Interaction>
              <Margins>0,5,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="32" />
              <Parent isRef="72" />
              <Text>{Sum(root_data_paymentDetails.totalFee)}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="77" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="78" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text39</Name>
              <Page isRef="32" />
              <Parent isRef="72" />
              <Text>{root_data_paymentDetails.className}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand3</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </GroupFooterBand3>
        <FooterBand4 Ref="79" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,16.6,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text73 Ref="80" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Underline</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.endDate}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="32" />
              <Parent isRef="79" />
              <Text>{root_data.paymentTypeTotalFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="81" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text75 Ref="82" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text75</Name>
              <Page isRef="32" />
              <Parent isRef="79" />
              <Text>收款合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand4</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </FooterBand4>
        <FooterBand3 Ref="83" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.2,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text5 Ref="84" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="32" />
              <Parent isRef="83" />
              <Text>{root_data.rmbPayTotalFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="85" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="86" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="32" />
              <Parent isRef="83" />
              <Text>现金交款</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand3</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </FooterBand3>
        <HeaderBand4 Ref="87" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,19.8,20,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text7 Ref="88" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,20,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text7</Name>
              <Page isRef="32" />
              <Parent isRef="87" />
              <Text>本班收银员收款分类汇总</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand4</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand4>
        <HeaderBand5 Ref="89" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,21.4,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text15 Ref="90" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text15</Name>
              <Page isRef="32" />
              <Parent isRef="89" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text8 Ref="91" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text8</Name>
              <Page isRef="32" />
              <Parent isRef="89" />
              <Text>发生金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text13 Ref="92" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text13</Name>
              <Page isRef="32" />
              <Parent isRef="89" />
              <Text>收款科目名称</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand5</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand5>
        <GroupHeaderBand1 Ref="93" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,23,20,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text20 Ref="94" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,20,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="32" />
              <Parent isRef="93" />
              <Text>收银员：{root_data_recorderPaymentDetails_accounts.root_data_recorderPaymentDetails.recorder}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
          </Components>
          <Condition>{root_data_recorderPaymentDetails_accounts.root_data_recorderPaymentDetails.recorder}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </GroupHeaderBand1>
        <GroupHeaderBand4 Ref="95" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,24.6,20,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{root_data_paymentDetails.classCode}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand4</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </GroupHeaderBand4>
        <DataBand11 Ref="96" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,25.4,20,0.82</ClientRectangle>
          <Components isList="true" count="3">
            <Text18 Ref="97" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text18</Name>
              <Page isRef="32" />
              <Parent isRef="96" />
              <Text>{root_data_recorderPaymentDetails_accounts.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text44 Ref="98" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Underline</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.endDate}&amp;subCode={root_data_recorderPaymentDetails_accounts.subCode}&amp;operator={root_data_recorderPaymentDetails_accounts.root_data_recorderPaymentDetails.recorder}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="32" />
              <Parent isRef="96" />
              <Text>{root_data_recorderPaymentDetails_accounts.totalFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="99" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text47 Ref="100" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text47</Name>
              <Page isRef="32" />
              <Parent isRef="96" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_recorderPaymentDetails_accounts</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand11</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Sort isList="true" count="0" />
        </DataBand11>
        <GroupFooterBand1 Ref="101" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,27.02,20,1.6</ClientRectangle>
          <Components isList="true" count="4">
            <Text11 Ref="102" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0.8,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text11</Name>
              <Page isRef="32" />
              <Parent isRef="101" />
              <Text>现金交款</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="103" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0.8,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="32" />
              <Parent isRef="101" />
              <Text>{root_data_recorderPaymentDetails_accounts.root_data_recorderPaymentDetails.memberRechargeRmbPayTotalFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="104" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text17 Ref="105" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text17</Name>
              <Page isRef="32" />
              <Parent isRef="101" />
              <Text>收款合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text42 Ref="106" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Underline</Font>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.startDate}&amp;endDate={root_data.endDate}&amp;operator={root_data_recorderPaymentDetails_accounts.root_data_recorderPaymentDetails.recorder}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>0,5,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="32" />
              <Parent isRef="101" />
              <Text>{root_data_recorderPaymentDetails_accounts.root_data_recorderPaymentDetails.totalFee}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextFormat Ref="107" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </GroupFooterBand1>
        <HeaderBand6 Ref="108" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,29.42,20,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text62 Ref="109" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,20,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text62</Name>
              <Page isRef="32" />
              <Parent isRef="108" />
              <Text>本班收银员AR账收款分类汇总</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand6</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand6>
        <HeaderBand7 Ref="110" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,31.02,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text63 Ref="111" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text63</Name>
              <Page isRef="32" />
              <Parent isRef="110" />
              <Text>AR账户</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="112" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text64</Name>
              <Page isRef="32" />
              <Parent isRef="110" />
              <Text>发生金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text66 Ref="113" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.61,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text66</Name>
              <Page isRef="32" />
              <Parent isRef="110" />
              <Text>收款科目名称</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand7</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand7>
        <GroupHeaderBand2 Ref="114" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,32.62,20,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text67 Ref="115" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,20,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Name>Text67</Name>
              <Page isRef="32" />
              <Parent isRef="114" />
              <Text>{root_data_arDetails.recorder}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
          </Components>
          <Condition>{root_data_arDetails.recorder}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </GroupHeaderBand2>
        <DataBand1 Ref="116" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,34.22,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text76 Ref="117" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text76</Name>
              <Page isRef="32" />
              <Parent isRef="116" />
              <Text>{root_data_arDetails_accounts.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text77 Ref="118" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Interaction Ref="119" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <DrillDownMode>SinglePage</DrillDownMode>
                <DrillDownReport>C:\Users\<USER>\OneDrive\桌面\Report03.mrt</DrillDownReport>
              </Interaction>
              <Margins>0,5,0,0</Margins>
              <Name>Text77</Name>
              <Page isRef="32" />
              <Parent isRef="116" />
              <Text>{root_data_arDetails_accounts.fee}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="120" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
            <Text78 Ref="121" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text78</Name>
              <Page isRef="32" />
              <Parent isRef="116" />
              <Text>{root_data_arDetails_accounts.arSetName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text78>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_arDetails_accounts</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand2 Ref="122" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,35.82,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text72 Ref="123" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text72</Name>
              <Page isRef="32" />
              <Parent isRef="122" />
              <Text>收款合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text74 Ref="124" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Interaction Ref="125" type="Stimulsoft.Report.Components.StiInteraction" isKey="true">
                <DrillDownReport>C:\Users\<USER>\OneDrive\桌面\Report03.mrt</DrillDownReport>
              </Interaction>
              <Margins>0,5,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="32" />
              <Parent isRef="122" />
              <Text>{root_data_arDetails.totalFee}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextFormat Ref="126" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </GroupFooterBand2>
        <DataBand3 Ref="127" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,37.42,20,0.4</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand3</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Sort isList="true" count="0" />
        </DataBand3>
        <HeaderBand8 Ref="128" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,38.62,20,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text57 Ref="129" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,-0.02,20,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text57</Name>
              <Page isRef="32" />
              <Parent isRef="128" />
              <Text>收款科目(会员充值收款)</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand8</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand8>
        <HeaderBand9 Ref="130" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,40.22,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text43 Ref="131" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text43</Name>
              <Page isRef="32" />
              <Parent isRef="130" />
              <Text>发生金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text45 Ref="132" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text45</Name>
              <Page isRef="32" />
              <Parent isRef="130" />
              <Text>收款科目名称</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text48 Ref="133" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text48</Name>
              <Page isRef="32" />
              <Parent isRef="130" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand9</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </HeaderBand9>
        <DataBand2 Ref="134" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,41.82,20,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text49 Ref="135" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.6,0,8.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text49</Name>
              <Page isRef="32" />
              <Parent isRef="134" />
              <Text>{root_data_memberRechargeAccounts.subName}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="136" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="32" />
              <Parent isRef="134" />
              <Text>{root_data_memberRechargeAccounts.totalFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="137" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text52 Ref="138" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text52</Name>
              <Page isRef="32" />
              <Parent isRef="134" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_memberRechargeAccounts</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand2</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
          <Sort isList="true" count="0" />
        </DataBand2>
        <FooterBand6 Ref="139" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,43.42,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text55 Ref="140" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="32" />
              <Parent isRef="139" />
              <Text>{root_data_memberRechargeAccounts.root_data.memberRechargeTotalFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="141" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
            <Text56 Ref="142" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text56</Name>
              <Page isRef="32" />
              <Parent isRef="139" />
              <Text>收款合计（会员充值收款合计）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand6</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </FooterBand6>
        <FooterBand5 Ref="143" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,45.02,20,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text53 Ref="144" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="32" />
              <Parent isRef="143" />
              <Text>{root_data_memberRechargeAccounts.root_data.memberRechargeRmbPayTotalFee}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="145" type="CurrencyFormat" isKey="true">
                <DecimalDigits>2</DecimalDigits>
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>2</NegativePattern>
                <PositivePattern>0</PositivePattern>
                <Symbol>¥</Symbol>
              </TextFormat>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text54 Ref="146" type="Text" isKey="true">
              <Border>All;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,14.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Name>Text54</Name>
              <Page isRef="32" />
              <Parent isRef="143" />
              <Text>现金交款（会员充值收款）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand5</Name>
          <Page isRef="32" />
          <Parent isRef="32" />
        </FooterBand5>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>7e5acb2f39954398ae7204d31a3306dd</Guid>
      <Margins>0.5,0.5,0.5,0.5</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportAuthor>Z zx</ReportAuthor>
  <ReportChanged>5/30/2025 9:41:13 AM</ReportChanged>
  <ReportCreated>7/4/2024 10:42:15 AM</ReportCreated>
  <ReportFile>D:\11\2\aflower-pms-front\public\reports\handoverReport.mrt</ReportFile>
  <ReportGuid>566660fe0d6045b3ae2b99ccebbfa4da</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="2">
    <样式1 Ref="147" type="Stimulsoft.Report.StiStyle" isKey="true">
      <Brush>Transparent</Brush>
      <Conditions isList="true" count="0" />
      <Font>Arial,8</Font>
      <Name>样式1</Name>
      <NegativeTextBrush>Red</NegativeTextBrush>
      <TextBrush>Black</TextBrush>
    </样式1>
    <样式2 Ref="148" type="Stimulsoft.Report.StiTableStyle" isKey="true">
      <AlternatingDataColor>238, 238, 238</AlternatingDataColor>
      <AlternatingDataForeground>34, 34, 34</AlternatingDataForeground>
      <BackColor>White</BackColor>
      <Conditions isList="true" count="0" />
      <DataBarsNegative>255, 0, 0</DataBarsNegative>
      <DataBarsOverlapped>51, 94, 150</DataBarsOverlapped>
      <DataBarsPositive>99, 142, 198</DataBarsPositive>
      <DataColor>180, 178, 178, 178</DataColor>
      <DataForeground>180, 38, 68, 120</DataForeground>
      <FooterColor>180, 35, 159, 217</FooterColor>
      <FooterForeground>White</FooterForeground>
      <GridColor>White</GridColor>
      <HeaderColor>180, 35, 159, 217</HeaderColor>
      <HeaderForeground>White</HeaderForeground>
      <HotHeaderColor>Transparent</HotHeaderColor>
      <IndicatorNegative>Red</IndicatorNegative>
      <IndicatorNeutral>LightGray</IndicatorNeutral>
      <IndicatorPositive>Green</IndicatorPositive>
      <Name>样式2</Name>
      <SelectedDataColor>52, 152, 219</SelectedDataColor>
      <SelectedDataForeground>White</SelectedDataForeground>
      <Sparkline>83, 126, 182</Sparkline>
      <WinLossNegative>255, 0, 0</WinLossNegative>
      <WinLossPositive>99, 142, 198</WinLossPositive>
    </样式2>
  </Styles>
</StiSerializer>