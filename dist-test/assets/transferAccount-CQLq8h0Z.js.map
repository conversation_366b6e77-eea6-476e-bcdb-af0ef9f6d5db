{"version": 3, "file": "transferAccount-CQLq8h0Z.js", "sources": ["../../src/views/order/info/components/orderdetail/transferAccount.vue"], "sourcesContent": ["/** * @description: 转账组件 */\r\n<i18n lang=\"json\">\r\n{\r\n  \"en\": {\r\n    \"dialog\": {\r\n      \"title\": \"Transfer Account\"\r\n    },\r\n    \"table\": {\r\n      \"subject\": \"Subject\",\r\n      \"consumption\": \"Consumption\",\r\n      \"payment\": \"Payment\",\r\n      \"businessDate\": \"Business Date\",\r\n      \"roomGuest\": \"Room/Guest\",\r\n      \"operator\": \"Operator\",\r\n      \"shift\": \"Shift\"\r\n    },\r\n    \"form\": {\r\n      \"transferAccount\": \"Transfer Account\",\r\n      \"selectAccount\": \"Please select account\",\r\n      \"remark\": \"Remark\",\r\n      \"enterRemark\": \"Please enter remark\"\r\n    },\r\n    \"summary\": {\r\n      \"consumption\": \"Consumption\",\r\n      \"payment\": \"Payment\",\r\n      \"total\": \"Total\"\r\n    },\r\n    \"buttons\": {\r\n      \"cancel\": \"Cancel\",\r\n      \"confirm\": \"Confirm\"\r\n    },\r\n    \"validation\": {\r\n      \"transferAccountRequired\": \"Please enter transfer account\",\r\n      \"remarkRequired\": \"Please enter remark\"\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"dialog\": {\r\n      \"title\": \"转账\"\r\n    },\r\n    \"table\": {\r\n      \"subject\": \"科目\",\r\n      \"consumption\": \"消费\",\r\n      \"payment\": \"付款\",\r\n      \"businessDate\": \"营业日期\",\r\n      \"roomGuest\": \"房号/姓名\",\r\n      \"operator\": \"操作人\",\r\n      \"shift\": \"班次\"\r\n    },\r\n    \"form\": {\r\n      \"transferAccount\": \"转入账户\",\r\n      \"selectAccount\": \"请选择账户\",\r\n      \"remark\": \"备注\",\r\n      \"enterRemark\": \"请输入备注\"\r\n    },\r\n    \"summary\": {\r\n      \"consumption\": \"消费\",\r\n      \"payment\": \"付款\",\r\n      \"total\": \"合计\"\r\n    },\r\n    \"buttons\": {\r\n      \"cancel\": \"取消\",\r\n      \"confirm\": \"确定\"\r\n    },\r\n    \"validation\": {\r\n      \"transferAccountRequired\": \"请输入转入账户\",\r\n      \"remarkRequired\": \"请输入备注\"\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"dialog\": {\r\n      \"title\": \"ផ្ទេរគណនី\"\r\n    },\r\n    \"table\": {\r\n      \"subject\": \"មុខវិជ្ជា\",\r\n      \"consumption\": \"ការប្រើប្រាស់\",\r\n      \"payment\": \"ការទូទាត់\",\r\n      \"businessDate\": \"កាលបរិច្ឆេទអាជីវកម្ម\",\r\n      \"roomGuest\": \"បន្ទប់/ភ្ញៀវ\",\r\n      \"operator\": \"អ្នកប្រតិបត្តិ\",\r\n      \"shift\": \"វេនការងារ\"\r\n    },\r\n    \"form\": {\r\n      \"transferAccount\": \"គណនីផ្ទេរចូល\",\r\n      \"selectAccount\": \"សូមជ្រើសរើសគណនី\",\r\n      \"remark\": \"កំណត់ចំណាំ\",\r\n      \"enterRemark\": \"សូមបញ្ចូលកំណត់ចំណាំ\"\r\n    },\r\n    \"summary\": {\r\n      \"consumption\": \"ការប្រើប្រាស់\",\r\n      \"payment\": \"ការទូទាត់\",\r\n      \"total\": \"សរុប\"\r\n    },\r\n    \"buttons\": {\r\n      \"cancel\": \"បោះបង់\",\r\n      \"confirm\": \"បញ្ជាក់\"\r\n    },\r\n    \"validation\": {\r\n      \"transferAccountRequired\": \"សូមបញ្ចូលគណនីផ្ទេរ\",\r\n      \"remarkRequired\": \"សូមបញ្ចូលកំណត់ចំណាំ\"\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormRules } from 'element-plus'\r\nimport { accountApi, orderApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    /** 账单号列表 */\r\n    accNos: string[]\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  accNos: props.accNos,\r\n  /** 账务表 单号 */\r\n  no: '',\r\n  remark: '',\r\n})\r\n/** 账务列表 */\r\nconst accounts = reactive({\r\n  list: [] as {\r\n    /** 账号 */\r\n    accNo: ''\r\n    /** 房号 */\r\n    rNo: ''\r\n    /** 客人姓名 */\r\n    guestName: ''\r\n    /** 金额 */\r\n    fee: ''\r\n    /** 消费科目 */\r\n    subCode: ''\r\n    /** 消费科目名称 */\r\n    subName: ''\r\n    /** 科目类型 0消费 1付款 */\r\n    subType: ''\r\n    /** 入账营业日 */\r\n    bizDate: ''\r\n    creator: ''\r\n    createTime: ''\r\n    handleShiftNo: ''\r\n  }[],\r\n  /** 消费 */\r\n  consumeSum: 0,\r\n  /** 付款 */\r\n  paySum: 0,\r\n  /** 余额合计 */\r\n  balanceSum: 0,\r\n})\r\n\r\nconst accList = reactive({\r\n  list: [] as {\r\n    /** 房号 */\r\n    rNo: string\r\n    /** 房号 */\r\n    orderNo: string | number\r\n    guestName: string\r\n  }[],\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  no: [{ required: true, message: t('validation.transferAccountRequired'), trigger: 'blur' }],\r\n  remark: [{ required: true, message: t('validation.remarkRequired'), trigger: 'blur' }],\r\n})\r\nonMounted(() => {\r\n  getAccountsByAccNos()\r\n  getAccList()\r\n})\r\n/**\r\n * 获取账号列表\r\n */\r\nfunction getAccList() {\r\n  orderApi.getInAccList({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      accList.list = res.data.list\r\n    }\r\n  })\r\n}\r\n\r\nfunction getAccountsByAccNos() {\r\n  accountApi.listByAccNos({ gcode: userStore.gcode, hcode: userStore.hcode, accNos: props.accNos }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      accounts.list = res.data.list\r\n      accounts.consumeSum = res.data.consumeSum\r\n      accounts.paySum = res.data.paySum\r\n      accounts.balanceSum = res.data.balanceSum\r\n    }\r\n  })\r\n}\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction onSubmit() {}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" :title=\"t('dialog.title')\" width=\"800px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\">\r\n      <el-table :data=\"accounts.list\" stripe style=\"width: 100%\">\r\n        <el-table-column prop=\"subName\" :label=\"t('table.subject')\" />\r\n        <el-table-column :label=\"t('table.consumption')\">\r\n          <template #default=\"scope\">\r\n            <span v-if=\"scope.row.subType === '0'\">￥{{ scope.row.fee }}</span>\r\n            <span v-else>--</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('table.payment')\">\r\n          <template #default=\"scope\">\r\n            <span v-if=\"scope.row.subType === '1'\">￥{{ scope.row.fee }}</span>\r\n            <span v-else>--</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('table.businessDate')\">\r\n          <template #default=\"scope\">\r\n            {{ dayjs(scope.row.bizDate).format('MM-DD') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('table.roomGuest')\">\r\n          <template #default=\"scope\">\r\n            {{ scope.row.rNo }}\r\n            <br />\r\n            {{ scope.row.guestName }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('table.operator')\">\r\n          <template #default=\"scope\">\r\n            {{ scope.row.creator }}\r\n            <br />\r\n            {{ dayjs(scope.row.createTime).format('MM-DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"handleShiftNo\" :label=\"t('table.shift')\" />\r\n      </el-table>\r\n      <el-form :model=\"form\" :rules=\"formRules\" label-width=\"100px\" label-suffix=\"：\" style=\"margin-top: 10px\">\r\n        <el-form-item :label=\"t('form.transferAccount')\" prop=\"no\">\r\n          <el-select v-model=\"form.no\" :placeholder=\"t('form.selectAccount')\">\r\n            <el-option v-for=\"item in accList.list\" :key=\"item.orderNo\" :label=\"`${item.rNo}-${item.guestName}`\" :value=\"item.orderNo\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('form.remark')\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" :placeholder=\"t('form.enterRemark')\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-row style=\"align-items: center; height: 40px; margin-right: -20px; margin-left: -20px; background-color: rgb(238 235 235)\">\r\n        <el-col :span=\"12\" style=\"padding-left: 40px; text-align: left\">\r\n          <span style=\"margin-right: 10px\">{{ t('summary.consumption') }}：{{ accounts.consumeSum }}</span>\r\n          <span>{{ t('summary.payment') }}：{{ accounts.paySum }}</span>\r\n        </el-col>\r\n        <el-col :span=\"12\" style=\"padding-right: 40px; text-align: right\"> {{ t('summary.total') }}：{{ accounts.balanceSum }} </el-col>\r\n      </el-row>\r\n      <template #footer>\r\n        <el-button @click=\"onCancel\">\r\n          {{ t('buttons.cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"onSubmit\">\r\n          {{ t('buttons.confirm') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sum {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "form", "ref", "gcode", "hcode", "accNos", "no", "remark", "accounts", "reactive", "list", "consumeSum", "paySum", "balanceSum", "accList", "formRules", "required", "message", "trigger", "onMounted", "accountApi", "listByAccNos", "then", "res", "code", "data", "orderApi", "getInAccList", "myVisible", "computed", "get", "modelValue", "set", "val", "onCancel", "value", "onSubmit"], "mappings": "ylCA+GA,MAAMA,EAAQC,EAURC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAOC,EAAI,CACfC,MAAOJ,EAAUI,MACjBC,MAAOL,EAAUK,MACjBC,OAAQZ,EAAMY,OAEdC,GAAI,GACJC,OAAQ,KAGJC,EAAWC,EAAS,CACxBC,KAAM,GAsBNC,WAAY,EAEZC,OAAQ,EAERC,WAAY,IAGRC,EAAUL,EAAS,CACvBC,KAAM,KASFK,EAAYb,EAAe,CAC/BI,GAAI,CAAC,CAAEU,UAAU,EAAMC,QAASpB,EAAE,sCAAuCqB,QAAS,SAClFX,OAAQ,CAAC,CAAES,UAAU,EAAMC,QAASpB,EAAE,6BAA8BqB,QAAS,WAE/EC,GAAU,KAgBRC,EAAWC,aAAa,CAAElB,MAAOJ,EAAUI,MAAOC,MAAOL,EAAUK,MAAOC,OAAQZ,EAAMY,SAAUiB,MAAMC,IACrF,IAAbA,EAAIC,OACGhB,EAAAE,KAAOa,EAAIE,KAAKf,KAChBF,EAAAG,WAAaY,EAAIE,KAAKd,WACtBH,EAAAI,OAASW,EAAIE,KAAKb,OAClBJ,EAAAK,WAAaU,EAAIE,KAAKZ,WAAA,IAbnCa,EAASC,aAAa,CAAExB,MAAOJ,EAAUI,MAAOC,MAAOL,EAAUK,QAASkB,MAAMC,IAC7D,IAAbA,EAAIC,OACEV,EAAAJ,KAAOa,EAAIE,KAAKf,KAAA,GARjB,IAwBb,MAAMkB,EAAYC,EAAS,CACzBC,IAAM,IACGrC,EAAMsC,WAEf,GAAAC,CAAIC,GACFtC,EAAM,oBAAqBsC,EAAG,IAIlC,SAASC,IACPN,EAAUO,OAAQ,CAAA,CAGpB,SAASC,IAAW"}