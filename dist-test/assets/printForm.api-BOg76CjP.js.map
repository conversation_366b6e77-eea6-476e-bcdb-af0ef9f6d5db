{"version": 3, "file": "printForm.api-BOg76CjP.js", "sources": ["../../src/api/modules/system/print/printForm.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/print'\r\n\r\n/**\r\n * 打印格式接口\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 批量更新打印格式\r\n   * @param data\r\n   */\r\n  printCheckInForm: (data: any) => {\r\n    return api.get(`${BASE_PATH}/checkin-form`, { params: data })\r\n  },\r\n\r\n  /**\r\n   * 单个房间账单(房间下所有账务)\r\n   * @param data\r\n   */\r\n  printSingleRoomBill: (data: any) => {\r\n    return api.get(`${BASE_PATH}/single-room-bill`, { params: data })\r\n  },\r\n\r\n  /**\r\n   * 联房账单\r\n   * @param data\r\n   */\r\n  printLinkRoomBill: (data: any) => {\r\n    return api.get(`${BASE_PATH}/link-room-bill`, { params: data })\r\n  },\r\n\r\n  /**\r\n   * 打印宾客账单(宾客下所有账务)\r\n   * @param data\r\n   */\r\n  printTogetherBill: (data: any) => {\r\n    return api.get(`${BASE_PATH}/bill-by-together`, { params: data })\r\n  },\r\n\r\n  /**\r\n   * 打印团队/联房入住登记单\r\n   * @param data\r\n   */\r\n  printMergeForm: (data: any) => {\r\n    return api.get(`${BASE_PATH}/merge-form`, { params: data })\r\n  },\r\n}\r\n"], "names": ["BASE_PATH", "printFormApi", "printCheckInForm", "data", "api", "get", "params", "printSingleRoomBill", "printLinkRoomBill", "printTogetherBill", "printMergeForm"], "mappings": "wCAEA,MAAMA,EAAY,sBAKHC,EAAA,CAMbC,iBAAmBC,GACVC,EAAIC,IAAI,GAAGL,iBAA0B,CAAEM,OAAQH,IAOxDI,oBAAsBJ,GACbC,EAAIC,IAAI,GAAGL,qBAA8B,CAAEM,OAAQH,IAO5DK,kBAAoBL,GACXC,EAAIC,IAAI,GAAGL,mBAA4B,CAAEM,OAAQH,IAO1DM,kBAAoBN,GACXC,EAAIC,IAAI,GAAGL,qBAA8B,CAAEM,OAAQH,IAO5DO,eAAiBP,GACRC,EAAIC,IAAI,GAAGL,eAAwB,CAAEM,OAAQH"}