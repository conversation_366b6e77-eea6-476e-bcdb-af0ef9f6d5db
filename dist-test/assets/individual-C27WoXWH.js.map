{"version": 3, "file": "individual-C27WoXWH.js", "sources": ["../../src/views/room/booking/individual.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"title\": \"Individual Booking\",\r\n    \"orderInfo\": \"Order Information\",\r\n    \"checkinType\": \"Check-in Type\",\r\n    \"selectCheckinType\": \"Select Check-in Type\",\r\n    \"guestSrcType\": \"Guest Source\",\r\n    \"selectSource\": \"Select Source\",\r\n    \"selectPlanCheckinTime\": \"Select Plan Check-in Time\",\r\n    \"selectPlanCheckoutTime\": \"Select Plan Checkout Time\",\r\n    \"days\": \"Days\",\r\n    \"checkinDuration\": \"Check-in Duration\",\r\n    \"retain\": \"Retain\",\r\n    \"remark\": \"Remark\",\r\n    \"selectRoom\": \"Select Room\",\r\n    \"noRoomAssigned\": \"No room assigned\",\r\n    \"roomType\": \"Room Type\",\r\n    \"discountPriceSalePrice\": \"Discount Price / Sale Price\",\r\n    \"availableRooms\": \"Available\",\r\n    \"availableOverRooms\": \"Available Over Rooms\",\r\n    \"bookedRooms\": \"Booked Rooms\",\r\n    \"freeBreakfast\": \"Free Breakfast/Portion\",\r\n    \"action\": \"Action\",\r\n    \"assignRoom\": \"Assign Room\",\r\n    \"sendSMS\": \"Send SMS\",\r\n    \"planCheckinTime\": \"Check-in\",\r\n    \"planCheckoutTime\": \"Check-out\",\r\n    \"orderSource\": \"Order Source\",\r\n    \"guarantyStyle\": \"Guarantee\",\r\n    \"retainTime\": \"Retention Time\",\r\n    \"cancel\": \"Cancel\",\r\n    \"adjustPrice\": \"Price\",\r\n    \"discountPrice\": \"Discount Price / Sale Price\",\r\n    \"availableNum\": \"Available Quantity\",\r\n    \"overbookAbleNum\": \"Overbookable Quantity\",\r\n    \"reservedRooms\": \"Reserved Rooms\",\r\n    \"breakfast\": \"Free Breakfast / Portions\",\r\n    \"roomArrange\": \"Room Arrangement\",\r\n    \"smsReminder\": \"Send SMS Reminder?\",\r\n    \"orderNo\": \"Order No\",\r\n    \"guestInfo\": \"Guest Information\",\r\n    \"contact\": \"Booker\",\r\n    \"contactPhone\": \"Booker Phone\",\r\n    \"checkinPerson\": \"Name\",\r\n    \"checkinPhone\": \"Check-in Phone\",\r\n    \"seller\": \"Sales\",\r\n    \"orderNos\": \"Order No\",\r\n    \"enterFullPhoneNumber\": \"Enter full phone number\",\r\n    \"selectAgent\": \"Please select an agent\",\r\n    \"selectProtocolUnit\": \"Please select a protocol unit\",\r\n    \"activeText\": \"Enable Price Modification\",\r\n    \"inactiveText\": \"Disable Price Modification\",\r\n    \"roomDiscountPrice\": \"Room Discount Price\",\r\n    \"overbookableRoomNumber\": \"Overbookable\",\r\n    \"reservedRoomCount\": \"Expected number of rooms\",\r\n    \"guestBreakfast\": \"Breakfast\",\r\n    \"arrangeRoom\": \"Arrange\",\r\n    \"checkinCheckoutTimeWarning\": \"Please set the check-in and check-out times, and ensure that check-out time is later than check-in time.\",\r\n    \"selectRoomNumWarning\": \"Please select the number of rooms for the current room type.\",\r\n    \"contactNameRequired\": \"The contact name is required.\",\r\n    \"bookingSuccess\": \"Booking successful.\",\r\n    \"roomsBookedWarning\": \"There are {bookedRoomsCount} rooms already booked. To reduce the number of rooms, please remove rooms first.\",\r\n    \"processReservation\": \"Process Reservation\",\r\n    \"selectRetainTime\": \"Please select retain time\",\r\n\r\n    \"placeholder\": {\r\n      \"contact\": \"Enter booker name\",\r\n      \"contactPhone\": \"Enter phone number\",\r\n      \"checkinPerson\": \"check-in person\",\r\n      \"checkinPhone\": \"Enter check-in phone number\",\r\n      \"orderNo\": \"Enter external order number\",\r\n      \"remark\": \"Enter remark\",\r\n      \"arbitraryTime\": \"Arbitrary time\",\r\n      \"checkinDuration\": \"Select duration\",\r\n      \"orderSource\": \"Select order source\",\r\n      \"guarantyStyle\": \"Select guarantee style\"\r\n    },\r\n    \"setHourRoom\": \"The current room type does not support hourly booking. Please go to [Settings] - [Basic Settings] - [Hourly Booking] to maintain hourly booking information before proceeding with hourly booking check-in.\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"title\": \"散客预订\",\r\n    \"orderInfo\": \"订单信息\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"selectCheckinType\": \"选择入住类型\",\r\n    \"guestSrcType\": \"客源类型\",\r\n    \"selectSource\": \"选择来源\",\r\n    \"selectPlanCheckinTime\": \"选择计划入住时间\",\r\n    \"selectPlanCheckoutTime\": \"选择计划退房时间\",\r\n    \"days\": \"天数\",\r\n    \"checkinDuration\": \"入住时长\",\r\n    \"retain\": \"保留\",\r\n    \"remark\": \"备注\",\r\n    \"selectRoom\": \"选择房间\",\r\n    \"noRoomAssigned\": \"没有分配房间\",\r\n    \"roomType\": \"房间类型\",\r\n    \"discountPriceSalePrice\": \"折扣价格 / 销售价\",\r\n    \"availableRooms\": \"可用房间\",\r\n    \"availableOverRooms\": \"超额房间\",\r\n    \"bookedRooms\": \"已预定房间\",\r\n    \"freeBreakfast\": \"免费早餐/份数\",\r\n    \"action\": \"操作\",\r\n    \"assignRoom\": \"分配房间\",\r\n    \"sendSMS\": \"发送短信\",\r\n    \"planCheckinTime\": \"预抵时间\",\r\n    \"planCheckoutTime\": \"预离时间\",\r\n    \"orderSource\": \"订单来源\",\r\n    \"guarantyStyle\": \"担保方式\",\r\n    \"retainTime\": \"保留时间\",\r\n    \"cancel\": \"取消\",\r\n    \"discountPrice\": \"优惠价 / 售价\",\r\n    \"overbookAbleNum\": \"可超数\",\r\n    \"reservedRooms\": \"预订间数\",\r\n    \"breakfast\": \"赠早 / 份\",\r\n    \"roomArrange\": \"排房\",\r\n    \"smsReminder\": \"是否发送短信？\",\r\n    \"orderNo\": \"订单号\",\r\n    \"guestInfo\": \"客人信息\",\r\n    \"contact\": \"预订人\",\r\n    \"adjustPrice\": \"价格\",\r\n    \"contactPhone\": \"预订人电话\",\r\n    \"checkinPerson\": \"入住人\",\r\n    \"checkinPhone\": \"入住人电话\",\r\n    \"seller\": \"销售员\",\r\n    \"orderNos\": \"外部订单号\",\r\n    \"enterFullPhoneNumber\": \"请输入完整手机号\",\r\n    \"selectAgent\": \"请选择中介\",\r\n    \"selectProtocolUnit\": \"请选择协议单位\",\r\n    \"activeText\": \"开启改房价\",\r\n    \"inactiveText\": \"关闭改房价\",\r\n    \"roomDiscountPrice\": \"房间折扣价\",\r\n    \"overbookableRoomNumber\": \"可超订数\",\r\n    \"reservedRoomCount\": \"预订间数\",\r\n    \"guestBreakfast\": \"赠早数\",\r\n    \"arrangeRoom\": \"排房\",\r\n    \"checkinCheckoutTimeWarning\": \"请先设置预抵和预离时间，并使预离时间晚于预抵时间。\",\r\n    \"selectRoomNumWarning\": \"请先选择当前房型预订间数\",\r\n    \"contactNameRequired\": \"预订人不能为空\",\r\n    \"bookingSuccess\": \"预订成功\",\r\n    \"roomsBookedWarning\": \"已排房{bookedRoomsCount}间, 若要减少预订间数，请先删减排房\",\r\n    \"processReservation\": \"办理预订\",\r\n    \"selectRetainTime\": \"请选择保留时间\",\r\n\r\n    \"placeholder\": {\r\n      \"contact\": \"请输入预订人\",\r\n      \"contactPhone\": \"请输入电话\",\r\n      \"checkinPerson\": \"请输入入住人\",\r\n      \"checkinPhone\": \"请输入入住人电话\",\r\n      \"orderNo\": \"请输入外部订单号\",\r\n      \"remark\": \"请输入备注\",\r\n      \"arbitraryTime\": \"任意时间\",\r\n      \"checkinDuration\": \"请选择时长\",\r\n      \"orderSource\": \"请选择订单来源\",\r\n      \"guarantyStyle\": \"请选择担保方式\"\r\n    },\r\n    \"setHourRoom\": \"当前房型不支持钟点房，请先前往[设置]-[基础设置]-[钟点房] 维护钟点房才可以办理钟点房入住.\"\r\n  },\r\n  \"km\": {\r\n    \"title\": \"ការកក់ទុកបុគ្គល\",\r\n    \"orderInfo\": \"ព័ត៌មានការកក់ទុក\",\r\n    \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n    \"selectCheckinType\": \"ជ្រើសរើសប្រភេទចូលស្នាក់នៅ\",\r\n    \"guestSrcType\": \"ប្រភពភ្ញៀវ\",\r\n    \"selectSource\": \"ជ្រើសរើសប្រភព\",\r\n    \"selectPlanCheckinTime\": \"ជ្រើសរើសពេលវេលាចូលស្នាក់នៅដែលគ្រោងទុក\",\r\n    \"selectPlanCheckoutTime\": \"ជ្រើសរើសពេលវេលាចាកចេញដែលគ្រោងទុក\",\r\n    \"days\": \"ថ្ងៃ\",\r\n    \"checkinDuration\": \"រយៈពេលចូលស្នាក់នៅ\",\r\n    \"retain\": \"រក្សាទុក\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"selectRoom\": \"ជ្រើសរើសបន្ទប់\",\r\n    \"noRoomAssigned\": \"គ្មានបន្ទប់ត្រូវបានចែកចាយ\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"discountPriceSalePrice\": \"តម្លៃបញ្ចុះតម្លៃ / តម្លៃលក់\",\r\n    \"availableRooms\": \"អាចប្រើបាន\",\r\n    \"availableOverRooms\": \"បន្ទប់លើសអាចប្រើបាន\",\r\n    \"bookedRooms\": \"បន្ទប់ដែលបានកក់ទុក\",\r\n    \"freeBreakfast\": \"អាហារពេលព្រឹកឥតគិតថ្លៃ/ចំនួន\",\r\n    \"action\": \"សកម្មភាព\",\r\n    \"assignRoom\": \"ចែកចាយបន្ទប់\",\r\n    \"sendSMS\": \"ផ្ញើ SMS\",\r\n    \"planCheckinTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n    \"planCheckoutTime\": \"ពេលវេលាចាកចេញ\",\r\n    \"orderSource\": \"ប្រភពការកក់ទុក\",\r\n    \"guarantyStyle\": \"វិធីសាក្សី\",\r\n    \"retainTime\": \"ពេលវេលារក្សាទុក\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"adjustPrice\": \"តម្លៃ\",\r\n    \"discountPrice\": \"តម្លៃបញ្ចុះតម្លៃ / តម្លៃលក់\",\r\n    \"availableNum\": \"ចំនួនដែលអាចប្រើបាន\",\r\n    \"overbookAbleNum\": \"ចំនួនអាចកក់លើស\",\r\n    \"reservedRooms\": \"បន្ទប់ដែលបានកក់ទុក\",\r\n    \"breakfast\": \"អាហារពេលព្រឹកឥតគិតថ្លៃ / ចំនួន\",\r\n    \"roomArrange\": \"ការរៀបចំបន្ទប់\",\r\n    \"smsReminder\": \"ផ្ញើសាររំលឹក SMS?\",\r\n    \"orderNo\": \"លេខការកក់ទុក\",\r\n    \"guestInfo\": \"ព័ត៌មានភ្ញៀវ\",\r\n    \"contact\": \"អ្នកកក់ទុក\",\r\n    \"contactPhone\": \"លេខទូរស័ព្ទអ្នកកក់ទុក\",\r\n    \"checkinPerson\": \"ឈ្មោះ\",\r\n    \"checkinPhone\": \"លេខទូរស័ព្ទចូលស្នាក់នៅ\",\r\n    \"seller\": \"អ្នកលក់\",\r\n    \"orderNos\": \"លេខការកក់ទុកខាងក្រៅ\",\r\n    \"enterFullPhoneNumber\": \"បញ្ចូលលេខទូរស័ព្ទពេញ\",\r\n    \"selectAgent\": \"សូមជ្រើសរើសភ្នាក់ងារ\",\r\n    \"selectProtocolUnit\": \"សូមជ្រើសរើសអង្គភាពអនុសញ្ញា\",\r\n    \"activeText\": \"បើកការផ្លាស់ប្តូរតម្លៃបន្ទប់\",\r\n    \"inactiveText\": \"បិទការផ្លាស់ប្តូរតម្លៃបន្ទប់\",\r\n    \"roomDiscountPrice\": \"តម្លៃបញ្ចុះតម្លៃបន្ទប់\",\r\n    \"overbookableRoomNumber\": \"អាចកក់លើស\",\r\n    \"reservedRoomCount\": \"ចំនួនបន្ទប់ដែលរំពឹងទុក\",\r\n    \"guestBreakfast\": \"អាហារពេលព្រឹក\",\r\n    \"arrangeRoom\": \"រៀបចំ\",\r\n    \"checkinCheckoutTimeWarning\": \"សូមកំណត់ពេលវេលាចូលស្នាក់នៅ និងចាកចេញ ហើយធានាថាពេលវេលាចាកចេញគឺក្រោយពេលវេលាចូលស្នាក់នៅ។\",\r\n    \"selectRoomNumWarning\": \"សូមជ្រើសរើសចំនួនបន្ទប់សម្រាប់ប្រភេទបន្ទប់បច្ចុប្បន្ន។\",\r\n    \"contactNameRequired\": \"ឈ្មោះអ្នកកក់ទុកត្រូវបានទាមទារ។\",\r\n    \"bookingSuccess\": \"ការកក់ទុកជោគជ័យ។\",\r\n    \"roomsBookedWarning\": \"មានបន្ទប់ {bookedRoomsCount} ដែលបានកក់ទុករួចហើយ។ ដើម្បីកាត់បន្ថយចំនួនបន្ទប់ សូមលុបបន្ទប់ជាមុនសិន។\",\r\n    \"processReservation\": \"ដំណើរការការកក់ទុក\",\r\n    \"selectRetainTime\": \"សូមជ្រើសរើសពេលវេលារក្សាទុក\",\r\n\r\n    \"placeholder\": {\r\n      \"contact\": \"បញ្ចូលឈ្មោះអ្នកកក់ទុក\",\r\n      \"contactPhone\": \"បញ្ចូលលេខទូរស័ព្ទ\",\r\n      \"checkinPerson\": \"អ្នកចូលស្នាក់នៅ\",\r\n      \"checkinPhone\": \"បញ្ចូលលេខទូរស័ព្ទចូលស្នាក់នៅ\",\r\n      \"orderNo\": \"បញ្ចូលលេខការកក់ទុកខាងក្រៅ\",\r\n      \"remark\": \"បញ្ចូលចំណាំ\",\r\n      \"arbitraryTime\": \"ពេលវេលាណាមួយ\",\r\n      \"checkinDuration\": \"ជ្រើសរើសរយៈពេល\",\r\n      \"orderSource\": \"ជ្រើសរើសប្រភពការកក់ទុក\",\r\n      \"guarantyStyle\": \"ជ្រើសរើសវិធីសាក្សី\"\r\n    },\r\n    \"setHourRoom\": \"ប្រភេទបន្ទប់បច្ចុប្បន្នមិនគាំទ្រការកក់ទុកម៉ោងទេ។ សូមទៅ [ការកំណត់] - [ការកំណត់មូលដ្ឋាន] - [ការកក់ទុកម៉ោង] ដើម្បីថែទាំព័ត៌មានការកក់ទុកម៉ោងមុនពេលដំណើរការការចូលស្នាក់នៅម៉ោង។\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { BookRoomsModel } from '@/models'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type individual from './individual'\r\nimport { bookApi, channelApi, customerApi, dictDataApi, generalConfigApi, hourRoomTypeApi, memberApi, priceAllDayRuleApi, serverTimeApi, userApi } from '@/api/modules/index'\r\n\r\nimport { BooleanEnum, CheckinType, CONSTANT_TYPE_CODE_SZ, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUARANTEE_TYPE, DICT_TYPE_GUEST_SRC_TYPE, GeneralConfigTypeEnum, GuestSrcType, NoType, ORDER_SOURCE, OrderType } from '@/models'\r\n\r\nimport useUserStore from '@/store/modules/user'\r\nimport ymdate, { ymdateHMS } from '@/utils/timeutils'\r\nimport BookDetails from '@/views/room/booking/bookDetails.vue'\r\nimport ArrangeRoomsDialog from '@/views/room/components/arrangeRooms/arrangeRooms.vue'\r\nimport RoomEditPrice from '@/views/room/components/roomEditPrice/index.vue'\r\nimport { validateTipPhone } from '@/views/room/realtime/components/utils.ts'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\nimport eventBus from '../realtime/event-bus'\r\n\r\ndefineOptions({\r\n  name: 'Individual',\r\n})\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rtCode?: string\r\n    rtName?: string\r\n    rNo?: string\r\n    rCode?: string\r\n    rtState?: string\r\n    bookData?: any\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    rtCode: '',\r\n    rtName: '',\r\n    rNo: '',\r\n    rCode: '',\r\n    rtState: '',\r\n    bookData: {},\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n}>()\r\nconst { t } = useI18n()\r\n\r\nconst formRef = ref<FormInstance>()\r\n\r\nconst formRules = ref<FormRules>({\r\n  contact: [{ required: true, message: t('placeholder.contact'), trigger: 'blur' }],\r\n})\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n})\r\n/** 预抵时间初始值 */\r\nconst myStartHour = ref('')\r\nconst myEndHour = ref('')\r\nconst earliestCheckinTime = ref('')\r\nconst hourNum = ref(1)\r\nconst oldType = ref(false)\r\n/** 开始时间 */\r\nconst startTime = ref('')\r\n/** 公用参数 */\r\nconst queryParams = reactive({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n})\r\n/** 结束时间 */\r\nconst endTime = ref('')\r\nconst rooms = ref([])\r\nconst form = ref({\r\n  ...queryParams,\r\n  /** 渠道代码 */\r\n  channelCode: 'lobby',\r\n  /** 订单来源 */\r\n  orderSource: 'lobby',\r\n  /** 客源类型 */\r\n  guestSrcType: GuestSrcType.WALK_IN,\r\n  /** 预订类型 DD_PT：(个人)普通订单  DD_TD：团队订单 */\r\n  bookType: OrderType.GENERAL,\r\n  /** 入住类型 当预订类型为团队预订时，入住类型为旅行团队或会议团队 */\r\n  checkinType: CheckinType.ALL_DAY,\r\n  hourCode: '',\r\n  /** 会员代码、协议单位、中介代码、团队代码 */\r\n  guestCode: '',\r\n  /** 会员姓名、协议单位、中介名称、团队名称 */\r\n  guestName: '',\r\n  /** 预抵时间 年月日 普通预订存储，团队预订抵离时间存储在预订房型中 */\r\n  planCheckinTime: dayjs().format('YYYY-MM-DD HH:mm'),\r\n  // dayjs().format('YYYY-MM-DD HH:mm').match(/(\\d{2}):(\\d{2})/)[0]\r\n  /** 预离时间 年月日 普通预订存储，团队预订抵离时间存储在预订房型中 */\r\n  planCheckoutTime: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm'),\r\n  days: 1,\r\n  /** 预订人 */\r\n  contact: '',\r\n  /** 电话 */\r\n  phone: '',\r\n  /** 入住人姓名 */\r\n  checkinPerson: '',\r\n  /** 入住人电话 */\r\n  checkinPhone: '',\r\n  /** 担保方式 */\r\n  guarantyStyle: 'no',\r\n  /** 销售员 */\r\n  seller: null as unknown as number | undefined,\r\n  retain: '0',\r\n  /** 保留时间 */\r\n  retainTime: dayjs().format('YYYY-MM-DD HH:mm'),\r\n  /** 是否发短信 0：否 1：是 */\r\n  isSendSms: '0',\r\n  /** 市场活动代码 */\r\n  marketActivityCode: '',\r\n  /** 外部订单号 */\r\n  outOrderNo: '',\r\n  /** 备注 */\r\n  remark: '',\r\n  dataList: [] as BookRoomsModel[],\r\n})\r\nconst changePrice = ref(true)\r\n/** 中介列表 */\r\nconst agents = ref<individual.simpleType[]>([])\r\n/** 协议单位列表 */\r\nconst protocols = ref<individual.simpleType[]>([])\r\n/** 服务器时间 */\r\nconst serverTime = ref()\r\n/** 设置的时间 */\r\nconst setTime = ref()\r\n// 设置预订数据的函数\r\nfunction setBookingData() {\r\n  if (props.bookData && (props.bookData.planCheckinTime || props.bookData.planCheckinDate)) {\r\n    if (props.bookData.planCheckinTime) {\r\n      // 如果传入了完整的时间，直接使用\r\n      form.value.planCheckinTime = props.bookData.planCheckinTime\r\n      // 重新计算预离时间（预抵时间后一天）\r\n      if (myEndHour.value) {\r\n        form.value.planCheckoutTime = `${dayjs(form.value.planCheckinTime).add(1, 'day').format('YYYY-MM-DD')} ${myEndHour.value}`\r\n      }\r\n    } else if (props.bookData.planCheckinDate) {\r\n      // 如果只传入了日期，根据系统配置设置时间点\r\n      const targetDate = props.bookData.planCheckinDate\r\n      const today = dayjs(serverTime.value)\r\n      const isToday = today.isSame(targetDate, 'day')\r\n\r\n      if (isToday) {\r\n        // 如果是今天，比较当前时间和配置时间\r\n        const now = today.format('HH:mm')\r\n        if (now > setTime.value) {\r\n          form.value.planCheckinTime = today.format('YYYY-MM-DD HH:mm')\r\n        } else {\r\n          form.value.planCheckinTime = `${targetDate} ${setTime.value}`\r\n        }\r\n      } else {\r\n        // 如果不是今天，使用配置的入住时间\r\n        form.value.planCheckinTime = `${targetDate} ${setTime.value}`\r\n      }\r\n      // 重新计算预离时间（预抵时间后一天）\r\n      if (myEndHour.value) {\r\n        form.value.planCheckoutTime = `${dayjs(form.value.planCheckinTime).add(1, 'day').format('YYYY-MM-DD')} ${myEndHour.value}`\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nonMounted(async () => {\r\n  getChannels()\r\n  getHour()\r\n  getSellers()\r\n  getAgents()\r\n  getProtocols()\r\n  getConstants()\r\n  await fetchTime()\r\n\r\n  getEarliestCheckinTime()\r\n\r\n  // 初始设置预订数据\r\n  setBookingData()\r\n})\r\n\r\n// 监听 bookData 变化\r\nwatch(\r\n  () => props.bookData,\r\n  () => {\r\n    setBookingData()\r\n  },\r\n  { deep: true }\r\n)\r\n\r\n// 通用字典\r\nconst dictTypes = [DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_CHECKIN_TYPE, ORDER_SOURCE, CONSTANT_TYPE_CODE_SZ, DICT_TYPE_GUARANTEE_TYPE]\r\n/** 客源类型列表 */\r\nconst srcTypeList = ref<individual.type[]>([])\r\n/** 入住类型列表 */\r\nconst checkinTypeList = ref<individual.type[]>([])\r\n/** 订单来源列表 */\r\nconst orderSources = ref<individual.type[]>([])\r\n/** 小时列表 */\r\nconst hourTypes = ref<{ hourCode: string; hourName: string; hour: number }[]>([])\r\n/** 担保列表 */\r\nconst dbs = ref<individual.type[]>([])\r\n\r\n/** 获取最早入住时间 */\r\nfunction getEarliestCheckinTime() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  priceAllDayRuleApi.getPriceAllDayRule(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      earliestCheckinTime.value = res.data.earliestCheckinTime\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取预订排房列表 */\r\nfunction getOutTime() {\r\n  const params = {\r\n    ...queryParams,\r\n    channelCode: form.value.channelCode, // 渠道\r\n    guestSrcType: form.value.guestSrcType, // 客源类型\r\n    orderSource: form.value.orderSource, // 订单来源\r\n    planCheckinTime: dayjs(form.value.planCheckinTime).format('YYYY-MM-DD'), // 计划入住时间\r\n    planCheckoutTime: dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD'), // 计划离店时间\r\n    mtCode: form.value.guestSrcType === GuestSrcType.MEMBER ? form.value.guestCode : '', // 会员类型代码\r\n  }\r\n\r\n  bookApi\r\n    .checkOutTime(params)\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        myEndHour.value = res.data.checkOutTime\r\n        form.value.planCheckoutTime = `${dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')} ${myEndHour.value}`\r\n        // 在获取到退房时间后，重新设置预订数据\r\n        setBookingData()\r\n        bookingRoomsList()\r\n      }\r\n    })\r\n    .catch()\r\n}\r\n/** 获取字典数据 */\r\nasync function getConstants() {\r\n  await dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    checkinTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n    checkinTypeList.value = checkinTypeList.value.filter((item: any) => item.code !== 'travel_group' && item.code !== 'meeting_group')\r\n    srcTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n    srcTypeList.value = srcTypeList.value.filter((item) => item.code !== '0' && item.code !== '0')\r\n    orderSources.value = res.data.filter((item: any) => item.dictType === ORDER_SOURCE)\r\n    dbs.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUARANTEE_TYPE)\r\n  })\r\n}\r\n/** 获得协议单位列表 */\r\nasync function getAgents() {\r\n  const { data } = await customerApi.simpleList({\r\n    gcode: userStore.gcode,\r\n    belongHcode: userStore.hcode,\r\n    paType: '1',\r\n    isEnable: BooleanEnum.YES,\r\n  })\r\n  agents.value = data\r\n}\r\n\r\n/** 获得协议中介列表 */\r\nasync function getProtocols() {\r\n  const { data } = await customerApi.simpleList({\r\n    gcode: userStore.gcode,\r\n    belongHcode: userStore.hcode,\r\n    paType: '0',\r\n    isEnable: BooleanEnum.YES,\r\n  })\r\n  protocols.value = data\r\n}\r\n\r\n/** 渠道列表 */\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\n/** 获取渠道列表 */\r\nasync function getChannels() {\r\n  const prms = {\r\n    ...queryParams,\r\n    isEnable: '1',\r\n  }\r\n  const { data } = await channelApi.getChannelSimpleList(prms)\r\n  channels.value = data\r\n}\r\n\r\n/** 销售员列表 */\r\nconst sellers = ref<{ username: number; nickname: string }[]>([])\r\n/** 获取销售员列表 */\r\nasync function getSellers() {\r\n  const { data } = await userApi.listSeller(queryParams)\r\n  sellers.value = data\r\n}\r\n/** 最晚退房时间 */\r\nasync function getTime() {\r\n  const { data } = await generalConfigApi.getCheckOutTime(queryParams)\r\n  myEndHour.value = data.value\r\n  form.value.planCheckoutTime = `${dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')} ${myEndHour.value}`\r\n  // 在获取到退房时间后，重新设置预订数据\r\n  setBookingData()\r\n  bookingRoomsList()\r\n}\r\n/** 获得可预订的房型列表 */\r\nfunction bookingRoomsList() {\r\n  data.value.loading = true\r\n  startTime.value = dayjs(form.value.planCheckinTime).format('YYYY-MM-DD HH:mm')\r\n  endTime.value = dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD HH:mm')\r\n  const params = {\r\n    ...queryParams,\r\n    channelCode: form.value.channelCode,\r\n    checkinType: form.value.checkinType,\r\n    guestSrcType: form.value.guestCode ? form.value.guestSrcType : GuestSrcType.WALK_IN,\r\n    guestCode: form.value.guestCode,\r\n    delayMinute: 0,\r\n    hourCode: form.value.checkinType === CheckinType.HOUR_ROOM ? form.value.hourCode : '',\r\n    orderSource: form.value.orderSource,\r\n    planCheckinTime: startTime.value,\r\n    planCheckoutTime: endTime.value,\r\n  }\r\n  bookApi.roomtypeList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      data.value.loading = false\r\n      form.value.dataList = res.data.map((_item: any) => {\r\n        return {\r\n          ..._item,\r\n          vipPrice: _item.dayPrices[0].vipPrice,\r\n        }\r\n      })\r\n      if ([CheckinType.SELF_USE, CheckinType.FREE].includes(form.value.checkinType)) {\r\n        form.value.dataList = res.data.map((_item: any) => {\r\n          return {\r\n            ..._item,\r\n            vipPrice: 0,\r\n          }\r\n        })\r\n      }\r\n      const prms = {\r\n        rtCode: props.rtCode,\r\n        rtName: props.rtName,\r\n        state: props.rtState,\r\n        bookRooms: [\r\n          {\r\n            preOccupied: '0',\r\n            state: props.rtState,\r\n            rNo: props.rNo,\r\n            rCode: props.rCode,\r\n          },\r\n        ],\r\n      }\r\n      if (props.bookData.bookingVisible) {\r\n        addRooms({\r\n          rtCode: props.bookData.bookRooms.rtCode,\r\n          bookRooms: [props.bookData.bookRooms],\r\n        })\r\n        rooms.value = props.bookData.bookRooms\r\n      } else {\r\n        addRooms(prms)\r\n      }\r\n    }\r\n  })\r\n}\r\nfunction handleChange(value: any) {\r\n  if (value.vipPrice) {\r\n    value.dayPrices = value.dayPrices.map((item: any) => {\r\n      return {\r\n        ...item,\r\n        vipPrice: value.vipPrice,\r\n      }\r\n    })\r\n  }\r\n}\r\n\r\nfunction handleClose(row: any, index: number) {\r\n  const x = form.value.dataList.findIndex((item) => item.rtCode === row.rtCode)\r\n  if (x !== -1) {\r\n    form.value.dataList[x].bookRooms?.splice(index, 1) // 直接使用 index 删除\r\n  }\r\n  getExpanded()\r\n}\r\n\r\nconst arrangeRoomsProps = ref({\r\n  visible: false,\r\n  rtCode: '',\r\n  rtName: '',\r\n  rtState: '',\r\n  rNos: [] as string[],\r\n  roomNum: 0,\r\n  planCheckinTime: '',\r\n  planCheckoutTime: '',\r\n})\r\n/** 获得钟点房房型列表 */\r\nasync function getHour() {\r\n  const { data } = await hourRoomTypeApi.getHourRoomTypeList(queryParams)\r\n  hourTypes.value = data\r\n}\r\n\r\nfunction addRoom(row: any) {\r\n  arrangeRoomsProps.value.rNos = []\r\n  if (form.value.planCheckinTime === '' || form.value.planCheckoutTime === '' || !dayjs(form.value.planCheckoutTime).isAfter(dayjs(form.value.planCheckinTime))) {\r\n    ElMessage.warning(t('checkinCheckoutTimeWarning'))\r\n    return\r\n  }\r\n  if (row.roomNum <= 0) {\r\n    ElMessage.warning(t('selectRoomNumWarning'))\r\n    return\r\n  }\r\n  arrangeRoomsProps.value.rtCode = row.rtCode\r\n  arrangeRoomsProps.value.rtName = row.rtName\r\n  arrangeRoomsProps.value.rtState = props.rtState\r\n  arrangeRoomsProps.value.roomNum = row.roomNum\r\n  arrangeRoomsProps.value.planCheckinTime = `${ymdate(form.value.planCheckinTime)} ${ymdateHMS(form.value.planCheckinTime).substring(11, 16)}`\r\n  arrangeRoomsProps.value.planCheckoutTime = `${ymdate(form.value.planCheckoutTime)} ${ymdateHMS(form.value.planCheckoutTime).substring(11, 16)}`\r\n  if (row.bookRooms) {\r\n    row.bookRooms.forEach((ls: any) => {\r\n      arrangeRoomsProps.value.rNos.push(ls.rNo)\r\n    })\r\n  }\r\n  rooms.value = row.bookRooms\r\n  arrangeRoomsProps.value.visible = true\r\n}\r\nconst expandedRows = ref([])\r\nfunction getExpanded() {\r\n  const list = [] as any\r\n  form.value.dataList.forEach((item) => {\r\n    if (item.bookRooms && item.bookRooms?.length > 0) {\r\n      list.push(item.rtCode)\r\n    }\r\n  })\r\n  expandedRows.value = list\r\n}\r\nfunction addRooms(data: any) {\r\n  form.value.dataList.forEach((item: any) => {\r\n    if (item.rtCode === data.rtCode && item.canSellNum + item.canOverNum > 0) {\r\n      item.bookRooms = data.bookRooms\r\n      item.roomNum = 1\r\n    }\r\n  })\r\n  getExpanded()\r\n}\r\n\r\nfunction selectRooms(data: any) {\r\n  form.value.dataList.forEach((item: any) => {\r\n    if (item.rtCode === data.rtCode) {\r\n      item.bookRooms = data.bookRooms\r\n    }\r\n  })\r\n  getExpanded()\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n  form.value.contact = ''\r\n  form.value.phone = ''\r\n  form.value.checkinPerson = ''\r\n  form.value.checkinPhone = ''\r\n  form.value.seller = undefined\r\n  form.value.remark = ''\r\n}\r\n\r\nfunction onChange() {\r\n  form.value.guestCode = ''\r\n  form.value.channelCode = 'lobby'\r\n  // form.value.contact = ''\r\n  // form.value.phone = ''\r\n  onCheckinTypeChange()\r\n}\r\nfunction onChangeGuest() {\r\n  if (form.value.guestCode) {\r\n    form.value.channelCode = agents.value.find((v) => {\r\n      return v.paCode === form.value.guestCode\r\n    })?.channel!\r\n  }\r\n  bookingRoomsList()\r\n}\r\n\r\nconst detailVisible = ref(false)\r\nconst routerName = ref('detail')\r\nconst typeName = ref('individual')\r\n\r\n/** 提交创建预订 */\r\nfunction onBookingSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        if (!form.value.contact) {\r\n          ElMessage.error(t('contactNameRequired'))\r\n          return\r\n        }\r\n        const bookRoomTypes = form.value.dataList.filter((item) => item.roomNum > 0)\r\n        const params = {\r\n          ...queryParams,\r\n          bookNo: '',\r\n          channelCode: form.value.channelCode,\r\n          orderSource: form.value.orderSource,\r\n          guestSrcType: form.value.guestSrcType,\r\n          bookType: 'general',\r\n          checkinType: form.value.checkinType,\r\n          hourCode: form.value.hourCode,\r\n          teamCode: '',\r\n          guestCode: form.value.guestCode,\r\n          guestName: form.value.guestName,\r\n          teamName: '',\r\n          contractNo: '',\r\n          planCheckinTime: `${ymdate(form.value.planCheckinTime)} ${ymdateHMS(form.value.planCheckinTime).substring(11, 16)}`,\r\n          planCheckoutTime: `${ymdate(form.value.planCheckoutTime)} ${ymdateHMS(form.value.planCheckoutTime).substring(11, 16)}`,\r\n          contact: form.value.contact,\r\n          phone: form.value.phone,\r\n          checkinPerson: form.value.checkinPerson,\r\n          checkinPhone: form.value.checkinPhone,\r\n          guarantyStyle: form.value.guarantyStyle,\r\n          retainTime: '',\r\n          delayMinute: 0,\r\n          isSendSms: form.value.isSendSms,\r\n          marketActivityCode: '',\r\n          marketActivityName: '',\r\n          seller: form.value.seller,\r\n          outOrderNo: form.value.outOrderNo,\r\n          remark: form.value.remark,\r\n          batches: [\r\n            {\r\n              batchNo: `${ymdate(form.value.planCheckinTime)}/${ymdate(form.value.planCheckoutTime)}`,\r\n              days: form.value.days,\r\n              planCheckinTime: `${ymdate(form.value.planCheckinTime)} ${ymdateHMS(form.value.planCheckinTime).substring(11, 16)}`,\r\n              planCheckoutTime: `${ymdate(form.value.planCheckoutTime)} ${ymdateHMS(form.value.planCheckoutTime).substring(11, 16)}`,\r\n              bookRoomTypes,\r\n            },\r\n          ],\r\n        }\r\n\r\n        // 如果是从住人房间进入的预订（有传入订单号），则添加订单号参数\r\n        if (props.bookData && props.bookData.orderNo) {\r\n          params.orderNo = props.bookData.orderNo\r\n        }\r\n\r\n        // 如果勾选保留，则添加保留时间\r\n        if (form.value.retain === '1') {\r\n          params.retainTime = `${ymdate(form.value.retainTime)} ${ymdateHMS(form.value.retainTime).substring(11, 16)}`\r\n        }\r\n\r\n        bookApi.createBook(params).then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success(t('bookingSuccess'))\r\n            detailVisible.value = true\r\n            eventBus.emit('invoke-parent-method', 'Message from child')\r\n            const emitSuccessData = {\r\n              noType: NoType.BOOK, // 普通预订单类型\r\n              no: res.data, // 预订单号\r\n            }\r\n            emits('success', emitSuccessData)\r\n            onCancel()\r\n          } else {\r\n            ElMessage.error(res.msg)\r\n          }\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\n/** 获取未脱敏会员详情 */\r\nasync function getInfo(query: string) {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    phone: query.length > 11 ? '' : query,\r\n    idNo: query.length > 11 ? query : '',\r\n    state: BooleanEnum.YES,\r\n  }\r\n  // TODO:没有实现脱敏，用的是未脱敏数据\r\n  const res = await memberApi.getMemberNoPage(params)\r\n  if (res && (res.data as any)) {\r\n    return res.data.list\r\n  }\r\n}\r\n/** 会员信息列表 */\r\nconst members = ref<individual.membersType[]>([])\r\n/** 远程查询会员信息 */\r\nfunction remoteQueryMember(query: string) {\r\n  // 拦截判断一下身份证或者手机号格式是否正确？\r\n  const fage = validateTipPhone(query)\r\n  if (!fage) {\r\n    members.value = []\r\n    data.value.loading = true\r\n    setTimeout(async () => {\r\n      data.value.loading = false\r\n      const res = await getInfo(query)\r\n      if (res) {\r\n        members.value = res\r\n      }\r\n    }, 200)\r\n  } else {\r\n    // ElMessage.warning(\"身份证或手机号格式错误\");\r\n    // members.value = [];\r\n  }\r\n}\r\n\r\nconst controls = ref(false)\r\nconst clearable = ref(false)\r\nfunction disabledDate(time: any) {\r\n  return time.getTime() < Date.now() - 8.64e7\r\n}\r\n\r\nconst roomPriceProps = ref({\r\n  visible: false,\r\n  rtCode: '',\r\n  initialPriceList: [] as individual.ParameterMaps[],\r\n})\r\n\r\nfunction changeOutHour() {\r\n  myEndHour.value = dayjs(form.value.planCheckoutTime).format('HH:mm')\r\n  bookingRoomsList()\r\n}\r\n/** 会员选中 */\r\nfunction vipChange(value) {\r\n  bookingRoomsList()\r\n  const data = members.value.filter((item: any) => item.mcode === value)[0]\r\n  form.value.contact = data.name\r\n  form.value.phone = data.phone\r\n}\r\n\r\nasync function onCheckinTypeChange() {\r\n  // switch (form.value.checkinType) {\r\n  //   case CheckinType.ALL_DAY:\r\n  //     // 全天房\r\n  //     break;\r\n  //   case CheckinType.LONG_STAY:\r\n  //     // 长包\r\n  //     break;\r\n  //   case CheckinType.SELF_USE:\r\n  //     // 自用\r\n  //     break;\r\n  //   case CheckinType.FREE:\r\n  //     // 免费\r\n  //     break;\r\n  //   case CheckinType.HOUR_ROOM:\r\n  //     // 钟点房\r\n  //     break;\r\n  // }\r\n  if (form.value.checkinType === CheckinType.HOUR_ROOM) {\r\n    oldType.value = true\r\n    if (!hourTypes.value || hourTypes.value.length < 1) {\r\n      ElMessage.warning(t('setHourRoom'))\r\n      return\r\n    }\r\n    form.value.hourCode = hourTypes.value[0].hourCode\r\n    hourNum.value = hourTypes.value[0].hour\r\n    form.value.planCheckinTime = dayjs().format('YYYY-MM-DD HH:mm')\r\n    form.value.planCheckoutTime = dayjs(form.value.planCheckinTime).add(hourNum.value, 'hour').format('YYYY-MM-DD HH:mm')\r\n    bookingRoomsList()\r\n  } else {\r\n    if (oldType.value) {\r\n      oldType.value = false\r\n      form.value.days = 1\r\n      form.value.planCheckinTime = dayjs().format('YYYY-MM-DD HH:mm')\r\n      form.value.planCheckoutTime = dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm')\r\n      form.value.hourCode = ''\r\n    }\r\n    if ([CheckinType.ALL_DAY, CheckinType.LONG_STAY].includes(form.value.checkinType)) {\r\n      getOutTime()\r\n    } else {\r\n      getTime()\r\n    }\r\n  }\r\n}\r\n/** 获取服务器进行比较作为当前时间 */\r\nasync function fetchTime() {\r\n  const { data } = await serverTimeApi.serverTime(userStore.gcode, '0')\r\n  const res = await generalConfigApi.getGeneralConfig({\r\n    ...queryParams,\r\n    code: GeneralConfigTypeEnum.CHECKIN_TIME,\r\n    type: GeneralConfigTypeEnum.CHECKIN_TIME,\r\n  })\r\n  serverTime.value = data\r\n  // 获取服务器时分\r\n  const now = dayjs(serverTime.value).format('HH:mm')\r\n  // 设置的时分\r\n  setTime.value = res.data.value\r\n  // 判断是否大于\r\n  if (now > setTime.value) {\r\n    form.value.planCheckinTime = dayjs(serverTime.value).format('YYYY-MM-DD HH:mm')\r\n  } else {\r\n    form.value.planCheckinTime = `${dayjs(serverTime.value).format('YYYY-MM-DD')} ${setTime.value}`\r\n  }\r\n  myStartHour.value = dayjs(form.value.planCheckinTime).format('HH:mm')\r\n  getOutTime()\r\n}\r\nfunction changeRoomNum(value: any) {\r\n  if (value.bookRooms && value.bookRooms.length > value.roomNum) {\r\n    value.roomNum = value.bookRooms.length\r\n    ElMessage.warning(t('roomsBookedWarning', { bookedRoomsCount: value.bookRooms.length }))\r\n  }\r\n}\r\n\r\nfunction checkType(value: any) {\r\n  roomPriceProps.value.rtCode = value.rtCode\r\n  roomPriceProps.value.initialPriceList = value.dayPrices\r\n  roomPriceProps.value.visible = true\r\n}\r\nfunction echoList(value: individual.ParameterMaps[], echoId: string) {\r\n  form.value.dataList.forEach((item) => {\r\n    if (item.rtCode === echoId) {\r\n      item.dayPrices = value\r\n      item.vipPrice = value[0].vipPrice\r\n    }\r\n  })\r\n}\r\n/** 选择预抵时间 */\r\nfunction CheckinTime(val: Date) {\r\n  // 选择当前的时间\r\n  const specificDate = dayjs(val).format('YYYY-MM-DD')\r\n  // 今天日期\r\n  const today = dayjs(serverTime.value)\r\n  // 是否今天\r\n  const isToday = today.isSame(specificDate, 'day')\r\n  if (isToday) {\r\n    // 获取服务器时分\r\n    const now = today.format('HH:mm')\r\n    // 判断是否大于\r\n    if (now > setTime.value) {\r\n      form.value.planCheckinTime = today.format('YYYY-MM-DD HH:mm')\r\n    } else {\r\n      form.value.planCheckinTime = today.format('YYYY-MM-DD') + setTime.value\r\n    }\r\n    myStartHour.value = dayjs(form.value.planCheckinTime).format('HH:mm')\r\n  } else {\r\n    form.value.planCheckinTime = `${specificDate} ${setTime.value}`\r\n  }\r\n  form.value.planCheckoutTime = `${dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')} ${myEndHour.value}`\r\n  if (form.value.checkinType === CONSTANT_TYPE_CODE_SZ) {\r\n    form.value.planCheckoutTime = dayjs(form.value.planCheckinTime).add(hourNum.value, 'hour').format('YYYY-MM-DD HH:mm')\r\n  } else if (dayjs(form.value.planCheckinTime).format('YYYY-MM-DD') >= dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')) {\r\n    form.value.planCheckoutTime = `${dayjs(form.value.planCheckinTime).add(1, 'day').format('YYYY-MM-DD')} ${myEndHour.value}`\r\n  }\r\n  bookingRoomsList()\r\n}\r\n/** 预抵时间小时获取 */\r\nfunction changeHour() {\r\n  myStartHour.value = dayjs(form.value.planCheckinTime).format('HH:mm')\r\n  hourNum.value = hourTypes.value.find((_item) => {\r\n    return _item.hourCode === form.value.hourCode\r\n  })?.hour!\r\n  if (form.value.checkinType === CONSTANT_TYPE_CODE_SZ) {\r\n    form.value.planCheckoutTime = dayjs(form.value.planCheckinTime).add(hourNum.value, 'hour').format('YYYY-MM-DD HH:mm')\r\n  } else {\r\n    getTime()\r\n  }\r\n  if (form.value.days > 0) {\r\n    const checkInDate = dayjs(ymdate(form.value.planCheckinTime))\r\n    const checkOutDate = dayjs(ymdate(form.value.planCheckoutTime))\r\n    // 如果预抵时间点小于最早入住时间，则天数加1\r\n    if (myStartHour.value < earliestCheckinTime.value) {\r\n      form.value.days = Math.abs(checkInDate.diff(checkOutDate, 'days')) + 1\r\n    } else {\r\n      form.value.days = Math.abs(checkInDate.diff(checkOutDate, 'days'))\r\n    }\r\n  }\r\n  bookingRoomsList()\r\n}\r\n\r\n/** 选择预离时间 */\r\nfunction checkOutTime() {\r\n  form.value.planCheckoutTime = `${dayjs(form.value.planCheckoutTime).format('YYYY-MM-DD')} ${myEndHour.value}`\r\n  if (form.value.days > 0) {\r\n    const checkInDate = dayjs(ymdate(form.value.planCheckinTime))\r\n    const checkOutDate = dayjs(ymdate(form.value.planCheckoutTime))\r\n    // 如果预抵时间点小于最早入住时间，则天数加1\r\n    if (myStartHour.value < earliestCheckinTime.value) {\r\n      form.value.days = Math.abs(checkInDate.diff(checkOutDate, 'days')) + 1\r\n    } else {\r\n      form.value.days = Math.abs(checkInDate.diff(checkOutDate, 'days'))\r\n    }\r\n    bookingRoomsList()\r\n  }\r\n}\r\n/** 禁用预抵时间 */\r\nfunction disabledCheckoutTime(time: any) {\r\n  const checkInDateTimeMp = new Date(form.value.planCheckinTime).getTime()\r\n  return time.getTime() <= checkInDateTimeMp\r\n}\r\nfunction updateDay() {\r\n  if (form.value.days > 0) {\r\n    if (myStartHour.value < earliestCheckinTime.value) {\r\n      form.value.planCheckoutTime = dayjs(form.value.planCheckinTime)\r\n        .add(form.value.days - 1, 'day')\r\n        .format(`YYYY-MM-DD ${myEndHour.value}`)\r\n    } else {\r\n      form.value.planCheckoutTime = dayjs(form.value.planCheckinTime).add(form.value.days, 'day').format(`YYYY-MM-DD ${myEndHour.value}`)\r\n    }\r\n    bookingRoomsList()\r\n  }\r\n}\r\nconst deatilVisabled = ref(false)\r\nconst deatilRtcode = ref()\r\n/** 点击可用房间 */\r\nfunction onClick(value: any) {\r\n  deatilRtcode.value = value.rtCode\r\n  deatilVisabled.value = true\r\n}\r\n/** =================================预定人输入搜索开始============================================= */\r\n/** 预订人姓名 */\r\ninterface dataListType {\r\n  id: number\r\n  /** 姓名 */\r\n  name: string\r\n  /** 手机号 */\r\n  phone: string\r\n  /** 身份证 */\r\n  idNo: string\r\n}\r\n/** 预订人列表 */\r\nconst tableData = ref<dataListType[]>([])\r\n/** 预订人搜索条件 */\r\nconst popoverQuery = reactive({\r\n  pageNo: 1,\r\n  pageSize: 10,\r\n})\r\n/** 预订人搜索名称 */\r\nconst queryString = ref()\r\n/** 预订人搜索名称(备份) */\r\nconst backQueryString = ref()\r\n/** 预订人定时器 */\r\nlet timeout: ReturnType<typeof setTimeout>\r\n/** 远程搜索预订人 */\r\nfunction querySearch(name: string, string: string, cb: (arg: any) => void) {\r\n  if (!string) {\r\n    return cb([])\r\n  }\r\n  queryString.value = string\r\n  clearTimeout(timeout)\r\n  // 值没有改变的情况下，不向后端发送请求\r\n  if (queryString.value !== backQueryString.value) {\r\n    tableData.value = []\r\n    timeout = setTimeout(async () => {\r\n      const { data } = await memberApi.getCustomerList({\r\n        ...queryParams,\r\n        ...popoverQuery,\r\n        [name]: queryString.value,\r\n      })\r\n      backQueryString.value = queryString.value\r\n      tableData.value = data.list || []\r\n      cb(tableData.value)\r\n    }, 1500 * Math.random())\r\n  } else {\r\n    cb(tableData.value)\r\n  }\r\n}\r\n/** table中二级表格选中 */\r\nfunction handleSelect(item: Record<string, any>) {\r\n  form.value.contact = item.name\r\n  form.value.phone = item.phone\r\n}\r\n/** =================================预定人输入搜索结束============================================= */\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('title')\" width=\"98%\" height=\"90vh\" :close-on-click-modal=\"false\" append-to-body destroy-on-close align-center :show-close=\"false\">\r\n    <template #header=\"{ close, titleId, titleClass }\">\r\n      <div class=\"custom-dialog-header\">\r\n        <span :id=\"titleId\" :class=\"titleClass\">{{ t('title') }}</span>\r\n        <el-button :icon=\"Close\" circle size=\"large\" @click=\"onCancel\" />\r\n      </div>\r\n    </template>\r\n\r\n    <div :class=\"{ 'absolute-container': data.tableAutoHeight }\" style=\"height: 80vh; background-color: #f2f3f5; overflow-y: auto\">\r\n      <div style=\"min-width: 1550px\">\r\n        <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" size=\"default\" label-width=\"120px\" inline-message inline class=\"search-form\">\r\n          <el-row>\r\n            <el-col :span=\"9\">\r\n              <page-main :title=\"t('orderInfo')\" class=\"ml-[10px] mt-[10px] py-[12px]\" style=\"min-width: 550px\">\r\n                <el-form-item :label=\"t('checkinType')\" class=\"w-[100%]\">\r\n                  <el-radio-group v-model=\"form.checkinType\" @change=\"onCheckinTypeChange\">\r\n                    <el-radio-button v-for=\"item in checkinTypeList\" :key=\"item.code\" :value=\"item.code\">\r\n                      {{ item.label }}\r\n                    </el-radio-button>\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-form-item :label=\"t('guestSrcType')\" class=\"w-[100%] !mr-[0px]\">\r\n                  <el-radio-group v-model=\"form.guestSrcType\" @change=\"onChange\">\r\n                    <el-radio-button v-for=\"item in srcTypeList\" :key=\"item.code\" :value=\"item.code\">\r\n                      {{ item.label }}\r\n                    </el-radio-button>\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-form-item v-if=\"form.guestSrcType === GuestSrcType.MEMBER || form.guestSrcType === GuestSrcType.AGENT || form.guestSrcType === GuestSrcType.PROTOCOL\" class=\"w-[100%]\" label=\" \">\r\n                  <template #label>\r\n                    {{ srcTypeList.filter((item) => item.code === form.guestSrcType)[0].label }}\r\n                  </template>\r\n                  <template v-if=\"form.guestSrcType === GuestSrcType.MEMBER\">\r\n                    <el-select v-model=\"form.guestCode\" filterable remote reserve-keyword :placeholder=\"t('enterFullPhoneNumber')\" :remote-method=\"remoteQueryMember\" :loading=\"data.loading\" style=\"width: 200px; margin-right: 8px\" @change=\"vipChange\">\r\n                      <el-option v-for=\"item in members\" :key=\"item.mcode\" :label=\"item.name\" :value=\"item.mcode\" />\r\n                    </el-select>\r\n                  </template>\r\n                  <template v-if=\"form.guestSrcType === GuestSrcType.AGENT\">\r\n                    <el-select v-model=\"form.guestCode\" filterable style=\"width: 200px\" :placeholder=\"t('selectAgent')\" @change=\"onChangeGuest\">\r\n                      <el-option v-for=\"item in agents\" :key=\"item.paCode\" :label=\"item.paName\" :value=\"item.paCode\" />\r\n                    </el-select>\r\n                  </template>\r\n\r\n                  <template v-if=\"form.guestSrcType === GuestSrcType.PROTOCOL\">\r\n                    <el-select v-model=\"form.guestCode\" filterable style=\"width: 200px\" :placeholder=\"t('selectProtocolUnit')\" @change=\"bookingRoomsList\">\r\n                      <el-option v-for=\"item in protocols\" :key=\"item.paCode\" :label=\"item.paName\" :value=\"item.paCode\" />\r\n                    </el-select>\r\n                  </template>\r\n                </el-form-item>\r\n                <el-form-item :label=\"t('planCheckinTime')\" class=\"w-[100%]\">\r\n                  <el-date-picker v-model=\"form.planCheckinTime\" :disabled-date=\"disabledDate\" :clearable=\"clearable\" class=\"!w-[120px]\" :placeholder=\"t('planCheckinTime')\" @change=\"CheckinTime\" />\r\n                  <el-time-picker v-model=\"form.planCheckinTime\" :placeholder=\"t('placeholder.arbitraryTime')\" :clearable=\"clearable\" format=\"HH:mm\" class=\"ml-[10px] !w-[80px]\" @change=\"changeHour\" />\r\n                </el-form-item>\r\n                <el-form-item :label=\"t('planCheckoutTime')\" class=\"w-[100%]\">\r\n                  <el-date-picker\r\n                    v-model=\"form.planCheckoutTime\"\r\n                    :disabled-date=\"disabledCheckoutTime\"\r\n                    :disabled=\"form.checkinType === CheckinType.HOUR_ROOM\"\r\n                    :clearable=\"clearable\"\r\n                    class=\"!w-[120px]\"\r\n                    :placeholder=\"t('planCheckoutTime')\"\r\n                    @change=\"checkOutTime\"\r\n                  />\r\n                  <el-time-picker\r\n                    v-model=\"form.planCheckoutTime\"\r\n                    :clearable=\"clearable\"\r\n                    :placeholder=\"t('placeholder.arbitraryTime')\"\r\n                    :disabled=\"form.checkinType === CheckinType.HOUR_ROOM\"\r\n                    format=\"HH:mm\"\r\n                    class=\"ml-[10px] !w-[80px]\"\r\n                    @change=\"changeOutHour\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item v-if=\"form.checkinType !== CheckinType.HOUR_ROOM\" class=\"w-[100%]\" :label=\"t('days')\">\r\n                  <el-input-number v-model=\"form.days\" :min=\"1\" :step=\"1\" :value-on-clear=\"1\" :precision=\"0\" :placeholder=\"t('days')\" class=\"!w-[130px]\" @change=\"updateDay\" />\r\n                  <el-checkbox v-model=\"form.retain\" true-value=\"1\" false-value=\"0\" class=\"!mx-[10px]\" :label=\"t('retain')\" />\r\n                  <el-date-picker v-if=\"form.retain === '1'\" v-model=\"form.retainTime\" type=\"datetime\" format=\"YYYY-MM-DD HH:mm\" :placeholder=\"t('selectRetainTime')\" class=\"!w-[170px]\" />\r\n                </el-form-item>\r\n                <el-form-item v-if=\"form.checkinType === CheckinType.HOUR_ROOM\" class=\"w-[100%] !mr-[0px]\" :label=\"t('checkinDuration')\">\r\n                  <el-select v-model=\"form.hourCode\" :placeholder=\"t('placeholder.checkinDuration')\" class=\"!w-[130px]\" @change=\"changeHour\">\r\n                    <el-option v-for=\"item in hourTypes\" :key=\"item.hourCode\" :label=\"item.hourName\" :value=\"item.hourCode\" />\r\n                  </el-select>\r\n                  <el-checkbox v-model=\"form.retain\" true-value=\"1\" false-value=\"0\" class=\"!mx-[10px]\" :label=\"t('retain')\" />\r\n                  <el-date-picker v-if=\"form.retain === '1'\" v-model=\"form.retainTime\" type=\"datetime\" format=\"YYYY-MM-DD HH:mm\" :placeholder=\"t('selectRetainTime')\" style=\"width: 200px\" />\r\n                </el-form-item>\r\n                <el-form-item :label=\"t('orderSource')\" class=\"!mr-[0px]\">\r\n                  <el-select v-model=\"form.orderSource\" :placeholder=\"t('placeholder.orderSource')\" class=\"!w-[130px]\">\r\n                    <el-option v-for=\"item in orderSources\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label=\"t('guarantyStyle')\" class=\"!mr-[0px]\">\r\n                  <el-select v-model=\"form.guarantyStyle\" :placeholder=\"t('placeholder.guarantyStyle')\" class=\"!w-[130px]\">\r\n                    <el-option v-for=\"item in dbs\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\">\r\n                      {{ item.label }}\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </page-main>\r\n              <page-main :title=\"t('guestInfo')\" style=\"margin-top: 10px; margin-left: 10px; flex: 1; display: flex; flex-direction: column\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item :label=\"t('contact')\" prop=\"contact\" style=\"width: 100%\">\r\n                      <el-autocomplete\r\n                        v-model=\"form.contact\"\r\n                        :placeholder=\"t('placeholder.contact')\"\r\n                        :fetch-suggestions=\"\r\n                          (queryString, cb) => {\r\n                            querySearch('name', queryString, cb)\r\n                          }\r\n                        \"\r\n                        @select=\"handleSelect\"\r\n                      >\r\n                        <template #default=\"{ item }\">\r\n                          <div class=\"flex-around\">\r\n                            <div class=\"w-[100px]\">\r\n                              {{ item.name }}\r\n                            </div>\r\n                            <div class=\"w-[120px]\">\r\n                              {{ item.phone }}\r\n                            </div>\r\n                            <div class=\"w-[60px] text-right\">\r\n                              {{ item.idNo.slice(-4) }}\r\n                            </div>\r\n                          </div>\r\n                        </template>\r\n                      </el-autocomplete>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item :label=\"t('contactPhone')\" style=\"width: 100%\">\r\n                      <ElInput v-model=\"form.phone\" :placeholder=\"t('placeholder.contactPhone')\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item :label=\"t('checkinPerson')\" style=\"width: 100%\">\r\n                      <ElInput v-model=\"form.checkinPerson\" :placeholder=\"t('placeholder.checkinPerson')\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item :label=\"t('checkinPhone')\" style=\"width: 100%\">\r\n                      <ElInput v-model=\"form.checkinPhone\" :placeholder=\"t('placeholder.checkinPhone')\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item :label=\"t('seller')\" style=\"width: 100%\">\r\n                      <el-select v-model=\"form.seller\" :placeholder=\"t('seller')\" style=\"width: 100%\">\r\n                        <el-option v-for=\"item in sellers\" :key=\"item.username\" :label=\"item.nickname\" :value=\"item.username\" />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item :label=\"t('orderNo')\" style=\"width: 100%\">\r\n                      <ElInput v-model=\"form.outOrderNo\" :placeholder=\"t('placeholder.orderNo')\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item :label=\"t('remark')\" style=\"width: 100%\">\r\n                      <ElInput v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('placeholder.remark')\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </page-main>\r\n            </el-col>\r\n            <el-col :span=\"15\">\r\n              <page-main class=\"ml-[-10px] mr-[10px] mt-[10px]\">\r\n                <template #title>\r\n                  <div class=\"flex-between\">\r\n                    {{ t('selectRoom') }}\r\n                    <el-switch v-if=\"![CheckinType.SELF_USE, CheckinType.FREE].includes(form.checkinType)\" v-model=\"changePrice\" :active-text=\"t('activeText')\" :inactive-text=\"t('inactiveText')\" inline-prompt />\r\n                  </div>\r\n                </template>\r\n                <el-table v-loading=\"data.loading\" :data=\"form.dataList\" height=\"100%\" row-key=\"rtCode\" :expand-row-keys=\"expandedRows\" border>\r\n                  <el-table-column type=\"expand\">\r\n                    <template #default=\"scope\">\r\n                      <div v-if=\"scope.row.bookRooms && scope.row.bookRooms.length > 0\">\r\n                        <el-tag v-for=\"(iem, index) in scope.row.bookRooms\" :key=\"iem.rNo\" style=\"margin-right: 5px; height: 30px; font-size: 14px; line-height: 28px\" closable @close=\"handleClose(scope.row, index)\">\r\n                          {{ iem.rNo }}\r\n                        </el-tag>\r\n                      </div>\r\n                      <span v-else>{{ t('reservedRoomCount') }}</span>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column prop=\"rtName\" :label=\"t('roomType')\" min-width=\"100\" />\r\n\r\n                  <el-table-column :label=\"t('discountPrice')\" min-width=\"165\" align=\"left\">\r\n                    <template #default=\"scope\">\r\n                      <div v-if=\"changePrice && ![CheckinType.SELF_USE, CheckinType.FREE].includes(form.checkinType)\" class=\"vipPrice\">\r\n                        <el-input-number v-model=\"scope.row.vipPrice\" :min=\"0\" :precision=\"2\" :controls=\"controls\" style=\"width: 70px; margin-right: 5px\" @blur=\"handleChange(scope.row)\" />\r\n                        / {{ scope.row.price }}\r\n                        <el-button type=\"primary\" text @click=\"checkType(scope.row)\">\r\n                          {{ t('adjustPrice') }}\r\n                        </el-button>\r\n                      </div>\r\n                      <div v-else class=\"vipPrice\">\r\n                        <el-input-number v-model=\"scope.row.vipPrice\" :min=\"0\" :precision=\"2\" :controls=\"controls\" style=\"width: 100px; margin-right: 5px\" disabled />\r\n                        / {{ scope.row.price }}\r\n                      </div>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column :label=\"t('availableRooms')\" prop=\"canSellNum\" align=\"right\" min-width=\"60px\">\r\n                    <template #default=\"scope\">\r\n                      <el-link type=\"primary\" :underline=\"false\" @click=\"onClick(scope.row)\">\r\n                        {{ scope.row.canSellNum }}\r\n                      </el-link>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column :label=\"t('overbookableRoomNumber')\" prop=\"canOverNum\" align=\"right\" min-width=\"60px\" />\r\n\r\n                  <el-table-column :label=\"t('reservedRoomCount')\" align=\"center\" min-width=\"110px\">\r\n                    <template #default=\"scope\">\r\n                      <el-input-number v-model=\"scope.row.roomNum\" :min=\"0\" :max=\"scope.row.canSellNum + scope.row.canOverNum\" :precision=\"0\" :value-on-clear=\"0\" style=\"width: 110px\" @change=\"changeRoomNum(scope.row)\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column :label=\"t('guestBreakfast')\" align=\"center\" min-width=\"80px\">\r\n                    <template #default=\"scope\">\r\n                      <el-input-number v-model=\"scope.row.bkNum\" :min=\"0\" :precision=\"0\" :value-on-clear=\"0\" :controls=\"controls\" style=\"width: 55px\" />\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column :label=\"t('action')\" min-width=\"60px\">\r\n                    <template #default=\"scope\">\r\n                      <el-button type=\"primary\" size=\"small\" @click=\"addRoom(scope.row)\">\r\n                        {{ t('arrangeRoom') }}\r\n                      </el-button>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </page-main>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n    <template #footer>\r\n      <el-checkbox v-model=\"form.isSendSms\" true-value=\"1\" false-value=\"0\" :label=\"t('smsReminder')\" />&nbsp;&nbsp;\r\n      <el-button @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" @click=\"onBookingSubmit\">\r\n        {{ t('processReservation') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n  <!--  排房弹窗 -->\r\n  <ArrangeRoomsDialog\r\n    v-if=\"arrangeRoomsProps.visible\"\r\n    v-model=\"arrangeRoomsProps.visible\"\r\n    :rt-code=\"arrangeRoomsProps.rtCode\"\r\n    :r-nos=\"arrangeRoomsProps.rNos\"\r\n    :rooms=\"rooms\"\r\n    :plan-checkin-time=\"arrangeRoomsProps.planCheckinTime\"\r\n    :plan-checkout-time=\"arrangeRoomsProps.planCheckoutTime\"\r\n    :rt-name=\"arrangeRoomsProps.rtName\"\r\n    :room-num=\"arrangeRoomsProps.roomNum\"\r\n    :rt-state=\"arrangeRoomsProps.rtState\"\r\n    @success=\"selectRooms\"\r\n  />\r\n\r\n  <!--    改价窗口 -->\r\n  <RoomEditPrice v-if=\"roomPriceProps.visible\" v-model=\"roomPriceProps.visible\" :room-list-price=\"roomPriceProps.initialPriceList\" :rt-code=\"roomPriceProps.rtCode\" @success=\"echoList\" />\r\n  <!-- 房型占用详情窗口 -->\r\n  <BookDetails v-if=\"deatilVisabled\" v-model=\"deatilVisabled\" :plan-checkin-time=\"startTime\" :plan-checkout-time=\"endTime\" :deatil-rtcode=\"deatilRtcode\" />\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 自定义头部样式 */\r\n.custom-dialog-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0;\r\n}\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.form-contact {\r\n  padding: 0;\r\n  .contact-title {\r\n    @include flex-around();\r\n    height: 34px;\r\n    line-height: 34px;\r\n    background-color: var(--el-border-color-light);\r\n  }\r\n  .contact-box {\r\n    height: 100%;\r\n    overflow: auto;\r\n    max-height: 274px;\r\n    .contact-no {\r\n      line-height: 100px;\r\n      text-align: center;\r\n    }\r\n    .contact-loading {\r\n      min-height: 80px;\r\n    }\r\n    ul {\r\n      box-sizing: border-box;\r\n      list-style: none;\r\n      margin: 0;\r\n      padding: 6px 0;\r\n      li {\r\n        box-sizing: border-box;\r\n        color: var(--el-text-color-regular);\r\n        cursor: pointer;\r\n        font-size: var(--el-font-size-base);\r\n        height: 34px;\r\n        line-height: 34px;\r\n        overflow: hidden;\r\n        padding: 0 32px 0 20px;\r\n        position: relative;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n      :hover {\r\n        background-color: var(--el-fill-color-light);\r\n      }\r\n    }\r\n  }\r\n}\r\n:deep(.vipPrice) {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: left;\r\n\r\n  .el-input-number .el-input__wrapper {\r\n    padding: 0 10px;\r\n  }\r\n\r\n  .el-input-number .el-input__inner {\r\n    text-align: left;\r\n  }\r\n\r\n  .price {\r\n    display: inline-block;\r\n    width: 40px;\r\n  }\r\n}\r\n\r\n//:deep(.el-input-number .is-without-controls .el-input__wrapper){\r\n//  padding: 0;\r\n//}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "formRef", "ref", "formRules", "contact", "required", "message", "trigger", "myVisible", "computed", "get", "modelValue", "set", "val", "userStore", "useUserStore", "data", "loading", "tableAutoHeight", "myStartHour", "myEndHour", "earliestCheckinTime", "hourNum", "oldType", "startTime", "queryParams", "reactive", "gcode", "hcode", "endTime", "rooms", "form", "channelCode", "orderSource", "guestSrcType", "GuestSrcType", "WALK_IN", "bookType", "OrderType", "GENERAL", "checkinType", "CheckinType", "ALL_DAY", "hourCode", "guest<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "planCheckinTime", "dayjs", "format", "planCheckoutTime", "add", "days", "phone", "checkin<PERSON><PERSON>", "checkinPhone", "guarantyStyle", "seller", "retain", "retainTime", "isSendSms", "marketActivityCode", "outOrderNo", "remark", "dataList", "changePrice", "agents", "protocols", "serverTime", "setTime", "setBookingData", "bookData", "planCheckinDate", "value", "targetDate", "today", "isSame", "onMounted", "async", "prms", "isEnable", "channelApi", "getChannelSimpleList", "channels", "getChannels", "hourRoomTypeApi", "getHourRoomTypeList", "hourTypes", "getHour", "userApi", "listSeller", "sellers", "getSellers", "customerApi", "simpleList", "belongHcode", "paType", "BooleanEnum", "YES", "getAgents", "getProtocols", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "then", "res", "checkinTypeList", "filter", "item", "dictType", "DICT_TYPE_CHECKIN_TYPE", "code", "srcTypeList", "DICT_TYPE_GUEST_SRC_TYPE", "orderSources", "ORDER_SOURCE", "dbs", "DICT_TYPE_GUARANTEE_TYPE", "getConstants", "serverTimeApi", "generalConfigApi", "getGeneralConfig", "GeneralConfigTypeEnum", "CHECKIN_TIME", "type", "now", "getOutTime", "fetchTime", "params", "priceAllDayRuleApi", "getPriceAllDayRule", "getEarliestCheckinTime", "watch", "deep", "CONSTANT_TYPE_CODE_SZ", "mtCode", "MEMBER", "bookApi", "checkOutTime", "bookingRoomsList", "catch", "getTime", "getCheckOutTime", "delayMinute", "HOUR_ROOM", "roomtypeList", "map", "_item", "vipPrice", "dayPrices", "SELF_USE", "FREE", "includes", "rtCode", "rtName", "state", "rtState", "bookRooms", "preOccupied", "rNo", "rCode", "bookingVisible", "addRooms", "arrangeRoomsProps", "visible", "rNos", "roomNum", "expandedRows", "getExpanded", "list", "for<PERSON>ach", "_a", "length", "push", "canSellNum", "canOverNum", "selectRooms", "onCancel", "onChange", "onCheckinTypeChange", "onChangeGuest", "find", "v", "paCode", "channel", "detailVisible", "onBookingSubmit", "validate", "valid", "ElMessage", "error", "bookRoomTypes", "bookNo", "teamCode", "teamName", "contractNo", "ymdate", "ymdateHMS", "substring", "marketActivityName", "batches", "batchNo", "orderNo", "createBook", "success", "eventBus", "emit", "emitSuccessData", "noType", "NoType", "BOOK", "no", "msg", "members", "remoteQueryMember", "query", "validateTipPhone", "setTimeout", "idNo", "memberApi", "getMemberNoPage", "getInfo", "controls", "clearable", "disabledDate", "time", "Date", "roomPriceProps", "initialPriceList", "changeOutHour", "vipChange", "mcode", "name", "warning", "hour", "LONG_STAY", "echoList", "echoId", "CheckinTime", "specificDate", "changeHour", "checkInDate", "checkOutDate", "Math", "abs", "diff", "disabledCheckoutTime", "checkInDateTimeMp", "updateDay", "deatilVisabled", "deatilRtcode", "tableData", "popover<PERSON><PERSON><PERSON>", "pageNo", "pageSize", "queryString", "backQueryString", "timeout", "handleSelect", "string", "cb", "clearTimeout", "getCustomerList", "random", "row", "index", "x", "findIndex", "splice", "bookedRoomsCount", "isAfter", "ls"], "mappings": "g0FAmQA,MAAMA,GAAQC,EAoBRC,GAAQC,IAIRC,EAAEA,IAAMC,IAERC,GAAUC,IAEVC,GAAYD,EAAe,CAC/BE,QAAS,CAAC,CAAEC,UAAU,EAAMC,QAASP,GAAE,uBAAwBQ,QAAS,WAGpEC,GAAYC,EAAS,CACzBC,IAAM,IACGf,GAAMgB,WAEf,GAAAC,CAAIC,GACFhB,GAAM,oBAAqBgB,EAAG,IAG5BC,GAAYC,IACZC,GAAOd,EAAI,CACfe,SAAS,EAETC,iBAAiB,IAGbC,GAAcjB,EAAI,IAClBkB,GAAYlB,EAAI,IAChBmB,GAAsBnB,EAAI,IAC1BoB,GAAUpB,EAAI,GACdqB,GAAUrB,GAAI,GAEdsB,GAAYtB,EAAI,IAEhBuB,GAAcC,EAAS,CAE3BC,MAAOb,GAAUa,MAEjBC,MAAOd,GAAUc,QAGbC,GAAU3B,EAAI,IACd4B,GAAQ5B,EAAI,IACZ6B,GAAO7B,EAAI,IACZuB,GAEHO,YAAa,QAEbC,YAAa,QAEbC,aAAcC,GAAaC,QAE3BC,SAAUC,GAAUC,QAEpBC,YAAaC,GAAYC,QACzBC,SAAU,GAEVC,UAAW,GAEXC,UAAW,GAEXC,gBAAiBC,IAAQC,OAAO,oBAGhCC,iBAAkBF,IAAQG,IAAI,EAAG,OAAOF,OAAO,oBAC/CG,KAAM,EAEN/C,QAAS,GAETgD,MAAO,GAEPC,cAAe,GAEfC,aAAc,GAEdC,cAAe,KAEfC,OAAQ,KACRC,OAAQ,IAERC,WAAYX,IAAQC,OAAO,oBAE3BW,UAAW,IAEXC,mBAAoB,GAEpBC,WAAY,GAEZC,OAAQ,GACRC,SAAU,KAENC,GAAc9D,GAAI,GAElB+D,GAAS/D,EAA6B,IAEtCgE,GAAYhE,EAA6B,IAEzCiE,GAAajE,IAEbkE,GAAUlE,IAEhB,SAASmE,KACP,GAAI1E,GAAM2E,WAAa3E,GAAM2E,SAASxB,iBAAmBnD,GAAM2E,SAASC,iBAClE,GAAA5E,GAAM2E,SAASxB,gBAEZf,GAAAyC,MAAM1B,gBAAkBnD,GAAM2E,SAASxB,gBAExC1B,GAAUoD,QACZzC,GAAKyC,MAAMvB,iBAAmB,GAAGF,EAAMhB,GAAKyC,MAAM1B,iBAAiBI,IAAI,EAAG,OAAOF,OAAO,iBAAiB5B,GAAUoD,cACrH,GACS7E,GAAM2E,SAASC,gBAAiB,CAEnC,MAAAE,EAAa9E,GAAM2E,SAASC,gBAC5BG,EAAQ3B,EAAMoB,GAAWK,OAG/B,GAFgBE,EAAMC,OAAOF,EAAY,OAE5B,CAECC,EAAM1B,OAAO,SACfoB,GAAQI,MAChBzC,GAAKyC,MAAM1B,gBAAkB4B,EAAM1B,OAAO,oBAE1CjB,GAAKyC,MAAM1B,gBAAkB,GAAG2B,KAAcL,GAAQI,OACxD,MAGAzC,GAAKyC,MAAM1B,gBAAkB,GAAG2B,KAAcL,GAAQI,QAGpDpD,GAAUoD,QACZzC,GAAKyC,MAAMvB,iBAAmB,GAAGF,EAAMhB,GAAKyC,MAAM1B,iBAAiBI,IAAI,EAAG,OAAOF,OAAO,iBAAiB5B,GAAUoD,QACrH,CAEJ,CAGFI,GAAUC,WA+GVA,iBACE,MAAMC,EAAO,IACRrD,GACHsD,SAAU,MAEJ/D,KAAAA,SAAegE,EAAWC,qBAAqBH,GACvDI,GAASV,MAAQxD,CAAA,CApHLmE,GAgOdN,iBACE,MAAQ7D,KAAAA,SAAeoE,GAAgBC,oBAAoB5D,IAC3D6D,GAAUd,MAAQxD,CAAA,CAjOVuE,GAyHVV,iBACE,MAAQ7D,KAAAA,SAAewE,GAAQC,WAAWhE,IAC1CiE,GAAQlB,MAAQxD,CAAA,CA1HL2E,GAoFbd,iBACE,MAAQ7D,KAAAA,SAAe4E,EAAYC,WAAW,CAC5ClE,MAAOb,GAAUa,MACjBmE,YAAahF,GAAUc,MACvBmE,OAAQ,IACRhB,SAAUiB,GAAYC,MAExBhC,GAAOO,MAAQxD,CAAA,CA1FLkF,GA8FZrB,iBACE,MAAQ7D,KAAAA,SAAe4E,EAAYC,WAAW,CAC5ClE,MAAOb,GAAUa,MACjBmE,YAAahF,GAAUc,MACvBmE,OAAQ,IACRhB,SAAUiB,GAAYC,MAExB/B,GAAUM,MAAQxD,CAAA,CApGLmF,GAuEftB,uBACQuB,GAAYC,iBAAiBC,IAAWC,MAAMC,IAClCC,GAAAjC,MAAQgC,EAAIxF,KAAK0F,QAAQC,GAAcA,EAAKC,WAAaC,KACzDJ,GAAAjC,MAAQiC,GAAgBjC,MAAMkC,QAAQC,GAA4B,iBAAdA,EAAKG,MAAyC,kBAAdH,EAAKG,OAC7FC,GAAAvC,MAAQgC,EAAIxF,KAAK0F,QAAQC,GAAcA,EAAKC,WAAaI,KACzDD,GAAAvC,MAAQuC,GAAYvC,MAAMkC,QAAQC,GAAuB,MAAdA,EAAKG,MAA8B,MAAdH,EAAKG,OACpEG,GAAAzC,MAAQgC,EAAIxF,KAAK0F,QAAQC,GAAcA,EAAKC,WAAaM,KAClEC,GAAA3C,MAAQgC,EAAIxF,KAAK0F,QAAQC,GAAcA,EAAKC,WAAaQ,IAAwB,GACtF,CA9EYC,SA2efxC,iBACQ,MAAE7D,KAAAA,SAAesG,GAAcnD,WAAWrD,GAAUa,MAAO,KAC3D6E,QAAYe,EAAiBC,iBAAiB,IAC/C/F,GACHqF,KAAMW,GAAsBC,aAC5BC,KAAMF,GAAsBC,eAE9BvD,GAAWK,MAAQxD,EAEnB,MAAM4G,EAAM7E,EAAMoB,GAAWK,OAAOxB,OAAO,SAEnCoB,GAAAI,MAAQgC,EAAIxF,KAAKwD,MAErBoD,EAAMxD,GAAQI,MAChBzC,GAAKyC,MAAM1B,gBAAkBC,EAAMoB,GAAWK,OAAOxB,OAAO,oBAE5DjB,GAAKyC,MAAM1B,gBAAkB,GAAGC,EAAMoB,GAAWK,OAAOxB,OAAO,iBAAiBoB,GAAQI,QAE1FrD,GAAYqD,MAAQzB,EAAMhB,GAAKyC,MAAM1B,iBAAiBE,OAAO,SAClD6E,IAAA,CA7fLC,GA+BR,WACE,MAAMC,EAAS,CACbpG,MAAOb,GAAUa,MACjBC,MAAOd,GAAUc,OAEnBoG,GAAmBC,mBAAmBF,GAAQxB,MAAMC,IACjC,IAAbA,EAAIM,OACczF,GAAAmD,MAAQgC,EAAIxF,KAAKK,oBAAA,GAExC,CAtCsB6G,GAGR7D,IAAA,IAIjB8D,GACE,IAAMxI,GAAM2E,WACZ,KACiBD,IAAA,GAEjB,CAAE+D,MAAM,IAIV,MAAM9B,GAAY,CAACU,GAA0BH,GAAwBK,GAAcmB,GAAuBjB,IAEpGL,GAAc7G,EAAuB,IAErCuG,GAAkBvG,EAAuB,IAEzC+G,GAAe/G,EAAuB,IAEtCoF,GAAYpF,EAA4D,IAExEiH,GAAMjH,EAAuB,IAgBnC,SAAS2H,KACP,MAAME,EAAS,IACVtG,GACHO,YAAaD,GAAKyC,MAAMxC,YACxBE,aAAcH,GAAKyC,MAAMtC,aACzBD,YAAaF,GAAKyC,MAAMvC,YACxBa,gBAAiBC,EAAMhB,GAAKyC,MAAM1B,iBAAiBE,OAAO,cAC1DC,iBAAkBF,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,cAC5DsF,OAAQvG,GAAKyC,MAAMtC,eAAiBC,GAAaoG,OAASxG,GAAKyC,MAAM5B,UAAY,IAGnF4F,EACGC,aAAaV,GACbxB,MAAMC,IACY,IAAbA,EAAIM,OACI1F,GAAAoD,MAAQgC,EAAIxF,KAAKyH,aAC3B1G,GAAKyC,MAAMvB,iBAAmB,GAAGF,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,iBAAiB5B,GAAUoD,QAEvFH,KACEqE,KAAA,IAGpBC,OAAM,CAoCL,MAAAzD,GAAWhF,EAAoD,IAY/D,MAAAwF,GAAUxF,EAA8C,IAO9D2E,eAAe+D,KACb,MAAQ5H,KAAAA,SAAeuG,EAAiBsB,gBAAgBpH,IACxDL,GAAUoD,MAAQxD,EAAKwD,MACvBzC,GAAKyC,MAAMvB,iBAAmB,GAAGF,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,iBAAiB5B,GAAUoD,QAEvFH,KACEqE,IAAA,CAGnB,SAASA,KACP1H,GAAKwD,MAAMvD,SAAU,EACrBO,GAAUgD,MAAQzB,EAAMhB,GAAKyC,MAAM1B,iBAAiBE,OAAO,oBAC3DnB,GAAQ2C,MAAQzB,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,oBAC1D,MAAM+E,EAAS,IACVtG,GACHO,YAAaD,GAAKyC,MAAMxC,YACxBQ,YAAaT,GAAKyC,MAAMhC,YACxBN,aAAcH,GAAKyC,MAAM5B,UAAYb,GAAKyC,MAAMtC,aAAeC,GAAaC,QAC5EQ,UAAWb,GAAKyC,MAAM5B,UACtBkG,YAAa,EACbnG,SAAUZ,GAAKyC,MAAMhC,cAAgBC,GAAYsG,UAAYhH,GAAKyC,MAAM7B,SAAW,GACnFV,YAAaF,GAAKyC,MAAMvC,YACxBa,gBAAiBtB,GAAUgD,MAC3BvB,iBAAkBpB,GAAQ2C,OAE5BgE,EAAQQ,aAAajB,GAAQxB,MAAMC,IAC7B,GAAa,IAAbA,EAAIM,KAAY,CAClB9F,GAAKwD,MAAMvD,SAAU,EACrBc,GAAKyC,MAAMT,SAAWyC,EAAIxF,KAAKiI,KAAKC,IAC3B,IACFA,EACHC,SAAUD,EAAME,UAAU,GAAGD,aAG7B,CAAC1G,GAAY4G,SAAU5G,GAAY6G,MAAMC,SAASxH,GAAKyC,MAAMhC,eAC/DT,GAAKyC,MAAMT,SAAWyC,EAAIxF,KAAKiI,KAAKC,IAC3B,IACFA,EACHC,SAAU,OAIhB,MAAMrE,EAAO,CACX0E,OAAQ7J,GAAM6J,OACdC,OAAQ9J,GAAM8J,OACdC,MAAO/J,GAAMgK,QACbC,UAAW,CACT,CACEC,YAAa,IACbH,MAAO/J,GAAMgK,QACbG,IAAKnK,GAAMmK,IACXC,MAAOpK,GAAMoK,SAIfpK,GAAM2E,SAAS0F,gBACRC,GAAA,CACPT,OAAQ7J,GAAM2E,SAASsF,UAAUJ,OACjCI,UAAW,CAACjK,GAAM2E,SAASsF,aAEvB9H,GAAA0C,MAAQ7E,GAAM2E,SAASsF,WAE7BK,GAASnF,EACX,IAEH,CAqBH,MAAMoF,GAAoBhK,EAAI,CAC5BiK,SAAS,EACTX,OAAQ,GACRC,OAAQ,GACRE,QAAS,GACTS,KAAM,GACNC,QAAS,EACTvH,gBAAiB,GACjBG,iBAAkB,KAgCd,MAAAqH,GAAepK,EAAI,IACzB,SAASqK,KACP,MAAMC,EAAO,GACbzI,GAAKyC,MAAMT,SAAS0G,SAAS9D,UACvBA,EAAKiD,YAAa,OAAAc,EAAA/D,EAAKiD,gBAAL,EAAAc,EAAgBC,QAAS,GACxCH,EAAAI,KAAKjE,EAAK6C,OAAM,IAGzBc,GAAa9F,MAAQgG,CAAA,CAEvB,SAASP,GAASjJ,GAChBe,GAAKyC,MAAMT,SAAS0G,SAAS9D,IACvBA,EAAK6C,SAAWxI,EAAKwI,QAAU7C,EAAKkE,WAAalE,EAAKmE,WAAa,IACrEnE,EAAKiD,UAAY5I,EAAK4I,UACtBjD,EAAK0D,QAAU,EAAA,IAGPE,IAAA,CAGd,SAASQ,GAAY/J,GACnBe,GAAKyC,MAAMT,SAAS0G,SAAS9D,IACvBA,EAAK6C,SAAWxI,EAAKwI,SACvB7C,EAAKiD,UAAY5I,EAAK4I,UAAA,IAGdW,IAAA,CAGd,SAASS,KACPxK,GAAUgE,OAAQ,EAClBzC,GAAKyC,MAAMpE,QAAU,GACrB2B,GAAKyC,MAAMpB,MAAQ,GACnBrB,GAAKyC,MAAMnB,cAAgB,GAC3BtB,GAAKyC,MAAMlB,aAAe,GAC1BvB,GAAKyC,MAAMhB,YAAS,EACpBzB,GAAKyC,MAAMV,OAAS,EAAA,CAGtB,SAASmH,KACPlJ,GAAKyC,MAAM5B,UAAY,GACvBb,GAAKyC,MAAMxC,YAAc,QAGLkJ,IAAA,CAEtB,SAASC,WACHpJ,GAAKyC,MAAM5B,YACbb,GAAKyC,MAAMxC,YAAc,OAAA0I,EAAAzG,GAAOO,MAAM4G,MAAMC,GACnCA,EAAEC,SAAWvJ,GAAKyC,MAAM5B,kBAC7B,EAAA8H,EAAAa,SAEW7C,IAAA,CAGb,MAAA8C,GAAgBtL,GAAI,GAK1B,SAASuL,KACPxL,GAAQuE,OACNvE,GAAQuE,MAAMkH,UAAUC,IACtB,GAAIA,EAAO,CACL,IAAC5J,GAAKyC,MAAMpE,QAEd,YADUwL,EAAAC,MAAM9L,GAAE,wBAGd,MAAA+L,EAAgB/J,GAAKyC,MAAMT,SAAS2C,QAAQC,GAASA,EAAK0D,QAAU,IACpEtC,EAAS,IACVtG,GACHsK,OAAQ,GACR/J,YAAaD,GAAKyC,MAAMxC,YACxBC,YAAaF,GAAKyC,MAAMvC,YACxBC,aAAcH,GAAKyC,MAAMtC,aACzBG,SAAU,UACVG,YAAaT,GAAKyC,MAAMhC,YACxBG,SAAUZ,GAAKyC,MAAM7B,SACrBqJ,SAAU,GACVpJ,UAAWb,GAAKyC,MAAM5B,UACtBC,UAAWd,GAAKyC,MAAM3B,UACtBoJ,SAAU,GACVC,WAAY,GACZpJ,gBAAiB,GAAGqJ,GAAOpK,GAAKyC,MAAM1B,oBAAoBsJ,GAAUrK,GAAKyC,MAAM1B,iBAAiBuJ,UAAU,GAAI,MAC9GpJ,iBAAkB,GAAGkJ,GAAOpK,GAAKyC,MAAMvB,qBAAqBmJ,GAAUrK,GAAKyC,MAAMvB,kBAAkBoJ,UAAU,GAAI,MACjHjM,QAAS2B,GAAKyC,MAAMpE,QACpBgD,MAAOrB,GAAKyC,MAAMpB,MAClBC,cAAetB,GAAKyC,MAAMnB,cAC1BC,aAAcvB,GAAKyC,MAAMlB,aACzBC,cAAexB,GAAKyC,MAAMjB,cAC1BG,WAAY,GACZoF,YAAa,EACbnF,UAAW5B,GAAKyC,MAAMb,UACtBC,mBAAoB,GACpB0I,mBAAoB,GACpB9I,OAAQzB,GAAKyC,MAAMhB,OACnBK,WAAY9B,GAAKyC,MAAMX,WACvBC,OAAQ/B,GAAKyC,MAAMV,OACnByI,QAAS,CACP,CACEC,QAAS,GAAGL,GAAOpK,GAAKyC,MAAM1B,oBAAoBqJ,GAAOpK,GAAKyC,MAAMvB,oBACpEE,KAAMpB,GAAKyC,MAAMrB,KACjBL,gBAAiB,GAAGqJ,GAAOpK,GAAKyC,MAAM1B,oBAAoBsJ,GAAUrK,GAAKyC,MAAM1B,iBAAiBuJ,UAAU,GAAI,MAC9GpJ,iBAAkB,GAAGkJ,GAAOpK,GAAKyC,MAAMvB,qBAAqBmJ,GAAUrK,GAAKyC,MAAMvB,kBAAkBoJ,UAAU,GAAI,MACjHP,mBAMFnM,GAAM2E,UAAY3E,GAAM2E,SAASmI,UAC5B1E,EAAA0E,QAAU9M,GAAM2E,SAASmI,SAIR,MAAtB1K,GAAKyC,MAAMf,SACbsE,EAAOrE,WAAa,GAAGyI,GAAOpK,GAAKyC,MAAMd,eAAe0I,GAAUrK,GAAKyC,MAAMd,YAAY2I,UAAU,GAAI,OAGzG7D,EAAQkE,WAAW3E,GAAQxB,MAAMC,IAC3B,GAAa,IAAbA,EAAIM,KAAY,CACR8E,EAAAe,QAAQ5M,GAAE,mBACpByL,GAAchH,OAAQ,EACboI,GAAAC,KAAK,uBAAwB,sBACtC,MAAMC,EAAkB,CACtBC,OAAQC,GAAOC,KACfC,GAAI1G,EAAIxF,MAEVnB,GAAM,UAAWiN,GACR9B,IAAA,MAECY,EAAAC,MAAMrF,EAAI2G,IAAG,GAE1B,IAEJ,CA/EcjN,EAAI,UACNA,EAAI,cAgGf,MAAAkN,GAAUlN,EAA8B,IAE9C,SAASmN,GAAkBC,GAEZC,GAAiBD,KAE5BF,GAAQ5I,MAAQ,GAChBxD,GAAKwD,MAAMvD,SAAU,EACrBuM,YAAW3I,UACT7D,GAAKwD,MAAMvD,SAAU,EACf,MAAAuF,QAxBZ3B,eAAuByI,GACrB,MAAMvF,EAAS,CACbpG,MAAOb,GAAUa,MACjByB,MAAOkK,EAAM3C,OAAS,GAAK,GAAK2C,EAChCG,KAAMH,EAAM3C,OAAS,GAAK2C,EAAQ,GAClC5D,MAAO1D,GAAYC,KAGfO,QAAYkH,EAAUC,gBAAgB5F,GACxC,GAAAvB,GAAQA,EAAIxF,KACd,OAAOwF,EAAIxF,KAAKwJ,IAClB,CAasBoD,CAAQN,GACtB9G,IACF4G,GAAQ5I,MAAQgC,EAAA,GAEjB,KAIL,CAGI,MAAAqH,GAAW3N,GAAI,GACf4N,GAAY5N,GAAI,GACtB,SAAS6N,GAAaC,GACpB,OAAOA,EAAKpF,UAAYqF,KAAKrG,MAAQ,KAAA,CAGvC,MAAMsG,GAAiBhO,EAAI,CACzBiK,SAAS,EACTX,OAAQ,GACR2E,iBAAkB,KAGpB,SAASC,KACPhN,GAAUoD,MAAQzB,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,SAC3C0F,IAAA,CAGnB,SAAS2F,GAAU7J,GACAkE,KACX1H,MAAAA,EAAOoM,GAAQ5I,MAAMkC,QAAQC,GAAcA,EAAK2H,QAAU9J,IAAO,GAClEzC,GAAAyC,MAAMpE,QAAUY,EAAKuN,KACrBxM,GAAAyC,MAAMpB,MAAQpC,EAAKoC,KAAA,CAG1ByB,eAAeqG,KAkBb,GAAInJ,GAAKyC,MAAMhC,cAAgBC,GAAYsG,UAAW,CAEpD,GADAxH,GAAQiD,OAAQ,GACXc,GAAUd,OAASc,GAAUd,MAAMmG,OAAS,EAE/C,YADUiB,EAAA4C,QAAQzO,GAAE,gBAGtBgC,GAAKyC,MAAM7B,SAAW2C,GAAUd,MAAM,GAAG7B,SACzCrB,GAAQkD,MAAQc,GAAUd,MAAM,GAAGiK,KACnC1M,GAAKyC,MAAM1B,gBAAkBC,IAAQC,OAAO,oBAC5CjB,GAAKyC,MAAMvB,iBAAmBF,EAAMhB,GAAKyC,MAAM1B,iBAAiBI,IAAI5B,GAAQkD,MAAO,QAAQxB,OAAO,oBACjF0F,IAAA,MAEbnH,GAAQiD,QACVjD,GAAQiD,OAAQ,EAChBzC,GAAKyC,MAAMrB,KAAO,EAClBpB,GAAKyC,MAAM1B,gBAAkBC,IAAQC,OAAO,oBACvCjB,GAAAyC,MAAMvB,iBAAmBF,IAAQG,IAAI,EAAG,OAAOF,OAAO,oBAC3DjB,GAAKyC,MAAM7B,SAAW,IAEpB,CAACF,GAAYC,QAASD,GAAYiM,WAAWnF,SAASxH,GAAKyC,MAAMhC,aACxDqF,KAEHe,IAEZ,CAoCO,SAAA+F,GAASnK,EAAmCoK,GACnD7M,GAAKyC,MAAMT,SAAS0G,SAAS9D,IACvBA,EAAK6C,SAAWoF,IAClBjI,EAAKyC,UAAY5E,EACZmC,EAAAwC,SAAW3E,EAAM,GAAG2E,SAAA,GAE5B,CAGH,SAAS0F,GAAYhO,GAEnB,MAAMiO,EAAe/L,EAAMlC,GAAKmC,OAAO,cAEjC0B,EAAQ3B,EAAMoB,GAAWK,OAG/B,GADgBE,EAAMC,OAAOmK,EAAc,OAC9B,CAECpK,EAAM1B,OAAO,SAEfoB,GAAQI,MAChBzC,GAAKyC,MAAM1B,gBAAkB4B,EAAM1B,OAAO,oBAE1CjB,GAAKyC,MAAM1B,gBAAkB4B,EAAM1B,OAAO,cAAgBoB,GAAQI,MAEpErD,GAAYqD,MAAQzB,EAAMhB,GAAKyC,MAAM1B,iBAAiBE,OAAO,QAAO,MAEpEjB,GAAKyC,MAAM1B,gBAAkB,GAAGgM,KAAgB1K,GAAQI,QAE1DzC,GAAKyC,MAAMvB,iBAAmB,GAAGF,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,iBAAiB5B,GAAUoD,QAClGzC,GAAKyC,MAAMhC,cAAgB6F,GAC7BtG,GAAKyC,MAAMvB,iBAAmBF,EAAMhB,GAAKyC,MAAM1B,iBAAiBI,IAAI5B,GAAQkD,MAAO,QAAQxB,OAAO,oBACzFD,EAAMhB,GAAKyC,MAAM1B,iBAAiBE,OAAO,eAAiBD,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,gBAC7GjB,GAAKyC,MAAMvB,iBAAmB,GAAGF,EAAMhB,GAAKyC,MAAM1B,iBAAiBI,IAAI,EAAG,OAAOF,OAAO,iBAAiB5B,GAAUoD,SAEpGkE,IAAA,CAGnB,SAASqG,WAUH,GATJ5N,GAAYqD,MAAQzB,EAAMhB,GAAKyC,MAAM1B,iBAAiBE,OAAO,SAC7D1B,GAAQkD,MAAQ,OAAAkG,EAAApF,GAAUd,MAAM4G,MAAMlC,GAC7BA,EAAMvG,WAAaZ,GAAKyC,MAAM7B,iBACnC,EAAA+H,EAAA+D,KACA1M,GAAKyC,MAAMhC,cAAgB6F,GAC7BtG,GAAKyC,MAAMvB,iBAAmBF,EAAMhB,GAAKyC,MAAM1B,iBAAiBI,IAAI5B,GAAQkD,MAAO,QAAQxB,OAAO,oBAE1F4F,KAEN7G,GAAKyC,MAAMrB,KAAO,EAAG,CACvB,MAAM6L,EAAcjM,EAAMoJ,GAAOpK,GAAKyC,MAAM1B,kBACtCmM,EAAelM,EAAMoJ,GAAOpK,GAAKyC,MAAMvB,mBAEzC9B,GAAYqD,MAAQnD,GAAoBmD,MACrCzC,GAAAyC,MAAMrB,KAAO+L,KAAKC,IAAIH,EAAYI,KAAKH,EAAc,SAAW,EAEhElN,GAAAyC,MAAMrB,KAAO+L,KAAKC,IAAIH,EAAYI,KAAKH,EAAc,QAC5D,CAEevG,IAAA,CAInB,SAASD,KAEH,GADJ1G,GAAKyC,MAAMvB,iBAAmB,GAAGF,EAAMhB,GAAKyC,MAAMvB,kBAAkBD,OAAO,iBAAiB5B,GAAUoD,QAClGzC,GAAKyC,MAAMrB,KAAO,EAAG,CACvB,MAAM6L,EAAcjM,EAAMoJ,GAAOpK,GAAKyC,MAAM1B,kBACtCmM,EAAelM,EAAMoJ,GAAOpK,GAAKyC,MAAMvB,mBAEzC9B,GAAYqD,MAAQnD,GAAoBmD,MACrCzC,GAAAyC,MAAMrB,KAAO+L,KAAKC,IAAIH,EAAYI,KAAKH,EAAc,SAAW,EAEhElN,GAAAyC,MAAMrB,KAAO+L,KAAKC,IAAIH,EAAYI,KAAKH,EAAc,SAE3CvG,IAAA,CACnB,CAGF,SAAS2G,GAAqBrB,GAC5B,MAAMsB,EAAoB,IAAIrB,KAAKlM,GAAKyC,MAAM1B,iBAAiB8F,UACxD,OAAAoF,EAAKpF,WAAa0G,CAAA,CAE3B,SAASC,KACHxN,GAAKyC,MAAMrB,KAAO,IAChBhC,GAAYqD,MAAQnD,GAAoBmD,MAC1CzC,GAAKyC,MAAMvB,iBAAmBF,EAAMhB,GAAKyC,MAAM1B,iBAC5CI,IAAInB,GAAKyC,MAAMrB,KAAO,EAAG,OACzBH,OAAO,cAAc5B,GAAUoD,SAElCzC,GAAKyC,MAAMvB,iBAAmBF,EAAMhB,GAAKyC,MAAM1B,iBAAiBI,IAAInB,GAAKyC,MAAMrB,KAAM,OAAOH,OAAO,cAAc5B,GAAUoD,SAE5GkE,KACnB,CAEI,MAAA8G,GAAiBtP,GAAI,GACrBuP,GAAevP,IAkBf,MAAAwP,GAAYxP,EAAoB,IAEhCyP,GAAejO,EAAS,CAC5BkO,OAAQ,EACRC,SAAU,KAGNC,GAAc5P,IAEd6P,GAAkB7P,IAEpB,IAAA8P,GA0BJ,SAASC,GAAatJ,GACf5E,GAAAyC,MAAMpE,QAAUuG,EAAK4H,KACrBxM,GAAAyC,MAAMpB,MAAQuD,EAAKvD,KAAA,4jQA1BjB,SAAYmL,EAAc2B,EAAgBC,GACjD,IAAKD,EACI,OAAAC,EAAG,IAEZL,GAAYtL,MAAQ0L,EACpBE,aAAaJ,IAETF,GAAYtL,QAAUuL,GAAgBvL,OACxCkL,GAAUlL,MAAQ,GAClBwL,GAAUxC,YAAW3I,UACnB,MAAQ7D,KAAAA,SAAe0M,EAAU2C,gBAAgB,IAC5C5O,MACAkO,GACHpB,CAACA,GAAOuB,GAAYtL,QAEtBuL,GAAgBvL,MAAQsL,GAAYtL,MAC1BkL,GAAAlL,MAAQxD,EAAKwJ,MAAQ,GAC/B2F,EAAGT,GAAUlL,MAAK,GACjB,KAAO0K,KAAKoB,WAEfH,EAAGT,GAAUlL,MACf,+iGAtdO,SAAY+L,EAAUC,SACvB,MAAAC,EAAI1O,GAAKyC,MAAMT,SAAS2M,WAAW/J,GAASA,EAAK6C,SAAW+G,EAAI/G,UACxD,IAAViH,IACF,OAAA/F,EAAA3I,GAAKyC,MAAMT,SAAS0M,GAAG7G,YAAvBc,EAAkCiG,OAAOH,EAAO,IAEtCjG,IAAA,iiBAhBd,IAAsB/F,YACV2E,WACR3E,EAAM4E,UAAY5E,EAAM4E,UAAUH,KAAKtC,IAC9B,IACFA,EACHwC,SAAU3E,EAAM2E,8JAqUL3E,QACF0J,GAAA1J,MAAMgF,OAAShF,EAAMgF,OACrB0E,GAAA1J,MAAM2J,iBAAmB3J,EAAM4E,eAC9C8E,GAAe1J,MAAM2F,SAAU,GAHjC,IAAmB3F,ygBAqGFA,QACfiL,GAAajL,MAAQA,EAAMgF,YAC3BgG,GAAehL,OAAQ,GAFzB,IAAiBA,+dA5GjB,IAAuBA,YACXoF,WAAapF,EAAMoF,UAAUe,OAASnG,EAAM6F,UAC9C7F,EAAA6F,QAAU7F,EAAMoF,UAAUe,OACtBiB,EAAA4C,QAAQzO,GAAE,qBAAsB,CAAE6Q,iBAAkBpM,EAAMoF,UAAUe,qgBAnSjE4F,QACGrG,GAAA1F,MAAM4F,KAAO,QACI,KAA/BrI,GAAKyC,MAAM1B,iBAA0D,KAAhCf,GAAKyC,MAAMvB,kBAA4BF,EAAMhB,GAAKyC,MAAMvB,kBAAkB4N,QAAQ9N,EAAMhB,GAAKyC,MAAM1B,kBAIxIyN,EAAIlG,SAAW,EACPuB,EAAA4C,QAAQzO,GAAE,0BAGJmK,GAAA1F,MAAMgF,OAAS+G,EAAI/G,OACnBU,GAAA1F,MAAMiF,OAAS8G,EAAI9G,OACnBS,GAAA1F,MAAMmF,QAAUhK,GAAMgK,QACtBO,GAAA1F,MAAM6F,QAAUkG,EAAIlG,QACtCH,GAAkB1F,MAAM1B,gBAAkB,GAAGqJ,GAAOpK,GAAKyC,MAAM1B,oBAAoBsJ,GAAUrK,GAAKyC,MAAM1B,iBAAiBuJ,UAAU,GAAI,MACvInC,GAAkB1F,MAAMvB,iBAAmB,GAAGkJ,GAAOpK,GAAKyC,MAAMvB,qBAAqBmJ,GAAUrK,GAAKyC,MAAMvB,kBAAkBoJ,UAAU,GAAI,MACtIkE,EAAI3G,WACF2G,EAAA3G,UAAUa,SAASqG,IACrB5G,GAAkB1F,MAAM4F,KAAKQ,KAAKkG,EAAGhH,IAAG,IAG5ChI,GAAM0C,MAAQ+L,EAAI3G,UAClBM,GAAkB1F,MAAM2F,SAAU,GAnBtByB,EAAA4C,QAAQzO,GAAE,gCAHxB,IAAiBwQ"}