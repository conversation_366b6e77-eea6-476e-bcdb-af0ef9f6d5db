{"version": 3, "file": "channel.api-CM6FWEgD.js", "sources": ["../../src/api/modules/pms/channel/channel.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/channel'\r\n/**\r\n * 渠道管理\r\n */\r\nexport default {\r\n  /**\r\n   * 渠道分页\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getChannelPage: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    isEnable?: string\r\n    channelType?: string\r\n    isG: string\r\n    channelName?: string\r\n    pageNo: number\r\n    pageSize: number\r\n  }) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获取渠道列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getChannelList: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    isEnable?: string\r\n    isG?: string\r\n    channelName?: string\r\n    channelType?: string\r\n  }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获得渠道列表(只包含渠道代码和名称)\r\n   * @param data\r\n   */\r\n  getChannelSimpleList: (data: any) => api.get(`${BASE_PATH}/simple-list`, { params: data }),\r\n  /**\r\n   * 获得渠道\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getChannel: (id: number) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: { id },\r\n    }),\r\n\r\n  /**\r\n   * 修改渠道状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateChannelStatus: (data: { id: number, isEnable: string }) =>\r\n    api.put(`${BASE_PATH}/update-status`, data),\r\n\r\n  /**\r\n   * 创建渠道\r\n   */\r\n  createChannel: (data: any) =>\r\n    api.post(`${BASE_PATH}/create`, data),\r\n\r\n  /**\r\n   * 修改渠道\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateChannel: (data: any) =>\r\n    api.put(`${BASE_PATH}/update`, data),\r\n}\r\n"], "names": ["BASE_PATH", "channelApi", "getChannelPage", "data", "api", "get", "params", "getChannelList", "getChannelSimpleList", "getChannel", "id", "updateChannelStatus", "put", "createChannel", "post", "updateChannel"], "mappings": "mCAEA,MAAMA,EAAY,wBAIHC,EAAA,CAMbC,eAAiBC,GAUfC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAQZI,eAAiBJ,GAQfC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAOZK,qBAAuBL,GAAcC,EAAIC,IAAI,GAAGL,gBAAyB,CAAEM,OAAQH,IAMnFM,WAAaC,GACXN,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CAAEI,QAQdC,oBAAsBR,GACpBC,EAAIQ,IAAI,GAAGZ,kBAA2BG,GAKxCU,cAAgBV,GACdC,EAAIU,KAAK,GAAGd,WAAoBG,GAOlCY,cAAgBZ,GACdC,EAAIQ,IAAI,GAAGZ,WAAoBG"}