{"version": 3, "file": "roomInTeam-BD8kwZeY.js", "sources": ["../../src/views/room/realtime/components/roomInTeam.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { bookApi, orderApi } from '@/api/modules/index'\r\nimport { OrderState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rCode: string\r\n    rNo: string\r\n    rtName: string\r\n    orderNo: number | string\r\n  }>(),\r\n  {\r\n    rCode: '',\r\n    rNo: '',\r\n    rtName: '',\r\n    orderNo: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n}>()\r\n\r\nconst userStore = useUserStore()\r\n\r\nconst formRef = ref<FormInstance>()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  rCode: props.rCode,\r\n  rNo: props.rNo,\r\n  vipPrice: 0,\r\n  orderNo: props.orderNo,\r\n  /** 团队代码 */\r\n  guestCode: '',\r\n})\r\nconst teams = ref<{ guestCode: string; guestName: string }[]>([])\r\nconst formRules = ref<FormRules>({\r\n  price: [{ required: true, message: '请输入房价', trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  getOrderPrices()\r\n  getTeamInList()\r\n})\r\n\r\nfunction getOrderPrices() {\r\n  const { gcode, hcode, orderNo } = form.value\r\n  orderApi.getOrderPrices({ gcode, hcode, orderNo }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      // TODO 这里需要根据当前时间获取当天的价格\r\n      // 这里只是演示，所以就取了第一个\r\n      form.value.vipPrice = res.data[0].vipPrice\r\n    }\r\n  })\r\n}\r\n\r\nfunction getTeamInList() {\r\n  bookApi\r\n    .getTeamInList({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      state: OrderState.CHECK_IN,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        teams.value = res.data\r\n      }\r\n    })\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        orderApi.edit(form.value).then((res: any) => {\r\n          if (res.code !== 0) {\r\n            ElMessage.error(res.msg)\r\n            return\r\n          }\r\n          ElMessage.success({\r\n            message: `房间「${props.rNo}」锁房转入团队成功`,\r\n            type: 'success',\r\n            center: true,\r\n          })\r\n          emits('success', {\r\n            rCode: props.rCode,\r\n            rNo: props.rNo,\r\n            guestCode: form.value.guestCode,\r\n          })\r\n          onCancel()\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"`加入团队：${props.rNo}`\" width=\"400px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"100px\" label-suffix=\"：\">\r\n      <el-form-item label=\"当前房型\">\r\n        {{ props.rtName }}\r\n      </el-form-item>\r\n      <el-form-item label=\"设置价格\">\r\n        <el-input v-model=\"form.vipPrice\" placeholder=\"请输入价格\" style=\"width: 150px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"加入团队\">\r\n        <el-select v-model=\"form.guestCode\" placeholder=\"请选择团队\">\r\n          <el-option v-for=\"team in teams\" :key=\"team.guestCode\" :label=\"team.guestName\" :value=\"team.guestCode\" />\r\n        </el-select>\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\"> 取消 </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\"> 确定 </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "form", "gcode", "hcode", "rCode", "rNo", "vipPrice", "orderNo", "guest<PERSON><PERSON>", "teams", "formRules", "price", "required", "message", "trigger", "onSubmit", "value", "validate", "valid", "orderApi", "edit", "then", "res", "code", "ElMessage", "success", "type", "center", "onCancel", "error", "msg", "onMounted", "getOrderPrices", "data", "bookApi", "getTeamInList", "state", "OrderState", "CHECK_IN"], "mappings": "i5BAMA,MAAMA,EAAQC,EAgBRC,EAAQC,EAKRC,EAAYC,IAEZC,EAAUC,IACVC,EAAYC,EAAS,CACzBC,IAAM,IACGV,EAAMW,WAEf,GAAAC,CAAIC,GACFX,EAAM,oBAAqBW,EAAG,IAG5BC,EAAOP,EAAI,CACfQ,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBC,MAAOjB,EAAMiB,MACbC,IAAKlB,EAAMkB,IACXC,SAAU,EACVC,QAASpB,EAAMoB,QAEfC,UAAW,KAEPC,EAAQf,EAAgD,IACxDgB,EAAYhB,EAAe,CAC/BiB,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAAS,QAASC,QAAS,WAiCvD,SAASC,IACPtB,EAAQuB,OACNvB,EAAQuB,MAAMC,UAAUC,IAClBA,GACFC,EAASC,KAAKnB,EAAKe,OAAOK,MAAMC,IACb,IAAbA,EAAIC,MAIRC,EAAUC,QAAQ,CAChBZ,QAAS,MAAM1B,EAAMkB,eACrBqB,KAAM,UACNC,QAAQ,IAEVtC,EAAM,UAAW,CACfe,MAAOjB,EAAMiB,MACbC,IAAKlB,EAAMkB,IACXG,UAAWP,EAAKe,MAAMR,YAEfoB,KAbGJ,EAAAK,MAAMP,EAAIQ,IAab,GACV,GAEJ,CAGL,SAASF,IACPjC,EAAUqB,OAAQ,CAAA,QAxDpBe,GAAU,MAKV,WACE,MAAM7B,MAAEA,EAAAC,MAAOA,EAAOI,QAAAA,GAAYN,EAAKe,MAC9BG,EAAAa,eAAe,CAAE9B,QAAOC,QAAOI,YAAWc,MAAMC,IACtC,IAAbA,EAAIC,OAGNtB,EAAKe,MAAMV,SAAWgB,EAAIW,KAAK,GAAG3B,SAAA,GAErC,CAZc0B,GAgBfE,EACGC,cAAc,CACbjC,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBiC,MAAOC,EAAWC,WAEnBjB,MAAMC,IACY,IAAbA,EAAIC,OACNd,EAAMO,MAAQM,EAAIW,KAAA,GAvBV"}