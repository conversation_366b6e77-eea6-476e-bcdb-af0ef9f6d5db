{"version": 3, "file": "dailyTasks-FG9XcnPB.js", "sources": ["../../src/views/housekeeping/housekeepingMgmt/components/dailyTasks.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"batchCancel\": \"Batch Cancel\",\r\n      \"batchMatchCleaner\": \"Batch Match Cleaner\",\r\n      \"confirmBatchCancel\": \"Are you sure to batch cancel?\",\r\n      \"buildingFloor\": \"Building Floor\",\r\n      \"selectBuildingFloor\": \"Please select building floor\",\r\n      \"issuanceStatus\": \"Issuance Status\",\r\n      \"all\": \"All\",\r\n      \"notIssued\": \"Not Issued\",\r\n      \"issued\": \"Issued\",\r\n      \"roomNumber\": \"Room Number\",\r\n      \"roomType\": \"Room Type\",\r\n      \"roomStatus\": \"Room Status\",\r\n      \"cleaningType\": \"Cleaning Type\",\r\n      \"cleaner\": \"Cleaner\",\r\n      \"cleaningStatus\": \"Cleaning Status\",\r\n      \"operation\": \"Actions\",\r\n      \"cancelCleaning\": \"Cancel Cleaning\",\r\n      \"modifyCleaner\": \"Modify Cleaner\",\r\n      \"cancelSuccess\": \"Cancel successful\",\r\n      \"modifySuccess\": \"Modify successful\",\r\n      \"selectRoomsToCancel\": \"Please select rooms to cancel\",\r\n      \"selectRoomsToMatch\": \"Please select rooms to match cleaner\",\r\n      \"batchMatchCleanerTitle\": \"Batch Match Cleaner\",\r\n      \"modifyCleanerTitle\": \"Modify Cleaner\",\r\n      \"batchMatchCleanerLabel\": \"Batch match selected rooms to cleaner\",\r\n      \"batchMatchCleanerRequired\": \"Batch match cleaner cannot be empty!\",\r\n      \"searchSuccess\": \"Search successful\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"batchCancel\": \"批量取消\",\r\n      \"batchMatchCleaner\": \"批量匹配保洁\",\r\n      \"confirmBatchCancel\": \"您是否批量取消？\",\r\n      \"buildingFloor\": \"楼栋楼层\",\r\n      \"selectBuildingFloor\": \"请选择楼栋楼层\",\r\n      \"issuanceStatus\": \"下发状态\",\r\n      \"all\": \"全部\",\r\n      \"notIssued\": \"未下发\",\r\n      \"issued\": \"已下发\",\r\n      \"roomNumber\": \"房号\",\r\n      \"roomType\": \"房型\",\r\n      \"roomStatus\": \"房态\",\r\n      \"cleaningType\": \"清扫类型\",\r\n      \"cleaner\": \"保洁员\",\r\n      \"cleaningStatus\": \"打扫状态\",\r\n      \"operation\": \"操作\",\r\n      \"cancelCleaning\": \"取消打扫\",\r\n      \"modifyCleaner\": \"修改保洁员\",\r\n      \"cancelSuccess\": \"取消成功\",\r\n      \"modifySuccess\": \"修改成功！\",\r\n      \"selectRoomsToCancel\": \"请选择需要取消的房型\",\r\n      \"selectRoomsToMatch\": \"请选择需要匹配保洁的房型\",\r\n      \"batchMatchCleanerTitle\": \"批量匹配保洁\",\r\n      \"modifyCleanerTitle\": \"修改保洁员\",\r\n      \"batchMatchCleanerLabel\": \"将所选房间批量匹配保洁\",\r\n      \"batchMatchCleanerRequired\": \"将所选房间批量匹配保洁不能为空！\",\r\n      \"searchSuccess\": \"搜索成功\"\r\n    },\r\n    \"km\": {\r\n      \"batchCancel\": \"បោះបង់ជាក្រុម\",\r\n      \"batchMatchCleaner\": \"ផ្គូផ្គងអ្នកសម្អាតជាក្រុម\",\r\n      \"confirmBatchCancel\": \"តើអ្នកពិតជាចង់បោះបង់ជាក្រុមទេ?\",\r\n      \"buildingFloor\": \"អាគារនិងជាន់\",\r\n      \"selectBuildingFloor\": \"សូមជ្រើសរើសអាគារនិងជាន់\",\r\n      \"issuanceStatus\": \"ស្ថានភាពចេញផ្សាយ\",\r\n      \"all\": \"ទាំងអស់\",\r\n      \"notIssued\": \"មិនទាន់ចេញផ្សាយ\",\r\n      \"issued\": \"បានចេញផ្សាយ\",\r\n      \"roomNumber\": \"លេខបន្ទប់\",\r\n      \"roomType\": \"ប្រភេទបន្ទប់\",\r\n      \"roomStatus\": \"ស្ថានភាពបន្ទប់\",\r\n      \"cleaningType\": \"ប្រភេទសម្អាត\",\r\n      \"cleaner\": \"អ្នកសម្អាត\",\r\n      \"cleaningStatus\": \"ស្ថានភាពសម្អាត\",\r\n      \"operation\": \"ប្រតិបត្តិការ\",\r\n      \"cancelCleaning\": \"បោះបង់ការសម្អាត\",\r\n      \"modifyCleaner\": \"កែសម្រួលអ្នកសម្អាត\",\r\n      \"cancelSuccess\": \"បោះបង់ជោគជ័យ\",\r\n      \"modifySuccess\": \"កែសម្រួលជោគជ័យ!\",\r\n      \"selectRoomsToCancel\": \"សូមជ្រើសរើសបន្ទប់ដែលត្រូវបោះបង់\",\r\n      \"selectRoomsToMatch\": \"សូមជ្រើសរើសបន្ទប់ដែលត្រូវផ្គូផ្គងអ្នកសម្អាត\",\r\n      \"batchMatchCleanerTitle\": \"ផ្គូផ្គងអ្នកសម្អាតជាក្រុម\",\r\n      \"modifyCleanerTitle\": \"កែសម្រួលអ្នកសម្អាត\",\r\n      \"batchMatchCleanerLabel\": \"ផ្គូផ្គងបន្ទប់ដែលបានជ្រើសរើសទៅអ្នកសម្អាតជាក្រុម\",\r\n      \"batchMatchCleanerRequired\": \"ការផ្គូផ្គងអ្នកសម្អាតជាក្រុមមិនអាចទទេបានទេ!\",\r\n      \"searchSuccess\": \"ស្វែងរកជោគជ័យ\"\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script lang=\"ts\" setup>\r\nimport type { batchUpdateCleanerType, roomPageType } from '@/api/modules/pms/task/taskQueryType'\r\nimport type { DictDataSimpleRespVO, dictTypeListType, RoomTaskRespVO, TreeString, UserSimpleRespVO } from './tasksList'\r\nimport { buildingFloorApi, dictDataApi, userApi } from '@/api/modules'\r\nimport taskApi from '@/api/modules/pms/task/task.api'\r\nimport { DICT_TYPE_ROOM_STATUS } from '@/models/dict/constants'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { isArray } from '@/utils/is'\r\nimport { ElButton, ElPopconfirm, ElTag, ElText } from 'element-plus'\r\n\r\ndefineOptions({\r\n  name: 'HousekeepingMgmtDailyTasks', // 每日任务下发\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n/** 列表查询条件 */\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n/** 获得房间匹配搜索条件 */\r\nconst queryOptionsParms = reactive<roomPageType>({\r\n  ...queryParams,\r\n  pageNo: 1,\r\n  pageSize: 10,\r\n})\r\n/** 弹窗属性 */\r\nconst dialog = reactive<{\r\n  /** 标题 */\r\n  title?: string\r\n  /** 值 */\r\n  value?: string\r\n}>({\r\n  title: '',\r\n  value: '',\r\n})\r\n/** 类型列表 */\r\nconst dictTypeList = reactive<dictTypeListType>({\r\n  roomStatus: [],\r\n})\r\n/** 保洁员列表 */\r\nconst cleanerList = reactive<UserSimpleRespVO[]>([])\r\n/** 楼栋楼层 */\r\nconst buildingFloors = reactive<TreeString[]>([])\r\n/** 弹窗ref */\r\nconst easyDialogRef = ref()\r\n/** form ref */\r\nconst easyFormRef = ref()\r\n/** 弹窗内容(初始化) */\r\nconst _modelForm = reactive<batchUpdateCleanerType>({\r\n  ...queryParams,\r\n  taskCodes: [],\r\n  username: '',\r\n})\r\n/** 弹窗内容 */\r\nconst modelForm = ref<batchUpdateCleanerType>({ ..._modelForm })\r\n/** form表单 */\r\nconst ruleFieldList = reactive<Form.FieldItem[]>([\r\n  {\r\n    label: t('batchMatchCleanerLabel'),\r\n    field: 'username',\r\n    type: 'select',\r\n    rules: [{ required: true, message: t('batchMatchCleanerRequired') }],\r\n    options: {\r\n      data: cleanerList,\r\n      valueKey: 'username',\r\n      labelKey: 'nickname',\r\n    },\r\n  },\r\n])\r\n/** 多选可修改保洁员列表 */\r\nconst multipleSelection = ref<RoomTaskRespVO[]>([])\r\n/** 单个可修改保洁员 */\r\nconst singleSelection = ref<RoomTaskRespVO>({})\r\n/** 气泡显示隐藏 */\r\nconst isVisible = ref(false)\r\n\r\n/** 表格上面按钮配置 */\r\nconst options = reactive<Table.Options>({\r\n  checkSelecKey: ['isUpdateUserName'], // 列表全选字段\r\n  checkSelecValue: ['0'],\r\n  showSearch: true,\r\n  border: false,\r\n  paginationConfig: {\r\n    total: Number(Number.NaN),\r\n  },\r\n})\r\n/** 加载状态 */\r\nconst loading = ref(false)\r\n/** 后台获取到的数据对象 */\r\nconst tableData = ref<RoomTaskRespVO[]>([])\r\n/** 表格配置 */\r\nconst tableColumn = computed<Table.Column<RoomTaskRespVO>[]>(() => [\r\n  { type: 'selection', width: '50', fixed: 'left' },\r\n  {\r\n    prop: 'roomCode',\r\n    label: t('buildingFloor'),\r\n    isHideColumn: true,\r\n    search: true,\r\n    searchFiledType: 'Cascader',\r\n    searchFieldOptions: {\r\n      placeholder: t('selectBuildingFloor'),\r\n      data: buildingFloors,\r\n      valueKey: 'code',\r\n      labelKey: 'name',\r\n      cascaderObj: {\r\n        checkStrictly: true,\r\n      },\r\n      showAllLevels: true,\r\n    },\r\n  },\r\n  {\r\n    prop: 'issuanceState',\r\n    label: t('issuanceStatus'),\r\n    search: true,\r\n    isHideColumn: true,\r\n    searchFiledType: 'radio',\r\n    searchFieldOptions: {\r\n      data: [\r\n        { label: t('all'), value: '' },\r\n        { label: t('notIssued'), value: '0' },\r\n        { label: t('issued'), value: '1' },\r\n      ],\r\n      value: '',\r\n      onChange: (val: string) => {\r\n        queryOptionsParms.issuanceState = val\r\n        getList()\r\n      },\r\n      radioValue: 'radioButton',\r\n    },\r\n    style: { width: 'auto' },\r\n  },\r\n  { prop: 'rNo', label: t('roomNumber'), search: true },\r\n  { prop: 'rtName', label: t('roomType') },\r\n  {\r\n    prop: 'roomState',\r\n    label: t('roomStatus'),\r\n    search: true,\r\n    searchFiledType: 'Select',\r\n    searchFieldOptions: {\r\n      data: dictTypeList.roomStatus,\r\n      valueKey: 'code',\r\n      labelKey: 'label',\r\n    },\r\n    render: ({ row }) => row.roomState ?? '--',\r\n  },\r\n  { prop: 'roomCleanTypeName', label: t('cleaningType') },\r\n  {\r\n    prop: 'username',\r\n    label: t('cleaner'),\r\n    search: true,\r\n    searchFiledType: 'Select',\r\n    searchFieldOptions: {\r\n      data: cleanerList,\r\n      valueKey: 'username',\r\n      labelKey: 'nickname',\r\n    },\r\n    render: ({ row }) => row.nickname ?? '--',\r\n  },\r\n  {\r\n    prop: 'cleanState',\r\n    label: t('cleaningStatus'),\r\n    minWidth: 120,\r\n    render: ({ row }) => [h(ElText, { type: stateType(row.cleanState) }, { default: () => row.cleanStateName ?? '--' }), row.tag && h(ElTag, { type: 'info', class: 'ml-10px' }, { default: () => row.tag })],\r\n  },\r\n  {\r\n    prop: 'issuanceState',\r\n    label: t('issuanceStatus'),\r\n    render: ({ row }) => h(ElTag, { type: row.issuanceState == 1 ? 'success' : 'primary' }, { default: () => (row.issuanceState == 1 ? t('issued') : t('notIssued')) }),\r\n  },\r\n  {\r\n    label: t('operation'),\r\n    width: '220',\r\n    render: ({ row }) => [\r\n      h(\r\n        ElPopconfirm,\r\n        {\r\n          title: t('confirmBatchCancel'),\r\n          width: '180',\r\n          hideIcon: true,\r\n          visible: row.isVisible,\r\n          onCancel: () => handleAction('cancel', row),\r\n          onConfirm: () => handleAction('confirm', row),\r\n        },\r\n        {\r\n          reference: () =>\r\n            h(\r\n              ElButton,\r\n              {\r\n                link: true,\r\n                type: 'primary',\r\n                disabled: row.isUpdateUserName == 0,\r\n                onClick: () => handleAction('cancel', row),\r\n              },\r\n              { default: () => t('cancelCleaning') }\r\n            ),\r\n        }\r\n      ),\r\n      h(ElButton, { link: true, type: 'primary', disabled: row.isUpdateUserName == 0, onClick: () => handleAction('edit', row) }, { default: () => t('modifyCleaner') }),\r\n    ],\r\n  },\r\n])\r\n/**\r\n * 打扫状态\r\n * @param value  pending_clean:待打扫，in_progress:打扫中，pending_inspection:待检查，completed:已干净，cancel:已取消\r\n */\r\nfunction stateType(value: string) {\r\n  // \"default\" | \"success \" | \"warning\" | \"info\" | \"primary\" | \"danger\"\r\n  let txt: Table.ValidTypes\r\n  switch (value) {\r\n    case 'completed':\r\n      txt = 'success'\r\n      break\r\n    case 'cancel':\r\n      txt = 'info'\r\n      break\r\n    case 'in_progress':\r\n      txt = 'danger'\r\n      break\r\n    case 'pending_inspection':\r\n      txt = 'warning'\r\n      break\r\n    default:\r\n      txt = 'primary'\r\n      break\r\n  }\r\n  return txt\r\n}\r\n/** 这里的类型是表格配置文件里定义的类型 */\r\nasync function handleAction(command: Table.Command, row: RoomTaskRespVO) {\r\n  switch (command) {\r\n    // 取消打扫弹窗显示\r\n    case 'cancel':\r\n      tableData.value.forEach((item) => {\r\n        if (item.rNo == row.rNo) {\r\n          item.isVisible = !item.isVisible\r\n        } else {\r\n          item.isVisible = false\r\n        }\r\n      })\r\n      break\r\n    // 取消打扫确认取消\r\n    case 'confirm':\r\n      singleSelection.value = row\r\n      dialog.value = 'single'\r\n      handleConfirm()\r\n      break\r\n    // 修改保洁员\r\n    case 'edit':\r\n      singleSelection.value = row\r\n      dialog.title = t('modifyCleanerTitle')\r\n      dialog.value = 'single'\r\n      easyDialogRef.value.show()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n/** 批量取消 */\r\nasync function handleConfirm() {\r\n  let taskCodes\r\n  if (dialog.value == 'multiple') {\r\n    taskCodes = multipleSelection.value.map((item: RoomTaskRespVO) => item.taskCode!)\r\n  }\r\n  if (dialog.value == 'single') {\r\n    taskCodes = [singleSelection.value.taskCode]\r\n  }\r\n  await taskApi.cancelTask({ ...queryParams, taskCodes })\r\n  ElMessage.success(t('cancelSuccess'))\r\n  getList()\r\n}\r\n/** 获取任务列表 */\r\nasync function getList() {\r\n  loading.value = true\r\n  const { data } = await taskApi.getRoomPageList(queryOptionsParms)\r\n  loading.value = false\r\n  if (data) {\r\n    data.pageSize = queryOptionsParms.pageNo\r\n    data.pageLimit = queryOptionsParms.pageSize\r\n    tableData.value =\r\n      data.list.map((item: RoomTaskRespVO) => {\r\n        item.isVisible = false\r\n        return item\r\n      }) || []\r\n    options.paginationConfig = data\r\n    multipleSelection.value = []\r\n    isVisible.value = false\r\n  }\r\n}\r\n/** 获取保洁员 */\r\nasync function getCleaner() {\r\n  cleanerList.length = 0\r\n  const { data } = await userApi.getCleanerList(queryParams)\r\n  data.forEach((item: UserSimpleRespVO) => {\r\n    cleanerList.push(item)\r\n  })\r\n} /** 获取字典类型 */\r\nasync function getRoomCleanType() {\r\n  const { data } = await dictDataApi.getDictDataBatch([DICT_TYPE_ROOM_STATUS])\r\n  data.forEach((item: DictDataSimpleRespVO) => {\r\n    if (item.dictType == DICT_TYPE_ROOM_STATUS) {\r\n      dictTypeList.roomStatus?.push(item)\r\n    }\r\n  })\r\n}\r\n/** 获取楼栋楼层 */\r\nasync function getBuildingFloors() {\r\n  buildingFloors.length = 0\r\n  const { data } = await buildingFloorApi.getBuildFloorTree(queryParams)\r\n  data.forEach((item: TreeString) => {\r\n    buildingFloors.push(item)\r\n  })\r\n}\r\n/** 批量取消 */\r\nfunction handleCancel() {\r\n  if (multipleSelection.value.length == 0) {\r\n    return ElMessage.error(t('selectRoomsToCancel'))\r\n  }\r\n  dialog.value = 'multiple'\r\n  isVisible.value = true\r\n}\r\n/** 批量匹配保洁 */\r\nfunction handleClick() {\r\n  if (multipleSelection.value.length == 0) {\r\n    return ElMessage.error(t('selectRoomsToMatch'))\r\n  }\r\n  easyDialogRef.value.show()\r\n  dialog.title = t('batchMatchCleanerTitle')\r\n  dialog.value = 'multiple'\r\n}\r\n/** 提交form */\r\nfunction formSubmit() {\r\n  if (!easyFormRef.value.formRef) {\r\n    return\r\n  }\r\n  easyFormRef.value.formRef.validate(async (valid: boolean) => {\r\n    if (valid) {\r\n      easyDialogRef.value.loading = true\r\n      const params = { ...modelForm.value }\r\n      let taskCodes: any\r\n      if (dialog.value == 'multiple') {\r\n        taskCodes = multipleSelection.value.map((item: RoomTaskRespVO) => item.taskCode!)\r\n      }\r\n      if (dialog.value == 'single') {\r\n        taskCodes = [singleSelection.value.taskCode]\r\n      }\r\n      params.taskCodes = taskCodes\r\n      const { data } = await taskApi.batchUpdateCleaner(params)\r\n      easyDialogRef.value.loading = false\r\n      if (data) {\r\n        ElMessage.success(t('modifySuccess'))\r\n        formClose()\r\n        getList()\r\n      }\r\n    }\r\n  })\r\n}\r\n/** 取消弹窗 */\r\nfunction formClose() {\r\n  // 清空校验\r\n  if (easyFormRef.value) {\r\n    easyFormRef.value.formRef.resetFields()\r\n  }\r\n  // 赋值给弹窗的值\r\n  for (const key in modelForm.value) {\r\n    modelForm.value[key] = _modelForm[key]\r\n  }\r\n  dialog.title = ''\r\n  dialog.value = ''\r\n  easyDialogRef.value.loading = false\r\n  easyDialogRef.value.visible = false\r\n}\r\n/** 列表复选框 table组件选中 */\r\nfunction handleSelection(val: RoomTaskRespVO[]) {\r\n  multipleSelection.value = val\r\n}\r\n/** limit或者currentPage改变触发 */\r\nfunction handlePaginationChange(page: number, limit: number) {\r\n  queryOptionsParms.pageNo = page\r\n  queryOptionsParms.pageSize = limit\r\n  getList()\r\n}\r\n/** tabs 搜索 */\r\nasync function handleSearch(value: Record<string, any>) {\r\n  // 楼栋楼层单独判断\r\n  if (isArray(value.roomCode)) {\r\n    if (value.roomCode.length > 1) {\r\n      // 如果选择是楼栋则删除楼层代码\r\n      delete queryOptionsParms.buildingCode\r\n      queryOptionsParms.floorCode = value.roomCode[1]\r\n    } else {\r\n      // 如果选择是楼层则删除楼栋代码\r\n      delete queryOptionsParms.floorCode\r\n      queryOptionsParms.buildingCode = value.roomCode[0]\r\n    }\r\n  } else {\r\n    delete queryOptionsParms.buildingCode\r\n    delete queryOptionsParms.floorCode\r\n  }\r\n  queryOptionsParms.issuanceState = value.issuanceState\r\n  queryOptionsParms.username = value.username\r\n  queryOptionsParms.roomState = value.roomState\r\n  queryOptionsParms.rNo = value.rNo\r\n  queryOptionsParms.pageNo = 1\r\n  getList()\r\n  ElMessage.success(t('searchSuccess'))\r\n}\r\nonMounted(() => {\r\n  getRoomCleanType()\r\n  getCleaner()\r\n  getBuildingFloors()\r\n  getList()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <EasyTable v-loading=\"loading\" :columns=\"tableColumn\" :options=\"options\" :table-data=\"tableData\" @selection-change=\"handleSelection\" @search=\"handleSearch\" @pagination-change=\"handlePaginationChange\">\r\n      <template #toolbar>\r\n        <div class=\"mb-20px flex justify-end\">\r\n          <ElPopconfirm :visible=\"isVisible\" :title=\"t('confirmBatchCancel')\" width=\"180\" hide-icon @cancel=\"isVisible = false\" @confirm=\"handleConfirm()\">\r\n            <template #reference>\r\n              <ElButton @click=\"handleCancel()\">\r\n                {{ t('batchCancel') }}\r\n              </ElButton>\r\n            </template>\r\n          </ElPopconfirm>\r\n          <ElButton @click=\"handleClick()\">\r\n            {{ t('batchMatchCleaner') }}\r\n          </ElButton>\r\n        </div>\r\n      </template>\r\n    </EasyTable>\r\n    <EasyDialog\r\n      ref=\"easyDialogRef\"\r\n      :title=\"dialog.title\"\r\n      :options=\"{\r\n        lockScroll: true,\r\n      }\"\r\n      show-cancel-button\r\n      show-confirm-button\r\n      @submit=\"formSubmit()\"\r\n      @close=\"formClose()\"\r\n    >\r\n      <EasyForm\r\n        ref=\"easyFormRef\"\r\n        class=\"invoiced-form\"\r\n        :field-list=\"ruleFieldList\"\r\n        :model=\"modelForm\"\r\n        :options=\"{\r\n          labelSuffix: '：',\r\n          labelWidth: 'auto',\r\n        }\"\r\n      />\r\n    </EasyDialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "queryParams", "reactive", "gcode", "hcode", "queryOptionsParms", "pageNo", "pageSize", "dialog", "title", "value", "dictTypeList", "roomStatus", "cleanerList", "buildingFloors", "easyDialogRef", "ref", "easyFormRef", "_modelForm", "taskCodes", "username", "modelForm", "ruleFieldList", "label", "field", "type", "rules", "required", "message", "options", "data", "valueKey", "labelKey", "multipleSelection", "singleSelection", "isVisible", "checkSelecKey", "checkSelecValue", "showSearch", "border", "paginationConfig", "total", "Number", "NaN", "loading", "tableData", "tableColumn", "computed", "width", "fixed", "prop", "isHideColumn", "search", "searchFiledType", "searchFieldOptions", "placeholder", "cascaderObj", "checkStrictly", "showAllLevels", "onChange", "val", "issuanceState", "getList", "radioValue", "style", "render", "row", "roomState", "nickname", "min<PERSON><PERSON><PERSON>", "h", "ElText", "stateType", "cleanState", "default", "cleanStateName", "tag", "ElTag", "class", "ElPopconfirm", "hideIcon", "visible", "onCancel", "handleAction", "onConfirm", "reference", "ElButton", "link", "disabled", "isUpdateUserName", "onClick", "txt", "async", "command", "for<PERSON>ach", "item", "rNo", "handleConfirm", "show", "map", "taskCode", "taskApi", "cancelTask", "ElMessage", "success", "getRoomPageList", "pageLimit", "list", "formClose", "formRef", "resetFields", "key", "handleSelection", "handlePaginationChange", "page", "limit", "handleSearch", "isArray", "roomCode", "length", "buildingCode", "floorCode", "onMounted", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "DICT_TYPE_ROOM_STATUS", "dictType", "_a", "push", "getRoomCleanType", "userApi", "getCleanerList", "get<PERSON><PERSON><PERSON>", "buildingFloorApi", "getBuildFloorTree", "getBuildingFloors", "error", "validate", "valid", "params", "batchUpdateCleaner"], "mappings": "qqDAyGM,MAAAA,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,EAAcC,EAAiB,CACnCC,MAAOJ,EAAUI,MACjBC,MAAOL,EAAUK,QAGbC,EAAoBH,EAAuB,IAC5CD,EACHK,OAAQ,EACRC,SAAU,KAGNC,EAASN,EAKZ,CACDO,MAAO,GACPC,MAAO,KAGHC,EAAeT,EAA2B,CAC9CU,WAAY,KAGRC,EAAcX,EAA6B,IAE3CY,EAAiBZ,EAAuB,IAExCa,EAAgBC,IAEhBC,EAAcD,IAEdE,EAAahB,EAAiC,IAC/CD,EACHkB,UAAW,GACXC,SAAU,KAGNC,EAAYL,EAA4B,IAAKE,IAE7CI,EAAgBpB,EAA2B,CAC/C,CACEqB,MAAO1B,EAAE,0BACT2B,MAAO,WACPC,KAAM,SACNC,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAAS/B,EAAE,+BACrCgC,QAAS,CACPC,KAAMjB,EACNkB,SAAU,WACVC,SAAU,eAKVC,EAAoBjB,EAAsB,IAE1CkB,EAAkBlB,EAAoB,IAEtCmB,EAAYnB,GAAI,GAGhBa,EAAU3B,EAAwB,CACtCkC,cAAe,CAAC,oBAChBC,gBAAiB,CAAC,KAClBC,YAAY,EACZC,QAAQ,EACRC,iBAAkB,CAChBC,MAAOC,OAAOA,OAAOC,QAInBC,EAAU5B,GAAI,GAEd6B,EAAY7B,EAAsB,IAElC8B,EAAcC,GAAyC,IAAM,CACjE,CAAEtB,KAAM,YAAauB,MAAO,KAAMC,MAAO,QACzC,CACEC,KAAM,WACN3B,MAAO1B,EAAE,iBACTsD,cAAc,EACdC,QAAQ,EACRC,gBAAiB,WACjBC,mBAAoB,CAClBC,YAAa1D,EAAE,uBACfiC,KAAMhB,EACNiB,SAAU,OACVC,SAAU,OACVwB,YAAa,CACXC,eAAe,GAEjBC,eAAe,IAGnB,CACER,KAAM,gBACN3B,MAAO1B,EAAE,kBACTuD,QAAQ,EACRD,cAAc,EACdE,gBAAiB,QACjBC,mBAAoB,CAClBxB,KAAM,CACJ,CAAEP,MAAO1B,EAAE,OAAQa,MAAO,IAC1B,CAAEa,MAAO1B,EAAE,aAAca,MAAO,KAChC,CAAEa,MAAO1B,EAAE,UAAWa,MAAO,MAE/BA,MAAO,GACPiD,SAAWC,IACTvD,EAAkBwD,cAAgBD,EAC1BE,IAAA,EAEVC,WAAY,eAEdC,MAAO,CAAEhB,MAAO,SAElB,CAAEE,KAAM,MAAO3B,MAAO1B,EAAE,cAAeuD,QAAQ,GAC/C,CAAEF,KAAM,SAAU3B,MAAO1B,EAAE,aAC3B,CACEqD,KAAM,YACN3B,MAAO1B,EAAE,cACTuD,QAAQ,EACRC,gBAAiB,SACjBC,mBAAoB,CAClBxB,KAAMnB,EAAaC,WACnBmB,SAAU,OACVC,SAAU,SAEZiC,OAAQ,EAAGC,SAAUA,EAAIC,WAAa,MAExC,CAAEjB,KAAM,oBAAqB3B,MAAO1B,EAAE,iBACtC,CACEqD,KAAM,WACN3B,MAAO1B,EAAE,WACTuD,QAAQ,EACRC,gBAAiB,SACjBC,mBAAoB,CAClBxB,KAAMjB,EACNkB,SAAU,WACVC,SAAU,YAEZiC,OAAQ,EAAGC,SAAUA,EAAIE,UAAY,MAEvC,CACElB,KAAM,aACN3B,MAAO1B,EAAE,kBACTwE,SAAU,IACVJ,OAAQ,EAAGC,SAAU,CAACI,EAAEC,EAAQ,CAAE9C,KAAM+C,GAAUN,EAAIO,aAAe,CAAEC,QAAS,IAAMR,EAAIS,gBAAkB,OAAST,EAAIU,KAAON,EAAEO,EAAO,CAAEpD,KAAM,OAAQqD,MAAO,WAAa,CAAEJ,QAAS,IAAMR,EAAIU,QAEpM,CACE1B,KAAM,gBACN3B,MAAO1B,EAAE,kBACToE,OAAQ,EAAGC,SAAUI,EAAEO,EAAO,CAAEpD,KAA2B,GAArByC,EAAIL,cAAqB,UAAY,WAAa,CAAEa,QAAS,IAA4B,GAArBR,EAAIL,cAAqBhE,EAAE,UAAYA,EAAE,gBAErJ,CACE0B,MAAO1B,EAAE,aACTmD,MAAO,MACPiB,OAAQ,EAAGC,SAAU,CACnBI,EACES,EACA,CACEtE,MAAOZ,EAAE,sBACTmD,MAAO,MACPgC,UAAU,EACVC,QAASf,EAAI/B,UACb+C,SAAU,IAAMC,GAAa,SAAUjB,GACvCkB,UAAW,IAAMD,GAAa,UAAWjB,IAE3C,CACEmB,UAAW,IACTf,EACEgB,EACA,CACEC,MAAM,EACN9D,KAAM,UACN+D,SAAkC,GAAxBtB,EAAIuB,iBACdC,QAAS,IAAMP,GAAa,SAAUjB,IAExC,CAAEQ,QAAS,IAAM7E,EAAE,sBAI3ByE,EAAEgB,EAAU,CAAEC,MAAM,EAAM9D,KAAM,UAAW+D,SAAkC,GAAxBtB,EAAIuB,iBAAuBC,QAAS,IAAMP,GAAa,OAAQjB,IAAQ,CAAEQ,QAAS,IAAM7E,EAAE,wBAQrJ,SAAS2E,GAAU9D,GAEb,IAAAiF,EACJ,OAAQjF,GACN,IAAK,YACGiF,EAAA,UACN,MACF,IAAK,SACGA,EAAA,OACN,MACF,IAAK,cACGA,EAAA,SACN,MACF,IAAK,qBACGA,EAAA,UACN,MACF,QACQA,EAAA,UAGH,OAAAA,CAAA,CAGMC,eAAAT,GAAaU,EAAwB3B,GAClD,OAAQ2B,GAEN,IAAK,SACOhD,EAAAnC,MAAMoF,SAASC,IACnBA,EAAKC,KAAO9B,EAAI8B,IACbD,EAAA5D,WAAa4D,EAAK5D,UAEvB4D,EAAK5D,WAAY,CAAA,IAGrB,MAEF,IAAK,UACHD,EAAgBxB,MAAQwD,EACxB1D,EAAOE,MAAQ,SACDuF,KACd,MAEF,IAAK,OACH/D,EAAgBxB,MAAQwD,EACjB1D,EAAAC,MAAQZ,EAAE,sBACjBW,EAAOE,MAAQ,SACfK,EAAcL,MAAMwF,OAIxB,CAGFN,eAAeK,KACT,IAAA9E,EACgB,YAAhBX,EAAOE,QACTS,EAAYc,EAAkBvB,MAAMyF,KAAKJ,GAAyBA,EAAKK,YAErD,UAAhB5F,EAAOE,QACGS,EAAA,CAACe,EAAgBxB,MAAM0F,iBAE/BC,EAAQC,WAAW,IAAKrG,EAAakB,cACjCoF,EAAAC,QAAQ3G,EAAE,kBACZiE,IAAA,CAGV8B,eAAe9B,KACblB,EAAQlC,OAAQ,EAChB,MAAMoB,KAAEA,SAAeuE,EAAQI,gBAAgBpG,GAC/CuC,EAAQlC,OAAQ,EACZoB,IACFA,EAAKvB,SAAWF,EAAkBC,OAClCwB,EAAK4E,UAAYrG,EAAkBE,SACnCsC,EAAUnC,MACRoB,EAAK6E,KAAKR,KAAKJ,IACbA,EAAK5D,WAAY,EACV4D,MACH,GACRlE,EAAQW,iBAAmBV,EAC3BG,EAAkBvB,MAAQ,GAC1ByB,EAAUzB,OAAQ,EACpB,CAuEF,SAASkG,KAEH3F,EAAYP,OACFO,EAAAP,MAAMmG,QAAQC,cAGjB,IAAA,MAAAC,KAAO1F,EAAUX,MAC1BW,EAAUX,MAAMqG,GAAO7F,EAAW6F,GAEpCvG,EAAOC,MAAQ,GACfD,EAAOE,MAAQ,GACfK,EAAcL,MAAMkC,SAAU,EAC9B7B,EAAcL,MAAMuE,SAAU,CAAA,CAGhC,SAAS+B,GAAgBpD,GACvB3B,EAAkBvB,MAAQkD,CAAA,CAGnB,SAAAqD,GAAuBC,EAAcC,GAC5C9G,EAAkBC,OAAS4G,EAC3B7G,EAAkBE,SAAW4G,EACrBrD,IAAA,CAGV8B,eAAewB,GAAa1G,GAEtB2G,EAAQ3G,EAAM4G,UACZ5G,EAAM4G,SAASC,OAAS,UAEnBlH,EAAkBmH,aACPnH,EAAAoH,UAAY/G,EAAM4G,SAAS,YAGtCjH,EAAkBoH,UACPpH,EAAAmH,aAAe9G,EAAM4G,SAAS,YAG3CjH,EAAkBmH,oBAClBnH,EAAkBoH,WAE3BpH,EAAkBwD,cAAgBnD,EAAMmD,cACxCxD,EAAkBe,SAAWV,EAAMU,SACnCf,EAAkB8D,UAAYzD,EAAMyD,UACpC9D,EAAkB2F,IAAMtF,EAAMsF,IAC9B3F,EAAkBC,OAAS,EACnBwD,KACEyC,EAAAC,QAAQ3G,EAAE,iBAAgB,QAEtC6H,GAAU,MA9GV9B,iBACQ,MAAA9D,KAAEA,SAAe6F,EAAYC,iBAAiB,CAACC,IAChD/F,EAAAgE,SAASC,UACRA,EAAK+B,UAAYD,IACN,OAAAE,EAAApH,EAAAC,eAAYoH,KAAKjC,GAAI,GAErC,CAyGgBkC,GAtHnBrC,iBACE/E,EAAY0G,OAAS,EACrB,MAAMzF,KAAEA,SAAeoG,EAAQC,eAAelI,GACzC6B,EAAAgE,SAASC,IACZlF,EAAYmH,KAAKjC,EAAI,GACtB,CAkHUqC,GAvGbxC,iBACE9E,EAAeyG,OAAS,EACxB,MAAMzF,KAAEA,SAAeuG,EAAiBC,kBAAkBrI,GACrD6B,EAAAgE,SAASC,IACZjF,EAAekH,KAAKjC,EAAI,GACzB,CAmGiBwC,GACVzE,IAAA,8YAjGV,WACM,GAAkC,GAAlC7B,EAAkBvB,MAAM6G,OAC1B,OAAOhB,EAAUiC,MAAM3I,EAAE,wBAE3BW,EAAOE,MAAQ,WACfyB,EAAUzB,OAAQ,CAAA,0HAGpB,WACM,GAAkC,GAAlCuB,EAAkBvB,MAAM6G,OAC1B,OAAOhB,EAAUiC,MAAM3I,EAAE,uBAE3BkB,EAAcL,MAAMwF,OACb1F,EAAAC,MAAQZ,EAAE,0BACjBW,EAAOE,MAAQ,UAAA,qRAIVO,EAAYP,MAAMmG,SAGvB5F,EAAYP,MAAMmG,QAAQ4B,UAAS7C,MAAO8C,IACxC,GAAIA,EAAO,CACT3H,EAAcL,MAAMkC,SAAU,EAC9B,MAAM+F,EAAS,IAAKtH,EAAUX,OAC1B,IAAAS,EACgB,YAAhBX,EAAOE,QACTS,EAAYc,EAAkBvB,MAAMyF,KAAKJ,GAAyBA,EAAKK,YAErD,UAAhB5F,EAAOE,QACGS,EAAA,CAACe,EAAgBxB,MAAM0F,WAErCuC,EAAOxH,UAAYA,EACnB,MAAMW,KAAEA,SAAeuE,EAAQuC,mBAAmBD,GAClD5H,EAAcL,MAAMkC,SAAU,EAC1Bd,IACQyE,EAAAC,QAAQ3G,EAAE,kBACV+G,KACF9C,KACV"}