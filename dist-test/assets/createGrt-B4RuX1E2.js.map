{"version": 3, "file": "createGrt-B4RuX1E2.js", "sources": ["../../src/views/group/group-room-type/rt/components/DetailForm/createGrt.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"form\": {\r\n      \"roomTypeName\": \"Room Type\",\r\n      \"enterRoomTypeName\": \"Please enter the room type name\",\r\n      \"groupRoomTypeAbbreviation\": \"Short Name\",\r\n      \"enterGroupRoomTypeAbbreviation\": \"Please enter the group room type abbreviation\",\r\n      \"roomArea\": \"Area\",\r\n      \"roomConfiguration\": \"Bed\",\r\n      \"bedType\": \"Bed Type\",\r\n      \"size\": \"Size\",\r\n      \"quantity\": \"Quantity\",\r\n      \"maxOccupancy\": \"Max Occupancy\",\r\n      \"hasWindow\": \"Has Window\",\r\n      \"pleaseSelect\": \"Please Select\",\r\n      \"hasRestRoom\": \"Has Rest Room\",\r\n      \"yes\": \"Enable\",\r\n      \"no\": \"Disable\",\r\n      \"isVirtualRoom\": \"Is Virtual Room\",\r\n      \"isEnabled\": \"Status\",\r\n      \"remarks\": \"Remark\",\r\n      \"submitSuccess\": \"Submission successful\",\r\n      \"rtNameRequired\": \"Room Type Name is required\",\r\n      \"shortNameRequired\": \"Group Room Type Abbreviation is required\",\r\n      \"peopleNumMessage\": \"Max occupancy must be between 1 and 100\",\r\n      \"areaMessage\": \"Area must be greater than 0\"\r\n    },\r\n    \"hasWindowWith\": \"With Window\",\r\n    \"hasWindowWithout\": \"Without Window\",\r\n    \"hasWindowPartial\": \"Partially with Window\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"form\": {\r\n      \"roomTypeName\": \"房型名称\",\r\n      \"enterRoomTypeName\": \"请输入房型名称\",\r\n      \"groupRoomTypeAbbreviation\": \"房型简称\",\r\n      \"enterGroupRoomTypeAbbreviation\": \"请输入房型简称\",\r\n      \"roomArea\": \"房间面积\",\r\n      \"roomConfiguration\": \"房间配置\",\r\n      \"bedType\": \"床型\",\r\n      \"size\": \"尺寸\",\r\n      \"quantity\": \"数量\",\r\n      \"maxOccupancy\": \"最大入住人数\",\r\n      \"hasWindow\": \"是否有窗\",\r\n      \"pleaseSelect\": \"请选择\",\r\n      \"hasRestRoom\": \"是否有卫生间\",\r\n      \"yes\": \"是\",\r\n      \"no\": \"否\",\r\n      \"isVirtualRoom\": \"是否为虚拟房间\",\r\n      \"isEnabled\": \"是否启用\",\r\n      \"remarks\": \"备注\",\r\n      \"submitSuccess\": \"提交成功\",\r\n      \"rtNameRequired\": \"房型名称为必填项\",\r\n      \"shortNameRequired\": \"房型简称为必填项\",\r\n      \"peopleNumMessage\": \"最大入住人数必须在1到100之间\",\r\n      \"areaMessage\": \"房间面积必须大于0\"\r\n    },\r\n    \"hasWindowWith\": \"有窗\",\r\n    \"hasWindowWithout\": \"无窗\",\r\n    \"hasWindowPartial\": \"部分有窗\"\r\n  },\r\n  \"km\": {\r\n    \"form\": {\r\n      \"roomTypeName\": \"ប្រភេទបន្ទប់\",\r\n      \"enterRoomTypeName\": \"សូមបញ្ចូលឈ្មោះប្រភេទបន្ទប់\",\r\n      \"groupRoomTypeAbbreviation\": \"ឈ្មោះខ្លី\",\r\n      \"enterGroupRoomTypeAbbreviation\": \"សូមបញ្ចូលឈ្មោះខ្លីនៃប្រភេទបន្ទប់ក្រុម\",\r\n      \"roomArea\": \"ផ្ទៃដី\",\r\n      \"roomConfiguration\": \"ការកំណត់រចនាសម្ព័ន្ធ\",\r\n      \"bedType\": \"ប្រភេទគ្រែ\",\r\n      \"size\": \"ទំហំ\",\r\n      \"quantity\": \"ចំនួន\",\r\n      \"maxOccupancy\": \"ចំនួនអ្នកស្នាក់នៅអតិបរមា\",\r\n      \"hasWindow\": \"មានបង្អួច\",\r\n      \"pleaseSelect\": \"សូមជ្រើសរើស\",\r\n      \"hasRestRoom\": \"មានបន្ទប់ទឹក\",\r\n      \"yes\": \"បើក\",\r\n      \"no\": \"បិទ\",\r\n      \"isVirtualRoom\": \"ជាបន្ទប់និម្មិត\",\r\n      \"isEnabled\": \"ស្ថានភាព\",\r\n      \"remarks\": \"ចំណាំ\",\r\n      \"submitSuccess\": \"បានដាក់ស្នើដោយជោគជ័យ\",\r\n      \"rtNameRequired\": \"ឈ្មោះប្រភេទបន្ទប់គឺត្រូវការ\",\r\n      \"shortNameRequired\": \"ឈ្មោះខ្លីនៃប្រភេទបន្ទប់ក្រុមគឺត្រូវការ\",\r\n      \"peopleNumMessage\": \"ចំនួនអ្នកស្នាក់នៅអតិបរមាត្រូវតែចាប់ពី 1 ដល់ 100\",\r\n      \"areaMessage\": \"ផ្ទៃដីត្រូវតែធំជាង 0\"\r\n    },\r\n    \"hasWindowWith\": \"មានបង្អួច\",\r\n    \"hasWindowWithout\": \"គ្មានបង្អួច\",\r\n    \"hasWindowPartial\": \"មានបង្អួចខ្លះ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script lang=\"ts\" setup>\r\nimport type { DictDataModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { dictDataApi, rtApi } from '@/api/modules/index'\r\nimport { DICT_TYPE_BED_TYPE, DictTypeEnum, HAS_WINDOWS } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { Minus, Plus } from '@element-plus/icons-vue'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  rtName: '',\r\n  area: 0,\r\n  isWindow: '',\r\n  peopleNum: 1,\r\n  isRestRoom: '1',\r\n  isVirtual: '0',\r\n  isGRt: '1',\r\n  shortName: '',\r\n  isEnable: '1',\r\n  intro: '',\r\n  roomTypeBeds: [\r\n    {\r\n      bedTypeCode: '',\r\n      // 床型名称\r\n      bedTypeName: '',\r\n      // 床型尺寸\r\n      sizeCode: '',\r\n      sizeName: '',\r\n      // 数量\r\n      num: 1,\r\n    },\r\n  ],\r\n})\r\nconst formRules = ref<FormRules>({\r\n  rtName: [{ required: true, message: t('form.rtNameRequired'), trigger: 'blur' }],\r\n  shortName: [{ required: true, message: t('form.shortNameRequired'), trigger: 'blur' }],\r\n  peopleNum: [{ type: 'number', min: 1, max: 100, message: t('form.peopleNumMessage'), trigger: 'blur' }],\r\n  area: [{ type: 'number', min: 1, message: t('form.areaMessage'), trigger: 'blur' }],\r\n})\r\nonMounted(() => {\r\n  getConstants()\r\n})\r\n\r\n// 常量里包括多个\r\nconst dictTypes = [DICT_TYPE_BED_TYPE, DictTypeEnum.BED_SIZE]\r\n\r\n// 床型列表\r\nconst bedTypeList = ref<DictDataModel[]>([])\r\n// 尺寸列表\r\nconst roomSizeList = ref<DictDataModel[]>([])\r\n\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    bedTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_BED_TYPE)\r\n    roomSizeList.value = res.data.filter((item: any) => item.dictType === DictTypeEnum.BED_SIZE)\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            // form.value.basePrice = parseFloat(form.value.basePrice)\r\n            rtApi.createRoomType(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('form.submitSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n\r\nfunction addRow() {\r\n  const newRow = {\r\n    bedTypeCode: '',\r\n    // 房型名称\r\n    bedTypeName: '',\r\n    // 房型尺寸\r\n    sizeCode: '',\r\n    sizeName: '',\r\n    num: 1,\r\n  }\r\n  form.value.roomTypeBeds.push(newRow)\r\n}\r\n\r\nfunction deleteRow(index: number) {\r\n  form.value.roomTypeBeds.splice(index, 1)\r\n}\r\n\r\nfunction updateTemplateName(ticketItem: any, templateCode: any) {\r\n  const selectedTicketData = bedTypeList.value.find((item) => item.value === templateCode)\r\n  if (selectedTicketData) {\r\n    ticketItem.bedTypeName = selectedTicketData.label\r\n  } else {\r\n    ticketItem.bedTypeName = ''\r\n  }\r\n}\r\n\r\nfunction updateTemplateSize(ticketItem: any, templateCode: any) {\r\n  const selectedTicketData = roomSizeList.value.find((item) => item.value === templateCode)\r\n  if (selectedTicketData) {\r\n    ticketItem.sizeName = selectedTicketData.label\r\n  } else {\r\n    ticketItem.sizeName = ''\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-suffix=\"：\" label-width=\"140px\">\r\n      <el-form-item :label=\"t('form.roomTypeName')\" prop=\"rtName\">\r\n        <el-input v-model=\"form.rtName\" maxlength=\"32\" :placeholder=\"t('form.enterRoomTypeName')\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.groupRoomTypeAbbreviation')\" prop=\"shortName\">\r\n        <el-input v-model=\"form.shortName\" maxlength=\"32\" :placeholder=\"t('form.enterGroupRoomTypeAbbreviation')\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.roomArea')\" prop=\"area\"> <el-input-number v-model=\"form.area\" :max=\"10000\" :min=\"0\" :precision=\"2\" controls-position=\"right\" /> <span style=\"margin-left: 3px; font-size: 16px\">㎡</span> </el-form-item>\r\n      <el-form-item :label=\"t('form.roomConfiguration')\">\r\n        <div v-for=\"(iem, index) in form.roomTypeBeds\" :key=\"index\" style=\"display: flex; align-items: center; margin-bottom: 8px\">\r\n          <el-select v-model=\"iem.bedTypeCode\" :placeholder=\"t('form.bedType')\" style=\"width: 100px\" @change=\"updateTemplateName(iem, $event)\">\r\n            <el-option v-for=\"item in bedTypeList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-select v-model=\"iem.sizeCode\" :placeholder=\"t('form.size')\" style=\"width: 100px\" @change=\"updateTemplateSize(iem, $event)\">\r\n            <el-option v-for=\"item in roomSizeList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-input-number v-model=\"iem.num\" :placeholder=\"t('form.quantity')\" :max=\"99\" :min=\"1\" controls-position=\"right\" style=\"width: 100px; margin-right: 10px\" />\r\n          <div class=\"add-delete\">\r\n            <el-button v-if=\"index === form.roomTypeBeds.length - 1\" :icon=\"Plus\" circle type=\"primary\" @click=\"addRow\" />\r\n            <el-button v-if=\"form.roomTypeBeds.length > 1\" :icon=\"Minus\" circle type=\"danger\" @click=\"deleteRow(index)\" />\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.maxOccupancy')\" prop=\"peopleNum\">\r\n        <el-input-number v-model=\"form.peopleNum\" :max=\"100\" :min=\"0\" controls-position=\"right\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.hasWindow')\">\r\n        <el-select v-model=\"form.isWindow\" :placeholder=\"t('form.pleaseSelect')\">\r\n          <el-option v-for=\"item in HAS_WINDOWS\" :key=\"item.key\" :value=\"item.key\" :label=\"t(item.value)\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.hasRestRoom')\">\r\n        <el-switch v-model=\"form.isRestRoom\" :active-text=\"t('form.yes')\" active-value=\"1\" :inactive-text=\"t('form.no')\" inactive-value=\"0\" inline-prompt />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.isVirtualRoom')\" prop=\"isVirtual\">\r\n        <el-switch v-model=\"form.isVirtual\" :active-text=\"t('form.yes')\" active-value=\"1\" :inactive-text=\"t('form.no')\" inactive-value=\"0\" inline-prompt />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.isEnabled')\" prop=\"isEnable\">\r\n        <el-switch v-model=\"form.isEnable\" :active-text=\"t('form.yes')\" active-value=\"1\" :inactive-text=\"t('form.no')\" inactive-value=\"0\" inline-prompt />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('form.remarks')\">\r\n        <el-input v-model=\"form.intro\" maxlength=\"255\" :rows=\"4\" type=\"textarea\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.add-delete {\r\n  display: flex;\r\n\r\n  span {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 26px;\r\n    height: 26px;\r\n    margin-left: 10px;\r\n    cursor: pointer;\r\n    border: 1px solid #999;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "formRef", "form", "gcode", "rtName", "area", "isWindow", "peopleNum", "isRestRoom", "isVirtual", "isGRt", "shortName", "isEnable", "intro", "roomTypeBeds", "bedTypeCode", "bedTypeName", "sizeCode", "sizeName", "num", "formRules", "required", "message", "trigger", "type", "min", "max", "onMounted", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "then", "res", "bedTypeList", "value", "data", "filter", "item", "dictType", "DICT_TYPE_BED_TYPE", "roomSizeList", "DictTypeEnum", "BED_SIZE", "addRow", "push", "__expose", "submit", "Promise", "resolve", "validate", "valid", "rtApi", "createRoomType", "code", "ElMessage", "success", "center", "error", "msg", "ticketItem", "templateCode", "selectedTicketData", "find", "label", "index", "splice"], "mappings": "45BAuGA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,MAAOR,EAAUQ,MACjBC,OAAQ,GACRC,KAAM,EACNC,SAAU,GACVC,UAAW,EACXC,WAAY,IACZC,UAAW,IACXC,MAAO,IACPC,UAAW,GACXC,SAAU,IACVC,MAAO,GACPC,aAAc,CACZ,CACEC,YAAa,GAEbC,YAAa,GAEbC,SAAU,GACVC,SAAU,GAEVC,IAAK,MAILC,EAAYpB,EAAe,CAC/BI,OAAQ,CAAC,CAAEiB,UAAU,EAAMC,QAASzB,EAAE,uBAAwB0B,QAAS,SACvEZ,UAAW,CAAC,CAAEU,UAAU,EAAMC,QAASzB,EAAE,0BAA2B0B,QAAS,SAC7EhB,UAAW,CAAC,CAAEiB,KAAM,SAAUC,IAAK,EAAGC,IAAK,IAAKJ,QAASzB,EAAE,yBAA0B0B,QAAS,SAC9FlB,KAAM,CAAC,CAAEmB,KAAM,SAAUC,IAAK,EAAGH,QAASzB,EAAE,oBAAqB0B,QAAS,WAE5EI,GAAU,KAaRC,EAAYC,iBAAiBC,GAAWC,MAAMC,IAChCC,EAAAC,MAAQF,EAAIG,KAAKC,QAAQC,GAAcA,EAAKC,WAAaC,IACxDC,EAAAN,MAAQF,EAAIG,KAAKC,QAAQC,GAAcA,EAAKC,WAAaG,EAAaC,UAAQ,GAdhF,IAIf,MAAMZ,EAAY,CAACS,EAAoBE,EAAaC,UAG9CT,EAAcjC,EAAqB,IAEnCwC,EAAexC,EAAqB,IAoC1C,SAAS2C,IAUFzC,EAAAgC,MAAMpB,aAAa8B,KATT,CACb7B,YAAa,GAEbC,YAAa,GAEbC,SAAU,GACVC,SAAU,GACVC,IAAK,GAE4B,QArCxB0B,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB/C,EAAQiC,OACNjC,EAAQiC,MAAMe,UAAUC,IAClBA,GAEFC,EAAMC,eAAelD,EAAKgC,OAAOH,MAAMC,IACpB,IAAbA,EAAIqB,MACNC,EAAUC,QAAQ,CAChBjC,QAASzB,EAAE,sBACX2D,QAAQ,IAEFR,KAERM,EAAUG,MAAM,CACdnC,QAASU,EAAI0B,IACbF,QAAQ,GACT,GAEJ,GAEJ,q5CAsBA,SAAmBG,EAAiBC,GACrC,MAAAC,EAAqB5B,EAAYC,MAAM4B,MAAMzB,GAASA,EAAKH,QAAU0B,IAEzED,EAAW3C,YADT6C,EACuBA,EAAmBE,MAEnB,EAC3B,uVAGO,SAAmBJ,EAAiBC,GACrC,MAAAC,EAAqBrB,EAAaN,MAAM4B,MAAMzB,GAASA,EAAKH,QAAU0B,IAE1ED,EAAWzC,SADT2C,EACoBA,EAAmBE,MAEnB,EACxB,2pBAnBF,SAAmBC,GACjB9D,EAAKgC,MAAMpB,aAAamD,OAAOD,EAAO,EAAC"}