{"version": 3, "file": "priceCalendar.api-DMl1yVkH.js", "sources": ["../../src/api/modules/pms/price/priceCalendar.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/price-calendar'\r\n/**\r\n * 价格日历(放盘价)\r\n */\r\nexport default {\r\n  /**\r\n   * 获得某天某房型某渠道的房价\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getPriceCalendarList: (data: {\r\n    gcode: string\r\n    hcode: string\r\n    channelCode: string\r\n    startDate: string\r\n    rtCode?: string\r\n  }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 批量更新房型门市价\r\n   * @param data\r\n   */\r\n  updateRoomTypeBasePrice: (data: any) => api.put(`${BASE_PATH}/update-base-price`, data, {}),\r\n\r\n  /**\r\n   * 获得房型门市价列表\r\n   * @param data\r\n   */\r\n  getRoomTypeBasePrices: (data: {\r\n    gcode: string\r\n    hcode: string\r\n  }) =>\r\n    api.get(`${BASE_PATH}/list-base-price`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获得某天某房型某渠道的房价\r\n   * @param data\r\n   */\r\n  getPriceCalendar: (data: {\r\n    gcode: string\r\n    hcode: string\r\n    channelCode: string\r\n    rtCode?: string\r\n    date: string\r\n  }) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 批量修改售价-统一改价\r\n   * @param data\r\n   */\r\n  updatePriceCalendarUnify: (data: any) => api.put(`${BASE_PATH}/update-unify`, data),\r\n\r\n  /**\r\n   * 批量修改售价-单独改价\r\n   * @param data\r\n   */\r\n  updatePriceCalendarAlone: (data: any) => api.put(`${BASE_PATH}/update-alone`, data),\r\n\r\n  /**\r\n   * 批量更新房型门市价\r\n   * @param data\r\n   */\r\n  updateBasePrice: (data: any) => api.put(`${BASE_PATH}/update-base-price`, data),\r\n\r\n  /**\r\n * 获得房价日历修改日志分页\r\n * @param data\r\n */priceCalendarLogPage\r\n  : (data: {\r\n    gcode: string\r\n    hcode: string\r\n  }) =>\r\n    api.get(`admin-api/pms/price-calendar-log/page`, {\r\n      params: data,\r\n    }),\r\n}\r\n"], "names": ["BASE_PATH", "priceCalendarApi", "getPriceCalendarList", "data", "api", "get", "params", "updateRoomTypeBasePrice", "put", "getRoomTypeBasePrices", "getPriceCalendar", "updatePriceCalendarUnify", "updatePriceCalendarAlone", "updateBasePrice", "priceCalendarLogPage"], "mappings": "wCAEA,MAAMA,EAAY,+BAIHC,EAAA,CAMbC,qBAAuBC,GAOrBC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAOZI,wBAA0BJ,GAAcC,EAAII,IAAI,GAAGR,sBAA+BG,EAAM,IAMxFM,sBAAwBN,GAItBC,EAAIC,IAAI,GAAGL,oBAA6B,CACtCM,OAAQH,IAOZO,iBAAmBP,GAOjBC,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQH,IAOZQ,yBAA2BR,GAAcC,EAAII,IAAI,GAAGR,iBAA0BG,GAM9ES,yBAA2BT,GAAcC,EAAII,IAAI,GAAGR,iBAA0BG,GAM9EU,gBAAkBV,GAAcC,EAAII,IAAI,GAAGR,sBAA+BG,GAKzEW,qBACEX,GAIDC,EAAIC,IAAI,wCAAyC,CAC/CC,OAAQH"}