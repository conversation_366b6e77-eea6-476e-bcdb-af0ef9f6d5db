{"version": 3, "file": "myStringUtil-D79vpWmP.js", "sources": ["../../src/utils/myStringUtil.ts"], "sourcesContent": ["/**\r\n * 当text长度超过length时，则只返回前10个字符，并在末尾添加\"...\"；否则，直接返回text。\r\n * @param text 文本\r\n * @param length 长度\r\n * @returns string\r\n */\r\nexport function limitedText(text: string, length: number) {\r\n  if (text.length > length) {\r\n    return `${text.slice(0, 10)}...`\r\n  }\r\n  return text\r\n}\r\n\r\n/**\r\n * 根据身份证号码判断性别\r\n * @param idCard 身份证号码\r\n * @returns 性别：'1' 表示男性，'0' 表示女性，'2' 表示未知\r\n */\r\nexport function getGenderFromIdCard(idCard: string): '0' | '1' | '2' {\r\n  if (!idCard || typeof idCard !== 'string') {\r\n    return '2' // 未知\r\n  }\r\n\r\n  // 去除空格和特殊字符\r\n  const cleanIdCard = idCard.replace(/\\s+/g, '').replace(/[^0-9X]/gi, '')\r\n\r\n  // 验证身份证号码格式\r\n  if (!/^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9X]$/i.test(cleanIdCard)) {\r\n    return '2' // 格式不正确，返回未知\r\n  }\r\n\r\n  // 获取倒数第二位数字（性别位）\r\n  const genderDigit = Number.parseInt(cleanIdCard.charAt(16))\r\n\r\n  // 奇数为男性，偶数为女性\r\n  return genderDigit % 2 === 1 ? '1' : '0'\r\n}\r\n\r\n/**\r\n * 验证身份证号码是否有效\r\n * @param idCard 身份证号码\r\n * @returns 是否有效\r\n */\r\nexport function validateIdCard(idCard: string): boolean {\r\n  if (!idCard || typeof idCard !== 'string') {\r\n    return false\r\n  }\r\n\r\n  // 去除空格和特殊字符\r\n  const cleanIdCard = idCard.replace(/\\s+/g, '').replace(/[^0-9X]/gi, '')\r\n\r\n  // 验证长度和基本格式\r\n  if (!/^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9X]$/i.test(cleanIdCard)) {\r\n    return false\r\n  }\r\n\r\n  // 验证校验码\r\n  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]\r\n  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']\r\n\r\n  let sum = 0\r\n  for (let i = 0; i < 17; i++) {\r\n    sum += Number.parseInt(cleanIdCard.charAt(i)) * weights[i]\r\n  }\r\n\r\n  const checkCode = checkCodes[sum % 11]\r\n  return cleanIdCard.charAt(17).toUpperCase() === checkCode\r\n}\r\n"], "names": ["limitedText", "text", "length", "slice", "getGenderFromIdCard", "idCard", "cleanIdCard", "replace", "test", "Number", "parseInt", "char<PERSON>t"], "mappings": "AAMgB,SAAAA,EAAYC,EAAcC,GACpC,OAAAD,EAAKC,OAASA,EACT,GAAGD,EAAKE,MAAM,EAAG,SAEnBF,CACT,CAOO,SAASG,EAAoBC,GAClC,IAAKA,GAA4B,iBAAXA,EACb,MAAA,IAIH,MAAAC,EAAcD,EAAOE,QAAQ,OAAQ,IAAIA,QAAQ,YAAa,IAGpE,IAAK,uFAAuFC,KAAKF,GACxF,MAAA,IAOF,OAHaG,OAAOC,SAASJ,EAAYK,OAAO,KAGlC,GAAM,EAAI,IAAM,GACvC"}