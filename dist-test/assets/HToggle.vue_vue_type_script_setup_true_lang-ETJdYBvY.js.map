{"version": 3, "file": "HToggle.vue_vue_type_script_setup_true_lang-ETJdYBvY.js", "sources": ["../../node_modules/@headlessui/vue/dist/components/switch/switch.js", "../../node_modules/@headlessui/vue/dist/hooks/use-controllable.js", "../../node_modules/@headlessui/vue/dist/utils/form.js", "../../src/layouts/ui-kit/HToggle.vue"], "sourcesContent": ["import{computed as u,defineComponent as v,Fragment as H,h as S,inject as M,onMounted as I,provide as P,ref as w,watch as j}from\"vue\";import{useControllable as G}from'../../hooks/use-controllable.js';import{useId as V}from'../../hooks/use-id.js';import{useResolveButtonType as F}from'../../hooks/use-resolve-button-type.js';import{Features as O,Hidden as A}from'../../internal/hidden.js';import{Keys as g}from'../../keyboard.js';import{dom as N}from'../../utils/dom.js';import{attemptSubmit as $}from'../../utils/form.js';import{compact as U,omit as _,render as k}from'../../utils/render.js';import{Description as q,useDescriptions as z}from'../description/description.js';import{Label as J,useLabels as Q}from'../label/label.js';let C=Symbol(\"GroupContext\"),oe=v({name:\"SwitchGroup\",props:{as:{type:[Object,String],default:\"template\"}},setup(l,{slots:c,attrs:i}){let r=w(null),f=Q({name:\"SwitchLabel\",props:{htmlFor:u(()=>{var t;return(t=r.value)==null?void 0:t.id}),onClick(t){r.value&&(t.currentTarget.tagName===\"LABEL\"&&t.preventDefault(),r.value.click(),r.value.focus({preventScroll:!0}))}}}),p=z({name:\"SwitchDescription\"});return P(C,{switchRef:r,labelledby:f,describedby:p}),()=>k({theirProps:l,ourProps:{},slot:{},slots:c,attrs:i,name:\"SwitchGroup\"})}}),ue=v({name:\"Switch\",emits:{\"update:modelValue\":l=>!0},props:{as:{type:[Object,String],default:\"button\"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(l,{emit:c,attrs:i,slots:r,expose:f}){var h;let p=(h=l.id)!=null?h:`headlessui-switch-${V()}`,n=M(C,null),[t,s]=G(u(()=>l.modelValue),e=>c(\"update:modelValue\",e),u(()=>l.defaultChecked));function m(){s(!t.value)}let E=w(null),o=n===null?E:n.switchRef,L=F(u(()=>({as:l.as,type:i.type})),o);f({el:o,$el:o});function D(e){e.preventDefault(),m()}function R(e){e.key===g.Space?(e.preventDefault(),m()):e.key===g.Enter&&$(e.currentTarget)}function x(e){e.preventDefault()}let d=u(()=>{var e,a;return(a=(e=N(o))==null?void 0:e.closest)==null?void 0:a.call(e,\"form\")});return I(()=>{j([d],()=>{if(!d.value||l.defaultChecked===void 0)return;function e(){s(l.defaultChecked)}return d.value.addEventListener(\"reset\",e),()=>{var a;(a=d.value)==null||a.removeEventListener(\"reset\",e)}},{immediate:!0})}),()=>{let{name:e,value:a,form:K,tabIndex:y,...b}=l,T={checked:t.value},B={id:p,ref:o,role:\"switch\",type:L.value,tabIndex:y===-1?0:y,\"aria-checked\":t.value,\"aria-labelledby\":n==null?void 0:n.labelledby.value,\"aria-describedby\":n==null?void 0:n.describedby.value,onClick:D,onKeyup:R,onKeypress:x};return S(H,[e!=null&&t.value!=null?S(A,U({features:O.Hidden,as:\"input\",type:\"checkbox\",hidden:!0,readOnly:!0,checked:t.value,form:K,disabled:b.disabled,name:e,value:a})):null,k({ourProps:B,theirProps:{...i,..._(b,[\"modelValue\",\"defaultChecked\"])},slot:T,attrs:i,slots:r,name:\"Switch\"})])}}}),de=J,ce=q;export{ue as Switch,ce as SwitchDescription,oe as SwitchGroup,de as SwitchLabel};\n", "import{computed as p,ref as s}from\"vue\";function d(u,e,r){let i=s(r==null?void 0:r.value),f=p(()=>u.value!==void 0);return[p(()=>f.value?u.value:i.value),function(t){return f.value||(i.value=t),e==null?void 0:e(t)}]}export{d as useControllable};\n", "function e(i={},s=null,t=[]){for(let[r,n]of Object.entries(i))o(t,f(s,r),n);return t}function f(i,s){return i?i+\"[\"+s+\"]\":s}function o(i,s,t){if(Array.isArray(t))for(let[r,n]of t.entries())o(i,f(s,r.toString()),n);else t instanceof Date?i.push([s,t.toISOString()]):typeof t==\"boolean\"?i.push([s,t?\"1\":\"0\"]):typeof t==\"string\"?i.push([s,t]):typeof t==\"number\"?i.push([s,`${t}`]):t==null?i.push([s,\"\"]):e(t,s,i)}function p(i){var t,r;let s=(t=i==null?void 0:i.form)!=null?t:i.closest(\"form\");if(s){for(let n of s.elements)if(n!==i&&(n.tagName===\"INPUT\"&&n.type===\"submit\"||n.tagName===\"BUTTON\"&&n.type===\"submit\"||n.nodeName===\"INPUT\"&&n.type===\"image\")){n.click();return}(r=s.requestSubmit)==null||r.call(s)}}export{p as attemptSubmit,e as objectToFormEntries};\n", "<script setup lang=\"ts\">\r\nimport { Switch } from '@headlessui/vue'\r\n\r\nwithDefaults(\r\n  defineProps<{\r\n    disabled?: boolean\r\n    onIcon?: string\r\n    offIcon?: string\r\n  }>(),\r\n  {\r\n    disabled: false,\r\n  },\r\n)\r\n\r\nconst enabled = defineModel<boolean>()\r\n</script>\r\n\r\n<template>\r\n  <Switch v-model=\"enabled\" :disabled=\"disabled\" class=\"relative h-5 w-10 inline-flex flex-shrink-0 cursor-pointer border-2 border-transparent rounded-full p-0 vertical-middle disabled-cursor-not-allowed disabled-opacity-50 focus-outline-none focus-visible-ring-2 focus-visible-ring-offset-2 focus-visible-ring-offset-white dark-focus-visible-ring-offset-gray-900\" :class=\"[enabled ? 'bg-ui-primary' : 'bg-stone-3 dark-bg-stone-7']\">\r\n    <span class=\"pointer-events-none relative inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition-margin duration-200 ease-in-out dark-bg-dark\" :class=\"[enabled ? 'ms-5' : 'ms-0']\">\r\n      <span class=\"absolute inset-0 h-full w-full flex items-center justify-center\">\r\n        <SvgIcon v-if=\"(enabled && onIcon) || (!enabled && offIcon)\" :name=\"(enabled ? onIcon : offIcon) as string\" class=\"h-3 w-3 text-stone-7 dark-text-stone-3\" />\r\n      </span>\r\n    </span>\r\n  </Switch>\r\n</template>\r\n"], "names": ["C", "Symbol", "ue", "v", "name", "emits", "l", "props", "as", "type", "Object", "String", "default", "modelValue", "Boolean", "defaultChecked", "optional", "form", "value", "id", "disabled", "tabIndex", "Number", "inheritAttrs", "setup", "emit", "c", "attrs", "i", "slots", "r", "expose", "f", "h", "p", "V", "n", "M", "t", "s", "u", "e", "G", "m", "s$1", "E", "w", "o", "switchRef", "L", "F", "D", "preventDefault", "R", "key", "g", "Space", "Enter", "closest", "elements", "tagName", "nodeName", "click", "requestSubmit", "call", "$", "currentTarget", "x", "el", "$el", "d", "a", "N", "I", "watch", "addEventListener", "removeEventListener", "immediate", "K", "y", "b", "T", "checked", "B", "ref", "role", "<PERSON>by", "<PERSON><PERSON>", "onClick", "onKeyup", "onKeypress", "S", "H", "A", "U", "features", "O", "Hidden", "hidden", "readOnly", "k", "ourProps", "theirProps", "_", "slot", "enabled", "_useModel", "__props"], "mappings": "6RAA4tB,IAACA,EAAEC,OAAO,gBAAwgBC,EAAGC,EAAE,CAACC,KAAK,SAASC,MAAM,CAAC,oBAAoBC,IAAG,GAAIC,MAAM,CAACC,GAAG,CAACC,KAAK,CAACC,OAAOC,QAAQC,QAAQ,UAAUC,WAAW,CAACJ,KAAKK,QAAQF,aAAQ,GAAQG,eAAe,CAACN,KAAKK,QAAQE,UAAS,GAAIC,KAAK,CAACR,KAAKE,OAAOK,UAAS,GAAIZ,KAAK,CAACK,KAAKE,OAAOK,UAAS,GAAIE,MAAM,CAACT,KAAKE,OAAOK,UAAS,GAAIG,GAAG,CAACV,KAAKE,OAAOC,QAAQ,MAAMQ,SAAS,CAACX,KAAKK,QAAQF,SAAQ,GAAIS,SAAS,CAACZ,KAAKa,OAAOV,QAAQ,IAAIW,cAAa,EAAG,KAAAC,CAAMlB,GAAGmB,KAAKC,EAAEC,MAAMC,EAAEC,MAAMC,EAAEC,OAAOC,IAAQC,IAAAA,EAAE,IAAIC,EAAY,OAATD,EAAE3B,EAAEa,IAAUc,EAAE,qBAAqBE,MAAMC,EAAEC,EAAErC,EAAE,OAAOsC,EAAEC,GCApsD,SAAWC,EAAEC,EAAEX,GAAG,IAAIF,EAAEW,EAAK,MAAHT,OAAQ,EAAOA,EAAEZ,OAAOc,EAAEE,GAAE,SAAc,IAAVM,EAAEtB,QAAsB,MAAA,CAACgB,GAAE,IAAIF,EAAEd,MAAMsB,EAAEtB,MAAMU,EAAEV,QAAO,SAASoB,GAAUN,OAAAA,EAAEd,QAAQU,EAAEV,MAAMoB,GAAM,MAAHG,OAAQ,EAAOA,EAAEH,EAAE,EAAE,CDAwhDI,CAAEF,GAAE,IAAIlC,EAAEO,cAAY4B,GAAGf,EAAE,oBAAoBe,IAAGD,GAAE,IAAIlC,EAAES,kBAAiB,SAAS4B,IAAMC,GAACN,EAAEpB,MAAM,CAAK2B,IAAAA,EAAEC,EAAE,MAAMC,EAAM,OAAJX,EAASS,EAAET,EAAEY,UAAUC,EAAEC,EAAEV,GAAE,KAAA,CAAMhC,GAAGF,EAAEE,GAAGC,KAAKmB,EAAEnB,SAAQsC,GAAmB,SAASI,EAAEV,GAAKA,EAAAW,iBAAiBT,GAAG,CAAC,SAASU,EAAEZ,GAAGA,EAAEa,MAAMC,EAAEC,OAAOf,EAAEW,iBAAiBT,KAAKF,EAAEa,MAAMC,EAAEE,OEA5nD,SAAW7B,GAAG,IAAIU,EAAER,EAAMS,IAAAA,EAA6B,OAA1BD,EAAK,MAAHV,OAAQ,EAAOA,EAAEX,MAAYqB,EAAEV,EAAE8B,QAAQ,QAAQ,GAAGnB,EAAE,CAAS,IAAA,IAAAH,KAAKG,EAAEoB,SAAS,GAAGvB,IAAIR,IAAgB,UAAZQ,EAAEwB,SAA4B,WAATxB,EAAE3B,MAA6B,WAAZ2B,EAAEwB,SAA6B,WAATxB,EAAE3B,MAA8B,UAAb2B,EAAEyB,UAA6B,UAATzB,EAAE3B,MAA2B,YAAV2B,EAAE0B,QAAoC,OAApBhC,EAAES,EAAEwB,gBAAsBjC,EAAEkC,KAAKzB,EAAE,CAAC,CFA01C0B,CAAExB,EAAEyB,cAAc,CAAC,SAASC,EAAE1B,GAAGA,EAAEW,gBAAgB,CAAhLpB,EAAE,CAACoC,GAAGrB,EAAEsB,IAAItB,IAAyKuB,IAAAA,EAAE9B,GAAE,KAAK,IAAIC,EAAE8B,EAAE,OAA2C,OAApCA,EAAY,OAAT9B,EAAE+B,EAAEzB,SAAU,EAAON,EAAEiB,cAAe,EAAOa,EAAEP,KAAKvB,EAAE,OAAM,IAAI,OAAOgC,GAAE,KAAOC,EAAA,CAACJ,IAAG,KAAK,GAAIA,EAAEpD,YAA0B,IAAnBZ,EAAES,eAAgE,OAAOuD,EAAEpD,MAAMyD,iBAAiB,QAAQlC,GAAG,KAAS,IAAA8B,EAAe,OAAZA,EAAED,EAAEpD,QAAcqD,EAAEK,oBAAoB,QAAQnC,EAAC,EAAzI,SAASA,IAAIF,EAAEjC,EAAES,eAAe,CAA0G,GAAG,CAAC8D,WAAU,GAAG,IAAI,KAAQ,IAACzE,KAAKqC,EAAEvB,MAAMqD,EAAEtD,KAAK6D,EAAEzD,SAAS0D,KAAKC,GAAG1E,EAAE2E,EAAE,CAACC,QAAQ5C,EAAEpB,OAAOiE,EAAE,CAAChE,GAAGe,EAAEkD,IAAIrC,EAAEsC,KAAK,SAAS5E,KAAKwC,EAAE/B,MAAMG,UAAa,IAAJ0D,EAAO,EAAEA,EAAE,eAAezC,EAAEpB,MAAM,kBAAqB,MAAHkB,OAAQ,EAAOA,EAAEkD,WAAWpE,MAAM,mBAAsB,MAAHkB,OAAQ,EAAOA,EAAEmD,YAAYrE,MAAMsE,QAAQrC,EAAEsC,QAAQpC,EAAEqC,WAAWvB,GAAG,OAAOwB,EAAEC,EAAE,CAAI,MAAHnD,GAAkB,MAATH,EAAEpB,MAAYyE,EAAEE,EAAEC,EAAE,CAACC,SAASC,EAAEC,OAAOzF,GAAG,QAAQC,KAAK,WAAWyF,QAAO,EAAGC,UAAS,EAAGjB,QAAQ5C,EAAEpB,MAAMD,KAAK6D,EAAE1D,SAAS4D,EAAE5D,SAAShB,KAAKqC,EAAEvB,MAAMqD,KAAK,KAAK6B,EAAE,CAACC,SAASlB,EAAEmB,WAAW,IAAI1E,KAAK2E,EAAEvB,EAAE,CAAC,aAAa,oBAAoBwB,KAAKvB,EAAEtD,MAAMC,EAAEC,MAAMC,EAAE1B,KAAK,YAAW,CAAE,kQGct9F,MAAAqG,EAAUC,EAAoBC,EAAA", "x_google_ignoreList": [0, 1, 2]}