{"version": 3, "file": "doorLockForm-CB-dcRNm.js", "sources": ["../../src/views/merchant/equipment/configuration/doorlocks/components/doorLockForm.vue"], "sourcesContent": ["<i18n>\r\n    {\r\n      \"en\": {\r\n        \"addDoorLock\": \"Add Door Lock\",\r\n        \"editDoorLock\": \"Edit Door Lock\",\r\n        \"deviceName\": \"Device Name\",\r\n        \"deviceCode\": \"Device Code\",\r\n        \"lockBrand\": \"Lock Brand\",\r\n        \"version\": \"Version\",\r\n        \"port\": \"Port\",\r\n        \"cardType\": \"Card Type\",\r\n        \"databasePath\": \"Database Path\",\r\n        \"allowUnlocking\": \"Allow Unlocking\",\r\n        \"replaceOldCard\": \"Replace Old Card\",\r\n        \"checkInTime\": \"Check Check-in Time\",\r\n        \"remark\": \"Remark\",\r\n        \"yes\": \"Yes\",\r\n        \"no\": \"No\",\r\n        \"save\": \"Save\",\r\n        \"cancel\": \"Cancel\",\r\n        \"saveSuccess\": \"Saved successfully\",\r\n        \"pleaseEnterDeviceName\": \"Please enter device name\",\r\n        \"pleaseEnterDeviceCode\": \"Please enter device code\",\r\n        \"pleaseSelectBrand\": \"Please select brand\",\r\n        \"pleaseEnterVersion\": \"Please enter version\",\r\n        \"pleaseEnterPort\": \"Please enter port\",\r\n        \"pleaseSelectCardType\": \"Please select card type\",\r\n        \"pleaseEnterDatabasePath\": \"Please enter database path\",\r\n        \"publicParams\": \"Public Parameters:\",\r\n        \"paramName\": \"Name\",\r\n        \"paramCode\": \"Code\",\r\n        \"paramContent\": \"Content\",\r\n        \"enterPlaceholder\": \"Please enter\",\r\n      },\r\n      \"zh-cn\": {\r\n        \"addDoorLock\": \"添加门锁\",\r\n        \"editDoorLock\": \"编辑门锁\",\r\n        \"deviceName\": \"设备名称\",\r\n        \"deviceCode\": \"设备代码\",\r\n        \"lockBrand\": \"门锁品牌\",\r\n        \"version\": \"版本\",\r\n        \"port\": \"端口\",\r\n        \"cardType\": \"发卡器类型\",\r\n        \"databasePath\": \"数据库路径\",\r\n        \"allowUnlocking\": \"允许开反锁\",\r\n        \"replaceOldCard\": \"新卡替换旧卡\",\r\n        \"checkInTime\": \"检查入住时间\",\r\n        \"remark\": \"备注\",\r\n        \"yes\": \"是\",\r\n        \"no\": \"否\",\r\n        \"save\": \"保存\",\r\n        \"cancel\": \"取消\",\r\n        \"saveSuccess\": \"保存成功\",\r\n        \"pleaseEnterDeviceName\": \"请输入设备名称\",\r\n        \"pleaseEnterDeviceCode\": \"请输入设备代码\",\r\n        \"pleaseSelectBrand\": \"请选择品牌\",\r\n        \"pleaseEnterVersion\": \"请输入版本\",\r\n        \"pleaseEnterPort\": \"请输入端口\",\r\n        \"pleaseSelectCardType\": \"请选择发卡器类型\",\r\n        \"pleaseEnterDatabasePath\": \"请输入数据库路径\",\r\n        \"publicParams\": \"公共参数：\",\r\n        \"paramName\": \"参数名称\",\r\n        \"paramCode\": \"参数代码\",\r\n        \"paramContent\": \"参数内容\",\r\n        \"enterPlaceholder\": \"请输入\",\r\n      },\r\n      \"km\": {\r\n        \"addDoorLock\": \"បន្ថែមសោទ្វារ\",\r\n        \"editDoorLock\": \"កែសម្រួលសោទ្វារ\",\r\n        \"deviceName\": \"ឈ្មោះឧបករណ៍\",\r\n        \"deviceCode\": \"លេខកូដឧបករណ៍\",\r\n        \"lockBrand\": \"ម៉ាកសោ\",\r\n        \"version\": \"កំណែ\",\r\n        \"port\": \"ច្រក\",\r\n        \"cardType\": \"ប្រភេទកាត\",\r\n        \"databasePath\": \"ផ្លូវទៅកាន់មូលដ្ឋានទិន្នន័យ\",\r\n        \"allowUnlocking\": \"អនុញ្ញាតឱ្យបើកសោ\",\r\n        \"replaceOldCard\": \"កាតថ្មីជំនួសកាតចាស់\",\r\n        \"checkInTime\": \"ពិនិត្យម៉ោងចូល\",\r\n        \"remark\": \"ចំណាំ\",\r\n        \"yes\": \"បាទ/ចាស\",\r\n        \"no\": \"ទេ\",\r\n        \"save\": \"រក្សាទុក\",\r\n        \"cancel\": \"បោះបង់\",\r\n        \"saveSuccess\": \"បានរក្សាទុកដោយជោគជ័យ\",\r\n        \"pleaseEnterDeviceName\": \"សូមបញ្ចូលឈ្មោះឧបករណ៍\",\r\n        \"pleaseEnterDeviceCode\": \"សូមបញ្ចូលលេខកូដឧបករណ៍\",\r\n        \"pleaseSelectBrand\": \"សូមជ្រើសរើសម៉ាក\",\r\n        \"pleaseEnterVersion\": \"សូមបញ្ចូលកំណែ\",\r\n        \"pleaseEnterPort\": \"សូមបញ្ចូលច្រក\",\r\n        \"pleaseSelectCardType\": \"សូមជ្រើសរើសប្រភេទកាត\",\r\n        \"pleaseEnterDatabasePath\": \"សូមបញ្ចូលផ្លូវទៅកាន់មូលដ្ឋានទិន្នន័យ\",\r\n        \"publicParams\": \"ប៉ារ៉ាម៉ែត្រសាធារណៈ៖\",\r\n        \"paramName\": \"ឈ្មោះប៉ារ៉ាម៉ែត្រ\",\r\n        \"paramCode\": \"លេខកូដប៉ារ៉ាម៉ែត្រ\",\r\n        \"paramContent\": \"មាតិកាប៉ារ៉ាម៉ែត្រ\",\r\n        \"enterPlaceholder\": \"សូមបញ្ចូល\"\r\n      }\r\n    }\r\n    </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { deviceApi } from '@/api/modules'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    editingId?: number | null\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    editingId: null,\r\n  }\r\n)\r\nconst emits = defineEmits<Emits>()\r\ninterface Emits {\r\n  (e: 'update:modelValue', value: boolean): void\r\n  (e: 'success'): void\r\n  (e: 'close'): void\r\n}\r\nconst dialogVisible = computed({\r\n  get: () => props.modelValue,\r\n  set: (value: boolean) => {\r\n    emits('update:modelValue', value)\r\n  },\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst formRef = ref()\r\nconst saving = ref(false)\r\nconst brandOptions = ref<{ label: string; value: string }[]>([])\r\nconst conf = ref<{ parameterName: string; parameterCode: string; parameterContent: string }[]>([])\r\nconst formData = ref({\r\n  deviceCode: '',\r\n  brandCode: '',\r\n  version: '',\r\n  remark: '',\r\n  state: '1',\r\n  devRemark: '',\r\n})\r\n\r\nconst formRules = {\r\n  deviceCode: [{ required: true, message: t('pleaseSelectBrand'), trigger: 'blur' }],\r\n  version: [{ required: true, message: t('pleaseEnterVersion'), trigger: 'blur' }],\r\n}\r\nconst showDeviceDetails = ref(false)\r\nonMounted(async () => {\r\n  await fetchBrandOptions()\r\n\r\n  // 如果有 editingId，获取门锁详情\r\n  if (props.editingId) {\r\n    await fetchDoorLockDetails(props.editingId)\r\n  }\r\n})\r\n\r\nwatch(\r\n  () => props.editingId,\r\n  async (newId) => {\r\n    if (newId) {\r\n      await fetchDoorLockDetails(newId)\r\n    } else {\r\n      // 重置表单\r\n      resetForm()\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\n\r\n// 获取门锁详情\r\nasync function fetchDoorLockDetails(id: number) {\r\n  try {\r\n    const res = await deviceApi.getHotelDeviceById({\r\n      id,\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n    })\r\n\r\n    if (res.code === 0 && res.data) {\r\n      const data = res.data\r\n\r\n      // 初始化表单数据\r\n      formData.value = {\r\n        deviceCode: data.deviceCode || '',\r\n        brandCode: data.brandCode || '',\r\n        version: data.version || '',\r\n        remark: data.remark || '',\r\n        state: data.state || '1',\r\n        devRemark: data.devRemark || '',\r\n      }\r\n\r\n      // 初始化配置参数\r\n      if (data.conf && Array.isArray(data.conf)) {\r\n        conf.value = data.conf.map((item: any) => ({\r\n          parameterCode: item.parameterCode || '',\r\n          parameterName: item.parameterName || '',\r\n          parameterContent: item.parameterContent || '',\r\n        }))\r\n      }\r\n\r\n      showDeviceDetails.value = true\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to fetch door lock details:', error)\r\n    ElMessage.error('获取门锁详情失败')\r\n  }\r\n}\r\n\r\n// 重置表单\r\nfunction resetForm() {\r\n  formData.value = {\r\n    deviceCode: '',\r\n    brandCode: '',\r\n    version: '',\r\n    remark: '',\r\n    state: '1',\r\n    devRemark: '',\r\n  }\r\n  conf.value = []\r\n  showDeviceDetails.value = false\r\n\r\n  // 重置表单验证\r\n  if (formRef.value) {\r\n    formRef.value.resetFields()\r\n  }\r\n}\r\n\r\n// 获取品牌列表\r\nasync function fetchBrandOptions() {\r\n  const res = await deviceApi.listBrand({\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  })\r\n  if (res.code === 0) {\r\n    brandOptions.value = res.data.map((item: any) => ({\r\n      label: `${item.brandName}/${item.deviceVerName}/${item.version}`,\r\n      value: item.deviceCode,\r\n    }))\r\n  }\r\n}\r\n\r\nfunction onChange() {\r\n  deviceApi\r\n    .deviceInfo({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      deviceCode: formData.value.deviceCode,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        formData.value.brandCode = res.data.brandCode\r\n        formData.value.version = res.data.version\r\n        formData.value.remark = res.data.remark || ''\r\n        formData.value.devRemark = res.data.remark || ''\r\n        if (res.data.conf && Array.isArray(res.data.conf)) {\r\n          conf.value = res.data.conf.map((item: any) => ({\r\n            parameterCode: item.parameterCode || '',\r\n            parameterName: item.parameterName || '',\r\n            parameterContent: item.parameterContent || '',\r\n          }))\r\n        }\r\n        showDeviceDetails.value = true\r\n      }\r\n    })\r\n}\r\n\r\n// 保存\r\nasync function handleSave() {\r\n  if (!formRef.value) {\r\n    return\r\n  }\r\n\r\n  try {\r\n    await formRef.value.validate()\r\n    saving.value = true\r\n\r\n    const data = {\r\n      ...formData.value,\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      deviceType: 'door_lock',\r\n      conf: conf.value,\r\n    }\r\n\r\n    let res\r\n    if (props.editingId) {\r\n      // 编辑\r\n      res = await deviceApi.updateHotelDevice({\r\n        ...data,\r\n        id: props.editingId,\r\n      })\r\n    } else {\r\n      // 添加\r\n      res = await deviceApi.createHotelDevice(data)\r\n    }\r\n\r\n    if (res.code === 0) {\r\n      ElMessage.success(t('saveSuccess'))\r\n      emits('success')\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to save door lock:', error)\r\n  } finally {\r\n    saving.value = false\r\n  }\r\n}\r\n\r\n// 关闭弹窗\r\nfunction handleClose() {\r\n  emits('close')\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"dialogVisible\" :title=\"editingId ? t('editDoorLock') : t('addDoorLock')\" width=\"600px\" :before-close=\"handleClose\" :close-on-click-modal=\"false\" destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"formData\" :rules=\"formRules\" label-width=\"120px\" label-position=\"right\">\r\n      <el-form-item :label=\"t('lockBrand')\" prop=\"deviceCode\">\r\n        <el-select v-model=\"formData.deviceCode\" :placeholder=\"t('pleaseSelectBrand')\" style=\"width: 100%\" @change=\"onChange\">\r\n          <el-option v-for=\"brand in brandOptions\" :key=\"brand.value\" :label=\"brand.label\" :value=\"brand.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <template v-if=\"showDeviceDetails\">\r\n        <el-form-item :label=\"t('version')\" prop=\"version\">\r\n          <el-input v-model=\"formData.version\" placeholder=\"\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('remark')\">\r\n          {{ formData.devRemark }}\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('publicParams')\">\r\n          <div v-for=\"(iem, index) in conf\" :key=\"index\" class=\"mb-2\" style=\"display: flex; align-items: center\">\r\n            <el-row>\r\n              <el-col :md=\"10\">\r\n                <el-form-item :label=\"t('paramName')\" label-width=\"80px\">\r\n                  <el-input v-model=\"iem.parameterName\" disabled :placeholder=\"t('enterPlaceholder')\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :md=\"10\">\r\n                <el-form-item :label=\"t('paramCode')\" label-width=\"80px\">\r\n                  <el-input v-model=\"iem.parameterCode\" disabled :placeholder=\"t('enterPlaceholder')\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :md=\"20\">\r\n                <el-form-item :label=\"t('paramContent')\" label-width=\"80px\">\r\n                  <el-input v-model=\"iem.parameterContent\" :placeholder=\"t('enterPlaceholder')\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </el-form-item>\r\n      </template>\r\n\r\n      <el-form-item :label=\"t('remark')\">\r\n        <el-input v-model=\"formData.remark\" type=\"textarea\" :rows=\"4\" :placeholder=\"t('remark')\" />\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button @click=\"handleClose\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" :loading=\"saving\" @click=\"handleSave\">\r\n          {{ t('save') }}\r\n        </el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "dialogVisible", "computed", "get", "modelValue", "set", "value", "t", "useI18n", "userStore", "useUserStore", "formRef", "ref", "saving", "brandOptions", "conf", "formData", "deviceCode", "brandCode", "version", "remark", "state", "devRemark", "formRules", "required", "message", "trigger", "showDeviceDetails", "async", "fetchDoorLockDetails", "id", "res", "deviceApi", "getHotelDeviceById", "gcode", "hcode", "code", "data", "Array", "isArray", "map", "item", "parameterCode", "parameterName", "parameterContent", "error", "console", "ElMessage", "onChange", "deviceInfo", "then", "handleSave", "validate", "deviceType", "editingId", "updateHotelDevice", "createHotelDevice", "success", "handleClose", "onMounted", "listBrand", "label", "brandName", "deviceVerName", "fetchBrandOptions", "watch", "newId", "resetFields", "immediate"], "mappings": "g6BAyGA,MAAMA,EAAQC,EAURC,EAAQC,EAMRC,EAAgBC,EAAS,CAC7BC,IAAK,IAAMN,EAAMO,WACjBC,IAAMC,IACJP,EAAM,oBAAqBO,EAAK,KAG9BC,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,EAAUC,IACVC,EAASD,GAAI,GACbE,EAAeF,EAAwC,IACvDG,EAAOH,EAAkF,IACzFI,EAAWJ,EAAI,CACnBK,WAAY,GACZC,UAAW,GACXC,QAAS,GACTC,OAAQ,GACRC,MAAO,IACPC,UAAW,KAGPC,EAAY,CAChBN,WAAY,CAAC,CAAEO,UAAU,EAAMC,QAASlB,EAAE,qBAAsBmB,QAAS,SACzEP,QAAS,CAAC,CAAEK,UAAU,EAAMC,QAASlB,EAAE,sBAAuBmB,QAAS,UAEnEC,EAAoBf,GAAI,GAwB9BgB,eAAeC,EAAqBC,GAC9B,IACI,MAAAC,QAAYC,EAAUC,mBAAmB,CAC7CH,KACAI,MAAOzB,EAAUyB,MACjBC,MAAO1B,EAAU0B,QAGnB,GAAiB,IAAbJ,EAAIK,MAAcL,EAAIM,KAAM,CAC9B,MAAMA,EAAON,EAAIM,KAGjBrB,EAASV,MAAQ,CACfW,WAAYoB,EAAKpB,YAAc,GAC/BC,UAAWmB,EAAKnB,WAAa,GAC7BC,QAASkB,EAAKlB,SAAW,GACzBC,OAAQiB,EAAKjB,QAAU,GACvBC,MAAOgB,EAAKhB,OAAS,IACrBC,UAAWe,EAAKf,WAAa,IAI3Be,EAAKtB,MAAQuB,MAAMC,QAAQF,EAAKtB,QAClCA,EAAKT,MAAQ+B,EAAKtB,KAAKyB,KAAKC,IAAe,CACzCC,cAAeD,EAAKC,eAAiB,GACrCC,cAAeF,EAAKE,eAAiB,GACrCC,iBAAkBH,EAAKG,kBAAoB,QAI/CjB,EAAkBrB,OAAQ,CAAA,QAErBuC,GACCC,QAAAD,MAAM,qCAAsCA,GACpDE,EAAUF,MAAM,WAAU,CAC5B,CAoCF,SAASG,IACPhB,EACGiB,WAAW,CACVf,MAAOzB,EAAUyB,MACjBC,MAAO1B,EAAU0B,MACjBlB,WAAYD,EAASV,MAAMW,aAE5BiC,MAAMnB,IACY,IAAbA,EAAIK,OACGpB,EAAAV,MAAMY,UAAYa,EAAIM,KAAKnB,UAC3BF,EAAAV,MAAMa,QAAUY,EAAIM,KAAKlB,QAClCH,EAASV,MAAMc,OAASW,EAAIM,KAAKjB,QAAU,GAC3CJ,EAASV,MAAMgB,UAAYS,EAAIM,KAAKjB,QAAU,GAC1CW,EAAIM,KAAKtB,MAAQuB,MAAMC,QAAQR,EAAIM,KAAKtB,QAC1CA,EAAKT,MAAQyB,EAAIM,KAAKtB,KAAKyB,KAAKC,IAAe,CAC7CC,cAAeD,EAAKC,eAAiB,GACrCC,cAAeF,EAAKE,eAAiB,GACrCC,iBAAkBH,EAAKG,kBAAoB,QAG/CjB,EAAkBrB,OAAQ,EAAA,GAE7B,CAILsB,eAAeuB,IACT,GAACxC,EAAQL,MAIT,UACIK,EAAQL,MAAM8C,WACpBvC,EAAOP,OAAQ,EAEf,MAAM+B,EAAO,IACRrB,EAASV,MACZ4B,MAAOzB,EAAUyB,MACjBC,MAAO1B,EAAU0B,MACjBkB,WAAY,YACZtC,KAAMA,EAAKT,OAGT,IAAAyB,EAGIA,EAFJlC,EAAMyD,gBAEItB,EAAUuB,kBAAkB,IACnClB,EACHP,GAAIjC,EAAMyD,kBAIAtB,EAAUwB,kBAAkBnB,GAGzB,IAAbN,EAAIK,OACIW,EAAAU,QAAQlD,EAAE,gBACpBR,EAAM,kBAED8C,GACCC,QAAAD,MAAM,4BAA6BA,EAAK,CAChD,QACAhC,EAAOP,OAAQ,CAAA,CACjB,CAIF,SAASoD,IACP3D,EAAM,QAAO,QAlKf4D,GAAU/B,gBAiFVA,iBACQ,MAAAG,QAAYC,EAAU4B,UAAU,CACpC1B,MAAOzB,EAAUyB,MACjBC,MAAO1B,EAAU0B,QAEF,IAAbJ,EAAIK,OACNtB,EAAaR,MAAQyB,EAAIM,KAAKG,KAAKC,IAAe,CAChDoB,MAAO,GAAGpB,EAAKqB,aAAarB,EAAKsB,iBAAiBtB,EAAKtB,UACvDb,MAAOmC,EAAKxB,eAEhB,CA1FM+C,GAGFnE,EAAMyD,iBACFzB,EAAqBhC,EAAMyD,UAAS,IAI9CW,GACE,IAAMpE,EAAMyD,YACZ1B,MAAOsC,IACDA,QACIrC,EAAqBqC,IAkD/BlD,EAASV,MAAQ,CACfW,WAAY,GACZC,UAAW,GACXC,QAAS,GACTC,OAAQ,GACRC,MAAO,IACPC,UAAW,IAEbP,EAAKT,MAAQ,GACbqB,EAAkBrB,OAAQ,EAGtBK,EAAQL,OACVK,EAAQL,MAAM6D,cA5DF,GAGd,CAAEC,WAAW"}