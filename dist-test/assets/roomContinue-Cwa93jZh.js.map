{"version": 3, "file": "roomContinue-Cwa93jZh.js", "sources": ["../../src/views/room/realtime/components/roomContinue.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"dialogTitle\": \"Continue/Advance\",\r\n    \"labelRoomNumber\": \"Room\",\r\n    \"labelCheckinTime\": \"Check-in Time\",\r\n    \"labelPlanCheckoutTime\": \"Plan Check-out\",\r\n    \"labelTodayPrice\": \"Today's Price\",\r\n    \"tabContinue\": \"Continue\",\r\n    \"tabAdvance\": \"Advance\",\r\n    \"labelContinueDays\": \"Days in Continue\",\r\n    \"labelPlanCheckoutNewTime\": \"Expected C/O Time (New)\",\r\n    \"labelContinuePrice\": \"Continue Price\",\r\n    \"labelContinueWith\": \"Continue with\",\r\n    \"labelRemark\": \"Remark\",\r\n    \"labelAdvanceDays\": \"Days in Advance\",\r\n    \"placeholderRemark\": \"Remarks will be written to the order log\",\r\n    \"footerContinue\": \"Continue {durationOfStay} nights, total price ${todayPrice}\",\r\n    \"footerAdvance\": \"Advance {leadTimeNum} days\",\r\n    \"buttonCancel\": \"Cancel\",\r\n    \"buttonConfirm\": \"Confirm\",\r\n    \"messageContinueSuccess\": \"Room continued successfully\",\r\n    \"messageAdvanceSuccess\": \"Room advanced successfully\",\r\n    \"placeholderDays\": \"Number of days\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"dialogTitle\": \"续住/提前\",\r\n    \"labelRoomNumber\": \"房号\",\r\n    \"labelCheckinTime\": \"入住时间\",\r\n    \"labelPlanCheckoutTime\": \"预离时间\",\r\n    \"labelTodayPrice\": \"今日房价\",\r\n    \"tabContinue\": \"续住\",\r\n    \"tabAdvance\": \"提前\",\r\n    \"labelContinueDays\": \"续住天数\",\r\n    \"labelPlanCheckoutNewTime\": \"预离时间(新)\",\r\n    \"labelContinuePrice\": \"续住价格\",\r\n    \"labelContinueWith\": \"一同续住\",\r\n    \"labelRemark\": \"信息备注\",\r\n    \"labelAdvanceDays\": \"提前天数\",\r\n    \"placeholderRemark\": \"备注内容会写入订单日志\",\r\n    \"footerContinue\": \"续住 {durationOfStay} 晚，总价￥{todayPrice}\",\r\n    \"footerAdvance\": \"提前 {leadTimeNum} 天\",\r\n    \"buttonCancel\": \"取消\",\r\n    \"buttonConfirm\": \"确定\",\r\n    \"messageContinueSuccess\": \"房间续住成功\",\r\n    \"messageAdvanceSuccess\": \"房间提前成功\",\r\n    \"placeholderDays\": \"天数\"\r\n  },\r\n  \"km\": {\r\n    \"dialogTitle\": \"បន្តរស់/ចូលមុន\",\r\n    \"labelRoomNumber\": \"បន្ទប់\",\r\n    \"labelCheckinTime\": \"ពេលចូលស្នាក់\",\r\n    \"labelPlanCheckoutTime\": \"ពេលចាកចេញគ្រោង\",\r\n    \"labelTodayPrice\": \"តម្លៃថ្ងៃនេះ\",\r\n    \"tabContinue\": \"បន្តរស់\",\r\n    \"tabAdvance\": \"ចូលមុន\",\r\n    \"labelContinueDays\": \"ថ្ងៃបន្តរស់\",\r\n    \"labelPlanCheckoutNewTime\": \"ពេលចាកចេញគ្រោង(ថ្មី)\",\r\n    \"labelContinuePrice\": \"តម្លៃបន្តរស់\",\r\n    \"labelContinueWith\": \"បន្តរស់ជាមួយ\",\r\n    \"labelRemark\": \"កំណត់ចំណាំ\",\r\n    \"labelAdvanceDays\": \"ថ្ងៃចូលមុន\",\r\n    \"placeholderRemark\": \"កំណត់ចំណាំនឹងត្រូវបានសរសេរទៅក្នុងកំណត់ហេតុការបញ្ជាទិញ\",\r\n    \"footerContinue\": \"បន្តរស់ {durationOfStay} យប់ តម្លៃសរុប ${todayPrice}\",\r\n    \"footerAdvance\": \"ចូលមុន {leadTimeNum} ថ្ងៃ\",\r\n    \"buttonCancel\": \"បោះបង់\",\r\n    \"buttonConfirm\": \"បញ្ជាក់\",\r\n    \"messageContinueSuccess\": \"បន្ទប់បន្តរស់ដោយជោគជ័យ\",\r\n    \"messageAdvanceSuccess\": \"បន្ទប់ចូលមុនដោយជោគជ័យ\",\r\n    \"placeholderDays\": \"ចំនួនថ្ងៃ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel } from '@/models/index'\r\nimport type { FormInstance } from 'element-plus'\r\nimport { dictDataApi, generalConfigApi, orderApi } from '@/api/modules/index'\r\nimport { DICT_TYPE_ROOM_CLEAN_TASK } from '@/models/dict/constants'\r\nimport { CheckinType, DictTypeEnum, OrderState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport ymdate, { ymdateHMS } from '@/utils/timeutils'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    orderNo: string | number\r\n    orderType: string\r\n    orderTogetherCode?: string\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    orderNo: '',\r\n    orderType: '',\r\n    orderTogetherCode: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst formRef = ref<FormInstance>()\r\n/** 是否自动创建任务 */\r\nconst isAutoTask = ref()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  rNo: '',\r\n  orderNo: props.orderNo,\r\n  checkinType: '',\r\n  /** 续住信息 */\r\n  continue: {\r\n    days: 1, // 续住天数\r\n    /** 入住时间 */\r\n    checkinTime: '',\r\n    /**\r\n     * 预离时间\r\n     */\r\n    planCheckoutTime: '',\r\n    /** 价格 */\r\n    prices: [] as {\r\n      /** 日期 */\r\n      date: string\r\n      /** 原价 */\r\n      price: number\r\n      /** 修改后价格 */\r\n      vipPrice: number\r\n    }[],\r\n    /** 备注 */\r\n    remark: '',\r\n    /** 续住价格类型 */\r\n    continuePriceType: 'last',\r\n    /** 房价 */\r\n    continuePrice: 0,\r\n    /** 续住集合 */\r\n    LivingList: [] as string[],\r\n    /** 预离时间(新) */\r\n    planCheckoutNewTime: dayjs().format('YYYY-MM-DD HH:mm'),\r\n    /** 预离时间(新)时分 */\r\n    outTime: dayjs().format('YYYY-MM-DD HH:mm'),\r\n  },\r\n  /** 提前信息 */\r\n  ahead: {\r\n    days: 1, // 提前天数\r\n    /**\r\n     * 新计划离店时间\r\n     */\r\n    planCheckoutTime: '',\r\n    /** 备注 */\r\n    remark: '',\r\n    /** 预离时间(新) */\r\n    planLeadNewTime: dayjs().format('YYYY-MM-DD HH:mm'),\r\n    /** 预离时间(新)时分 */\r\n    LeadTime: dayjs().format('YYYY-MM-DD HH:mm'),\r\n    /** 提前天数 */\r\n    leadTimeNum: 1,\r\n  },\r\n  /** 当前订单信息 */\r\n  order: {\r\n    orderNo: '',\r\n    /** 渠道代码 */\r\n    channelCode: '',\r\n    /** 房型代码 */\r\n    rtCode: '',\r\n    rtName: '',\r\n    guestName: '',\r\n    checkinTime: '',\r\n    planCheckoutTime: '',\r\n    prices: [] as {\r\n      /** 房型代码 */\r\n      rtCode: string\r\n      /** 房间代码 */\r\n      rCode: string\r\n      /** 房号 */\r\n      rNo: string\r\n      /** 原始价格 */\r\n      price: number\r\n      /** 调整后的价格 */\r\n      vipPrice: number\r\n      /** 价格类型 0 放盘价 1 手工价 */\r\n      priceType: string\r\n      /** 价格开始时间 */\r\n      priceStartTime: string\r\n      /** 价格结束时间 */\r\n      priceEndTime: string\r\n    }[],\r\n  },\r\n  /** 续住/提前类型 1续住 2 提前 */\r\n  handleType: '1',\r\n})\r\n\r\nonMounted(() => {\r\n  getRoomData()\r\n  getConstants()\r\n  getAccountList()\r\n  getRoomCleanType()\r\n})\r\n\r\nconst continueLivingList = ref<{ orderNo: string; togetherCode: string; label: string }[]>([])\r\n/** 获取字典类型 */\r\nasync function getRoomCleanType() {\r\n  const params = { gcode: userStore.gcode, types: [DICT_TYPE_ROOM_CLEAN_TASK].join(',') }\r\n  const { data } = await generalConfigApi.list(params)\r\n  data.forEach((item) => {\r\n    if (item.code == OrderState.CONTINUE) {\r\n      isAutoTask.value = item.value\r\n    }\r\n  })\r\n}\r\nfunction getRoomData() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    orderNo: props.orderNo,\r\n    togetherCode: props.orderTogetherCode,\r\n  }\r\n  orderApi.getOrderDtail(params).then((res: any) => {\r\n    form.value.orderNo = res.data.order.orderNo\r\n    form.value.order.rtCode = res.data.order.rtCode\r\n    form.value.rNo = res.data.order.rNo\r\n    form.value.order.rtCode = res.data.order.rtCode\r\n    form.value.continue.checkinTime = res.data.orderTogether.checkinTime\r\n    form.value.continue.planCheckoutTime = res.data.orderTogether.planCheckoutTime\r\n    form.value.ahead.planCheckoutTime = res.data.orderTogether.planCheckoutTime\r\n    form.value.ahead.planLeadNewTime =\r\n      res.data.order.checkinType === CheckinType.HOUR_ROOM ? dayjs(res.data.orderTogether.planCheckoutTime).format('YYYY-MM-DD HH:mm') : dayjs(res.data.orderTogether.planCheckoutTime).add(-1, 'day').format('YYYY-MM-DD HH:mm')\r\n    form.value.continue.planCheckoutNewTime =\r\n      res.data.order.checkinType === CheckinType.HOUR_ROOM ? dayjs(res.data.orderTogether.planCheckoutTime).format('YYYY-MM-DD HH:mm') : dayjs(res.data.orderTogether.planCheckoutTime).add(1, 'day').format('YYYY-MM-DD HH:mm')\r\n    form.value.continue.outTime = res.data.orderTogether.planCheckoutTime\r\n    form.value.ahead.LeadTime = res.data.orderTogether.planCheckoutTime\r\n    // 房型\r\n    form.value.checkinType = res.data.order.checkinType\r\n    if (form.value.order.rtCode) {\r\n      getcontinueDataPrice(form.value.continue.continuePriceType)\r\n    }\r\n  })\r\n}\r\n\r\n// 续住列表\r\nfunction getAccountList() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    no: props.orderNo,\r\n    noType: props.orderType,\r\n  }\r\n  orderApi.relationList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      // 转换用户列表数据\r\n      continueLivingList.value = transformData(res.data.orders)\r\n      if (continueLivingList.value) {\r\n        continueLivingList.value.forEach((item) => {\r\n          form.value.continue.LivingList.push(item.togetherCode)\r\n        })\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\n/** 转换用户列表数据 */\r\nfunction transformData(data: any): any[] {\r\n  return data.flatMap((order: any) => {\r\n    if (order.state === OrderState.CHECK_IN && order.orderNo === props.orderNo) {\r\n      return order.guests\r\n        .filter((guest: any) => guest.state === OrderState.CHECK_IN && guest.togetherCode !== props.orderTogetherCode)\r\n        .map((guest: any) => ({\r\n          orderNo: order.orderNo,\r\n          togetherCode: guest.togetherCode,\r\n          label: guest.name,\r\n        }))\r\n    }\r\n    return []\r\n  })\r\n}\r\n\r\nconst continuePrice = ref<DictDataModel[]>([])\r\n/** 常量里包括多个 */\r\nconst dictTypes = [DictTypeEnum.CONTINUE_PRICE]\r\n// 获取续住价格类型\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    continuePrice.value = res.data.filter((item: any) => item.dictType === DictTypeEnum.CONTINUE_PRICE)\r\n  })\r\n}\r\n\r\nfunction getcontinueDataPrice(value: string) {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    orderNo: props.orderNo,\r\n    rtCode: form.value.order.rtCode,\r\n    continuePriceType: value,\r\n  }\r\n  orderApi.continueDataPrice(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      form.value.continue.continuePrice = res.data.vipPrice\r\n    }\r\n  })\r\n}\r\n/** 提交续住 */\r\nfunction onSubmit() {\r\n  // 提交先过滤当前宾客代码\r\n  if (props.orderTogetherCode) {\r\n    form.value.continue.LivingList = form.value.continue.LivingList.filter((code) => code !== props.orderTogetherCode)\r\n  }\r\n  if (form.value.handleType === '1') {\r\n    // 加上当前宾客代码\r\n    form.value.continue.LivingList.push(props.orderTogetherCode)\r\n    const params = {\r\n      isAutoTask: isAutoTask.value,\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      orderNo: props.orderNo,\r\n      continuePriceType: form.value.continue.continuePriceType,\r\n      planCheckoutTime: `${ymdate(form.value.continue.planCheckoutNewTime)} ${ymdateHMS(form.value.continue.outTime).substring(11, 16)}`,\r\n      togetherCodes: form.value.continue.LivingList,\r\n    }\r\n    orderApi.continueIn(params).then((res: any) => {\r\n      if (res.code == 0) {\r\n        ElMessage.success(t('messageContinueSuccess'))\r\n        emits('success')\r\n        onCancel()\r\n      }\r\n    })\r\n  } else {\r\n    // 加上当前宾客代码\r\n    form.value.continue.LivingList.push(props.orderTogetherCode)\r\n    const params = {\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      orderNo: props.orderNo,\r\n      planCheckoutTime: `${ymdate(form.value.ahead.planLeadNewTime)} ${ymdateHMS(form.value.ahead.LeadTime).substring(11, 16)}`,\r\n      togetherCodes: form.value.continue.LivingList,\r\n    }\r\n    orderApi.advanceOut(params).then((res: any) => {\r\n      if (res.code == 0) {\r\n        ElMessage.success(t('messageAdvanceSuccess'))\r\n        emits('success')\r\n        onCancel()\r\n      }\r\n    })\r\n  }\r\n}\r\n\r\n/** 续住天数 */\r\nfunction updateCheckoutTime() {\r\n  if (form.value.continue.days > 0) {\r\n    form.value.continue.planCheckoutNewTime = dayjs(form.value.continue.planCheckoutTime).add(form.value.continue.days, 'day').format('YYYY-MM-DD HH:mm')\r\n  }\r\n}\r\n/** 续住日期 */\r\nfunction updateCheckoutDays() {\r\n  const checkInDate = dayjs(ymdate(form.value.continue.planCheckoutTime))\r\n  const checkOutDate = dayjs(ymdate(form.value.continue.planCheckoutNewTime))\r\n  form.value.continue.days = Math.abs(checkInDate.diff(checkOutDate, 'days'))\r\n}\r\n\r\n/** 提前天数 */\r\nfunction updateLeadTime() {\r\n  if (form.value.ahead.days > 0) {\r\n    form.value.ahead.planLeadNewTime = dayjs(form.value.ahead.planCheckoutTime).add(-form.value.ahead.days, 'day').format('YYYY-MM-DD HH:mm')\r\n  }\r\n}\r\n/** 提前日期 */\r\nfunction updateLeadDays() {\r\n  const checkInDate = dayjs(ymdate(form.value.ahead.planCheckoutTime))\r\n  const checkOutDate = dayjs(ymdate(form.value.ahead.planLeadNewTime))\r\n  form.value.ahead.days = Math.abs(checkInDate.diff(checkOutDate, 'days'))\r\n}\r\n\r\n/** 今日房价总计 */\r\nconst todayPrice = computed(() => {\r\n  const total = form.value.continue.days * form.value.continue.continuePrice\r\n  return Math.round(total * 100) / 100 // 保留两位小数\r\n})\r\n\r\nfunction handChange(value: any) {\r\n  if (value === 'custom') {\r\n    getcontinueDataPrice('baseprice')\r\n  } else {\r\n    getcontinueDataPrice(value)\r\n  }\r\n}\r\nconst clearable = ref(false)\r\nfunction disabledCheckoutDate(time: any) {\r\n  if (form.value.continue.planCheckoutTime) {\r\n    const preDepartureTime = new Date(form.value.continue.planCheckoutTime)\r\n    const preDepartureTimestamp = preDepartureTime.getTime()\r\n    return time.getTime() < preDepartureTimestamp\r\n  }\r\n}\r\nfunction disabledLeadDate(time: any) {\r\n  if (form.value.ahead.planCheckoutTime) {\r\n    const preDepartureTime = new Date(form.value.ahead.planCheckoutTime)\r\n    const preDepartureTimestamp = preDepartureTime.getTime()\r\n    return time.getTime() > preDepartureTimestamp\r\n  }\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\n// 小时范围选择\r\nfunction disabledHours() {\r\n  const list = []\r\n  if (form.value.checkinType === CheckinType.HOUR_ROOM) {\r\n    const nowTime = new Date().getHours()\r\n    let startH: any = dayjs(form.value.continue.outTime).format('H')\r\n    if (nowTime > startH) {\r\n      startH = nowTime\r\n    }\r\n    for (let i = 0; i < 24; i++) {\r\n      if (i >= startH) {\r\n        continue\r\n      }\r\n      list.push(i)\r\n    }\r\n  }\r\n  return list\r\n}\r\n// 分钟范围选择\r\nfunction disabledMinutes(hour: number, role: string, comparingDate?: any) {\r\n  const list = []\r\n  let fage = false\r\n  if (form.value.checkinType === CheckinType.HOUR_ROOM) {\r\n    const nowTime = new Date().getHours()\r\n    let startH: any = dayjs(form.value.continue.outTime).format('H')\r\n    if (nowTime > startH) {\r\n      startH = nowTime\r\n      fage = true\r\n    }\r\n    if (hour === startH) {\r\n      for (let i = 0; i < 60; i++) {\r\n        if ((fage ? new Date().getMinutes() : new Date(form.value.continue.outTime).getMinutes()) <= i) {\r\n          continue\r\n        }\r\n        list.push(i)\r\n      }\r\n    }\r\n  }\r\n  return list\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('dialogTitle')\" width=\"900px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"form\" label-width=\"140px\" label-suffix=\":\">\r\n      <div class=\"flex justify-between\">\r\n        <div class=\"flex\">\r\n          <el-form-item :label=\"t('labelRoomNumber')\" label-width=\"65px\">\r\n            {{ form.rNo }}\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('labelCheckinTime')\">\r\n            {{ form.continue.checkinTime }}\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('labelPlanCheckoutTime')\">\r\n            {{ form.continue.planCheckoutTime }}\r\n          </el-form-item>\r\n          <div>\r\n            <el-form-item :label=\"t('labelTodayPrice')\">\r\n              <span class=\"text-red font-bold\">￥{{ form.continue.continuePrice }}</span>\r\n            </el-form-item>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-tabs v-model=\"form.handleType\">\r\n        <el-tab-pane :label=\"t('tabContinue')\" name=\"1\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item :label=\"t('labelContinueDays')\">\r\n                <el-input-number\r\n                  v-model=\"form.continue.days\"\r\n                  :disabled=\"form.checkinType === CheckinType.HOUR_ROOM\"\r\n                  style=\"width: 180px\"\r\n                  :min=\"1\"\r\n                  :step=\"1\"\r\n                  :value-on-clear=\"1\"\r\n                  :precision=\"0\"\r\n                  :placeholder=\"t('labelContinueDays')\"\r\n                  @change=\"updateCheckoutTime\"\r\n                  @input=\"updateCheckoutTime\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item :label=\"t('labelPlanCheckoutNewTime')\">\r\n                <el-date-picker\r\n                  v-model=\"form.continue.planCheckoutNewTime\"\r\n                  :disabled=\"form.checkinType === CheckinType.HOUR_ROOM\"\r\n                  :disabled-date=\"disabledCheckoutDate\"\r\n                  :clearable=\"clearable\"\r\n                  :placeholder=\"t('labelPlanCheckoutNewTime')\"\r\n                  style=\"width: 150px\"\r\n                  @change=\"updateCheckoutDays\"\r\n                />\r\n                <el-time-picker v-model=\"form.continue.outTime\" :disabled-hours=\"disabledHours\" :disabled-minutes=\"disabledMinutes\" :clearable=\"clearable\" placeholder=\"Arbitrary time\" format=\"HH:mm\" style=\"width: 100px; margin-left: 10px\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row v-if=\"form.checkinType !== CheckinType.HOUR_ROOM\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item :label=\"t('labelContinuePrice')\">\r\n                <el-select v-model=\"form.continue.continuePriceType\" style=\"width: 200px\" @change=\"handChange\">\r\n                  <el-option v-for=\"item in continuePrice\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col v-if=\"continueLivingList.length\" :span=\"24\">\r\n              <el-form-item :label=\"t('labelContinueWith')\">\r\n                <el-checkbox-group v-model=\"form.continue.LivingList\">\r\n                  <el-checkbox v-for=\"item in continueLivingList\" :key=\"item.togetherCode\" :label=\"item.label\" :value=\"item.togetherCode\" />\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              <el-form-item :label=\"t('labelRemark')\">\r\n                <el-input v-model=\"form.continue.remark\" type=\"textarea\" :placeholder=\"t('placeholderRemark')\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n        <el-tab-pane v-if=\"form.checkinType !== CheckinType.HOUR_ROOM\" :label=\"t('tabAdvance')\" name=\"2\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item :label=\"t('labelAdvanceDays')\" prop=\"days\">\r\n                <el-input-number v-model=\"form.ahead.days\" style=\"width: 200px\" :min=\"1\" :step=\"1\" :value-on-clear=\"1\" :precision=\"0\" :placeholder=\"t('labelAdvanceDays')\" @change=\"updateLeadTime\" @input=\"updateLeadTime\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item :label=\"t('labelPlanCheckoutNewTime')\">\r\n                <el-date-picker v-model=\"form.ahead.planLeadNewTime\" :disabled-date=\"disabledLeadDate\" :clearable=\"clearable\" :placeholder=\"t('labelPlanCheckoutNewTime')\" style=\"width: 150px\" @change=\"updateLeadDays\" @input=\"updateLeadDays\" />\r\n                <el-time-picker v-model=\"form.ahead.LeadTime\" :clearable=\"clearable\" placeholder=\"Arbitrary time\" format=\"HH:mm\" style=\"width: 100px; margin-left: 10px\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col v-if=\"continueLivingList.length\" :span=\"24\">\r\n              <el-form-item :label=\"t('labelContinueWith')\">\r\n                <el-checkbox-group v-model=\"form.continue.LivingList\">\r\n                  <el-checkbox v-for=\"item in continueLivingList\" :key=\"item.togetherCode\" :label=\"item.label\" :value=\"item.togetherCode\" />\r\n                </el-checkbox-group>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              <el-form-item :label=\"t('labelRemark')\">\r\n                <el-input v-model=\"form.ahead.remark\" type=\"textarea\" maxlength=\"250\" :placeholder=\"t('placeholderRemark')\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-form>\r\n    <!--    <el-row v-else type=\"flex\" justify=\"center\"> -->\r\n    <!--      <el-result -->\r\n    <!--        icon=\"success\" title=\"续住/提前成功\" -->\r\n    <!--        :sub-title=\"form.handleType === '1' ? `续住${form.continue.days}晚，共计￥${totalPrice}` : `提前${form.ahead.days}天离店`\" -->\r\n    <!--      > -->\r\n    <!--        <template #extra> -->\r\n    <!--          <el-form-item label=\"房型/房号\"> -->\r\n    <!--            {{ form.order.rtName }}-{{ props.rNo }} -->\r\n    <!--          </el-form-item> -->\r\n    <!--          <el-form-item label=\"入住人\"> -->\r\n    <!--            {{ form.order.guestName }} -->\r\n    <!--          </el-form-item> -->\r\n    <!--          <el-form-item label=\"离店时间\"> -->\r\n    <!--            <span v-if=\"form.handleType === '1'\">{{ dayjs(form.continue.planCheckoutTime).format('YYYY-MM-DD HH:mm') -->\r\n    <!--            }}</span> -->\r\n    <!--            <span v-else>{{ dayjs(form.ahead.planCheckoutTime).format('YYYY-MM-DD HH:mm') }}</span> -->\r\n    <!--          </el-form-item> -->\r\n    <!--          <el-button type=\"primary\" plain @click=\"\"> -->\r\n    <!--            制卡 -->\r\n    <!--          </el-button> -->\r\n    <!--        </template> -->\r\n    <!--      </el-result> -->\r\n    <!--    </el-row> -->\r\n    <template #footer>\r\n      <div class=\"flex-between\">\r\n        <div>\r\n          <span v-if=\"form.handleType === '1'\">\r\n            {{\r\n              t('footerContinue', {\r\n                durationOfStay: form.continue.days,\r\n                todayPrice,\r\n              })\r\n            }}\r\n          </span>\r\n          <span v-else>\r\n            {{ t('footerAdvance', { leadTimeNum: form.ahead.leadTimeNum }) }}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <el-checkbox v-model=\"isAutoTask\" true-value=\"1\" false-value=\"0\" label=\"下发客房清扫任务\" />\r\n          <el-button class=\"ml-20px\" @click=\"onCancel\">\r\n            {{ t('buttonCancel') }}\r\n          </el-button>\r\n          <el-button type=\"primary\" @click=\"onSubmit\">\r\n            {{ t('buttonConfirm') }}\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "formRef", "ref", "isAutoTask", "myVisible", "computed", "get", "modelValue", "set", "val", "form", "gcode", "hcode", "rNo", "orderNo", "checkinType", "continue", "days", "checkinTime", "planCheckoutTime", "prices", "remark", "continuePriceType", "continuePrice", "LivingList", "planCheckoutNewTime", "dayjs", "format", "outTime", "ahead", "planLeadNewTime", "LeadTime", "leadTimeNum", "order", "channelCode", "rtCode", "rtName", "<PERSON><PERSON><PERSON>", "handleType", "onMounted", "params", "togetherCode", "orderTogetherCode", "orderApi", "getOrderDtail", "then", "res", "value", "data", "orderTogether", "CheckinType", "HOUR_ROOM", "add", "getcontinueDataPrice", "getRoomData", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "filter", "item", "dictType", "DictTypeEnum", "CONTINUE_PRICE", "no", "noType", "orderType", "relationList", "code", "continueLivingList", "orders", "flatMap", "state", "OrderState", "CHECK_IN", "guests", "guest", "map", "label", "name", "for<PERSON>ach", "push", "getAccountList", "async", "types", "DICT_TYPE_ROOM_CLEAN_TASK", "join", "generalConfigApi", "list", "CONTINUE", "getRoomCleanType", "continueDataPrice", "vipPrice", "onSubmit", "ymdate", "ymdateHMS", "substring", "togetherCodes", "continueIn", "ElMessage", "success", "onCancel", "advanceOut", "updateCheckoutTime", "updateCheckoutDays", "checkInDate", "checkOutDate", "Math", "abs", "diff", "updateLeadTime", "updateLeadDays", "todayPrice", "total", "round", "handChange", "clearable", "disabledCheckoutDate", "time", "preDepartureTimestamp", "Date", "getTime", "disabledLeadDate", "disabledHours", "nowTime", "getHours", "startH", "i", "disabledMinutes", "hour", "role", "comparingDate", "fage", "getMinutes"], "mappings": "8jDAoFA,MAAMA,EAAQC,EAeRC,GAAQC,GAKRC,EAAEA,IAAMC,IAERC,GAAYC,IACZC,GAAUC,IAEVC,GAAaD,IACbE,GAAYC,EAAS,CACzBC,IAAM,IACGb,EAAMc,WAEf,GAAAC,CAAIC,GACFd,GAAM,oBAAqBc,EAAG,IAI5BC,GAAOR,EAAI,CACfS,MAAOZ,GAAUY,MACjBC,MAAOb,GAAUa,MACjBC,IAAK,GACLC,QAASrB,EAAMqB,QACfC,YAAa,GAEbC,SAAU,CACRC,KAAM,EAENC,YAAa,GAIbC,iBAAkB,GAElBC,OAAQ,GASRC,OAAQ,GAERC,kBAAmB,OAEnBC,cAAe,EAEfC,WAAY,GAEZC,oBAAqBC,IAAQC,OAAO,oBAEpCC,QAASF,IAAQC,OAAO,qBAG1BE,MAAO,CACLZ,KAAM,EAINE,iBAAkB,GAElBE,OAAQ,GAERS,gBAAiBJ,IAAQC,OAAO,oBAEhCI,SAAUL,IAAQC,OAAO,oBAEzBK,YAAa,GAGfC,MAAO,CACLnB,QAAS,GAEToB,YAAa,GAEbC,OAAQ,GACRC,OAAQ,GACRC,UAAW,GACXnB,YAAa,GACbC,iBAAkB,GAClBC,OAAQ,IAoBVkB,WAAY,MAGdC,GAAU,MAkBV,WACE,MAAMC,EAAS,CACb7B,MAAOZ,GAAUY,MACjBC,MAAOb,GAAUa,MACjBE,QAASrB,EAAMqB,QACf2B,aAAchD,EAAMiD,mBAEtBC,EAASC,cAAcJ,GAAQK,MAAMC,IACnCpC,GAAKqC,MAAMjC,QAAUgC,EAAIE,KAAKf,MAAMnB,QACpCJ,GAAKqC,MAAMd,MAAME,OAASW,EAAIE,KAAKf,MAAME,OACzCzB,GAAKqC,MAAMlC,IAAMiC,EAAIE,KAAKf,MAAMpB,IAChCH,GAAKqC,MAAMd,MAAME,OAASW,EAAIE,KAAKf,MAAME,OACzCzB,GAAKqC,MAAM/B,SAASE,YAAc4B,EAAIE,KAAKC,cAAc/B,YACzDR,GAAKqC,MAAM/B,SAASG,iBAAmB2B,EAAIE,KAAKC,cAAc9B,iBAC9DT,GAAKqC,MAAMlB,MAAMV,iBAAmB2B,EAAIE,KAAKC,cAAc9B,iBAC3DT,GAAKqC,MAAMlB,MAAMC,gBACfgB,EAAIE,KAAKf,MAAMlB,cAAgBmC,EAAYC,UAAYzB,EAAMoB,EAAIE,KAAKC,cAAc9B,kBAAkBQ,OAAO,oBAAsBD,EAAMoB,EAAIE,KAAKC,cAAc9B,kBAAkBiC,KAAI,EAAI,OAAOzB,OAAO,oBAC1MjB,GAAKqC,MAAM/B,SAASS,oBAClBqB,EAAIE,KAAKf,MAAMlB,cAAgBmC,EAAYC,UAAYzB,EAAMoB,EAAIE,KAAKC,cAAc9B,kBAAkBQ,OAAO,oBAAsBD,EAAMoB,EAAIE,KAAKC,cAAc9B,kBAAkBiC,IAAI,EAAG,OAAOzB,OAAO,oBACzMjB,GAAKqC,MAAM/B,SAASY,QAAUkB,EAAIE,KAAKC,cAAc9B,iBACrDT,GAAKqC,MAAMlB,MAAME,SAAWe,EAAIE,KAAKC,cAAc9B,iBAEnDT,GAAKqC,MAAMhC,YAAc+B,EAAIE,KAAKf,MAAMlB,YACpCL,GAAKqC,MAAMd,MAAME,QACEkB,GAAA3C,GAAKqC,MAAM/B,SAASM,kBAAiB,GAE7D,CA3CWgC,GAwFZC,EAAYC,iBAAiBC,IAAWZ,MAAMC,IAC9BvB,GAAAwB,MAAQD,EAAIE,KAAKU,QAAQC,GAAcA,EAAKC,WAAaC,EAAaC,gBAAc,IA1CtG,WACE,MAAMtB,EAAS,CACb7B,MAAOZ,GAAUY,MACjBC,MAAOb,GAAUa,MACjBmD,GAAItE,EAAMqB,QACVkD,OAAQvE,EAAMwE,WAEhBtB,EAASuB,aAAa1B,GAAQK,MAAMC,IACjB,IAAbA,EAAIqB,OAENC,GAAmBrB,MAAsBD,EAAIE,KAAKqB,OAY1CC,SAASrC,GACfA,EAAMsC,QAAUC,EAAWC,UAAYxC,EAAMnB,UAAYrB,EAAMqB,QAC1DmB,EAAMyC,OACVhB,QAAQiB,GAAeA,EAAMJ,QAAUC,EAAWC,UAAYE,EAAMlC,eAAiBhD,EAAMiD,oBAC3FkC,KAAKD,IAAgB,CACpB7D,QAASmB,EAAMnB,QACf2B,aAAckC,EAAMlC,aACpBoC,MAAOF,EAAMG,SAGZ,KArBDV,GAAmBrB,OACFqB,GAAArB,MAAMgC,SAASpB,IAChCjD,GAAKqC,MAAM/B,SAASQ,WAAWwD,KAAKrB,EAAKlB,aAAY,IAEzD,GAEH,CA9DcwC,GAMjBC,iBACQ,MAAA1C,EAAS,CAAE7B,MAAOZ,GAAUY,MAAOwE,MAAO,CAACC,GAA2BC,KAAK,OAC3ErC,KAAEA,SAAesC,EAAiBC,KAAK/C,GACxCQ,EAAA+B,SAASpB,IACRA,EAAKQ,MAAQK,EAAWgB,WAC1BrF,GAAW4C,MAAQY,EAAKZ,MAAA,GAE3B,CAZgB0C,EAAA,IAGb,MAAArB,GAAqBlE,EAAgE,IA6ErF,MAAAqB,GAAgBrB,EAAqB,IAErCuD,GAAY,CAACI,EAAaC,gBAQhC,SAAST,GAAqBN,GAC5B,MAAMP,EAAS,CACb7B,MAAOZ,GAAUY,MACjBC,MAAOb,GAAUa,MACjBE,QAASrB,EAAMqB,QACfqB,OAAQzB,GAAKqC,MAAMd,MAAME,OACzBb,kBAAmByB,GAErBJ,EAAS+C,kBAAkBlD,GAAQK,MAAMC,IACtB,IAAbA,EAAIqB,OACNzD,GAAKqC,MAAM/B,SAASO,cAAgBuB,EAAIE,KAAK2C,SAAA,GAEhD,CAGH,SAASC,KAKH,GAHAnG,EAAMiD,oBACRhC,GAAKqC,MAAM/B,SAASQ,WAAad,GAAKqC,MAAM/B,SAASQ,WAAWkC,QAAQS,GAASA,IAAS1E,EAAMiD,qBAEpE,MAA1BhC,GAAKqC,MAAMT,WAAoB,CAEjC5B,GAAKqC,MAAM/B,SAASQ,WAAWwD,KAAKvF,EAAMiD,mBAC1C,MAAMF,EAAS,CACbrC,WAAYA,GAAW4C,MACvBpC,MAAOZ,GAAUY,MACjBC,MAAOb,GAAUa,MACjBE,QAASrB,EAAMqB,QACfQ,kBAAmBZ,GAAKqC,MAAM/B,SAASM,kBACvCH,iBAAkB,GAAG0E,EAAOnF,GAAKqC,MAAM/B,SAASS,wBAAwBqE,EAAUpF,GAAKqC,MAAM/B,SAASY,SAASmE,UAAU,GAAI,MAC7HC,cAAetF,GAAKqC,MAAM/B,SAASQ,YAErCmB,EAASsD,WAAWzD,GAAQK,MAAMC,IAChB,GAAZA,EAAIqB,OACI+B,EAAAC,QAAQtG,GAAE,2BACpBF,GAAM,WACGyG,KAAA,GAEZ,KACI,CAEL1F,GAAKqC,MAAM/B,SAASQ,WAAWwD,KAAKvF,EAAMiD,mBAC1C,MAAMF,EAAS,CACb7B,MAAOZ,GAAUY,MACjBC,MAAOb,GAAUa,MACjBE,QAASrB,EAAMqB,QACfK,iBAAkB,GAAG0E,EAAOnF,GAAKqC,MAAMlB,MAAMC,oBAAoBgE,EAAUpF,GAAKqC,MAAMlB,MAAME,UAAUgE,UAAU,GAAI,MACpHC,cAAetF,GAAKqC,MAAM/B,SAASQ,YAErCmB,EAAS0D,WAAW7D,GAAQK,MAAMC,IAChB,GAAZA,EAAIqB,OACI+B,EAAAC,QAAQtG,GAAE,0BACpBF,GAAM,WACGyG,KAAA,GAEZ,CACH,CAIF,SAASE,KACH5F,GAAKqC,MAAM/B,SAASC,KAAO,IAC7BP,GAAKqC,MAAM/B,SAASS,oBAAsBC,EAAMhB,GAAKqC,MAAM/B,SAASG,kBAAkBiC,IAAI1C,GAAKqC,MAAM/B,SAASC,KAAM,OAAOU,OAAO,oBACpI,CAGF,SAAS4E,KACP,MAAMC,EAAc9E,EAAMmE,EAAOnF,GAAKqC,MAAM/B,SAASG,mBAC/CsF,EAAe/E,EAAMmE,EAAOnF,GAAKqC,MAAM/B,SAASS,sBACjDf,GAAAqC,MAAM/B,SAASC,KAAOyF,KAAKC,IAAIH,EAAYI,KAAKH,EAAc,QAAO,CAI5E,SAASI,KACHnG,GAAKqC,MAAMlB,MAAMZ,KAAO,IAC1BP,GAAKqC,MAAMlB,MAAMC,gBAAkBJ,EAAMhB,GAAKqC,MAAMlB,MAAMV,kBAAkBiC,KAAK1C,GAAKqC,MAAMlB,MAAMZ,KAAM,OAAOU,OAAO,oBACxH,CAGF,SAASmF,KACP,MAAMN,EAAc9E,EAAMmE,EAAOnF,GAAKqC,MAAMlB,MAAMV,mBAC5CsF,EAAe/E,EAAMmE,EAAOnF,GAAKqC,MAAMlB,MAAMC,kBAC9CpB,GAAAqC,MAAMlB,MAAMZ,KAAOyF,KAAKC,IAAIH,EAAYI,KAAKH,EAAc,QAAO,CAInE,MAAAM,GAAa1G,GAAS,KAC1B,MAAM2G,EAAQtG,GAAKqC,MAAM/B,SAASC,KAAOP,GAAKqC,MAAM/B,SAASO,cAC7D,OAAOmF,KAAKO,MAAc,IAARD,GAAe,GAAA,IAGnC,SAASE,GAAWnE,GAEhBM,GADY,WAAVN,EACmB,YAEAA,EACvB,CAEI,MAAAoE,GAAYjH,GAAI,GACtB,SAASkH,GAAqBC,GACxB,GAAA3G,GAAKqC,MAAM/B,SAASG,iBAAkB,CACxC,MACMmG,EADmB,IAAIC,KAAK7G,GAAKqC,MAAM/B,SAASG,kBACPqG,UACxC,OAAAH,EAAKG,UAAYF,CAAA,CAC1B,CAEF,SAASG,GAAiBJ,GACpB,GAAA3G,GAAKqC,MAAMlB,MAAMV,iBAAkB,CACrC,MACMmG,EADmB,IAAIC,KAAK7G,GAAKqC,MAAMlB,MAAMV,kBACJqG,UACxC,OAAAH,EAAKG,UAAYF,CAAA,CAC1B,CAEF,SAASlB,KACPhG,GAAU2C,OAAQ,CAAA,CAIpB,SAAS2E,KACP,MAAMnC,EAAO,GACb,GAAI7E,GAAKqC,MAAMhC,cAAgBmC,EAAYC,UAAW,CACpD,MAAMwE,GAAU,IAAIJ,MAAOK,WACvB,IAAAC,EAAcnG,EAAMhB,GAAKqC,MAAM/B,SAASY,SAASD,OAAO,KACxDgG,EAAUE,IACHA,EAAAF,GAEX,IAAA,IAASG,EAAI,EAAGA,EAAI,GAAIA,IAClBA,GAAKD,GAGTtC,EAAKP,KAAK8C,EACZ,CAEK,OAAAvC,CAAA,CAGA,SAAAwC,GAAgBC,EAAcC,EAAcC,GACnD,MAAM3C,EAAO,GACb,IAAI4C,GAAO,EACX,GAAIzH,GAAKqC,MAAMhC,cAAgBmC,EAAYC,UAAW,CACpD,MAAMwE,GAAU,IAAIJ,MAAOK,WACvB,IAAAC,EAAcnG,EAAMhB,GAAKqC,MAAM/B,SAASY,SAASD,OAAO,KAK5D,GAJIgG,EAAUE,IACHA,EAAAF,EACFQ,GAAA,GAELH,IAASH,EACX,IAAA,IAASC,EAAI,EAAGA,EAAI,GAAIA,KACjBK,GAAO,IAAIZ,MAAOa,aAAe,IAAIb,KAAK7G,GAAKqC,MAAM/B,SAASY,SAASwG,eAAiBN,GAG7FvC,EAAKP,KAAK8C,EAEd,CAEK,OAAAvC,CAAA"}