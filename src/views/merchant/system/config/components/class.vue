<i18n>
{
  "en": {
    "shiftMode": "Shift Mode",
    "cashFlowMode": "Cash Flow Mode",
    "receivableMode": "Accrual Basis (Receivable)",
    "paidInMode": "Cash Basis (Received)",
    "pettyCash": "Petty Cash",
    "enterPettyCash": "Please enter petty cash",
    "edit": "Edit",
    "cancel": "Cancel",
    "save": "Save",
    "startTime": "Start Time",
    "endTime": "End Time",
    "enabled": "Enabled",
    "editSuccess": "Edit successful",
    "currencyUnit": "Yuan (CNY)"
  },
  "zh-cn": {
    "shiftMode": "交班模式",
    "cashFlowMode": "现金流模式",
    "receivableMode": "权责发生制(应收)",
    "paidInMode": "收付实现制(实收)",
    "pettyCash": "备用金",
    "enterPettyCash": "请输入备用金",
    "edit": "编辑",
    "cancel": "取消",
    "save": "保存",
    "startTime": "开始时间",
    "endTime": "结束时间",
    "enabled": "启用",
    "editSuccess": "修改成功",
    "currencyUnit": "元"
  },
  "km": {
    "shiftMode": "របៀបផ្លាស់ប្តូរវេន",
    "cashFlowMode": "របៀបលំហូរសាច់ប្រាក់",
    "receivableMode": "គោលការណ៍គណនាប្រាក់ដែលត្រូវទទួល",
    "paidInMode": "គោលការណ៍គណនាប្រាក់ដែលបានទទួល",
    "pettyCash": "ប្រាក់រង្វាន់",
    "enterPettyCash": "សូមបញ្ចូលប្រាក់រង្វាន់",
    "edit": "កែសម្រួល",
    "cancel": "បោះបង់",
    "save": "រក្សាទុក",
    "startTime": "ពេលវេលាចាប់ផ្តើម",
    "endTime": "ពេលវេលាបញ្ចប់",
    "enabled": "បានបើក",
    "editSuccess": "កែសម្រួលដោយជោគជ័យ",
    "currencyUnit": "រៀល"
  }
}
</i18n>

<script setup lang="ts">
import { hotelParamConfigApi, shiftTimeApi } from '@/api/modules/index'
import { ParamConfigTypeEnum } from '@/models/index'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const { t } = useI18n()
const loading = ref(false)
const isEdit = ref(false)
const isEditBanner = ref(false)
interface ShiftData {
  id?: number
  gcode: string
  hcode: string
  shiftCode: string
  shiftName: string
  state: string | number
  startTime: string
  endTime: string
  disabled?: boolean
}

interface Form {
  gcode: string
  hcode: string
  paramType: string
  data: ShiftData[] // 使用 ShiftData 接口定义数组类型
}

// 假设 userStore 已经定义并且包含了 gcode 和 hcode 属性
const form = reactive<Form>({
  gcode: userStore.gcode,
  hcode: userStore.hcode,
  paramType: '',
  data: [],
})
form.data = [
  {
    gcode: '',
    hcode: '',
    shiftCode: '',
    shiftName: '',
    state: '1',
    startTime: '08:00',
    endTime: '16:00',
    disabled: false,
  },
  // ... 其他班次数据
]
const cash_flow = ref(ParamConfigTypeEnum.CASH_FLOW)
const paid_in = ref(ParamConfigTypeEnum.PAID_IN)
const receivable = ref(ParamConfigTypeEnum.RECEIVABLE)
const formBanner = ref({
  gcode: userStore.gcode,
  hcode: userStore.hcode,
  paramType: '',
  value: {
    shiftMode: '', // 交班模式 receive权责发生制-应收 real收付实现制-实收 cash现金流模式
    pettyCash: 0, // 备用金
  },
})

onMounted(() => {
  getInfo()
  getInfoBanner()
})
/**
 * 获取班次设置
 */
function getInfo() {
  loading.value = true
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  shiftTimeApi.getShiftTimeList(params).then((res: any) => {
    if (res.code === 0) {
      loading.value = false
      form.data = res.data.map((e: ShiftData) => {
        e.disabled = false
        if (e.state != 1) {
          e.disabled = true
          e.endTime = ''
          e.startTime = ''
        }
        return e
      })
    }
  })
}
/**
 * 获取交班模式
 */
function getInfoBanner() {
  loading.value = true
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  hotelParamConfigApi.getHotelParamConfigShiftmode(params).then((res: any) => {
    if (res.code === 0) {
      loading.value = false
      formBanner.value = res.data
    }
  })
}
/**
 * 班次设置保存
 */
function onEdit() {
  return new Promise<void>(() => {
    const params = [...form.data].map((e) => {
      if (!e.disabled || e.disabled) {
        delete e.disabled
      }
      return e
    })
    shiftTimeApi.updateShiftTime(params).then((res: any) => {
      if (res.code === 0) {
        ElMessage.success({
          message: t('editSuccess'),
          center: true,
        })
        editime()
      } else {
        ElMessage.error({
          message: res.msg,
          center: true,
        })
      }
    })
  })
}
/**
 * 交班模式保存
 */
function onEditBanner() {
  return new Promise<void>(() => {
    if (formBanner.value.value.shiftMode !== cash_flow.value) {
      formBanner.value.value.pettyCash = 0
    }
    hotelParamConfigApi.edit(formBanner.value).then((res: any) => {
      if (res.code === 0) {
        ElMessage.success({
          message: t('editSuccess'),
          center: true,
        })
        editBaner()
      } else {
        ElMessage.error({
          message: res.msg,
          center: true,
        })
      }
    })
  })
}
/**
 * 取消班次设置
 */
async function editime() {
  await getInfo()
  isEdit.value = false
}
/**
 * 取消交班模式
 */
async function editBaner() {
  await getInfoBanner()
  isEditBanner.value = false
}

/**
 * 是否启用
 */
function checkboxChange() {
  const noFormList = form.data.filter((n: any) => n.state == 0) // 不启用
  // 循环遍历班次时间设置
  form.data.forEach((e: ShiftData) => {
    if (e.state == 1) {
      e.disabled = false
    }
    noFormList.forEach((e1: ShiftData) => {
      if (e.id == e1.id) {
        e.endTime = ''
        e.startTime = ''
        e.disabled = true
      }
    })
  })
}
/**
 * 对比两个数组的不同值及返回其键
 * @param arr1 新值
 * @param arr2 旧值
 */
function getDifferences(arr1: string[], arr2: string[]) {
  // 获取两个数组的不同值及其键
  const diffValues = arr1.filter((value) => !arr2.includes(value))
  const diffKeys = diffValues.map((value) => arr1.indexOf(value))
  // 返回不同值和对应的键
  return diffValues.map((value, index) => ({ value, key: diffKeys[index] }))[0]
}
/**
 * 监听班次开始时间和结束时间
 */
watch(
  [() => form.data.map((item) => item.startTime), () => form.data.map((item) => item.endTime)],
  (newValue, oldValue) => {
    /**
     * newValue[0] 是开始时间
     * newValue[1] 是结束时间
     * 新值必须与旧值的长度相等，否则是第一次赋的值，而不是修改之后的值
     */
    if (newValue[0].length === oldValue[0]?.length) {
      const startTimeNewValue = newValue[0].filter(Boolean) // 新值开始时间(过滤空值)
      const endTimeNewValue = newValue[1].filter(Boolean) // 新值结束时间(过滤空值)

      const startTimeOldValue = oldValue[0]!.filter(Boolean) // 旧值开始时间(过滤空值)
      const endTimeOldValue = oldValue[1]!.filter(Boolean) // 旧值结束时间(过滤空值)

      const startNewValue = getDifferences(startTimeNewValue, startTimeOldValue) // 对比新值是否有变化，并返回新值与key
      const endNewValue = getDifferences(endTimeNewValue, endTimeOldValue) // 对比旧值是否有变化，并返回新值与key
      // 查找原数组中 state=1 的最后一个下标
      const oldIndex = form.data.findLastIndex((element) => element.state == '1')
      /**
       * 判断修改后的开始与结束时间是否有值
       * 如有值则进行循环原数组 并修改需要自己想要修改的key和值
       * 并且判断是否启用
       */
      // 修改开始时间
      if (startNewValue) {
        form.data.forEach((item, index) => {
          // 判断修改的时间不是开始时间的第一个
          if (startNewValue.key == index && startNewValue.key != 0 && item.state == '1') {
            // 修改当前这条数据前一条数据的结束时间
            form.data[index - 1].endTime = startNewValue.value
            return // 终止本次循环
          }
          // 判断修改的时间是开始时间的第一个
          if (startNewValue.key == index && startNewValue.key == 0 && item.state == '1') {
            // 修改当前这条数据最后一条数据的结束时间
            form.data[oldIndex].endTime = startNewValue.value
            // 终止本次循环
          }
        })
      }
      // 修改结束时间
      if (endNewValue) {
        form.data.forEach((item, index) => {
          // 判断修改的时间不是结束时间的第一个
          if (endNewValue.key == index && oldIndex != endNewValue.key && item.state == '1') {
            // 修改当前这条数据后一条数据的开始时间
            form.data[index + 1].startTime = endNewValue.value
            return // 终止本次循环
          }
          // 判断修改的时间不是结束时间的第一个
          if (endNewValue.key == index && oldIndex == endNewValue.key && item.state == '1') {
            // 修改当前这条数据第一条数据的开始时间
            form.data[0].startTime = endNewValue.value
            // 终止本次循环
          }
        })
      }
    }
  },
  {
    deep: true,
    immediate: true,
  }
)
</script>

<template>
  <div class="ml-[20px]">
    <el-form v-loading="loading" :model="form" label-suffix="：" label-width="120px">
      <div v-for="item in form.data" :key="item.shiftCode">
        <el-form-item :label="item.shiftName">
          <span v-show="isEdit">
            <el-time-select v-model="item.startTime" :placeholder="t('startTime')" start="00:00" :disabled="item.disabled" step="00:15" end="24:00" style="width: 150px" />
            -
            <el-time-select v-model="item.endTime" :placeholder="t('endTime')" start="00:00" :disabled="item.disabled" step="00:15" end="24:00" style="width: 150px" />
          </span>

          <span v-show="!isEdit">{{ item.startTime }}-{{ item.endTime }}</span>
          <el-checkbox v-model="item.state" style="margin-left: 15px" true-value="1" false-value="0" :disabled="!isEdit" @change="checkboxChange">
            {{ t('enabled') }}
          </el-checkbox>
        </el-form-item>
      </div>
      <el-form-item>
        <el-button v-if="!isEdit" v-auth="'pms:shift-time:update'" type="primary" plain @click="isEdit = !isEdit">
          {{ t('edit') }}
        </el-button>
        <el-button v-if="isEdit" @click="editime">
          {{ t('cancel') }}
        </el-button>
        <el-button v-if="isEdit" type="primary" @click="onEdit">
          {{ t('save') }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-form v-loading="loading" :model="formBanner" label-suffix="：" label-width="120px">
      <el-form-item :label="t('shiftMode')">
        <el-radio-group v-if="isEditBanner" v-model="formBanner.value.shiftMode" style="margin-right: 10px">
          <el-radio :value="cash_flow">
            {{ t('cashFlowMode') }}
          </el-radio>
          <el-radio :value="paid_in">
            {{ t('paidInMode') }}
          </el-radio>
          <!-- <el-radio :value="receivable">
            {{ t('receivableMode') }}
          </el-radio> -->
        </el-radio-group>
        <span v-else>{{ formBanner.value.shiftMode === receivable ? t('receivableMode') : formBanner.value.shiftMode === paid_in ? t('paidInMode') : t('cashFlowMode') }}</span>
        <span v-if="formBanner.value.shiftMode === cash_flow" style="margin-left: 10px">
          {{ t('pettyCash') }}
          <el-input-number v-if="isEditBanner" v-model="formBanner.value.pettyCash" :placeholder="t('enterPettyCash')" :max="100000" :min="0" :precision="0" style="width: 150px; margin: 0 10px" controls-position="right" />
          <span v-else> {{ formBanner.value.pettyCash }}{{ t('currencyUnit') }} </span>
        </span>
      </el-form-item>
      <el-form-item>
        <el-button v-if="!isEditBanner" v-auth="'pms:hotel-param-config:update:shiftmode'" type="primary" plain @click="isEditBanner = !isEditBanner">
          {{ t('edit') }}
        </el-button>
        <el-button v-if="isEditBanner" @click="editBaner()">
          {{ t('cancel') }}
        </el-button>
        <el-button v-if="isEditBanner" type="primary" @click="onEditBanner">
          {{ t('save') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.el-form-item-msg {
  width: 100%;
  clear: both;
  font-size: 12px;
  color: #999;
}
</style>
