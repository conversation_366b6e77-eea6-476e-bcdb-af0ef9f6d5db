{"version": 3, "file": "roomfeature-BdrWwlcr.js", "sources": ["../../src/views/merchant/base/room/components/DetailForm/roomfeature.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"addRoomFeatures\": \"Add Room Features\",\r\n    \"selectedRooms\": \"Selected Rooms\",\r\n    \"roomFeatures\": \"Room Features\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"editSuccess\": \"Edit successful\",\r\n    \"addRoomFeatureDescription\": \"Adding room feature descriptions can help guests better understand the room, such as south-facing, north-facing, near the elevator, no windows, etc.\",\r\n    \"batchSetRoomFeatures\": \"Multiple rooms can be selected on the room information page to batch set room feature information.\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"addRoomFeatures\": \"添加房间特征\",\r\n    \"selectedRooms\": \"已选房间\",\r\n    \"roomFeatures\": \"房间特征\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"editSuccess\": \"编辑成功\",\r\n    \"addRoomFeatureDescription\": \"增加房间特征描述可以让客人更好的了解房间,如：朝南、朝北、靠近电梯、无窗等。\",\r\n    \"batchSetRoomFeatures\": \"房间信息页多选房间，可批量设置房间特征信息。\"\r\n  },\r\n  \"km\": {\r\n    \"addRoomFeatures\": \"បន្ថែមលក្ខណៈបន្ទប់\",\r\n    \"selectedRooms\": \"បន្ទប់ដែលបានជ្រើសរើស\",\r\n    \"roomFeatures\": \"លក្ខណៈបន្ទប់\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\",\r\n    \"addRoomFeatureDescription\": \"ការបន្ថែមការពិពណ៌នាអំពីលក្ខណៈបន្ទប់អាចជួយអ្នកភោជនាដឹងពីបន្ទប់កាន់តែប្រសើរឡើង ដូចជា ខាងត្បូង ខាងជើង ជិតជណ្តើយយន្ត គ្មានបង្អួច ជាដើម។\",\r\n    \"batchSetRoomFeatures\": \"អ្នកអាចជ្រើសរើសបន្ទប់ច្រើននៅលើទំព័រព័ត៌មានបន្ទប់ ដើម្បីកំណត់ព័ត៌មានលក្ខណៈបន្ទប់ជាក្រុម។\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance } from 'element-plus'\r\n// import api from '@/api/modules/room.api'\r\n\r\nimport type { DictDataModel } from '@/models/index'\r\nimport { DICT_TYPE_ROOM_FEATURE } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    rooms: { rNo: string; rCode: string }[]\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  editFeature: [state: boolean]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  features: [] as string[],\r\n})\r\nconst myVisible = ref(props.modelValue)\r\nconst features = ref<DictDataModel[]>([])\r\nconst constants: DictDataModel[] = JSON.parse(userStore.constants)\r\nonMounted(() => {\r\n  // getFeatures()\r\n})\r\n\r\nfunction getFeatures() {\r\n  constants.forEach((item: DictDataModel) => {\r\n    if (item.typeCode === DICT_TYPE_ROOM_FEATURE) {\r\n      features.value.push({ code: item.code, name: item.name })\r\n    }\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        api.edit(form.value).then(() => {\r\n          ElMessage.success({\r\n            message: t('editSuccess'),\r\n            center: true,\r\n          })\r\n          emits('editFeature', true)\r\n          onCancel()\r\n        })\r\n      }\r\n    })\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('addRoomFeatures')\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close @closed=\"emits('update:modelValue', false)\">\r\n    <el-form ref=\"formRef\" :model=\"form\" label-width=\"120px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('selectedRooms')\">\r\n        <label v-for=\"room in props.rooms\" :key=\"room.rCode\">{{ room.rNo }}, </label>\r\n      </el-form-item>\r\n      <el-form-item label=\"t('roomFeatures')\">\r\n        <el-checkbox-group v-model=\"form.features\" size=\"small\">\r\n          <el-checkbox v-for=\"ft in features\" :key=\"ft.code\" :value=\"ft.code\">\r\n            {{ ft.name }}\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-alert :title=\"t('addRoomFeatureDescription')\" type=\"info\" show-icon :closable=\"false\" />\r\n    <el-alert :title=\"t('batchSetRoomFeatures')\" type=\"info\" :closable=\"false\" />\r\n    <template #footer>\r\n      <el-button @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" @click=\"onSubmit\">\r\n        {{ t('save') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "ref", "formRef", "form", "features", "myVisible", "modelValue", "onSubmit", "value", "validate", "valid", "api", "edit", "then", "ElMessage", "success", "message", "center", "onCancel", "JSON", "parse", "constants", "onMounted"], "mappings": "wqBA2CA,MAAMA,EAAQC,EASRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IACFC,GAAI,GACpB,MAAMC,EAAUD,IACVE,EAAOF,EAAI,CACfG,SAAU,KAENC,EAAYJ,EAAIR,EAAMa,YACtBF,EAAWH,EAAqB,IActC,SAASM,IACPL,EAAQM,OACNN,EAAQM,MAAMC,UAAUC,IAClBA,GACFC,IAAIC,KAAKT,EAAKK,OAAOK,MAAK,KACxBC,EAAUC,QAAQ,CAChBC,QAASnB,EAAE,eACXoB,QAAQ,IAEVtB,EAAM,eAAe,GACZuB,GAAA,GACV,GAEJ,CAEL,SAASA,IACPb,EAAUG,OAAQ,CAAA,QA7BoBW,KAAAC,MAAMrB,EAAUsB,WACxDC,GAAU"}