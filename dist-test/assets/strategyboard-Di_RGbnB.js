import{d as e,ai as t,aj as l,b as a,y as o,B as r,b3 as s,o as i,c,f as d,w as n,u as p,F as u,ag as m,e as h,i as b,h as g,Y as y,aq as f,a7 as v,bK as _,m as j,j as C,k as T,ax as k,l as S,_ as w,q as I,x,t as V,v as P,aT as D}from"./index-CkEhI1Zk.js";/* empty css                   */import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-DK1kOKr4.js";/* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css               */import{_ as L}from"./index.vue_vue_type_script_setup_true_lang-DvSmdj52.js";/* empty css                *//* empty css                  *//* empty css                       *//* empty css                        *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import{m as U}from"./memberType.api-Cpg3heHv.js";import{c as O}from"./channel.api-CM6FWEgD.js";import{p as R}from"./priceStrategy.api-Deh17XpK.js";import{p as A}from"./protocolAgent.api-B70PZhBJ.js";import{h as B}from"./hourRoomType.api-B3G_Zz25.js";import{r as H}from"./rt.api-5a8-At7-.js";import{d as M}from"./dictData.api-DUabpYqy.js";import{F as Y,j as N,B as q,q as F,u as z,v as W}from"./constants-Cg3j_uH4.js";import{y as $}from"./timeutils-Ib6GkGcq.js";import J from"./index-C6hOtDtU.js";import{_ as K}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                   *//* empty css                  */import"./createStrategy-BbXwUKlP.js";/* empty css                          *//* empty css                */import"./index-DAulSAJI.js";/* empty css                */import"./index-D8c6PuWt.js";/* empty css                      *//* empty css                        *//* empty css                         *//* empty css                */import"./index-CDbn0nBx.js";/* empty css                          *//* empty css                 */import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";/* empty css                   */import"./member.api-2tU9HGvl.js";import"./generalConfig.api-CEBBd8kx.js";import"./merchant.api-BtmIsRm3.js";import"./detail-DK5_oKxe.js";const X=e({name:"SellPriceStrategyBoardList",__name:"strategyboard",setup(e){const K=t(),{t:X}=l(),G=a({loading:!1,isEdit:!1,handle:"",tableAutoHeight:!1,formModeProps:{visible:!1,id:"",strategyCode:""},search:{date:new Date,rtCode:[],order:"lobby",channelCode:Y.LOBBY,checkInType:N.ALL_DAY,checkinTypes:""},dataList:[]}),Q=a([]),Z=a([]),ee=a([]),te=a([]),le=a([]),ae=a("1");o((()=>{!function(){const e={gcode:K.gcode,isEnable:q.YES};U.getTypeSimpleList(e).then((e=>{0===e.code&&(Q.value=e.data)}))}(),function(){const e={gcode:K.gcode,belongHcode:K.hcode,paType:"1",isEnable:q.YES};A.getProtocolAgentListSimple(e).then((e=>{0===e.code&&(Z.value=e.data)}))}(),function(){const e={gcode:K.gcode,belongHcode:K.hcode,paType:"0",isEnable:q.YES};A.getProtocolAgentListSimple(e).then((e=>{0===e.code&&(ee.value=e.data)}))}(),function(){const e={gcode:K.gcode,hcode:K.hcode,isEnable:q.YES};O.getChannelSimpleList(e).then((e=>{0===e.code&&(te.value=e.data)}))}(),function(){const e={gcode:K.gcode,hcode:K.hcode,isVirtual:"0",isEnable:q.YES};H.getRoomTypeSimpleList(e).then((e=>{0===e.code&&(oe.value=e.data,oe.value.forEach((e=>{G.value.search.rtCode.push(e.rtCode)})),ne())}))}(),M.getDictDataBatch(ie).then((e=>{ce.value=e.data.filter((e=>e.dictType===F&&e.code!==N.HOUR_ROOM)),de.value=e.data.filter((e=>e.dictType===z)),re.value=e.data.filter((e=>e.dictType===W)),ue()})),function(){const e={gcode:K.gcode,hcode:K.hcode};B.getHourRoomTypeList(e).then((e=>{0===e.code&&(le.value=e.data)}))}()}));const oe=a([]);const re=a([]),se=a([]),ie=[F,z,W],ce=a([]),de=a([]);function ne(){G.value.loading=!1;const e={gcode:K.gcode,hcode:K.hcode,date:$(G.value.search.date),rtCodes:G.value.search.rtCode.join(","),channelCode:G.value.search.channelCode,orderSource:G.value.search.order,checkinType:G.value.search.checkInType};R.getPriceStrategyBoardList(e).then((e=>{G.value.loading=!1,0===e.code&&(G.value.dataList=e.data)}))}function pe(e){G.value.isEdit=e}function ue(){"1"===ae.value?se.value=ce.value:se.value=le.value.map((e=>({code:e.hourCode,label:e.hourName}))),se.value.length>0&&(G.value.search.checkInType=se.value[0].code),G.value.search.rtCode=[]}function me(e,t){let l="-";const a=e.find((e=>e.code===t));return a&&(l=`￥${a.price}`),l}function he(){G.value.formModeProps.strategyCode="",G.value.formModeProps.visible=!0,G.value.handle="create"}function be(e){return e.getTime()<Date.now()-864e5}const ge=r((()=>{if("2"===ae.value&&G.value.search.checkInType){const e=le.value.find((e=>e.hourCode===G.value.search.checkInType));return e&&e.rts?oe.value.filter((t=>e.rts.some((e=>e.rtCode===t.rtCode)))):[]}return oe.value}));return(e,t)=>{const l=_,a=j,o=C,r=T,U=k,O=S,R=w,A=I,B=x,H=L,M=V,Y=P,N=E,q=s("auth"),F=D;return i(),c("div",{class:v({"absolute-container":p(G).tableAutoHeight})},[d(N,null,{default:n((()=>[d(H,{"show-toggle":!1},{default:n((()=>[d(B,{model:p(G).search,size:"default","label-width":"110px","inline-message":"",inline:"",class:"search-form"},{default:n((()=>[d(a,{label:p(X)("date")},{default:n((()=>[d(l,{modelValue:p(G).search.date,"onUpdate:modelValue":t[0]||(t[0]=e=>p(G).search.date=e),style:{width:"150px"},"disabled-date":be,type:"date",placeholder:p(X)("selectDate")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),d(a,{label:p(X)("channel")},{default:n((()=>[d(r,{modelValue:p(G).search.channelCode,"onUpdate:modelValue":t[1]||(t[1]=e=>p(G).search.channelCode=e),style:{width:"150px"},placeholder:p(X)("selectChannel")},{default:n((()=>[(i(!0),c(u,null,m(p(te),(e=>(i(),h(o,{key:e.channelCode,label:e.channelName,value:e.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),d(a,{label:p(X)("checkInType")},{default:n((()=>[d(O,{modelValue:p(ae),"onUpdate:modelValue":t[2]||(t[2]=e=>b(ae)?ae.value=e:null),onChange:ue},{default:n((()=>[d(U,{label:p(X)("allDay"),value:"1"},null,8,["label"]),d(U,{label:p(X)("hourly"),value:"2"},null,8,["label"])])),_:1},8,["modelValue"]),d(r,{modelValue:p(G).search.checkInType,"onUpdate:modelValue":t[3]||(t[3]=e=>p(G).search.checkInType=e),placeholder:p(X)("selectCheckInType"),style:{width:"200px"}},{default:n((()=>[(i(!0),c(u,null,m(p(se),(e=>(i(),h(o,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),d(a,{label:p(X)("roomType")},{default:n((()=>[d(r,{modelValue:p(G).search.rtCode,"onUpdate:modelValue":t[4]||(t[4]=e=>p(G).search.rtCode=e),placeholder:p(X)("selectRoomType"),clearable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"250px"}},{default:n((()=>[(i(!0),c(u,null,m(p(ge),(e=>(i(),h(o,{key:e.rtCode,label:e.rtName,value:e.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),d(a,{label:p(X)("orderSource")},{default:n((()=>[d(r,{modelValue:p(G).search.order,"onUpdate:modelValue":t[5]||(t[5]=e=>p(G).search.order=e),placeholder:p(X)("selectOrderSource"),style:{width:"150px"}},{default:n((()=>[(i(!0),c(u,null,m(p(de),(e=>(i(),h(o,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),d(a,{style:{float:"right"}},{default:n((()=>[d(A,{type:"primary",onClick:t[6]||(t[6]=e=>ne())},{icon:n((()=>[d(R,{name:"ep:search"})])),default:n((()=>[g(" "+y(p(X)("filter")),1)])),_:1}),f((i(),h(A,{size:"default",onClick:he},{icon:n((()=>[d(R,{name:"ep:plus"})])),default:n((()=>[g(" "+y(p(X)("createPriceStrategy")),1)])),_:1})),[[q,"pms:price-strategy:create"]])])),_:1})])),_:1},8,["model"])])),_:1}),d(J,{modelValue:p(G).formModeProps.visible,"onUpdate:modelValue":t[7]||(t[7]=e=>p(G).formModeProps.visible=e),"strategy-code":p(G).formModeProps.strategyCode,handle:p(G).handle,"is-edit":p(G).isEdit,onEditStatus:pe,onSuccess:ne},null,8,["modelValue","strategy-code","handle","is-edit"]),f((i(),h(Y,{class:"list-table",data:p(G).dataList,"highlight-current-row":"",height:"100%",style:{"margin-top":"15px"}},{default:n((()=>[d(M,{prop:"rt.rtName",label:p(X)("roomType"),fixed:"",width:"180"},null,8,["label"]),d(M,{label:p(X)("walkIn"),align:"right"},{default:n((({row:e})=>[g(" ￥"+y(e.walkInPrice),1)])),_:1},8,["label"]),d(M,{label:p(X)("member")},{default:n((()=>[(i(!0),c(u,null,m(p(Q),(e=>(i(),h(M,{key:e.mtCode,label:e.mtName,align:"right"},{default:n((({row:t})=>[g(y(me(t.mts,e.mtCode)),1)])),_:2},1032,["label"])))),128))])),_:1},8,["label"]),d(M,{label:p(X)("agent")},{default:n((()=>[(i(!0),c(u,null,m(p(Z),(e=>(i(),h(M,{key:e.paCode,label:e.paName,align:"right"},{default:n((({row:t})=>[g(y(me(t.agents,e.paCode)),1)])),_:2},1032,["label"])))),128))])),_:1},8,["label"]),d(M,{label:p(X)("protocolUnit")},{default:n((()=>[(i(!0),c(u,null,m(p(ee),(e=>(i(),h(M,{key:e.paCode,label:e.paName,align:"right"},{default:n((({row:t})=>[g(y(me(t.protocols,e.paCode)),1)])),_:2},1032,["label"])))),128))])),_:1},8,["label"])])),_:1},8,["data"])),[[F,p(G).loading]])])),_:1})],2)}}});function G(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{date:{t:0,b:{t:2,i:[{t:3}],s:"Date"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"Channel"}},checkInType:{t:0,b:{t:2,i:[{t:3}],s:"Check-In Type"}},allDay:{t:0,b:{t:2,i:[{t:3}],s:"Full Day"}},hourly:{t:0,b:{t:2,i:[{t:3}],s:"Hourly"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"Select Date"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"Select Channel"}},selectCheckInType:{t:0,b:{t:2,i:[{t:3}],s:"Select Check-In Type"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Select Room Type"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"Order Source"}},selectOrderSource:{t:0,b:{t:2,i:[{t:3}],s:"Select Order Source"}},filter:{t:0,b:{t:2,i:[{t:3}],s:"Filter"}},createPriceStrategy:{t:0,b:{t:2,i:[{t:3}],s:"Create Price Strategy"}},walkIn:{t:0,b:{t:2,i:[{t:3}],s:"Walk-In"}},member:{t:0,b:{t:2,i:[{t:3}],s:"Member"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"Agent"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"Protocol Unit"}}},"zh-cn":{date:{t:0,b:{t:2,i:[{t:3}],s:"日期"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"渠道"}},checkInType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},allDay:{t:0,b:{t:2,i:[{t:3}],s:"全天"}},hourly:{t:0,b:{t:2,i:[{t:3}],s:"钟点"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"请选择日期"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"请选择渠道"}},selectCheckInType:{t:0,b:{t:2,i:[{t:3}],s:"请选择入住类型"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"请选择房型"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"订单来源"}},selectOrderSource:{t:0,b:{t:2,i:[{t:3}],s:"请选择订单来源"}},filter:{t:0,b:{t:2,i:[{t:3}],s:"筛选"}},createPriceStrategy:{t:0,b:{t:2,i:[{t:3}],s:"新增房价策略"}},walkIn:{t:0,b:{t:2,i:[{t:3}],s:"散客"}},member:{t:0,b:{t:2,i:[{t:3}],s:"会员"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}}},km:{date:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទ"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},checkInType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលស្នាក់"}},allDay:{t:0,b:{t:2,i:[{t:3}],s:"មួយថ្ងៃ"}},hourly:{t:0,b:{t:2,i:[{t:3}],s:"ម៉ោង"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកាលបរិច្ឆេទ"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឆានែល"}},selectCheckInType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទចូលស្នាក់"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទបន្ទប់"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពលំដាប់"}},selectOrderSource:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភពលំដាប់"}},filter:{t:0,b:{t:2,i:[{t:3}],s:"តម្រង"}},createPriceStrategy:{t:0,b:{t:2,i:[{t:3}],s:"បង្កើតយុទ្ធសាស្ត្រតម្លៃ"}},walkIn:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវដោយផ្ទាល់"}},member:{t:0,b:{t:2,i:[{t:3}],s:"សមាជិក"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"ភ្នាក់ងារ"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"អង្គភាពព្រមព្រៀង"}}}}})}G(X);const Q=K(X,[["__scopeId","data-v-5862ff29"]]);export{Q as default};
//# sourceMappingURL=strategyboard-Di_RGbnB.js.map
