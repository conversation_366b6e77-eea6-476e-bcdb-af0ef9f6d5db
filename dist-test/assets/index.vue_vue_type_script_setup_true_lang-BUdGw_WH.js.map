{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js", "sources": ["../../src/components/EasyRadio/index.vue"], "sourcesContent": ["<template>\r\n  <el-radio-group v-model=\"newValue\" :size=\"size\" :disabled=\"disabled\" @change=\"handleChange(newValue)\">\r\n    <div v-if=\"radioValue == 'radioButton'\">\r\n      <el-radio-button v-for=\"item in data\" :label=\"item[labelKey]\" :value=\"item[valueKey]\">\r\n        <slot :label=\"item[labelKey]\" :value=\"item[valueKey]\" />\r\n      </el-radio-button>\r\n    </div>\r\n    <div v-else>\r\n      <el-radio :border=\"item.border\" v-for=\"item in data\" :name=\"item.name\" :size=\"item.size\" :label=\"item[labelKey]\" :value=\"item[valueKey]\">\r\n        <slot :label=\"item[labelKey]\" :value=\"item[valueKey]\" />\r\n      </el-radio>\r\n    </div>\r\n  </el-radio-group>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n// 父组件传递的值\r\ninterface Props {\r\n  data: Select.selectItem[]\r\n  /** 选中项绑定值 */\r\n  modelValue: Select.value\r\n  /** 显示的类型 */\r\n  radioValue?: '' | 'radio' | 'radioButton' | undefined\r\n  /** 选项的标签，若不设置则默认与value相同 */\r\n  labelKey?: string\r\n  /** 选项的值 */\r\n  valueKey?: string\r\n  /** 是否禁用 */\r\n  disabled?: boolean\r\n  /** 框尺寸 */\r\n  size?: '' | 'default' | 'small' | 'large' | undefined\r\n  /** 选中值发生变化时触发 */\r\n  // onChange?: (value: Number | String | Boolean) => void\r\n}\r\nconst props = withDefaults(defineProps<Props>(), {\r\n  labelKey: 'label',\r\n  valueKey: 'value',\r\n})\r\n\r\ninterface EmitEvent {\r\n  /** 选中项绑定值 */\r\n  (e: 'update:modelValue', vlaue: Select.value): void\r\n  /** 选中值发生变化时触发 */\r\n  (e: 'onChange', value: Number | String | Boolean): void\r\n}\r\nfunction handleChange(value) {\r\n  emit('onChange', value)\r\n}\r\nconst emit = defineEmits<EmitEvent>()\r\nconst newValue = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value: Select.value) {\r\n    emit('update:modelValue', value)\r\n  },\r\n})\r\n</script>\r\n"], "names": ["props", "__props", "emit", "__emit", "newValue", "computed", "get", "modelValue", "set", "value"], "mappings": "ydAkCA,MAAMA,EAAQC,EAcd,MAAMC,EAAOC,EACPC,EAAWC,EAAS,CACxBC,IAAM,IACGN,EAAMO,WAEf,GAAAC,CAAIC,GACFP,EAAK,oBAAqBO,EAAK,8LATbA,YACpBP,EAAK,WAAYO,GADnB,IAAsBA"}