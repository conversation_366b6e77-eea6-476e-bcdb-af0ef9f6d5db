{"version": 3, "file": "arrangeRooms-CPfs5GXR.js", "sources": ["../../src/views/room/components/arrangeRooms/arrangeRooms.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"roomArrangement\": \"Room Arrangement\",\r\n    \"roomType\": \"Room Type\",\r\n    \"roomStatus\": \"Room Status\",\r\n    \"others\": \"Others\",\r\n    \"bookedRoom\": \"Booked Room\",\r\n    \"roomsArranged\": \"Rooms Arranged\",\r\n    \"rooms\": \"rooms\",\r\n    \"cancel\": \"Cancel\",\r\n    \"confirm\": \"Confirm\",\r\n    \"roomArrangementSuccess\": \"Room arrangement successful\",\r\n    \"maxRoomSelection\": \"You can select up to {num} rooms\",\r\n    \"getRoomTypeListError\": \"Failed to retrieve room type list\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"roomArrangement\": \"排房\",\r\n    \"roomType\": \"房型\",\r\n    \"roomStatus\": \"房态\",\r\n    \"others\": \"其他\",\r\n    \"bookedRoom\": \"预订单占用房间\",\r\n    \"roomsArranged\": \"已排房\",\r\n    \"rooms\": \"间\",\r\n    \"cancel\": \"取消\",\r\n    \"confirm\": \"确定\",\r\n    \"roomArrangementSuccess\": \"排房成功\",\r\n    \"maxRoomSelection\": \"最多选择{num}间房间\",\r\n    \"getRoomTypeListError\": \"获取房型列表失败\"\r\n  },\r\n  \"km\": {\r\n    \"roomArrangement\": \"ការរៀបចំបន្ទប់\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"roomStatus\": \"ស្ថានភាពបន្ទប់\",\r\n    \"others\": \"ផ្សេងៗ\",\r\n    \"bookedRoom\": \"បន្ទប់ដែលបានកក់\",\r\n    \"roomsArranged\": \"បន្ទប់ដែលបានរៀបចំ\",\r\n    \"rooms\": \"បន្ទប់\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"confirm\": \"បញ្ជាក់\",\r\n    \"roomArrangementSuccess\": \"ការរៀបចំបន្ទប់ជោគជ័យ\",\r\n    \"maxRoomSelection\": \"អ្នកអាចជ្រើសរើសបានរហូតដល់ {num} បន្ទប់\",\r\n    \"getRoomTypeListError\": \"មិនអាចទាញយកបញ្ជីប្រភេទបន្ទប់បាន\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DetailFormProps } from './types'\r\nimport { bookApi, dictDataApi } from '@/api/modules/index'\r\nimport { BooleanEnum, DICT_TYPE_ROOM_STATUS, RoomState } from '@/models'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(\r\n  defineProps<\r\n    {\r\n      modelValue?: boolean\r\n      rooms?: Array<any>\r\n      isAlone?: boolean\r\n    } & DetailFormProps\r\n  >(),\r\n  {\r\n    rtCode: '',\r\n    rtName: '',\r\n    checkinType: '',\r\n    guestSrcType: '',\r\n    orderSource: '',\r\n    roomNum: 0,\r\n    rtState: '',\r\n    rNos: (): string[] => {\r\n      // 返回一个字符串数组\r\n      return []\r\n    },\r\n    planCheckinTime: '',\r\n    planCheckoutTime: '',\r\n    rooms: [],\r\n    bookNo: '',\r\n    orderNo: '',\r\n    isAlone: false,\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n  reload: [data: any]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst data = ref({\r\n  rtCode: props.rtCode,\r\n  rtName: props.rtName,\r\n  /** 房间状态 VC 空净  VD 空脏  OC 住净 OD 住脏  OO维修 */\r\n  state: props.rtState ? props.rtState : 'VC',\r\n  /** 预订单占用房间 0:否 1:是 */\r\n  isBookedRoom: '0',\r\n  selectRooms: [] as string[],\r\n  dayPrices: [] as { date: string; price: string }[],\r\n  planCheckinTime: props.planCheckinTime,\r\n  planCheckoutTime: props.planCheckoutTime,\r\n  // 定价\r\n  price: 0 as number | string,\r\n  bookRooms: [] as {\r\n    preOccupied: string\r\n    state: string\r\n    rNo: string\r\n    rCode: string\r\n  }[],\r\n})\r\n\r\n/** 房间列表 */\r\nconst roomPrices = ref<{ rNo: string; rCode: string }[]>([])\r\nconst roomAll = ref<{ rNo: string; rCode: string; state: string; lockNo: string }[]>([])\r\n/** 房型列表 */\r\nconst rts = ref<{ rtCode: string; rtName: string; price: string | number }[]>([])\r\nconst roomList = ref<{ rNo: string; rCode: string; rtCode: string; rtName: string }[]>([])\r\nonMounted(async () => {\r\n  getConstants()\r\n  if (props.rNos.length > 0) {\r\n    data.value.selectRooms = props.rNos\r\n  }\r\n  await getRooms()\r\n  roomList.value = props.rooms && props.rooms.length > 0 ? props.rooms : []\r\n  if (props.rtCode === '') {\r\n    await getRts()\r\n  }\r\n})\r\n\r\nasync function getRts() {\r\n  await bookApi\r\n    .roomtypeList({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      channelCode: 'lobby', // 渠道  默认门店 lobby\r\n      delayMinute: 0,\r\n      checkinType: props.checkinType, // 入住类型\r\n      guestSrcType: props.guestSrcType, // 客源\r\n      orderSource: props.orderSource, // 订单来源\r\n      planCheckinTime: props.planCheckinTime,\r\n      planCheckoutTime: props.planCheckoutTime,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code !== 0) {\r\n        ElMessage.error(t('getRoomTypeListError'))\r\n        return\r\n      }\r\n      rts.value = res.data\r\n      if (rts.value.length > 0) {\r\n        data.value.rtCode = rts.value[0].rtCode\r\n        data.value.rtName = rts.value[0].rtName\r\n        data.value.price = rts.value[0].price\r\n      }\r\n    })\r\n}\r\n\r\n// 通用字典\r\nconst dictTypes = [DICT_TYPE_ROOM_STATUS]\r\n/** 房间状态 */\r\nconst roomStates = ref<{ code: string; label: string }[]>([])\r\n\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    roomStates.value = res.data.filter((item: any) => item.code !== RoomState.OO)\r\n  })\r\n}\r\n\r\nasync function getRooms() {\r\n  loading.value = true\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    rtCode: data.value.rtCode,\r\n    state: data.value.state,\r\n    planCheckinTime: dayjs(props.planCheckinTime).format('YYYY-MM-DD HH:mm'),\r\n    planCheckoutTime: dayjs(props.planCheckoutTime).format('YYYY-MM-DD HH:mm'),\r\n    isMeetingRoom: data.value.isBookedRoom,\r\n    preOccupied: '0',\r\n  }\r\n  await bookApi.canBookRoomList(params).then((res: any) => {\r\n    loading.value = false\r\n    if (res.code !== 0) {\r\n      ElMessage.error(res.msg)\r\n      return\r\n    }\r\n    roomAll.value = res.data\r\n    roomPrices.value = roomAll.value.filter((item: { state: string }) => {\r\n      return item.state === data.value.state\r\n    })\r\n  })\r\n}\r\n\r\nfunction handleClose(tag: string) {\r\n  data.value.selectRooms.splice(data.value.selectRooms.indexOf(tag), 1)\r\n}\r\n\r\nfunction doCheck(val: any) {\r\n  if (props.roomNum > 0 && data.value.selectRooms.length > props.roomNum) {\r\n    ElMessage.warning(t('maxRoomSelection', { num: props.roomNum }))\r\n    data.value.selectRooms = data.value.selectRooms.slice(0, props.roomNum)\r\n    return false\r\n  } else {\r\n    if (roomList.value.length < data.value.selectRooms.length) {\r\n      roomList.value.unshift({\r\n        rNo: val.rNo,\r\n        rCode: val.rCode,\r\n        rtCode: data.value.rtCode,\r\n        rtName: data.value.rtName,\r\n      })\r\n    } else {\r\n      // roomList.value = roomList.value.slice(0, 1)\r\n      roomList.value = roomList.value.filter((item) => {\r\n        return data.value.selectRooms.includes(item.rNo)\r\n      })\r\n    }\r\n    return true\r\n  }\r\n}\r\nfunction changeState() {\r\n  data.value.isBookedRoom = '0'\r\n  roomPrices.value = roomAll.value.filter((item: { state: string }) => {\r\n    return item.state === data.value.state\r\n  })\r\n}\r\n\r\nfunction rtChange() {\r\n  if (rts.value && Array.isArray(rts.value) && data && typeof data.value === 'object') {\r\n    data.value.rtName = rts.value.find((item) => item.rtCode === data.value.rtCode)?.rtName ?? ''\r\n    data.value.price = rts.value.find((item) => item.price === data.value.price)?.price ?? ''\r\n  } else {\r\n    console.error('rts or data is not in the expected format.')\r\n  }\r\n  getRooms()\r\n}\r\n\r\nfunction selectRoomType(rtCode: string) {\r\n  // 如果点击的是已选中的房型，不执行任何操作\r\n  if (data.value.rtCode === rtCode) {\r\n    return\r\n  }\r\n  data.value.rtCode = rtCode\r\n  rtChange()\r\n}\r\n\r\nfunction bookedRoomChange() {\r\n  if (data.value.isBookedRoom === BooleanEnum.YES) {\r\n    data.value.state = '' as RoomState\r\n  } else {\r\n    data.value.state = RoomState.VC\r\n  }\r\n  getRooms()\r\n}\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val: boolean) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nfunction onSubmit() {\r\n  if (data.value.selectRooms.length <= props.roomNum) {\r\n    const list = roomList.value.filter((person) => data.value.selectRooms.includes(person.rNo))\r\n    // list = roomPrices.value.filter((person) =>\r\n    //   data.value.selectRooms.includes(person.rNo)\r\n    // );\r\n    data.value.bookRooms = list.map((item) => {\r\n      return {\r\n        ...item,\r\n        preOccupied: data.value.isBookedRoom,\r\n        state: data.value.state,\r\n      }\r\n    })\r\n    if (props.isAlone) {\r\n      // 单独排房\r\n      const params = {\r\n        gcode: userStore.gcode,\r\n        hcode: userStore.hcode,\r\n        bookNo: props.bookNo,\r\n        batchNo: props.batchNo,\r\n        rooms: [\r\n          {\r\n            rCode: data.value.bookRooms[0]?.rCode,\r\n            rNo: data.value.bookRooms[0]?.rNo,\r\n            orderNo: props.orderNo,\r\n          },\r\n        ],\r\n      }\r\n      bookApi.arrangeBook(params).then((res: any) => {\r\n        if (res.code === 0) {\r\n          ElMessage.success(t('roomArrangementSuccess'))\r\n          emits('reload', data.value.selectRooms)\r\n          onCancel()\r\n        }\r\n      })\r\n    } else {\r\n      emits('success', data.value)\r\n      onCancel()\r\n    }\r\n  }\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" :title=\"t('roomArrangement')\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <el-form size=\"default\" label-width=\"120px\" inline-message inline class=\"search-form\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('roomType')\" label-width=\"80px\">\r\n              <div class=\"room-type-buttons\">\r\n                <el-button v-if=\"props.rtCode !== ''\" type=\"primary\" size=\"small\">\r\n                  {{ props.rtName }}\r\n                </el-button>\r\n                <el-button\r\n                  v-else\r\n                  v-for=\"item in rts\"\r\n                  :key=\"item.rtCode\"\r\n                  size=\"small\"\r\n                  :type=\"data.rtCode === item.rtCode ? 'primary' : 'default'\"\r\n                  @click=\"selectRoomType(item.rtCode)\"\r\n                >\r\n                  {{ item.rtName }}\r\n                </el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('roomStatus')\" label-width=\"80px\">\r\n              <el-radio-group v-model=\"data.state\" size=\"small\" @change=\"changeState\">\r\n                <el-radio-button v-for=\"item in roomStates\" :key=\"item.code\" :value=\"item.code\" border>\r\n                  {{ item.label }}\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('others')\" label-width=\"80px\">\r\n              <el-checkbox v-model=\"data.isBookedRoom\" true-value=\"1\" false-value=\"0\" size=\"small\" :label=\"t('bookedRoom')\" border @change=\"bookedRoomChange()\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div class=\"special_td\">\r\n        <div style=\"margin-bottom: 15px\">\r\n          <span v-if=\"props.roomNum > 0\">{{ t('roomsArranged') }}: {{ data.selectRooms.length }} / {{ props.roomNum }} {{ t('rooms') }}</span>\r\n          <span v-else>{{ t('roomsArranged') }}: {{ data.selectRooms.length }}{{ t('rooms') }}</span>\r\n        </div>\r\n        <el-tag v-for=\"item in data.selectRooms\" :key=\"item\" class=\"roomtag\" type=\"danger\" closable @close=\"handleClose(item)\">\r\n          {{ item }}\r\n        </el-tag>\r\n      </div>\r\n      <div class=\"roomList\">\r\n        <div class=\"flexBox\">\r\n          <ul style=\"padding-left: 0; margin-bottom: 0; list-style: none\">\r\n            <el-checkbox-group v-model=\"data.selectRooms\">\r\n              <li v-for=\"item in roomPrices\" :key=\"item.rCode\" class=\"xxx\">\r\n                {{ item }}\r\n                <el-checkbox v-model=\"item.rNo\" :value=\"item.rNo\" border @change=\"doCheck(item)\">\r\n                  {{ item.rNo }}\r\n                </el-checkbox>\r\n              </li>\r\n            </el-checkbox-group>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          {{ t('confirm') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.special_td {\r\n  padding: 10px;\r\n  line-height: 20px;\r\n  color: #000;\r\n  background: #f7f7f7;\r\n  border-radius: 3px;\r\n}\r\n\r\n.xxx {\r\n  position: relative;\r\n  float: left;\r\n  padding: 3px;\r\n  margin-bottom: 5px;\r\n  cursor: pointer;\r\n}\r\n\r\n.flexBox {\r\n  display: flex;\r\n  flex-flow: row wrap;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.roomList {\r\n  height: 260px;\r\n  padding: 5px;\r\n  margin-top: 10px;\r\n  overflow: auto;\r\n  background-color: #f7f7f7;\r\n}\r\n\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n/* 表单标签和子元素统一字体大小 */\r\n.search-form {\r\n  :deep(.el-form-item__label) {\r\n    font-size: 14px !important;\r\n  }\r\n\r\n  :deep(.el-radio-button__inner) {\r\n    font-size: 14px !important;\r\n    height: 30px !important;\r\n    line-height: 30px !important;\r\n    display: flex !important;\r\n    align-items: center !important;\r\n    justify-content: center !important;\r\n  }\r\n\r\n  :deep(.el-checkbox__label) {\r\n    font-size: 14px !important;\r\n  }\r\n\r\n  :deep(.el-checkbox) {\r\n    height: 30px !important;\r\n    display: flex !important;\r\n    align-items: center !important;\r\n  }\r\n\r\n  :deep(.el-checkbox__input) {\r\n    display: flex !important;\r\n    align-items: center !important;\r\n  }\r\n}\r\n\r\n/* 房型按钮样式 */\r\n.room-type-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px 4px;\r\n\r\n  .el-button {\r\n    margin: 0;\r\n    font-size: 14px !important;\r\n    height: 30px !important;\r\n\r\n    &.el-button--primary {\r\n      &:hover {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        transform: none !important;\r\n        box-shadow: none !important;\r\n      }\r\n\r\n      &:focus {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        box-shadow: none !important;\r\n      }\r\n\r\n      &:active {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        transform: none !important;\r\n      }\r\n\r\n      &:focus-visible {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        box-shadow: none !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 其他文字元素统一字体大小 */\r\n.special_td {\r\n  font-size: 14px !important;\r\n}\r\n\r\n.roomtag {\r\n  margin-right: 5px;\r\n  margin-bottom: 8px;\r\n  font-size: 14px !important;\r\n  height: 30px !important;\r\n  line-height: 30px !important;\r\n  display: inline-flex !important;\r\n  align-items: center !important;\r\n}\r\n\r\n.roomList {\r\n  font-size: 14px !important;\r\n\r\n  .el-checkbox {\r\n    font-size: 14px !important;\r\n  }\r\n\r\n  .el-checkbox__label {\r\n    font-size: 14px !important;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "data", "rtCode", "rtName", "state", "rtState", "isBookedRoom", "selectRooms", "dayPrices", "planCheckinTime", "planCheckoutTime", "price", "bookRooms", "roomPrices", "roomAll", "rts", "roomList", "onMounted", "async", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "then", "res", "roomStates", "value", "filter", "item", "code", "RoomState", "OO", "rNos", "length", "getRooms", "rooms", "bookApi", "roomtypeList", "gcode", "hcode", "channelCode", "delayMinute", "checkinType", "guestSrcType", "orderSource", "ElMessage", "error", "getRts", "DICT_TYPE_ROOM_STATUS", "params", "dayjs", "format", "isMeetingRoom", "preOccupied", "canBookRoomList", "msg", "changeState", "selectRoomType", "Array", "isArray", "_a", "find", "_b", "console", "myVisible", "computed", "get", "modelValue", "set", "val", "onSubmit", "roomNum", "list", "person", "includes", "rNo", "map", "isAlone", "bookNo", "batchNo", "rCode", "orderNo", "arrangeBook", "success", "onCancel", "BooleanEnum", "YES", "VC", "tag", "splice", "indexOf", "warning", "num", "slice", "unshift"], "mappings": "6iDAsDA,MAAMA,EAAQC,EA6BRC,EAAQC,GAKRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAOD,EAAI,CACfE,OAAQX,EAAMW,OACdC,OAAQZ,EAAMY,OAEdC,MAAOb,EAAMc,QAAUd,EAAMc,QAAU,KAEvCC,aAAc,IACdC,YAAa,GACbC,UAAW,GACXC,gBAAiBlB,EAAMkB,gBACvBC,iBAAkBnB,EAAMmB,iBAExBC,MAAO,EACPC,UAAW,KASPC,EAAab,EAAsC,IACnDc,EAAUd,EAAqE,IAE/Ee,EAAMf,EAAkE,IACxEgB,GAAWhB,EAAsE,IACvFiB,GAAUC,UA6CRC,EAAYC,iBAAiBC,IAAWC,MAAMC,IACjCC,GAAAC,MAAQF,EAAItB,KAAKyB,QAAQC,GAAcA,EAAKC,OAASC,EAAUC,IAAE,IA5C1EvC,EAAMwC,KAAKC,OAAS,IACjB/B,EAAAwB,MAAMlB,YAAchB,EAAMwC,YAE3BE,KACGjB,GAAAS,MAAQlC,EAAM2C,OAAS3C,EAAM2C,MAAMF,OAAS,EAAIzC,EAAM2C,MAAQ,GAClD,KAAjB3C,EAAMW,cAKZgB,uBACQiB,EACHC,aAAa,CACZC,MAAOxC,EAAUwC,MACjBC,MAAOzC,EAAUyC,MACjBC,YAAa,QACbC,YAAa,EACbC,YAAalD,EAAMkD,YACnBC,aAAcnD,EAAMmD,aACpBC,YAAapD,EAAMoD,YACnBlC,gBAAiBlB,EAAMkB,gBACvBC,iBAAkBnB,EAAMmB,mBAEzBY,MAAMC,IACY,IAAbA,EAAIK,MAIRb,EAAIU,MAAQF,EAAItB,KACZc,EAAIU,MAAMO,OAAS,IACrB/B,EAAKwB,MAAMvB,OAASa,EAAIU,MAAM,GAAGvB,OACjCD,EAAKwB,MAAMtB,OAASY,EAAIU,MAAM,GAAGtB,OACjCF,EAAKwB,MAAMd,MAAQI,EAAIU,MAAM,GAAGd,QAPtBiC,EAAAC,MAAMlD,EAAE,wBAOc,GAEnC,CA5BKmD,EAAO,IAgCX,MAAAzB,GAAY,CAAC0B,GAEbvB,GAAaxB,EAAuC,IAQ1DkB,eAAee,KACblC,EAAQ0B,OAAQ,EAChB,MAAMuB,EAAS,CACbX,MAAOxC,EAAUwC,MACjBC,MAAOzC,EAAUyC,MACjBpC,OAAQD,EAAKwB,MAAMvB,OACnBE,MAAOH,EAAKwB,MAAMrB,MAClBK,gBAAiBwC,EAAM1D,EAAMkB,iBAAiByC,OAAO,oBACrDxC,iBAAkBuC,EAAM1D,EAAMmB,kBAAkBwC,OAAO,oBACvDC,cAAelD,EAAKwB,MAAMnB,aAC1B8C,YAAa,WAETjB,EAAQkB,gBAAgBL,GAAQ1B,MAAMC,IAC1CxB,EAAQ0B,OAAQ,EACC,IAAbF,EAAIK,MAIRd,EAAQW,MAAQF,EAAItB,KACpBY,EAAWY,MAAQX,EAAQW,MAAMC,QAAQC,GAChCA,EAAKvB,QAAUH,EAAKwB,MAAMrB,SALvBwC,EAAAC,MAAMtB,EAAI+B,IAMrB,GACF,CA6BH,SAASC,KACPtD,EAAKwB,MAAMnB,aAAe,IAC1BO,EAAWY,MAAQX,EAAQW,MAAMC,QAAQC,GAChCA,EAAKvB,QAAUH,EAAKwB,MAAMrB,OAClC,CAaH,SAASoD,GAAetD,GAVxB,QAYMD,EAAKwB,MAAMvB,SAAWA,IAG1BD,EAAKwB,MAAMvB,OAASA,EAdhBa,EAAIU,OAASgC,MAAMC,QAAQ3C,EAAIU,QAAUxB,GAA8B,iBAAfA,EAAKwB,OAC/DxB,EAAKwB,MAAMtB,QAAS,OAAAwD,EAAI5C,EAAAU,MAAMmC,MAAMjC,GAASA,EAAKzB,SAAWD,EAAKwB,MAAMvB,mBAASC,SAAU,GAC3FF,EAAKwB,MAAMd,OAAQ,OAAAkD,EAAI9C,EAAAU,MAAMmC,MAAMjC,GAASA,EAAKhB,QAAUV,EAAKwB,MAAMd,kBAAQA,QAAS,IAEvFmD,QAAQjB,MAAM,8CAEPZ,KASA,CAWX,MAAM8B,GAAYC,EAAS,CACzBC,IAAM,IACG1E,EAAM2E,WAEf,GAAAC,CAAIC,GACF3E,EAAM,oBAAqB2E,EAAG,IAGlC,SAASC,aACP,GAAIpE,EAAKwB,MAAMlB,YAAYyB,QAAUzC,EAAM+E,QAAS,CAClD,MAAMC,EAAOvD,GAASS,MAAMC,QAAQ8C,GAAWvE,EAAKwB,MAAMlB,YAAYkE,SAASD,EAAOE,OAWtF,GAPAzE,EAAKwB,MAAMb,UAAY2D,EAAKI,KAAKhD,IACxB,IACFA,EACHyB,YAAanD,EAAKwB,MAAMnB,aACxBF,MAAOH,EAAKwB,MAAMrB,UAGlBb,EAAMqF,QAAS,CAEjB,MAAM5B,EAAS,CACbX,MAAOxC,EAAUwC,MACjBC,MAAOzC,EAAUyC,MACjBuC,OAAQtF,EAAMsF,OACdC,QAASvF,EAAMuF,QACf5C,MAAO,CACL,CACE6C,MAAO,OAAApB,EAAK1D,EAAAwB,MAAMb,UAAU,SAAI,EAAA+C,EAAAoB,MAChCL,IAAK,OAAAb,EAAK5D,EAAAwB,MAAMb,UAAU,SAAI,EAAAiD,EAAAa,IAC9BM,QAASzF,EAAMyF,WAIrB7C,EAAQ8C,YAAYjC,GAAQ1B,MAAMC,IACf,IAAbA,EAAIK,OACIgB,EAAAsC,QAAQvF,EAAE,2BACdF,EAAA,SAAUQ,EAAKwB,MAAMlB,aAClB4E,KAAA,GAEZ,MAEK1F,EAAA,UAAWQ,EAAKwB,OACb0D,IACX,CACF,CAEF,SAASA,KACPpB,GAAUtC,OAAQ,CAAA,+0DAzDdxB,EAAKwB,MAAMnB,eAAiB8E,EAAYC,IAC1CpF,EAAKwB,MAAMrB,MAAQ,GAEdH,EAAAwB,MAAMrB,MAAQyB,EAAUyD,QAEtBrD,kcA1DUsD,SACdtF,EAAAwB,MAAMlB,YAAYiF,OAAOvF,EAAKwB,MAAMlB,YAAYkF,QAAQF,GAAM,GADrE,IAAqBA,iYAIJnB,IACX7E,EAAM+E,QAAU,GAAKrE,EAAKwB,MAAMlB,YAAYyB,OAASzC,EAAM+E,SACnD1B,EAAA8C,QAAQ/F,EAAE,mBAAoB,CAAEgG,IAAKpG,EAAM+E,WAChDrE,EAAAwB,MAAMlB,YAAcN,EAAKwB,MAAMlB,YAAYqF,MAAM,EAAGrG,EAAM+E,UACxD,IAEHtD,GAASS,MAAMO,OAAS/B,EAAKwB,MAAMlB,YAAYyB,OACjDhB,GAASS,MAAMoE,QAAQ,CACrBnB,IAAKN,EAAIM,IACTK,MAAOX,EAAIW,MACX7E,OAAQD,EAAKwB,MAAMvB,OACnBC,OAAQF,EAAKwB,MAAMtB,SAIrBa,GAASS,MAAQT,GAASS,MAAMC,QAAQC,GAC/B1B,EAAKwB,MAAMlB,YAAYkE,SAAS9C,EAAK+C,QAGzC,GAnBX,IAAiBN"}