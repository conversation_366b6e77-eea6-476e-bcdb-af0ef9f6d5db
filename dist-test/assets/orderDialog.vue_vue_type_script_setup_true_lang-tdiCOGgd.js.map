{"version": 3, "file": "orderDialog.vue_vue_type_script_setup_true_lang-tdiCOGgd.js", "sources": ["../../src/views/order/info/orderDialog.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport Order from './order.vue'\r\n\r\ndefineOptions({\r\n  name: 'OrderInfoIndex',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    tabName?: string\r\n    bookNo?: string\r\n    bookType?: string\r\n    /** 单个订单号或单个预订单号或团队主单号（订单表中bind_code）或联房单号 */\r\n    no?: number | string\r\n    /** 订单类型 order:订单 book:预订单 team:团队主单 merge:联房单 orderList:所有房间单 */\r\n    noType?: string\r\n    tabType?: string\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    noType: '',\r\n    tabName: '',\r\n    tabType: '',\r\n    bookNo: '',\r\n    bookType: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n  reload: []\r\n}>()\r\n\r\nonMounted(() => {})\r\n\r\nconst formRef = ref()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n  emits('reload')\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" width=\"98%\" :close-on-click-modal=\"false\" append-to-body destroy-on-close align-center :show-close=\"false\">\r\n      <Order ref=\"formRef\" v-bind=\"props\" @close=\"onCancel\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["props", "__props", "emits", "__emit", "onMounted", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "onCancel", "value"], "mappings": "6eAOA,MAAMA,EAAQC,EAsBRC,EAAQC,EAMdC,GAAU,SAEV,MAAMC,EAAUC,IACVC,EAAYC,EAAS,CACzBC,IAAM,IACGT,EAAMU,WAEf,GAAAC,CAAIC,GACFV,EAAM,oBAAqBU,EAAG,IAGlC,SAASC,IACPN,EAAUO,OAAQ,EAClBZ,EAAM,SAAQ"}