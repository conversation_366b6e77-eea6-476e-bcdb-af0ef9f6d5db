{"version": 3, "file": "serverTime.api-D89oCqKL.js", "sources": ["../../src/api/modules/system/serverTime.api.ts"], "sourcesContent": ["import api from '../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/system/time'\r\nexport default {\r\n  /**\r\n   * 获取服务器时间\r\n   * @param gcode\r\n   * @param type 0: YYYY-MM-DD HH:MI:SS  1: YYYY-MM-DD 2:HH:mm 3:时间戳\r\n   * @returns\r\n   */\r\n  serverTime: (gcode: string, type: string) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        gcode,\r\n        type,\r\n      },\r\n    }),\r\n}\r\n"], "names": ["serverTimeApi", "serverTime", "gcode", "type", "api", "get", "params"], "mappings": "wCAEA,MACeA,EAAA,CAObC,WAAY,CAACC,EAAeC,IAC1BC,EAAIC,IAAI,4BAAoB,CAC1BC,OAAQ,CACNJ,QACAC"}