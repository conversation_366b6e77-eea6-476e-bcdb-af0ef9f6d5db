# 优惠券列表添加来源列功能实现

## 功能说明
在优惠券列表的状态列后面添加一个"来源"列，显示 `activityName` 字段的数据。

## 修改文件
- `src/views/customer/member/info/components/DetailForm/coupon.vue`

## 已完成的修改

### 1. ✅ 添加国际化文本
在 `<i18n>` 标签中为三种语言添加了"来源"的翻译：

```json
{
  "en": {
    "source": "Source"
  },
  "zh-cn": {
    "source": "来源"
  },
  "km": {
    "source": "ប្រភព"
  }
}
```

### 2. ✅ 添加表格列
在状态列后面添加了新的表格列：

```vue
<el-table-column prop="activityName" :label="t('source')" />
```

## 实现效果

### 表格列顺序
1. 券号 (couponCode)
2. 券名称 (templateName)
3. 券类型 (couponTypeName)
4. 发放时间 (grantTime)
5. 到期日期 (expDate)
6. 订单号 (orderNo)
7. 使用时间 (useTime)
8. 状态 (state) - 显示标签
9. **来源 (activityName)** - 新添加的列

### 数据显示
- 来源列直接显示 `activityName` 字段的值
- 支持多语言显示列标题
- 与现有表格样式保持一致

### 多语言支持
- 中文：来源
- 英文：Source
- 柬埔寨语：ប្រភព

## 技术实现细节

### 国际化实现
使用 Vue I18n 的 `t()` 函数实现多语言支持：
```vue
:label="t('source')"
```

### 数据绑定
直接绑定到数据对象的 `activityName` 属性：
```vue
prop="activityName"
```

### 样式继承
新列自动继承现有表格的样式设置，包括：
- 表头样式：`{ background: '#f5f7fa', color: '#606266' }`
- 高亮当前行：`highlight-current-row`
- 加载状态：`v-loading="data.loading"`

## 数据来源
- 字段名：`activityName`
- 数据来源：后端 API 返回的优惠券列表数据
- 显示位置：状态列右侧

## 状态：✅ 实现完成
优惠券列表已成功添加来源列，显示 `activityName` 字段数据，支持多语言显示。
