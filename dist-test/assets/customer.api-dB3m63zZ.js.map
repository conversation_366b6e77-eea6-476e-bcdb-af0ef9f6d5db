{"version": 3, "file": "customer.api-dB3m63zZ.js", "sources": ["../../src/api/modules/pms/customer/customer.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n/**\r\n * 中介、协议单位，包含创建、更新、删除、获取客历信息及分页查询等功能\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 中介协议单位分页列表\r\n   * @param data\r\n   */\r\n  list: (data: any) => api.get('/admin-api/pms/protocol-agent/page', { params: data }),\r\n\r\n  /**\r\n   * 中介、协议单位列表\r\n   * @param data\r\n   */\r\n  agentList: (data: any) => api.get('/admin-api/pms/protocol-agent/list', { params: data }),\r\n\r\n  /**\r\n   * 创建中介、协议单位\r\n   * @param data\r\n   */\r\n  create: (data: any) => api.post('/admin-api/pms/protocol-agent/create', data),\r\n\r\n  /**\r\n   * 状态修改\r\n   * @param data\r\n   */\r\n  changeStatus: (data: any) => api.put('/admin-api/pms/protocol-agent/update-status', data),\r\n\r\n  /**\r\n   * 详情\r\n   * @param data\r\n   */\r\n  detail: (data: any) => api.get('/admin-api/pms/protocol-agent/get', { params: data }),\r\n\r\n  /**\r\n   * 更新\r\n   * @param data\r\n   */\r\n  edit: (data: any) => api.put('/admin-api/pms/protocol-agent/update', data),\r\n\r\n  /**\r\n   * 获得协议单位,中介列表,只包括代码和名称\r\n   */\r\n  simpleList: (data: any) => api.get('/admin-api/pms/protocol-agent/list-simple', { params: data }),\r\n\r\n  /**\r\n   * 创建中介、协议单位\r\n   * @param data\r\n   */\r\n  checkIn: (data: any) => api.post('/admin-api/pms/order/check-in', data),\r\n}\r\n"], "names": ["customerApi", "list", "data", "api", "get", "params", "agentList", "create", "post", "changeStatus", "put", "detail", "edit", "simpleList", "checkIn"], "mappings": "mCAKA,MAAeA,EAAA,CAMbC,KAAOC,GAAcC,EAAIC,IAAI,qCAAsC,CAAEC,OAAQH,IAM7EI,UAAYJ,GAAcC,EAAIC,IAAI,qCAAsC,CAAEC,OAAQH,IAMlFK,OAASL,GAAcC,EAAIK,KAAK,uCAAwCN,GAMxEO,aAAeP,GAAcC,EAAIO,IAAI,8CAA+CR,GAMpFS,OAAST,GAAcC,EAAIC,IAAI,oCAAqC,CAAEC,OAAQH,IAM9EU,KAAOV,GAAcC,EAAIO,IAAI,uCAAwCR,GAKrEW,WAAaX,GAAcC,EAAIC,IAAI,4CAA6C,CAAEC,OAAQH,IAM1FY,QAAUZ,GAAcC,EAAIK,KAAK,gCAAiCN"}