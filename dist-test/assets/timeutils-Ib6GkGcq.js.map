{"version": 3, "file": "timeutils-Ib6GkGcq.js", "sources": ["../../src/utils/timeutils.ts"], "sourcesContent": ["/**\r\n * 转换成年月日\r\n * @param val 需转换的日期\r\n * @returns 如：2025-1-13\r\n */\r\nexport default function ymdate(val: number | string | Date): string {\r\n  const date = new Date(val)\r\n  const year = date.getFullYear()\r\n  const month = (date.getMonth() + 1).toString().padStart(2, '0')\r\n  const day = date.getDate().toString().padStart(2, '0')\r\n  return `${year}-${month}-${day}`\r\n}\r\n/**\r\n * 转换为ISO格式的日期时间字符串\r\n * @param date  设置UTC时间\r\n * @returns\r\n */\r\nexport function convertToDateTimeString(date: string) {\r\n  const d = new Date(date)\r\n  d.setUTCHours(0, 0, 0, 0) // 设置UTC时间\r\n  return d.toISOString() // 转换为ISO格式的日期时间字符串\r\n}\r\n/**\r\n * 转换成年月日时分秒\r\n * @param val 需转换的日期\r\n * @returns 如：2025-1-13 00:00:00\r\n */\r\nexport function ymdateHMS(val: number | string | Date) {\r\n  const date = new Date(val)\r\n  const year = date.getFullYear()\r\n  const month = (date.getMonth() + 1).toString().padStart(2, '0')\r\n  const day = date.getDate().toString().padStart(2, '0')\r\n  const hour = date.getHours().toString().padStart(2, '0')\r\n  const minute = date.getMinutes().toString().padStart(2, '0')\r\n  const second = date.getSeconds().toString().padStart(2, '0')\r\n  return `${year}-${month}-${day} ${hour}:${minute}:${second}`\r\n}\r\n"], "names": ["ymdate", "val", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "ymdateHMS", "getHours", "getMinutes", "getSeconds"], "mappings": "AAKA,SAAwBA,EAAOC,GACvB,MAAAC,EAAO,IAAIC,KAAKF,GAItB,MAAO,GAHMC,EAAKE,kBACHF,EAAKG,WAAa,GAAGC,WAAWC,SAAS,EAAG,QAC/CL,EAAKM,UAAUF,WAAWC,SAAS,EAAG,MAEpD,CAgBO,SAASE,EAAUR,GAClB,MAAAC,EAAO,IAAIC,KAAKF,GAOf,MAAA,GANMC,EAAKE,kBACHF,EAAKG,WAAa,GAAGC,WAAWC,SAAS,EAAG,QAC/CL,EAAKM,UAAUF,WAAWC,SAAS,EAAG,QACrCL,EAAKQ,WAAWJ,WAAWC,SAAS,EAAG,QACrCL,EAAKS,aAAaL,WAAWC,SAAS,EAAG,QACzCL,EAAKU,aAAaN,WAAWC,SAAS,EAAG,MAE1D"}