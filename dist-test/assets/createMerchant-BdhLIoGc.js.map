{"version": 3, "file": "createMerchant-BdhLIoGc.js", "sources": ["../../src/views/group/base-info/merchant/components/DetailForm/createMerchant.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"hotelName\": \"Hotel Name\",\r\n    \"pleaseEnterHotelName\": \"Please enter hotel name\",\r\n    \"department\": \"Department\",\r\n    \"pleaseSelect\": \"Please select\",\r\n    \"hotelType\": \"Hotel Type\",\r\n    \"brand\": \"Brand\",\r\n    \"city\": \"City\",\r\n    \"telephone\": \"Telephone\",\r\n    \"openDate\": \"Opening Date\",\r\n    \"status\": \"Status\",\r\n    \"services\": \"Services\",\r\n    \"hotelIntro\": \"Hotel Intro\",\r\n    \"createSuccess\": \"Added successfully\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"hotelName\": \"酒店名称\",\r\n    \"pleaseEnterHotelName\": \"请输入酒店名称\",\r\n    \"department\": \"所属部门\",\r\n    \"pleaseSelect\": \"请选择\",\r\n    \"hotelType\": \"酒店类型\",\r\n    \"brand\": \"品牌\",\r\n    \"city\": \"城市\",\r\n    \"telephone\": \"电话\",\r\n    \"openDate\": \"开业日期\",\r\n    \"status\": \"状态\",\r\n    \"services\": \"服务项目\",\r\n    \"hotelIntro\": \"酒店介绍\",\r\n    \"createSuccess\": \"新增成功\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\"\r\n  },\r\n  \"km\": {\r\n    \"hotelName\": \"ឈ្មោះសណ្ឋាគារ\",\r\n    \"pleaseEnterHotelName\": \"សូមបញ្ចូលឈ្មោះសណ្ឋាគារ\",\r\n    \"department\": \"ផ្នែក\",\r\n    \"pleaseSelect\": \"សូមជ្រើសរើស\",\r\n    \"hotelType\": \"ប្រភេទសណ្ឋាគារ\",\r\n    \"brand\": \"ម៉ាក\",\r\n    \"city\": \"ទីក្រុង\",\r\n    \"telephone\": \"ទូរស័ព្ទ\",\r\n    \"openDate\": \"ថ្ងៃបើក\",\r\n    \"status\": \"ស្ថានភាព\",\r\n    \"services\": \"សេវាកម្ម\",\r\n    \"hotelIntro\": \"ការណែនាំសណ្ឋាគារ\",\r\n    \"createSuccess\": \"បន្ថែមដោយជោគជ័យ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n所有翻译已添加完成，模板部分已正确使用 `t()` 函数，无需修改。\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { brandApi, deptApi, dictDataApi, merchantApi } from '@/api/modules/index'\r\nimport { BooleanEnum, DICT_TYPE_HOTEL_SERVICE, DICT_TYPE_HOTEL_STATUS, DICT_TYPE_HOTEL_TYPE } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  hcode: '',\r\n})\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: '',\r\n  hname: '',\r\n  pinyin: '',\r\n  deptCode: '',\r\n  region: [],\r\n  manageType: '',\r\n  brandCode: '',\r\n  state: '',\r\n  pca: [],\r\n  telephone: '',\r\n  openDate: '',\r\n  intro: '',\r\n  gpsX: '',\r\n  gpsY: '',\r\n  services: [],\r\n  pics: [],\r\n  isActive: BooleanEnum.YES,\r\n  isAuth: BooleanEnum.YES,\r\n  ext: {\r\n    serv: [],\r\n  },\r\n})\r\nconst formRules = ref<FormRules>({\r\n  hname: [{ required: true, message: t('pleaseEnterHotelName'), trigger: 'blur' }],\r\n  deptCode: [{ required: true, message: t('pleaseSelect'), trigger: 'blur' }],\r\n  brandCode: [{ required: true, message: t('pleaseSelect'), trigger: 'blur' }],\r\n  state: [{ required: true, message: t('pleaseSelect'), trigger: 'blur' }],\r\n  pca: [{ required: true, message: t('pleaseSelect'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  getBrandList()\r\n  getDept()\r\n  getConstants()\r\n})\r\n\r\n// 服务项目\r\nconst allServices = ref<DictDataModel[]>([])\r\n// 酒店类型列表\r\nconst hotelTypeList = ref<DictDataModel[]>([])\r\n// 状态\r\nconst statusList = ref<DictDataModel[]>([])\r\n// 常量里包括多个\r\nconst dictTypes = [DICT_TYPE_HOTEL_SERVICE, DICT_TYPE_HOTEL_TYPE, DICT_TYPE_HOTEL_STATUS]\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    allServices.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_HOTEL_SERVICE)\r\n    hotelTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_HOTEL_TYPE)\r\n    statusList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_HOTEL_STATUS)\r\n  })\r\n}\r\n\r\n// 获取集团\r\nconst deptList = ref<{ id: number; name: string }[]>([])\r\nfunction getDept() {\r\n  deptApi.getSimpleDeptList({ gcode: userStore.gcode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      deptList.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n// 所属品牌\r\nconst brandList = ref<{ brandCode: string; brandName: string }[]>([])\r\nfunction getBrandList() {\r\n  brandApi.getGroupBrands(userStore.gcode).then((res: any) => {\r\n    if (res.code === 0) {\r\n      brandList.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            merchantApi.create(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('createSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"120px\" label-suffix=\"：\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('hotelName')\" prop=\"hname\">\r\n            <el-input v-model=\"form.hname\" :placeholder=\"t('pleaseEnterHotelName')\" maxlength=\"30\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('department')\" prop=\"deptCode\">\r\n            <el-select v-model=\"form.deptCode\" clearable :placeholder=\"t('pleaseSelect')\">\r\n              <el-option v-for=\"item in deptList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('hotelType')\" prop=\"manageType\">\r\n            <el-select v-model=\"form.manageType\" clearable :placeholder=\"t('pleaseSelect')\">\r\n              <el-option v-for=\"item in hotelTypeList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('brand')\" prop=\"brandCode\">\r\n            <el-select v-model=\"form.brandCode\" clearable :placeholder=\"t('pleaseSelect')\">\r\n              <el-option v-for=\"item in brandList\" :key=\"item.brandCode\" :label=\"item.brandName\" :value=\"item.brandCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('city')\" prop=\"pca\">\r\n            <pcas-cascader v-model=\"form.pca\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('telephone')\" prop=\"telephone\">\r\n            <el-input v-model=\"form.telephone\" maxlength=\"20\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('openDate')\" prop=\"openDate\">\r\n            <el-date-picker v-model=\"form.openDate\" type=\"date\" :placeholder=\"t('pleaseSelect')\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"8\">\r\n          <el-form-item :label=\"t('status')\" prop=\"state\">\r\n            <el-select v-model=\"form.state\" clearable :placeholder=\"t('pleaseSelect')\">\r\n              <el-option v-for=\"item in statusList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <!--        <el-col :md=\"8\"> -->\r\n        <!--          <el-form-item label=\"经纬度\"> -->\r\n        <!--            <el-icon class=\"i-ep:plus\" @click=\"\" style=\"cursor: pointer;\"/> -->\r\n        <!--          </el-form-item> -->\r\n        <!--        </el-col> -->\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('services')\">\r\n            <el-checkbox-group v-model=\"form.ext.serv\">\r\n              <el-checkbox v-for=\"item in allServices\" :key=\"item.value\" :value=\"item.value\">\r\n                {{ item.label }}\r\n              </el-checkbox>\r\n            </el-checkbox-group>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('hotelIntro')\">\r\n            <el-input v-model=\"form.intro\" clearable type=\"textarea\" :rows=\"3\" maxlength=\"255\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <!--      <el-row :gutter=\"20\"> -->\r\n      <!--        <el-col :md=\"24\"> -->\r\n      <!--          <el-form-item label=\"酒店图片\"> -->\r\n      <!--            <images-upload v-model:url=\"form.pics\" -->\r\n      <!--                           action=\"https://console-mock.apipost.cn/app/mock/project/1f50f1da-5189-4282-d3c7-c133a514c5a8/upload/image\" -->\r\n      <!--                           name=\"image\" @on-success=\"updateSuccess\"/> -->\r\n      <!--          </el-form-item> -->\r\n      <!--        </el-col> -->\r\n      <!--      </el-row> -->\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "formRef", "form", "gcode", "hcode", "hname", "pinyin", "deptCode", "region", "manageType", "brandCode", "state", "pca", "telephone", "openDate", "intro", "gpsX", "gpsY", "services", "pics", "isActive", "BooleanEnum", "YES", "isAuth", "ext", "serv", "formRules", "required", "message", "trigger", "onMounted", "brandApi", "getGroupBrands", "then", "res", "code", "brandList", "value", "data", "<PERSON>pt<PERSON><PERSON>", "getSimpleDeptList", "deptList", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "allServices", "filter", "item", "dictType", "DICT_TYPE_HOTEL_SERVICE", "hotelTypeList", "DICT_TYPE_HOTEL_TYPE", "statusList", "DICT_TYPE_HOTEL_STATUS", "__expose", "submit", "Promise", "resolve", "validate", "valid", "merchantApi", "create", "ElMessage", "success", "center", "error", "msg"], "mappings": "sxCAsEA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,MAAOR,EAAUQ,MACjBC,MAAO,GACPC,MAAO,GACPC,OAAQ,GACRC,SAAU,GACVC,OAAQ,GACRC,WAAY,GACZC,UAAW,GACXC,MAAO,GACPC,IAAK,GACLC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,SAAUC,EAAYC,IACtBC,OAAQF,EAAYC,IACpBE,IAAK,CACHC,KAAM,MAGJC,EAAY1B,EAAe,CAC/BK,MAAO,CAAC,CAAEsB,UAAU,EAAMC,QAAS/B,EAAE,wBAAyBgC,QAAS,SACvEtB,SAAU,CAAC,CAAEoB,UAAU,EAAMC,QAAS/B,EAAE,gBAAiBgC,QAAS,SAClEnB,UAAW,CAAC,CAAEiB,UAAU,EAAMC,QAAS/B,EAAE,gBAAiBgC,QAAS,SACnElB,MAAO,CAAC,CAAEgB,UAAU,EAAMC,QAAS/B,EAAE,gBAAiBgC,QAAS,SAC/DjB,IAAK,CAAC,CAAEe,UAAU,EAAMC,QAAS/B,EAAE,gBAAiBgC,QAAS,WAG/DC,GAAU,KAmCRC,EAASC,eAAerC,EAAUQ,OAAO8B,MAAMC,IAC5B,IAAbA,EAAIC,OACNC,EAAUC,MAAQH,EAAII,KAAA,IAZlBC,EAAAC,kBAAkB,CAAErC,MAAOR,EAAUQ,QAAS8B,MAAMC,IACzC,IAAbA,EAAIC,OACNM,EAASJ,MAAQH,EAAII,KAAA,IAZzBI,EAAYC,iBAAiBC,GAAWX,MAAMC,IAChCW,EAAAR,MAAQH,EAAII,KAAKQ,QAAQC,GAAcA,EAAKC,WAAaC,IACvDC,EAAAb,MAAQH,EAAII,KAAKQ,QAAQC,GAAcA,EAAKC,WAAaG,IAC5DC,EAAAf,MAAQH,EAAII,KAAKQ,QAAQC,GAAcA,EAAKC,WAAaK,GAAsB,GAf/E,IAIT,MAAAR,EAAc7C,EAAqB,IAEnCkD,EAAgBlD,EAAqB,IAErCoD,EAAapD,EAAqB,IAElC4C,EAAY,CAACK,EAAyBE,EAAsBE,GAU5D,MAAAZ,EAAWzC,EAAoC,IAU/C,MAAAoC,EAAYpC,EAAgD,WASrDsD,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBxD,EAAQoC,OACNpC,EAAQoC,MAAMqB,UAAUC,IAClBA,GACFC,EAAYC,OAAO3D,EAAKmC,OAAOJ,MAAMC,IAClB,IAAbA,EAAIC,MACN2B,EAAUC,QAAQ,CAChBnC,QAAS/B,EAAE,iBACXmE,QAAQ,IAEFP,KAERK,EAAUG,MAAM,CACdrC,QAASM,EAAIgC,IACbF,QAAQ,GACT,GAEJ,GAEJ"}