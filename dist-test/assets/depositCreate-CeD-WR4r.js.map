{"version": 3, "file": "depositCreate-CeD-WR4r.js", "sources": ["../../src/views/room/goods/deposit/components/DetailForm/depositCreate.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"deposit\": {\r\n        \"create\": {\r\n          \"validation\": {\r\n            \"goods\": \"Please enter deposit items\",\r\n            \"depositor\": \"Please enter depositor name\"\r\n          },\r\n          \"success\": {\r\n            \"add\": \"Added successfully\"\r\n          },\r\n          \"labels\": {\r\n            \"goods\": \"Item Name\",\r\n            \"depositor\": \"Depositor Name\",\r\n            \"depositPhone\": \"Depositor Phone\",\r\n            \"depositCode\": \"Deposit Code\",\r\n            \"roomNo\": \"Room No.\",\r\n            \"remark\": \"Remark\"\r\n          },\r\n          \"placeholders\": {\r\n            \"goods\": \"Please enter item information\",\r\n            \"depositor\": \"Please enter depositor\",\r\n            \"depositPhone\": \"Please enter depositor phone\",\r\n            \"depositCode\": \"Please enter deposit code\",\r\n            \"roomNo\": \"Please enter room number\",\r\n            \"remark\": \"Please enter key features of the deposit item, such as color, size, quantity, etc.\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"zh-cn\": {\r\n      \"deposit\": {\r\n        \"create\": {\r\n          \"validation\": {\r\n            \"goods\": \"请输入寄存物品\",\r\n            \"depositor\": \"请输入寄存人姓名\"\r\n          },\r\n          \"success\": {\r\n            \"add\": \"添加成功\"\r\n          },\r\n          \"labels\": {\r\n            \"goods\": \"物品名称\",\r\n            \"depositor\": \"寄存人姓名\",\r\n            \"depositPhone\": \"寄存人电话\",\r\n            \"depositCode\": \"寄存凭证号\",\r\n            \"roomNo\": \"房号\",\r\n            \"remark\": \"备注\"\r\n          },\r\n          \"placeholders\": {\r\n            \"goods\": \"请输入物品信息\",\r\n            \"depositor\": \"请输入寄存人\",\r\n            \"depositPhone\": \"请输入寄存人电话\",\r\n            \"depositCode\": \"请输入寄存凭证号\",\r\n            \"roomNo\": \"请输入房号\",\r\n            \"remark\": \"请输入寄存物品的关键特征，如颜色、大小、数量等\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"km\": {\r\n      \"deposit\": {\r\n        \"create\": {\r\n          \"validation\": {\r\n            \"goods\": \"សូមបញ្ចូលវត្ថុដាក់បញ្ចាំ\",\r\n            \"depositor\": \"សូមបញ្ចូលឈ្មោះអ្នកដាក់បញ្ចាំ\"\r\n          },\r\n          \"success\": {\r\n            \"add\": \"បានបន្ថែមដោយជោគជ័យ\"\r\n          },\r\n          \"labels\": {\r\n            \"goods\": \"ឈ្មោះវត្ថុ\",\r\n            \"depositor\": \"ឈ្មោះអ្នកដាក់បញ្ចាំ\",\r\n            \"depositPhone\": \"លេខទូរស័ព្ទអ្នកដាក់បញ្ចាំ\",\r\n            \"depositCode\": \"លេខបញ្ជាក់ការដាក់បញ្ចាំ\",\r\n            \"roomNo\": \"លេខបន្ទប់\",\r\n            \"remark\": \"ចំណាំ\"\r\n          },\r\n          \"placeholders\": {\r\n            \"goods\": \"សូមបញ្ចូលព័ត៌មានវត្ថុ\",\r\n            \"depositor\": \"សូមបញ្ចូលអ្នកដាក់បញ្ចាំ\",\r\n            \"depositPhone\": \"សូមបញ្ចូលលេខទូរស័ព្ទអ្នកដាក់បញ្ចាំ\",\r\n            \"depositCode\": \"សូមបញ្ចូលលេខបញ្ជាក់ការដាក់បញ្ចាំ\",\r\n            \"roomNo\": \"សូមបញ្ចូលលេខបន្ទប់\",\r\n            \"remark\": \"សូមបញ្ចូលលក្ខណៈសំខាន់នៃវត្ថុដាក់បញ្ចាំ ដូចជាពណ៌ ទំហំ បរិមាណ ជាដើម\"\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { depositApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n  /** 房号 */\r\n  rNo: '',\r\n  /** 寄存人姓名 */\r\n  depositor: '',\r\n  /** 寄存凭证号 */\r\n  depositCode: '',\r\n  /** 寄存物品 */\r\n  goods: '',\r\n  /** 寄存时间 */\r\n  depositTime: '',\r\n  /** 寄存操作人 */\r\n  depositOpt: '',\r\n  /** 认领人 */\r\n  claimant: '',\r\n  /** 认领人电话 */\r\n  claimantPhone: '',\r\n  /** 领取操作员 */\r\n  claimOpt: '',\r\n  /** 领取时间 */\r\n  claimTime: '',\r\n  /** 状态 0：未领取 1：已领取 */\r\n  state: '0',\r\n  remark: '',\r\n  /** 寄存人电话 */\r\n  depositPhone: '',\r\n})\r\n\r\nonMounted(() => {})\r\n\r\nconst formRules = ref<FormRules>({\r\n  goods: [{ required: true, message: t('deposit.create.validation.goods'), trigger: 'blur' }],\r\n  depositor: [{ required: true, message: t('deposit.create.validation.depositor'), trigger: 'blur' }],\r\n})\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            depositApi.create(form.value).then(() => {\r\n              ElMessage.success({\r\n                message: t('deposit.create.success.add'),\r\n                center: true,\r\n              })\r\n              resolve()\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"150px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('deposit.create.labels.goods')\" prop=\"goods\">\r\n        <el-input v-model=\"form.goods\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('deposit.create.placeholders.goods')\" maxlength=\"200\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('deposit.create.labels.depositor')\" prop=\"depositor\">\r\n        <el-input v-model=\"form.depositor\" :placeholder=\"t('deposit.create.placeholders.depositor')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('deposit.create.labels.depositPhone')\" prop=\"depositPhone\">\r\n        <el-input v-model=\"form.depositPhone\" :placeholder=\"t('deposit.create.placeholders.depositPhone')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('deposit.create.labels.depositCode')\" prop=\"depositCode\">\r\n        <el-input v-model=\"form.depositCode\" :placeholder=\"t('deposit.create.placeholders.depositCode')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('deposit.create.labels.roomNo')\">\r\n        <el-input v-model=\"form.rNo\" :placeholder=\"t('deposit.create.placeholders.roomNo')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('deposit.create.labels.remark')\" prop=\"remark\">\r\n        <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('deposit.create.placeholders.remark')\" maxlength=\"200\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "hcode", "rNo", "depositor", "depositCode", "goods", "depositTime", "depositOpt", "claimant", "claimant<PERSON><PERSON>", "claimOpt", "claimTime", "state", "remark", "depositPhone", "onMounted", "formRules", "required", "message", "trigger", "__expose", "submit", "Promise", "resolve", "value", "validate", "valid", "depositApi", "create", "then", "ElMessage", "success", "center"], "mappings": "wbAmGM,MAAAA,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,MAAON,EAAUM,MAEjBC,MAAOP,EAAUO,MAEjBC,IAAK,GAELC,UAAW,GAEXC,YAAa,GAEbC,MAAO,GAEPC,YAAa,GAEbC,WAAY,GAEZC,SAAU,GAEVC,cAAe,GAEfC,SAAU,GAEVC,UAAW,GAEXC,MAAO,IACPC,OAAQ,GAERC,aAAc,KAGhBC,GAAU,SAEV,MAAMC,EAAYnB,EAAe,CAC/BQ,MAAO,CAAC,CAAEY,UAAU,EAAMC,QAAS1B,EAAE,mCAAoC2B,QAAS,SAClFhB,UAAW,CAAC,CAAEc,UAAU,EAAMC,QAAS1B,EAAE,uCAAwC2B,QAAS,kBAG/EC,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBzB,EAAQ0B,OACN1B,EAAQ0B,MAAMC,UAAUC,IAClBA,GACFC,EAAWC,OAAO7B,EAAKyB,OAAOK,MAAK,KACjCC,EAAUC,QAAQ,CAChBb,QAAS1B,EAAE,8BACXwC,QAAQ,IAEFT,GAAA,GACT,GAEJ"}