{"version": 3, "file": "priceStrategy.api-Deh17XpK.js", "sources": ["../../src/api/modules/pms/price/priceStrategy.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/price-strategy'\r\n/**\r\n * 房价策略接口\r\n */\r\nexport default {\r\n  /**\r\n   * 获得策略看板列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getPriceStrategyBoardList: (data: any) =>\r\n    api.get(`${BASE_PATH}/list-board`, {\r\n      params: data,\r\n    }),\r\n  /**\r\n   * 获得房价策略列表\r\n   * @param data\r\n   */\r\n  getPriceStrategyList: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    rtCode?: string // 房型代码\r\n    guestSrcType?: string // 渠道代码\r\n    strategyName?: string\r\n    isEnable?: string\r\n    isG?: string\r\n  }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n  /**\r\n   * 创建房价策略\r\n   * @param data\r\n   */\r\n  // createPriceStrategy: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n  createPriceStrategy: (data: any) => api.post('/admin-api/pms/price-strategy/create', data),\r\n  /**\r\n   * 获得房价策略\r\n   * @param id\r\n   */\r\n  getPriceStrategy: (strategyCode: string) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        strategyCode,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 更新房价策略\r\n   * @param data\r\n   */\r\n  updatePriceStrategy: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n\r\n  /**\r\n   * 更新房价策略状态\r\n   * @param data\r\n   */\r\n  updatePriceStrategyStatus: (data: { strategyCode: string, isEnable: string }) =>\r\n    api.put(`${BASE_PATH}/update-status`, data),\r\n}\r\n"], "names": ["BASE_PATH", "priceStrategyApi", "getPriceStrategyBoardList", "data", "api", "get", "params", "getPriceStrategyList", "createPriceStrategy", "post", "getPriceStrategy", "strategyCode", "updatePriceStrategy", "put", "updatePriceStrategyStatus"], "mappings": "wCAEA,MAAMA,EAAY,+BAIHC,EAAA,CAMbC,0BAA4BC,GAC1BC,EAAIC,IAAI,GAAGL,eAAwB,CACjCM,OAAQH,IAMZI,qBAAuBJ,GASrBC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAOZK,oBAAsBL,GAAcC,EAAIK,KAAK,uCAAwCN,GAKrFO,iBAAmBC,GACjBP,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CACNK,kBAQNC,oBAAsBT,GAAcC,EAAIS,IAAI,GAAGb,WAAoBG,GAMnEW,0BAA4BX,GAC1BC,EAAIS,IAAI,GAAGb,kBAA2BG"}