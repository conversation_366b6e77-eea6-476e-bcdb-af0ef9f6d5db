import{a as e}from"./index-CkEhI1Zk.js";const a="admin-api/pms/price-calendar",p={getPriceCalendarList:p=>e.get(`${a}/list`,{params:p}),updateRoomTypeBasePrice:p=>e.put(`${a}/update-base-price`,p,{}),getRoomTypeBasePrices:p=>e.get(`${a}/list-base-price`,{params:p}),getPriceCalendar:p=>e.get(`${a}/get`,{params:p}),updatePriceCalendarUnify:p=>e.put(`${a}/update-unify`,p),updatePriceCalendarAlone:p=>e.put(`${a}/update-alone`,p),updateBasePrice:p=>e.put(`${a}/update-base-price`,p),priceCalendarLogPage:a=>e.get("admin-api/pms/price-calendar-log/page",{params:a})};export{p};
//# sourceMappingURL=priceCalendar.api-DMl1yVkH.js.map
