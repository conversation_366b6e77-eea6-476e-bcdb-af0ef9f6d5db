{"version": 3, "file": "bookDetails.d-D8PR9R8R.js", "sources": ["../../src/views/room/booking/bookDetails.d.ts"], "sourcesContent": ["/** 房型占用详情 */\r\ndeclare namespace bookDetails {\r\n  /** 新窗口打开需要传递的值 */\r\n  interface queryTypes {\r\n    /** 是否全屏 */\r\n    fullscreen: string\r\n    /** 订单号 */\r\n    no?: string | number\r\n    /** 订单类型 */\r\n    noType: string\r\n    /** 打开弹窗为必传 */\r\n    modelValue: string\r\n    /** 显示的tabs页 */\r\n    tabName: string\r\n  }\r\n  /** tabs切换页 */\r\n  interface stateListTypes {\r\n    /** 数值 */\r\n    num: number\r\n    /** 名称 */\r\n    label: string\r\n    /** 值 */\r\n    value: string\r\n  }\r\n  /** 查询列表条件 */\r\n  interface queryParams extends PageQuery, hgCode {\r\n    /** 开始时间 */\r\n    startTime?: string\r\n    /** 结束时间 */\r\n    endTime?: string\r\n    /** 房间代码 */\r\n    rtCode?: string\r\n    /** 日期 */\r\n    date?: string\r\n  }\r\n  /** 返回总数 */\r\n  interface totalType {\r\n    total?: number\r\n    [property: string]: any\r\n  }\r\n  /**\r\n   * 在住订单分页  返回数据\r\n   *\r\n   * orderPagerList\r\n   */\r\n  interface orderPager extends totalType {\r\n    /** 数据 */\r\n    list?: orderPagerList[]\r\n  }\r\n  /** 在住订单分页  列表 */\r\n  interface orderPagerList {\r\n    /** 预订单号 */\r\n    bookNo?: string\r\n    /** 渠道代码 */\r\n    channelCode?: string\r\n    /** 渠道名称 */\r\n    channelName?: string\r\n    /** 入住时间 */\r\n    checkinTime?: string\r\n    /** 入住类型 */\r\n    checkinType?: string\r\n    /** 入住类型名称 */\r\n    checkinTypeName?: string\r\n    /** 创建时间 */\r\n    createTime?: string\r\n    /** 集团代码 */\r\n    gcode?: string\r\n    /** 客源类型 */\r\n    guestSrcType?: string\r\n    /** 客源类型名称 */\r\n    guestSrcTypeName?: string\r\n    /** 门店代码 */\r\n    hcode?: string\r\n    /** 钟点房转全天房 0:否 1:是 */\r\n    hourToFull?: string\r\n    /** id */\r\n    id?: number\r\n    /** 证件号 */\r\n    idNo?: string\r\n    /** 证件类型 */\r\n    idType?: string\r\n    /** 客人姓名 */\r\n    name?: string\r\n    /** 订单号 */\r\n    orderNo?: string\r\n    /** 外部订单号;OTA的订单号 */\r\n    outOrderNo?: string\r\n    /** 订单来源名称 */\r\n    orderSourceName?: string\r\n    /** 订单来源 */\r\n    orderSource?: string\r\n    /** 电话 */\r\n    phone?: string\r\n    /** 预抵时间 */\r\n    planCheckinTime?: string\r\n    /** 预离时间 */\r\n    planCheckoutTime?: string\r\n    /** 房价 */\r\n    price?: number\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n    /** 订单状态 */\r\n    state?: string\r\n    /** 团队代码 */\r\n    teamCode?: string\r\n    /** 团队名称 */\r\n    teamName?: string\r\n    /** 团队类型 */\r\n    teamType?: string\r\n    /** 客单代码 */\r\n    togetherCode?: string\r\n    [property: string]: any\r\n  }\r\n  /**\r\n   * 预抵订单分页  返回数据\r\n   *\r\n   * pageBookList\r\n   */\r\n  interface pageBook extends totalType {\r\n    /** 数据 */\r\n    list?: pageBookList[]\r\n  }\r\n  /**\r\n   * 预抵订单分页  列表\r\n   *\r\n   * pageBookList\r\n   */\r\n  interface pageBookList {\r\n    /** 预订单号 */\r\n    bookNo?: string\r\n    /** 渠道代码 */\r\n    channelCode?: string\r\n    /** 渠道名称 */\r\n    channelName?: string\r\n    /** 入住时间 */\r\n    checkinTime?: string\r\n    /** 入住类型 */\r\n    checkinType?: string\r\n    /** 入住类型名称 */\r\n    checkinTypeName?: string\r\n    /** 创建时间 */\r\n    createTime?: string\r\n    /** 集团代码 */\r\n    gcode?: string\r\n    /** 客源类型 */\r\n    guestSrcType?: string\r\n    /** 客源类型名称 */\r\n    guestSrcTypeName?: string\r\n    /** 门店代码 */\r\n    hcode?: string\r\n    /**\r\n     * 钟点房转全天房 0:否 1:是\r\n     */\r\n    hourToFull?: string\r\n    /** id */\r\n    id?: number\r\n    /** 证件号 */\r\n    idNo?: string\r\n    /** 证件类型 */\r\n    idType?: string\r\n    /** 客人姓名\r\n     */\r\n    name?: string\r\n    /** 订单号 */\r\n    orderNo?: string\r\n    /** 外部订单号;OTA的订单号 */\r\n    outOrderNo?: string\r\n    /** 电话 */\r\n    phone?: string\r\n    /** 预抵时间 */\r\n    planCheckinTime?: string\r\n    /** 预离时间 */\r\n    planCheckoutTime?: string\r\n    /** 房价 */\r\n    price?: number\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n    /** 订单状态 */\r\n    state?: string\r\n    /** 团队代码 */\r\n    teamCode?: string\r\n    /** 团队名称 */\r\n    teamName?: string\r\n    /** 团队类型 */\r\n    teamType?: string\r\n    /** 客单代码 */\r\n    togetherCode?: string\r\n    [property: string]: any\r\n  }\r\n\r\n  /**\r\n   * 预离列表  返回数据\r\n   *\r\n   * todayPlanCheckOut\r\n   */\r\n  interface todayPlanCheckOut extends totalType {\r\n    /**\r\n     * 数据\r\n     */\r\n    list?: todayPlanCheckOutList[]\r\n  }\r\n\r\n  /**\r\n   * 预离列表  列表\r\n   *\r\n   * todayPlanCheckOutList\r\n   */\r\n  interface todayPlanCheckOutList {\r\n    /** 预订单号 */\r\n    bookNo?: string\r\n    /** 渠道代码 */\r\n    channelCode?: string\r\n    /** 渠道名称 */\r\n    channelName?: string\r\n    /** 入住时间 */\r\n    checkinTime?: string\r\n    /** 入住类型 */\r\n    checkinType?: string\r\n    /** 入住类型名称 */\r\n    checkinTypeName?: string\r\n    /** 创建时间 */\r\n    createTime?: string\r\n    /** 集团代码 */\r\n    gcode?: string\r\n    /** 客源类型 */\r\n    guestSrcType?: string\r\n    /** 客源类型名称 */\r\n    guestSrcTypeName?: string\r\n    /** 门店代码 */\r\n    hcode?: string\r\n    /** 钟点房转全天房 0:否 1:是 */\r\n    hourToFull?: string\r\n    /** id */\r\n    id?: number\r\n    /** 证件号 */\r\n    idNo?: string\r\n    /** 证件类型 */\r\n    idType?: string\r\n    /** 客人姓名 */\r\n    name?: string\r\n    /** 订单号 */\r\n    orderNo?: string\r\n    /** 外部订单号;OTA的订单号 */\r\n    outOrderNo?: string\r\n    /** 电话 */\r\n    phone?: string\r\n    /** 预抵时间 */\r\n    planCheckinTime?: string\r\n    /** 预离时间 */\r\n    planCheckoutTime?: string\r\n    /** 房价 */\r\n    price?: number\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n    /** 订单状态 */\r\n    state?: string\r\n    /** 团队代码 */\r\n    teamCode?: string\r\n    /** 团队名称 */\r\n    teamName?: string\r\n    /** 团队类型 */\r\n    teamType?: string\r\n    /** 客单代码 */\r\n    togetherCode?: string\r\n    [property: string]: any\r\n  }\r\n\r\n  /**\r\n   * 维修列表  返回数据\r\n   *\r\n   * getRepairRoom\r\n   */\r\n  interface getRepairRoom extends totalType {\r\n    /**\r\n     * 数据\r\n     */\r\n    list?: getRepairRoomList[]\r\n  }\r\n\r\n  /**\r\n   * 维修列表  列表\r\n   *\r\n   * getRepairRoomList\r\n   */\r\n  interface getRepairRoomList {\r\n    /** 床位数 */\r\n    bedNum?: number\r\n    /** 楼栋代码 */\r\n    buildingCode?: string\r\n    /** 楼栋名称 */\r\n    buildingName?: string\r\n    /** 创建时间 */\r\n    createTime?: string\r\n    /** 分机号 */\r\n    extNum?: string\r\n    /** 房间特征 */\r\n    feature?: string[]\r\n    /** 楼层代码 */\r\n    floorCode?: string\r\n    /** 楼层名称 */\r\n    floorName?: string\r\n    /** 集团代码 */\r\n    gcode?: string\r\n    /** 客控账号 */\r\n    guestControlAccount?: string\r\n    /** 门店代码 */\r\n    hcode?: string\r\n    /** id */\r\n    id?: number\r\n    /** 是否查房;是否查房(0 未查房 1 查房中 2 ok房) */\r\n    isChecked?: string\r\n    /** 是否已扫;是否已扫(0.未扫 1.已扫 2.未通过) */\r\n    isCleaned?: string\r\n    /** 是否启用;0: 停用 1:启用 */\r\n    isEnable?: string\r\n    /** 是否房间客控 */\r\n    isGuestControl?: string\r\n    /** 是否锁房;0: 否 1:是 */\r\n    isLocked?: string\r\n    /** 是否网络锁门 */\r\n    isNetworkLock?: string\r\n    /** 是否网络开门 */\r\n    isNetworkOpenDoor?: string\r\n    /** 是否网络取电 */\r\n    isNetworkPowerControl?: string\r\n    /** 最后一次入住离店时间;用于计算多久没有住人 */\r\n    lastCheckoutTime?: string\r\n    /** 锁房原因 */\r\n    lockedReason?: string\r\n    /** 房间锁号 */\r\n    lockNo?: string\r\n    /** 网络锁号 */\r\n    networkLockNo?: string\r\n    /** 网络取电地址 */\r\n    networkPowerAddress?: string\r\n    /** 所属业主 */\r\n    owner?: string\r\n    /** 房间代码 */\r\n    rCode?: string\r\n    /** 备注 */\r\n    remark?: string\r\n    /** 维修结束时间;当状态是维修时 */\r\n    repairEndTime?: string\r\n    /** 维修原因;当状态是维修时 */\r\n    repairReason?: string\r\n    /** 维修开始时间;当状态是维修时 */\r\n    repairStartTime?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n    /** 房态 */\r\n    state?: string\r\n    [property: string]: any\r\n  }\r\n  /** 占用房类型 */\r\n  interface BookConflictRespVO {\r\n    /** 入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队 */\r\n    checkinType?: string\r\n    /** 入住类型名称 */\r\n    checkinTypeName?: string\r\n    /** 结束时间 */\r\n    endTime?: string\r\n    /** 入住人姓名 */\r\n    name?: string\r\n    /** 单号 */\r\n    no?: string\r\n    /** 占用间数 */\r\n    num?: number\r\n    /** 入住人电话 */\r\n    phone?: string\r\n    /** 房间代码 */\r\n    rCode?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称  */\r\n    rtName?: string\r\n    /** 开始时间 */\r\n    startTime?: string\r\n    /** 状态 */\r\n    state?: string\r\n  }\r\n}\r\nexport = bookDetails\r\n"], "names": ["require_bookDetails_d_048", "exports", "module", "bookDetails"], "mappings": "qCA+YAA,KAAA,CAAA,mCAAAC,EAAAC,GAAAA,EAASD,QAAAE,WAAA"}