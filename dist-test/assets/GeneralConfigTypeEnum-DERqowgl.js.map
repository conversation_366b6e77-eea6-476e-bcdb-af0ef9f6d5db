{"version": 3, "file": "GeneralConfigTypeEnum-DERqowgl.js", "sources": ["../../src/models/dict/GeneralConfigTypeEnum.ts"], "sourcesContent": ["export enum GeneralConfigTypeEnum {\r\n  CANCEL_REASON = 'cancel_reason',\r\n  CHANGE_REASON = 'change_reason',\r\n  OFFSET_REASON = 'offset_reason',\r\n  LOCK_REASON = 'lock_reason',\r\n  REPAIR_REASON = 'repair_reason',\r\n\r\n  AGENT_LEVEL = 'agent_level',\r\n  PROTOCOL_LEVEL = 'protocol_level',\r\n  BROKERAGE_LEVEL = 'brokerage_level',\r\n\r\n  PAY_ACCOUNT_TYPE = 'pay_account_type',\r\n  CONSUME_ACCOUNT_TYPE = 'consume_account_type',\r\n  PAY_ACCOUNT = 'pay_account',\r\n  CONSUME_ACCOUNT = 'consume_account',\r\n\r\n  ALL_CREDIT_ACCOUNT = 'all_credit_account',\r\n  RECORD_CREDIT_ACCOUNT = 'record_credit_account',\r\n  RECORD_CASH_CREDIT_ACCOUNT = 'record_cash_credit_account',\r\n  REFUND_CASH_CREDIT_ACCOUNT = 'refund_cash_credit_account',\r\n  DEPOSIT_CREDIT_ACCOUNT = 'deposit_credit_account',\r\n  SECURITY_CREDIT_ACCOUNT = 'security_credit_account',\r\n  ALI_CREDIT_ACCOUNT = 'ali_credit_account',\r\n  PAY_CREDIT_ACCOUNT = 'pay_credit_account',\r\n  MEMBER_CREDIT_ACCOUNT = 'member_credit_account',\r\n  UP_CREDIT_ACCOUNT = 'up_credit_account',\r\n  CANCEL_AR_CREDIT_ACCOUNT = 'cancel_ar_credit_account',\r\n  CANCEL_AR_REFUND_CREDIT_ACCOUNT = 'cancel_ar_refund_credit_account',\r\n\r\n  ALL_DEBIT_ACCOUNT = 'all_debit_account',\r\n  ADJUST_DEBIT_ACCOUNT = 'adjust_debit_account',\r\n  CASH_DEBIT_ACCOUNT = 'cash_debit_account',\r\n  HAND_DEBIT_ACCOUNT = 'hand_debit_account',\r\n\r\n  SERVICE = 'service',\r\n\r\n  BIZ_DATE = 'biz_date',\r\n  CHECKIN_TIME = 'default_plan_checkin_time',\r\n\r\n  LATEST_CHECK_OUT_TIME = 'latest_check_out_time',\r\n\r\n  HOW_MINUTE = 'how_minute',\r\n}\r\n"], "names": ["GeneralConfigTypeEnum"], "mappings": "AAAY,IAAAA,GAAAA,IACVA,EAAgB,cAAA,gBAChBA,EAAgB,cAAA,gBAChBA,EAAgB,cAAA,gBAChBA,EAAc,YAAA,cACdA,EAAgB,cAAA,gBAEhBA,EAAc,YAAA,cACdA,EAAiB,eAAA,iBACjBA,EAAkB,gBAAA,kBAElBA,EAAmB,iBAAA,mBACnBA,EAAuB,qBAAA,uBACvBA,EAAc,YAAA,cACdA,EAAkB,gBAAA,kBAElBA,EAAqB,mBAAA,qBACrBA,EAAwB,sBAAA,wBACxBA,EAA6B,2BAAA,6BAC7BA,EAA6B,2BAAA,6BAC7BA,EAAyB,uBAAA,yBACzBA,EAA0B,wBAAA,0BAC1BA,EAAqB,mBAAA,qBACrBA,EAAqB,mBAAA,qBACrBA,EAAwB,sBAAA,wBACxBA,EAAoB,kBAAA,oBACpBA,EAA2B,yBAAA,2BAC3BA,EAAkC,gCAAA,kCAElCA,EAAoB,kBAAA,oBACpBA,EAAuB,qBAAA,uBACvBA,EAAqB,mBAAA,qBACrBA,EAAqB,mBAAA,qBAErBA,EAAU,QAAA,UAEVA,EAAW,SAAA,WACXA,EAAe,aAAA,4BAEfA,EAAwB,sBAAA,wBAExBA,EAAa,WAAA,aAzCHA,IAAAA,GAAA,CAAA"}