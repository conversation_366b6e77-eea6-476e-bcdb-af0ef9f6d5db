{
  "compilerOptions": {
    "target": "ESNext", // 将代码编译为最新版本的 JS
    "jsx": "preserve", // 在 .tsx文件里支持JSX： "React"或 "Preserve"。查看 JSX。
    "lib": ["ESNext", "DOM", "DOM.Iterable"], //项目依赖的库
    "moduleDetection": "force",
    "useDefineForClassFields": true, // 使用 Object.defineProperty 定义 class 中的属性，而非使用 obj.key = value 的形式定义属性
    "baseUrl": "./", // 解析非相对模块的基地址，默认是当前目录
    "module": "ESNext", // 使用 ES Module 格式打包编译后的文件
    "moduleResolution": "Bundler", // 使用 Node 的模块解析策略
    "paths": {
      "@/*": ["src/*"],
      "#/*": ["src/types/*"]
    },
    "resolveJsonModule": true, //允许导入json模块
    "types": [
      "vite/client",
      "vite-plugin-pages/client",
      "vite-plugin-vue-meta-layouts/client",
      "@intlify/unplugin-vue-i18n/messages",
      "vite-plugin-app-loading/client",
      "element-plus/global"
    ], // 加载的声明文件包
    "composite": true,
    "allowImportingTsExtensions": true,
    "allowJs": false, // 是否对js文件进行编译
    "strict": true, // 启用所有严格的类型检查
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "sourceMap": true, //是否生成目标文件的sourceMap文件
    "esModuleInterop": true, //启用ES模块与非ES模块的转换
    "isolatedModules": true,
    "skipLibCheck": true, // 跳过对 .d.ts 文件的类型检查

    "noLib": false, //编译是否包含默认库文件
    "removeComments": false, // 删除所有注释，除了以 /!*开头的版权信息。
    "allowSyntheticDefaultImports": true, //允许默认导入
    "forceConsistentCasingInFileNames": true, //强制文件名称大小写值一致
    "jsxFactory": "h", // 指定生成目标为react JSX时，使用的JSX工厂函数，比如 React.createElement或 h。
    "jsxFragmentFactory": "Fragment"
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
