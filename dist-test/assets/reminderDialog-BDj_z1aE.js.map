{"version": 3, "file": "reminderDialog-BDj_z1aE.js", "sources": ["../../src/views/order/components/reminderDialog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"title\": \"Payment Reminder\",\r\n    \"guestName\": \"Guest Name\",\r\n    \"roomNo\": \"Room No\",\r\n    \"checkinTime\": \"Check-in Time\",\r\n    \"planCheckoutTime\": \"Plan Checkout Time\",\r\n    \"checkinType\": \"Check-in Type\",\r\n    \"guestSrcType\": \"Guest Source Type\",\r\n    \"levelOrCompany\": \"Level/Company\",\r\n    \"roomPrice\": \"Room Price\",\r\n    \"consumeFee\": \"Consume Fee\",\r\n    \"payFee\": \"Pay Fee\",\r\n    \"preFee\": \"Pre-auth Amount\",\r\n    \"addRoomFee\": \"Add Room Fee\",\r\n    \"arrearsFee\": \"Arrears Fee\",\r\n    \"close\": \"Close\",\r\n    \"loading\": \"Loading...\",\r\n    \"noData\": \"No data\",\r\n    \"filterType\": \"Filter Type\",\r\n    \"singleOrder\": \"Single Order\",\r\n    \"joinedRoom\": \"Joined Room\",\r\n    \"team\": \"Team\",\r\n    \"roomNoSearch\": \"Room No Search\",\r\n    \"roomNoPlaceholder\": \"Enter room number\",\r\n    \"search\": \"Search\",\r\n    \"reset\": \"Reset\",\r\n    \"showTogether\": \"Show Together Guests\",\r\n    \"mainGuest\": \"Main\",\r\n    \"arrearsFormula\": \"Arrears = Consume Fee + Add Room Fee - Pre-auth Amount - Pay Fee\",\r\n    \"addRoomFeeNote\": \"Add Room Fee: Full day room fee for overnight stays on the same day\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"title\": \"催押\",\r\n    \"guestName\": \"客人姓名\",\r\n    \"roomNo\": \"房号\",\r\n    \"checkinTime\": \"入住时间\",\r\n    \"planCheckoutTime\": \"预离时间\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"guestSrcType\": \"客源类型\",\r\n    \"levelOrCompany\": \"会员级别/公司\",\r\n    \"roomPrice\": \"房价\",\r\n    \"consumeFee\": \"消费合计\",\r\n    \"payFee\": \"付款合计\",\r\n    \"preFee\": \"预授权金额\",\r\n    \"addRoomFee\": \"需加收房费\",\r\n    \"arrearsFee\": \"预计欠费\",\r\n    \"close\": \"关闭\",\r\n    \"loading\": \"加载中...\",\r\n    \"noData\": \"暂无数据\",\r\n    \"filterType\": \"筛选类型\",\r\n    \"singleOrder\": \"单订单\",\r\n    \"joinedRoom\": \"联房\",\r\n    \"team\": \"团队\",\r\n    \"roomNoSearch\": \"房号查询\",\r\n    \"roomNoPlaceholder\": \"请输入房号\",\r\n    \"search\": \"查询\",\r\n    \"reset\": \"重置\",\r\n    \"showTogether\": \"显示同住客人\",\r\n    \"mainGuest\": \"主客\",\r\n    \"arrearsFormula\": \"预计欠款=消费总计+需加收房费－预授权金额－付款总计\",\r\n    \"addRoomFeeNote\": \"需加收房费：当天过夜要加收全天的房费\"\r\n  },\r\n  \"km\": {\r\n    \"title\": \"រំលឹកការបង់ប្រាក់\",\r\n    \"guestName\": \"ឈ្មោះភ្ញៀវ\",\r\n    \"roomNo\": \"លេខបន្ទប់\",\r\n    \"checkinTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n    \"planCheckoutTime\": \"ពេលវេលាគ្រោងចាកចេញ\",\r\n    \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n    \"guestSrcType\": \"ប្រភេទប្រភពភ្ញៀវ\",\r\n    \"levelOrCompany\": \"កម្រិត/ក្រុមហ៊ុន\",\r\n    \"roomPrice\": \"តម្លៃបន្ទប់\",\r\n    \"consumeFee\": \"ថ្លៃប្រើប្រាស់សរុប\",\r\n    \"payFee\": \"ថ្លៃបង់សរុប\",\r\n    \"preFee\": \"ចំនួនទឹកប្រាក់អនុញ្ញាតមុន\",\r\n    \"addRoomFee\": \"ថ្លៃបន្ទប់បន្ថែម\",\r\n    \"arrearsFee\": \"ថ្លៃជំពាក់គ្រោង\",\r\n    \"close\": \"បិទ\",\r\n    \"loading\": \"កំពុងផ្ទុក...\",\r\n    \"noData\": \"គ្មានទិន្នន័យ\",\r\n    \"filterType\": \"ប្រភេទតម្រង\",\r\n    \"singleOrder\": \"បញ្ជាទិញតែមួយ\",\r\n    \"joinedRoom\": \"បន្ទប់ភ្ជាប់\",\r\n    \"team\": \"ក្រុម\",\r\n    \"roomNoSearch\": \"ស្វែងរកលេខបន្ទប់\",\r\n    \"roomNoPlaceholder\": \"បញ្ចូលលេខបន្ទប់\",\r\n    \"search\": \"ស្វែងរក\",\r\n    \"reset\": \"កំណត់ឡើងវិញ\",\r\n    \"showTogether\": \"បង្ហាញភ្ញៀវស្នាក់នៅជាមួយ\",\r\n    \"mainGuest\": \"ភ្ញៀវសំខាន់\",\r\n    \"arrearsFormula\": \"ថ្លៃជំពាក់គ្រោង = ថ្លៃប្រើប្រាស់សរុប + ថ្លៃបន្ទប់បន្ថែម - ចំនួនទឹកប្រាក់អនុញ្ញាតមុន - ថ្លៃបង់សរុប\",\r\n    \"addRoomFeeNote\": \"ថ្លៃបន្ទប់បន្ថែម៖ ថ្លៃបន្ទប់ពេញមួយថ្ងៃសម្រាប់ការស្នាក់នៅពេញមួយយប់នៅថ្ងៃដដែល\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { accountApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport usePagination from '@/utils/composables/usePagination'\r\nimport dayjs from 'dayjs'\r\nimport { computed, onMounted, ref, watch } from 'vue'\r\n\r\ninterface ReminderData {\r\n  type: string\r\n  typeName: string\r\n  orderNo: string\r\n  togetherCode: string\r\n  name: string\r\n  rNo: string\r\n  checkinTime: string\r\n  planCheckoutTime: string\r\n  checkinType: string\r\n  checkinTypeName: string\r\n  guestSrcType: string\r\n  guestSrcTypeName: string\r\n  levelOrCompanyName: string\r\n  price: number\r\n  consumeFee: number\r\n  payFee: number\r\n  preFee: number\r\n  addRoomFee: number\r\n  arrearsFee: number\r\n  teamCode: string\r\n  bindCode: string\r\n  isMain: string | number // 是否主客：1-主客，0-同住客人\r\n}\r\n\r\nconst props = defineProps<{\r\n  modelValue: boolean\r\n}>()\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n}>()\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst visible = computed({\r\n  get: () => props.modelValue,\r\n  set: (value) => emits('update:modelValue', value),\r\n})\r\n\r\nconst loading = ref(false)\r\nconst reminderData = ref<ReminderData[]>([])\r\nconst originalReminderData = ref<ReminderData[]>([]) // 保存原始数据用于排序\r\n\r\n// 排序状态\r\nconst sortState = ref({\r\n  prop: '',\r\n  order: '',\r\n})\r\n\r\n// 筛选条件\r\nconst filterForm = ref({\r\n  types: ['1', '3', '4'], // 默认勾选单订单、联房、团队\r\n  rNo: '',\r\n  isShowTogether: 0, // 默认不显示同住客人\r\n})\r\n\r\n// 类型选项\r\nconst typeOptions = [\r\n  { label: t('singleOrder'), value: '1' },\r\n  { label: t('joinedRoom'), value: '3' },\r\n  { label: t('team'), value: '4' },\r\n]\r\n\r\n// 分页和排序相关\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\n// 排序数据\r\nfunction sortData(data: ReminderData[], prop: string, order: string): ReminderData[] {\r\n  if (!prop || !order) {\r\n    return data\r\n  }\r\n\r\n  const sortedData = [...data]\r\n  const isAscending = order === 'ascending'\r\n\r\n  sortedData.sort((a, b) => {\r\n    const aValue = a[prop as keyof ReminderData]\r\n    const bValue = b[prop as keyof ReminderData]\r\n\r\n    // 处理数字类型的排序\r\n    if (typeof aValue === 'number' && typeof bValue === 'number') {\r\n      return isAscending ? aValue - bValue : bValue - aValue\r\n    }\r\n\r\n    // 处理字符串类型的排序\r\n    if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n      return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)\r\n    }\r\n\r\n    return 0\r\n  })\r\n\r\n  return sortedData\r\n}\r\n\r\n// 根据类型分组数据\r\nconst groupedData = computed(() => {\r\n  const typeGroups: { [key: string]: { [key: string]: ReminderData[] } } = {}\r\n\r\n  // 先按原始顺序分组，然后在每个组内排序\r\n  reminderData.value.forEach((item) => {\r\n    const typeName = item.typeName || '未分类'\r\n\r\n    if (!typeGroups[typeName]) {\r\n      typeGroups[typeName] = {}\r\n    }\r\n\r\n    let subKey = ''\r\n    if (item.type === '2') {\r\n      // 同住订单：orderNo一样的为一组\r\n      subKey = item.orderNo\r\n    } else if (item.type === '3') {\r\n      // 联房：bindCode一样的为一组\r\n      subKey = item.bindCode\r\n    } else if (item.type === '4') {\r\n      // 团队：teamCode一样的为一组\r\n      subKey = item.teamCode\r\n    } else {\r\n      // type为1或其他：全部放在一起\r\n      subKey = 'all'\r\n    }\r\n\r\n    if (!typeGroups[typeName][subKey]) {\r\n      typeGroups[typeName][subKey] = []\r\n    }\r\n    typeGroups[typeName][subKey].push(item)\r\n  })\r\n\r\n  // 如果有排序，则在每个子组内进行排序\r\n  if (sortState.value.prop && sortState.value.order) {\r\n    Object.keys(typeGroups).forEach((typeName) => {\r\n      Object.keys(typeGroups[typeName]).forEach((subKey) => {\r\n        typeGroups[typeName][subKey] = sortData(typeGroups[typeName][subKey], sortState.value.prop, sortState.value.order)\r\n      })\r\n    })\r\n  }\r\n\r\n  return typeGroups\r\n})\r\n\r\n// 计算小分类的合计\r\nfunction calculateSubTotal(items: ReminderData[]) {\r\n  return {\r\n    consumeFee: items.reduce((sum, item) => sum + item.consumeFee, 0),\r\n    payFee: items.reduce((sum, item) => sum + item.payFee, 0),\r\n    preFee: items.reduce((sum, item) => sum + item.preFee, 0),\r\n    addRoomFee: items.reduce((sum, item) => sum + item.addRoomFee, 0),\r\n    arrearsFee: items.reduce((sum, item) => sum + item.arrearsFee, 0),\r\n  }\r\n}\r\n\r\n// 计算大分类的合计\r\nfunction calculateTypeTotal(typeData: { [key: string]: ReminderData[] }) {\r\n  const allItems = Object.values(typeData).flat()\r\n  return calculateSubTotal(allItems)\r\n}\r\n\r\n// 获取子分组标题\r\nfunction getSubGroupTitle(item: ReminderData): string {\r\n  if (item.type === '2') {\r\n    return `订单号: ${item.orderNo}`\r\n  } else if (item.type === '3') {\r\n    return `联房组: ${item.bindCode}`\r\n  } else if (item.type === '4') {\r\n    return `团队: ${item.teamCode}`\r\n  }\r\n  return ''\r\n}\r\n\r\n// 表格合计方法\r\nfunction getSummaryMethod(param: any) {\r\n  const { columns, data } = param\r\n  const sums: string[] = []\r\n\r\n  columns.forEach((column: any, index: number) => {\r\n    if (index === 0) {\r\n      sums[index] = '合计'\r\n      return\r\n    }\r\n\r\n    const prop = column.property\r\n    if (['consumeFee', 'payFee', 'preFee', 'addRoomFee', 'arrearsFee'].includes(prop)) {\r\n      const sum = data.reduce((total: number, item: any) => {\r\n        return total + (Number(item[prop]) || 0)\r\n      }, 0)\r\n      sums[index] = `¥${formatAmount(sum)}`\r\n    } else {\r\n      sums[index] = ''\r\n    }\r\n  })\r\n\r\n  return sums\r\n}\r\n\r\n// 获取催押数据\r\nasync function getReminderData() {\r\n  loading.value = true\r\n  try {\r\n    const params: any = {\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      pageNo: pagination.value.pageNo,\r\n      pageSize: pagination.value.pageSize,\r\n    }\r\n\r\n    // 添加筛选条件\r\n    // 只有当不是全部勾选时才传types参数\r\n    if (filterForm.value.types.length > 0 && filterForm.value.types.length < 3) {\r\n      params.types = filterForm.value.types.join(',')\r\n    }\r\n    if (filterForm.value.rNo.trim()) {\r\n      params.rNo = filterForm.value.rNo.trim()\r\n    }\r\n    params.isShowTogether = filterForm.value.isShowTogether\r\n\r\n    const res = await accountApi.getReminderList(params)\r\n\r\n    if (res.code === 0) {\r\n      // 根据接口返回的数据结构调整\r\n      const data = res.data?.list || res.data || []\r\n      reminderData.value = data\r\n      originalReminderData.value = [...data] // 保存原始数据\r\n      pagination.value.total = res.data?.total || 0\r\n    }\r\n  } catch (error) {\r\n    console.error('获取催押数据失败:', error)\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 分页相关函数\r\nfunction sizeChange(size: number) {\r\n  pagination.value.pageSize = size\r\n  pagination.value.pageNo = 1\r\n  getReminderData()\r\n}\r\n\r\nfunction currentChange(page: number) {\r\n  pagination.value.pageNo = page\r\n  getReminderData()\r\n}\r\n\r\n// 搜索功能\r\nfunction handleSearch() {\r\n  pagination.value.pageNo = 1\r\n  getReminderData()\r\n}\r\n\r\n// 重置功能\r\nfunction handleReset() {\r\n  filterForm.value.types = ['1', '3', '4']\r\n  filterForm.value.rNo = ''\r\n  filterForm.value.isShowTogether = 0\r\n  pagination.value.pageNo = 1\r\n  getReminderData()\r\n}\r\n\r\n// 格式化金额\r\nfunction formatAmount(amount: number): string {\r\n  return amount.toFixed(2)\r\n}\r\n\r\n// 格式化时间\r\nfunction formatTime(time: string): string {\r\n  return dayjs(time).format('YYYY-MM-DD HH:mm')\r\n}\r\n\r\n// 处理表格排序\r\nfunction handleSortChange({ prop, order }: { prop: string; order: string }) {\r\n  sortState.value.prop = prop\r\n  sortState.value.order = order\r\n}\r\n\r\nwatch(\r\n  () => props.modelValue,\r\n  (newVal) => {\r\n    if (newVal) {\r\n      // 重置分页到第一页\r\n      pagination.value.pageNo = 1\r\n      getReminderData()\r\n    }\r\n  }\r\n)\r\n\r\n// 组件挂载时如果弹窗已经打开，也要获取数据\r\nonMounted(() => {\r\n  if (props.modelValue) {\r\n    getReminderData()\r\n  }\r\n})\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"visible\" :title=\"t('title')\" width=\"80%\" :close-on-click-modal=\"false\" destroy-on-close class=\"reminder-dialog\">\r\n    <div v-loading=\"loading\" :element-loading-text=\"t('loading')\" class=\"dialog-content\">\r\n      <!-- 筛选条件 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"filter-wrapper\">\r\n          <el-form :model=\"filterForm\" inline class=\"filter-form\">\r\n            <el-form-item :label=\"t('filterType')\">\r\n              <el-checkbox-group v-model=\"filterForm.types\" class=\"type-checkboxes\">\r\n                <el-checkbox v-for=\"option in typeOptions\" :key=\"option.value\" :label=\"option.value\">\r\n                  {{ option.label }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item :label=\"t('roomNoSearch')\">\r\n              <el-input v-model=\"filterForm.rNo\" :placeholder=\"t('roomNoPlaceholder')\" clearable style=\"width: 150px\" @keyup.enter=\"handleSearch\" />\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleSearch\">\r\n                {{ t('search') }}\r\n              </el-button>\r\n              <el-button @click=\"handleReset\">\r\n                {{ t('reset') }}\r\n              </el-button>\r\n            </el-form-item>\r\n            <el-form-item :label=\"t('showTogether')\">\r\n              <el-switch v-model=\"filterForm.isShowTogether\" :active-value=\"1\" :inactive-value=\"0\" @change=\"handleSearch\" />\r\n            </el-form-item>\r\n          </el-form>\r\n          <div class=\"formula-text\">\r\n            <div class=\"arrears-formula\">\r\n              {{ t('arrearsFormula') }}\r\n            </div>\r\n            <div class=\"formula-note\">\r\n              {{ t('addRoomFeeNote') }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"!loading && reminderData.length === 0\" class=\"no-data\">\r\n        {{ t('noData') }}\r\n      </div>\r\n\r\n      <div v-else class=\"table-container\">\r\n        <div v-for=\"(typeGroup, typeName) in groupedData\" :key=\"typeName\" class=\"type-section\">\r\n          <h2 class=\"type-title\">\r\n            {{ typeName }}\r\n          </h2>\r\n\r\n          <div v-for=\"(subGroup, subKey) in typeGroup\" :key=\"subKey\" class=\"sub-group-section\">\r\n            <h4 v-if=\"Object.keys(typeGroup).length > 1\" class=\"sub-group-title\">\r\n              {{ getSubGroupTitle(subGroup[0]) }}\r\n            </h4>\r\n\r\n            <el-table :data=\"subGroup\" stripe show-summary border :summary-method=\"getSummaryMethod\" @sort-change=\"handleSortChange\">\r\n              <el-table-column :label=\"t('roomNo')\">\r\n                <template #default=\"{ row }\">\r\n                  <span :class=\"{ 'text-red font-bold': row.arrearsFee > 0 }\">\r\n                    {{ row.rNo }}\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column :label=\"t('guestName')\">\r\n                <template #default=\"{ row }\">\r\n                  {{ row.name }}\r\n                  <el-tag v-if=\"filterForm.isShowTogether === 1 && (row.isMain === 1 || row.isMain === '1')\" type=\"primary\" size=\"small\" class=\"main-guest-tag\">\r\n                    {{ t('mainGuest') }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column :label=\"t('checkinTime')\">\r\n                <template #default=\"{ row }\">\r\n                  {{ formatTime(row.checkinTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column :label=\"t('planCheckoutTime')\">\r\n                <template #default=\"{ row }\">\r\n                  {{ formatTime(row.planCheckoutTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"checkinTypeName\" :label=\"t('checkinType')\" />\r\n              <el-table-column prop=\"guestSrcTypeName\" :label=\"t('guestSrcType')\" />\r\n              <el-table-column prop=\"levelOrCompanyName\" :label=\"t('levelOrCompany')\" />\r\n              <el-table-column prop=\"price\" :label=\"t('roomPrice')\" align=\"right\" sortable=\"custom\">\r\n                <template #default=\"{ row }\"> ¥{{ formatAmount(row.price) }} </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"consumeFee\" :label=\"t('consumeFee')\" align=\"right\" sortable=\"custom\">\r\n                <template #default=\"{ row }\"> ¥{{ formatAmount(row.consumeFee) }} </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"payFee\" :label=\"t('payFee')\" align=\"right\" sortable=\"custom\">\r\n                <template #default=\"{ row }\"> ¥{{ formatAmount(row.payFee) }} </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"preFee\" :label=\"t('preFee')\" align=\"right\" sortable=\"custom\">\r\n                <template #default=\"{ row }\"> ¥{{ formatAmount(row.preFee) }} </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"addRoomFee\" :label=\"t('addRoomFee')\" align=\"right\" sortable=\"custom\">\r\n                <template #default=\"{ row }\"> ¥{{ formatAmount(row.addRoomFee) }} </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"arrearsFee\" :label=\"t('arrearsFee')\" align=\"right\" sortable=\"custom\">\r\n                <template #default=\"{ row }\">\r\n                  <span :class=\"{ 'text-red font-bold': row.arrearsFee > 0 }\"> ¥{{ formatAmount(row.arrearsFee) }} </span>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div v-if=\"pagination.total > 0\" class=\"pagination\">\r\n        <el-pagination\r\n          :current-page=\"pagination.pageNo\"\r\n          :total=\"pagination.total\"\r\n          :page-size=\"pagination.pageSize\"\r\n          :page-sizes=\"pagination.sizes\"\r\n          :layout=\"pagination.layout\"\r\n          :hide-on-single-page=\"false\"\r\n          background\r\n          @size-change=\"sizeChange\"\r\n          @current-change=\"currentChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <el-button @click=\"visible = false\">\r\n        {{ t('close') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n:deep(.reminder-dialog) {\r\n  .el-dialog {\r\n    height: 80vh !important;\r\n    max-height: 80vh !important;\r\n    display: flex !important;\r\n    flex-direction: column !important;\r\n    margin: 5vh auto !important;\r\n    overflow: hidden !important;\r\n  }\r\n\r\n  .el-dialog__header {\r\n    flex-shrink: 0 !important;\r\n    padding: 20px 20px 10px 20px !important;\r\n  }\r\n\r\n  .el-dialog__body {\r\n    flex: 1 !important;\r\n    overflow: hidden !important;\r\n    padding: 10px 20px !important;\r\n    display: flex !important;\r\n    flex-direction: column !important;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    flex-shrink: 0 !important;\r\n    padding: 10px 20px 20px 20px !important;\r\n  }\r\n}\r\n\r\n.dialog-content {\r\n  height: 100% !important;\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  min-height: 0 !important;\r\n}\r\n\r\n.filter-section {\r\n  flex-shrink: 0 !important;\r\n  padding: 16px 0 !important;\r\n  border-bottom: 1px solid #ebeef5 !important;\r\n  margin-bottom: 16px !important;\r\n}\r\n\r\n.filter-wrapper {\r\n  display: flex !important;\r\n  justify-content: space-between !important;\r\n  align-items: center !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.filter-form {\r\n  margin: 0 !important;\r\n  flex: 1 !important;\r\n\r\n  :deep(.el-form-item) {\r\n    margin-bottom: 0 !important;\r\n    margin-right: 24px !important;\r\n\r\n    &:last-child {\r\n      margin-right: 0 !important;\r\n    }\r\n  }\r\n\r\n  :deep(.el-form-item__label) {\r\n    font-weight: 500 !important;\r\n    color: #606266 !important;\r\n  }\r\n}\r\n\r\n.type-checkboxes {\r\n  :deep(.el-checkbox) {\r\n    margin-right: 16px !important;\r\n\r\n    &:last-child {\r\n      margin-right: 0 !important;\r\n    }\r\n  }\r\n}\r\n\r\n.formula-text {\r\n  flex-shrink: 0 !important;\r\n  margin-left: 20px !important;\r\n  margin-right: 8px !important; /* 与表格容器的 padding-right 对齐 */\r\n  text-align: right !important;\r\n\r\n  .arrears-formula {\r\n    color: #606266 !important;\r\n    font-size: 12px !important;\r\n    font-style: italic !important;\r\n    white-space: nowrap !important;\r\n    margin-bottom: 2px !important;\r\n  }\r\n\r\n  .formula-note {\r\n    color: #606266 !important;\r\n    font-size: 12px !important;\r\n    font-style: italic !important;\r\n    white-space: nowrap !important;\r\n  }\r\n}\r\n\r\n.table-container {\r\n  flex: 1 !important;\r\n  overflow-y: auto !important;\r\n  padding-right: 8px !important;\r\n  min-height: 0 !important;\r\n  max-height: calc(80vh - 240px) !important; /* 减去头部、底部、筛选条件和分页的高度 */\r\n}\r\n\r\n.type-section {\r\n  margin-bottom: 32px;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.type-title {\r\n  margin: 0 0 16px 0;\r\n  padding: 12px 16px;\r\n  background-color: #e6f7ff;\r\n  border-left: 4px solid #1890ff;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 20;\r\n}\r\n\r\n.sub-group-section {\r\n  margin-bottom: 16px;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.sub-group-title {\r\n  margin: 0 0 8px 0;\r\n  padding: 6px 12px;\r\n  background-color: #f5f7fa;\r\n  border-left: 3px solid #52c41a;\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n  color: #595959;\r\n  position: sticky;\r\n  top: 60px;\r\n  z-index: 15;\r\n}\r\n\r\n.no-data {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 200px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.text-red {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n.font-bold {\r\n  font-weight: bold;\r\n}\r\n\r\n.main-guest-tag {\r\n  margin-left: 6px !important;\r\n  font-size: 10px !important;\r\n  padding: 1px 4px !important;\r\n  height: 16px !important;\r\n  line-height: 14px !important;\r\n  border-radius: 2px !important;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  padding: 12px 0;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n}\r\n\r\n:deep(.el-table) {\r\n  font-size: 14px;\r\n\r\n  .el-table__header th {\r\n    background-color: #fafafa;\r\n    color: #606266;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-table__footer-wrapper {\r\n    .el-table__footer {\r\n      background-color: #f0f9ff;\r\n\r\n      .cell {\r\n        font-weight: 600 !important;\r\n        color: #262626 !important;\r\n        background-color: #f0f9ff !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-table__footer {\r\n    background-color: #f0f9ff !important;\r\n  }\r\n\r\n  .el-table__footer tr td {\r\n    background-color: #f0f9ff !important;\r\n    font-weight: 600 !important;\r\n    color: #262626 !important;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.table-container::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "visible", "computed", "get", "modelValue", "set", "value", "loading", "ref", "reminderData", "originalReminderData", "sortState", "prop", "order", "filterForm", "types", "rNo", "isShowTogether", "typeOptions", "label", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "groupedData", "typeGroups", "for<PERSON>ach", "item", "typeName", "subKey", "type", "orderNo", "bindCode", "teamCode", "push", "Object", "keys", "data", "sortedData", "isAscending", "sort", "a", "b", "aValue", "bValue", "localeCompare", "sortData", "getSummaryMethod", "param", "columns", "sums", "column", "index", "property", "includes", "sum", "reduce", "total", "Number", "formatAmount", "async", "getReminderData", "params", "gcode", "hcode", "pageNo", "pageSize", "length", "join", "trim", "res", "accountApi", "getReminderList", "code", "_a", "list", "_b", "error", "console", "sizeChange", "size", "currentChange", "page", "handleSearch", "handleReset", "amount", "toFixed", "formatTime", "time", "dayjs", "format", "handleSortChange", "watch", "newVal", "onMounted"], "mappings": "+5CAkIA,MAAMA,EAAQC,EAIRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,GAAUC,EAAS,CACvBC,IAAK,IAAMV,EAAMW,WACjBC,IAAMC,GAAUX,EAAM,oBAAqBW,KAGvCC,GAAUC,GAAI,GACdC,GAAeD,EAAoB,IACnCE,GAAuBF,EAAoB,IAG3CG,GAAYH,EAAI,CACpBI,KAAM,GACNC,MAAO,KAIHC,GAAaN,EAAI,CACrBO,MAAO,CAAC,IAAK,IAAK,KAClBC,IAAK,GACLC,eAAgB,IAIZC,GAAc,CAClB,CAAEC,MAAOtB,EAAE,eAAgBS,MAAO,KAClC,CAAEa,MAAOtB,EAAE,cAAeS,MAAO,KACjC,CAAEa,MAAOtB,EAAE,QAASS,MAAO,OAIvBc,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,mBAAcC,GAAiBC,aAAAA,IAAiBC,IAgCzE,MAAAC,GAAcxB,GAAS,KAC3B,MAAMyB,EAAmE,CAAC,EAwCnE,OArCMlB,GAAAH,MAAMsB,SAASC,IACpB,MAAAC,EAAWD,EAAKC,UAAY,MAE7BH,EAAWG,KACHH,EAAAG,GAAY,CAAC,GAG1B,IAAIC,EAAS,GAGXA,EAFgB,MAAdF,EAAKG,KAEEH,EAAKI,QACS,MAAdJ,EAAKG,KAELH,EAAKK,SACS,MAAdL,EAAKG,KAELH,EAAKM,SAGL,MAGNR,EAAWG,GAAUC,KACxBJ,EAAWG,GAAUC,GAAU,IAEjCJ,EAAWG,GAAUC,GAAQK,KAAKP,EAAI,IAIpClB,GAAUL,MAAMM,MAAQD,GAAUL,MAAMO,OAC1CwB,OAAOC,KAAKX,GAAYC,SAASE,IAC/BO,OAAOC,KAAKX,EAAWG,IAAWF,SAASG,IACzCJ,EAAWG,GAAUC,GAjEpB,SAASQ,EAAsB3B,EAAcC,GAChD,IAACD,IAASC,EACL,OAAA0B,EAGH,MAAAC,EAAa,IAAID,GACjBE,EAAwB,cAAV5B,EAmBb,OAjBI2B,EAAAE,MAAK,CAACC,EAAGC,KACZ,MAAAC,EAASF,EAAE/B,GACXkC,EAASF,EAAEhC,GAGjB,MAAsB,iBAAXiC,GAAyC,iBAAXC,EAChCL,EAAcI,EAASC,EAASA,EAASD,EAI5B,iBAAXA,GAAyC,iBAAXC,EAChCL,EAAcI,EAAOE,cAAcD,GAAUA,EAAOC,cAAcF,GAGpE,CAAA,IAGFL,CAAA,CAwC8BQ,CAASrB,EAAWG,GAAUC,GAASpB,GAAUL,MAAMM,KAAMD,GAAUL,MAAMO,MAAK,GAClH,IAIEc,CAAA,IAiCT,SAASsB,GAAiBC,GAClB,MAAAC,QAAEA,EAASZ,KAAAA,GAASW,EACpBE,EAAiB,GAmBhB,OAjBCD,EAAAvB,SAAQ,CAACyB,EAAaC,KAC5B,GAAc,IAAVA,EAEF,YADAF,EAAKE,GAAS,MAIhB,MAAM1C,EAAOyC,EAAOE,SAChB,GAAA,CAAC,aAAc,SAAU,SAAU,aAAc,cAAcC,SAAS5C,GAAO,CACjF,MAAM6C,EAAMlB,EAAKmB,QAAO,CAACC,EAAe9B,IAC/B8B,GAASC,OAAO/B,EAAKjB,KAAU,IACrC,GACHwC,EAAKE,GAAS,IAAIO,GAAaJ,IAAI,MAEnCL,EAAKE,GAAS,EAAA,IAIXF,CAAA,CAITU,eAAeC,aACbxD,GAAQD,OAAQ,EACZ,IACF,MAAM0D,EAAc,CAClBC,MAAOlE,EAAUkE,MACjBC,MAAOnE,EAAUmE,MACjBC,OAAQ/C,GAAWd,MAAM6D,OACzBC,SAAUhD,GAAWd,MAAM8D,UAKzBtD,GAAWR,MAAMS,MAAMsD,OAAS,GAAKvD,GAAWR,MAAMS,MAAMsD,OAAS,IACvEL,EAAOjD,MAAQD,GAAWR,MAAMS,MAAMuD,KAAK,MAEzCxD,GAAWR,MAAMU,IAAIuD,SACvBP,EAAOhD,IAAMF,GAAWR,MAAMU,IAAIuD,QAE7BP,EAAA/C,eAAiBH,GAAWR,MAAMW,eAEzC,MAAMuD,QAAYC,EAAWC,gBAAgBV,GAEzC,GAAa,IAAbQ,EAAIG,KAAY,CAElB,MAAMpC,GAAO,OAAAqC,EAAIJ,EAAAjC,eAAMsC,OAAQL,EAAIjC,MAAQ,GAC3C9B,GAAaH,MAAQiC,EACA7B,GAAAJ,MAAQ,IAAIiC,GACjCnB,GAAWd,MAAMqD,OAAQ,OAAAmB,EAAIN,EAAAjC,eAAMoB,QAAS,CAAA,QAEvCoB,GACCC,QAAAD,MAAM,YAAaA,EAAK,CAChC,QACAxE,GAAQD,OAAQ,CAAA,CAClB,CAIF,SAAS2E,GAAWC,GAClB9D,GAAWd,MAAM8D,SAAWc,EAC5B9D,GAAWd,MAAM6D,OAAS,EACVJ,IAAA,CAGlB,SAASoB,GAAcC,GACrBhE,GAAWd,MAAM6D,OAASiB,EACVrB,IAAA,CAIlB,SAASsB,KACPjE,GAAWd,MAAM6D,OAAS,EACVJ,IAAA,CAIlB,SAASuB,KACPxE,GAAWR,MAAMS,MAAQ,CAAC,IAAK,IAAK,KACpCD,GAAWR,MAAMU,IAAM,GACvBF,GAAWR,MAAMW,eAAiB,EAClCG,GAAWd,MAAM6D,OAAS,EACVJ,IAAA,CAIlB,SAASF,GAAa0B,GACb,OAAAA,EAAOC,QAAQ,EAAC,CAIzB,SAASC,GAAWC,GAClB,OAAOC,EAAMD,GAAME,OAAO,mBAAkB,CAI9C,SAASC,IAAiBjF,KAAEA,EAAMC,MAAAA,IAChCF,GAAUL,MAAMM,KAAOA,EACvBD,GAAUL,MAAMO,MAAQA,CAAA,QAG1BiF,GACE,IAAMrG,EAAMW,aACX2F,IACKA,IAEF3E,GAAWd,MAAM6D,OAAS,EACVJ,KAAA,IAMtBiC,GAAU,KACJvG,EAAMW,YACQ2D,IAAA,28DAlIMlC,OACN,MAAdA,EAAKG,KACA,QAAQH,EAAKI,UACG,MAAdJ,EAAKG,KACP,QAAQH,EAAKK,WACG,MAAdL,EAAKG,KACP,OAAOH,EAAKM,WAEd,m7DART,IAA0BN"}