{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-BvUZdSGB.js", "sources": ["../../src/layouts/components/HotkeysIntro/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport eventBus from '@/utils/eventBus'\r\n\r\ndefineOptions({\r\n  name: 'HotkeysIntro',\r\n})\r\n\r\nconst isShow = ref(false)\r\n\r\nconst settingsStore = useSettingsStore()\r\n\r\nonMounted(() => {\r\n  eventBus.on('global-hotkeys-intro-toggle', () => {\r\n    isShow.value = !isShow.value\r\n  })\r\n})\r\n</script>\r\n\r\n<template>\r\n  <HDialog v-model=\"isShow\" title=\"快捷键介绍\">\r\n    <div class=\"px-4\">\r\n      <div class=\"grid gap-2 sm-grid-cols-2\">\r\n        <div>\r\n          <h2 class=\"m-0 text-lg font-bold\">\r\n            全局\r\n          </h2>\r\n          <ul class=\"list-none ps-4 text-sm\">\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>I</HKbd>\r\n              查看系统信息\r\n            </li>\r\n            <li v-if=\"settingsStore.settings.toolbar.navSearch && settingsStore.settings.navSearch.enableHotkeys\" class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>S</HKbd>\r\n              唤起导航搜索\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <div v-if=\"settingsStore.settings.menu.enableHotkeys && ['side', 'head'].includes(settingsStore.settings.menu.mode)\">\r\n          <h2 class=\"m-0 text-lg font-bold\">\r\n            主导航\r\n          </h2>\r\n          <ul class=\"list-none ps-4 text-sm\">\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>`</HKbd>\r\n              激活下一个主导航\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <div v-if=\"settingsStore.settings.tabbar.enable && settingsStore.settings.tabbar.enableHotkeys\">\r\n          <h2 class=\"m-0 text-lg font-bold\">\r\n            标签栏\r\n          </h2>\r\n          <ul class=\"list-none ps-4 text-sm\">\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>←</HKbd>\r\n              切换到上一个标签页\r\n            </li>\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>→</HKbd>\r\n              切换到下一个标签页\r\n            </li>\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>W</HKbd>\r\n              关闭当前标签页\r\n            </li>\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>1~9</HKbd>\r\n              切换到第 n 个标签页\r\n            </li>\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>0</HKbd>\r\n              切换到最后一个标签页\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <div v-if=\"settingsStore.settings.mainPage.enableHotkeys\">\r\n          <h2 class=\"m-0 text-lg font-bold\">\r\n            页面\r\n          </h2>\r\n          <ul class=\"list-none ps-4 text-sm\">\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>↑</HKbd>\r\n              最大化\r\n            </li>\r\n            <li class=\"py-1\">\r\n              <HKbd>{{ settingsStore.os === 'mac' ? '⌥' : 'Alt' }}</HKbd>\r\n              <HKbd>↓</HKbd>\r\n              退出最大化\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </HDialog>\r\n</template>\r\n"], "names": ["isShow", "ref", "settingsStore", "useSettingsStore", "onMounted", "eventBus", "on", "value"], "mappings": "osBAQM,MAAAA,EAASC,GAAI,GAEbC,EAAgBC,WAEtBC,GAAU,KACCC,EAAAC,GAAG,+BAA+B,KAClCN,EAAAO,OAASP,EAAOO,KAAA,GACxB"}