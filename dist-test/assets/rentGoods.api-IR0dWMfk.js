import{a as e}from"./index-CkEhI1Zk.js";const t="admin-api/pms/rent",o={getRentGoodsList:o=>e.get(`${t}/page`,{params:o}),getRentGoods:o=>e.get(`${t}/get`,{params:{id:o}}),deleteRentGoods:o=>e.delete(`${t}/delete`,{params:{id:o}}),updateRentGoods:o=>e.put(`${t}/update`,o,{}),backRentGoods:o=>e.put(`${t}/back`,o,{}),indemnityRentGoods:o=>e.put(`${t}/indemnity`,o,{}),createRentGoods:o=>e.post(`${t}/create`,o,{})};export{o as r};
//# sourceMappingURL=rentGoods.api-IR0dWMfk.js.map
