{"version": 3, "file": "rechargePlan.api-CkxheMlB.js", "sources": ["../../src/api/modules/member/member/rechargePlan.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nexport default {\r\n  /**\r\n   * 获取活动列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  list: (data: any) => api.get('/admin-api/member/recharge-activity/list', { params: data }),\r\n\r\n  /**\r\n   * 创建活动\r\n   * @param\r\n   * @returns\r\n   */\r\n  create: (data: any) => api.post('/admin-api/member/recharge-activity/create', data),\r\n\r\n  /**\r\n   * 更新充值活动状态\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  updateActivityState: (data: any) => api.put('/admin-api/member/recharge-activity/update-status', data),\r\n\r\n  /**\r\n   * 更新充值活动\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  updateActivity: (data: any) => api.put('/admin-api/member/recharge-activity/update', data),\r\n\r\n  /**\r\n   * 详情\r\n   * @param data\r\n   * @returns\r\n   */\r\n  detail: (data: any) => api.get('/admin-api/member/recharge-activity/get', { params: data }),\r\n\r\n}\r\n"], "names": ["rechargePlanApi", "list", "data", "api", "get", "params", "create", "post", "updateActivityState", "put", "updateActivity", "detail"], "mappings": "mCAEA,MAAeA,EAAA,CAMbC,KAAOC,GAAcC,EAAIC,IAAI,2CAA4C,CAAEC,OAAQH,IAOnFI,OAASJ,GAAcC,EAAII,KAAK,6CAA8CL,GAO9EM,oBAAsBN,GAAcC,EAAIM,IAAI,oDAAqDP,GAOjGQ,eAAiBR,GAAcC,EAAIM,IAAI,6CAA8CP,GAOrFS,OAAST,GAAcC,EAAIC,IAAI,0CAA2C,CAAEC,OAAQH"}