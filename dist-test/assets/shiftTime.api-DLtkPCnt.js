import{a as t}from"./index-CkEhI1Zk.js";const a={getShiftTimeList:a=>t.get("/admin-api/pms/shift-time/list",{params:a}),getShiftTime:a=>t.get("/admin-api/pms/shift-time/get",{params:a}),updateShiftTime:a=>t.put("/admin-api/pms/shift-time/update",a),handoverReport:a=>t.get("/admin-api/pms/account/handover-report",{params:a}),handoverReportCashRealization:a=>t.get("/admin-api/pms/account/handover-report-cash-realization",{params:a}),getChangeShiftTime:a=>t.get("/admin-api/pms/shift-time/get-change-shift",{params:a}),handover:a=>t.post("/admin-api/pms/shift-time/handover",a),getChangeShiftByShiftSet:(a,i)=>t.get("/admin-api/pms/shift-time/get-change-shift-by-shift-set",{params:a,...i})};export{a as s};
//# sourceMappingURL=shiftTime.api-DLtkPCnt.js.map
