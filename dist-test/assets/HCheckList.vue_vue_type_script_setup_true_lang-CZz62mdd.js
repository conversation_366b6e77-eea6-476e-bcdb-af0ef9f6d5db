import{d as e,a1 as a,a2 as s,D as t,o as i,c as o,F as l,ag as n,a7 as d,e as r,h as u,Y as c,_ as b}from"./index-CkEhI1Zk.js";const m={class:"inline-flex select-none items-center justify-center of-hidden rounded-md bg-stone-3 dark-bg-stone-7"},p=["disabled","onClick"],x=e({__name:"HCheckList",props:a({options:{},disabled:{type:Boolean,default:!1}},{modelValue:{},modelModifiers:{}}),emits:a(["change"],["update:modelValue"]),setup(e,{emit:a}){const x=a,y=s(e,"modelValue");return t(y,(e=>{x("change",e)})),(e,a)=>{const s=b;return i(),o("div",m,[(i(!0),o(l,null,n(e.options,(a=>(i(),o("button",{key:a.value,disabled:e.disabled||a.disabled,class:d(["flex cursor-pointer items-center truncate border-size-0 bg-inherit px-2 py-1.5 text-sm disabled-cursor-not-allowed disabled-opacity-50 hover-not-disabled-bg-ui-primary hover-not-disabled-text-ui-text",{"text-ui-text bg-ui-primary":y.value===a.value}]),onClick:e=>y.value=a.value},[a.icon?(i(),r(s,{key:0,name:a.icon},null,8,["name"])):(i(),o(l,{key:1},[u(c(a.label),1)],64))],10,p)))),128))])}}});export{x as _};
//# sourceMappingURL=HCheckList.vue_vue_type_script_setup_true_lang-CZz62mdd.js.map
