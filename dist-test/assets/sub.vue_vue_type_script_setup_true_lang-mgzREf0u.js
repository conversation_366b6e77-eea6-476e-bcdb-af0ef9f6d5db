import{d as e,ao as t,ap as n,B as i,am as l,o as u,c as s,f as a,u as o,e as r,w as d,aq as p,ar as c,a7 as g,F as m,ag as y,R as f,as as h,a6 as v,at as x,U as M,V as b,K as w,ah as C}from"./index-CkEhI1Zk.js";import{I as H,r as R}from"./item-4L_tAnSD.js";const q=e({name:"SubMenu",__name:"sub",props:{uniqueKey:{},menu:{},level:{default:0}},setup(e){const q=e,k=q.menu.path??JSON.stringify(q.menu),E=t("itemRef"),P=t("subMenuRef"),B=n(R),K=i((()=>B.openedMenus.includes(q.uniqueKey.at(-1)))),I=i((()=>B.alwaysOpenedsMenus.includes(q.uniqueKey.at(-1)))),S=i((()=>B.isMenuPopup?{enter(e){e.offsetHeight>window.innerHeight&&(e.style.height=`${window.innerHeight}px`)},afterEnter:()=>{},beforeLeave:e=>{e.style.overflow="hidden",e.style.maxHeight=`${e.offsetHeight}px`},leave:e=>{e.style.maxHeight="0"},afterLeave(e){e.style.overflow="",e.style.maxHeight=""}}:{enter(e){const t=e.offsetHeight;e.style.maxHeight="0",e.style.overflow="hidden",e.offsetHeight,e.style.maxHeight=`${t}px`},afterEnter(e){e.style.overflow="",e.style.maxHeight=""},beforeLeave(e){e.style.overflow="hidden",e.style.maxHeight=`${e.offsetHeight}px`},leave(e){e.style.maxHeight="0"},afterLeave(e){e.style.overflow="",e.style.maxHeight=""}})),_=i((()=>B.isMenuPopup?{enterActiveClass:"ease-in-out duration-300",enterFromClass:"opacity-0 translate-x-4",enterToClass:"opacity-100",leaveActiveClass:"ease-in-out duration-300",leaveFromClass:"opacity-100",leaveToClass:"opacity-0"}:{enterActiveClass:"ease-in-out duration-300",enterFromClass:"opacity-0 translate-y-4 scale-95 blur-4",enterToClass:"opacity-100 translate-y-0 scale-100 blur-0",leaveActiveClass:"ease-in-out duration-300",leaveFromClass:"opacity-100 translate-y-0 scale-100 blur-0",leaveToClass:"opacity-0 translate-y-4 scale-95 blur-4"})),$=i((()=>{let e=!0;return q.menu.children?q.menu.children.every((e=>{var t;return!1===(null==(t=e.meta)?void 0:t.menu)}))&&(e=!1):e=!1,e}));function F(){I.value||B.isMenuPopup&&$.value||($.value?B.handleSubMenuClick(k,q.uniqueKey):B.handleMenuItemClick(k))}let T;function W(){B.isMenuPopup&&(B.mouseInMenu=q.uniqueKey,null==T||T(),({stop:T}=w((()=>{if($.value)B.openMenu(k,q.uniqueKey),C((()=>{var e;const t=null==(e=E.value)?void 0:e.ref;if(!t)return;let n=0,i=0;"vertical"===B.props.mode||0!==q.level?(n=t.getBoundingClientRect().top+t.scrollTop,i=("ltr"===B.props.direction?t.getBoundingClientRect().left:document.documentElement.clientWidth-t.getBoundingClientRect().right)+t.getBoundingClientRect().width,n+P.value.getElement().offsetHeight>window.innerHeight&&(n=window.innerHeight-P.value.getElement().offsetHeight)):(n=t.getBoundingClientRect().top+t.getBoundingClientRect().height,i="ltr"===B.props.direction?t.getBoundingClientRect().left:document.documentElement.clientWidth-t.getBoundingClientRect().right,n+P.value.getElement().offsetHeight>window.innerHeight&&(P.value.getElement().style.height=window.innerHeight-n+"px")),i+P.value.getElement().offsetWidth>document.documentElement.clientWidth&&(i=("ltr"===B.props.direction?t.getBoundingClientRect().left:document.documentElement.clientWidth-t.getBoundingClientRect().right)-t.getBoundingClientRect().width),P.value.getElement().style.top=`${n}px`,P.value.getElement().style.insetInlineStart=`${i}px`}));else{const e=q.menu.children?B.subMenus[k].indexPath.at(-1):B.items[k].indexPath.at(-1);B.openMenu(e,B.subMenus[e].indexPath)}}),300)))}function A(){B.isMenuPopup&&(B.mouseInMenu=[],null==T||T(),({stop:T}=w((()=>{0===B.mouseInMenu.length?B.closeMenu(q.uniqueKey):$.value&&!B.mouseInMenu.includes(q.uniqueKey.at(-1))&&B.closeMenu(q.uniqueKey.at(-1))}),300)))}return(e,t)=>{const n=l("SubMenu");return u(),s(m,null,[a(H,{ref_key:"itemRef",ref:E,"unique-key":e.uniqueKey,item:e.menu,level:e.level,"sub-menu":o($),expand:o(K),"always-expand":o(I),onClick:F,onMouseenter:W,onMouseleave:A},null,8,["unique-key","item","level","sub-menu","expand","always-expand"]),o($)?(u(),r(b,{key:0,to:"body",disabled:!o(B).isMenuPopup},[a(M,v(o(_),x(o(S))),{default:d((()=>[p(a(o(c),{ref_key:"subMenuRef",ref:P,options:{scrollbars:{visibility:"hidden"}},defer:"",class:g(["sub-menu static",{"bg-[var(--g-sub-sidebar-bg)]":o(B).isMenuPopup,"ring-1 ring-stone-2 dark-ring-stone-8 shadow-xl fixed! z-3000 w-[200px]":o(B).isMenuPopup,"mx-1":o(B).isMenuPopup&&("vertical"===o(B).props.mode||0!==e.level),"py-1":o(B).isMenuPopup,"rounded-lg":o(B).props.rounded}])},{default:d((()=>[(u(!0),s(m,null,y(e.menu.children,(t=>{var i;return u(),s(m,{key:t.path??JSON.stringify(t)},[!1!==(null==(i=t.meta)?void 0:i.menu)?(u(),r(n,{key:0,"unique-key":[...e.uniqueKey,t.path??JSON.stringify(t)],menu:t,level:e.level+1},null,8,["unique-key","menu","level"])):f("",!0)],64)})),128))])),_:1},8,["class"]),[[h,o(K)]])])),_:1},16)],8,["disabled"])):f("",!0)],64)}}});export{q as _};
//# sourceMappingURL=sub.vue_vue_type_script_setup_true_lang-mgzREf0u.js.map
