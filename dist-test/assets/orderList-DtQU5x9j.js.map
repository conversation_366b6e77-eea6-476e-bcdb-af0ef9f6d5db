{"version": 3, "file": "orderList-DtQU5x9j.js", "sources": ["../../src/views/order/info/components/orderdetail/orderList.vue"], "sourcesContent": ["<route lang=\"yaml\">\r\nmeta:\r\nenabled: false\r\n</route>\r\n\r\n<i18n>\r\n{\r\n  \"en\": {\r\n    \"orderList\": {\r\n      \"actions\": {\r\n        \"batchBreakfast\": \"Batch Breakfast Gift (Dummy)\",\r\n        \"continueStay\": \"Extend Stay (Dummy)\",\r\n        \"printTeamCheckInForm\": \"Print Team/Shared Room Check-in Form\"\r\n      },\r\n      \"table\": {\r\n        \"roomName\": \"Room No./Name\",\r\n        \"name\": \"Name\",\r\n        \"status\": \"Status\",\r\n        \"roomType\": \"Room Type\",\r\n        \"checkinTime\": \"Check-in Time\",\r\n        \"mergeRoomRelation\": \"Merge Room Relation\",\r\n        \"operation\": \"Operation\"\r\n      },\r\n      \"status\": {\r\n        \"check_in\": \"Checked In\",\r\n        \"no_check_in\": \"Booked\",\r\n        \"check_out\": \"Checked Out\"\r\n      },\r\n      \"mergeRoom\": {\r\n        \"main\": \"Main Order\",\r\n        \"sub\": \"Sub Order\"\r\n      },\r\n      \"operation\": {\r\n        \"unmerge\": \"Unmerge Room\",\r\n        \"setAsMain\": \"Set as Main Order\"\r\n      },\r\n      \"messages\": {\r\n        \"editMainSuccess\": \"Main order updated successfully\",\r\n        \"unmergeSuccess\": \"Unmerged room successfully\",\r\n        \"editMainError\": \"Failed to update main order\",\r\n        \"unmergeError\": \"Failed to unmerge room\"\r\n      }\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"orderList\": {\r\n      \"actions\": {\r\n        \"batchBreakfast\": \"批量赠早餐（假的）\",\r\n        \"continueStay\": \"续住（假的）\",\r\n        \"printTeamCheckInForm\": \"打印团队/联房入住单\"\r\n      },\r\n      \"table\": {\r\n        \"roomName\": \"房号/姓名\",\r\n        \"name\": \"姓名\",\r\n        \"status\": \"状态\",\r\n        \"roomType\": \"房型\",\r\n        \"checkinTime\": \"入住时间\",\r\n        \"mergeRoomRelation\": \"联房关系\",\r\n        \"operation\": \"操作\"\r\n      },\r\n      \"status\": {\r\n        \"check_in\": \"在住\",\r\n        \"no_check_in\": \"预订\",\r\n        \"check_out\": \"离店\"\r\n      },\r\n      \"mergeRoom\": {\r\n        \"main\": \"主单\",\r\n        \"sub\": \"从单\"\r\n      },\r\n      \"operation\": {\r\n        \"unmerge\": \"解除联房\",\r\n        \"setAsMain\": \"设为主单\"\r\n      },\r\n      \"messages\": {\r\n        \"editMainSuccess\": \"修改主单成功\",\r\n        \"unmergeSuccess\": \"退出联房成功\",\r\n        \"editMainError\": \"修改主单失败\",\r\n        \"unmergeError\": \"退出联房失败\"\r\n      }\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"orderList\": {\r\n      \"actions\": {\r\n        \"batchBreakfast\": \"អំណោយអាហារពេលព្រឹកជាក្រុម (គ្មានប្រសិទ្ធភាព)\",\r\n        \"continueStay\": \"បន្តស្នាក់នៅ (គ្មានប្រសិទ្ធភាព)\",\r\n        \"printTeamCheckInForm\": \"បោះពុម្ពទម្រង់ចុះឈ្មោះសម្រាប់ក្រុម/បន្ទប់រួម\"\r\n      },\r\n      \"table\": {\r\n        \"roomName\": \"លេខបន្ទប់/ឈ្មោះ\",\r\n        \"name\": \"ឈ្មោះ\",\r\n        \"status\": \"ស្ថានភាព\",\r\n        \"roomType\": \"ប្រភេទបន្ទប់\",\r\n        \"checkinTime\": \"ពេលវេលាចុះឈ្មោះ\",\r\n        \"mergeRoomRelation\": \"ទំនាក់ទំនងបន្ទប់រួម\",\r\n        \"operation\": \"ប្រតិបត្តិការ\"\r\n      },\r\n      \"status\": {\r\n        \"check_in\": \"កំពុងស្នាក់នៅ\",\r\n        \"no_check_in\": \"បានកក់\",\r\n        \"check_out\": \"ចាកចេញ\"\r\n      },\r\n      \"mergeRoom\": {\r\n        \"main\": \"ការកក់សំខាន់\",\r\n        \"sub\": \"ការកក់រង\"\r\n      },\r\n      \"operation\": {\r\n        \"unmerge\": \"ដកចេញពីបន្ទប់រួម\",\r\n        \"setAsMain\": \"កំណត់ជាការកក់សំខាន់\"\r\n      },\r\n      \"messages\": {\r\n        \"editMainSuccess\": \"កែប្រែការកក់សំខាន់បានជោគជ័យ\",\r\n        \"unmergeSuccess\": \"ដកចេញពីបន្ទប់រួមបានជោគជ័យ\",\r\n        \"editMainError\": \"កែប្រែការកក់សំខាន់បរាជ័យ\",\r\n        \"unmergeError\": \"ដកចេញពីបន្ទប់រួមបរាជ័យ\"\r\n      }\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { orderApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport PrintMergeForm from '@/views/print/mergeForm.vue'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    bindCode: string\r\n    isEntryAccount: string\r\n  }>(),\r\n  {\r\n    bindCode: '',\r\n    isEntryAccount: '0',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  editStatus: [value: boolean]\r\n  refresh: [order: string, togCode: string, noType: string]\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\n// 打印联房订单\r\nconst printMergeFormVisible = ref(false)\r\nconst data = ref({\r\n  loading: false,\r\n  // 批量操作\r\n  batch: {\r\n    enable: false,\r\n    selectionDataList: [],\r\n  },\r\n})\r\n\r\nonMounted(() => {\r\n  getDataList()\r\n})\r\n\r\ninterface OrderVisibility {\r\n  orderNo: string\r\n  name: string\r\n  orderType: string\r\n  bindCode: string\r\n  isMain: string\r\n  state: string\r\n  checkinTime: string\r\n  planCheckoutTime: string\r\n  checkoutTime: string\r\n  rtCode: string\r\n  rtName: string\r\n  rNo: string\r\n}\r\n\r\nconst dataList = ref<OrderVisibility[]>([\r\n  {\r\n    // 订单号\r\n    orderNo: '',\r\n    // 客人姓名\r\n    name: ' ',\r\n    // 订单类型;general：普通订单 join：联房订单 group：团队订单\r\n    orderType: '',\r\n    // 绑定代码;当为直接入住时，值为联房号(单个订单也生成唯一的联房号，多个订单生成相同的联房号)。当订单来自预订时，值为预订单号.\r\n    bindCode: '',\r\n    // 是否主订单;是否为主订单（0否 1是）\r\n    isMain: '',\r\n    // 订单状态;订单状态（在住:check_in 、已离店:check_out）\r\n    state: '',\r\n    // 入住时间\r\n    checkinTime: '',\r\n    // 预离时间\r\n    planCheckoutTime: '',\r\n    // 退房时间\r\n    checkoutTime: '',\r\n    // 房型代码\r\n    rtCode: '',\r\n    // 房型名称\r\n    rtName: '',\r\n    // 房号\r\n    rNo: '',\r\n  },\r\n])\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  orderApi\r\n    .getMergeRoomList({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      bindCode: props.bindCode,\r\n    })\r\n    .then((res: any) => {\r\n      data.value.loading = false\r\n      if (res.code === 0) {\r\n        dataList.value = res.data\r\n      } else {\r\n        ElMessage.error(res.msg)\r\n      }\r\n    })\r\n    .catch(() => {\r\n      data.value.loading = false\r\n    })\r\n}\r\n\r\nfunction editState(value: any) {\r\n  orderApi\r\n    .mainState({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      orderNo: value.orderNo,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('orderList.messages.editMainSuccess'))\r\n        emits('refresh', '', '', 'orderList')\r\n        getDataList()\r\n      } else {\r\n        ElMessage.error(res.msg || t('orderList.messages.editMainError'))\r\n      }\r\n    })\r\n    .catch(() => {\r\n      ElMessage.error(t('orderList.messages.editMainError'))\r\n    })\r\n}\r\n\r\nfunction secureState(value: any) {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    orderNo: value.orderNo,\r\n  }\r\n  orderApi\r\n    .quitMergeRoom(params)\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('orderList.messages.unmergeSuccess'))\r\n        emits('refresh', '', '', 'orderList')\r\n        getDataList()\r\n      } else {\r\n        ElMessage.error(res.msg || t('orderList.messages.unmergeError'))\r\n      }\r\n    })\r\n    .catch(() => {\r\n      ElMessage.error(t('orderList.messages.unmergeError'))\r\n    })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <div flex style=\"justify-content: right\">\r\n      <!--      <el-button> -->\r\n      <!--        {{ t('orderList.actions.batchBreakfast') }} -->\r\n      <!--      </el-button> -->\r\n      <!--      <el-button> -->\r\n      <!--        {{ t('orderList.actions.continueStay') }} -->\r\n      <!--      </el-button> -->\r\n      <el-button type=\"primary\" plain @click=\"printMergeFormVisible = true\">\r\n        {{ t('orderList.actions.printTeamCheckInForm') }}\r\n      </el-button>\r\n    </div>\r\n    <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"dataList\" stripe highlight-current-row height=\"100%\">\r\n      <el-table-column type=\"selection\" align=\"center\" fixed />\r\n      <el-table-column prop=\"rNo\" :label=\"t('orderList.table.roomName')\" align=\"left\" />\r\n      <el-table-column prop=\"name\" :label=\"t('orderList.table.name')\" align=\"left\" />\r\n      <el-table-column :label=\"t('orderList.table.status')\" align=\"left\">\r\n        <template #default=\"scope\">\r\n          <span v-if=\"scope.row.state === 'check_in'\">{{ t('orderList.status.check_in') }}</span>\r\n          <span v-if=\"scope.row.state === 'no_check_in'\">{{ t('orderList.status.no_check_in') }}</span>\r\n          <span v-if=\"scope.row.state === 'check_out'\">{{ t('orderList.status.check_out') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"rtName\" :label=\"t('orderList.table.roomType')\" align=\"left\" />\r\n      <el-table-column :label=\"t('orderList.table.checkinTime')\" align=\"left\">\r\n        <template #default=\"scope\">\r\n          <span>{{ dayjs(scope.row.planCheckinTime).format('MM/DD HH:mm').concat(' - ').concat(dayjs(scope.row.planCheckoutTime).format('MM/DD HH:mm')) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('orderList.table.mergeRoomRelation')\" align=\"left\">\r\n        <template #default=\"scope\">\r\n          <el-tag v-if=\"scope.row.isMain === '1'\" type=\"primary\">\r\n            {{ t('orderList.mergeRoom.main') }}\r\n          </el-tag>\r\n          <el-tag v-else type=\"success\">\r\n            {{ t('orderList.mergeRoom.sub') }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('orderList.table.operation')\" width=\"250\" align=\"center\" fixed=\"right\">\r\n        <template v-if=\"dataList.length > 1\" #default=\"scope\">\r\n          <el-link v-auth=\"'pms:order:update:quit-merge-room'\" type=\"primary\" @click=\"secureState(scope.row)\">\r\n            {{ t('orderList.operation.unmerge') }}\r\n          </el-link>\r\n          <el-link v-if=\"scope.row.isMain === '0'\" v-auth=\"'pms:order:update:update-main-state'\" type=\"primary\" @click=\"editState(scope.row)\">\r\n            {{ t('orderList.operation.setAsMain') }}\r\n          </el-link>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 打印团队/联房入住登记单 -->\r\n    <PrintMergeForm v-if=\"printMergeFormVisible\" v-model=\"printMergeFormVisible\" :bind-code=\"props.bindCode\" />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-link {\r\n  margin: 0 10px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "printMergeFormVisible", "ref", "data", "loading", "batch", "enable", "selectionDataList", "onMounted", "getDataList", "dataList", "orderNo", "name", "orderType", "bindCode", "is<PERSON><PERSON>", "state", "checkinTime", "planCheckoutTime", "checkoutTime", "rtCode", "rtName", "rNo", "value", "orderApi", "getMergeRoomList", "gcode", "hcode", "then", "res", "code", "ElMessage", "error", "msg", "catch", "params", "quitMergeRoom", "success", "mainState"], "mappings": "4lCA+HA,MAAMA,EAAQC,EAWRC,EAAQC,GAMRC,EAAEA,GAAMC,IAERC,EAAYC,IAEZC,EAAwBC,GAAI,GAC5BC,EAAOD,EAAI,CACfE,SAAS,EAETC,MAAO,CACLC,QAAQ,EACRC,kBAAmB,MAIvBC,GAAU,KACIC,GAAA,IAkBd,MAAMC,EAAWR,EAAuB,CACtC,CAEES,QAAS,GAETC,KAAM,IAENC,UAAW,GAEXC,SAAU,GAEVC,OAAQ,GAERC,MAAO,GAEPC,YAAa,GAEbC,iBAAkB,GAElBC,aAAc,GAEdC,OAAQ,GAERC,OAAQ,GAERC,IAAK,MAIT,SAASb,IACPN,EAAKoB,MAAMnB,SAAU,EACrBoB,EACGC,iBAAiB,CAChBC,MAAO3B,EAAU2B,MACjBC,MAAO5B,EAAU4B,MACjBb,SAAUrB,EAAMqB,WAEjBc,MAAMC,IACL1B,EAAKoB,MAAMnB,SAAU,EACJ,IAAbyB,EAAIC,KACNpB,EAASa,MAAQM,EAAI1B,KAEX4B,EAAAC,MAAMH,EAAII,IAAG,IAG1BC,OAAM,KACL/B,EAAKoB,MAAMnB,SAAU,CAAA,GACtB,2uDAwBL,SAAqBmB,GACnB,MAAMY,EAAS,CACbT,MAAO3B,EAAU2B,MACjBC,MAAO5B,EAAU4B,MACjBhB,QAASY,EAAMZ,SAEjBa,EACGY,cAAcD,GACdP,MAAMC,IACY,IAAbA,EAAIC,MACIC,EAAAM,QAAQxC,EAAE,sCACdF,EAAA,UAAW,GAAI,GAAI,aACbc,KAEZsB,EAAUC,MAAMH,EAAII,KAAOpC,EAAE,mCAAkC,IAGlEqC,OAAM,KACKH,EAAAC,MAAMnC,EAAE,mCAAkC,GACrD,mNAxCc0B,aACjBC,EACGc,UAAU,CACTZ,MAAO3B,EAAU2B,MACjBC,MAAO5B,EAAU4B,MACjBhB,QAASY,EAAMZ,UAEhBiB,MAAMC,IACY,IAAbA,EAAIC,MACIC,EAAAM,QAAQxC,EAAE,uCACdF,EAAA,UAAW,GAAI,GAAI,aACbc,KAEZsB,EAAUC,MAAMH,EAAII,KAAOpC,EAAE,oCAAmC,IAGnEqC,OAAM,KACKH,EAAAC,MAAMnC,EAAE,oCAAmC,IAjB3D,IAAmB0B"}