import{a as e}from"./index-CkEhI1Zk.js";const t={list:t=>e.get("/admin-api/marketing/coupon-template/page",{params:t}),roomCouponList:t=>e.get("/admin-api/marketing/coupon-template/room/list",{params:t}),listTree:t=>e.get("marketing/coupon/template/tree",{params:t}),detail:(t,a)=>e.get("marketing/coupon/template/detail",{params:{gcode:t,templateCode:a}}),create:t=>e.post("/admin-api/marketing/coupon-template/create",t),edit:t=>e.put("/admin-api/marketing/coupon-template/update",t),updateTemplateStatus:t=>e.put("/admin-api/marketing/coupon-template/lose-efficacy",t)};export{t as c};
//# sourceMappingURL=couponTemplate.api-3l2fuazK.js.map
