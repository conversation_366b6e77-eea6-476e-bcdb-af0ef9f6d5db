{"version": 3, "file": "changeDepartmentModal-BfglnT3G.js", "sources": ["../../src/views/group/org/dept-user/components/DetailForm/changeDepartmentModal.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"changeDepartment\": \"Change Department\",\r\n    \"nickname\": \"Nick<PERSON>\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"pleaseSelectDepartment\": \"Please select a department\",\r\n    \"changeDepartmentSuccess\": \"Department changed successfully\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"changeDepartment\": \"变更部门\",\r\n    \"nickname\": \"昵称\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"pleaseSelectDepartment\": \"请选择部门\",\r\n    \"changeDepartmentSuccess\": \"变更部门成功\"\r\n  },\r\n  \"km\": {\r\n    \"changeDepartment\": \"ផ្លាស់ប្តូរនាយកដ្ឋាន\",\r\n    \"nickname\": \"ឈ្មោះហៅក្រៅ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"pleaseSelectDepartment\": \"សូមជ្រើសរើសនាយកដ្ឋាន\",\r\n    \"changeDepartmentSuccess\": \"បានផ្លាស់ប្តូរនាយកដ្ឋានដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { deptApi, userApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { handleTree } from '@/utils/grouptree'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    userid: string\r\n    useridList: any\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    userid: '',\r\n    useridList: [],\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst deptList = ref<any>([])\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  deptId: '',\r\n})\r\nconst formRules = ref<FormRules>({\r\n  deptId: [\r\n    {\r\n      required: true,\r\n      message: t('pleaseSelectDepartment'),\r\n      trigger: ['blur', 'change'],\r\n    },\r\n  ],\r\n})\r\n\r\n// 获取部门\r\nfunction getDeptList() {\r\n  deptApi.list({ gcode: userStore.gcode }).then((res) => {\r\n    deptList.value = handleTree(res.data)\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        userApi\r\n          .updateUserDept({\r\n            deptId: form.value.deptId[form.value.deptId.length - 1],\r\n            ids: props.userid ? [props.userid] : props.useridList.map((v: { id: any }) => v.id),\r\n          })\r\n          .then((res) => {\r\n            ElMessage.success({\r\n              message: t('changeDepartmentSuccess'),\r\n              center: true,\r\n            })\r\n            emits('success')\r\n            onCancel()\r\n          })\r\n      }\r\n    })\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nonMounted(() => {\r\n  getDeptList()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('changeDepartment')\" :close-on-click-modal=\"false\" width=\"400px\" append-to-body :modal=\"true\" destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-suffix=\"\">\r\n      <el-form-item :label=\"t('nickname')\" prop=\"deptId\">\r\n        <el-cascader v-model=\"form.deptId\" :options=\"deptList\" :props=\"{ value: 'id', label: 'name' }\" :show-all-levels=\"false\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('save') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n.box {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "myVisible", "computed", "get", "modelValue", "set", "val", "deptList", "ref", "formRef", "form", "deptId", "formRules", "required", "message", "trigger", "onSubmit", "value", "validate", "valid", "userApi", "updateUserDept", "length", "ids", "userid", "useridList", "map", "v", "id", "then", "res", "ElMessage", "success", "center", "onCancel", "onMounted", "<PERSON>pt<PERSON><PERSON>", "list", "gcode", "handleTree", "data"], "mappings": "q7BAmCA,MAAMA,EAAQC,EAaRC,EAAQC,GAKRC,EAAEA,GAAMC,IAERC,EAAYC,IAEZC,EAAYC,EAAS,CACzBC,IAAM,IACGV,EAAMW,WAEf,GAAAC,CAAIC,GACFX,EAAM,oBAAqBW,EAAG,IAG5BC,EAAWC,EAAS,IACpBC,EAAUD,IACVE,EAAOF,EAAI,CACfG,OAAQ,KAEJC,EAAYJ,EAAe,CAC/BG,OAAQ,CACN,CACEE,UAAU,EACVC,QAASjB,EAAE,0BACXkB,QAAS,CAAC,OAAQ,cAYxB,SAASC,IACPP,EAAQQ,OACNR,EAAQQ,MAAMC,UAAUC,IAClBA,GACFC,EACGC,eAAe,CACdV,OAAQD,EAAKO,MAAMN,OAAOD,EAAKO,MAAMN,OAAOW,OAAS,GACrDC,IAAK9B,EAAM+B,OAAS,CAAC/B,EAAM+B,QAAU/B,EAAMgC,WAAWC,KAAKC,GAAmBA,EAAEC,OAEjFC,MAAMC,IACLC,EAAUC,QAAQ,CAChBlB,QAASjB,EAAE,2BACXoC,QAAQ,IAEVtC,EAAM,WACGuC,GAAA,GACV,GAEN,CAEL,SAASA,IACPjC,EAAUgB,OAAQ,CAAA,QAGpBkB,GAAU,KA7BAC,EAAAC,KAAK,CAAEC,MAAOvC,EAAUuC,QAAST,MAAMC,IACpCvB,EAAAU,MAAQsB,EAAWT,EAAIU,KAAI,GA6B1B"}