import e from"./HKbd-j4z1e5lJ.js";import{d as s,W as a,aj as t,o as r,c as n,u as o,e as i,f as c,g as p,Y as l,w as m,h as d,R as g,a7 as x,X as h,_ as k}from"./index-CkEhI1Zk.js";const u={key:1,class:"group inline-flex cursor-pointer items-center gap-1 whitespace-nowrap rounded-2 bg-stone-1 px-2 py-1.5 text-dark ring-stone-3 ring-inset transition dark-bg-stone-9 dark-text-white hover-ring-1 dark-ring-stone-7"},b={class:"text-sm text-stone-5 transition group-hover-text-dark dark-group-hover-text-white"},f=s({name:"NavSearch",__name:"index",setup(s){const f=a(),{t:v}=t();return(s,a)=>{const t=k,w=e;return r(),n("span",{class:x(["flex-center cursor-pointer","mobile"===o(f).mode?"p-2":"px-2"]),onClick:a[0]||(a[0]=e=>o(h).emit("global-search-toggle"))},["mobile"===o(f).mode?(r(),i(t,{key:0,name:"i-ri:search-line"})):(r(),n("span",u,[c(t,{name:"i-ri:search-line"}),p("span",b,l(o(v)("app.search.text")),1),o(f).settings.navSearch.enableHotkeys?(r(),i(w,{key:0,class:"ms-2"},{default:m((()=>[d(l("mac"===o(f).os?"⌥":"Alt")+" S",1)])),_:1})):g("",!0)]))],2)}}});export{f as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-CB3L42GY.js.map
