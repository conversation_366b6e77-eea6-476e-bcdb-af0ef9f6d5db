{"version": 3, "file": "consumeLog-SEbTwZ4k.js", "sources": ["../../src/views/customer/member/info/components/DetailForm/consumeLog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"businessDate\": \"Biz Date\",\r\n    \"name\": \"Name\",\r\n    \"consumeScene\": \"Consume Scene\",\r\n    \"paymentAmount\": \"Payment Amount\",\r\n    \"paymentMethod\": \"Payment Method\",\r\n    \"operator\": \"Op/Time\",\r\n    \"storeName\": \"Hotel\",\r\n    \"view\": \"View\",\r\n    \"noData\": \"No data available\",\r\n    \"unknownError\": \"Unknown error\",\r\n    \"networkError\": \"Network error\",\r\n    \"rangeSeparator\": \"To\",\r\n    \"startPlaceholder\": \"Start\",\r\n    \"endPlaceholder\": \"End\",\r\n  },\r\n  \"zh-cn\": {\r\n    \"businessDate\": \"营业日期\",\r\n    \"name\": \"姓名\",\r\n    \"consumeScene\": \"消费场景\",\r\n    \"paymentAmount\": \"支付金额(元)\",\r\n    \"paymentMethod\": \"支付方式\",\r\n    \"operator\": \"操作员\",\r\n    \"storeName\": \"发生门店\",\r\n    \"view\": \"查看\",\r\n    \"noData\": \"暂无数据\",\r\n    \"unknownError\": \"未知错误\",\r\n    \"networkError\": \"网络错误\",\r\n    \"rangeSeparator\": \"至\",\r\n    \"startPlaceholder\": \"开始\",\r\n    \"endPlaceholder\": \"结束\",\r\n  },\r\n  \"km\": {\r\n    \"businessDate\": \"កាលបរិច្ឆេទអាជីវកម្ម\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"consumeScene\": \"ឈុតចំណាយ\",\r\n    \"paymentAmount\": \"ចំនួនទឹកប្រាក់បង់ (យ៉ាន)\",\r\n    \"paymentMethod\": \"វិធីសាបង់ប្រាក់\",\r\n    \"operator\": \"ប្រតិបត្តិករ/ពេលវេលា\",\r\n    \"storeName\": \"សណ្ឋាគារកើតឡើង\",\r\n    \"view\": \"មើល\",\r\n    \"noData\": \"គ្មានទិន្នន័យ\",\r\n    \"unknownError\": \"កំហុសមិនស្គាល់\",\r\n    \"networkError\": \"កំហុសបណ្តាញ\",\r\n    \"rangeSeparator\": \"ដល់\",\r\n    \"startPlaceholder\": \"ចាប់ផ្តើម\",\r\n    \"endPlaceholder\": \"បញ្ចប់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel, MemberConsumeLogModel } from '@/models/index'\r\nimport { dictDataApi, generalConfigApi, memberLogApi } from '@/api/modules/index'\r\nimport { DictTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = defineProps<{\r\n  mcode: string\r\n  isEdit: boolean\r\n}>()\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: true,\r\n  // 搜索\r\n  search: {\r\n    bizDate: '',\r\n  },\r\n  dataList: [] as MemberConsumeLogModel[],\r\n})\r\n\r\n/** 场景列表 */\r\nconst scenes = ref<DictDataModel[]>([])\r\n/** 支付方式列表 */\r\nconst payTypes = ref<DictDataModel[]>([])\r\n\r\nonMounted(() => {\r\n  getPayTypes()\r\n  getScenes()\r\n  getDataList()\r\n})\r\n\r\n/**\r\n * 获取消费场景\r\n */\r\nfunction getScenes() {\r\n  dictDataApi\r\n    .list({ dictType: DictTypeEnum.CONSUME_SCENE })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        scenes.value = res.data\r\n      }\r\n    })\r\n    .catch(() => {\r\n      ElMessage.error({\r\n        message: t('unknownError'),\r\n        center: true,\r\n      })\r\n    })\r\n}\r\n\r\n/**\r\n * 获取支付方式\r\n */\r\nfunction getPayTypes() {\r\n  generalConfigApi\r\n    .getPayAccountList(userStore.gcode)\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        payTypes.value = res.data\r\n      }\r\n    })\r\n    .catch(() => {\r\n      ElMessage.error({\r\n        message: t('unknownError'),\r\n        center: true,\r\n      })\r\n    })\r\n}\r\n\r\n/**\r\n * 获取消费记录列表\r\n */\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  // 格式化 bizDate 为 \"yyyy-MM-dd\" 格式\r\n  const formattedBizDate = data.value.search.bizDate ? data.value.search.bizDate.map((date: any) => dayjs(date).format('YYYY-MM-DD')).join(',') : []\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    mcode: props.mcode,\r\n    ...getParams(),\r\n    bizDate: formattedBizDate,\r\n  }\r\n  memberLogApi\r\n    .consumeloglist(params)\r\n    .then((res: any) => {\r\n      data.value.loading = false\r\n      data.value.dataList = res.data.list\r\n      pagination.value.total = res.data.total\r\n    })\r\n    .catch(() => {\r\n      data.value.loading = false\r\n      ElMessage.error({\r\n        message: t('unknownError'),\r\n        center: true,\r\n      })\r\n    })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\n/**\r\n * 将消费场景编码转换为名称\r\n */\r\nfunction scenes2Name(row: any) {\r\n  const scene = scenes.value.find((item: any) => item.code === row.scene)\r\n  return scene ? scene.label : t('noData')\r\n}\r\n\r\n/**\r\n * 将支付方式编码转换为名称\r\n */\r\nfunction payTypes2Name(row: any) {\r\n  const payType = payTypes.value.find((item: any) => item.code === row.payMethod)\r\n  return payType ? payType.name : t('noData')\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <search-bar :show-toggle=\"false\">\r\n      <el-form :model=\"data.search\" size=\"default\" label-width=\"150px\" inline-message inline class=\"search-form\">\r\n        <el-form-item :label=\"t('businessDate')\">\r\n          <el-date-picker v-model=\"data.search.bizDate\" type=\"daterange\" :range-separator=\"t('rangeSeparator')\" :start-placeholder=\"t('startPlaceholder')\" :end-placeholder=\"t('endPlaceholder')\" style=\"width: 300px\" @change=\"getDataList()\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </search-bar>\r\n    <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" highlight-current-row height=\"100%\" @sort-change=\"sortChange\">\r\n      <el-table-column prop=\"name\" :label=\"t('name')\" />\r\n      <!-- <el-table-column :label=\"t('memberCardNo')\">\r\n        <template #default=\"scope\">\r\n          <el-tag>\r\n            {{ scope.row.storeCardNo }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column :label=\"t('consumeScene')\">\r\n        <template #default=\"scope\">\r\n          <el-tag>{{ scenes2Name(scope.row) }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"fee\" :label=\"t('paymentAmount')\" align=\"right\">\r\n        <template #default=\"scope\">\r\n          {{ scope.row.fee.toFixed(2) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('paymentMethod')\">\r\n        <template #default=\"scope\">\r\n          {{ payTypes2Name(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"bizDate\" :label=\"t('businessDate')\" />\r\n      <el-table-column :label=\"t('operator')\" min-width=\"180\">\r\n        <template #default=\"scope\">\r\n          <div class=\"operator-time\">\r\n            <div>{{ scope.row.operator }}</div>\r\n            <div>{{ scope.row.createTime }}</div>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"hname\" :label=\"t('storeName')\" />\r\n    </el-table>\r\n    <el-pagination\r\n      v-if=\"pagination.total > 10\"\r\n      :current-page=\"pagination.pageNo\"\r\n      :total=\"pagination.total\"\r\n      :page-size=\"pagination.pageSize\"\r\n      :page-sizes=\"pagination.sizes\"\r\n      :layout=\"pagination.layout\"\r\n      :hide-on-single-page=\"false\"\r\n      class=\"pagination\"\r\n      background\r\n      @size-change=\"sizeChange\"\r\n      @current-change=\"currentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-main {\r\n    display: flex;\r\n\r\n    /* 让 page-main 的高度自适应 */\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.operator-time {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  div:first-child {\r\n    margin-bottom: 4px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "data", "ref", "loading", "tableAutoHeight", "search", "bizDate", "dataList", "scenes", "payTypes", "getDataList", "value", "formattedBizDate", "map", "date", "dayjs", "format", "join", "params", "gcode", "mcode", "memberLogApi", "consumeloglist", "then", "res", "list", "total", "catch", "ElMessage", "error", "message", "center", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "scenes2Name", "row", "scene", "find", "item", "code", "label", "payTypes2Name", "payType", "payMethod", "name", "onMounted", "generalConfigApi", "getPayAccountList", "dict<PERSON>ata<PERSON><PERSON>", "dictType", "DictTypeEnum", "CONSUME_SCENE"], "mappings": "ypCA4DA,MAAMA,EAAQC,GAKRC,EAAEA,GAAMC,IACRC,EAAYC,KACZC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IAEzEC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAEjBC,OAAQ,CACNC,QAAS,IAEXC,SAAU,KAINC,EAASN,EAAqB,IAE9BO,EAAWP,EAAqB,IAiDtC,SAASQ,IACPT,EAAKU,MAAMR,SAAU,EAEf,MAAAS,EAAmBX,EAAKU,MAAMN,OAAOC,QAAUL,EAAKU,MAAMN,OAAOC,QAAQO,KAAKC,GAAcC,EAAMD,GAAME,OAAO,gBAAeC,KAAK,KAAO,GAC1IC,EAAS,CACbC,MAAO1B,EAAU0B,MACjBC,MAAO/B,EAAM+B,SACVxB,IACHU,QAASM,GAEXS,EACGC,eAAeJ,GACfK,MAAMC,IACLvB,EAAKU,MAAMR,SAAU,EAChBF,EAAAU,MAAMJ,SAAWiB,EAAIvB,KAAKwB,KACpB9B,EAAAgB,MAAMe,MAAQF,EAAIvB,KAAKyB,KAAA,IAEnCC,OAAM,KACL1B,EAAKU,MAAMR,SAAU,EACrByB,EAAUC,MAAM,CACdC,QAASvC,EAAE,gBACXwC,QAAQ,GACT,GACF,CAIL,SAASC,EAAWC,GAClBpC,EAAaoC,GAAMV,MAAK,IAAMb,KAAa,CAIpC,SAAAwB,EAAcC,EAAO,GAC5BrC,EAAgBqC,GAAMZ,MAAK,IAAMb,KAAa,CAIhD,SAAS0B,GAAWC,KAAEA,EAAMC,MAAAA,IAC1BvC,EAAasC,EAAMC,GAAOf,MAAK,IAAMb,KAAa,CAMpD,SAAS6B,EAAYC,GACb,MAAAC,EAAQjC,EAAOG,MAAM+B,MAAMC,GAAcA,EAAKC,OAASJ,EAAIC,QACjE,OAAOA,EAAQA,EAAMI,MAAQtD,EAAE,SAAQ,CAMzC,SAASuD,EAAcN,GACf,MAAAO,EAAUtC,EAASE,MAAM+B,MAAMC,GAAcA,EAAKC,OAASJ,EAAIQ,YACrE,OAAOD,EAAUA,EAAQE,KAAO1D,EAAE,SAAQ,QArG5C2D,GAAU,KA6BRC,EACGC,kBAAkB3D,EAAU0B,OAC5BI,MAAMC,IACY,IAAbA,EAAIoB,OACNnC,EAASE,MAAQa,EAAIvB,KAAA,IAGxB0B,OAAM,KACLC,EAAUC,MAAM,CACdC,QAASvC,EAAE,gBACXwC,QAAQ,GACT,IA7BFsB,EAAA5B,KAAK,CAAE6B,SAAUC,EAAaC,gBAC9BjC,MAAMC,IACY,IAAbA,EAAIoB,OACNpC,EAAOG,MAAQa,EAAIvB,KAAA,IAGtB0B,OAAM,KACLC,EAAUC,MAAM,CACdC,QAASvC,EAAE,gBACXwC,QAAQ,GACT,IAlBOrB,GAAA"}