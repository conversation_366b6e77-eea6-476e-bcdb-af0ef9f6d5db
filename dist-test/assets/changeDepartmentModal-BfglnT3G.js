import{d as e,aj as t,ai as a,B as s,b as l,y as o,o as i,e as n,w as r,f as p,h as c,Y as d,u as m,i as u,aR as f,aQ as b,m as g,x as h,q as _,ay as j}from"./index-CkEhI1Zk.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                          *//* empty css                 *//* empty css                  *//* empty css               *//* empty css                    *//* empty css                 *//* empty css                     */import{d as v}from"./dept.api-DoD8EACm.js";import{u as D}from"./user.api-BYl7ypOS.js";import{h as k}from"./grouptree-B8OdHqYc.js";import{_ as y}from"./_plugin-vue_export-helper-BCo6x5W8.js";const V=e({__name:"changeDepartmentModal",props:{modelValue:{type:Boolean,default:!1},userid:{default:""},useridList:{default:[]}},emits:["update:modelValue","success"],setup(e,{emit:y}){const V=e,I=y,{t:S}=t(),x=a(),C=s({get:()=>V.modelValue,set(e){I("update:modelValue",e)}}),q=l([]),w=l(),z=l({deptId:""}),U=l({deptId:[{required:!0,message:S("pleaseSelectDepartment"),trigger:["blur","change"]}]});function B(){w.value&&w.value.validate((e=>{e&&D.updateUserDept({deptId:z.value.deptId[z.value.deptId.length-1],ids:V.userid?[V.userid]:V.useridList.map((e=>e.id))}).then((e=>{f.success({message:S("changeDepartmentSuccess"),center:!0}),I("success"),L()}))}))}function L(){C.value=!1}return o((()=>{v.list({gcode:x.gcode}).then((e=>{q.value=k(e.data)}))})),(e,t)=>{const a=b,s=g,l=h,o=_,f=j;return i(),n(f,{modelValue:m(C),"onUpdate:modelValue":t[1]||(t[1]=e=>u(C)?C.value=e:null),title:m(S)("changeDepartment"),"close-on-click-modal":!1,width:"400px","append-to-body":"",modal:!0,"destroy-on-close":""},{footer:r((()=>[p(o,{size:"large",onClick:L},{default:r((()=>[c(d(m(S)("cancel")),1)])),_:1}),p(o,{type:"primary",size:"large",onClick:B},{default:r((()=>[c(d(m(S)("save")),1)])),_:1})])),default:r((()=>[p(l,{ref_key:"formRef",ref:w,model:m(z),rules:m(U),"label-suffix":""},{default:r((()=>[p(s,{label:m(S)("nickname"),prop:"deptId"},{default:r((()=>[p(a,{modelValue:m(z).deptId,"onUpdate:modelValue":t[0]||(t[0]=e=>m(z).deptId=e),options:m(q),props:{value:"id",label:"name"},"show-all-levels":!1},null,8,["modelValue","options"])])),_:1},8,["label"])])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])}}});function I(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{changeDepartment:{t:0,b:{t:2,i:[{t:3}],s:"Change Department"}},nickname:{t:0,b:{t:2,i:[{t:3}],s:"Nickname"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}},pleaseSelectDepartment:{t:0,b:{t:2,i:[{t:3}],s:"Please select a department"}},changeDepartmentSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Department changed successfully"}}},"zh-cn":{changeDepartment:{t:0,b:{t:2,i:[{t:3}],s:"变更部门"}},nickname:{t:0,b:{t:2,i:[{t:3}],s:"昵称"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},save:{t:0,b:{t:2,i:[{t:3}],s:"保存"}},pleaseSelectDepartment:{t:0,b:{t:2,i:[{t:3}],s:"请选择部门"}},changeDepartmentSuccess:{t:0,b:{t:2,i:[{t:3}],s:"变更部门成功"}}},km:{changeDepartment:{t:0,b:{t:2,i:[{t:3}],s:"ផ្លាស់ប្តូរនាយកដ្ឋាន"}},nickname:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះហៅក្រៅ"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},save:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}},pleaseSelectDepartment:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសនាយកដ្ឋាន"}},changeDepartmentSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានផ្លាស់ប្តូរនាយកដ្ឋានដោយជោគជ័យ"}}}}})}I(V);const S=y(V,[["__scopeId","data-v-8680f2c9"]]);export{S as default};
//# sourceMappingURL=changeDepartmentModal-BfglnT3G.js.map
