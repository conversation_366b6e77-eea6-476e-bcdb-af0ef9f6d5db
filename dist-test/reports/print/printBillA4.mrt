<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <printBill78 Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>printBill78</Alias>
        <HeadersString />
        <Key />
        <Name>printBill78</Name>
        <PathData>D:\11\2\aflower-pms-front\public\reports\print\printBill78.json</PathData>
      </printBill78>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="24">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>hname,System.String</value>
          <value>frontPhone,System.String</value>
          <value>address,System.String</value>
          <value>orderNo,System.String</value>
          <value>printDate,System.String</value>
          <value>name,System.String</value>
          <value>buildingName,System.String</value>
          <value>floorName,System.String</value>
          <value>rtName,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>orderRemark,System.String</value>
          <value>info,System.String</value>
          <value>balance,System.String</value>
          <value>point,System.String</value>
          <value>checkinTime,System.String</value>
          <value>checkoutTime,System.String</value>
          <value>payTotalFee,System.Decimal</value>
          <value>consumeTotalFee,System.Decimal</value>
          <value>paymentDetails,System.String</value>
          <value>consumptionDetails,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>b1f5c9ba210940598a75a6f9c5e390d1</Key>
        <Name>root</Name>
        <NameInSource>printBill78.root</NameInSource>
      </root>
      <root_consumptionDetails Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_consumptionDetails</Alias>
        <Columns isList="true" count="8">
          <value>bizDate,System.String</value>
          <value>accDetail,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>createTime,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>4aefbea7e9694fb98cd18054b5f826ca</Key>
        <Name>root_consumptionDetails</Name>
        <NameInSource>printBill78.root_consumptionDetails</NameInSource>
      </root_consumptionDetails>
      <root_paymentDetails Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_paymentDetails</Alias>
        <Columns isList="true" count="8">
          <value>bizDate,System.String</value>
          <value>accDetail,System.String</value>
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>createTime,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>11524c5e45494dd1a3a041d8e98516e7</Key>
        <Name>root_paymentDetails</Name>
        <NameInSource>printBill78.root_paymentDetails</NameInSource>
      </root_paymentDetails>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_consumptionDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>consumptionDetails</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root Ref="7" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_paymentDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>paymentDetails</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>0ef85986616a4f04acdff5fb1e254469</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand1 Ref="9" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,2.4</ClientRectangle>
          <Components isList="true" count="4">
            <Text5 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,11,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>{root.hname}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text1 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,19,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>房间账单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,4.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text3</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>NO:{root.orderNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,4.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text4</Name>
              <Page isRef="8" />
              <Parent isRef="9" />
              <Text>打印时间:{Time}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="14" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.6,19,2</ClientRectangle>
          <Components isList="true" count="20">
            <Text6 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>客人姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text2 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0,4.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>{root.name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text8 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text7 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,0,4.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>{root.rtName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text9 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,0,4.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>{root.rNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text15 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>入住时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text20 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,0.6,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>积分余额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text41 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,0.6,10.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>{root.point}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text26 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.2,4.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>{root.checkinTime.Substring(0, 16)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,1.2,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text30 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,1.2,10.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>{root.checkoutTime.Substring(0, 16)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text37 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>储值余额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text39 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0.6,4.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>{root.balance}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text13 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text13</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>账目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text14</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text23 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text23</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>付款</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <文本7 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,1.8,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本7</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,3.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本8</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>营业日</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本11 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本11</Name>
              <Page isRef="8" />
              <Parent isRef="14" />
              <Text>业务详情</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <PrintOn>OnlyFirstPage</PrintOn>
        </HeaderBand1>
        <DataBand1 Ref="35" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <CanBreak>True</CanBreak>
          <CanShrink>True</CanShrink>
          <ClientRectangle>0,6.4,19,2.4</ClientRectangle>
          <Components isList="true" count="1">
            <面板2 Ref="36" type="Panel" isKey="true">
              <Brush>Transparent</Brush>
              <CanBreak>True</CanBreak>
              <ClientRectangle>0,0,19,2.4</ClientRectangle>
              <Components isList="true" count="2">
                <数据区2 Ref="37" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <CanShrink>True</CanShrink>
                  <ClientRectangle>0,0.4,19,0.6</ClientRectangle>
                  <Components isList="true" count="6">
                    <文本1 Ref="38" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.2,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本1</Name>
                      <Page isRef="8" />
                      <Parent isRef="37" />
                      <Text>{root_consumptionDetails.subName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本1>
                    <文本2 Ref="39" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>12.6,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本2</Name>
                      <Page isRef="8" />
                      <Parent isRef="37" />
                      <Text>{root_consumptionDetails.fee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本2>
                    <文本6 Ref="40" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>15.8,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,8</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本6</Name>
                      <Page isRef="8" />
                      <Parent isRef="37" />
                      <Text>-</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本6>
                    <文本9 Ref="41" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,3.4,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本9</Name>
                      <Page isRef="8" />
                      <Parent isRef="37" />
                      <Text>{root_consumptionDetails.bizDate}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="42" type="DateFormat" isKey="true" />
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本9>
                    <文本10 Ref="43" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>3.4,0,2.8,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本10</Name>
                      <Page isRef="8" />
                      <Parent isRef="37" />
                      <Text>{root_consumptionDetails.rNo}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本10>
                    <文本12 Ref="44" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.4,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本12</Name>
                      <Page isRef="8" />
                      <Parent isRef="37" />
                      <Text>{root_consumptionDetails.accDetail}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本12>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_consumptionDetails</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>数据区2</Name>
                  <Page isRef="8" />
                  <Parent isRef="36" />
                  <Sort isList="true" count="0" />
                </数据区2>
                <数据区1 Ref="45" type="DataBand" isKey="true">
                  <Brush>Transparent</Brush>
                  <BusinessObjectGuid isNull="true" />
                  <CanShrink>True</CanShrink>
                  <ClientRectangle>0,1.8,19,0.6</ClientRectangle>
                  <Components isList="true" count="6">
                    <文本3 Ref="46" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>6.2,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本3</Name>
                      <Page isRef="8" />
                      <Parent isRef="45" />
                      <Text>{root_paymentDetails.subName}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本3>
                    <文本4 Ref="47" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>15.8,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本4</Name>
                      <Page isRef="8" />
                      <Parent isRef="45" />
                      <Text>{root_paymentDetails.fee}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本4>
                    <文本5 Ref="48" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>12.6,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,8</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本5</Name>
                      <Page isRef="8" />
                      <Parent isRef="45" />
                      <Text>-</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>Expression</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本5>
                    <文本13 Ref="49" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>9.4,0,3.2,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本13</Name>
                      <Page isRef="8" />
                      <Parent isRef="45" />
                      <Text>{root_paymentDetails.accDetail}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本13>
                    <文本14 Ref="50" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>3.4,0,2.8,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本14</Name>
                      <Page isRef="8" />
                      <Parent isRef="45" />
                      <Text>{root_paymentDetails.rNo}</Text>
                      <TextBrush>Black</TextBrush>
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本14>
                    <文本15 Ref="51" type="Text" isKey="true">
                      <Border>All;Black;1;Solid;False;4;Black</Border>
                      <Brush>Transparent</Brush>
                      <ClientRectangle>0,0,3.4,0.6</ClientRectangle>
                      <Conditions isList="true" count="0" />
                      <Expressions isList="true" count="0" />
                      <Font>Arial,9</Font>
                      <HorAlignment>Center</HorAlignment>
                      <Name>文本15</Name>
                      <Page isRef="8" />
                      <Parent isRef="45" />
                      <Text>{root_paymentDetails.bizDate}</Text>
                      <TextBrush>Black</TextBrush>
                      <TextFormat Ref="52" type="DateFormat" isKey="true" />
                      <Type>DataColumn</Type>
                      <VertAlignment>Center</VertAlignment>
                    </文本15>
                  </Components>
                  <Conditions isList="true" count="0" />
                  <DataRelationName isNull="true" />
                  <DataSourceName>root_paymentDetails</DataSourceName>
                  <Expressions isList="true" count="0" />
                  <Filters isList="true" count="0" />
                  <Name>数据区1</Name>
                  <Page isRef="8" />
                  <Parent isRef="36" />
                  <Sort isList="true" count="0" />
                </数据区1>
              </Components>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Name>面板2</Name>
              <Page isRef="8" />
              <Parent isRef="35" />
            </面板2>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <FooterBand1 Ref="53" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.6,19,4.4</ClientRectangle>
          <Components isList="true" count="5">
            <Text25 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,12.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text25</Name>
              <Page isRef="8" />
              <Parent isRef="53" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text28 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text28</Name>
              <Page isRef="8" />
              <Parent isRef="53" />
              <Text>{root.consumeTotalFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text29</Name>
              <Page isRef="8" />
              <Parent isRef="53" />
              <Text>{root.payTotalFee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text11 Ref="57" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.4,19,1.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text11</Name>
              <Page isRef="8" />
              <Parent isRef="53" />
              <Text>
酒店电话：{root.frontPhone}     宾客签名：

酒店地址：{root.address}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text11>
            <Text31 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0.8,19,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,3,0</Margins>
              <Name>Text31</Name>
              <Page isRef="8" />
              <Parent isRef="53" />
              <Text>订单备注：
{root.orderRemark}

备注：
{root.info}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
            </Text31>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <PrintOn>OnlyLastPage</PrintOn>
        </FooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>da66d5df7e7a4844bed63ecba995b7c7</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>房间账单</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/2/2025 2:00:02 PM</ReportChanged>
  <ReportCreated>11/1/2024 9:59:01 AM</ReportCreated>
  <ReportFile>D:\11\2\aflower-pms-front\public\reports\print\printBillA4.mrt</ReportFile>
  <ReportGuid>594fc8871d704924b514e12be25503bf</ReportGuid>
  <ReportName>房间账单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 房间账单 : Stimulsoft.Report.StiReport
    {
        public 房间账单()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>