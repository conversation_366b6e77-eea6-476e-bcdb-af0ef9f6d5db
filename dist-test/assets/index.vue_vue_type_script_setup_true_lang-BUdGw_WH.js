import{d as e,B as a,o as l,e as d,w as s,c as u,F as o,ag as t,u as i,i as r,l as n,ab as b,ax as m,E as v}from"./index-CkEhI1Zk.js";/* empty css                       *//* empty css                 *//* empty css                        */const p={key:0},y={key:1},K=e({__name:"index",props:{data:{},modelValue:{},radioValue:{},labelKey:{default:"label"},valueKey:{default:"value"},disabled:{type:Boolean},size:{}},emits:["update:modelValue","onChange"],setup(e,{emit:K}){const V=e;const f=K,z=a({get:()=>V.modelValue,set(e){f("update:modelValue",e)}});return(e,a)=>{const K=m,V=v,g=n;return l(),d(g,{modelValue:i(z),"onUpdate:modelValue":a[0]||(a[0]=e=>r(z)?z.value=e:null),size:e.size,disabled:e.disabled,onChange:a[1]||(a[1]=e=>{return a=i(z),void f("onChange",a);var a})},{default:s((()=>["radioButton"==e.radioValue?(l(),u("div",p,[(l(!0),u(o,null,t(e.data,(a=>(l(),d(K,{label:a[e.labelKey],value:a[e.valueKey]},{default:s((()=>[b(e.$slots,"default",{label:a[e.labelKey],value:a[e.valueKey]})])),_:2},1032,["label","value"])))),256))])):(l(),u("div",y,[(l(!0),u(o,null,t(e.data,(a=>(l(),d(V,{border:a.border,name:a.name,size:a.size,label:a[e.labelKey],value:a[e.valueKey]},{default:s((()=>[b(e.$slots,"default",{label:a[e.labelKey],value:a[e.valueKey]})])),_:2},1032,["border","name","size","label","value"])))),256))]))])),_:3},8,["modelValue","size","disabled"])}}});export{K as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js.map
