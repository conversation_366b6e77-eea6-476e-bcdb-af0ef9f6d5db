{"version": 3, "file": "roomHandle-C4e1TRGz.js", "sources": ["../../src/views/room/realtime/components/roomHandle.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"zh-cn\": {\r\n    \"loading\": \"载入中...\",\r\n    \"unlockRoom\": \"解除锁房\",\r\n    \"orderDetail\": \"详单\",\r\n    \"account\": \"账务\",\r\n    \"finishRepair\": \"完成维修\",\r\n    \"checkin\": \"入住\",\r\n    \"hourlyCheckin\": \"钟点入住\",\r\n    \"booking\": \"预订\",\r\n    \"markDirty\": \"置脏\",\r\n    \"markRepair\": \"置维修\",\r\n    \"lockRoom\": \"锁房\",\r\n    \"markClean\": \"置干净\",\r\n    \"continueStay\": \"续住\",\r\n    \"joinRoom\": \"加入联房\",\r\n    \"joinTeam\": \"加入团队\",\r\n    \"leaveRoom\": \"脱离联房\",\r\n    \"transferTeam\": \"转出团队\",\r\n    \"postAccount\": \"入账\",\r\n    \"changeRoom\": \"换房\",\r\n    \"log\": \"日志\",\r\n    \"repairReason\": \"维修原因\",\r\n    \"roomRemark\": \"房间备注\",\r\n    \"updateRoomRemark\": \"修改房间备注\",\r\n    \"updateOrderRemark\": \"修改订单备注\",\r\n    \"editRemark\": \"修改\",\r\n    \"saveRemark\": \"保存\",\r\n    \"guestName\": \"客人姓名\",\r\n    \"checkinTime\": \"入住时间\",\r\n    \"checkoutTime\": \"预离时间\",\r\n    \"guestSource\": \"客源类型\",\r\n    \"channelName\": \"渠道名称\",\r\n    \"todayPrice\": \"今日房价\",\r\n    \"balance\": \"余额\",\r\n    \"consume\": \"消费\",\r\n    \"payment\": \"付款\",\r\n    \"preAuth\": \"预授权\",\r\n    \"orderRemark\": \"订单备注\",\r\n    \"startRepair\": \"开始时间\",\r\n    \"endRepair\": \"结束时间\",\r\n    \"orderNo\": \"订单号\",\r\n    \"bookingName\": \"预订人\",\r\n    \"bookingPhone\": \"电话\",\r\n    \"bookingFee\": \"付款\",\r\n    \"bookingSource\": \"客源类型\",\r\n    \"bookingType\": \"入住类型\",\r\n    \"guaranteeStyle\": \"担保方式\",\r\n    \"setHourRoom\": \"当前房型不支持钟点房，请先前往[设置]-[基础设置]-[钟点房] 维护钟点房才可以办理钟点房入住.\",\r\n    \"dirtyRooms\":\"脏房不可入住！\",\r\n    \"cleaningTask\": \"清扫任务\",\r\n    \"order_remark_update_success\": \"订单备注更新成功\",\r\n    \"lockReason\": \"锁房原因\"\r\n  },\r\n  \"en\": {\r\n    \"loading\": \"Loading...\",\r\n    \"unlockRoom\": \"Unlock Room\",\r\n    \"orderDetail\": \"Detail\",\r\n    \"account\": \"Account\",\r\n    \"finishRepair\": \"Finish Repair\",\r\n    \"checkin\": \"Check-in\",\r\n    \"hourlyCheckin\": \"Hourly Check-in\",\r\n    \"booking\": \"Booking\",\r\n    \"markDirty\": \"Mark Dirty\",\r\n    \"markRepair\": \"Mark Repair\",\r\n    \"lockRoom\": \"Lock Room\",\r\n    \"markClean\": \"Mark Clean\",\r\n    \"continueStay\": \"Extend\",\r\n    \"joinRoom\": \"Join Room\",\r\n    \"joinTeam\": \"Join Team\",\r\n    \"leaveRoom\": \"Leave Room\",\r\n    \"transferTeam\": \"Transfer Team\",\r\n    \"postAccount\": \"Post\",\r\n    \"changeRoom\": \"Change\",\r\n    \"log\": \"Log\",\r\n    \"repairReason\": \"Repair Reason\",\r\n    \"roomRemark\": \"Room Remark\",\r\n    \"updateRoomRemark\": \"Edit Room Remark\",\r\n    \"updateOrderRemark\": \"Edit Order Remark\",\r\n    \"editRemark\": \"Edit\",\r\n    \"saveRemark\": \"Save\",\r\n    \"guestName\": \"Name\",\r\n    \"checkinTime\": \"Check-in\",\r\n    \"checkoutTime\": \"Check-out\",\r\n    \"guestSource\": \"Guest Source\",\r\n    \"channelName\": \"Channel\",\r\n    \"todayPrice\": \"Today's Price\",\r\n    \"balance\": \"Balance\",\r\n    \"consume\": \"Consume\",\r\n    \"payment\": \"Payment\",\r\n    \"preAuth\": \"Pre-Auth\",\r\n    \"orderRemark\": \"Order Remark\",\r\n    \"startRepair\": \"Start Time\",\r\n    \"endRepair\": \"End Time\",\r\n    \"orderNo\": \"Order No\",\r\n    \"bookingName\": \"Booking Name\",\r\n    \"bookingPhone\": \"Booking Phone\",\r\n    \"bookingFee\": \"Booking Fee\",\r\n    \"bookingSource\": \"Booking Source\",\r\n    \"bookingType\": \"Booking Type\",\r\n    \"guaranteeStyle\": \"Guarantee Style\",\r\n    \"setHourRoom\": \"The current room type does not support hourly booking. Please go to [Settings] - [Basic Settings] - [Hourly Booking] to maintain hourly booking information before proceeding with hourly booking check-in.\",\r\n    \"dirtyRooms\": \"Dirty rooms cannot be checked in!\",\r\n    \"cleaningTask\": \"Cleaning Task\",\r\n    \"order_remark_update_success\": \"Order remark updated successfully\",\r\n    \"lockReason\": \"Lock Reason\"\r\n  },\r\n  \"km\": {\r\n    \"loading\": \"កំពុងផ្ទុក...\",\r\n    \"unlockRoom\": \"ដោះសោ\",\r\n    \"orderDetail\": \"ព័ត៌មាន\",\r\n    \"account\": \"គណនី\",\r\n    \"finishRepair\": \"ចប់ជួសជុល\",\r\n    \"checkin\": \"ចូល\",\r\n    \"hourlyCheckin\": \"ចូលម៉ោង\",\r\n    \"booking\": \"កក់\",\r\n    \"markDirty\": \"កខ្វក់\",\r\n    \"markRepair\": \"ជួសជុល\",\r\n    \"lockRoom\": \"ចាក់សោ\",\r\n    \"markClean\": \"ស្អាត\",\r\n    \"continueStay\": \"បន្ត\",\r\n    \"joinRoom\": \"ចូលរួម\",\r\n    \"joinTeam\": \"ចូលក្រុម\",\r\n    \"leaveRoom\": \"ចេញ\",\r\n    \"transferTeam\": \"ផ្ទេរ\",\r\n    \"postAccount\": \"បញ្ចូល\",\r\n    \"changeRoom\": \"ប្តូរ\",\r\n    \"log\": \"កំណត់ហេតុ\",\r\n    \"repairReason\": \"ហេតុជួសជុល\",\r\n    \"roomRemark\": \"ផ្នែកបន្ទប់\",\r\n    \"updateRoomRemark\": \"កែបន្ទប់\",\r\n    \"updateOrderRemark\": \"កែបញ្ជាទិញ\",\r\n    \"editRemark\": \"កែ\",\r\n    \"saveRemark\": \"រក្សា\",\r\n    \"guestName\": \"ឈ្មោះ\",\r\n    \"checkinTime\": \"ម៉ោងចូល\",\r\n    \"checkoutTime\": \"ម៉ោងចេញ\",\r\n    \"guestSource\": \"ប្រភេទភ្ញៀវ\",\r\n    \"channelName\": \"ឆានែល\",\r\n    \"todayPrice\": \"តម្លៃថ្ងៃនេះ\",\r\n    \"balance\": \"សមតុល្យ\",\r\n    \"consume\": \"ប្រើប្រាស់\",\r\n    \"payment\": \"ទូទាត់\",\r\n    \"preAuth\": \"អនុញ្ញាតមុន\",\r\n    \"orderRemark\": \"ផ្នែកបញ្ជាទិញ\",\r\n    \"startRepair\": \"ចាប់ផ្តើម\",\r\n    \"endRepair\": \"បញ្ចប់\",\r\n    \"orderNo\": \"លេខបញ្ជាទិញ\",\r\n    \"bookingName\": \"ឈ្មោះកក់\",\r\n    \"bookingPhone\": \"ទូរស័ព្ទ\",\r\n    \"bookingFee\": \"ថ្លៃកក់\",\r\n    \"bookingSource\": \"ប្រភពកក់\",\r\n    \"bookingType\": \"ប្រភេទចូល\",\r\n    \"guaranteeStyle\": \"វិធីសាក្សី\",\r\n    \"setHourRoom\": \"បន្ទប់មិនគាំទ្រម៉ោង។ សូមកំណត់នៅ [ការកំណត់] - [មូលដ្ឋាន] - [ម៉ោង]។\",\r\n    \"dirtyRooms\": \"បន្ទប់កខ្វក់មិនអាចចូលបាន។\",\r\n    \"cleaningTask\": \"សម្អាត\",\r\n    \"order_remark_update_success\": \"ការកែប្រែចំណាំការបញ្ជាទិញបានជោគជ័យ\",\r\n    \"lockReason\": \"លាក់សោ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { OrderHousePanelInfoRespVO } from '@/models/biz/order/orderHousePanelInfoRespVO.model'\r\nimport type { BookRespVO, RoomRespVO } from '@/models/biz/order/roomRespVO'\r\nimport { generalConfigApi, hotelParamConfigApi, hourRoomTypeApi, orderApi, roomApi } from '@/api/modules/index'\r\nimport { DICT_TYPE_ROOM_HOUSEKEEPING } from '@/models/dict/constants'\r\n\r\nimport { BooleanEnum, IdType, OrderState, OrderType, RoomState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { limitedText } from '@/utils/myStringUtil'\r\nimport dayjs from 'dayjs'\r\n\r\ninterface IRoomProps {\r\n  rCode?: string\r\n  noType?: string\r\n  bookInfo?: any\r\n}\r\nconst props = defineProps<IRoomProps>()\r\n\r\nconst emits = defineEmits<{\r\n  buttonClicked: [data: any]\r\n}>()\r\n\r\n// 设置option默认值，如果传入自定义的配置则合并option配置项\r\nconst _options: ComputedRef<IRoomProps> = computed(() => {\r\n  const option = {\r\n    rCode: '',\r\n    noType: '',\r\n    bookInfo: {},\r\n  }\r\n  return Object.assign(option, props)\r\n})\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n/** 查询条件 */\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n\r\ninterface RoomOrder {\r\n  isOOPlanned: string\r\n  room: RoomRespVO\r\n  book?: BookRespVO\r\n  order?: OrderHousePanelInfoRespVO\r\n}\r\n\r\nconst loading = ref(false)\r\n/** 是否显示开启清扫任务 */\r\nconst isVisible = ref()\r\nconst form = ref({\r\n  remark: '',\r\n  orderRemark: '',\r\n})\r\nconst ro = ref<RoomOrder>()\r\nconst book_Info = ref<BookRespVO>()\r\nconst isEdit = ref(false)\r\nconst isOrderRemarkEdit = ref(false)\r\nonMounted(() => {\r\n  getRoomOrder()\r\n  getRoomCleanType()\r\n})\r\n\r\n/** 获取字典类型 */\r\nasync function getRoomCleanType() {\r\n  const params = { ...queryParams, types: [DICT_TYPE_ROOM_HOUSEKEEPING].join(',') }\r\n  const { data } = await generalConfigApi.list(params)\r\n  data.forEach((item) => {\r\n    if (item.code == 'clean_task') {\r\n      isVisible.value = item.value\r\n    }\r\n  })\r\n}\r\n\r\nfunction getRoomOrder() {\r\n  form.value.remark = ''\r\n  form.value.orderRemark = ''\r\n  loading.value = true\r\n  orderApi\r\n    .getRoomInfo({\r\n      ...queryParams,\r\n      rCode: _options.value.rCode,\r\n    })\r\n    .then((res: any) => {\r\n      loading.value = false\r\n      if (res.code === 0) {\r\n        ro.value = res.data\r\n        book_Info.value = res.data.book\r\n        form.value.remark = res.data.room.remark\r\n        form.value.orderRemark = res.data.order?.remark || ''\r\n      }\r\n    })\r\n}\r\n\r\n/**\r\n * 订单备注修改\r\n */\r\nfunction updateOrderRemark() {\r\n  if (!ro.value?.order?.orderNo) {\r\n    return\r\n  }\r\n\r\n  const params = {\r\n    ...queryParams,\r\n    orderNo: ro.value.order.orderNo,\r\n    remark: form.value.orderRemark,\r\n  }\r\n\r\n  orderApi.updateOrderRemark(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      isOrderRemarkEdit.value = false\r\n      if (ro.value && ro.value.order) {\r\n        ro.value.order.remark = form.value.orderRemark\r\n      }\r\n      ElMessage.success(t('order_remark_update_success'))\r\n    } else {\r\n      ElMessage.error(res.msg)\r\n    }\r\n  })\r\n}\r\n\r\n/**\r\n * 房间备注修改\r\n */\r\nfunction updateRoomRemark() {\r\n  const params = {\r\n    ...queryParams,\r\n    rCode: _options.value.rCode,\r\n    remark: form.value.remark,\r\n  }\r\n  roomApi.updateRoomRemark(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      isEdit.value = false\r\n      getRoomOrder()\r\n    } else {\r\n      ElMessage.error(res.msg)\r\n    }\r\n  })\r\n}\r\n/**\r\n * 打开对应的弹窗\r\n * @param who 需打开的弹窗\r\n * @param val 0 空脏\r\n */\r\nasync function onClicked(who: string, val?: number) {\r\n  // 处理详单和账务按钮的特殊逻辑\r\n  if (who === 'detail' || who === 'account') {\r\n    let no: string | number | undefined\r\n    let noType: string\r\n\r\n    // 根据房间状态设置正确的noType和no\r\n    if (ro.value?.order?.orderNo) {\r\n      // 如果有订单，使用订单号和订单类型\r\n      no = ro.value.order.orderNo\r\n      noType = 'order'\r\n    } else if (ro.value?.book?.xDay === 0) {\r\n      // 如果有当天入住的预订单，使用预订单号和预订单类型\r\n      no = ro.value.book.bookNo\r\n      if (ro.value.book.bookType === OrderType.GROUP) {\r\n        noType = 'team'\r\n      } else {\r\n        noType = 'book'\r\n      }\r\n    } else {\r\n      // 默认值\r\n      no = ro.value?.order?.orderNo || ro.value?.book?.bookNo\r\n      noType = 'order'\r\n    }\r\n\r\n    emits('buttonClicked', {\r\n      who,\r\n      no,\r\n      noType,\r\n    })\r\n    return\r\n  }\r\n\r\n  let data: {\r\n    rNo: any\r\n    rtCode: any\r\n    rCode: string | undefined | null\r\n    lockNo: string | undefined | null\r\n    mac: string | undefined | null\r\n    lockVersion: string | undefined | null\r\n    buildNo: string | undefined | null\r\n    floorNo: string | undefined | null\r\n    rtName: any\r\n    idType: string\r\n    bkTicketNum: number\r\n    list: { guestName: string; phone: string; idType: string; idNo: string }[]\r\n  }[] = []\r\n  data = [\r\n    {\r\n      rNo: ro.value?.room.rNo,\r\n      rCode: ro.value?.room.rCode,\r\n      lockNo: ro.value?.room.lockNo,\r\n      mac: ro.value?.room.mac,\r\n      lockVersion: ro.value?.room.lockVersion,\r\n      buildNo: ro.value?.room.buildNo,\r\n      floorNo: ro.value?.room.floorNo,\r\n      rtCode: ro.value?.room.rtCode,\r\n      rtName: ro.value?.room.rtName,\r\n      idType: IdType.IDCERT,\r\n      bkTicketNum: 0,\r\n      list: [] as {\r\n        guestName: string\r\n        phone: string\r\n        idType: string\r\n        idNo: string\r\n      }[],\r\n    },\r\n  ]\r\n\r\n  // 检查是否为入住操作（全天入住或钟点入住）\r\n  if (who === 'allDay' || who === 'hour') {\r\n    // val 不等于1时 则调用获得门店参数设置-前台接口进行查询\r\n    if (val !== 1) {\r\n      const res = await hotelParamConfigApi.getHotelParamConfigFront(queryParams)\r\n      if (res.data.value.dirtyAlert === '0') {\r\n        return ElMessage.warning(t('dirtyRooms'))\r\n      }\r\n    }\r\n\r\n    const result = await hasHourConfig()\r\n    if (who === 'hour' && !result) {\r\n      return ElMessage.warning(t('setHourRoom'))\r\n    }\r\n\r\n    emits('buttonClicked', {\r\n      who,\r\n      data,\r\n      canChangePrice: ro.value?.order?.canChangePrice,\r\n      togetherCode: ro.value?.order?.togetherCode,\r\n      guestCode: ro.value?.order?.guestCode,\r\n      orderSource: ro.value?.order?.orderSource,\r\n      hourCode: ro.value?.order?.hourCode,\r\n      orderNo: ro.value?.order?.orderNo,\r\n    })\r\n  } else {\r\n    // val 不等于1时 则调用获得门店参数设置-前台接口进行查询\r\n    if (val !== 1) {\r\n      const res = await hotelParamConfigApi.getHotelParamConfigFront(queryParams)\r\n      if (res.data.value.dirtyAlert === '0') {\r\n        return ElMessage.warning(t('dirtyRooms'))\r\n      }\r\n    }\r\n\r\n    const result = await hasHourConfig()\r\n    if (who === 'hour' && !result) {\r\n      return ElMessage.warning(t('setHourRoom'))\r\n    }\r\n\r\n    emits('buttonClicked', {\r\n      who,\r\n      data,\r\n      canChangePrice: ro.value?.order?.canChangePrice,\r\n      togetherCode: ro.value?.order?.togetherCode,\r\n      guestCode: ro.value?.order?.guestCode,\r\n      orderSource: ro.value?.order?.orderSource,\r\n      hourCode: ro.value?.order?.hourCode,\r\n      orderNo: ro.value?.order?.orderNo,\r\n    })\r\n  }\r\n}\r\n\r\n/**\r\n * 获取门店配置的钟点房，如果没有，那么当点击\"钟点入住\"时需要提醒\r\n */\r\nasync function hasHourConfig(): Promise<boolean | { message: string }> {\r\n  const params = {\r\n    ...queryParams,\r\n    rtCode: ro.value?.room.rtCode,\r\n  }\r\n  try {\r\n    const res = (await hourRoomTypeApi.getHourRoomTypeList(params)) as any\r\n    if (res.code !== 0) {\r\n      throw new Error('请求报错..')\r\n    }\r\n    if (res.data.length > 0) {\r\n      return true\r\n    } else {\r\n      return false\r\n    }\r\n  } catch (error) {\r\n    return { message: '请求报错..' }\r\n  }\r\n}\r\n\r\nfunction orderRemarkClick() {\r\n  isOrderRemarkEdit.value = true\r\n  form.value.orderRemark = ro.value?.order?.remark || ''\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"room-handle-container\">\r\n    <div v-if=\"loading\" class=\"custom-loading-container\">\r\n      <div class=\"loader\" />\r\n      <p>{{ t('loading') }}</p>\r\n    </div>\r\n    <div v-else class=\"room-info-wrapper\">\r\n      <!-- 房间标题和状态 -->\r\n      <div class=\"room-header\">\r\n        <div class=\"room-title\">\r\n          {{ ro?.room.rtName }}-{{ ro?.room.rNo }}\r\n          <el-tag v-if=\"props.noType === OrderState.IN_BOOKING\" type=\"primary\" effect=\"light\" size=\"small\">\r\n            {{ t('booking') }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 操作按钮区域 -->\r\n      <div class=\"buttons-container\">\r\n        <template v-if=\"ro?.room.isLocked === '1'\">\r\n          <el-button v-auth=\"'pms:room:update:update-room-state'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('unlock', 1)\">\r\n            {{ t('unlockRoom') }}\r\n          </el-button>\r\n        </template>\r\n        <template v-else-if=\"props.noType === OrderState.IN_BOOKING\">\r\n          <el-button v-auth=\"'pms:order:query:get-order-detail'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('detail', 1)\">\r\n            {{ t('orderDetail') }}\r\n          </el-button>\r\n          <el-button v-auth=\"'pms:account:query:page'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('account', 1)\">\r\n            {{ t('account') }}\r\n          </el-button>\r\n        </template>\r\n        <template v-else-if=\"props.noType === 'repair'\">\r\n          <el-button v-auth=\"'pms:room:update:finish-repair'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('finishRepair', 1)\">\r\n            {{ t('finishRepair') }}\r\n          </el-button>\r\n        </template>\r\n        <template v-else>\r\n          <!-- 空净 -->\r\n          <template v-if=\"ro?.room.state === RoomState.VC\">\r\n            <el-button v-auth=\"'pms:order:create:check-in'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('allDay', 1)\">\r\n              {{ t('checkin') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:order:create:check-in'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('hour', 1)\">\r\n              {{ t('hourlyCheckin') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:book:create'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('booking', 1)\">\r\n              {{ t('booking') }}\r\n            </el-button>\r\n            <el-button v-if=\"!props.noType\" v-auth=\"'pms:room:update:update-room-state'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('dirty', 1)\">\r\n              {{ t('markDirty') }}\r\n            </el-button>\r\n            <el-button v-if=\"ro?.isOOPlanned === BooleanEnum.YES\" v-auth=\"'pms:room:update:finish-repair'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('finishRepair', 1)\">\r\n              {{ t('finishRepair') }}\r\n            </el-button>\r\n            <el-button v-else v-auth=\"'pms:room:update:update-room-state'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('repairVisible', 1)\">\r\n              {{ t('markRepair') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:room:update:update-room-state'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('lockVisible', 1)\">\r\n              {{ t('lockRoom') }}\r\n            </el-button>\r\n          </template>\r\n          <!-- 空脏 -->\r\n          <template v-else-if=\"ro?.room.state === RoomState.VD\">\r\n            <el-button v-auth=\"'pms:order:create:check-in'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('allDay', 0)\">\r\n              {{ t('checkin') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:order:create:check-in'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('hour', 0)\">\r\n              {{ t('hourlyCheckin') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:book:create'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('booking', 1)\">\r\n              {{ t('booking') }}\r\n            </el-button>\r\n            <el-button v-if=\"!props.noType && isVisible === BooleanEnum.NO\" v-auth=\"'pms:room:update:turn-clean'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('clean', 1)\">\r\n              {{ t('markClean') }}\r\n            </el-button>\r\n            <el-button v-if=\"ro?.isOOPlanned === BooleanEnum.YES\" v-auth=\"'pms:room:update:finish-repair'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('finishRepair', 1)\">\r\n              {{ t('finishRepair') }}\r\n            </el-button>\r\n            <el-button v-else v-auth=\"'pms:room:update:update-room-state'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('repairVisible', 1)\">\r\n              {{ t('markRepair') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:room:update:update-room-state'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('lockVisible', 1)\">\r\n              {{ t('lockRoom') }}\r\n            </el-button>\r\n          </template>\r\n          <!-- 住净 -->\r\n          <template v-else-if=\"ro?.room.state === RoomState.OC\">\r\n            <el-button v-auth=\"'pms:order:query:get-order-detail'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('detail', 1)\">\r\n              {{ t('orderDetail') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:account:query:page'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('account', 1)\">\r\n              {{ t('account') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:order:update:continue-in'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('continue', 1)\">\r\n              {{ t('continueStay') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:book:create'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('nextBooking', 1)\">\r\n              {{ t('booking') }}\r\n            </el-button>\r\n            <el-button v-if=\"!props.noType\" v-auth=\"'pms:room:update:update-room-state'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('dirty', 1)\">\r\n              {{ t('markDirty') }}\r\n            </el-button>\r\n            <el-button v-if=\"ro?.order?.orderType === OrderType.GENERAL || ro?.order?.orderType === OrderType.JOIN\" v-auth=\"'pms:order:update:merge-room'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('entryJoin', 1)\">\r\n              {{ t('joinRoom') }}\r\n            </el-button>\r\n            <!--            <el-button v-auth=\"'pms:order:update:change-in-team'\" v-if=\"ro?.order?.orderType === OrderType.GENERAL\" type=\"primary\" plain size=\"small\" -->\r\n            <!--              @click=\"onClicked('teamIn')\">{{ t('joinTeam') }}</el-button> -->\r\n            <el-button v-if=\"ro?.order?.orderType === OrderType.JOIN\" v-auth=\"'pms:order:update:quit-merge-room'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('away', 1)\">\r\n              {{ t('leaveRoom') }}\r\n            </el-button>\r\n            <!--            <el-button v-auth=\"'pms:order:update:quit-team'\" v-if=\"ro?.order?.orderType === OrderType.GROUP\" type=\"primary\" plain size=\"small\" -->\r\n            <!--              @click=\"onClicked('teamOut')\">{{ t('transferTeam') }}</el-button> -->\r\n            <el-button v-auth=\"'pms:account:create'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('entryAccount', 1)\">\r\n              {{ t('postAccount') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:order:update:change-room'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('exchange', 1)\">\r\n              {{ t('changeRoom') }}\r\n            </el-button>\r\n            <!--            <el-button type=\"primary\" plain size=\"small\" @click=\"onClicked('onClicked')\">查房</el-button> -->\r\n            <!--            <el-button type=\"primary\" plain size=\"small\" @click=\"\">制卡</el-button> -->\r\n          </template>\r\n          <!-- 住脏 -->\r\n          <template v-else-if=\"ro?.room.state === RoomState.OD\">\r\n            <el-button v-auth=\"'pms:order:query:get-order-detail'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('detail', 1)\">\r\n              {{ t('orderDetail') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:account:query:page'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('account', 1)\">\r\n              {{ t('account') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:order:update:continue-in'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('continue', 1)\">\r\n              {{ t('continueStay') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:book:create'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('nextBooking', 1)\">\r\n              {{ t('booking') }}\r\n            </el-button>\r\n            <el-button v-if=\"!props.noType && isVisible === BooleanEnum.NO\" v-auth=\"'pms:room:update:turn-clean'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('clean', 1)\">\r\n              {{ t('markClean') }}\r\n            </el-button>\r\n            <el-button v-if=\"ro?.order?.orderType === OrderType.GENERAL || ro?.order?.orderType === OrderType.JOIN\" v-auth=\"'pms:order:update:merge-room'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('entryJoin', 1)\">\r\n              {{ t('joinRoom') }}\r\n            </el-button>\r\n            <!--            <el-button v-auth=\"'pms:order:update:change-in-team'\" v-if=\"ro?.order?.orderType === OrderType.GENERAL\" type=\"primary\" plain size=\"small\" -->\r\n            <!--              @click=\"onClicked('teamIn')\">{{ t('joinTeam') }}</el-button> -->\r\n            <el-button v-if=\"ro?.order?.orderType === OrderType.JOIN\" v-auth=\"'pms:order:update:quit-merge-room'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('away', 1)\">\r\n              {{ t('leaveRoom') }}\r\n            </el-button>\r\n            <!--            <el-button v-auth=\"'pms:order:update:quit-team'\" v-if=\"ro?.order?.orderType === OrderType.GROUP\" type=\"primary\" plain size=\"small\" -->\r\n            <!--              @click=\"onClicked('teamOut')\">{{ t('transferTeam') }}</el-button> -->\r\n            <el-button v-auth=\"'pms:account:create'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('entryAccount', 1)\">\r\n              {{ t('postAccount') }}\r\n            </el-button>\r\n            <el-button v-auth=\"'pms:order:update:change-room'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('exchange', 1)\">\r\n              {{ t('changeRoom') }}\r\n            </el-button>\r\n            <!--            <el-button type=\"primary\" plain size=\"small\" @click=\"onClicked('onClicked')\">查房</el-button> -->\r\n            <!--            <el-button type=\"primary\" plain size=\"small\" @click=\"\">制卡</el-button> -->\r\n          </template>\r\n          <!-- 维修 -->\r\n          <template v-else>\r\n            <el-button v-auth=\"'pms:room:update:finish-repair'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('finishRepair', 1)\">\r\n              {{ t('finishRepair') }}\r\n            </el-button>\r\n          </template>\r\n        </template>\r\n        <el-button v-auth=\"'pms:room-log:query'\" type=\"primary\" plain size=\"small\" @click=\"onClicked('logVisible', 1)\">\r\n          {{ t('log') }}\r\n        </el-button>\r\n        <el-button v-if=\"ro?.room.state !== RoomState.OO && isVisible === BooleanEnum.YES.toString()\" type=\"primary\" plain size=\"small\" @click=\"onClicked('task', 1)\">\r\n          {{ t('cleaningTask') }}\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 房间信息区域 -->\r\n      <div class=\"room-content\">\r\n        <div v-if=\"book_Info && book_Info.state === OrderState.IN_BOOKING\">\r\n          <div class=\"info-grid\">\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('bookingName') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.contact }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('checkinTime') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.planCheckinTime ? dayjs(book_Info.planCheckinTime).format('YYYY-MM-DD HH:mm') : '' }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('bookingPhone') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.phone }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('checkoutTime') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.planCheckoutTime }}</span>\r\n            </div>\r\n            <div class=\"info-item full-width\">\r\n              <span class=\"info-label\">{{ t('orderNo') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.orderNo }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('bookingType') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.checkinTypeName }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('bookingFee') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.fee }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('guestSource') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.guestSrcTypeName }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('channelName') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.channelName }}</span>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <span class=\"info-label\">{{ t('guaranteeStyle') }}</span>\r\n              <span class=\"info-value\">{{ book_Info.guarantyStyleName }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div v-else>\r\n          <!-- 锁房 -->\r\n          <template v-if=\"ro?.room.isLocked === '1'\">\r\n            <div class=\"info-section\">\r\n              <div class=\"info-item full-width\">\r\n                <span class=\"info-label\">{{ t('lockReason') }}</span>\r\n                <span class=\"info-value\">{{ ro?.room.lockedReason }}</span>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-else>\r\n            <!-- 如果是住净或住脏 -->\r\n            <template v-if=\"ro?.room.state === RoomState.OC || ro?.room.state === RoomState.OD\">\r\n              <div class=\"info-grid\">\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('guestName') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.names }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('checkinTime') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.checkinTime ? dayjs(ro?.order?.checkinTime).format('YYYY-MM-DD HH:mm') : '' }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('guestSource') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.guestSrcTypeName }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('checkoutTime') }}：</span>\r\n                  <span class=\"info-value\">\r\n                    {{ ro?.order?.planCheckoutTime ? dayjs(ro?.order?.planCheckoutTime).format('YYYY-MM-DD HH:mm') : '' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('bookingType') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.checkinTypeName }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('channelName') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.channelName }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('todayPrice') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.vipPrice?.toFixed(2) }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('balance') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.balanceAmount?.toFixed(2) }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('consume') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.consumeAmount?.toFixed(2) }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('payment') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.payAmount?.toFixed(2) }}</span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('preAuth') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.order?.preAuthAmount?.toFixed(2) }}</span>\r\n                </div>\r\n                <div class=\"info-item full-width\">\r\n                  <span class=\"info-label\">{{ t('orderRemark') }}：</span>\r\n                  <div class=\"info-value order-remark-container\">\r\n                    <template v-if=\"ro?.order?.remark !== undefined\">\r\n                      <div v-if=\"!isOrderRemarkEdit\" class=\"remark-text\">\r\n                        <el-tooltip :content=\"ro?.order?.remark || '-'\" placement=\"top\" :disabled=\"!ro?.order?.remark || ro?.order?.remark.length <= 50\" effect=\"light\">\r\n                          <div class=\"remark-content\">\r\n                            {{ ro?.order?.remark || '-' }}\r\n                          </div>\r\n                        </el-tooltip>\r\n                        <el-button v-if=\"ro?.order?.orderNo\" v-auth=\"'pms:order:update:update-remark'\" type=\"primary\" text size=\"small\" @click=\"orderRemarkClick()\">\r\n                          {{ t('updateOrderRemark') }}\r\n                        </el-button>\r\n                      </div>\r\n                      <div v-else class=\"remark-edit\">\r\n                        <el-input v-model=\"form.orderRemark\" :placeholder=\"t('orderRemark')\" type=\"textarea\" :rows=\"3\" />\r\n                        <el-button v-auth=\"'pms:order:update:update-remark'\" type=\"primary\" size=\"small\" @click=\"updateOrderRemark()\">\r\n                          {{ t('saveRemark') }}\r\n                        </el-button>\r\n                      </div>\r\n                    </template>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n            <!-- 维修 -->\r\n            <template v-if=\"ro?.room.repairEndTime\">\r\n              <div class=\"info-grid\">\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('startRepair') }}：</span>\r\n                  <span class=\"info-value\">\r\n                    {{ ro?.room.repairStartTime ? dayjs(ro?.room.repairStartTime).format('YYYY-MM-DD HH:mm') : '' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <span class=\"info-label\">{{ t('endRepair') }}：</span>\r\n                  <span class=\"info-value\">\r\n                    {{ ro?.room.repairEndTime ? dayjs(ro?.room.repairEndTime).format('YYYY-MM-DD HH:mm') : '' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"info-item full-width\">\r\n                  <span class=\"info-label\">{{ t('repairReason') }}：</span>\r\n                  <span class=\"info-value\">{{ ro?.room.repairReason }}</span>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </template>\r\n\r\n          <!-- 房间备注区域 -->\r\n          <div class=\"room-remark\">\r\n            <div class=\"remark-content\">\r\n              <template v-if=\"ro?.room.remark !== ''\">\r\n                <div v-if=\"!isEdit\" class=\"remark-text\">\r\n                  {{ limitedText(form.remark, 200) }}\r\n                  <el-button v-auth=\"'pms:account:update:update-remark'\" type=\"primary\" text size=\"small\" @click=\"isEdit = true\">\r\n                    {{ t('updateRoomRemark') }}\r\n                  </el-button>\r\n                </div>\r\n                <div v-else class=\"remark-edit\">\r\n                  <el-input v-model=\"form.remark\" :placeholder=\"t('roomRemark')\" type=\"textarea\" :rows=\"2\" />\r\n                  <el-button v-auth=\"'pms:account:update:update-remark'\" type=\"primary\" size=\"small\" @click=\"updateRoomRemark()\">\r\n                    {{ t('saveRemark') }}\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n              <template v-else>\r\n                <div class=\"remark-edit\">\r\n                  <el-input v-model=\"form.remark\" :placeholder=\"t('roomRemark')\" type=\"textarea\" :rows=\"2\" />\r\n                  <el-button v-auth=\"'pms:account:update:update-remark'\" type=\"primary\" size=\"small\" @click=\"updateRoomRemark()\">\r\n                    {{ t('saveRemark') }}\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.room-handle-container {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\r\n  color: #1f2329;\r\n  background-color: #fff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);\r\n  padding: 0px;\r\n}\r\n\r\n.room-info-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.room-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0px;\r\n  padding-bottom: 4px;\r\n  border-bottom: 1px solid #eaecef;\r\n}\r\n\r\n.room-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.buttons-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 4px;\r\n  margin: 0px 0;\r\n  padding: 0;\r\n}\r\n\r\n.buttons-container :deep(.el-button) {\r\n  margin: 1px;\r\n  border-radius: 4px;\r\n  font-weight: 400;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.buttons-container :deep(.el-button--info.is-plain) {\r\n  border-color: #e8f3ff;\r\n  background-color: #f5faff;\r\n  color: #3370ff;\r\n}\r\n\r\n.buttons-container :deep(.el-button--info.is-plain:hover) {\r\n  background-color: #e8f3ff;\r\n  border-color: #d4e8ff;\r\n}\r\n\r\n.room-content {\r\n  padding: 2px 0;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 6px 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.info-item.full-width {\r\n  grid-column: span 2;\r\n}\r\n\r\n.info-label {\r\n  color: #646a73;\r\n  min-width: 70px;\r\n  font-size: 13px;\r\n  margin-right: 6px;\r\n  flex-shrink: 0;\r\n  text-align: right;\r\n}\r\n\r\n.info-value {\r\n  color: #1f2329;\r\n  font-size: 13px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.info-section {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.room-remark {\r\n  margin-top: 12px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n}\r\n\r\n.remark-text {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  justify-content: space-between;\r\n  padding: 6px 10px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  min-height: 32px;\r\n  gap: 8px;\r\n}\r\n\r\n.remark-content {\r\n  flex: 1;\r\n  word-wrap: break-word;\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n  line-height: 1.4;\r\n  max-height: 80px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-edit {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n  width: 100%;\r\n}\r\n\r\n.remark-edit :deep(.el-textarea) {\r\n  width: 100%;\r\n}\r\n\r\n.remark-edit :deep(.el-textarea__inner) {\r\n  width: 100%;\r\n  min-width: 360px;\r\n  resize: horizontal;\r\n}\r\n\r\n.remark-edit :deep(.el-button) {\r\n  align-self: flex-end;\r\n}\r\n\r\n.custom-loading-container {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 250px;\r\n  background: rgb(255 255 255 / 50%);\r\n}\r\n\r\n.row-spacing {\r\n  height: 24px;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.row-spacing .el-col {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.row-spacing .el-col span:first-child {\r\n  color: #646a73;\r\n  min-width: 70px;\r\n  font-size: 13px;\r\n}\r\n\r\n.row-spacing .el-col span:last-child {\r\n  color: #1f2329;\r\n  font-size: 13px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.loader {\r\n  border: 2px solid #f3f3f3;\r\n  border-radius: 50%;\r\n  border-top: 2px solid #3370ff;\r\n  width: 20px;\r\n  height: 20px;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 房间标题样式 */\r\ndiv[style='font-weight: bold'] {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  padding-bottom: 6px;\r\n  border-bottom: 1px solid #eaecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* 房间信息容器 */\r\ndiv[style='display: flex; flex-flow: column wrap'] {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 2px 0;\r\n}\r\n\r\n/* 备注编辑区域 */\r\n.el-input {\r\n  width: calc(100% - 60px) !important;\r\n}\r\n\r\n:deep(.el-tag--dark) {\r\n  background-color: #3370ff;\r\n  border-color: #3370ff;\r\n}\r\n\r\n:deep(.el-link) {\r\n  font-size: 12px;\r\n}\r\n\r\n:deep(.el-link:hover) {\r\n  color: #2b5cd6;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "_options", "computed", "Object", "assign", "rCode", "noType", "bookInfo", "t", "useI18n", "userStore", "useUserStore", "queryParams", "reactive", "gcode", "hcode", "loading", "ref", "isVisible", "form", "remark", "orderRemark", "ro", "book_Info", "isEdit", "isOrderRemarkEdit", "getRoomOrder", "value", "orderApi", "getRoomInfo", "then", "res", "code", "data", "book", "room", "_a", "order", "updateRoomRemark", "params", "roomApi", "ElMessage", "error", "msg", "async", "onClicked", "who", "val", "no", "_b", "orderNo", "_d", "_c", "xDay", "bookNo", "bookType", "OrderType", "GROUP", "_f", "_e", "_h", "_g", "rNo", "_i", "_j", "lockNo", "_k", "mac", "_l", "lockVersion", "_m", "buildNo", "_n", "floorNo", "_o", "rtCode", "_p", "rtName", "_q", "idType", "IdType", "IDCERT", "bkTicketNum", "list", "hotelParamConfigApi", "getHotelParamConfigFront", "<PERSON><PERSON><PERSON><PERSON>", "warning", "result", "hasHourConfig", "canChangePrice", "_s", "_r", "togetherCode", "_u", "_t", "guest<PERSON><PERSON>", "_w", "_v", "orderSource", "_y", "_x", "hourCode", "_A", "_z", "_C", "_B", "_E", "_D", "_G", "_F", "_I", "_H", "_K", "_J", "_M", "_L", "_O", "_N", "hourRoomTypeApi", "getHourRoomTypeList", "Error", "length", "message", "onMounted", "types", "DICT_TYPE_ROOM_HOUSEKEEPING", "join", "generalConfigApi", "for<PERSON>ach", "item", "getRoomCleanType", "updateOrderRemark", "success"], "mappings": "w1GAoLA,MAAMA,GAAQC,EAERC,GAAQC,EAKRC,GAAoCC,GAAS,IAM1CC,OAAOC,OALC,CACbC,MAAO,GACPC,OAAQ,GACRC,SAAU,CAAA,GAEiBV,OAGzBW,EAAEA,IAAMC,IACRC,GAAYC,IAEZC,GAAcC,EAAiB,CACnCC,MAAOJ,GAAUI,MACjBC,MAAOL,GAAUK,QAUbC,GAAUC,GAAI,GAEdC,GAAYD,IACZE,GAAOF,EAAI,CACfG,OAAQ,GACRC,YAAa,KAETC,GAAKL,IACLM,GAAYN,IACZO,GAASP,GAAI,GACbQ,GAAoBR,GAAI,GAiB9B,SAASS,KACPP,GAAKQ,MAAMP,OAAS,GACpBD,GAAKQ,MAAMN,YAAc,GACzBL,GAAQW,OAAQ,EAChBC,EACGC,YAAY,IACRjB,GACHP,MAAOJ,GAAS0B,MAAMtB,QAEvByB,MAAMC,UACLf,GAAQW,OAAQ,EACC,IAAbI,EAAIC,OACNV,GAAGK,MAAQI,EAAIE,KACLV,GAAAI,MAAQI,EAAIE,KAAKC,KAC3Bf,GAAKQ,MAAMP,OAASW,EAAIE,KAAKE,KAAKf,OAClCD,GAAKQ,MAAMN,aAAc,OAAAe,EAAAL,EAAIE,KAAKI,gBAAOjB,SAAU,GAAA,GAEtD,CAiCL,SAASkB,KACP,MAAMC,EAAS,IACV3B,GACHP,MAAOJ,GAAS0B,MAAMtB,MACtBe,OAAQD,GAAKQ,MAAMP,QAErBoB,EAAQF,iBAAiBC,GAAQT,MAAMC,IACpB,IAAbA,EAAIC,MACNR,GAAOG,OAAQ,EACFD,MAEHe,EAAAC,MAAMX,EAAIY,IAAG,GAE1B,CAOYC,eAAAC,GAAUC,EAAaC,yFAEhC,GAAQ,WAARD,GAA4B,YAARA,EAAmB,CACrC,IAAAE,EACA1C,EA0BJ,OAvBI,OAAA2C,EAAA,OAAGb,EAAAd,GAAAK,YAAO,EAAAS,EAAAC,gBAAOa,UAEdF,EAAA1B,GAAGK,MAAMU,MAAMa,QACX5C,EAAA,SACyB,KAAzB,OAAA6C,EAAA,OAAGC,EAAA9B,GAAAK,gBAAOO,WAAV,EAAAiB,EAAgBE,OAEpBL,EAAA1B,GAAGK,MAAMO,KAAKoB,OAERhD,EADPgB,GAAGK,MAAMO,KAAKqB,WAAaC,EAAUC,MAC9B,OAEA,SAINT,GAAA,OAAAU,EAAA,OAAAC,EAAArC,GAAGK,YAAH,EAAAgC,EAAUtB,YAAV,EAAAqB,EAAiBR,WAAW,OAAAU,EAAA,OAAGC,EAAAvC,GAAAK,YAAO,EAAAkC,EAAA3B,WAAM,EAAA0B,EAAAN,QACxChD,EAAA,cAGXP,GAAM,gBAAiB,CACrB+C,MACAE,KACA1C,UAEF,CAGF,IAAI2B,EAaE,GAwBF,GAvBGA,EAAA,CACL,CACE6B,IAAK,OAAAC,EAAAzC,GAAGK,YAAH,EAAAoC,EAAU5B,KAAK2B,IACpBzD,MAAO,OAAA2D,EAAA1C,GAAGK,YAAH,EAAAqC,EAAU7B,KAAK9B,MACtB4D,OAAQ,OAAAC,EAAA5C,GAAGK,YAAH,EAAAuC,EAAU/B,KAAK8B,OACvBE,IAAK,OAAAC,EAAA9C,GAAGK,YAAH,EAAAyC,EAAUjC,KAAKgC,IACpBE,YAAa,OAAAC,EAAAhD,GAAGK,YAAH,EAAA2C,EAAUnC,KAAKkC,YAC5BE,QAAS,OAAAC,EAAAlD,GAAGK,YAAH,EAAA6C,EAAUrC,KAAKoC,QACxBE,QAAS,OAAAC,EAAApD,GAAGK,YAAH,EAAA+C,EAAUvC,KAAKsC,QACxBE,OAAQ,OAAAC,EAAAtD,GAAGK,YAAH,EAAAiD,EAAUzC,KAAKwC,OACvBE,OAAQ,OAAAC,EAAAxD,GAAGK,YAAH,EAAAmD,EAAU3C,KAAK0C,OACvBE,OAAQC,EAAOC,OACfC,YAAa,EACbC,KAAM,KAUE,WAARrC,GAA4B,SAARA,EAAgB,CAEtC,GAAY,IAARC,EAAW,CAEb,GAAkC,aADhBqC,EAAoBC,yBAAyBzE,KACvDqB,KAAKN,MAAM2D,WACjB,OAAO7C,EAAU8C,QAAQ/E,GAAE,cAC7B,CAGI,MAAAgF,QAAeC,KACjB,GAAQ,SAAR3C,IAAmB0C,EACrB,OAAO/C,EAAU8C,QAAQ/E,GAAE,gBAG7BT,GAAM,gBAAiB,CACrB+C,MACAb,OACAyD,eAAgB,OAAAC,EAAA,OAAAC,EAAAtE,GAAGK,YAAH,EAAAiE,EAAUvD,YAAO,EAAAsD,EAAAD,eACjCG,aAAc,OAAAC,EAAA,OAAAC,EAAAzE,GAAGK,YAAH,EAAAoE,EAAU1D,YAAO,EAAAyD,EAAAD,aAC/BG,UAAW,OAAAC,EAAA,OAAAC,EAAA5E,GAAGK,YAAH,EAAAuE,EAAU7D,YAAO,EAAA4D,EAAAD,UAC5BG,YAAa,OAAAC,EAAA,OAAAC,EAAA/E,GAAGK,YAAH,EAAA0E,EAAUhE,YAAO,EAAA+D,EAAAD,YAC9BG,SAAU,OAAAC,EAAA,OAAAC,EAAAlF,GAAGK,YAAH,EAAA6E,EAAUnE,YAAO,EAAAkE,EAAAD,SAC3BpD,QAAS,OAAAuD,EAAA,OAAAC,EAAApF,GAAGK,YAAH,EAAA+E,EAAUrE,YAAO,EAAAoE,EAAAvD,SAC3B,KACI,CAEL,GAAY,IAARH,EAAW,CAEb,GAAkC,aADhBqC,EAAoBC,yBAAyBzE,KACvDqB,KAAKN,MAAM2D,WACjB,OAAO7C,EAAU8C,QAAQ/E,GAAE,cAC7B,CAGI,MAAAgF,QAAeC,KACjB,GAAQ,SAAR3C,IAAmB0C,EACrB,OAAO/C,EAAU8C,QAAQ/E,GAAE,gBAG7BT,GAAM,gBAAiB,CACrB+C,MACAb,OACAyD,eAAgB,OAAAiB,EAAA,OAAAC,EAAAtF,GAAGK,YAAH,EAAAiF,EAAUvE,YAAO,EAAAsE,EAAAjB,eACjCG,aAAc,OAAAgB,EAAA,OAAAC,EAAAxF,GAAGK,YAAH,EAAAmF,EAAUzE,YAAO,EAAAwE,EAAAhB,aAC/BG,UAAW,OAAAe,EAAA,OAAAC,EAAA1F,GAAGK,YAAH,EAAAqF,EAAU3E,YAAO,EAAA0E,EAAAf,UAC5BG,YAAa,OAAAc,EAAA,OAAAC,EAAA5F,GAAGK,YAAH,EAAAuF,EAAU7E,YAAO,EAAA4E,EAAAd,YAC9BG,SAAU,OAAAa,EAAA,OAAAC,EAAA9F,GAAGK,YAAH,EAAAyF,EAAU/E,YAAO,EAAA8E,EAAAb,SAC3BpD,QAAS,OAAAmE,EAAA,OAAAC,EAAAhG,GAAGK,YAAH,EAAA2F,EAAUjF,YAAO,EAAAgF,EAAAnE,SAC3B,CACH,CAMFN,eAAe6C,WACb,MAAMlD,EAAS,IACV3B,GACH+D,OAAQ,OAAAvC,EAAAd,GAAGK,YAAH,EAAAS,EAAUD,KAAKwC,QAErB,IACF,MAAM5C,QAAawF,EAAgBC,oBAAoBjF,GACnD,GAAa,IAAbR,EAAIC,KACA,MAAA,IAAIyF,MAAM,UAEd,OAAA1F,EAAIE,KAAKyF,OAAS,QAKfhF,GACA,MAAA,CAAEiF,QAAS,SAAS,CAC7B,QAnOFC,GAAU,KACKlG,KAKfkB,iBACQ,MAAAL,EAAS,IAAK3B,GAAaiH,MAAO,CAACC,GAA6BC,KAAK,OACrE9F,KAAEA,SAAe+F,EAAiB7C,KAAK5C,GACxCN,EAAAgG,SAASC,IACK,cAAbA,EAAKlG,OACPd,GAAUS,MAAQuG,EAAKvG,MAAA,GAE1B,CAXgBwG,EAAA,2nZAqCnB,mBACE,KAAK,OAAAlF,EAAA,OAAGb,EAAAd,GAAAK,YAAO,EAAAS,EAAAC,gBAAOa,SACpB,OAGF,MAAMX,EAAS,IACV3B,GACHsC,QAAS5B,GAAGK,MAAMU,MAAMa,QACxB9B,OAAQD,GAAKQ,MAAMN,aAGrBO,EAASwG,kBAAkB7F,GAAQT,MAAMC,IACtB,IAAbA,EAAIC,MACNP,GAAkBE,OAAQ,EACtBL,GAAGK,OAASL,GAAGK,MAAMU,QACvBf,GAAGK,MAAMU,MAAMjB,OAASD,GAAKQ,MAAMN,aAE3BoB,EAAA4F,QAAQ7H,GAAE,iCAEViC,EAAAC,MAAMX,EAAIY,IAAG,GAE1B,oqBA0KH,mBACElB,GAAkBE,OAAQ,EAC1BR,GAAKQ,MAAMN,aAAc,OAAA4B,EAAA,OAAAb,EAAAd,GAAGK,YAAO,EAAAS,EAAAC,gBAAOjB,SAAU,EAAA"}