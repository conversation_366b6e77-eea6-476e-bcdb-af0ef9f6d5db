<i18n>
{
  "en": {
    "pageHeader": {
      "brandManagement": "Channel Mgmt"
    },
    "back":"Back",
    "options":{
      "meituan": "meituan",
      "ctrip": "ctrip",
      "tiktok": "tiktok",
      "xiaohongshu": "xiaoh<PERSON><PERSON>",
      "jd": "jd"
    }
  },
  "zh-cn": {
    "pageHeader": {
      "brandManagement": "渠道管理"
    },
    "back":"返回",
    "options":{
      "meituan": "美团",
      "ctrip": "携程",
      "tiktok": "抖音",
      "xiaohongshu": "小红书",
      "jd": "京东"
    }
  },
  "km": {
    "pageHeader": {
      "brandManagement": "ការគ្រប់គ្រងឆានែល"
    },
    "back":"ត្រឡប់ក្រោយ",
    "options":{
      "meituan": "ម៉េយទួន",
      "ctrip": "ស៊ីធីរីភី",
      "tiktok": "ធីកធីក",
      "xiaohongshu": "សៀវភៅក្រហម",
      "jd": "ហ្វីស៊ី"
    }
  }
}
</i18n>

<script setup lang="ts">
import type channel from './index.ts'
import otaApi from '@/api/modules/pms/serviceintegration/serviceintegration.api'
import useUserStore from '@/store/modules/user'
import { deepCopy } from '@/utils/index'
import { Back, Refresh } from '@element-plus/icons-vue'
import { ElButton, ElMessageBox, ElText, type TabsPaneContext } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'

defineOptions({
  name: 'ChannelInfo', // 渠道管理详情
})
const router = useRouter()
const { query } = useRoute()
/** 获取缓存数据 */
const userStore = useUserStore()
const { t } = useI18n()
/** 房型搜索 */
const selectValue = ref()
/** 渠道选中 */
const tagRadio = ref<string>('')
watch(
  () => query.tags as string,
  (newValue) => {
    tagRadio.value = newValue
  },
  { immediate: true }
)
// 添加对tagRadio的监听
watch(
  () => tagRadio.value,
  async (newValue) => {
    if (newValue) {
      // 当渠道切换时，重新获取对应渠道的房型数据
      await getOtaRoomTypeList()
      // 同时重新获取房型绑定列表
      await getList()
    }
  },
  { immediate: false } // 不需要立即执行，因为onMounted已经会调用
)
/** 订单助手tabs */
const activeName = ref('1')
/** 详情弹窗 */
const easyDialogRef = ref()
/** 获取form表格ref */
const easyFormRefUpdate = ref()
/** ota列表数据 */
const roomOptions = reactive<channel.otaListType[]>([])
/** ota radio列表 */
const otaOptions = reactive<channel.otaOptions[]>([
  {
    value: 'meituan',
    label: t('options.meituan'),
  },
  {
    value: 'ctrip',
    label: t('options.ctrip'),
  },
  {
    value: 'tiktok',
    label: t('options.tiktok'),
  },
  {
    value: 'xiaohongshu',
    label: t('options.xiaohongshu'),
  },
  {
    value: 'jd',
    label: t('options.jd'),
  },
])
/** 是否加载 */
const loading = ref(false)
/** 列表查询条件 */
const queryParams = reactive<channel.queryParams>({
  gcode: userStore.gcode,
  hcode: userStore.hcode,
})
/** 表格配置 */
const options = reactive({
  maxHeight: 495,
})

onMounted(async () => {
  await getOtaRoomTypeList()
  await getList()
})

/** 后台获取到的数据对象(渠道房型绑定) */
const tableData1 = ref<channel.roomTypeRefList[]>([])
const backTableData1 = ref<channel.roomTypeRefList[]>([])
/** 表格配置(渠道房型绑定) */
const tableColumns1 = reactive<Table.Column<channel.roomTypeRefList>[]>([
  {
    prop: 'rtName',
    label: 'PMS房型',
  },
  {
    prop: 'state',
    label: '绑定状态',
    render: ({ row }) =>
      h(
        ElText,
        {
          type: row.state == 1 ? 'success' : 'danger',
        },
        { default: () => stateType(row.state) }
      ),
  },
  {
    prop: 'otaRoomTypes',
    label: '渠道房型',
    render: ({ row }) =>
      h(
        ElText,
        {
          type: row.otaRoomTypeName ? '' : 'info',
          size: 'small',
        },
        { default: () => row.otaRoomTypeName || '未关联房型，点击右侧关联房型' }
      ),
  },
  {
    prop: '',
    label: '操作',
    render: ({ row }) =>
      h('div', {}, [
        h(
          ElButton,
          {
            type: 'primary',
            link: true,
            onClick: () => handleRenderClick(row),
          },
          { default: () => '关联房型' }
        ),
        // 当绑定状态为已绑定时，显示解除绑定按钮
        row.state == 1
          ? h(
              ElButton,
              {
                type: 'danger',
                link: true,
                onClick: () => handleUnbind(row),
              },
              { default: () => '解除绑定' }
            )
          : null,
      ]),
  },
])
/** 后台获取到的数据对象(门店绑定) */
const tableData2 = ref<channel.HotelRefRespVO[]>([])
/** 表格配置(门店绑定) */
const tableColumns2 = reactive<Table.Column<channel.HotelRefRespVO>[]>([
  {
    prop: 'rtName',
    label: 'PMS酒店名称',
  },
  {
    prop: 'otaHname',
    label: `${otaOptions.filter((item) => item.value == tagRadio.value)[0].label}酒店名称`,
  },
])
/** 弹窗内容(初始化) */
const _modelForm = reactive<channel.modelFormType>({
  ...queryParams,
  roomTypeCode: '',
  roomTypeName: '',
  channel: '',
  otaRoomTypes: [],
})
/** 弹窗内容 */
const modelForm = ref<channel.modelFormType>({ ..._modelForm })
/** form表单 */
const ruleFieldList = reactive<Form.FieldItem[]>([
  {
    label: 'PMS房型',
    field: 'roomTypeName',
    readonly: true,
  },
  {
    label: '渠道房型',
    field: 'otaRoomTypes',
    type: 'select',
    rules: [{ required: true, message: '渠道房型不能为空！' }],
    options: {
      data: roomOptions,
      labelKey: 'roomName',
      valueKey: 'roomId',
      selectOptions: {
        multiple: true,
        collapseTags: true,
        maxCollapseTags: 1,
      },
    },
  },
])
/** 绑定状态 */
function stateType(id: number) {
  let txt = ''
  switch (id) {
    case 0:
      txt = '未绑定'
      break
    case 1:
      txt = '已绑定'
      break
  }
  return txt
}
/** 渠道切换 */
async function tagRadioChange() {
  selectValue.value = ''
  if (activeName.value == '1') {
    getList()
  } else if (activeName.value == '2') {
    storeList()
  }
}
/** 重新获取最新 */
function handleClick(tab: TabsPaneContext) {
  if (tab.props.name == 1) {
    getList()
  } else if (tab.props.name == 2) {
    storeList()
  }
}
/** 获取门店绑定数据 */
async function storeList() {
  loading.value = true
  const { data } = await otaApi.getHotelRefList({
    ...queryParams,
    channel: tagRadio.value,
  })
  tableData2.value = data.map((e: channel.HotelRefRespVO) => {
    e.rtName = userStore.hname
    return e
  })
  loading.value = false
}
/** 获取渠道房型绑定 */
async function getList() {
  loading.value = true
  tableData1.value = []
  const { data } = await otaApi.getRoomTypeRefList({
    ...queryParams,
    channel: tagRadio.value,
  })
  // pms数据
  const res = await otaApi.getSimpleList(queryParams)
  loading.value = false
  tableData1.value = res.data.map((itemA: channel.roomTypeRefList) => {
    // 查找rtCode与roomTypeCode是否相等,相等则返回
    const isDef = data.filter((itemB: channel.roomTypeRefList) => itemB.roomTypeCode == itemA.rtCode)[0]
    // 判断是否有值,有则源对象上追加并给绑定上
    if (isDef) {
      itemA = { ...itemA, ...isDef, state: 1 }
    } else {
      itemA.state = 0
    }
    // 拼接成字符串格式
    const arr: string[] = []
    // OTA房型列表有值，则进行循环
    itemA.otaRoomTypes?.forEach((item: any) => {
      arr.push(item.otaRoomTypeName)
      // 把ota房型代码 转数字类型
      item.otaRoomTypeCode = Number(item.otaRoomTypeCode)
    })
    itemA.otaRoomTypeName = arr.join('、')
    return itemA
  })
  roomOptionsForEach()
  backTableData1.value = deepCopy(tableData1.value)
}
/** 提交form */
function formSubmit() {
  if (!easyFormRefUpdate.value.formRef) {
    return
  }
  easyFormRefUpdate.value.formRef.validate(async (valid: boolean) => {
    if (valid) {
      const params = { ...modelForm.value }
      const otaRoomTypes: channel.OtaRoomType[] = []
      // ota房型列表 循环成后台需要的参数值
      modelForm.value.otaRoomTypes.forEach((itemA: any) => {
        const isDef: channel.otaListType = roomOptions.filter((itemB: channel.otaListType) => itemA == itemB.roomId)[0]

        if (tagRadio.value === 'meituan' || tagRadio.value === 'tiktok' || tagRadio.value === 'xiaohongshu') {
          otaRoomTypes.push({
            otaRoomTypeCode: isDef.roomId,
            otaRoomTypeName: isDef.roomName,
          })
        } else if (tagRadio.value === 'ctrip' || tagRadio.value === 'jd') {
          const arrProduct: channel.OtaProduct[] = []
          isDef.roomInfos.forEach((itemC: any) => {
            arrProduct.push({
              productCode: itemC.productCode,
              productName: itemC.productName,
            })
          })
          otaRoomTypes.push({
            otaRoomTypeCode: isDef.roomId,
            otaRoomTypeName: isDef.roomName,
            otaProducts: arrProduct,
          })
        }
      })
      params.otaRoomTypes = otaRoomTypes
      params.channel = tagRadio.value
      await otaApi.putRoomTypeRefAssociation(params)
      formClose()
      getList()
    }
  })
}
/** 取消弹窗 */
function formClose() {
  // 清空校验
  if (easyFormRefUpdate.value) {
    easyFormRefUpdate.value.formRef.resetFields()
  }
  // 赋值给弹窗的值
  for (const key in modelForm.value) {
    modelForm.value[key] = _modelForm[key]
  }
  easyDialogRef.value.loading = false
  easyDialogRef.value.visible = false
}
/** 查找列表中是否有关联房型，关联的房型全部不能选择 */
function roomOptionsForEach() {
  tableData1.value.forEach((itemA: channel.roomTypeRefList) => {
    itemA.otaRoomTypes?.forEach((item: any) => {
      roomOptions.map((itemA) => {
        if (item.otaRoomTypeCode == itemA.roomId) {
          itemA.disabled = true
        }
        return itemA
      })
    })
  })
}

/**
 * 请求第三方的数据
 */
async function getOtaRoomTypeList() {
  const channel = tagRadio.value
  roomOptions.length = 0
  loading.value = true
  // 调用获取OTA房型列表接口
  otaApi
    .getOtaRoomTypeList({
      ...queryParams,
      channel,
    })
    .then((res) => {
      if (res.code === 0 && res.data) {
        // 将返回的数据转换为EasySelect需要的格式
        roomOptions.push(
          ...res.data.map((item: any) => {
            return {
              roomId: item.otaRoomTypeCode,
              roomName: item.otaRoomTypeName,
              disabled: false,
              // 如果是携程，可能需要保存产品信息
              roomInfos: item.otaProducts || [],
            }
          })
        )
      }
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

/** 关联房型（查看） */
function handleRenderClick(row: channel.roomTypeRefList) {
  roomOptionsForEach()
  if (row.otaRoomTypes) {
    const arr: any[] = []
    row.otaRoomTypes.forEach((item) => {
      arr.push(item.otaRoomTypeCode)
    })
    modelForm.value.otaRoomTypes = arr
  }
  /** 循环查找当前行有没有选中的，有则把disabled重置flase */
  roomOptions.map((itemA) => {
    row.otaRoomTypes?.forEach((itemB) => {
      if (itemA.roomId === itemB.otaRoomTypeCode) {
        itemA.disabled = false
      }
    })
    return itemA
  })
  modelForm.value.roomTypeName = row.rtName
  modelForm.value.roomTypeCode = row.rtCode
  easyDialogRef.value.show()
}

/** 解除绑定 */
function handleUnbind(row: channel.roomTypeRefList) {
  ElMessageBox.confirm('解除绑定后，会造成OTA上当前房型订单无法同步，确定要解除该房型的绑定关系吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        loading.value = true
        await otaApi.deleteRoomTypeRef({
          ...queryParams,
          channel: tagRadio.value,
          roomTypeCode: row.rtCode,
        })
        ElMessage.success('解除绑定成功')
        getList() // 刷新列表
      } catch (error) {
        console.error('解除绑定失败:', error)
        ElMessage.error('解除绑定失败')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

/** 房型搜索 */
function handleChange(value: number) {
  if (value) {
    // 过滤选中的房型，并只显示选中已关联的房型
    tableData1.value = backTableData1.value.filter((element) => element.otaRoomTypes?.some((type) => type.otaRoomTypeCode == value))
  } else {
    tableData1.value = backTableData1.value
  }
}
</script>

<template>
  <div class="container">
    <PageHeader>
      <template #title>
        <ElButton :icon="Back" class="mr-[20px]" @click="router.go(-1)">
          {{ t('back') }}
        </ElButton>
        {{ t('pageHeader.brandManagement') }}
      </template>
    </PageHeader>
    <page-main>
      <div class="container-title">
        <el-image class="h-[50px] w-[50px]" src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" fit="cover" />
        <div class="ml-[10px]">
          <div>
            <ElText tag="p" class="title">
              订单蚁来
              <ElText tag="sub" class="!ml-2" size="small"> 订单直连同步助手 </ElText>
            </ElText>
          </div>
          <ElText tag="p"> 您已开通订单蚁来订单同步助手，请完成各个ota平台的酒店绑定和渠道房型绑定 </ElText>
        </div>
      </div>
      <div class="container-tag">
        渠道：
        <el-radio-group v-model="tagRadio" class="container-tag-radio" @change="tagRadioChange">
          <el-radio v-for="(item, index) in otaOptions" :key="index" :value="item.value" border>
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </page-main>
    <page-main>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="渠道房型绑定" name="1">
          <div class="flex-between mb-[15px]">
            <EasySelect v-model="selectValue" class="!w-[230px]" :data="roomOptions" label-key="roomName" value-key="roomId" clearable placeholder="请选择渠道房型" @change="handleChange" />
            <ElButton :icon="Refresh" @click="getList()"> 获取最新房型 </ElButton>
          </div>
          <EasyTable v-loading="loading" :columns="tableColumns1" :table-data="tableData1" :options="options" @refresh="getList()" />
        </el-tab-pane>
        <el-tab-pane label="门店绑定" name="2">
          <EasyTable v-loading="loading" :columns="tableColumns2" :table-data="tableData2" />
        </el-tab-pane>
      </el-tabs>
    </page-main>
    <EasyDialog ref="easyDialogRef" title="关联本地房型" is-body show-cancel-button show-confirm-button dialog-width="400" :options="{ class: 'channel-dialog' }" @submit="formSubmit()" @close="formClose()">
      <easyForm
        ref="easyFormRefUpdate"
        class="invoiced-form"
        :field-list="ruleFieldList"
        :model="modelForm"
        :options="{
          labelSuffix: '：',
        }"
      />
    </EasyDialog>
  </div>
</template>

<style lang="scss" scoped>
.container {
  width: 100%;
  max-width: 100%;
  :deep(.el-card__header) {
    border-bottom: 0;
  }
  .container-title {
    margin-bottom: 20px;
    font-size: 18px;
    @include flex-center();
    justify-content: flex-start;
    .title {
      color: var(--el-popup-modal-bg-color);
    }
  }
  .container-tag {
    .container-tag-radio {
      :deep(.el-radio.is-bordered) {
        border-color: transparent;
        @apply mr-[0];
        .el-radio__input {
          display: none;
        }
        .el-radio__label {
          @apply pr-[0px];
        }
      }

      :deep(.el-radio.is-bordered.is-checked) {
        border-color: var(--el-color-primary);
      }
    }
  }
}
</style>
