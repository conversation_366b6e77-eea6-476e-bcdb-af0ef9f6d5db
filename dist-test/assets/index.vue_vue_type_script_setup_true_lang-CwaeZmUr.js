import{d as e,y as a,Q as s,b as t,o,e as n,f as r,w as l,u as i,c,R as d,S as u,T as m,U as g,V as y,_ as p}from"./index-CkEhI1Zk.js";const v=e({name:"BackTop",__name:"index",setup(e){const v={enterActiveClass:"ease-out duration-300",enterFromClass:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95",enterToClass:"opacity-100 translate-y-0 lg-scale-100",leaveActiveClass:"ease-in duration-200",leaveFromClass:"opacity-100 translate-y-0 lg-scale-100",leaveToClass:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95"};a((()=>{window.addEventListener("scroll",k),k()})),s((()=>{window.removeEventListener("scroll",k)}));const f=t(null);function k(){f.value=document.documentElement.scrollTop}function b(){document.documentElement.scrollTo({top:0,behavior:"smooth"})}return(e,a)=>{const s=p;return o(),n(y,{to:"body"},[r(g,u(m(v)),{default:l((()=>[i(f)&&i(f)>=200?(o(),c("div",{key:0,class:"fixed bottom-4 right-4 z-1000 h-12 w-12 flex cursor-pointer items-center justify-center rounded-full bg-white shadow-lg ring-1 ring-stone-3 ring-inset dark-bg-dark hover-bg-stone-1 dark-ring-stone-7 dark-hover-bg-dark/50",onClick:b},[r(s,{name:"i-icon-park-outline:to-top-one",size:24})])):d("",!0)])),_:1},16)])}}});export{v as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-CwaeZmUr.js.map
