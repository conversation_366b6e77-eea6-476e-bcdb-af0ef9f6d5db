{"version": 3, "file": "roomLogList-BHEIqHo3.js", "sources": ["../../src/views/room/realtime/components/roomLogList.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"roomLogList\": {\r\n      \"dialogTitle\": \"Log: {rNo}\",\r\n      \"operationTime\": \"Operation Time\",\r\n      \"startTime\": \"Start Time\",\r\n      \"endTime\": \"End Time\",\r\n      \"filter\": \"Filter\",\r\n      \"operationContent\": \"Operation Content\",\r\n      \"operationType\": \"Operation Type\",\r\n      \"operatorAndTime\": \"Operator/Operation Time\"\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"roomLogList\": {\r\n      \"dialogTitle\": \"日志：{rNo}\",\r\n      \"operationTime\": \"操作时间\",\r\n      \"startTime\": \"开始时间\",\r\n      \"endTime\": \"结束时间\",\r\n      \"filter\": \"筛选\",\r\n      \"operationContent\": \"操作内容\",\r\n      \"operationType\": \"操作类型\",\r\n      \"operatorAndTime\": \"操作人/操作时间\"\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"roomLogList\": {\r\n      \"dialogTitle\": \"កំណត់ហេតុ៖ {rNo}\",\r\n      \"operationTime\": \"ពេលវេលាប្រតិបត្តិការ\",\r\n      \"startTime\": \"ពេលវេលាចាប់ផ្តើម\",\r\n      \"endTime\": \"ពេលវេលាបញ្ចប់\",\r\n      \"filter\": \"តម្រង\",\r\n      \"operationContent\": \"មាតិកាប្រតិបត្តិការ\",\r\n      \"operationType\": \"ប្រភេទប្រតិបត្តិការ\",\r\n      \"operatorAndTime\": \"អ្នកប្រតិបត្តិ/ពេលវេលាប្រតិបត្តិការ\"\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { RoomLogModel } from '@/models/index'\r\nimport { roomLogApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\ndefineOptions({\r\n  name: 'RoomLogList',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rCode: string\r\n    rNo: string\r\n  }>(),\r\n  {\r\n    rCode: '',\r\n    rNo: '',\r\n    modelValue: false,\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n\r\n  // 搜索\r\n  search: {\r\n    startTime: dayjs().add(-1, 'day').format('YYYY-MM-DD HH:mm:ss'),\r\n    endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),\r\n  },\r\n\r\n  // 列表数据\r\n  dataList: [] as RoomLogModel[],\r\n})\r\n\r\nonMounted(() => {\r\n  getDataList()\r\n})\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    rCode: props.rCode,\r\n    createTime: [dayjs(data.value.search.startTime).format('YYYY-MM-DD HH:mm:ss'), dayjs(data.value.search.endTime).format('YYYY-MM-DD HH:mm:ss')].join(','),\r\n  }\r\n  roomLogApi.page(params).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data.list\r\n    pagination.value.total = res.data.total\r\n  })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('roomLogList.dialogTitle', { rNo: props.rNo })\" width=\"900px\" style=\"height: 600px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <el-form :model=\"data.search\" size=\"default\" label-width=\"120px\" inline-message inline class=\"search-form\">\r\n      <el-form-item :label=\"t('roomLogList.operationTime')\">\r\n        <el-date-picker v-model=\"data.search.startTime\" type=\"date\" :clearable=\"false\" :placeholder=\"t('roomLogList.startTime')\" style=\"width: 150px\" />-\r\n        <el-date-picker v-model=\"data.search.endTime\" type=\"date\" :clearable=\"false\" :placeholder=\"t('roomLogList.endTime')\" style=\"width: 150px\" />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"currentChange()\">\r\n          <template #icon>\r\n            <svg-icon name=\"ep:search\" />\r\n          </template>\r\n          {{ t('roomLogList.filter') }}\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\" @sort-change=\"sortChange\">\r\n      <el-table-column prop=\"content\" :label=\"t('roomLogList.operationContent')\" />\r\n      <el-table-column prop=\"handle\" :label=\"t('roomLogList.operationType')\" />\r\n      <el-table-column :label=\"t('roomLogList.operatorAndTime')\" align=\"center\">\r\n        <template #default=\"{ row }\">\r\n          <span>{{ row.creator }} / {{ row.createTime }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-pagination\r\n      v-if=\"pagination.total > 10\"\r\n      :current-page=\"pagination.pageNo\"\r\n      :total=\"pagination.total\"\r\n      :page-size=\"pagination.pageSize\"\r\n      :page-sizes=\"pagination.sizes\"\r\n      :layout=\"pagination.layout\"\r\n      :hide-on-single-page=\"false\"\r\n      class=\"pagination\"\r\n      background\r\n      @size-change=\"sizeChange\"\r\n      @current-change=\"currentChange\"\r\n    />\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "myVisible", "computed", "get", "modelValue", "set", "val", "data", "ref", "loading", "tableAutoHeight", "search", "startTime", "dayjs", "add", "format", "endTime", "dataList", "getDataList", "value", "params", "gcode", "hcode", "rCode", "createTime", "join", "roomLogApi", "page", "then", "res", "list", "total", "sizeChange", "size", "currentChange", "sortChange", "prop", "order", "onMounted"], "mappings": "ulCAmDA,MAAMA,EAAQC,EAaRC,EAAQC,GAKRC,EAAEA,GAAMC,IAERC,EAAYC,KACZC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IAEzEC,EAAYC,EAAS,CACzBC,IAAM,IACGhB,EAAMiB,WAEf,GAAAC,CAAIC,GACFjB,EAAM,oBAAqBiB,EAAG,IAI5BC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAGjBC,OAAQ,CACNC,UAAWC,IAAQC,OAAQ,OAAOC,OAAO,uBACzCC,QAASH,IAAQE,OAAO,wBAI1BE,SAAU,KAOZ,SAASC,IACPX,EAAKY,MAAMV,SAAU,EACrB,MAAMW,EAAS,IACVxB,IACHyB,MAAO5B,EAAU4B,MACjBC,MAAO7B,EAAU6B,MACjBC,MAAOpC,EAAMoC,MACbC,WAAY,CAACX,EAAMN,EAAKY,MAAMR,OAAOC,WAAWG,OAAO,uBAAwBF,EAAMN,EAAKY,MAAMR,OAAOK,SAASD,OAAO,wBAAwBU,KAAK,MAEtJC,EAAWC,KAAKP,GAAQQ,MAAMC,IAC5BtB,EAAKY,MAAMV,SAAU,EAChBF,EAAAY,MAAMF,SAAWY,EAAItB,KAAKuB,KACpBnC,EAAAwB,MAAMY,MAAQF,EAAItB,KAAKwB,KAAA,GACnC,CAIH,SAASC,EAAWC,GAClBpC,EAAaoC,GAAML,MAAK,IAAMV,KAAa,CAIpC,SAAAgB,EAAcP,EAAO,GAC5B7B,EAAgB6B,GAAMC,MAAK,IAAMV,KAAa,CAIhD,SAASiB,GAAWC,KAAEA,EAAMC,MAAAA,IAC1BtC,EAAaqC,EAAMC,GAAOT,MAAK,IAAMV,KAAa,QAhCpDoB,GAAU,KACIpB,GAAA"}