<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <printMergeForm78 Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>printMergeForm78</Alias>
        <HeadersString />
        <Key />
        <Name>printMergeForm78</Name>
        <PathData>D:\workspace\qz\frontend\aflower-pms-front\public\reports\print\printMergeForm78.json</PathData>
      </printMergeForm78>
    </Databases>
    <DataSources isList="true" count="2">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="14">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>hname,System.String</value>
          <value>orderNo,System.String</value>
          <value>printDate,System.String</value>
          <value>teamName,System.String</value>
          <value>contact,System.String</value>
          <value>phone,System.String</value>
          <value>checkinTime,System.String</value>
          <value>checkoutTime,System.String</value>
          <value>remark,System.String</value>
          <value>frontPhone,System.String</value>
          <value>address,System.String</value>
          <value>roomList,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>a8e9548306954367b6e81bd4da5a980e</Key>
        <Name>root</Name>
        <NameInSource>printMergeForm78.root</NameInSource>
      </root>
      <root_roomList Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_roomList</Alias>
        <Columns isList="true" count="8">
          <value>orderNo,System.String</value>
          <value>buildingName,System.String</value>
          <value>floorName,System.String</value>
          <value>rtName,System.String</value>
          <value>name,System.String</value>
          <value>sex,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>b5d9867b43424730b38114b7791fa82a</Key>
        <Name>root_roomList</Name>
        <NameInSource>printMergeForm78.root_roomList</NameInSource>
      </root_roomList>
    </DataSources>
    <Relations isList="true" count="1">
      <root Ref="5" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_roomList</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>roomList</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>834eed8e9f524e06b2d8fd320b7d5c16</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="6" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand1 Ref="7" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,2.8</ClientRectangle>
          <Components isList="true" count="4">
            <Text1 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,11</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>团队入住登记单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,1.6,9.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,10</Font>
              <Name>Text3</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>NO:{root.orderNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,2.2,9.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,10</Font>
              <Name>Text4</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>打印时间:{Time}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,16,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text5</Name>
              <Page isRef="6" />
              <Parent isRef="7" />
              <Text>{root.hname}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="12" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4,19,4.2</ClientRectangle>
          <Components isList="true" count="16">
            <Text6 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,0,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>团队名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text2 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>{root.teamName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text9 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,0.6,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>联系人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0.6,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>{root.contact}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,1.2,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>联系电话</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text15 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,1.8,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>入住时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,1.2,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>{root.phone}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text26 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,1.8,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>{root.checkinTime.Substring(0, 16)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,2.4,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,3,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>订单备注</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,3.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text29</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,2.4,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>{root.checkoutTime.Substring(0, 16)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text7 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,3,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>{root.remark}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,3.6,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text8</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text12 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.6,3.6,5.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text12</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,3.6,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text13</Name>
              <Page isRef="6" />
              <Parent isRef="12" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </HeaderBand1>
        <DataBand1 Ref="29" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,9,19,0.6</ClientRectangle>
          <Components isList="true" count="4">
            <Text17 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>5,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="6" />
              <Parent isRef="29" />
              <Text>{Line}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>5,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="6" />
              <Parent isRef="29" />
              <Text>{root_roomList.rNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.6,0,5.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>5,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="6" />
              <Parent isRef="29" />
              <Text>{root_roomList.rtName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,8,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>5,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="6" />
              <Parent isRef="29" />
              <Text>{root_roomList.name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_roomList</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <FooterBand1 Ref="34" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,10.4,19,2.2</ClientRectangle>
          <Components isList="true" count="4">
            <Text20 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,0,18.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text20</Name>
              <Page isRef="6" />
              <Parent isRef="34" />
              <Text>宾客签名：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text23 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.2,0.6,18.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text23</Name>
              <Page isRef="6" />
              <Parent isRef="34" />
              <Text>酒店电话：{root.frontPhone}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text20 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.2,1.2,1.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text20</Name>
              <Page isRef="6" />
              <Parent isRef="34" />
              <Text>酒店地址：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text20>
            <Text24 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>1.8,1.2,17,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="6" />
              <Parent isRef="34" />
              <Text>{root.address}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
            </Text24>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </FooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>e766cee5e10d4d27ad06b002e8851339</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>15</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>12/24/2024 6:52:25 PM</ReportChanged>
  <ReportCreated>10/31/2024 5:41:35 PM</ReportCreated>
  <ReportFile>D:\workspace\qz\frontend\aflower-pms-front\public\reports\print\printMergeFormA4-1-2.mrt</ReportFile>
  <ReportGuid>6fb38f7e8ba542b6a07eb92944a41d52</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2025.1.1.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>