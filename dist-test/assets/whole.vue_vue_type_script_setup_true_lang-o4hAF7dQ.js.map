{"version": 3, "file": "whole.vue_vue_type_script_setup_true_lang-o4hAF7dQ.js", "sources": ["../../src/views/center/rule-cofig/components/whole.vue"], "sourcesContent": ["<script lang=\"ts\" setup>\r\n// do not use same name with ref\r\nconst form = reactive({\r\n  name: '',\r\n  region: '',\r\n  date1: '',\r\n  date2: '',\r\n  delivery: false,\r\n  type: [],\r\n  resource: '',\r\n  desc: '',\r\n})\r\nconst num = ref(1)\r\nfunction onSubmit() {\r\n  console.log('submit!')\r\n}\r\nconst dataList = ref([\r\n  {\r\n    Id: 1613655716970497,\r\n    DebitTypeValue: 'D1000',\r\n    VatRate: '30%',\r\n  },\r\n  {\r\n    Id: 1613655716970498,\r\n    DebitTypeValue: 'D1001',\r\n    VatRate: '40%',\r\n  },\r\n])\r\n</script>\r\n\r\n<template>\r\n  <el-form :model=\"form\" label-width=\"auto\">\r\n    <el-form-item label=\"会员信息共享范围\">\r\n      <el-radio-group v-model=\"form.resource\">\r\n        <div>\r\n          <div>\r\n            <el-radio value=\"1不共享\" />\r\n          </div>\r\n          <div>\r\n            <el-radio value=\"2共享给所有门店\" />\r\n          </div>\r\n          <div style=\"display: flex\">\r\n            <el-radio value=\"3共享给所有组织门店\" />\r\n            <el-select\r\n              v-model=\"form.region\"\r\n              style=\"width: 150px\"\r\n              placeholder=\"选择\"\r\n            >\r\n              <el-option label=\"组织1\" value=\"shanghai\" />\r\n              <el-option label=\"组织2\" value=\"beijing\" />\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item label=\"会员余额积分共享范围\">\r\n      <el-radio-group v-model=\"form.resource\">\r\n        <div>\r\n          <div>\r\n            <el-radio value=\"1不共享\" />\r\n          </div>\r\n          <div>\r\n            <el-radio value=\"2共享给所有门店\" />\r\n            <el-checkbox-group v-model=\"form.type\">\r\n              <el-checkbox label=\"直营\" name=\"type\" />\r\n              <el-checkbox label=\"加盟\" name=\"type\" />\r\n              <el-checkbox label=\"托管\" name=\"type\" />\r\n            </el-checkbox-group>\r\n          </div>\r\n          <div style=\"display: flex\">\r\n            <el-radio value=\"3共享给所有组织门店\" />\r\n            <el-select\r\n              v-model=\"form.region\"\r\n              style=\"width: 150px\"\r\n              placeholder=\"选择\"\r\n            >\r\n              <el-option label=\"组织1\" value=\"shanghai\" />\r\n              <el-option label=\"组织2\" value=\"beijing\" />\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item label=\"会员储值金额入账\">\r\n      <el-radio-group v-model=\"form.resource\">\r\n        <div>\r\n          <div>\r\n            <el-radio value=\"入账到门店\" />\r\n          </div>\r\n          <div>\r\n            <el-radio value=\"入账到集团\" />\r\n          </div>\r\n          <!--          <div> -->\r\n          <!--            仅支持线上移动支付(微信和支付宝)，入账到集团商户号；集团与门店进行结算 -->\r\n          <!--          </div> -->\r\n          <!--          <div> -->\r\n          <!--            集团入账商户号设置路径：集团管理-中央管理-全局配置 -->\r\n          <!--          </div> -->\r\n        </div>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item label=\"会员储值提成和报表\" />\r\n    <el-form-item label=\"提成模式\">\r\n      <div>\r\n        <div>\r\n          <el-radio-group v-model=\"form.resource\">\r\n            <el-radio label=\"按储值金额比例提成\" />\r\n            <el-radio label=\"按储值金额范围提成\" />\r\n          </el-radio-group>\r\n        </div>\r\n        <div>报表：门店-会员储值明细报表 <span>报表号 mdhy00000012</span></div>\r\n      </div>\r\n    </el-form-item>\r\n    <div style=\"width: 600px; margin-left: 150px; text-align: right\">\r\n      <el-form-item>\r\n        <el-button type=\"danger\" @click=\"onSubmit\"> 新增 </el-button>\r\n      </el-form-item>\r\n    </div>\r\n    <el-table\r\n      :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\"\r\n      :data=\"dataList\"\r\n      height=\"100%\"\r\n      style=\"width: 800px; margin-left: 150px\"\r\n    >\r\n      <el-table-column label=\"储值规则\" align=\"left\" width=\"350\">\r\n        <template #default=\"scope\">\r\n          <!--          <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.name\" size=\"small\"/> -->\r\n          <!--          <span v-else>{{ scope.row.DebitTypeValue }}</span> -->\r\n          <div\r\n            style=\"\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: space-between;\r\n            \"\r\n          >\r\n            <el-input-number\r\n              v-model=\"num\"\r\n              :min=\"1\"\r\n              :max=\"10\"\r\n              :controls=\"false\"\r\n              placeholder=\"请输入金额\"\r\n            />\r\n            至\r\n            <el-input-number\r\n              v-model=\"num\"\r\n              :min=\"1\"\r\n              :max=\"10\"\r\n              :controls=\"false\"\r\n              placeholder=\"请输入金额\"\r\n            />\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"储值提成\" align=\"left\">\r\n        <template #default=\"scope\">\r\n          <!--          <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.remark\" size=\"small\"/> -->\r\n          <!--          <span v-else>{{ scope.row.VatRate }}</span> -->\r\n          <div>\r\n            提成\r\n            <el-input-number\r\n              v-model=\"num\"\r\n              :min=\"1\"\r\n              :max=\"10\"\r\n              :controls=\"false\"\r\n              placeholder=\"请输入金额\"\r\n            />%\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"left\" fixed=\"right\">\r\n        <template #default=\"scope\">\r\n          <template v-if=\"scope.row.isEdit\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              size=\"small\"\r\n              @click=\"onEdit(scope.row)\"\r\n            >\r\n              修改\r\n            </el-button>\r\n            <el-button plain size=\"small\" @click=\"scope.row.isEdit = false\">\r\n              取消\r\n            </el-button>\r\n          </template>\r\n          <template v-else>\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              size=\"small\"\r\n              @click=\"scope.row.isEdit = true\"\r\n            >\r\n              编辑\r\n            </el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              size=\"small\"\r\n              @click=\"scope.row.isEdit = true\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </template>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-form-item label=\"会员办卡提成规则\" />\r\n    <el-form-item label=\"提成模式\">\r\n      <div>\r\n        <div>\r\n          <el-radio-group v-model=\"form.resource\">\r\n            <el-radio label=\"按储值金额比例提成\" />\r\n            <el-radio label=\"按储值金额范围提成\" />\r\n          </el-radio-group>\r\n        </div>\r\n        <div>报表：门店-会员储值明细报表 <span>报表号 mdhy00000012</span></div>\r\n      </div>\r\n    </el-form-item>\r\n    <div style=\"width: 600px; margin-left: 150px; text-align: right\">\r\n      <el-form-item>\r\n        <el-button type=\"danger\" @click=\"onSubmit\"> 新增 </el-button>\r\n      </el-form-item>\r\n    </div>\r\n    <el-table\r\n      :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\"\r\n      :data=\"dataList\"\r\n      height=\"100%\"\r\n      style=\"width: 800px; margin-left: 150px\"\r\n    >\r\n      <el-table-column label=\"会员等级\" align=\"left\" width=\"350\">\r\n        <template #default=\"scope\">\r\n          <!--          <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.name\" size=\"small\"/> -->\r\n          <!--          <span v-else>{{ scope.row.DebitTypeValue }}</span> -->\r\n          <el-select v-model=\"form.region\" placeholder=\"选择\">\r\n            <el-option label=\"金卡\" value=\"shanghai\" />\r\n            <el-option label=\"钻石卡\" value=\"beijing\" />\r\n            <el-option label=\"黑金卡\" value=\"shanghai\" />\r\n          </el-select>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"提成金额\" align=\"left\">\r\n        <template #default=\"scope\">\r\n          <!--          <el-input v-if=\"scope.row.isEdit\" v-model=\"scope.row.remark\" size=\"small\"/> -->\r\n          <!--          <span v-else>{{ scope.row.VatRate }}</span> -->\r\n          <div>\r\n            提成\r\n            <el-input-number\r\n              v-model=\"num\"\r\n              :min=\"1\"\r\n              :max=\"10\"\r\n              :controls=\"false\"\r\n              placeholder=\"请输入金额\"\r\n            />%\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"left\" fixed=\"right\">\r\n        <template #default=\"scope\">\r\n          <template v-if=\"scope.row.isEdit\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              size=\"small\"\r\n              @click=\"onEdit(scope.row)\"\r\n            >\r\n              修改\r\n            </el-button>\r\n            <el-button plain size=\"small\" @click=\"scope.row.isEdit = false\">\r\n              取消\r\n            </el-button>\r\n          </template>\r\n          <template v-else>\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              size=\"small\"\r\n              @click=\"scope.row.isEdit = true\"\r\n            >\r\n              编辑\r\n            </el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              size=\"small\"\r\n              @click=\"scope.row.isEdit = true\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </template>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n  </el-form>\r\n</template>\r\n"], "names": ["form", "reactive", "name", "region", "date1", "date2", "delivery", "type", "resource", "desc", "num", "ref", "onSubmit", "console", "log", "dataList", "Id", "DebitTypeValue", "VatRate"], "mappings": "6/BAEA,MAAMA,EAAOC,EAAS,CACpBC,KAAM,GACNC,OAAQ,GACRC,MAAO,GACPC,MAAO,GACPC,UAAU,EACVC,KAAM,GACNC,SAAU,GACVC,KAAM,KAEFC,EAAMC,EAAI,GAChB,SAASC,IACPC,QAAQC,IAAI,UAAS,CAEvB,MAAMC,EAAWJ,EAAI,CACnB,CACEK,GAAI,gBACJC,eAAgB,QAChBC,QAAS,OAEX,CACEF,GAAI,gBACJC,eAAgB,QAChBC,QAAS"}