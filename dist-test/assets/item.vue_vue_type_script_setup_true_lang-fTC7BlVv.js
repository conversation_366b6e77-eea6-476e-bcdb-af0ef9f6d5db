import{d as t,a0 as a,o as e,c as s,g as o,Y as r,ab as p,a7 as c}from"./index-CkEhI1Zk.js";const i={class:"breadcrumb-item flex items-center text-dark dark-text-white"},n={class:"separator mx-2"},l=t({__name:"item",props:{to:{},replace:{type:Boolean},separator:{default:"/"}},setup(t){const l=t,m=a();function d(){l.to&&(l.replace?m.replace(l.to):m.push(l.to))}return(t,a)=>(e(),s("div",i,[o("span",n,r(t.separator),1),o("span",{class:c(["text flex items-center opacity-60",{"is-link cursor-pointer transition-opacity hover-opacity-100":!!l.to}]),onClick:d},[p(t.$slots,"default")],2)]))}});export{l as _};
//# sourceMappingURL=item.vue_vue_type_script_setup_true_lang-fTC7BlVv.js.map
