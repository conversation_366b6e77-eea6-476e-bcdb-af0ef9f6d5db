import{d as t,ai as e,aj as o,b as i,y as a,b3 as s,o as r,c as l,f as n,w as c,u as p,g as m,F as d,ag as u,e as b,b6 as h,h as j,Y as g,aq as y,av as f,R as k,i as _,a7 as v,j as C,k as T,m as w,q as x,aS as N,b5 as S,x as L,t as M,bz as O,v as P,bv as V,bt as R,aT as D}from"./index-CkEhI1Zk.js";/* empty css                   */import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-DK1kOKr4.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";import{_ as G}from"./index.vue_vue_type_script_setup_true_lang-DvSmdj52.js";/* empty css                *//* empty css                  *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";import{c as z}from"./channel.api-CM6FWEgD.js";import{o as I}from"./order.api-B-JCVvq6.js";import{r as U}from"./rt.api-5a8-At7-.js";import{d as H}from"./dictData.api-DUabpYqy.js";import{B as W,r as q,q as A,N as B,O as F}from"./constants-Cg3j_uH4.js";import Y from"./order-ChSzi_-7.js";import K from"./index-M2JMYKA8.js";import{u as $}from"./usePagination-DYjsSSf4.js";import{_ as Q}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                   *//* empty css                         */import"./index-i2MX-1er.js";/* empty css                    */import"./account-Dw8d3GK0.js";import"./index-3RMLzyhA.js";import"./index-ADu0XAHG.js";/* empty css                        *//* empty css                          *//* empty css                 *//* empty css                       *//* empty css                          *//* empty css                       *//* empty css                        *//* empty css                *//* empty css               *//* empty css               *//* empty css                        *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-CJlU19fC.js";import"./account.api-CSMEUacF.js";import"./rentGoods.api-IR0dWMfk.js";import"./orderBill-CgM-5HXN.js";/* empty css                 */import"./print.api-DEqPqKcI.js";import"./printForm.api-BOg76CjP.js";import"./posBillForm-Cigy56-i.js";import"./togetherBill-ByLEtbff.js";import"./index-BqUz2moK.js";import"./generalConfig.api-CEBBd8kx.js";import"./index-Dlhx8lGo.js";import"./index-K7z-WsFs.js";/* empty css                */import"./couponConfig.api-DtISSIXY.js";import"./member.api-2tU9HGvl.js";import"./arSet.api-BZHDDSla.js";import"./hotelParamConfig.api-CbdvhUfn.js";import"./auth.api-C96jzWEY.js";import"./CouponDialog-DTl141Zt.js";import"./coupon.api-aMKZ7FC4.js";/* empty css                       */import"./index-B36WBY8p.js";import"./index-C6K_fo9Y.js";import"./index-_28SpMym.js";/* empty css                   */import"./index-DnGZTrHX.js";import"./list-cSBPeYXE.js";/* empty css                      */import"./index-Cjr3dIX4.js";import"./detail-Dh370UMq.js";import"./rentCompensation-DGZqTwko.js";/* empty css                   */import"./rentCreate-DgokBdtt.js";import"./rent.api-DzgTHAr9.js";import"./rentEdit-kpM-6Ev1.js";import"./rentReturn-Bn7G8O-o.js";import"./goodsModal-DNVgoATn.js";import"./index-DAulSAJI.js";import"./index-D8c6PuWt.js";/* empty css                */import"./index-CDbn0nBx.js";import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";import"./decimal-gPLAeiS8.js";import"./grantModal-S8hNNB6E.js";import"./invoicedModal-XEk1BZXP.js";import"./remark-D99iiFr3.js";import"./splitAccount-DpqmmImE.js";import"./consume-DgDuQkgE.js";import"./indemnityGoods.api-BzuE6zcC.js";import"./retailGoods.api-CPINo1es.js";import"./payment-vLdXRLoR.js";import"./checkinForm-DTcWmPmJ.js";import"./posCheckInForm-BJfHKK6-.js";import"./preAuth-CG1cg58P.js";import"./bookingDetail-BY3bduLn.js";import"./book.api-ERXvEXQF.js";import"./serverTime.api-D89oCqKL.js";import"./timeutils-Ib6GkGcq.js";import"./cancelPopUP-BbPXaQdi.js";import"./arrangeRooms-CPfs5GXR.js";import"./index-CkWKDwTG.js";import"./index-Eu7Cs0xe.js";import"./checkModal-tyH9Ceqi.js";/* empty css                        */import"./DictTypeEnum-DKIIlHnN.js";import"./CardReader-BeR26SIt.js";import"./myStringUtil-D79vpWmP.js";import"./sensitive-la8iBfdn.js";import"./log-BF-F0S6C.js";import"./roomLog.api-D1y-EVTQ.js";import"./user.api-BYl7ypOS.js";import"./orderDetail-B1c5B-Jp.js";import"./customer.api-dB3m63zZ.js";import"./device.api-BsgckoMw.js";import"./roomCardLog.api-pw0J1hl7.js";import"./roomCardUtil-DBQw7z7m.js";import"./index-CYIY_xy7.js";import"./utils-S8-xpbSs.js";import"./index-DcUuNG9v.js";import"./route-block-B_A1xBdJ.js";import"./roomContinue-Cwa93jZh.js";/* empty css                       */import"./roomExchange-DyqICf4D.js";import"./arrangeRooms-DLQ6Ij2m.js";import"./roomCardLogList-DIxcpNbT.js";import"./orderList-DtQU5x9j.js";import"./mergeForm-C0XQeWLX.js";import"./teamBookDetail-CaYBewxN.js";import"./arrangeRts-C83hWsgy.js";import"./GeneralConfigTypeEnum-DERqowgl.js";import"./teamMainOrder-RmJelicD.js";import"./teamReception-BVmeD-Jb.js";const X={class:"filter-row"},J={class:"filter-row"},Z={class:"switch-container"},tt={class:"expand-content"},et={key:1,class:"no-data"},ot={class:"guest-info"},it={key:0},at={class:"order-info"},st={class:"label"},rt={key:0},lt={class:"label"},nt={class:"room-info"},ct={class:"room-type"},pt={class:"price"},mt=t({name:"OrderInHandPlanCheckoutList",__name:"todayCheckoutList",setup(t){const Q=e(),{pagination:mt,getParams:dt,onSizeChange:ut,onCurrentChange:bt,onSortChange:ht}=$(),{t:jt}=o(),gt=i({loading:!1,tableAutoHeight:!1,formMode:"dialog",formModeProps:{visible:!1,no:"",orderTogetherCode:""},search:{channelCode:"-1",rtCode:"",guestSrcType:"",checkinType:"",searchType:"0",searchContent:"",displayWay:"0"},dataList:[]});a((()=>{!function(){const t={gcode:Q.gcode,hcode:Q.hcode,isEnable:W.YES};z.getChannelSimpleList(t).then((t=>{0===t.code&&(yt.value=t.data)}))}(),function(){const t={gcode:Q.gcode,hcode:Q.hcode,isVirtual:W.NO,isEnable:W.YES};U.getRoomTypeSimpleList(t).then((t=>{0===t.code&&(ft.value=t.data)}))}(),H.getDictDataBatch(kt).then((t=>{_t.value=t.data.filter((t=>t.dictType===q)),vt.value=t.data.filter((t=>t.dictType===A))})),Ct()}));const yt=i([]);const ft=i([]);const kt=[q,A],_t=i([]),vt=i([]);function Ct(){gt.value.loading=!0;const t={...dt(),gcode:Q.gcode,hcode:Q.hcode,keyWords:gt.value.search.searchContent,channelCode:"-1"===gt.value.search.channelCode?"":gt.value.search.channelCode,guestSrcType:gt.value.search.guestSrcType,rtCode:gt.value.search.rtCode,checkinType:gt.value.search.checkinType,displayWay:gt.value.search.displayWay};I.todayCheckOutList(t).then((t=>{gt.value.loading=!1,t.data.list&&(gt.value.dataList=t.data.list,mt.value.total=t.data.total)}))}function Tt(t){ut(t).then((()=>Ct()))}function wt(t=1){bt(t).then((()=>Ct()))}function xt({prop:t,order:e}){ht(t,e).then((()=>Ct()))}const Nt=i(!1),St=i("detail"),Lt=i("individual");const Mt=i(!1),Ot=i("");function Pt(t){switch(t){case F.CHECK_IN:return"success";case F.CHECK_OUT:return"info";case F.IN_BOOKING:return"warning";case F.CANCEL:case F.REFUSE:case F.NOSHOW:return"danger";case F.OVER:case F.BE_CONFIRM:return"";case F.CREDIT:case F.CONTINUE:return"warning";default:return""}}return(t,e)=>{const o=C,i=T,a=w,z=x,I=N,U=S,H=L,W=G,q=M,A=O,F=P,$=V,Q=R,dt=E,ut=s("auth"),bt=D;return r(),l("div",{class:v({"absolute-container":p(gt).tableAutoHeight})},[n(dt,null,{default:c((()=>[n(W,{"show-toggle":!1},{default:c((()=>[n(H,{model:p(gt).search,size:"default","label-width":"80px","inline-message":"",inline:"",class:"search-form"},{default:c((()=>[m("div",X,[n(a,{label:p(jt)("channel")},{default:c((()=>[n(i,{modelValue:p(gt).search.channelCode,"onUpdate:modelValue":e[0]||(e[0]=t=>p(gt).search.channelCode=t),clearable:"",class:"filter-select"},{default:c((()=>[n(o,{label:p(jt)("all"),value:"-1"},null,8,["label"]),(r(!0),l(d,null,u(p(yt),(t=>(r(),b(o,{key:t.channelCode,label:t.channelName,value:t.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),n(a,{label:p(jt)("guestSourceType")},{default:c((()=>[n(i,{modelValue:p(gt).search.guestSrcType,"onUpdate:modelValue":e[1]||(e[1]=t=>p(gt).search.guestSrcType=t),clearable:"",class:"filter-select"},{default:c((()=>[(r(!0),l(d,null,u(p(_t),(t=>(r(),b(o,{key:t.code,label:t.label,value:t.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),n(a,{label:p(jt)("roomType")},{default:c((()=>[n(i,{modelValue:p(gt).search.rtCode,"onUpdate:modelValue":e[2]||(e[2]=t=>p(gt).search.rtCode=t),clearable:"",class:"filter-select"},{default:c((()=>[(r(!0),l(d,null,u(p(ft),(t=>(r(),b(o,{key:t.rtCode,label:t.rtName,value:t.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])]),m("div",J,[n(a,{label:p(jt)("checkinType")},{default:c((()=>[n(i,{modelValue:p(gt).search.checkinType,"onUpdate:modelValue":e[3]||(e[3]=t=>p(gt).search.checkinType=t),clearable:"",class:"filter-select"},{default:c((()=>[(r(!0),l(d,null,u(p(vt),(t=>(r(),b(o,{key:t.code,label:t.label,value:t.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),n(a,{label:p(jt)("search"),class:"search-input-item"},{default:c((()=>[n(I,{modelValue:p(gt).search.searchContent,"onUpdate:modelValue":e[5]||(e[5]=t=>p(gt).search.searchContent=t),class:"filter-select w-350px!",clearable:"",placeholder:p(jt)("searchPlaceholder")},{append:c((()=>[n(z,{icon:p(h),onClick:e[4]||(e[4]=t=>wt())},null,8,["icon"])])),_:1},8,["modelValue","placeholder"]),n(z,{type:"primary",class:"query-button",onClick:Ct},{default:c((()=>[j(g(p(jt)("query")),1)])),_:1}),m("div",Z,[n(U,{modelValue:p(gt).search.displayWay,"onUpdate:modelValue":e[6]||(e[6]=t=>p(gt).search.displayWay=t),"active-value":"1","inactive-value":"0","active-text":p(jt)("guestMode"),"inactive-text":p(jt)("orderMode"),"inline-prompt":"",style:{"--el-switch-on-color":"#13ce66","--el-switch-off-color":"#554dd6"}},null,8,["modelValue","active-text","inactive-text"])])])),_:1},8,["label"])])])),_:1},8,["model"])])),_:1}),y((r(),b(F,{class:"list-table","header-cell-style":{background:"#f5f7fa",color:"#606266"},data:p(gt).dataList,stripe:"","highlight-current-row":"",border:"",height:"100%",onSortChange:xt},{default:c((()=>["0"===p(gt).search.displayWay?(r(),b(q,{key:0,type:"expand",width:"50"},{default:c((({row:t})=>[m("div",tt,[t.togetherList&&t.togetherList.length>0?(r(),b(F,{key:0,data:t.togetherList,class:"expand-table",border:""},{default:c((()=>[n(q,{prop:"name",label:p(jt)("guestLabel")},null,8,["label"]),n(q,{prop:"phone",label:p(jt)("phoneLabel")},null,8,["label"]),n(q,{label:p(jt)("guestSex")},{default:c((t=>[n(A,{type:"1"===t.row.sex?"primary":"danger",size:"small"},{default:c((()=>[j(g("1"===t.row.sex?p(jt)("male"):"0"===t.row.sex?p(jt)("female"):p(jt)("unknown")),1)])),_:2},1032,["type"])])),_:2},1032,["label"]),n(q,{label:p(jt)("mainOrderStatus")},{default:c((e=>[n(A,{type:"1"===e.row.isMain||1===t.togetherList.length?"success":"info",size:"small"},{default:c((()=>[j(g("1"===e.row.isMain||1===t.togetherList.length?p(jt)("mainGuest"):p(jt)("companion")),1)])),_:2},1032,["type"])])),_:2},1032,["label"]),n(q,{label:p(jt)("checkinTime")},{default:c((t=>[j(g(t.row.checkinTime?p(f)(t.row.checkinTime).format("MM/DD HH:mm"):""),1)])),_:2},1032,["label"]),n(q,{label:p(jt)("checkoutTime")},{default:c((t=>[j(g(t.row.checkoutTime?p(f)(t.row.checkoutTime).format("MM/DD HH:mm"):""),1)])),_:2},1032,["label"]),n(q,{label:p(jt)("planCheckoutTime")},{default:c((t=>[j(g(t.row.planCheckoutTime?p(f)(t.row.planCheckoutTime).format("MM/DD HH:mm"):""),1)])),_:2},1032,["label"]),n(q,{label:p(jt)("status"),width:"100"},{default:c((t=>[n(A,{type:Pt(t.row.state),size:"small"},{default:c((()=>{return[j(g((e=t.row.state,jt(`orderStatus.${e}`)||e)),1)];var e})),_:2},1032,["type"])])),_:2},1032,["label"])])),_:2},1032,["data"])):(r(),l("div",et,g(p(jt)("noData")),1))])])),_:1})):k("",!0),n(q,{label:p(jt)("guestPhone"),"min-width":"155"},{default:c((t=>[m("div",ot,[m("div",null,g(t.row.name),1),t.row.phone?(r(),l("div",it,g(t.row.phone),1)):k("",!0)])])),_:1},8,["label"]),n(q,{label:p(jt)("orderNo"),"min-width":"190"},{default:c((t=>[m("div",at,[m("div",null,[m("span",st,g(p(jt)("orderNoLabel")),1),j(g(t.row.orderNo),1)]),t.row.outOrderNo?(r(),l("div",rt,[m("span",lt,g(p(jt)("outOrderNoLabel")),1),j(g(t.row.outOrderNo),1)])):k("",!0)])])),_:1},8,["label"]),n(q,{label:p(jt)("roomTypePrice"),"min-width":"180"},{default:c((t=>[m("div",nt,[m("span",ct,g(t.row.rtName),1),m("span",pt,g(p(jt)("currencySymbol"))+g(t.row.price),1)])])),_:1},8,["label"]),n(q,{prop:"rNo",label:p(jt)("roomNo")},null,8,["label"]),n(q,{label:p(jt)("checkinTime")},{default:c((t=>[m("label",null,g(p(f)(t.row.checkinTime).format("MM/DD HH:mm")),1)])),_:1},8,["label"]),n(q,{prop:"checkinTypeName",label:p(jt)("checkinType")},null,8,["label"]),n(q,{prop:"guestSrcTypeName",label:p(jt)("guestSource")},null,8,["label"]),n(q,{prop:"channelName",label:p(jt)("channelLabel")},null,8,["label"]),n(q,{label:p(jt)("operation"),width:"130",align:"center",fixed:"right"},{default:c((t=>[y((r(),b($,{type:"primary",onClick:e=>{return o=t.row,gt.value.formModeProps.no=o.orderNo,void(Nt.value=!0);var o}},{default:c((()=>[j(g(p(jt)("view")),1)])),_:2},1032,["onClick"])),[[ut,"pms:order:query:get-order-detail"]])])),_:1},8,["label"])])),_:1},8,["data"])),[[bt,p(gt).loading]]),p(mt).total>10?(r(),b(Q,{key:0,"current-page":p(mt).pageNo,total:p(mt).total,"page-size":p(mt).pageSize,"page-sizes":p(mt).sizes,layout:p(mt).layout,"hide-on-single-page":!1,class:"pagination",background:"",onSizeChange:Tt,onCurrentChange:wt},null,8,["current-page","total","page-size","page-sizes","layout"])):k("",!0),p(Nt)?(r(),b(Y,{key:1,modelValue:p(Nt),"onUpdate:modelValue":e[7]||(e[7]=t=>_(Nt)?Nt.value=t:null),no:p(gt).formModeProps.no,"no-type":p(B).ORDER,"tab-name":p(St),"tab-type":p(Lt),onReload:Ct},null,8,["modelValue","no","no-type","tab-name","tab-type"])):k("",!0),p(Mt)?(r(),b(K,{key:2,modelValue:p(Mt),"onUpdate:modelValue":e[8]||(e[8]=t=>_(Mt)?Mt.value=t:null),"order-no":p(gt).formModeProps.no,"order-together-code":p(gt).formModeProps.orderTogetherCode,"order-type":p(B).ORDER,"tab-name":p(Ot)},null,8,["modelValue","order-no","order-together-code","order-type","tab-name"])):k("",!0)])),_:1})],2)}}});function dt(t){const e=t;e.__i18n=e.__i18n||[],e.__i18n.push({locale:"",resource:{en:{channel:{t:0,b:{t:2,i:[{t:3}],s:"Channel"}},search:{t:0,b:{t:2,i:[{t:3}],s:"Precise Search"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Type"}},query:{t:0,b:{t:2,i:[{t:3}],s:"Filter"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"Guest/Phone"}},orderNo:{t:0,b:{t:2,i:[{t:3}],s:"Order No"}},roomTypeLabel:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},roomTypePrice:{t:0,b:{t:2,i:[{t:3}],s:"Room Type/Price"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"Room No"}},price:{t:0,b:{t:2,i:[{t:3}],s:"Price"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Time"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source"}},channelLabel:{t:0,b:{t:2,i:[{t:3}],s:"Channel"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"Actions"}},view:{t:0,b:{t:2,i:[{t:3}],s:"View"}},entryAccount:{t:0,b:{t:2,i:[{t:3}],s:"Post"}},all:{t:0,b:{t:2,i:[{t:3}],s:"All"}},searchPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"OrderNo、Name、Phone、Room No"}},guestLabel:{t:0,b:{t:2,i:[{t:3}],s:"Guest"}},phoneLabel:{t:0,b:{t:2,i:[{t:3}],s:"Phone:"}},orderNoLabel:{t:0,b:{t:2,i:[{t:3}],s:"Order No:"}},outOrderNoLabel:{t:0,b:{t:2,i:[{t:3}],s:"External Order No:"}},currencySymbol:{t:0,b:{t:2,i:[{t:3}],s:"$"}},displayWay:{t:0,b:{t:2,i:[{t:3}],s:"Display Mode"}},orderMode:{t:0,b:{t:2,i:[{t:3}],s:"Order Mode"}},guestMode:{t:0,b:{t:2,i:[{t:3}],s:"Guest Mode"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"Guest Information"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},guestSex:{t:0,b:{t:2,i:[{t:3}],s:"Gender"}},mainOrderStatus:{t:0,b:{t:2,i:[{t:3}],s:"Is Main Guest"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-out Time"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Planned Check-out Time"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},male:{t:0,b:{t:2,i:[{t:3}],s:"Male"}},female:{t:0,b:{t:2,i:[{t:3}],s:"Female"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"Unknown"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"Yes"}},no:{t:0,b:{t:2,i:[{t:3}],s:"No"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"Main Guest"}},companion:{t:0,b:{t:2,i:[{t:3}],s:"Companion"}},orderStatus:{no_check_in:{t:0,b:{t:2,i:[{t:3}],s:"Booking"}},check_in:{t:0,b:{t:2,i:[{t:3}],s:"Checked In"}},check_out:{t:0,b:{t:2,i:[{t:3}],s:"Checked Out"}},noshow:{t:0,b:{t:2,i:[{t:3}],s:"No Show"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancelled"}},be_confirm:{t:0,b:{t:2,i:[{t:3}],s:"Pending Confirmation"}},refuse:{t:0,b:{t:2,i:[{t:3}],s:"Refused"}},over:{t:0,b:{t:2,i:[{t:3}],s:"Completed"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"Credit"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"Extended Stay"}}}},"zh-cn":{channel:{t:0,b:{t:2,i:[{t:3}],s:"渠道"}},search:{t:0,b:{t:2,i:[{t:3}],s:"精准搜索"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},query:{t:0,b:{t:2,i:[{t:3}],s:"查询"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"客人/电话"}},orderNo:{t:0,b:{t:2,i:[{t:3}],s:"订单号/外部订单号"}},roomTypeLabel:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},roomTypePrice:{t:0,b:{t:2,i:[{t:3}],s:"房型/房价"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},price:{t:0,b:{t:2,i:[{t:3}],s:"房价"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"入住时间"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},channelLabel:{t:0,b:{t:2,i:[{t:3}],s:"渠道"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},view:{t:0,b:{t:2,i:[{t:3}],s:"查看"}},entryAccount:{t:0,b:{t:2,i:[{t:3}],s:"入账"}},all:{t:0,b:{t:2,i:[{t:3}],s:"全部"}},searchPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"订单号、外部订单号、姓名、手机号、房号"}},guestLabel:{t:0,b:{t:2,i:[{t:3}],s:"客人"}},phoneLabel:{t:0,b:{t:2,i:[{t:3}],s:"电话："}},orderNoLabel:{t:0,b:{t:2,i:[{t:3}],s:"订单号："}},outOrderNoLabel:{t:0,b:{t:2,i:[{t:3}],s:"外部订单号："}},currencySymbol:{t:0,b:{t:2,i:[{t:3}],s:"￥"}},displayWay:{t:0,b:{t:2,i:[{t:3}],s:"显示方式"}},orderMode:{t:0,b:{t:2,i:[{t:3}],s:"订单模式"}},guestMode:{t:0,b:{t:2,i:[{t:3}],s:"客人模式"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"客人信息"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"姓名"}},guestSex:{t:0,b:{t:2,i:[{t:3}],s:"性别"}},mainOrderStatus:{t:0,b:{t:2,i:[{t:3}],s:"是否主客"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"退房时间"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"预离时间"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},male:{t:0,b:{t:2,i:[{t:3}],s:"男"}},female:{t:0,b:{t:2,i:[{t:3}],s:"女"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"保密"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"是"}},no:{t:0,b:{t:2,i:[{t:3}],s:"否"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"主客"}},companion:{t:0,b:{t:2,i:[{t:3}],s:"同住"}},orderStatus:{no_check_in:{t:0,b:{t:2,i:[{t:3}],s:"预订中"}},check_in:{t:0,b:{t:2,i:[{t:3}],s:"已入住"}},check_out:{t:0,b:{t:2,i:[{t:3}],s:"已退房"}},noshow:{t:0,b:{t:2,i:[{t:3}],s:"未到店"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"已取消"}},be_confirm:{t:0,b:{t:2,i:[{t:3}],s:"待确认"}},refuse:{t:0,b:{t:2,i:[{t:3}],s:"已拒绝"}},over:{t:0,b:{t:2,i:[{t:3}],s:"已完成"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"挂账"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"续住"}}}},km:{channel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},search:{t:0,b:{t:2,i:[{t:3}],s:"ស្វែងរកត្រឹមត្រូវ"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពភ្ញៀវ"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលសំណាក់"}},query:{t:0,b:{t:2,i:[{t:3}],s:"ត្រង"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវ/ទូរស័ព្ទ"}},orderNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែ"}},roomTypeLabel:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},roomTypePrice:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់/តម្លៃ"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់"}},price:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃ"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលចូលសំណាក់"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពភ្ញៀវ"}},channelLabel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"សកម្មភាព"}},view:{t:0,b:{t:2,i:[{t:3}],s:"មើល"}},entryAccount:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចូលគណនី"}},all:{t:0,b:{t:2,i:[{t:3}],s:"ទាំងអស់"}},searchPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែ, ឈ្មោះ, ទូរស័ព្ទ, លេខបន្ទប់"}},guestLabel:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវ"}},phoneLabel:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទ៖"}},orderNoLabel:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែ៖"}},outOrderNoLabel:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែខាងក្រៅ៖"}},currencySymbol:{t:0,b:{t:2,i:[{t:3}],s:"៛"}},displayWay:{t:0,b:{t:2,i:[{t:3}],s:"របៀបបង្ហាញ"}},orderMode:{t:0,b:{t:2,i:[{t:3}],s:"របៀបកម្មងែ"}},guestMode:{t:0,b:{t:2,i:[{t:3}],s:"របៀបភ្ញៀវ"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានភ្ញៀវ"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},guestSex:{t:0,b:{t:2,i:[{t:3}],s:"ភេទ"}},mainOrderStatus:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាពកម្មងែសំខាន់"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលចេញ"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលចេញគ្រោងទុក"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},male:{t:0,b:{t:2,i:[{t:3}],s:"ប្រុស"}},female:{t:0,b:{t:2,i:[{t:3}],s:"ស្រី"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"មិនស្គាល់"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"ប្រាក់"}},no:{t:0,b:{t:2,i:[{t:3}],s:"មិនប្រាក់"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវសំខាន់"}},companion:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវអមដំណើរ"}},orderStatus:{no_check_in:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងកក់"}},check_in:{t:0,b:{t:2,i:[{t:3}],s:"បានចូលស្នាក់នៅ"}},check_out:{t:0,b:{t:2,i:[{t:3}],s:"បានចាកចេញ"}},noshow:{t:0,b:{t:2,i:[{t:3}],s:"មិនបានមកដល់"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បានលុបចោល"}},be_confirm:{t:0,b:{t:2,i:[{t:3}],s:"រង់ចាំការបញ្ជាក់"}},refuse:{t:0,b:{t:2,i:[{t:3}],s:"បានបដិសេធ"}},over:{t:0,b:{t:2,i:[{t:3}],s:"បានបញ្ចប់"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"ជំពាក់"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"បន្តស្នាក់នៅ"}}}}}})}dt(mt);const ut=Q(mt,[["__scopeId","data-v-8f1fae36"]]);export{ut as default};
//# sourceMappingURL=todayCheckoutList-BLOPyol2.js.map
