{"version": 3, "file": "verifylog-BLfRViCm.js", "sources": ["../../src/views/merchant-finance/ar/acc-verify/components/DetailForm/verifylog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"writeOffSuccess\": \"Write-off successful\",\r\n    \"writeOffRecord\": \"Record\",\r\n    \"verifier\": \"Verifier\",\r\n    \"writeOffAmount\": \"Amount\",\r\n    \"writeOffTime\": \"Time\",\r\n    \"remark\": \"Remark\",\r\n    \"operation\": \"Operation\",\r\n    \"cancelWriteOff\": \"Cancel\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"writeOffSuccess\": \"核销成功\",\r\n    \"writeOffRecord\": \"核销记录\",\r\n    \"verifier\": \"核销人\",\r\n    \"writeOffAmount\": \"核销金额\",\r\n    \"writeOffTime\": \"核销时间\",\r\n    \"remark\": \"备注\",\r\n    \"operation\": \"操作\",\r\n    \"cancelWriteOff\": \"撤销核销\"\r\n  },\r\n  \"km\": {\r\n    \"writeOffSuccess\": \"ការលុបចោលដោយជោគជ័យ\",\r\n    \"writeOffRecord\": \"កំណត់ត្រាការលុបចោល\",\r\n    \"verifier\": \"អ្នកផ្ទៀងផ្ទាត់\",\r\n    \"writeOffAmount\": \"ចំនួនទឹកប្រាក់ដែលត្រូវលុបចោល\",\r\n    \"writeOffTime\": \"ពេលវេលាលុបចោល\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"operation\": \"ប្រតិបត្តិការ\",\r\n    \"cancelWriteOff\": \"បោះបង់ការលុបចោល\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { AccVerifyLogModel } from '@/models/index'\r\nimport type { DetailFormProps } from '../../types'\r\n\r\nconst props = withDefaults(\r\n  defineProps<\r\n    {\r\n      modelValue?: boolean\r\n    } & DetailFormProps\r\n  >(),\r\n  {\r\n    modelValue: false,\r\n    arSetCode: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst { t } = useI18n()\r\n\r\n// const userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst data = ref({\r\n  arSetCode: props.arSetCode,\r\n\r\n  dataList: [] as AccVerifyLogModel[],\r\n})\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nonMounted(() => {})\r\n\r\n/**\r\n * Cancel Write-off\r\n * @param row\r\n */\r\nfunction onCancelVerify(_row: any) {\r\n  ElMessage({\r\n    type: 'warning',\r\n    message: t('writeOffSuccess'),\r\n  })\r\n  onCancel()\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-dialog v-model=\"myVisible\" :title=\"t('writeOffRecord')\" width=\"1000px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <el-table v-loading=\"loading\" class=\"list-table\" :data=\"data.dataList\" highlight-current-row height=\"100%\" style=\"margin-top: 0\">\r\n        <el-table-column prop=\"creator\" :label=\"t('verifier')\" />\r\n        <el-table-column :label=\"t('writeOffAmount')\" align=\"right\">\r\n          <template #default=\"{ row }\"> ￥{{ row.fee }} </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('writeOffTime')\">\r\n          <template #default=\"{ row }\">\r\n            {{ row.createTime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"remark\" :label=\"t('remark')\" />\r\n        <el-table-column :label=\"t('operation')\">\r\n          <template #default=\"{ row }\">\r\n            <el-button type=\"text\" @click=\"onCancelVerify(row)\">\r\n              {{ t('cancelWriteOff') }}\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "loading", "ref", "data", "arSetCode", "dataList", "myVisible", "computed", "get", "modelValue", "set", "val", "onCancelVerify", "_row", "ElMessage", "type", "message", "value", "onMounted"], "mappings": "+tBAuCA,MAAMA,EAAQC,EAYRC,EAAQC,GAKRC,EAAEA,GAAMC,IAGRC,EAAUC,GAAI,GACdC,EAAOD,EAAI,CACfE,UAAWT,EAAMS,UAEjBC,SAAU,KAENC,EAAYC,EAAS,CACzBC,IAAM,IACGb,EAAMc,WAEf,GAAAC,CAAIC,GACFd,EAAM,oBAAqBc,EAAG,IAclC,SAASC,EAAeC,GACZC,EAAA,CACRC,KAAM,UACNC,QAASjB,EAAE,qBAZbO,EAAUW,OAAQ,CAcT,QAXXC,GAAU"}