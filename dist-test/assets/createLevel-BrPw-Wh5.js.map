{"version": 3, "file": "createLevel-BrPw-Wh5.js", "sources": ["../../src/views/group-member-plan/member-level/components/DetailForm/createLevel.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"pleaseEnterMemberLevelName\": \"Please enter member level name\",\r\n    \"addedSuccessfully\": \"Added successfully\",\r\n    \"levelName\": \"Level Name\",\r\n    \"memberLevel\": \"Member Level\",\r\n    \"pleaseSelect\": \"Please select\",\r\n    \"joiningMethod\": \"Joining Method\",\r\n    \"freeRegistration\": \"Free Registration\",\r\n    \"paymentAmount\": \"Payment Amount\",\r\n    \"allowModifyCardFeeWithPermission\": \"Allow modifying card fee amount, requires permission verification\",\r\n    \"enableRegistrationSmsVerification\": \"Enable registration must have SMS verification\",\r\n    \"membershipPeriod\": \"Period\",\r\n    \"permanent\": \"Permanent\",\r\n    \"fromRegistrationDate\": \"From the date of registration\",\r\n    \"monthsIsOneMembershipPeriod\": \"months is one membership period\",\r\n    \"membershipPeriodNote\": \"After the end, re-enter a new membership period; if the membership level changes during this period, a new membership period will be recalculated from the date of change.\",\r\n    \"storedValueAndConsumption\": \"Stored Value and Consumption\",\r\n    \"supportStoredValue\": \"Support Stored Value\",\r\n    \"noVerificationRequired\": \"No verification required\",\r\n    \"consumptionRequiresMemberPasswordVerification\": \"Consumption requires member password verification\",\r\n    \"consumptionRequiresSmsVerification\": \"Consumption requires SMS verification\",\r\n    \"reservedTime\": \"Reserved Time\",\r\n    \"unifiedNoshowTime\": \"Unified No-show Time\",\r\n    \"delayNoshowTimeTo\": \"Delay No-show Time to\",\r\n    \"exampleTime\": \"e.g.: 20:00\",\r\n    \"remarks\": \"Remark\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"pleaseEnterMemberLevelName\": \"请输入会员级别名称\",\r\n    \"addedSuccessfully\": \"新增成功\",\r\n    \"levelName\": \"级别名称\",\r\n    \"memberLevel\": \"会员等级\",\r\n    \"pleaseSelect\": \"请选择\",\r\n    \"joiningMethod\": \"加入方式\",\r\n    \"freeRegistration\": \"免费注册\",\r\n    \"paymentAmount\": \"付款金额\",\r\n    \"allowModifyCardFeeWithPermission\": \"允许修改卡费金额,需权限验证\",\r\n    \"enableRegistrationSmsVerification\": \"开启注册必须短信验证码认证\",\r\n    \"membershipPeriod\": \"有效期\",\r\n    \"permanent\": \"永久\",\r\n    \"fromRegistrationDate\": \"自注册之日起\",\r\n    \"monthsIsOneMembershipPeriod\": \"个月之内为1个会员周期\",\r\n    \"membershipPeriodNote\": \"结束后重新进入新的会员周期；若此期间发生会员等级变更，自变更之日起，重新计算新的会员周期.\",\r\n    \"storedValueAndConsumption\": \"储值和消费\",\r\n    \"supportStoredValue\": \"支持储值\",\r\n    \"noVerificationRequired\": \"无需验证\",\r\n    \"consumptionRequiresMemberPasswordVerification\": \"消费需要验证会员密码\",\r\n    \"consumptionRequiresSmsVerification\": \"消费需要短信验证码\",\r\n    \"reservedTime\": \"预留时间\",\r\n    \"unifiedNoshowTime\": \"统一Noshow时间\",\r\n    \"delayNoshowTimeTo\": \"延迟Noshow时间至\",\r\n    \"exampleTime\": \"如：20:00\",\r\n    \"remarks\": \"备注\"\r\n  },\r\n  \"km\": {\r\n    \"pleaseEnterMemberLevelName\": \"សូមបញ្ចូលឈ្មោះកម្រិតសមាជិក\",\r\n    \"addedSuccessfully\": \"បន្ថែមដោយជោគជ័យ\",\r\n    \"levelName\": \"ឈ្មោះកម្រិត\",\r\n    \"memberLevel\": \"កម្រិតសមាជិក\",\r\n    \"pleaseSelect\": \"សូមជ្រើសរើស\",\r\n    \"joiningMethod\": \"វិធីសាកសម\",\r\n    \"freeRegistration\": \"ចុះឈ្មោះដោយឥតគិតថ្លៃ\",\r\n    \"paymentAmount\": \"ចំនួនទឹកប្រាក់\",\r\n    \"allowModifyCardFeeWithPermission\": \"អនុញ្ញាតឱ្យកែសម្រួលថ្លៃកាត ត្រូវការផ្ទៀងផ្ទាត់សិទ្ធិ\",\r\n    \"enableRegistrationSmsVerification\": \"បើកដំណើរការការផ្ទៀងផ្ទាត់សារអសយដ្ឋាន\",\r\n    \"membershipPeriod\": \"រយៈពេលសមាជិក\",\r\n    \"permanent\": \"អចិន្ត្រៃយ៍\",\r\n    \"fromRegistrationDate\": \"ចាប់ពីថ្ងៃចុះឈ្មោះ\",\r\n    \"monthsIsOneMembershipPeriod\": \"ខែជារយៈពេលសមាជិកមួយ\",\r\n    \"membershipPeriodNote\": \"បន្ទាប់ពីបញ្ចប់ ចូលរយៈពេលសមាជិកថ្មីម្តងទៀត។ ប្រសិនបើកម្រិតសមាជិកផ្លាស់ប្តូរក្នុងអំឡុងពេលនេះ រយៈពេលសមាជិកថ្មីនឹងត្រូវបានគណនាឡើងវិញចាប់ពីថ្ងៃផ្លាស់ប្តូរ។\",\r\n    \"storedValueAndConsumption\": \"ការផ្ទុកតម្លៃនិងការប្រើប្រាស់\",\r\n    \"supportStoredValue\": \"គាំទ្រការផ្ទុកតម្លៃ\",\r\n    \"noVerificationRequired\": \"មិនចាំបាច់ផ្ទៀងផ្ទាត់\",\r\n    \"consumptionRequiresMemberPasswordVerification\": \"ការប្រើប្រាស់ត្រូវការផ្ទៀងផ្ទាត់ពាក្យសម្ងាត់សមាជិក\",\r\n    \"consumptionRequiresSmsVerification\": \"ការប្រើប្រាស់ត្រូវការលេខកូដសារអសយដ្ឋាន\",\r\n    \"reservedTime\": \"ពេលវេលាកក់ទុក\",\r\n    \"unifiedNoshowTime\": \"ពេលវេលា No-show ឯកសណ្ឋាន\",\r\n    \"delayNoshowTimeTo\": \"ពន្យារពេលវេលា No-show ដល់\",\r\n    \"exampleTime\": \"ឧទាហរណ៍៖ 20:00\",\r\n    \"remarks\": \"ចំណាំ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { memberTypeApi } from '@/api/modules/index'\r\nimport { BooleanEnum, MEMBER_TYPE_LEVEL } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  mtCode: '',\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\n\r\nconst form = ref({\r\n  mtCode: props.mtCode,\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 会员类型名称 */\r\n  mtName: '',\r\n  /** 级别 */\r\n  level: 1,\r\n  /** 是否永久有效 */\r\n  isForEver: '1',\r\n  /** 有效期（月） */\r\n  indateMonth: 12,\r\n  /** 是否免费 */\r\n  isFree: '1',\r\n  /** 购卡金额 */\r\n  fee: 0,\r\n  /** 允许修改卡费金额 */\r\n  canUpdateFee: '1',\r\n  /** 开启注册必须短信验证码 */\r\n  needSms: '1',\r\n  /** 是否支持储值 */\r\n  supportStore: '1',\r\n  /** 消费验证方式 0:无需验证 1:消费需要验证会员密码 2:消费需要短信验证码 */\r\n  verifyMode: '0',\r\n  /** 是否延迟noshow时间 */\r\n  delayNoshow: '1',\r\n  /** 延迟时间 */\r\n  noshowTime: '',\r\n  /** 是否有效 0：否 1：是 */\r\n  isEnable: BooleanEnum.YES,\r\n  /** 备注 */\r\n  remark: '',\r\n})\r\nconst formRules = ref<FormRules>({\r\n  mtName: [\r\n    {\r\n      required: true,\r\n      message: t('pleaseEnterMemberLevelName'),\r\n      trigger: 'blur',\r\n    },\r\n  ],\r\n})\r\n\r\nonMounted(() => {})\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            memberTypeApi.createType(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('addedSuccessfully'),\r\n                  center: true,\r\n                })\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n              resolve()\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"160px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('levelName')\" prop=\"mtName\">\r\n        <el-input v-model=\"form.mtName\" :placeholder=\"t('pleaseEnterMemberLevelName')\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('memberLevel')\" prop=\"level\">\r\n        <el-select v-model=\"form.level\" :placeholder=\"t('pleaseSelect')\">\r\n          <el-option v-for=\"item in MEMBER_TYPE_LEVEL\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('joiningMethod')\">\r\n        <el-radio-group v-model=\"form.isFree\" class=\"vertical-radio-group\">\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('freeRegistration') }}\r\n          </el-radio>\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('paymentAmount') }}\r\n            <el-input-number v-model=\"form.fee\" :min=\"0\" :disabled=\"form.isFree === '1'\" />\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-checkbox v-model=\"form.canUpdateFee\" true-value=\"1\" false-value=\"0\" :disabled=\"form.isFree === '1'\">\r\n          {{ t('allowModifyCardFeeWithPermission') }}\r\n        </el-checkbox>\r\n        <el-checkbox v-model=\"form.needSms\" true-value=\"1\" false-value=\"0\" :disabled=\"form.isFree === '1'\">\r\n          {{ t('enableRegistrationSmsVerification') }}\r\n        </el-checkbox>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('membershipPeriod')\">\r\n        <el-radio-group v-model=\"form.isForEver\" class=\"vertical-radio-group\">\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('permanent') }}\r\n          </el-radio>\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('fromRegistrationDate') }}\r\n            <el-input-number v-model=\"form.indateMonth\" :min=\"1\" :disabled=\"form.isForEver === '1'\" />\r\n            {{ t('monthsIsOneMembershipPeriod') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n        <div class=\"el-form-item-msg\">\r\n          {{ t('membershipPeriodNote') }}\r\n        </div>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <el-form-item :label=\"t('storedValueAndConsumption')\">\r\n        <el-checkbox v-model=\"form.supportStore\" true-value=\"1\" false-value=\"0\">\r\n          {{ t('supportStoredValue') }}\r\n        </el-checkbox>\r\n        <div class=\"el-form-item-msg\">\r\n          <el-radio-group v-if=\"form.supportStore === '1'\" v-model=\"form.verifyMode\">\r\n            <el-radio value=\"0\" size=\"large\">\r\n              {{ t('noVerificationRequired') }}\r\n            </el-radio>\r\n            <el-radio value=\"1\" size=\"large\">\r\n              {{ t('consumptionRequiresMemberPasswordVerification') }}\r\n            </el-radio>\r\n            <el-radio value=\"2\" size=\"large\">\r\n              {{ t('consumptionRequiresSmsVerification') }}\r\n            </el-radio>\r\n          </el-radio-group>\r\n        </div>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <el-form-item :label=\"t('reservedTime')\">\r\n        <el-radio-group v-model=\"form.delayNoshow\">\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('unifiedNoshowTime') }}\r\n          </el-radio>\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('delayNoshowTimeTo') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n        <el-time-select v-model=\"form.noshowTime\" start=\"12:00\" step=\"00:15\" end=\"23:59\" :placeholder=\"t('exampleTime')\" style=\"width: 240px\" :disabled=\"form.delayNoshow === '0'\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('remarks')\">\r\n        <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"5\" maxlength=\"250\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.vertical-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  text-align: left;\r\n\r\n  .el-checkbox {\r\n    margin-bottom: 5px;\r\n  }\r\n}\r\n\r\n.vertical-radio {\r\n  margin-right: 0;\r\n\r\n  /* 移除右侧的间距 */\r\n}\r\n\r\n.settprice {\r\n  margin-left: 5px;\r\n  color: rgba(85, 77, 214, 1);\r\n  cursor: pointer;\r\n}\r\n\r\n.setticon {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 5px;\r\n  color: #999;\r\n\r\n  .el-icon {\r\n    margin-right: 2px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "mtCode", "gcode", "mtName", "level", "isForEver", "indateMonth", "isFree", "fee", "canUpdateFee", "needSms", "supportStore", "verifyMode", "delayNoshow", "noshowTime", "isEnable", "BooleanEnum", "YES", "remark", "formRules", "required", "message", "trigger", "onMounted", "__expose", "submit", "Promise", "resolve", "value", "validate", "valid", "memberTypeApi", "createType", "then", "res", "code", "ElMessage", "success", "center", "error", "msg"], "mappings": "giCA6FA,MAAMA,EAAQC,GAIRC,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IAEVE,EAAOF,EAAI,CACfG,OAAQV,EAAMU,OAEdC,MAAOP,EAAUO,MAEjBC,OAAQ,GAERC,MAAO,EAEPC,UAAW,IAEXC,YAAa,GAEbC,OAAQ,IAERC,IAAK,EAELC,aAAc,IAEdC,QAAS,IAETC,aAAc,IAEdC,WAAY,IAEZC,YAAa,IAEbC,WAAY,GAEZC,SAAUC,EAAYC,IAEtBC,OAAQ,KAEJC,EAAYrB,EAAe,CAC/BK,OAAQ,CACN,CACEiB,UAAU,EACVC,QAAS5B,EAAE,8BACX6B,QAAS,kBAKfC,GAAU,SAEGC,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB5B,EAAQ6B,OACN7B,EAAQ6B,MAAMC,UAAUC,IAClBA,GACFC,EAAcC,WAAWhC,EAAK4B,OAAOK,MAAMC,IACxB,IAAbA,EAAIC,KACNC,EAAUC,QAAQ,CAChBhB,QAAS5B,EAAE,qBACX6C,QAAQ,IAGVF,EAAUG,MAAM,CACdlB,QAASa,EAAIM,IACbF,QAAQ,IAGJX,GAAA,GACT,GAEJ"}