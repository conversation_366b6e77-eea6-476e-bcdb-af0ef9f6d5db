{"version": 3, "file": "grouptree-B8OdHqYc.js", "sources": ["../../src/utils/grouptree.ts"], "sourcesContent": ["export function handleTree(data: any[], id?: string, parentId?: string, children?: string) {\r\n  if (!Array.isArray(data)) {\r\n    return []\r\n  }\r\n  const config = {\r\n    id: id || 'id',\r\n    parentId: parentId || 'parentId',\r\n    childrenList: children || 'children',\r\n  }\r\n\r\n  const childrenListMap = {}\r\n  const nodeIds = {}\r\n  const tree: any[] = []\r\n\r\n  for (const d of data) {\r\n    const parentId = d[config.parentId]\r\n    if (childrenListMap[parentId] == null) {\r\n      childrenListMap[parentId] = []\r\n    }\r\n    nodeIds[d[config.id]] = d\r\n    childrenListMap[parentId].push(d)\r\n  }\r\n\r\n  for (const d of data) {\r\n    const parentId = d[config.parentId]\r\n    if (nodeIds[parentId] == null) {\r\n      tree.push(d)\r\n    }\r\n  }\r\n\r\n  for (const t of tree) {\r\n    adaptToChildrenList(t)\r\n  }\r\n\r\n  function adaptToChildrenList(o: any) {\r\n    if (childrenListMap[o[config.id]] !== null) {\r\n      o[config.childrenList] = childrenListMap[o[config.id]]\r\n    }\r\n    if (o[config.childrenList]) {\r\n      for (const c of o[config.childrenList]) {\r\n        adaptToChildrenList(c)\r\n      }\r\n    }\r\n  }\r\n  return tree\r\n}\r\n"], "names": ["handleTree", "data", "id", "parentId", "children", "Array", "isArray", "config", "childrenListMap", "nodeIds", "tree", "d", "push", "t", "adaptToChildrenList", "o", "c"], "mappings": "AAAO,SAASA,EAAWC,EAAaC,EAAaC,EAAmBC,GACtE,IAAKC,MAAMC,QAAQL,GACjB,MAAO,GAET,MAAMM,EACM,KADNA,EAEkB,WAFlBA,EAGsB,WAGtBC,EAAkB,CAAC,EACnBC,EAAU,CAAC,EACXC,EAAc,GAEpB,IAAA,MAAWC,KAAKV,EAAM,CACdE,MAAAA,EAAWQ,EAAEJ,GACc,MAA7BC,EAAgBL,KACFA,EAAAA,GAAY,IAE9BM,EAAQE,EAAEJ,IAAcI,EACRR,EAAAA,GAAUS,KAAKD,EAAC,CAGlC,IAAA,MAAWA,KAAKV,EAAM,CAEK,MAArBQ,EADaE,EAAEJ,KAEjBG,EAAKE,KAAKD,EACZ,CAGF,IAAA,MAAWE,KAAKH,EACdI,EAAoBD,GAGtB,SAASC,EAAoBC,GAIvB,GAHkC,OAAlCP,EAAgBO,EAAER,MACpBQ,EAAER,GAAuBC,EAAgBO,EAAER,KAEzCQ,EAAER,GACJ,IAAA,MAAWS,KAAKD,EAAER,GAChBO,EAAoBE,EAExB,CAEK,OAAAN,CACT"}