# 联房组件预订单功能测试

## 修改内容总结

### 1. 类型定义扩展 (lianFang.d.ts)
- 添加了 `BookOrderRespVO` 接口，包含预订单的所有必要字段
- 添加了 `PageResultBookOrderRespVO` 接口用于分页结果

### 2. 组件功能扩展 (index.vue)
- 添加了预订单标签页 "预订单"
- 添加了预订单数据状态管理
- 添加了预订单表格配置，包含以下列：
  - 状态 (stateName)
  - 姓名 (contact) 
  - 房型 (rtName)
  - 电话 (phone)
  - 预抵时间 (planCheckinTime)
  - 预离时间 (planCheckoutTime)

### 3. API 集成
- 集成了 `bookApi.getMergeReceptionBooks` 接口
- 传递参数：gcode, hcode (从用户存储获取)
- 支持分页和搜索功能

### 4. 国际化支持
- 添加了中英文标签：
  - labelPlanCheckinTime: "预抵时间" / "Plan Checkin"
  - labelContact: "联系人" / "Contact"  
  - labelPhone: "电话" / "Phone"

## 功能特性

1. **双标签页设计**：接待单和预订单分别在不同标签页
2. **统一搜索**：房号搜索对两个标签页都有效
3. **独立选择**：每个标签页有独立的多选功能
4. **数据展示**：按照需求展示状态、姓名、房型、电话、预抵时间、预离时间
5. **响应式加载**：标签页切换时自动加载对应数据

## 待完善功能

- 预订单的联房提交逻辑（目前显示警告信息）
- 可能需要根据实际业务需求调整预订单联房的处理方式

## 测试建议

1. 测试标签页切换功能
2. 测试预订单数据加载
3. 测试搜索功能在预订单标签页的效果
4. 测试分页功能
5. 验证数据展示格式是否符合需求
