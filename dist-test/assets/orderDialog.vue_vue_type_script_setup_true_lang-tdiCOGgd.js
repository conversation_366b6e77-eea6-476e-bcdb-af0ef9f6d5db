import{d as e,y as o,b as a,B as l,o as s,c as t,f as d,w as u,a6 as n,u as r,i as m,ay as p}from"./index-CkEhI1Zk.js";/* empty css                  *//* empty css                   */import f from"./order-ChSzi_-7.js";const i=e({name:"OrderInfoIndex",__name:"orderDialog",props:{modelValue:{type:Boolean,default:!1},tabName:{default:""},bookNo:{default:""},bookType:{default:""},no:{},noType:{default:""},tabType:{default:""}},emits:["update:modelValue","success","reload"],setup(e,{emit:i}){const c=e,y=i;o((()=>{}));const V=a(),b=l({get:()=>c.modelValue,set(e){y("update:modelValue",e)}});function _(){b.value=!1,y("reload")}return(e,o)=>{const a=p;return s(),t("div",null,[d(a,{modelValue:r(b),"onUpdate:modelValue":o[0]||(o[0]=e=>m(b)?b.value=e:null),width:"98%","close-on-click-modal":!1,"append-to-body":"","destroy-on-close":"","align-center":"","show-close":!1},{default:u((()=>[d(f,n({ref_key:"formRef",ref:V},c,{onClose:_}),null,16)])),_:1},8,["modelValue"])])}}});export{i as _};
//# sourceMappingURL=orderDialog.vue_vue_type_script_setup_true_lang-tdiCOGgd.js.map
