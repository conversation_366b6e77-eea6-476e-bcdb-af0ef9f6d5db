import{d as a,a3 as t,b as e,o,c as s,ab as r,h as n,Y as i,R as l,g as c,u as d,f as p,a7 as b,an as g,_ as u}from"./index-CkEhI1Zk.js";const f={key:0,class:"title-container border-b-1 border-b-[var(--g-bg)] border-b-solid px-5 py-4 transition-border-color-300"},m={class:"main-container p-5"},v=a({name:"PageMain",__name:"index",props:{title:{default:""},collaspe:{type:Boolean,default:!1},height:{default:""}},setup(a){const v=a,h=!!t().title,x=e(v.collaspe);function y(){x.value=!1}return(a,t)=>{const e=u;return o(),s("div",{class:b(["page-main relative m-4 flex flex-col bg-[var(--g-container-bg)] transition-background-color-300",{"of-hidden":d(x)}]),style:g({height:d(x)?a.height:""})},[h||a.title?(o(),s("div",f,[r(a.$slots,"title",{},(()=>[n(i(a.title),1)]))])):l("",!0),c("div",m,[r(a.$slots,"default")]),d(x)?(o(),s("div",{key:1,class:"collaspe absolute bottom-0 w-full cursor-pointer from-transparent to-[var(--g-container-bg)] bg-gradient-to-b pb-2 pt-10 text-center",onClick:y},[p(e,{name:"i-ep:arrow-down",class:"text-xl op-30 transition-opacity hover-op-100"})])):l("",!0)],6)}}});export{v as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-DK1kOKr4.js.map
