{"version": 3, "file": "useTabbar-vVaUGHZV.js", "sources": ["../../src/utils/composables/useTabbar.ts"], "sourcesContent": ["import type { RouteLocationRaw } from 'vue-router'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useTabbarStore from '@/store/modules/tabbar'\r\nimport Message from 'vue-m-message'\r\n\r\nexport default function useTabbar() {\r\n  const route = useRoute()\r\n  const router = useRouter()\r\n\r\n  const settingsStore = useSettingsStore()\r\n  const tabbarStore = useTabbarStore()\r\n\r\n  function getId() {\r\n    return settingsStore.settings.tabbar.mergeTabsBy === 'activeMenu' ? (route.meta.activeMenu ?? route.fullPath) : route.fullPath\r\n  }\r\n\r\n  function open(to: RouteLocationRaw) {\r\n    const index = tabbarStore.list.findIndex(item => item.tabId === getId())\r\n    tabbarStore.$patch({\r\n      leaveIndex: index,\r\n    })\r\n    router.push(to)\r\n  }\r\n\r\n  function go(delta: number) {\r\n    const tabId = getId()\r\n    router.go(delta)\r\n    tabbarStore.remove(tabId)\r\n  }\r\n\r\n  function close(to: RouteLocationRaw) {\r\n    const tabId = getId()\r\n    router.push(to).then(() => {\r\n      tabbarStore.remove(tabId)\r\n    })\r\n  }\r\n\r\n  function closeById(tabId = getId()) {\r\n    const activedTabId = getId()\r\n    if (tabbarStore.list.some(item => item.tabId === tabId)) {\r\n      if (tabbarStore.list.length > 1) {\r\n        // 如果关闭的标签正好是当前路由\r\n        if (tabId === activedTabId) {\r\n          const index = tabbarStore.list.findIndex(item => item.tabId === tabId)\r\n          if (index < tabbarStore.list.length - 1) {\r\n            close(tabbarStore.list[index + 1].fullPath)\r\n          }\r\n          else {\r\n            close(tabbarStore.list[index - 1].fullPath)\r\n          }\r\n        }\r\n        else {\r\n          tabbarStore.remove(tabId)\r\n        }\r\n      }\r\n      else {\r\n        Message.error('当前只有一个标签页，已阻止关闭', {\r\n          zIndex: 2000,\r\n        })\r\n      }\r\n    }\r\n    else {\r\n      Message.error('关闭的页面不存在', {\r\n        zIndex: 2000,\r\n      })\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 关闭两侧标签页\r\n   */\r\n  function closeOtherSide(tabId = getId()) {\r\n    const activedTabId = getId()\r\n    // 如果操作的是非当前路由标签页，则先跳转到指定路由标签页\r\n    if (tabId !== activedTabId) {\r\n      const index = tabbarStore.list.findIndex(item => item.tabId === tabId)\r\n      router.push(tabbarStore.list[index].fullPath)\r\n    }\r\n    tabbarStore.removeOtherSide(tabId)\r\n  }\r\n\r\n  /**\r\n   * 关闭左侧标签页\r\n   */\r\n  function closeLeftSide(tabId = getId()) {\r\n    const activedTabId = getId()\r\n    // 如果操作的是非当前路由标签页，需要判断当前标签页是否在指定标签页左侧，如果是则先跳转到指定路由标签页\r\n    if (tabId !== activedTabId) {\r\n      const index = tabbarStore.list.findIndex(item => item.tabId === tabId)\r\n      const activedIndex = tabbarStore.list.findIndex(item => item.tabId === activedTabId)\r\n      if (activedIndex < index) {\r\n        router.push(tabbarStore.list[index].fullPath)\r\n      }\r\n    }\r\n    tabbarStore.removeLeftSide(tabId)\r\n  }\r\n\r\n  /**\r\n   * 关闭右侧标签页\r\n   */\r\n  function closeRightSide(tabId = getId()) {\r\n    const activedTabId = getId()\r\n    // 如果操作的是非当前路由标签页，需要判断当前标签页是否在指定标签页右侧，如果是则先跳转到指定路由标签页\r\n    if (tabId !== activedTabId) {\r\n      const index = tabbarStore.list.findIndex(item => item.tabId === tabId)\r\n      const activedIndex = tabbarStore.list.findIndex(item => item.tabId === activedTabId)\r\n      if (activedIndex > index) {\r\n        router.push(tabbarStore.list[index].fullPath)\r\n      }\r\n    }\r\n    tabbarStore.removeRightSide(tabId)\r\n  }\r\n\r\n  /**\r\n   * 校验指定标签两侧是否有可关闭的标签\r\n   */\r\n  function checkCloseOtherSide(tabId = getId()) {\r\n    return tabbarStore.list.some((item) => {\r\n      return !item.isPermanent && !item.isPin && item.tabId !== tabId\r\n    })\r\n  }\r\n\r\n  /**\r\n   * 校验指定标签左侧是否有可关闭的标签\r\n   */\r\n  function checkCloseLeftSide(tabId = getId()) {\r\n    let flag = true\r\n    if (tabId === tabbarStore.list[0]?.tabId) {\r\n      flag = false\r\n    }\r\n    else {\r\n      const index = tabbarStore.list.findIndex(item => item.tabId === tabId)\r\n      flag = tabbarStore.list.some((item, i) => {\r\n        return i < index && !item.isPermanent && !item.isPin && item.tabId !== tabId\r\n      })\r\n    }\r\n    return flag\r\n  }\r\n\r\n  /**\r\n   * 校验指定标签右侧是否有可关闭的标签\r\n   */\r\n  function checkCloseRightSide(tabId = getId()) {\r\n    let flag = true\r\n    if (tabId === tabbarStore.list.at(-1)?.tabId) {\r\n      flag = false\r\n    }\r\n    else {\r\n      const index = tabbarStore.list.findIndex(item => item.tabId === tabId)\r\n      flag = tabbarStore.list.some((item, i) => {\r\n        return i >= index && !item.isPermanent && !item.isPin && item.tabId !== tabId\r\n      })\r\n    }\r\n    return flag\r\n  }\r\n\r\n  return {\r\n    getId,\r\n    open,\r\n    go,\r\n    close,\r\n    closeById,\r\n    closeOtherSide,\r\n    closeLeftSide,\r\n    closeRightSide,\r\n    checkCloseOtherSide,\r\n    checkCloseLeftSide,\r\n    checkCloseRightSide,\r\n  }\r\n}\r\n"], "names": ["useTabbar", "route", "useRoute", "router", "useRouter", "settingsStore", "useSettingsStore", "tabbarStore", "useTabbarStore", "getId", "settings", "tabbar", "mergeTabsBy", "meta", "activeMenu", "fullPath", "close", "to", "tabId", "push", "then", "remove", "open", "index", "list", "findIndex", "item", "$patch", "leaveIndex", "go", "delta", "closeById", "activedTabId", "some", "length", "Message", "error", "zIndex", "closeOtherSide", "removeOtherSide", "closeLeftSide", "removeLeftSide", "closeRightSide", "removeRightSide", "checkCloseOtherSide", "isPermanent", "isPin", "checkCloseLeftSide", "flag", "_a", "i", "checkCloseRightSide", "at"], "mappings": "sEAKA,SAAwBA,IACtB,MAAMC,EAAQC,IACRC,EAASC,IAETC,EAAgBC,IAChBC,EAAcC,IAEpB,SAASC,IACA,MAA8C,eAA9CJ,EAAcK,SAASC,OAAOC,YAAgCX,EAAMY,KAAKC,YAAcb,EAAMc,SAAYd,EAAMc,QAAA,CAiBxH,SAASC,EAAMC,GACb,MAAMC,EAAQT,IACdN,EAAOgB,KAAKF,GAAIG,MAAK,KACnBb,EAAYc,OAAOH,EAAK,GACzB,CA0HI,MAAA,CACLT,QACAa,KA9IF,SAAcL,GACNM,MAAAA,EAAQhB,EAAYiB,KAAKC,cAAkBC,EAAKR,QAAUT,MAChEF,EAAYoB,OAAO,CACjBC,WAAYL,IAEdpB,EAAOgB,KAAKF,EAAE,EA0IdY,GAvIF,SAAYC,GACV,MAAMZ,EAAQT,IACdN,EAAO0B,GAAGC,GACVvB,EAAYc,OAAOH,EAAK,EAqIxBF,QACAe,UA5HO,SAAUb,EAAQT,KACzB,MAAMuB,EAAevB,IACjB,GAAAF,EAAYiB,KAAKS,SAAaP,EAAKR,QAAUA,IAC3C,GAAAX,EAAYiB,KAAKU,OAAS,EAE5B,GAAIhB,IAAUc,EAAc,CACpBT,MAAAA,EAAQhB,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUA,IAC5DK,EAAQhB,EAAYiB,KAAKU,OAAS,EACpClB,EAAMT,EAAYiB,KAAKD,EAAQ,GAAGR,UAGlCC,EAAMT,EAAYiB,KAAKD,EAAQ,GAAGR,SACpC,MAGAR,EAAYc,OAAOH,QAIrBiB,EAAQC,MAAM,kBAAmB,CAC/BC,OAAQ,WAKZF,EAAQC,MAAM,WAAY,CACxBC,OAAQ,KAEZ,EAiGAC,eA3FO,SAAepB,EAAQT,KAC9B,MAAMuB,EAAevB,IAErB,GAAIS,IAAUc,EAAc,CACpBT,MAAAA,EAAQhB,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUA,IAChEf,EAAOgB,KAAKZ,EAAYiB,KAAKD,GAAOR,SAAQ,CAE9CR,EAAYgC,gBAAgBrB,EAAK,EAqFjCsB,cA/EO,SAActB,EAAQT,KAC7B,MAAMuB,EAAevB,IAErB,GAAIS,IAAUc,EAAc,CACpBT,MAAAA,EAAQhB,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUA,IAC3CX,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUc,IACpDT,GACjBpB,EAAOgB,KAAKZ,EAAYiB,KAAKD,GAAOR,SACtC,CAEFR,EAAYkC,eAAevB,EAAK,EAsEhCwB,eAhEO,SAAexB,EAAQT,KAC9B,MAAMuB,EAAevB,IAErB,GAAIS,IAAUc,EAAc,CACpBT,MAAAA,EAAQhB,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUA,IAC3CX,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUc,IACpDT,GACjBpB,EAAOgB,KAAKZ,EAAYiB,KAAKD,GAAOR,SACtC,CAEFR,EAAYoC,gBAAgBzB,EAAK,EAuDjC0B,oBAjDO,SAAoB1B,EAAQT,KACnC,OAAOF,EAAYiB,KAAKS,MAAMP,IACpBA,EAAKmB,cAAgBnB,EAAKoB,OAASpB,EAAKR,QAAUA,GAC3D,EA+CD6B,mBAzCO,SAAmB7B,EAAQT,WAClC,IAAIuC,GAAO,EACX,GAAI9B,KAAU,OAAA+B,EAAY1C,EAAAiB,KAAK,aAAIN,OAC1B8B,GAAA,MAEJ,CACGzB,MAAAA,EAAQhB,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUA,IAChE8B,EAAOzC,EAAYiB,KAAKS,MAAK,CAACP,EAAMwB,IAC3BA,EAAI3B,IAAUG,EAAKmB,cAAgBnB,EAAKoB,OAASpB,EAAKR,QAAUA,GACxE,CAEI,OAAA8B,CAAA,EA+BPG,oBAzBO,SAAoBjC,EAAQT,WACnC,IAAIuC,GAAO,EACX,GAAI9B,KAAU,OAAA+B,EAAY1C,EAAAiB,KAAK4B,IAAK,aAAGlC,OAC9B8B,GAAA,MAEJ,CACGzB,MAAAA,EAAQhB,EAAYiB,KAAKC,WAAkBC,GAAAA,EAAKR,QAAUA,IAChE8B,EAAOzC,EAAYiB,KAAKS,MAAK,CAACP,EAAMwB,IAC3BA,GAAK3B,IAAUG,EAAKmB,cAAgBnB,EAAKoB,OAASpB,EAAKR,QAAUA,GACzE,CAEI,OAAA8B,CAAA,EAgBX"}