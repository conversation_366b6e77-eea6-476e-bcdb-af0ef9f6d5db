{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-BcBNymSh.js", "sources": ["../../src/components/PcasCascader/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\n// 行政区划数据来源于 https://github.com/modood/Administrative-divisions-of-China\r\nimport pcasRaw from './pcas-code.json'\r\n\r\ndefineOptions({\r\n  name: 'PcasCascader',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    disabled?: boolean\r\n    type?: 'pc' | 'pca' | 'pcas'\r\n    format?: 'code' | 'name' | 'both'\r\n  }>(),\r\n  {\r\n    disabled: false,\r\n    type: 'pca',\r\n    format: 'code',\r\n  },\r\n)\r\n\r\nconst value = defineModel<string[] | {\r\n  code: string\r\n  name: string\r\n}[]>({\r\n  default: [],\r\n})\r\n\r\ninterface pcasItem {\r\n  code: string\r\n  name: string\r\n  children?: pcasItem[]\r\n}\r\n\r\nconst pcasData = computed(() => {\r\n  const data: pcasItem[] = []\r\n  // 省份\r\n  pcasRaw.forEach((p) => {\r\n    const tempP: pcasItem = {\r\n      code: p.code,\r\n      name: p.name,\r\n    }\r\n    const tempChildrenC: pcasItem[] = []\r\n    // 城市\r\n    p.children.forEach((c) => {\r\n      const tempC: pcasItem = {\r\n        code: c.code,\r\n        name: c.name,\r\n      }\r\n      if (['pca', 'pcas'].includes(props.type)) {\r\n        const tempChildrenA: pcasItem[] = []\r\n        // 区县\r\n        c.children.forEach((a) => {\r\n          const tempA: pcasItem = {\r\n            code: a.code,\r\n            name: a.name,\r\n          }\r\n          if (props.type === 'pcas') {\r\n            const tempChildrenS: pcasItem[] = []\r\n            // 街道\r\n            a.children.forEach((s) => {\r\n              const tempS: pcasItem = {\r\n                code: s.code,\r\n                name: s.name,\r\n              }\r\n              tempChildrenS.push(tempS)\r\n            })\r\n            tempA.children = tempChildrenS\r\n          }\r\n          tempChildrenA.push(tempA)\r\n        })\r\n        tempC.children = tempChildrenA\r\n      }\r\n      tempChildrenC.push(tempC)\r\n    })\r\n    tempP.children = tempChildrenC\r\n    data.push(tempP)\r\n  })\r\n  return data\r\n})\r\n\r\nconst myValue = computed({\r\n  // 将入参数据转成 code 码\r\n  get: () => {\r\n    return anyToCode(value.value)\r\n  },\r\n  // 将 code 码转成出参数据\r\n  set: (val) => {\r\n    value.value = val ? codeToAny(val) : []\r\n  },\r\n})\r\n\r\nfunction anyToCode(value: any[], dictionarie: any[] = pcasData.value) {\r\n  const input: string[] = []\r\n  if (value.length > 0) {\r\n    const findItem = dictionarie.find((item) => {\r\n      if (props.format === 'code') {\r\n        return item.code === value[0]\r\n      }\r\n      else if (props.format === 'name') {\r\n        return item.name === value[0]\r\n      }\r\n      else {\r\n        return item.name === value[0].name && item.code === value[0].code\r\n      }\r\n    })\r\n    input.push(findItem.code)\r\n    if (findItem.children) {\r\n      input.push(...anyToCode(value.slice(1 - value.length), findItem.children))\r\n    }\r\n  }\r\n  return input\r\n}\r\n\r\nfunction codeToAny(codes: string[], dictionarie: any[] = pcasData.value): any {\r\n  const output = []\r\n  const findItem = dictionarie.find(item => item.code === codes[0])\r\n  if (findItem) {\r\n    switch (props.format) {\r\n      case 'code':\r\n        output.push(findItem.code)\r\n        break\r\n      case 'name':\r\n        output.push(findItem.name)\r\n        break\r\n      case 'both':\r\n        output.push({\r\n          code: findItem.code,\r\n          name: findItem.name,\r\n        })\r\n    }\r\n    const newCodes = codes.slice(1 - codes.length)\r\n    if (newCodes.length > 0 && findItem.children) {\r\n      output.push(...codeToAny(newCodes, findItem.children))\r\n    }\r\n  }\r\n  return output\r\n}\r\n</script>\r\n\r\n<template>\r\n  <ElCascader v-model=\"myValue\" :options=\"pcasData as any[]\" :props=\"{ value: 'code', label: 'name' }\" :disabled=\"disabled\" clearable filterable />\r\n</template>\r\n"], "names": ["props", "__props", "value", "_useModel", "pcasData", "computed", "data", "pcasRaw", "for<PERSON>ach", "p", "tempP", "code", "name", "tempChildrenC", "children", "c", "tempC", "includes", "type", "tempChildrenA", "a", "tempA", "tempChildrenS", "s", "tempS", "push", "myValue", "get", "anyToCode", "set", "val", "codeToAny", "dictionarie", "input", "length", "findItem", "find", "item", "format", "slice", "codes", "output", "newCodes"], "mappings": "k801CAQA,MAAMA,EAAQC,EAaRC,EAAQC,EAGVF,EAAA,cAUEG,EAAWC,GAAS,KACxB,MAAMC,EAAmB,GA2ClB,OAzCCC,EAAAC,SAASC,IACf,MAAMC,EAAkB,CACtBC,KAAMF,EAAEE,KACRC,KAAMH,EAAEG,MAEJC,EAA4B,GAEhCJ,EAAAK,SAASN,SAASO,IAClB,MAAMC,EAAkB,CACtBL,KAAMI,EAAEJ,KACRC,KAAMG,EAAEH,MAEV,GAAI,CAAC,MAAO,QAAQK,SAASjB,EAAMkB,MAAO,CACxC,MAAMC,EAA4B,GAEhCJ,EAAAD,SAASN,SAASY,IAClB,MAAMC,EAAkB,CACtBV,KAAMS,EAAET,KACRC,KAAMQ,EAAER,MAEN,GAAe,SAAfZ,EAAMkB,KAAiB,CACzB,MAAMI,EAA4B,GAEhCF,EAAAN,SAASN,SAASe,IAClB,MAAMC,EAAkB,CACtBb,KAAMY,EAAEZ,KACRC,KAAMW,EAAEX,MAEVU,EAAcG,KAAKD,EAAK,IAE1BH,EAAMP,SAAWQ,CAAA,CAEnBH,EAAcM,KAAKJ,EAAK,IAE1BL,EAAMF,SAAWK,CAAA,CAEnBN,EAAcY,KAAKT,EAAK,IAE1BN,EAAMI,SAAWD,EACjBP,EAAKmB,KAAKf,EAAK,IAEVJ,CAAA,IAGHoB,EAAUrB,EAAS,CAEvBsB,IAAK,IACIC,EAAU1B,EAAMA,OAGzB2B,IAAMC,IACJ5B,EAAMA,MAAQ4B,EAAMC,EAAUD,GAAO,EAAC,IAI1C,SAASF,EAAU1B,EAAc8B,EAAqB5B,EAASF,OAC7D,MAAM+B,EAAkB,GACpB/B,GAAAA,EAAMgC,OAAS,EAAG,CACpB,MAAMC,EAAWH,EAAYI,MAAMC,GACZ,SAAjBrC,EAAMsC,OACDD,EAAK1B,OAAST,EAAM,GAEH,SAAjBF,EAAMsC,OACND,EAAKzB,OAASV,EAAM,GAGpBmC,EAAKzB,OAASV,EAAM,GAAGU,MAAQyB,EAAK1B,OAAST,EAAM,GAAGS,OAG3DsB,EAAAR,KAAKU,EAASxB,MAChBwB,EAASrB,UACLmB,EAAAR,QAAQG,EAAU1B,EAAMqC,MAAM,EAAIrC,EAAMgC,QAASC,EAASrB,UAClE,CAEK,OAAAmB,CAAA,CAGT,SAASF,EAAUS,EAAiBR,EAAqB5B,EAASF,OAChE,MAAMuC,EAAS,GACTN,EAAWH,EAAYI,MAAKC,GAAQA,EAAK1B,OAAS6B,EAAM,KAC9D,GAAIL,EAAU,CACZ,OAAQnC,EAAMsC,QACZ,IAAK,OACIG,EAAAhB,KAAKU,EAASxB,MACrB,MACF,IAAK,OACI8B,EAAAhB,KAAKU,EAASvB,MACrB,MACF,IAAK,OACH6B,EAAOhB,KAAK,CACVd,KAAMwB,EAASxB,KACfC,KAAMuB,EAASvB,OAGrB,MAAM8B,EAAWF,EAAMD,MAAM,EAAIC,EAAMN,QACnCQ,EAASR,OAAS,GAAKC,EAASrB,UAClC2B,EAAOhB,QAAQM,EAAUW,EAAUP,EAASrB,UAC9C,CAEK,OAAA2B,CAAA"}