{"version": 3, "file": "arrangeRooms-DLQ6Ij2m.js", "sources": ["../../src/views/order/info/components/orderdetail/arrangeRooms.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"roomType\": \"Type\",\r\n    \"roomState\": \"Status\",\r\n    \"other\": \"Other\",\r\n    \"preOrderedRoom\": \"Pre-ordered Rooms\",\r\n    \"roomAllocated\": \"Room Allocated\",\r\n    \"cancel\": \"Cancel\",\r\n    \"confirm\": \"Confirm\",\r\n    \"getRoomTypesFailed\": \"Failed to get room type list\",\r\n    \"maxSelectRooms\": \"Max select up to {roomNum} rooms\",\r\n    \"selectAtLeastOneRoom\": \"At least one room must be selected\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"roomType\": \"房型\",\r\n    \"roomState\": \"房态\",\r\n    \"other\": \"其他\",\r\n    \"preOrderedRoom\": \"预订单占用房间\",\r\n    \"roomAllocated\": \"已排房\",\r\n    \"cancel\": \"取消\",\r\n    \"confirm\": \"确定\",\r\n    \"getRoomTypesFailed\": \"获取房型列表失败\",\r\n    \"maxSelectRooms\": \"最多选择{roomNum}间房间\",\r\n    \"selectAtLeastOneRoom\": \"至少选中一间房\",\r\n    \"rooms\": \"房间\"\r\n  },\r\n  \"km\": {\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"roomState\": \"ស្ថានភាពបន្ទប់\",\r\n    \"other\": \"ផ្សេងៗ\",\r\n    \"preOrderedRoom\": \"បន្ទប់ដែលបានកក់ទុកជាមុន\",\r\n    \"roomAllocated\": \"បន្ទប់ដែលបានចែកចាយ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"confirm\": \"បញ្ជាក់\",\r\n    \"getRoomTypesFailed\": \"បរាជ័យក្នុងការទទួលបានបញ្ជីប្រភេទបន្ទប់\",\r\n    \"maxSelectRooms\": \"អាចជ្រើសរើសបានអតិបរមា {roomNum} បន្ទប់\",\r\n    \"selectAtLeastOneRoom\": \"ត្រូវជ្រើសរើសយ៉ាងហោចណាស់មួយបន្ទប់\",\r\n    \"rooms\": \"បន្ទប់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { bookApi, dictDataApi, rtApi } from '@/api/modules/index'\r\nimport { CheckinType, DICT_TYPE_ROOM_STATUS, RoomState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    /** 预订单号 */\r\n    bookNo: string\r\n    /** 批次号 */\r\n    batchNo: string\r\n    /** 订单号 */\r\n    orderNo: string\r\n    /** 房间类型 */\r\n    rtCode: string\r\n    /** 房间类型名称 */\r\n    rtName: string\r\n    /** 房间号列表 */\r\n    rNos: string[]\r\n    /** 预抵时间 */\r\n    planCheckinTime: string\r\n    /** 预离时间 */\r\n    planCheckoutTime: string\r\n    /** 预订占用房间 */\r\n    isBookedRoom: string\r\n    checkinType: string\r\n    hourCode: string\r\n    guestSrcTypeName: string\r\n    guestSrcType: string\r\n    guestCode: string\r\n    channelCode: string\r\n    orderSource: string\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    bookNo: '',\r\n    batchNo: '',\r\n    orderNo: '',\r\n    rtCode: '',\r\n    rtName: '',\r\n    rNos: () => [],\r\n    planCheckinTime: '',\r\n    planCheckoutTime: '',\r\n    isBookedRoom: '',\r\n    checkinType: '',\r\n    hourCode: '',\r\n    guestSrcTypeName: '',\r\n    guestSrcType: '',\r\n    guestCode: '',\r\n    channelCode: '',\r\n    orderSource: '',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n  selected: [data: any]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst data = ref({\r\n  rtCode: props.rtCode,\r\n  rtName: props.rtName,\r\n  /** 房间状态 VC 空净  VD 空脏  OC 住净 OD 住脏  OO维修 */\r\n  state: 'VC',\r\n  /** 预订单占用房间 0:否 1:是 */\r\n  isBookedRoom: '0',\r\n  selectRooms: props.rNos,\r\n  dayPrices: [] as { date: string; price: string }[],\r\n  planCheckinTime: props.planCheckinTime,\r\n  planCheckoutTime: props.planCheckoutTime,\r\n  roomNum: 1,\r\n})\r\nconst roomNum = ref<number>(1)\r\n/** 房间列表 */\r\nconst roomPrices = ref<{ rNo: string; rCode: string }[]>([])\r\nconst roomAll = ref<{ rNo: string; rCode: string; state: string }[]>([])\r\n\r\nonMounted(async () => {\r\n  getConstants()\r\n  if (props.orderNo === '') {\r\n    getRts()\r\n  } else {\r\n    getRooms()\r\n  }\r\n})\r\n/** 房型列表 */\r\nconst rts = ref<{ rtCode: string; rtName: string; price: string | number }[]>([])\r\nasync function getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    delayMinute: 0,\r\n    channelCode: props.channelCode,\r\n    orderSource: props.orderSource,\r\n    planCheckinTime: props.planCheckinTime,\r\n    planCheckoutTime: props.planCheckoutTime,\r\n    checkinType: props.checkinType,\r\n    hourCode: props.checkinType === CheckinType.HOUR_ROOM ? props.hourCode : null,\r\n    guestSrcTypeName: props.guestSrcTypeName,\r\n    guestSrcType: props.guestSrcType,\r\n    guestCode: props.guestCode,\r\n  }\r\n  await bookApi.roomtypeList(params).then((res: any) => {\r\n    if (res.code !== 0) {\r\n      ElMessage.error(t('getRoomTypesFailed'))\r\n      return\r\n    }\r\n    rts.value = res.data\r\n    if (rts.value.length > 0 && props.orderNo === '' && props.rtCode === '') {\r\n      data.value.rtCode = rts.value[0].rtCode\r\n      data.value.rtName = rts.value[0].rtName\r\n    }\r\n    if (props.isBookedRoom === '1') {\r\n      data.value.state = ''\r\n      data.value.isBookedRoom = props.isBookedRoom\r\n    }\r\n    getRooms()\r\n  })\r\n}\r\n// 通用字典\r\nconst dictTypes = [DICT_TYPE_ROOM_STATUS]\r\n/** 房间状态 */\r\nconst roomStates = ref<{ code: string; label: string }[]>([])\r\n\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    roomStates.value = res.data.filter((item: any) => item.code !== 'OO')\r\n  })\r\n}\r\nfunction getRoomChange() {\r\n  if (data.value.state) {\r\n    data.value.isBookedRoom = '0'\r\n  }\r\n  roomPrices.value = roomAll.value.filter((item: { state: string }) => {\r\n    return item.state === data.value.state\r\n  })\r\n}\r\nasync function getRooms() {\r\n  loading.value = true\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    rtCode: data.value.rtCode,\r\n    state: data.value.state,\r\n    planCheckinTime: dayjs(props.planCheckinTime).format('YYYY-MM-DD HH:mm'),\r\n    planCheckoutTime: dayjs(props.planCheckoutTime).format('YYYY-MM-DD HH:mm'),\r\n    isMeetingRoom: '0',\r\n    preOccupied: data.value.isBookedRoom,\r\n  }\r\n  await bookApi.canBookRoomList(params).then((res: any) => {\r\n    loading.value = false\r\n    if (res.code !== 0) {\r\n      ElMessage.error(t('getRoomTypesFailed'))\r\n      return\r\n    }\r\n    roomAll.value = res.data\r\n    roomPrices.value = res.data.filter((item: { state: string }) => {\r\n      return item.state === data.value.state\r\n    })\r\n  })\r\n}\r\n\r\nfunction handleClose(tag: string) {\r\n  data.value.selectRooms.splice(data.value.selectRooms.indexOf(tag), 1)\r\n}\r\n\r\nfunction doCheck() {\r\n  if (roomNum.value > 0 && data.value.selectRooms.length > roomNum.value) {\r\n    ElMessage.warning(t('maxSelectRooms', { roomNum: roomNum.value }))\r\n    data.value.selectRooms = data.value.selectRooms.slice(0, roomNum.value)\r\n    return false\r\n  } else {\r\n    return true\r\n  }\r\n}\r\n\r\nfunction bookedRoomChange() {\r\n  if (data.value.isBookedRoom === '1') {\r\n    data.value.state = '' as RoomState\r\n  } else {\r\n    data.value.state = RoomState.VC\r\n  }\r\n  getRooms()\r\n}\r\n\r\nfunction selectRoomType(rtCode: string) {\r\n  // 如果点击的是已选中的房型，不执行任何操作\r\n  if (data.value.rtCode === rtCode) {\r\n    return\r\n  }\r\n  data.value.rtCode = rtCode\r\n  getRooms()\r\n}\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nfunction onSubmit() {\r\n  if (props.orderNo) {\r\n    let list = []\r\n    list = roomPrices.value.filter((person) => data.value.selectRooms.includes(person.rNo))\r\n    const params = {\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      bookNo: props.bookNo,\r\n      batchNo: props.batchNo,\r\n      rooms: [\r\n        {\r\n          rCode: list.length ? list[0].rCode : '',\r\n          rNo: list.length ? list[0].rNo : '',\r\n          orderNo: props.orderNo,\r\n        },\r\n      ],\r\n    }\r\n    bookApi.arrangeBook(params).then((res: any) => {\r\n      if (res.code === 0) {\r\n        emits('success')\r\n        onCancel()\r\n      } else {\r\n        ElMessage.error({\r\n          message: res.msg,\r\n          center: true,\r\n        })\r\n      }\r\n    })\r\n  } else {\r\n    let list = []\r\n    let roomList = []\r\n    list = roomPrices.value.filter((person) => data.value.selectRooms.includes(person.rNo))\r\n    roomList = rts.value.filter((item: any) => item.rtCode === data.value.rtCode)\r\n    if (list.length > 0) {\r\n      const params = {\r\n        rtName: roomList[0].rtName,\r\n        rtCode: roomList[0].rtCode,\r\n        rCode: list.length ? list[0].rCode : '',\r\n        rNo: list.length ? list[0].rNo : '',\r\n        isBookedRoom: data.value.isBookedRoom === '1' ? data.value.isBookedRoom : '',\r\n      }\r\n      emits('selected', params)\r\n      onCancel()\r\n    } else {\r\n      ElMessage.error({\r\n        message: t('selectAtLeastOneRoom'),\r\n        center: true,\r\n      })\r\n    }\r\n  }\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" :title=\"t('roomAllocated')\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <el-form size=\"default\" label-width=\"50px\" inline-message inline class=\"search-form\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('roomType')\" label-width=\"80px\">\r\n              <div class=\"room-type-buttons\">\r\n                <el-button v-if=\"props.orderNo\" type=\"primary\" size=\"small\">\r\n                  {{ props.rtName }}\r\n                </el-button>\r\n                <el-button\r\n                  v-else\r\n                  v-for=\"item in rts\"\r\n                  :key=\"item.rtCode\"\r\n                  size=\"small\"\r\n                  :type=\"data.rtCode === item.rtCode ? 'primary' : 'default'\"\r\n                  @click=\"selectRoomType(item.rtCode)\"\r\n                >\r\n                  {{ item.rtName }}\r\n                </el-button>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('roomState')\" label-width=\"80px\">\r\n              <el-radio-group v-model=\"data.state\" size=\"small\" @change=\"getRoomChange\">\r\n                <el-radio-button v-for=\"item in roomStates\" :key=\"item.code\" :value=\"item.code\" border>\r\n                  {{ item.label }}\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item :label=\"t('other')\" label-width=\"80px\">\r\n              <el-checkbox v-model=\"data.isBookedRoom\" true-value=\"1\" false-value=\"0\" size=\"small\" :label=\"t('preOrderedRoom')\" border @change=\"bookedRoomChange()\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div class=\"special_td\">\r\n        <div style=\"margin-bottom: 15px\">\r\n          <span v-if=\"roomNum > 0\">{{ t('roomAllocated') }}: {{ data.selectRooms.length }} / {{ roomNum }} {{ t('rooms') }}</span>\r\n          <span v-else>{{ t('roomAllocated') }}: {{ data.selectRooms.length }}{{ t('rooms') }}</span>\r\n        </div>\r\n        <el-tag v-for=\"item in data.selectRooms\" :key=\"item\" class=\"roomtag\" type=\"danger\" closable @close=\"handleClose(item)\">\r\n          {{ item }}\r\n        </el-tag>\r\n      </div>\r\n      <div class=\"roomList\">\r\n        <div class=\"flexBox\">\r\n          <ul style=\"padding-left: 0; margin-bottom: 0; list-style: none\">\r\n            <el-checkbox-group v-model=\"data.selectRooms\">\r\n              <li v-for=\"item in roomPrices\" :key=\"item.rCode\" class=\"xxx\">\r\n                {{ item }}\r\n                <el-checkbox v-model=\"item.rNo\" :value=\"item.rNo\" border @change=\"doCheck\">\r\n                  {{ item.rNo }}\r\n                </el-checkbox>\r\n              </li>\r\n            </el-checkbox-group>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          {{ t('confirm') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.special_td {\r\n  padding: 10px;\r\n  line-height: 20px;\r\n  color: #000;\r\n  background: #f7f7f7;\r\n  border-radius: 3px;\r\n}\r\n\r\n.xxx {\r\n  position: relative;\r\n  float: left;\r\n  padding: 3px;\r\n  margin-bottom: 5px;\r\n  cursor: pointer;\r\n}\r\n\r\n.flexBox {\r\n  display: flex;\r\n  flex-flow: row wrap;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.roomList {\r\n  height: 260px;\r\n  padding: 5px;\r\n  margin-top: 10px;\r\n  overflow: auto;\r\n  background-color: #f7f7f7;\r\n}\r\n\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n/* 表单标签和子元素统一字体大小 */\r\n.search-form {\r\n  :deep(.el-form-item__label) {\r\n    font-size: 14px !important;\r\n  }\r\n\r\n  :deep(.el-radio-button__inner) {\r\n    font-size: 14px !important;\r\n    height: 30px !important;\r\n    line-height: 30px !important;\r\n    display: flex !important;\r\n    align-items: center !important;\r\n    justify-content: center !important;\r\n  }\r\n\r\n  :deep(.el-checkbox__label) {\r\n    font-size: 14px !important;\r\n  }\r\n\r\n  :deep(.el-checkbox) {\r\n    height: 30px !important;\r\n    display: flex !important;\r\n    align-items: center !important;\r\n  }\r\n\r\n  :deep(.el-checkbox__input) {\r\n    display: flex !important;\r\n    align-items: center !important;\r\n  }\r\n}\r\n\r\n/* 房型按钮样式 */\r\n.room-type-buttons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px 4px;\r\n\r\n  .el-button {\r\n    margin: 0;\r\n    font-size: 14px !important;\r\n    height: 30px !important;\r\n\r\n    &.el-button--primary {\r\n      &:hover {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        transform: none !important;\r\n        box-shadow: none !important;\r\n      }\r\n\r\n      &:focus {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        box-shadow: none !important;\r\n      }\r\n\r\n      &:active {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        transform: none !important;\r\n      }\r\n\r\n      &:focus-visible {\r\n        background-color: var(--el-button-bg-color) !important;\r\n        border-color: var(--el-button-border-color) !important;\r\n        color: var(--el-button-text-color) !important;\r\n        box-shadow: none !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 其他文字元素统一字体大小 */\r\n.special_td {\r\n  font-size: 14px !important;\r\n}\r\n\r\n.roomtag {\r\n  margin-right: 5px;\r\n  margin-bottom: 8px;\r\n  font-size: 14px !important;\r\n  height: 30px !important;\r\n  line-height: 30px !important;\r\n  display: inline-flex !important;\r\n  align-items: center !important;\r\n}\r\n\r\n.roomList {\r\n  font-size: 14px !important;\r\n\r\n  .el-checkbox {\r\n    font-size: 14px !important;\r\n  }\r\n\r\n  .el-checkbox__label {\r\n    font-size: 14px !important;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "data", "rtCode", "rtName", "state", "isBookedRoom", "selectRooms", "rNos", "dayPrices", "planCheckinTime", "planCheckoutTime", "roomNum", "roomPrices", "roomAll", "onMounted", "async", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "then", "res", "roomStates", "value", "filter", "item", "code", "orderNo", "params", "gcode", "hcode", "delayMinute", "channelCode", "orderSource", "checkinType", "hourCode", "CheckinType", "HOUR_ROOM", "guestSrcTypeName", "guestSrcType", "guest<PERSON><PERSON>", "bookApi", "roomtypeList", "rts", "length", "getRooms", "ElMessage", "error", "getRts", "DICT_TYPE_ROOM_STATUS", "getRoomChange", "dayjs", "format", "isMeetingRoom", "preOccupied", "canBookRoomList", "do<PERSON><PERSON><PERSON>", "warning", "slice", "myVisible", "computed", "get", "modelValue", "set", "val", "onSubmit", "list", "person", "includes", "rNo", "bookNo", "batchNo", "rooms", "rCode", "arrangeBook", "onCancel", "message", "msg", "center", "roomList", "RoomState", "VC", "tag", "splice", "indexOf"], "mappings": "2kDAiDA,MAAMA,EAAQC,EAiDRC,EAAQC,GAKRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAOD,EAAI,CACfE,OAAQX,EAAMW,OACdC,OAAQZ,EAAMY,OAEdC,MAAO,KAEPC,aAAc,IACdC,YAAaf,EAAMgB,KACnBC,UAAW,GACXC,gBAAiBlB,EAAMkB,gBACvBC,iBAAkBnB,EAAMmB,iBACxBC,QAAS,IAELA,EAAUX,EAAY,GAEtBY,EAAaZ,EAAsC,IACnDa,EAAUb,EAAqD,IAErEc,GAAUC,UAgDRC,EAAYC,iBAAiBC,IAAWC,MAAMC,IACjCC,GAAAC,MAAQF,EAAInB,KAAKsB,QAAQC,GAA4B,OAAdA,EAAKC,MAAa,IA/ChD,KAAlBlC,EAAMmC,QAQZX,iBACE,MAAMY,EAAS,CACbC,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjBC,YAAa,EACbC,YAAaxC,EAAMwC,YACnBC,YAAazC,EAAMyC,YACnBvB,gBAAiBlB,EAAMkB,gBACvBC,iBAAkBnB,EAAMmB,iBACxBuB,YAAa1C,EAAM0C,YACnBC,SAAU3C,EAAM0C,cAAgBE,EAAYC,UAAY7C,EAAM2C,SAAW,KACzEG,iBAAkB9C,EAAM8C,iBACxBC,aAAc/C,EAAM+C,aACpBC,UAAWhD,EAAMgD,iBAEbC,EAAQC,aAAad,GAAQR,MAAMC,IACtB,IAAbA,EAAIK,MAIRiB,GAAIpB,MAAQF,EAAInB,KACZyC,GAAIpB,MAAMqB,OAAS,GAAuB,KAAlBpD,EAAMmC,SAAmC,KAAjBnC,EAAMW,SACxDD,EAAKqB,MAAMpB,OAASwC,GAAIpB,MAAM,GAAGpB,OACjCD,EAAKqB,MAAMnB,OAASuC,GAAIpB,MAAM,GAAGnB,QAER,MAAvBZ,EAAMc,eACRJ,EAAKqB,MAAMlB,MAAQ,GACdH,EAAAqB,MAAMjB,aAAed,EAAMc,cAEzBuC,MAZGC,EAAAC,MAAMnD,EAAE,sBAYX,GACV,CArCQoD,GAEEH,IAAA,IAIP,MAAAF,GAAM1C,EAAkE,IAkCxE,MAAAkB,GAAY,CAAC8B,GAEb3B,GAAarB,EAAuC,IAO1D,SAASiD,KACHhD,EAAKqB,MAAMlB,QACbH,EAAKqB,MAAMjB,aAAe,KAE5BO,EAAWU,MAAQT,EAAQS,MAAMC,QAAQC,GAChCA,EAAKpB,QAAUH,EAAKqB,MAAMlB,OAClC,CAEHW,eAAe6B,KACb7C,EAAQuB,OAAQ,EAChB,MAAMK,EAAS,CACbC,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjB3B,OAAQD,EAAKqB,MAAMpB,OACnBE,MAAOH,EAAKqB,MAAMlB,MAClBK,gBAAiByC,EAAM3D,EAAMkB,iBAAiB0C,OAAO,oBACrDzC,iBAAkBwC,EAAM3D,EAAMmB,kBAAkByC,OAAO,oBACvDC,cAAe,IACfC,YAAapD,EAAKqB,MAAMjB,oBAEpBmC,EAAQc,gBAAgB3B,GAAQR,MAAMC,IAC1CrB,EAAQuB,OAAQ,EACC,IAAbF,EAAIK,MAIRZ,EAAQS,MAAQF,EAAInB,KACpBW,EAAWU,MAAQF,EAAInB,KAAKsB,QAAQC,GAC3BA,EAAKpB,QAAUH,EAAKqB,MAAMlB,SALvByC,EAAAC,MAAMnD,EAAE,sBAMnB,GACF,CAOH,SAAS4D,KACH,QAAA5C,EAAQW,MAAQ,GAAKrB,EAAKqB,MAAMhB,YAAYqC,OAAShC,EAAQW,SACrDuB,EAAAW,QAAQ7D,EAAE,iBAAkB,CAAEgB,QAASA,EAAQW,SACpDrB,EAAAqB,MAAMhB,YAAcL,EAAKqB,MAAMhB,YAAYmD,MAAM,EAAG9C,EAAQW,QAC1D,EAGT,CAoBF,MAAMoC,GAAYC,EAAS,CACzBC,IAAM,IACGrE,EAAMsE,WAEf,GAAAC,CAAIC,GACFtE,EAAM,oBAAqBsE,EAAG,IAGlC,SAASC,KACP,GAAIzE,EAAMmC,QAAS,CACjB,IAAIuC,EAAO,GACJA,EAAArD,EAAWU,MAAMC,QAAQ2C,GAAWjE,EAAKqB,MAAMhB,YAAY6D,SAASD,EAAOE,OAClF,MAAMzC,EAAS,CACbC,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjBwC,OAAQ9E,EAAM8E,OACdC,QAAS/E,EAAM+E,QACfC,MAAO,CACL,CACEC,MAAOP,EAAKtB,OAASsB,EAAK,GAAGO,MAAQ,GACrCJ,IAAKH,EAAKtB,OAASsB,EAAK,GAAGG,IAAM,GACjC1C,QAASnC,EAAMmC,WAIrBc,EAAQiC,YAAY9C,GAAQR,MAAMC,IACf,IAAbA,EAAIK,MACNhC,EAAM,WACGiF,MAET7B,EAAUC,MAAM,CACd6B,QAASvD,EAAIwD,IACbC,QAAQ,GACT,GAEJ,KACI,CACL,IAAIZ,EAAO,GACPa,EAAW,GAGX,GAFGb,EAAArD,EAAWU,MAAMC,QAAQ2C,GAAWjE,EAAKqB,MAAMhB,YAAY6D,SAASD,EAAOE,OACvEU,EAAApC,GAAIpB,MAAMC,QAAQC,GAAcA,EAAKtB,SAAWD,EAAKqB,MAAMpB,SAClE+D,EAAKtB,OAAS,EAAG,CACnB,MAAMhB,EAAS,CACbxB,OAAQ2E,EAAS,GAAG3E,OACpBD,OAAQ4E,EAAS,GAAG5E,OACpBsE,MAAOP,EAAKtB,OAASsB,EAAK,GAAGO,MAAQ,GACrCJ,IAAKH,EAAKtB,OAASsB,EAAK,GAAGG,IAAM,GACjC/D,aAA0C,MAA5BJ,EAAKqB,MAAMjB,aAAuBJ,EAAKqB,MAAMjB,aAAe,IAE5EZ,EAAM,WAAYkC,GACT+C,IAAA,MAET7B,EAAUC,MAAM,CACd6B,QAAShF,EAAE,wBACXkF,QAAQ,GAEZ,CACF,CAEF,SAASH,KACPhB,GAAUpC,OAAQ,CAAA,y8BApEIpB,gBAElBD,EAAKqB,MAAMpB,SAAWA,IAG1BD,EAAKqB,MAAMpB,OAASA,EACX0C,OANX,IAAwB1C,g4BARU,MAA5BD,EAAKqB,MAAMjB,aACbJ,EAAKqB,MAAMlB,MAAQ,GAEdH,EAAAqB,MAAMlB,MAAQ2E,EAAUC,QAEtBpC,wbApBUqC,SACdhF,EAAAqB,MAAMhB,YAAY4E,OAAOjF,EAAKqB,MAAMhB,YAAY6E,QAAQF,GAAM,GADrE,IAAqBA"}