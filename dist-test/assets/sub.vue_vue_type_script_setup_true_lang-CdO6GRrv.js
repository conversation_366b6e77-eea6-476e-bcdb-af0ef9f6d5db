import{d as e,ao as n,ap as t,B as l,am as i,o as u,c as s,f as a,u as o,e as r,U as d,a6 as m,at as c,w as g,aq as v,ar as f,a7 as h,g as y,F as p,ag as b,R as k,as as M,V as w,K as x,ah as q}from"./index-CkEhI1Zk.js";import{I as C,r as H}from"./item-BWmZJmeN.js";const E={class:"columns-3 p-2 2xl:columns-4"},R=e({name:"SubMenu",__name:"sub",props:{uniqueKey:{},menu:{},level:{default:0}},setup(e){const R=e,K=R.menu.path??JSON.stringify(R.menu),B=n("itemRef"),S=n("subMenuRef"),I=t(H),_=l((()=>I.openedMenus.includes(<PERSON>.uniqueKey.at(-1)))),W={enter(e){e.offsetHeight>window.innerHeight&&(e.style.height=`${window.innerHeight}px`)},afterEnter:()=>{},beforeLeave:e=>{e.style.overflow="hidden",e.style.maxHeight=`${e.offsetHeight}px`},leave:e=>{e.style.maxHeight="0"},afterLeave(e){e.style.overflow="",e.style.maxHeight=""}},J={enterActiveClass:"ease-in-out duration-300",enterFromClass:"opacity-0 translate-x-4",enterToClass:"opacity-100",leaveActiveClass:"ease-in-out duration-300",leaveFromClass:"opacity-100",leaveToClass:"opacity-0"},N=l((()=>{let e=!0;return R.menu.children?R.menu.children.every((e=>{var n;return!1===(null==(n=e.meta)?void 0:n.menu)}))&&(e=!1):e=!1,e}));function O(){N.value||I.handleMenuItemClick(K)}let $;function F(){0===R.level&&(I.mouseInMenu=R.uniqueKey,null==$||$(),({stop:$}=x((()=>{if(N.value)I.openMenu(K),q((()=>{var e;const n=null==(e=B.value)?void 0:e.ref;if(!n)return;let t=0,l=0;"vertical"===I.props.mode||0!==R.level?(t=n.getBoundingClientRect().top+n.scrollTop,l=("ltr"===I.props.direction?n.getBoundingClientRect().left:document.documentElement.clientWidth-n.getBoundingClientRect().right)+n.getBoundingClientRect().width,t+S.value.getElement().offsetHeight>window.innerHeight&&(t=window.innerHeight-S.value.getElement().offsetHeight)):(t=n.getBoundingClientRect().top+n.getBoundingClientRect().height,l="ltr"===I.props.direction?n.getBoundingClientRect().left:document.documentElement.clientWidth-n.getBoundingClientRect().right,t+S.value.getElement().offsetHeight>window.innerHeight&&(S.value.getElement().style.height=window.innerHeight-t+"px"),l+S.value.getElement().offsetWidth>document.documentElement.clientWidth&&(l=document.documentElement.clientWidth-S.value.getElement().offsetWidth)),S.value.getElement().style.top=`${t}px`,S.value.getElement().style.insetInlineStart=`${l}px`}));else{const e=R.menu.children?I.subMenus[K].indexPath.at(-1):I.items[K].indexPath.at(-1);I.openMenu(e)}}),300)))}function T(){0===R.level&&(I.mouseInMenu=[],null==$||$(),({stop:$}=x((()=>{0===I.mouseInMenu.length?I.closeMenu(R.uniqueKey):N.value&&!I.mouseInMenu.includes(R.uniqueKey.at(-1))&&I.closeMenu(R.uniqueKey.at(-1))}),300)))}function j(){null==$||$()}return(e,n)=>{const t=i("SubMenu");return u(),s(p,null,[a(C,{ref_key:"itemRef",ref:B,"unique-key":e.uniqueKey,item:e.menu,level:e.level,"sub-menu":o(N),onClick:O,onMouseenter:F,onMouseleave:T},null,8,["unique-key","item","level","sub-menu"]),o(N)?(u(),r(w,{key:0,to:"body",disabled:0!==e.level},[0===e.level?(u(),r(d,m({key:0},J,c(W)),{default:g((()=>[v(a(o(f),{ref_key:"subMenuRef",ref:S,options:{scrollbars:{visibility:"hidden"}},defer:"",class:h(["sub-menu fixed z-3000 bg-[var(--g-sub-sidebar-bg)] px-1 shadow-xl ring-1 ring-stone-2 dark-ring-stone-8",{"rounded-lg":o(I).props.rounded}]),onMouseenter:j,onMouseleave:T},{default:g((()=>[y("div",E,[(u(!0),s(p,null,b(e.menu.children,(n=>{var l;return u(),s("div",{key:n.path??JSON.stringify(n),class:"w-[200px] break-inside-avoid"},[!1!==(null==(l=n.meta)?void 0:l.menu)?(u(),r(t,{key:0,"unique-key":[...e.uniqueKey,n.path??JSON.stringify(n)],menu:n,level:e.level+1},null,8,["unique-key","menu","level"])):k("",!0)])})),128))])])),_:1},8,["class"]),[[M,o(_)]])])),_:1},16)):(u(!0),s(p,{key:1},b(e.menu.children,(n=>{var l;return u(),s(p,{key:n.path??JSON.stringify(n)},[!1!==(null==(l=n.meta)?void 0:l.menu)?(u(),r(t,{key:0,"unique-key":[...e.uniqueKey,n.path??JSON.stringify(n)],menu:n,level:e.level+1},null,8,["unique-key","menu","level"])):k("",!0)],64)})),128))],8,["disabled"])):k("",!0)],64)}}});export{R as _};
//# sourceMappingURL=sub.vue_vue_type_script_setup_true_lang-CdO6GRrv.js.map
