{"version": 3, "file": "event-bus-CDHrRX6w.js", "sources": ["../../src/views/room/realtime/event-bus.ts"], "sourcesContent": ["import { getCurrentInstance, onUnmounted } from 'vue'\r\n\r\ninterface EventBus {\r\n  on: (eventName: string, callback: (...args: any[]) => void) => void\r\n  emit: (eventName: string, ...args: any[]) => void\r\n  off: (eventName: string, callback?: (...args: any[]) => void) => void\r\n  clear: () => void\r\n}\r\n\r\nclass EventBusImpl implements EventBus {\r\n  private events: Record<string, Array<(...args: any[]) => void>> = {}\r\n\r\n  on(eventName: string, callback: (...args: any[]) => void) {\r\n    this.events[eventName] ||= []\r\n    this.events[eventName].push(callback)\r\n\r\n    // 如果在组件中调用，自动注册清理\r\n    const instance = getCurrentInstance()\r\n    if (instance) {\r\n      onUnmounted(() => {\r\n        this.off(eventName, callback)\r\n      })\r\n    }\r\n  }\r\n\r\n  emit(eventName: string, ...args: any[]) {\r\n    const callbacks = this.events[eventName]\r\n    if (callbacks) {\r\n      for (const cb of callbacks) {\r\n        cb(...args)\r\n      }\r\n    }\r\n  }\r\n\r\n  off(eventName: string, callback?: (...args: any[]) => void) {\r\n    const callbacks = this.events[eventName]\r\n    if (callbacks) {\r\n      if (callback) {\r\n        this.events[eventName] = callbacks.filter((cb) => cb !== callback)\r\n      } else {\r\n        delete this.events[eventName]\r\n      }\r\n    }\r\n  }\r\n\r\n  clear() {\r\n    this.events = {}\r\n  }\r\n}\r\n\r\nconst eventBus = new EventBusImpl()\r\n\r\nexport default eventBus\r\n"], "names": ["eventBus", "constructor", "__publicField", "this", "on", "eventName", "callback", "_a", "events", "push", "getCurrentInstance", "onUnmounted", "off", "emit", "args", "callbacks", "cb", "filter", "clear"], "mappings": "0MAkDM,MAAAA,EAAW,IAzCjB,MAAA,WAAAC,GACUC,EAAAC,KAAA,SAA0D,CAAC,EAAA,CAEnE,EAAAC,CAAGC,EAAmBC,UACfC,EAAAJ,KAAAK,QAAAH,UAAsB,IAC3BF,KAAKK,OAAOH,GAAWI,KAAKH,GAGXI,KAEfC,GAAY,KACLR,KAAAS,IAAIP,EAAWC,EAAQ,GAEhC,CAGF,IAAAO,CAAKR,KAAsBS,GACnB,MAAAC,EAAYZ,KAAKK,OAAOH,GAC9B,GAAIU,EACF,IAAA,MAAWC,KAAMD,EACfC,KAAMF,EAEV,CAGF,GAAAF,CAAIP,EAAmBC,GACf,MAAAS,EAAYZ,KAAKK,OAAOH,GAC1BU,IACET,EACGH,KAAAK,OAAOH,GAAaU,EAAUE,QAAQD,GAAOA,IAAOV,WAElDH,KAAKK,OAAOH,GAEvB,CAGF,KAAAa,GACEf,KAAKK,OAAS,CAAC,CAAA"}