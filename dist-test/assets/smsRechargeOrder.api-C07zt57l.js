import{a as t}from"./index-CkEhI1Zk.js";const e={list:e=>t.get("marketing/sms/order/list",{params:e}),create:e=>t.post("marketing/sms/order/create",e,{}),edit:e=>t.post("marketing/sms/order/edit",e,{}),pageList:e=>t.post("admin-api/marketing/sms-send-record/pageList",e),statistics:e=>t.post("admin-api/marketing/sms-send-record/statistics",e),templatePageList:e=>t.post("admin-api/marketing/sms-default-template/page",e),templateEnabled:e=>t.put("admin-api/marketing/sms-send-record/pageList",e),templateUpdate:e=>t.put("admin-api/marketing/sms-default-template/update-enabled",e)};export{e as s};
//# sourceMappingURL=smsRechargeOrder.api-C07zt57l.js.map
