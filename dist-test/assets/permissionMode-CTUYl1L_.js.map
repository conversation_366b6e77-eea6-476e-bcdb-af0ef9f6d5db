{"version": 3, "file": "permissionMode-CTUYl1L_.js", "sources": ["../../src/views/group/org/role/components/permissionMode.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"zh-cn\": {\r\n    \"configPermissions\": \"配置权限\",\r\n    \"copyPermissions\": \"复制权限\",\r\n    \"roleName\": \"角色名称\",\r\n    \"remark\": \"备注\",\r\n    \"pleaseEnterRoleName\": \"请输入角色名称\",\r\n    \"pleaseEnterRemark\": \"请输入备注\",\r\n    \"editSuccess\": \"编辑成功\",\r\n    \"copySuccess\": \"复制成功\",\r\n    \"module\": \"模块\",\r\n    \"permissions\": \"权限\",\r\n    \"cancle\": \"取消\",\r\n    \"save\": \"确定\"\r\n  },\r\n  \"en\": {\r\n    \"configPermissions\": \"Configure Permissions\",\r\n    \"copyPermissions\": \"Copy Permissions\",\r\n    \"roleName\": \"Role Name\",\r\n    \"remark\": \"Remark\",\r\n    \"pleaseEnterRoleName\": \"Please enter role name\",\r\n    \"pleaseEnterRemark\": \"Please enter remark\",\r\n    \"editSuccess\": \"Edit successful\",\r\n    \"copySuccess\": \"Copy successful\",\r\n    \"module\": \"Module\",\r\n    \"permissions\": \"Permissions\",\r\n    \"cancle\": \"Cancel\",\r\n    \"save\": \"Save\"\r\n  },\r\n  \"km\": {\r\n    \"configPermissions\": \"កំណត់រចនាសម្ព័ន្ធសិទ្ធិ\",\r\n    \"copyPermissions\": \"ចម្លងសិទ្ធិ\",\r\n    \"roleName\": \"ឈ្មោះតួនាទី\",\r\n    \"remark\": \"កំណត់សម្គាល់\",\r\n    \"pleaseEnterRoleName\": \"សូមបញ្ចូលឈ្មោះតួនាទី\",\r\n    \"pleaseEnterRemark\": \"សូមបញ្ចូលកំណត់សម្គាល់\",\r\n    \"editSuccess\": \"កែប្រែដោយជោគជ័យ\",\r\n    \"copySuccess\": \"ចម្លងដោយជោគជ័យ\",\r\n    \"module\": \"ម៉ូឌុល\",\r\n    \"permissions\": \"សិទ្ធិ\",\r\n    \"cancle\": \"បោះបង់\",\r\n    \"save\": \"បញ្ជាក់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport userApi from '@/api/modules/user.api'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    roleId: number\r\n    name: string\r\n    type: string\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    roleId: 0,\r\n    name: '',\r\n    type: '',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  name: '',\r\n  remark: '',\r\n  code: '',\r\n  sort: 0,\r\n})\r\nconst formRules = ref<FormRules>({\r\n  name: [{ required: true, message: t('pleaseEnterRoleName'), trigger: 'blur' }],\r\n})\r\n// 数据扁平处理\r\nconst flatData = ref<any>([])\r\nconst active = ref('')\r\nconst PermissionTreeLIst = ref<any>([])\r\nconst tableData = ref([])\r\nasync function handleCheckAllChange(val: any) {\r\n  if (val.authData) {\r\n    val.checked = val.checkAll ? val.authData.map((item: any) => item.id) : []\r\n  }\r\n  val.isIndeterminate = false\r\n  if (val.children) {\r\n    await setChildren(val.children, val.checkAll)\r\n  }\r\n  setFather(tableData.value)\r\n}\r\nfunction handleCheckedCitiesChange(value: any) {\r\n  setFather(tableData.value)\r\n}\r\nfunction setChildren(children: any, state: boolean) {\r\n  children.forEach(async (item: any) => {\r\n    if (item.children && item.children.length > 0) {\r\n      await setChildren(item.children, state)\r\n    }\r\n    item.checkAll = state\r\n    item.isIndeterminate = false\r\n    if (item.authData) {\r\n      item.checked = item.checkAll ? item.authData.map((item: any) => item.id) : []\r\n    }\r\n  })\r\n}\r\n\r\nfunction setFather(list: any) {\r\n  list.forEach((item: any) => {\r\n    if (item.children && Array.isArray(item.children) && item.children.length > 0) {\r\n      // 递归调用，更新子节点的父节点状态\r\n      setFather(item.children)\r\n    }\r\n\r\n    if ([1, 2].includes(item.type)) {\r\n      if (item.authData) {\r\n        // 判断子权限的选中情况\r\n        item.checkAll = item.checked.length === item.authData.length\r\n        item.isIndeterminate = item.checked.length > 0 && item.checked.length < item.authData.length\r\n      } else if (Array.isArray(item.children) && item.children.length > 0) {\r\n        // 判断子节点的选中情况\r\n        const allChecked = item.children.every((child: any) => child.checkAll)\r\n        const someChecked = item.children.some((child: any) => child.isIndeterminate || child.checkAll)\r\n\r\n        item.checkAll = allChecked\r\n        item.isIndeterminate = !allChecked && someChecked\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\nfunction onClose() {\r\n  myVisible.value = false\r\n}\r\n\r\nconst loading = ref(false)\r\nfunction onSubmit() {\r\n  // 数据扁平处理\r\n  flatData.value = []\r\n\r\n  // 处理所有权限树的状态，包括未展示的 Tab\r\n  PermissionTreeLIst.value.forEach((tab: any) => {\r\n    if (tab.children) {\r\n      setFather(tab.children) // 确保所有 Tab 数据的状态正确\r\n      getFlatData(tab.children) // 扁平化数据\r\n    }\r\n  })\r\n\r\n  if (props.type === '0') {\r\n    userApi.updatePermission({ roleId: props.roleId, menuIds: flatData.value }).then(() => {\r\n      ElMessage.success(t('editSuccess'))\r\n      onClose()\r\n      emits('success')\r\n    })\r\n  } else if (props.type === '1') {\r\n    formRef.value &&\r\n      formRef.value.validate((valid) => {\r\n        if (valid) {\r\n          loading.value = true\r\n          userApi.createRole({ ...form.value, gcode: userStore.gcode }).then((res: any) => {\r\n            userApi.updatePermission({ roleId: res.data, menuIds: flatData.value }).then(() => {\r\n              loading.value = false\r\n              ElMessage.success(t('copySuccess'))\r\n              onClose()\r\n              emits('success')\r\n            })\r\n          })\r\n        }\r\n      })\r\n  }\r\n}\r\n\r\nfunction getFlatData(value: any) {\r\n  value.forEach((item: any) => {\r\n    if (item.children && item.children.length > 0) {\r\n      getFlatData(item.children)\r\n    }\r\n    if (item.authData && (item.checked.length > 0 || item.isIndeterminate)) {\r\n      // 如果当前项有子权限被选中，或者其本身被选中，加入该项的 id\r\n      flatData.value = [...new Set([...flatData.value, ...item.checked, item.id])]\r\n    } else if (item.children && (item.children.some((v: { checkAll: boolean }) => v.checkAll) || item.children.some((v: { isIndeterminate: boolean }) => v.isIndeterminate))) {\r\n      // 如果当前项的子项被部分或全部选中，也需要包含当前项的 id\r\n      flatData.value = [...new Set([...flatData.value, item.id])]\r\n    }\r\n  })\r\n}\r\n\r\nfunction handleClick() {\r\n  // 保存当前 Tab 的状态\r\n  const currentTab = PermissionTreeLIst.value.find((item: any) => item.id === active.value)\r\n  if (currentTab) {\r\n    setFather(currentTab.children)\r\n  }\r\n\r\n  // 切换到新 Tab\r\n  const newTab = PermissionTreeLIst.value.find((item: any) => item.id === active.value)\r\n  if (newTab) {\r\n    tableData.value = newTab.children\r\n    setFather(tableData.value)\r\n  }\r\n}\r\n\r\n// 获取权限树\r\nasync function getPermissionTree() {\r\n  await userApi.permissionTree({ status: '0', roleId: props.roleId }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      PermissionTreeLIst.value = res.data\r\n      handleTree(PermissionTreeLIst.value)\r\n\r\n      // 遍历所有 Tabs 并更新父节点状态\r\n      PermissionTreeLIst.value.forEach((tab: any) => {\r\n        if (tab.children) {\r\n          setFather(tab.children) // 确保每个 Tab 中的树都更新状态\r\n        }\r\n      })\r\n\r\n      active.value = PermissionTreeLIst.value[0].id\r\n      tableData.value = PermissionTreeLIst.value[0].children\r\n    }\r\n  })\r\n}\r\n// 处理树\r\nfunction handleTree(list: any) {\r\n  list.forEach((item: any) => {\r\n    if (item.children && item.checked) {\r\n      item.authData = item.children\r\n      item.children = null\r\n    }\r\n    if (item.children && item.children.length > 0) {\r\n      handleTree(item.children)\r\n    }\r\n  })\r\n}\r\nonMounted(() => {\r\n  getPermissionTree()\r\n  if (props.type === '0') {\r\n    form.value.name = props.name\r\n  }\r\n})\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"props.type === '0' ? t('configPermissions') : t('copyPermissions')\" width=\"70vw\" align-center>\r\n    <div>\r\n      <el-form ref=\"formRef\" :inline=\"false\" :model=\"form\" :rules=\"formRules\" label-width=\"100px\" :inline-message=\"true\">\r\n        <el-form-item :label=\"t('roleName')\" prop=\"name\" style=\"width: 50%\">\r\n          <el-input v-model=\"form.name\" :disabled=\"props.type === '0'\" :placeholder=\"t('pleaseEnterRoleName')\" />\r\n        </el-form-item>\r\n        <el-form-item v-if=\"props.type === '1'\" :label=\"t('remark')\" style=\"width: 50%\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" maxlength=\"250\" :placeholder=\"t('pleaseEnterRemark')\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-tabs v-model=\"active\" class=\"demo-tabs\" @tab-change=\"handleClick\">\r\n        <el-tab-pane v-for=\"item in PermissionTreeLIst\" :key=\"item.id\" :label=\"item.name\" :name=\"item.id\" />\r\n      </el-tabs>\r\n      <el-table :data=\"tableData\" style=\"width: 100%; margin-bottom: 20px\" row-key=\"id\" max-height=\"68vh\" default-expand-all border>\r\n        <el-table-column prop=\"date\" width=\"300px\" :label=\"t('module')\">\r\n          <template #default=\"scope\">\r\n            <el-checkbox v-model=\"scope.row.checkAll\" :indeterminate=\"scope.row.isIndeterminate\" @change=\"handleCheckAllChange(scope.row)\">\r\n              {{ scope.row.name }}\r\n            </el-checkbox>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"name\" :label=\"t('permissions')\">\r\n          <template #default=\"scope\">\r\n            <el-checkbox-group v-model=\"scope.row.checked\">\r\n              <el-checkbox v-for=\"(item, index) in scope.row.authData\" :key=\"index\" :value=\"item.id\" @change=\"handleCheckedCitiesChange(scope.row)\">\r\n                {{ item.name }}\r\n              </el-checkbox>\r\n            </el-checkbox-group>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button :loading=\"loading\" @click=\"onClose\">\r\n          {{ t('cancle') }}\r\n        </el-button>\r\n        <el-button :loading=\"loading\" type=\"primary\" @click=\"onSubmit\">\r\n          {{ t('save') }}\r\n        </el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n.box {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "t", "useI18n", "myVisible", "computed", "get", "modelValue", "set", "val", "formRef", "ref", "form", "name", "remark", "code", "sort", "formRules", "required", "message", "trigger", "flatData", "active", "PermissionTreeLIst", "tableData", "async", "handleCheckAllChange", "authData", "checked", "checkAll", "map", "item", "id", "isIndeterminate", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "value", "state", "for<PERSON>ach", "length", "list", "Array", "isArray", "includes", "type", "allChecked", "every", "child", "someChecked", "some", "onClose", "loading", "onSubmit", "tab", "getFlatData", "useApi", "updatePermission", "roleId", "menuIds", "then", "ElMessage", "success", "validate", "valid", "userApi", "createRole", "gcode", "res", "data", "Set", "v", "handleClick", "currentTab", "find", "newTab", "getPermissionTree", "permissionTree", "status", "handleTree", "onMounted"], "mappings": "8+BAoDA,MAAMA,EAAQC,EAcRC,EAAQC,EAIRC,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAYC,EAAS,CACzBC,IAAM,IACGV,EAAMW,WAEf,GAAAC,CAAIC,GACFX,EAAM,oBAAqBW,EAAG,IAG5BC,EAAUC,IACVC,EAAOD,EAAI,CACfE,KAAM,GACNC,OAAQ,GACRC,KAAM,GACNC,KAAM,IAEFC,EAAYN,EAAe,CAC/BE,KAAM,CAAC,CAAEK,UAAU,EAAMC,QAASjB,EAAE,uBAAwBkB,QAAS,WAGjEC,EAAWV,EAAS,IACpBW,EAASX,EAAI,IACbY,EAAqBZ,EAAS,IAC9Ba,EAAYb,EAAI,IACtBc,eAAeC,EAAqBjB,GAC9BA,EAAIkB,WACFlB,EAAAmB,QAAUnB,EAAIoB,SAAWpB,EAAIkB,SAASG,KAAKC,GAAcA,EAAKC,KAAM,IAE1EvB,EAAIwB,iBAAkB,EAClBxB,EAAIyB,gBACAC,EAAY1B,EAAIyB,SAAUzB,EAAIoB,UAEtCO,EAAUZ,EAAUa,MAAK,CAKlB,SAAAF,EAAYD,EAAeI,GACzBJ,EAAAK,SAAQd,MAAOM,IAClBA,EAAKG,UAAYH,EAAKG,SAASM,OAAS,SACpCL,EAAYJ,EAAKG,SAAUI,GAEnCP,EAAKF,SAAWS,EAChBP,EAAKE,iBAAkB,EACnBF,EAAKJ,WACFI,EAAAH,QAAUG,EAAKF,SAAWE,EAAKJ,SAASG,KAAKC,GAAcA,EAAKC,KAAM,GAAC,GAE/E,CAGH,SAASI,EAAUK,GACZA,EAAAF,SAASR,IAMZ,GALIA,EAAKG,UAAYQ,MAAMC,QAAQZ,EAAKG,WAAaH,EAAKG,SAASM,OAAS,GAE1EJ,EAAUL,EAAKG,UAGb,CAAC,EAAG,GAAGU,SAASb,EAAKc,MACvB,GAAId,EAAKJ,SAEPI,EAAKF,SAAWE,EAAKH,QAAQY,SAAWT,EAAKJ,SAASa,OACjDT,EAAAE,gBAAkBF,EAAKH,QAAQY,OAAS,GAAKT,EAAKH,QAAQY,OAAST,EAAKJ,SAASa,YAAA,GAC7EE,MAAMC,QAAQZ,EAAKG,WAAaH,EAAKG,SAASM,OAAS,EAAG,CAEnE,MAAMM,EAAaf,EAAKG,SAASa,OAAOC,GAAeA,EAAMnB,WACvDoB,EAAclB,EAAKG,SAASgB,MAAMF,GAAeA,EAAMf,iBAAmBe,EAAMnB,WAEtFE,EAAKF,SAAWiB,EACXf,EAAAE,iBAAmBa,GAAcG,CAAA,CACxC,GAEH,CAGH,SAASE,IACP/C,EAAUiC,OAAQ,CAAA,CAGd,MAAAe,EAAUzC,GAAI,GACpB,SAAS0C,IAEPhC,EAASgB,MAAQ,GAGEd,EAAAc,MAAME,SAASe,IAC5BA,EAAIpB,WACNE,EAAUkB,EAAIpB,UACdqB,EAAYD,EAAIpB,UAAQ,IAIT,MAAftC,EAAMiD,KACAW,EAAAC,iBAAiB,CAAEC,OAAQ9D,EAAM8D,OAAQC,QAAStC,EAASgB,QAASuB,MAAK,KACrEC,EAAAC,QAAQ5D,EAAE,gBACZiD,IACRrD,EAAM,UAAS,IAEO,MAAfF,EAAMiD,MACfnC,EAAQ2B,OACN3B,EAAQ2B,MAAM0B,UAAUC,IAClBA,IACFZ,EAAQf,OAAQ,EAChB4B,EAAQC,WAAW,IAAKtD,EAAKyB,MAAO8B,MAAOnE,EAAUmE,QAASP,MAAMQ,IAC1DZ,EAAAC,iBAAiB,CAAEC,OAAQU,EAAIC,KAAMV,QAAStC,EAASgB,QAASuB,MAAK,KAC3ER,EAAQf,OAAQ,EACNwB,EAAAC,QAAQ5D,EAAE,gBACZiD,IACRrD,EAAM,UAAS,GAChB,IACF,GAGT,CAGF,SAASyD,EAAYlB,GACbA,EAAAE,SAASR,IACTA,EAAKG,UAAYH,EAAKG,SAASM,OAAS,GAC1Ce,EAAYxB,EAAKG,UAEfH,EAAKJ,WAAaI,EAAKH,QAAQY,OAAS,GAAKT,EAAKE,iBAEpDZ,EAASgB,MAAQ,IAAI,IAAIiC,IAAI,IAAIjD,EAASgB,SAAUN,EAAKH,QAASG,EAAKC,MAC9DD,EAAKG,WAAaH,EAAKG,SAASgB,MAAMqB,GAA6BA,EAAE1C,YAAaE,EAAKG,SAASgB,MAAMqB,GAAoCA,EAAEtC,qBAErJZ,EAASgB,MAAQ,IAAI,IAAIiC,IAAI,IAAIjD,EAASgB,MAAON,EAAKC,MAAI,GAE7D,CAGH,SAASwC,IAED,MAAAC,EAAalD,EAAmBc,MAAMqC,MAAM3C,GAAcA,EAAKC,KAAOV,EAAOe,QAC/EoC,GACFrC,EAAUqC,EAAWvC,UAIjB,MAAAyC,EAASpD,EAAmBc,MAAMqC,MAAM3C,GAAcA,EAAKC,KAAOV,EAAOe,QAC3EsC,IACFnD,EAAUa,MAAQsC,EAAOzC,SACzBE,EAAUZ,EAAUa,OACtB,CAIFZ,eAAemD,WACPX,EAAQY,eAAe,CAAEC,OAAQ,IAAKpB,OAAQ9D,EAAM8D,SAAUE,MAAMQ,IACvD,IAAbA,EAAIrD,OACNQ,EAAmBc,MAAQ+B,EAAIC,KAC/BU,GAAWxD,EAAmBc,OAGXd,EAAAc,MAAME,SAASe,IAC5BA,EAAIpB,UACNE,EAAUkB,EAAIpB,SAAQ,IAI1BZ,EAAOe,MAAQd,EAAmBc,MAAM,GAAGL,GAC3CR,EAAUa,MAAQd,EAAmBc,MAAM,GAAGH,SAAA,GAEjD,CAGH,SAAS6C,GAAWtC,GACbA,EAAAF,SAASR,IACRA,EAAKG,UAAYH,EAAKH,UACxBG,EAAKJ,SAAWI,EAAKG,SACrBH,EAAKG,SAAW,MAEdH,EAAKG,UAAYH,EAAKG,SAASM,OAAS,GAC1CuC,GAAWhD,EAAKG,SAAQ,GAE3B,QAEH8C,GAAU,KACUJ,KACC,MAAfhF,EAAMiD,OACHjC,EAAAyB,MAAMxB,KAAOjB,EAAMiB,KAAA,msEAhJ1BuB,EAAUZ,EAAUa"}