{"version": 3, "file": "couponTemplate.api-3l2fuazK.js", "sources": ["../../src/api/modules/marketing/coupon/couponTemplate.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n/**\r\n * @description: 优惠券活动\r\n */\r\nexport default {\r\n  /**\r\n   * @description: 优惠券模板列表\r\n   */\r\n  list: (data: any) => api.get('/admin-api/marketing/coupon-template/page', { params: data }),\r\n\t/**\r\n\t * @description: 获得房间可用优惠券模板列表\r\n\t */\r\n\troomCouponList: (data: any) => api.get('/admin-api/marketing/coupon-template/room/list', { params: data }),\r\n  /**\r\n   * 优惠券模板树，一级为券类型，二级为券\r\n   * @param data\r\n   * @returns\r\n   */\r\n  listTree: (data: { gcode: string, hcode?: string }) =>\r\n    api.get('marketing/coupon/template/tree', {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 活动明细\r\n   * @param gcode\r\n   * @param templateCode 活动代码\r\n   * @returns\r\n   */\r\n  detail: (gcode: string, templateCode: string) =>\r\n    api.get('marketing/coupon/template/detail', {\r\n      params: {\r\n        gcode,\r\n        templateCode,\r\n      },\r\n    }),\r\n  /**\r\n   * 创建活动\r\n   * @param data\r\n   * @returns\r\n   */\r\n  create: (data: any) => api.post('/admin-api/marketing/coupon-template/create', data),\r\n  /**\r\n   * 编辑活动\r\n   * @param data\r\n   * @returns\r\n   */\r\n  edit: (data: any) => api.put('/admin-api/marketing/coupon-template/update', data),\r\n\r\n  /**\r\n   * 修改优惠券模板状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateTemplateStatus: (data: any) =>\r\n    api.put('/admin-api/marketing/coupon-template/lose-efficacy', data),\r\n}\r\n"], "names": ["couponTemplateApi", "list", "data", "api", "get", "params", "roomCouponList", "listTree", "detail", "gcode", "templateCode", "create", "post", "edit", "put", "updateTemplateStatus"], "mappings": "wCAIA,MAAeA,EAAA,CAIbC,KAAOC,GAAcC,EAAIC,IAAI,4CAA6C,CAAEC,OAAQH,IAIrFI,eAAiBJ,GAAcC,EAAIC,IAAI,iDAAkD,CAAEC,OAAQH,IAMlGK,SAAWL,GACTC,EAAIC,IAAI,iCAAkC,CACxCC,OAAQH,IASZM,OAAQ,CAACC,EAAeC,IACtBP,EAAIC,IAAI,mCAAoC,CAC1CC,OAAQ,CACNI,QACAC,kBAQNC,OAAST,GAAcC,EAAIS,KAAK,8CAA+CV,GAM/EW,KAAOX,GAAcC,EAAIW,IAAI,8CAA+CZ,GAO5Ea,qBAAuBb,GACrBC,EAAIW,IAAI,qDAAsDZ"}