{"version": 3, "file": "accRecord.api-i1EZ7kom.js", "sources": ["../../src/api/modules/pms/accset/accRecord.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/acc-record'\r\n/**\r\n * @description: 现付账记录\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 更新现付账记录\r\n   * @param data\r\n   */\r\n  updateAccRecord: (data: any) => api.post(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 删除现付账记录\r\n   * @param id\r\n   */\r\n  deleteAccRecord: (id: number) => api.post(`${BASE_PATH}/delete`, {\r\n    params: {\r\n      id,\r\n    },\r\n  }),\r\n\r\n  /**\r\n   * 获得现付账记录\r\n   * @param data\r\n   */\r\n  getAccRecord: (data: { gcode: string, accNo: string }) => api.get(`${BASE_PATH}/get`, {\r\n    params: data,\r\n  }),\r\n\r\n  /**\r\n   * 获得现付账记录列表\r\n   * @param data \r\n   * @returns \r\n   */\r\n  getAccRecordList: (data: any) => api.get(`${BASE_PATH}/list`, {\r\n   params:data, \r\n  }),\r\n\r\n  /**\r\n   * 获得现付账记录分页\r\n   * @param data\r\n   */\r\n  getAccRecordPage: (data: any) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n}\r\n"], "names": ["BASE_PATH", "accRecordApi", "updateAccRecord", "data", "api", "post", "deleteAccRecord", "id", "params", "getAccRecord", "get", "getAccRecordList", "getAccRecordPage"], "mappings": "wCAEA,MAAMA,EAAY,2BAIHC,EAAA,CAMbC,gBAAkBC,GAAcC,EAAIC,KAAK,GAAGL,WAAoBG,EAAM,IAMtEG,gBAAkBC,GAAeH,EAAIC,KAAK,GAAGL,WAAoB,CAC/DQ,OAAQ,CACND,QAQJE,aAAeN,GAA2CC,EAAIM,IAAI,GAAGV,QAAiB,CACpFQ,OAAQL,IAQVQ,iBAAmBR,GAAcC,EAAIM,IAAI,GAAGV,SAAkB,CAC7DQ,OAAOL,IAORS,iBAAmBT,GACjBC,EAAIM,IAAI,GAAGV,SAAkB,CAC3BQ,OAAQL"}