{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-Br6wkbiu.js", "sources": ["../../src/components/PageHeader/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\ndefineOptions({\r\n  name: '<PERSON><PERSON>ead<PERSON>',\r\n})\r\n\r\ndefineProps<{\r\n  title?: string\r\n  content?: string\r\n}>()\r\n\r\nconst slots = useSlots()\r\n</script>\r\n\r\n<template>\r\n  <div class=\"page-header mb-5 flex flex-wrap items-center justify-between gap-5 bg-[var(--g-container-bg)] px-5 py-4 transition-background-color-300\">\r\n    <div class=\"main flex-[1_1_70%]\">\r\n      <div class=\"text-2xl\">\r\n        <slot name=\"title\">\r\n          {{ title }}\r\n        </slot>\r\n      </div>\r\n      <div class=\"mt-2 text-sm text-stone-5 empty-hidden\">\r\n        <slot name=\"content\">\r\n          {{ content }}\r\n        </slot>\r\n      </div>\r\n    </div>\r\n    <div v-if=\"slots.default\" class=\"ml-a flex-none\">\r\n      <slot />\r\n    </div>\r\n  </div>\r\n</template>\r\n"], "names": ["slots", "useSlots"], "mappings": "udAUA,MAAMA,EAAQC"}