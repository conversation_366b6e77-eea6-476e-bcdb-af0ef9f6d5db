{"version": 3, "file": "roomCardLog.api-pw0J1hl7.js", "sources": ["../../src/api/modules/pms/room/roomCardLog.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/room-card-log'\r\nexport default {\r\n  /**\r\n   * 获得门卡记录分页\r\n   * @param data\r\n   */\r\n  getRoomCardLogPage: (data: any) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 创建门卡记录\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createRoomCardLog: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n}\r\n"], "names": ["BASE_PATH", "roomCardLogApi", "getRoomCardLogPage", "data", "api", "get", "params", "createRoomCardLog", "post"], "mappings": "mCAEA,MAAMA,EAAY,8BACHC,EAAA,CAKbC,mBAAqBC,GACnBC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAQZI,kBAAoBJ,GAAcC,EAAII,KAAK,GAAGR,WAAoBG,EAAM,CAAE"}