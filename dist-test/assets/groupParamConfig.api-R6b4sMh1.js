import{a as e}from"./index-CkEhI1Zk.js";const g="admin-api/pms/group-param-config",a={getGroupParamConfigOrder:a=>e.get(`${g}/get/order`,{params:{gcode:a}}),getGroupParamConfigNightAudit:a=>e.get(`${g}/get/night-audit`,{params:{gcode:a}}),getGroupParamConfigFinance:a=>e.get(`${g}/get/finance`,{params:{gcode:a}}),getGroupParamConfigMember:a=>e.get(`${g}/get/member`,{params:{gcode:a}}),getGroupParamConfigMemberRule:a=>e.get(`${g}/get/member-rule`,{params:{gcode:a}}),getGroupParamConfigRoomColor:a=>e.get(`${g}/get/color`,{params:{gcode:a}}),getGroupParamConfigNightNum:a=>e.get(`${g}/get/night-num`,{params:{gcode:a}}),getGroupParamConfigHour:a=>e.get(`${g}/get/hour`,{params:{gcode:a}}),updateGroupParamConfig:a=>e.put(`${g}/update`,a)};export{a as g};
//# sourceMappingURL=groupParamConfig.api-R6b4sMh1.js.map
