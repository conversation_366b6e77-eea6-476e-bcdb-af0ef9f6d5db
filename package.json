{"type": "module", "version": "4.12.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "vite", "build": "vite build", "build:test": "node --max_old_space_size=1024000 ./node_modules/vite/bin/vite.js build --mode test", "serve": "http-server ./dist -o", "serve:test": "http-server ./dist-test -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc -b", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "postinstall": "simple-git-hooks", "preinstall": "npx only-allow pnpm", "release": "bumpp"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@imengyu/vue3-context-menu": "^1.4.3", "@vueuse/components": "^11.1.0", "@vueuse/core": "^11.1.0", "@vueuse/integrations": "^11.1.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "defu": "^6.1.4", "disable-devtool": "^0.3.8", "element-plus": "^2.8.5", "eruda": "^3.4.0", "event-source-polyfill": "^1.0.31", "floating-vue": "5.2.2", "hotkeys-js": "^3.13.7", "lodash-es": "^4.17.21", "medium-zoom": "^1.1.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "overlayscrollbars": "^2.10.0", "overlayscrollbars-vue": "^0.5.9", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^2.2.4", "pinyin-pro": "^3.25.0", "qs": "^6.13.0", "scule": "^1.3.0", "sortablejs": "^1.15.3", "spinkit": "^2.0.1", "splitpanes": "^3.1.5", "timeago.js": "^4.0.2", "uuid": "^11.1.0", "v-wave": "^3.0.2", "vconsole": "^3.15.1", "vue": "^3.5.12", "vue-currency-input": "^3.1.0", "vue-i18n": "^10.0.4", "vue-m-message": "^4.0.2", "vue-router": "^4.4.5", "watermark-js-plus": "^1.5.7"}, "devDependencies": {"@antfu/eslint-config": "3.8.0", "@iconify/json": "^2.2.261", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^5.2.0", "@stylistic/stylelint-config": "^2.0.0", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.16", "@types/sortablejs": "^1.15.8", "@unocss/eslint-plugin": "^0.63.4", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "autoprefixer": "^10.4.20", "boxen": "^8.0.1", "bumpp": "^9.7.1", "cz-git": "^1.10.1", "eslint": "^9.12.0", "eslint-config-prettier": "^10.1.5", "esno": "^4.8.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "inquirer": "^12.0.0", "lint-staged": "^15.2.10", "npm-run-all2": "^6.2.4", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.4.47", "postcss-nested": "^6.2.0", "sass-embedded": "^1.80.2", "simple-git-hooks": "^2.11.1", "stylelint": "^16.10.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.8.1", "svgo": "^3.3.2", "typescript": "^5.6.3", "unocss": "^0.63.4", "unocss-preset-scrollbar": "^0.3.1", "unplugin-auto-import": "^0.18.3", "unplugin-icons": "^0.16.6", "unplugin-turbo-console": "^1.10.4", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.9", "vite-plugin-app-loading": "^0.3.0", "vite-plugin-archiver": "^0.1.1", "vite-plugin-banner": "^0.8.0", "vite-plugin-compression2": "^1.3.0", "vite-plugin-fake-server": "^2.1.2", "vite-plugin-pages": "^0.32.3", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.5.2", "vite-plugin-vue-meta-layouts": "^0.4.3", "vue-tsc": "^2.1.6"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}