{"version": 3, "file": "levelDetail-C_O9UC0C.js", "sources": ["../../src/views/group-member-plan/member-level/components/DetailForm/levelDetail.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"pleaseEnterLevelName\": \"Please enter the level name\",\r\n    \"editSuccessful\": \"Edit successful\",\r\n    \"levelName\": \"Level Name\",\r\n    \"memberLevel\": \"Member Level\",\r\n    \"pleaseSelect\": \"Please select\",\r\n    \"joiningMethod\": \"Joining Method\",\r\n    \"freeRegistration\": \"Free Registration\",\r\n    \"paymentAmount\": \"Payment Amount\",\r\n    \"allowModifyCardFee\": \"Allow modifying card fee amount, requires permission verification\",\r\n    \"enableRegistrationSMSVerification\": \"Enable registration must have SMS verification\",\r\n    \"membershipPeriod\": \"Membership Period\",\r\n    \"permanent\": \"Permanent\",\r\n    \"fromRegistrationWithinMonths1\": \"From the date of registration for\",\r\n    \"fromRegistrationWithinMonths2\": \"months is one membership period\",\r\n    \"membershipPeriodNote\": \"After the end, re-enter a new membership period; if the membership level changes during this period, a new membership period will be recalculated from the date of change.\",\r\n    \"storedValueAndConsumption\": \"Stored Value and Consumption\",\r\n    \"supportStoredValue\": \"Support Stored Value\",\r\n    \"noVerificationRequired\": \"No verification required\",\r\n    \"consumptionRequiresPasswordVerification\": \"Consumption requires member password verification\",\r\n    \"consumptionRequiresSMSVerification\": \"Consumption requires SMS verification code\",\r\n    \"reservedTime\": \"Reserved Time\",\r\n    \"unifiedNoshowTime\": \"Unified No-show Time\",\r\n    \"delayNoshowTimeTo\": \"Delay No-show Time to\",\r\n    \"e.g.20:00\": \"e.g.: 20:00\",\r\n    \"remarks\": \"Remark\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"pleaseEnterLevelName\": \"请输入级别名称\",\r\n    \"editSuccessful\": \"编辑成功\",\r\n    \"levelName\": \"级别名称\",\r\n    \"memberLevel\": \"会员等级\",\r\n    \"pleaseSelect\": \"请选择\",\r\n    \"joiningMethod\": \"加入方式\",\r\n    \"freeRegistration\": \"免费注册\",\r\n    \"paymentAmount\": \"付款金额\",\r\n    \"allowModifyCardFee\": \"允许修改卡费金额,需权限验证\",\r\n    \"enableRegistrationSMSVerification\": \"开启注册必须短信验证码认证\",\r\n    \"membershipPeriod\": \"会员周期\",\r\n    \"permanent\": \"永久\",\r\n    \"fromRegistrationWithinMonths1\": \"自注册之日起\",\r\n    \"fromRegistrationWithinMonths2\": \"个月内为1个会员周期\",\r\n    \"membershipPeriodNote\": \"结束后重新进入新的会员周期；若此期间发生会员等级变更，自变更之日起，重新计算新的会员周期。\",\r\n    \"storedValueAndConsumption\": \"储值和消费\",\r\n    \"supportStoredValue\": \"支持储值\",\r\n    \"noVerificationRequired\": \"无需验证\",\r\n    \"consumptionRequiresPasswordVerification\": \"消费需要验证会员密码\",\r\n    \"consumptionRequiresSMSVerification\": \"消费需要短信验证码\",\r\n    \"reservedTime\": \"预留时间\",\r\n    \"unifiedNoshowTime\": \"统一No-show时间\",\r\n    \"delayNoshowTimeTo\": \"延迟No-show时间至\",\r\n    \"e.g.20:00\": \"如：20:00\",\r\n    \"remarks\": \"备注\"\r\n  },\r\n  \"km\": {\r\n    \"pleaseEnterLevelName\": \"សូមបញ្ចូលឈ្មោះកម្រិត\",\r\n    \"editSuccessful\": \"កែសម្រួលដោយជោគជ័យ\",\r\n    \"levelName\": \"ឈ្មោះកម្រិត\",\r\n    \"memberLevel\": \"កម្រិតសមាជិក\",\r\n    \"pleaseSelect\": \"សូមជ្រើសរើស\",\r\n    \"joiningMethod\": \"វិធីសាកសម\",\r\n    \"freeRegistration\": \"ចុះឈ្មោះដោយឥតគិតថ្លៃ\",\r\n    \"paymentAmount\": \"ចំនួនទឹកប្រាក់\",\r\n    \"allowModifyCardFee\": \"អនុញ្ញាតឱ្យកែសម្រួលថ្លៃកាត ត្រូវការផ្ទៀងផ្ទាត់សិទ្ធិ\",\r\n    \"enableRegistrationSMSVerification\": \"បើកដំណើរការការផ្ទៀងផ្ទាត់សារអសយដ្ឋាន\",\r\n    \"membershipPeriod\": \"រយៈពេលសមាជិក\",\r\n    \"permanent\": \"អចិន្ត្រៃយ៍\",\r\n    \"fromRegistrationWithinMonths1\": \"ចាប់ពីថ្ងៃចុះឈ្មោះ\",\r\n    \"fromRegistrationWithinMonths2\": \"ខែជារយៈពេលសមាជិកមួយ\",\r\n    \"membershipPeriodNote\": \"បន្ទាប់ពីបញ្ចប់ ចូលរយៈពេលសមាជិកថ្មីម្តងទៀត។ ប្រសិនបើកម្រិតសមាជិកផ្លាស់ប្តូរក្នុងអំឡុងពេលនេះ រយៈពេលសមាជិកថ្មីនឹងត្រូវបានគណនាឡើងវិញចាប់ពីថ្ងៃផ្លាស់ប្តូរ។\",\r\n    \"storedValueAndConsumption\": \"ការផ្ទុកតម្លៃនិងការប្រើប្រាស់\",\r\n    \"supportStoredValue\": \"គាំទ្រការផ្ទុកតម្លៃ\",\r\n    \"noVerificationRequired\": \"មិនចាំបាច់ផ្ទៀងផ្ទាត់\",\r\n    \"consumptionRequiresPasswordVerification\": \"ការប្រើប្រាស់ត្រូវការផ្ទៀងផ្ទាត់ពាក្យសម្ងាត់សមាជិក\",\r\n    \"consumptionRequiresSMSVerification\": \"ការប្រើប្រាស់ត្រូវការលេខកូដសារអសយដ្ឋាន\",\r\n    \"reservedTime\": \"ពេលវេលាកក់ទុក\",\r\n    \"unifiedNoshowTime\": \"ពេលវេលា No-show ឯកសណ្ឋាន\",\r\n    \"delayNoshowTimeTo\": \"ពន្យារពេលវេលា No-show ដល់\",\r\n    \"e.g.20:00\": \"ឧទាហរណ៍៖ 20:00\",\r\n    \"remarks\": \"ចំណាំ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { memberTypeApi } from '@/api/modules/index'\r\nimport { MEMBER_TYPE_LEVEL } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  mtCode: '',\r\n})\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst { t } = useI18n()\r\n\r\nconst form = ref({\r\n  mtCode: props.mtCode,\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 会员类型名称 */\r\n  mtName: '',\r\n  /** 级别 */\r\n  level: 1,\r\n  /** 是否永久有效 */\r\n  isForEver: '1',\r\n  /** 有效期（月） */\r\n  indateMonth: 12,\r\n  /** 是否免费 */\r\n  isFree: '1',\r\n  /** 购卡金额 */\r\n  fee: 0,\r\n  /** 允许修改卡费金额 */\r\n  canUpdateFee: '1',\r\n  /** 开启注册必须短信验证码 */\r\n  needSms: '1',\r\n  /** 是否支持储值 */\r\n  supportStore: '1',\r\n  /** 消费验证方式 0:无需验证 1:消费需要验证会员密码 2:消费需要短信验证码 */\r\n  verifyMode: '0',\r\n  /** 是否延迟noshow时间 */\r\n  delayNoshow: '1',\r\n  /** 延迟时间 */\r\n  noshowTime: '',\r\n  /** 是否有效 0：否 1：是 */\r\n  isEnable: '1',\r\n  /** 备注 */\r\n  remark: '',\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  mtName: [{ required: true, message: t('pleaseEnterLevelName'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  if (form.value.mtCode !== '') {\r\n    getInfo()\r\n  }\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  memberTypeApi.getType({ gcode: userStore.gcode, mtCode: props.mtCode }).then((res: any) => {\r\n    loading.value = false\r\n    form.value = res.data\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            memberTypeApi\r\n              .updateType({\r\n                ...form.value,\r\n                indateMonth: form.value.isForEver === '1' ? null : form.value.indateMonth,\r\n              })\r\n              .then((res: any) => {\r\n                if (res.code === 0) {\r\n                  ElMessage.success({\r\n                    message: t('editSuccessful'),\r\n                    center: true,\r\n                  })\r\n                } else {\r\n                  ElMessage.error({\r\n                    message: res.msg,\r\n                    center: true,\r\n                  })\r\n                }\r\n                resolve()\r\n              })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"150px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('levelName')\" prop=\"mtName\">\r\n        <el-input v-if=\"props.isEdit\" v-model=\"form.mtName\" :placeholder=\"t('pleaseEnterLevelName')\" />\r\n        <span v-else>{{ form.mtName }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('memberLevel')\" prop=\"level\">\r\n        <el-select v-model=\"form.level\" :placeholder=\"t('pleaseSelect')\" :disabled=\"!props.isEdit\">\r\n          <el-option v-for=\"item in MEMBER_TYPE_LEVEL\" :key=\"item.key\" :label=\"item.value\" :value=\"item.key\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('joiningMethod')\">\r\n        <el-radio-group v-model=\"form.isFree\" class=\"vertical-radio-group\" :disabled=\"!props.isEdit\">\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('freeRegistration') }}\r\n          </el-radio>\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('paymentAmount') }}\r\n            <el-input-number v-model=\"form.fee\" :min=\"0\" :disabled=\"form.isFree === '1' || !props.isEdit\" />\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-checkbox v-model=\"form.canUpdateFee\" true-value=\"1\" false-value=\"0\" :disabled=\"form.isFree === '1' || !props.isEdit\">\r\n          {{ t('allowModifyCardFee') }}\r\n        </el-checkbox>\r\n        <el-checkbox v-model=\"form.needSms\" true-value=\"1\" false-value=\"0\" :disabled=\"form.isFree === '1' || !props.isEdit\">\r\n          {{ t('enableRegistrationSMSVerification') }}\r\n        </el-checkbox>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('membershipPeriod')\">\r\n        <el-radio-group v-model=\"form.isForEver\" class=\"vertical-radio-group\" :disabled=\"!props.isEdit\">\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('permanent') }}\r\n          </el-radio>\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('fromRegistrationWithinMonths1') }}\r\n            <el-input-number v-model=\"form.indateMonth\" :min=\"1\" :disabled=\"form.isForEver === '1' || !props.isEdit\" />\r\n            {{ t('fromRegistrationWithinMonths2') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n        <div class=\"el-form-item-msg\">\r\n          {{ t('membershipPeriodNote') }}\r\n        </div>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <el-form-item :label=\"t('storedValueAndConsumption')\">\r\n        <el-checkbox v-model=\"form.supportStore\" true-value=\"1\" false-value=\"0\" :disabled=\"!props.isEdit\">\r\n          {{ t('supportStoredValue') }}\r\n        </el-checkbox>\r\n        <div class=\"el-form-item-msg\">\r\n          <el-radio-group v-if=\"form.supportStore === '1'\" v-model=\"form.verifyMode\" :disabled=\"!props.isEdit\">\r\n            <el-radio value=\"0\" size=\"large\">\r\n              {{ t('noVerificationRequired') }}\r\n            </el-radio>\r\n            <el-radio value=\"1\" size=\"large\">\r\n              {{ t('consumptionRequiresPasswordVerification') }}\r\n            </el-radio>\r\n            <el-radio value=\"2\" size=\"large\">\r\n              {{ t('consumptionRequiresSMSVerification') }}\r\n            </el-radio>\r\n          </el-radio-group>\r\n        </div>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <el-form-item :label=\"t('reservedTime')\">\r\n        <el-radio-group v-if=\"props.isEdit\" v-model=\"form.delayNoshow\">\r\n          <el-radio value=\"0\" size=\"large\">\r\n            {{ t('unifiedNoshowTime') }}\r\n          </el-radio>\r\n          <el-radio value=\"1\" size=\"large\">\r\n            {{ t('delayNoshowTimeTo') }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n        <span v-else>\r\n          {{ form.delayNoshow === '1' ? `${t('delayNoshowTimeTo')}${form.noshowTime}` : t('unifiedNoshowTime') }}\r\n        </span>\r\n        <el-time-select v-if=\"form.delayNoshow === '1' && props.isEdit\" v-model=\"form.noshowTime\" start=\"12:00\" step=\"00:15\" end=\"23:59\" :placeholder=\"t('e.g.20:00')\" style=\"width: 240px\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('remarks')\">\r\n        <el-input v-if=\"props.isEdit\" v-model=\"form.remark\" type=\"textarea\" :rows=\"5\" />\r\n        <span v-else>{{ form.remark }}</span>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.vertical-radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  text-align: left;\r\n}\r\n\r\n.vertical-radio {\r\n  margin-right: 0;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "userStore", "useUserStore", "loading", "ref", "formRef", "t", "useI18n", "form", "mtCode", "gcode", "mtName", "level", "isForEver", "indateMonth", "isFree", "fee", "canUpdateFee", "needSms", "supportStore", "verifyMode", "delayNoshow", "noshowTime", "isEnable", "remark", "formRules", "required", "message", "trigger", "onMounted", "value", "memberTypeApi", "getType", "then", "res", "data", "__expose", "submit", "Promise", "resolve", "validate", "valid", "updateType", "code", "ElMessage", "success", "center", "error", "msg"], "mappings": "kjCA6FA,MAAMA,EAAQC,EAGRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,KACVE,EAAEA,GAAMC,IAERC,EAAOJ,EAAI,CACfK,OAAQV,EAAMU,OAEdC,MAAOT,EAAUS,MAEjBC,OAAQ,GAERC,MAAO,EAEPC,UAAW,IAEXC,YAAa,GAEbC,OAAQ,IAERC,IAAK,EAELC,aAAc,IAEdC,QAAS,IAETC,aAAc,IAEdC,WAAY,IAEZC,YAAa,IAEbC,WAAY,GAEZC,SAAU,IAEVC,OAAQ,KAGJC,EAAYrB,EAAe,CAC/BO,OAAQ,CAAC,CAAEe,UAAU,EAAMC,QAASrB,EAAE,wBAAyBsB,QAAS,kBAG1EC,GAAU,KACkB,KAAtBrB,EAAKsB,MAAMrB,SAMfN,EAAQ2B,OAAQ,EAChBC,EAAcC,QAAQ,CAAEtB,MAAOT,EAAUS,MAAOD,OAAQV,EAAMU,SAAUwB,MAAMC,IAC5E/B,EAAQ2B,OAAQ,EAChBtB,EAAKsB,MAAQI,EAAIC,IAAA,IART,IAYCC,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBlC,EAAQyB,OACNzB,EAAQyB,MAAMU,UAAUC,IAClBA,GACFV,EACGW,WAAW,IACPlC,EAAKsB,MACRhB,YAAsC,MAAzBN,EAAKsB,MAAMjB,UAAoB,KAAOL,EAAKsB,MAAMhB,cAE/DmB,MAAMC,IACY,IAAbA,EAAIS,KACNC,EAAUC,QAAQ,CAChBlB,QAASrB,EAAE,kBACXwC,QAAQ,IAGVF,EAAUG,MAAM,CACdpB,QAASO,EAAIc,IACbF,QAAQ,IAGJP,GAAA,GACT,GAEN"}