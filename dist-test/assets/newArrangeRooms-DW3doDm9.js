import{d as e,aj as o,b as t,ai as a,y as l,o as s,c as i,f as r,w as m,h as c,Y as d,u as n,g as u,F as p,ag as v,aq as f,i as b,aR as h,av as k,q as y,m as g,b1 as C,b2 as R,l as N,n as _,x,p as V,ay as P,aT as j,e as w,ax as T,bz as A}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                          *//* empty css               *//* empty css                *//* empty css                    *//* empty css                       *//* empty css                        *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                  */import{b as D}from"./book.api-ERXvEXQF.js";import{d as B}from"./dictData.api-DUabpYqy.js";import{B as S,R as L,d as Y}from"./constants-Cg3j_uH4.js";import{_ as O}from"./_plugin-vue_export-helper-BCo6x5W8.js";const z={class:"room-type-buttons"},M={class:"special_td"},U={style:{"margin-bottom":"15px"}},H={key:0},q={key:1},I={class:"roomList"},F={class:"flexBox"},$={style:{"padding-left":"0","margin-bottom":"0","list-style":"none"}},E=e({__name:"newArrangeRooms",props:{roomNum:{default:0}},emits:["update:modelValue","success"],setup(e,{expose:O,emit:E}){const G=e,J=E,{t:K}=o(),Q=t(!1),W=a(),X=t(!1),Z=t({rtCode:"",rtName:"",state:"VC",isBookedRoom:S.NO.toString(),selectRooms:[],checkRoomList:[],dayPrices:[],planCheckinTime:"",planCheckoutTime:"",price:0,vipPrice:0}),ee=t([]),oe=t([]),te=t([]),ae=t([]);l((async()=>{}));const le=t({formData:{}});const se=[Y];function ie(){Z.value.isBookedRoom=S.NO.toString(),ee.value=oe.value.filter((e=>e.state===Z.value.state))}async function re(){const e={gcode:W.gcode,hcode:W.hcode,rtCode:Z.value.rtCode,state:Z.value.state,planCheckinTime:k(le.value.formData.planCheckinTime).format("YYYY-MM-DD HH:mm"),planCheckoutTime:k(le.value.formData.planCheckoutTime).format("YYYY-MM-DD HH:mm"),isMeetingRoom:Z.value.isBookedRoom,preOccupied:"0"};X.value=!0,await D.canBookRoomList(e).then((e=>{if(X.value=!1,0!==e.code)return h.error(e.msg);oe.value=e.data,ee.value=e.data.filter((e=>e.state===Z.value.state))}))}function me(e){var o,t,a,l;Z.value.rtCode!==e&&(Z.value.rtCode=e,te.value&&Array.isArray(te.value)&&Z&&"object"==typeof Z.value&&(Z.value.rtName=(null==(o=te.value.find((e=>e.rtCode===Z.value.rtCode)))?void 0:o.rtName)??"",Z.value.price=(null==(t=te.value.find((e=>e.rtCode===Z.value.rtCode)))?void 0:t.price)??"",Z.value.vipPrice=(null==(a=te.value.find((e=>e.rtCode===Z.value.rtCode)))?void 0:a.dayPrices[0].vipPrice)??"",Z.value.dayPrices=(null==(l=te.value.find((e=>e.rtCode===Z.value.rtCode)))?void 0:l.dayPrices)??[]),re())}function ce(){"1"===Z.value.isBookedRoom?Z.value.state="":Z.value.state=L.VC,re()}function de(){(0===G.roomNum||Z.value.selectRooms.length<=G.roomNum)&&(J("success",Z.value),Q.value=!1)}function ne(){Q.value=!1}return O({open:async function(e){Z.value.checkRoomList=e.rooms,le.value=e,e.rooms.length>0&&(Z.value.selectRooms=e.rooms.map((e=>e.rNo))),Q.value=!0,await async function(){await D.roomtypeList({...le.value.formData,delayMinute:0,gcode:W.gcode,hcode:W.hcode}).then((e=>{0===e.code?(te.value=e.data,e.data&&e.data.length>0&&(Z.value.rtCode=e.data[0].rtCode,Z.value.rtName=e.data[0].rtName,Z.value.price=e.data[0].price,Z.value.vipPrice=e.data[0].dayPrices[0].vipPrice,Z.value.dayPrices=e.data[0].dayPrices)):h.error(`${K("roomType")} ${K("failed")}`)}))}(),await async function(){await B.getDictDataBatch(se).then((e=>{ae.value=e.data.filter((e=>[L.VC,L.VD].includes(e.code)))}))}(),await re()}}),(e,o)=>{const t=y,a=g,l=C,k=R,D=T,B=N,S=_,L=x,Y=A,O=V,E=P,J=j;return s(),i("div",null,[r(E,{modelValue:n(Q),"onUpdate:modelValue":o[3]||(o[3]=e=>b(Q)?Q.value=e:null),title:n(K)("roomsAssigned"),width:"600px","close-on-click-modal":!1,"append-to-body":"","destroy-on-close":""},{footer:m((()=>[r(t,{size:"large",onClick:ne},{default:m((()=>[c(d(n(K)("cancel")),1)])),_:1}),r(t,{type:"primary",size:"large",onClick:de},{default:m((()=>[c(d(n(K)("confirm")),1)])),_:1})])),default:m((()=>[r(L,{size:"default","label-width":"140px","inline-message":"",inline:"",class:"search-form"},{default:m((()=>[r(k,null,{default:m((()=>[r(l,{span:24},{default:m((()=>[r(a,{label:n(K)("roomType"),"label-width":"80px"},{default:m((()=>[u("div",z,[(s(!0),i(p,null,v(n(te),(e=>(s(),w(t,{key:e.rtCode,size:"small",type:n(Z).rtCode===e.rtCode?"primary":"default",onClick:o=>me(e.rtCode)},{default:m((()=>[c(d(e.rtName),1)])),_:2},1032,["type","onClick"])))),128))])])),_:1},8,["label"])])),_:1})])),_:1}),r(k,null,{default:m((()=>[r(l,{span:24},{default:m((()=>[r(a,{label:n(K)("roomState"),"label-width":"80px"},{default:m((()=>[r(B,{modelValue:n(Z).state,"onUpdate:modelValue":o[0]||(o[0]=e=>n(Z).state=e),size:"small",onChange:ie},{default:m((()=>[(s(!0),i(p,null,v(n(ae),(e=>(s(),w(D,{key:e.code,value:e.code,border:""},{default:m((()=>[c(d(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),r(k,null,{default:m((()=>[r(l,{span:24},{default:m((()=>[r(a,{label:n(K)("preOrderedRoom")},{default:m((()=>[r(S,{modelValue:n(Z).isBookedRoom,"onUpdate:modelValue":o[1]||(o[1]=e=>n(Z).isBookedRoom=e),"true-value":"1","false-value":"0",size:"small",label:"预订单占用房间",border:"",onChange:ce},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1}),u("div",M,[u("div",U,[G.roomNum>0?(s(),i("span",H,d(n(K)("roomsAssigned"))+": "+d(n(Z).selectRooms.length)+" / "+d(G.roomNum)+" "+d(n(K)("rooms")),1)):(s(),i("span",q,d(n(K)("roomsAssigned"))+": "+d(n(Z).selectRooms.length)+d(n(K)("rooms")),1))]),(s(!0),i(p,null,v(n(Z).selectRooms,(e=>(s(),w(Y,{key:e,class:"roomtag",type:"danger",closable:"",onClose:o=>{return t=e,Z.value.selectRooms.splice(Z.value.selectRooms.indexOf(t),1),void Z.value.checkRoomList.splice(Z.value.checkRoomList.findIndex((e=>e.rNo===t)),1);var t}},{default:m((()=>[c(d(e),1)])),_:2},1032,["onClose"])))),128))]),f((s(),i("div",I,[u("div",F,[u("ul",$,[r(O,{modelValue:n(Z).selectRooms,"onUpdate:modelValue":o[2]||(o[2]=e=>n(Z).selectRooms=e)},{default:m((()=>[(s(!0),i(p,null,v(n(ee),(e=>(s(),i("li",{key:e.rCode,class:"xxx"},[c(d(e)+" ",1),r(S,{modelValue:e.rNo,"onUpdate:modelValue":o=>e.rNo=o,value:e.rNo,border:"",onChange:o=>{return t=o,a=e,G.roomNum>0&&Z.value.selectRooms.length>G.roomNum?(h.warning(K("maxRooms",{max:G.roomNum})),Z.value.selectRooms=Z.value.selectRooms.slice(0,G.roomNum),!1):(t?Z.value.checkRoomList.push({rNo:a.rNo,rCode:a.rCode,lockNo:a.lockNo,mac:a.mac,lockVersion:a.lockVersion,buildNo:a.buildNo,floorNo:a.floorNo,rtCode:Z.value.rtCode,rtName:Z.value.rtName,price:Z.value.price,vipPrice:Z.value.vipPrice,dayPrices:Z.value.dayPrices,idType:"id_cert",list:[],bkNum:0}):Z.value.checkRoomList.splice(Z.value.checkRoomList.findIndex((e=>e.rNo===a.rNo)),1),!0);var t,a}},{default:m((()=>[c(d(e.rNo),1)])),_:2},1032,["modelValue","onUpdate:modelValue","value","onChange"])])))),128))])),_:1},8,["modelValue"])])])])),[[J,n(X)]])])),_:1},8,["modelValue","title"])])}}});function G(e){const o=e;o.__i18n=o.__i18n||[],o.__i18n.push({locale:"",resource:{en:{checkin:{t:0,b:{t:2,i:[{t:3}],s:"Check-in"}},priceSecrecy:{t:0,b:{t:2,i:[{t:3}],s:"Price Secrecy"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},checkinAndPrintRegistration:{t:0,b:{t:2,i:[{t:3}],s:"Check-in and Print Registration Form"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},roomState:{t:0,b:{t:2,i:[{t:3}],s:"Room Status"}},preOrderedRoom:{t:0,b:{t:2,i:[{t:3}],s:"Pre-booked Room"}},roomsAssigned:{t:0,b:{t:2,i:[{t:3}],s:"Rooms Assigned"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"Rooms"}},maxRooms:{t:0,b:{t:2,i:[{t:3,v:"Maximum rooms exceeded! You can select up to "},{t:4,k:"max"},{t:3,v:" rooms."}]}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"Confirm"}}},"zh-cn":{checkin:{t:0,b:{t:2,i:[{t:3}],s:"办理入住"}},priceSecrecy:{t:0,b:{t:2,i:[{t:3}],s:"房价保密"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},checkinAndPrintRegistration:{t:0,b:{t:2,i:[{t:3}],s:"入住并打印登记单"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},roomState:{t:0,b:{t:2,i:[{t:3}],s:"房态"}},preOrderedRoom:{t:0,b:{t:2,i:[{t:3}],s:"预订单占用房间"}},roomsAssigned:{t:0,b:{t:2,i:[{t:3}],s:"已排房"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"间"}},maxRooms:{t:0,b:{t:2,i:[{t:3,v:"最多选择 "},{t:4,k:"max"},{t:3,v:" 间房间"}]}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"确定"}}},km:{checkin:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅ"}},priceSecrecy:{t:0,b:{t:2,i:[{t:3}],s:"ភាពសម្ងាត់តម្លៃ"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},checkinAndPrintRegistration:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅ និងបោះពុម្ពទម្រង់ចុះឈ្មោះ"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},roomState:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាពបន្ទប់"}},preOrderedRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានកក់មុន"}},roomsAssigned:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានចាត់តាំង"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់"}},maxRooms:{t:0,b:{t:2,i:[{t:3,v:"លើសពីចំនួនបន្ទប់អតិបរមា! អ្នកអាចជ្រើសរើសបានរហូតដល់ "},{t:4,k:"max"},{t:3,v:" បន្ទប់។"}]}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ជាក់"}}}}})}G(E);const J=O(E,[["__scopeId","data-v-77677a41"]]);export{J as default};
//# sourceMappingURL=newArrangeRooms-DW3doDm9.js.map
