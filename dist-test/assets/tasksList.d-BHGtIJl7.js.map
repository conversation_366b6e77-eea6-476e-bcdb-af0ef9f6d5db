{"version": 3, "file": "tasksList.d-BHGtIJl7.js", "sources": ["../../src/views/housekeeping/housekeepingMgmt/components/tasksList.d.ts"], "sourcesContent": ["/** 任务列表 */\r\ndeclare namespace tasksListType {\r\n  interface dictTypeListType {\r\n    /** 房态 */\r\n    roomStatus?: DictDataSimpleRespVO[]\r\n    /** 清扫类型 */\r\n    roomCleanType?: DictDataSimpleRespVO[]\r\n    /** 清扫类型(备份) */\r\n    roomCleanTypeBlack?: DictDataSimpleRespVO[]\r\n    /** 打扫状态 */\r\n    taskState?: DictDataSimpleRespVO[]\r\n  }\r\n  /** 字典类型 */\r\n  interface DictDataSimpleRespVO {\r\n    /** 代码 */\r\n    code?: string\r\n    /** 颜色类型，default、primary、success、info、warning、danger */\r\n    colorType?: string\r\n    /** css 样式 */\r\n    cssClass?: string\r\n    /** 字典类型 */\r\n    dictType?: string\r\n    /** 字典标签 */\r\n    label?: string\r\n    /** 字典键值 */\r\n    value?: string\r\n  }\r\n  /** 集团字典类型 */\r\n  interface GeneralConfigRespVO {\r\n    /** 代码 */\r\n    code?: string\r\n    /** 创建时间 */\r\n    createTime?: string\r\n    /** 货币单位 */\r\n    currencyUnit?: string\r\n    /** 集团代码 */\r\n    gcode?: string\r\n    /** 门店代码;集团配置该字段值为0 */\r\n    hcode?: string\r\n    /** id */\r\n    id?: number\r\n    /** 是否有效;0:无效 1：有效 */\r\n    isEnable?: string\r\n    /** 是否集团配置;0:否 1：是 */\r\n    isG?: string\r\n    /** 收款模式 */\r\n    mode?: string\r\n    /** 名称 */\r\n    name?: string\r\n    /** 父代码;如果当前节点无父节点，值为0 */\r\n    parentCode?: string\r\n    /** 备注 */\r\n    remark?: string\r\n    /** 类别 */\r\n    type?: string\r\n    /** 值 */\r\n    value?: string\r\n    /** 是否禁用 */\r\n    isDisabled?: boolean\r\n  }\r\n  /** 保洁员类型 */\r\n  interface UserSimpleRespVO {\r\n    /** 部门ID */\r\n    deptId?: number | null\r\n    /** 部门名称 */\r\n    deptName?: null | string\r\n    /** 用户编号 */\r\n    id?: number | null\r\n    /** 用户昵称 */\r\n    nickname?: null | string\r\n    /** 用户名称 */\r\n    username?: null | string\r\n  }\r\n  /** 获取楼栋楼层 */\r\n  interface TreeString {\r\n    /** 楼层楼栋代码 */\r\n    code?: string\r\n    /** 是否为楼层：0、楼栋 1、楼层 */\r\n    isFloor?: string\r\n    /** 楼栋或楼层名称 */\r\n    name?: string\r\n    /** 楼层所属楼栋代码 */\r\n    parentCode?: string\r\n    children?: TreeString[]\r\n  }\r\n  /** 房型 */\r\n  interface RoomTypeSimpleRespVO {\r\n    /** 房型代码 */\r\n    rtCode?: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n  }\r\n  /** 获得任务分页数据 */\r\n  interface PageResultTaskRespVO {\r\n    /** 获得任务数据列表 */\r\n    list?: TaskRespVO[]\r\n    /** 总量 */\r\n    total?: number\r\n  }\r\n  /** 获得任务数据列表 */\r\n  interface TaskRespVO extends hgCode {\r\n    /** 审核人账号 */\r\n    auditor?: string\r\n    /** 审核人账号名称 */\r\n    auditorName?: string\r\n    /** 审核备注 */\r\n    auditorRemark?: string\r\n    /** 审核（检查）时间 */\r\n    auditTime?: string\r\n    /** 日期 */\r\n    bizDate?: string\r\n    /** 保洁员 */\r\n    nickname?: string\r\n    /** 房间代码 */\r\n    rCode?: string\r\n    /** 打扫备注 */\r\n    remark?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房态 */\r\n    roomState?: string\r\n    /** 房态名称 */\r\n    roomStateName?: string\r\n    /** 房型代码名称 */\r\n    rtCodeName?: string\r\n    /** 状态 */\r\n    state?: string\r\n    /** 打扫状态 */\r\n    stateName?: string\r\n    /** 标签 */\r\n    tag?: string\r\n    /** 任务代码 */\r\n    taskCode?: string\r\n    /** 任务执行结束时间 */\r\n    taskEndTime?: string\r\n    /** 任务执行开始时间 */\r\n    taskStartTime?: string\r\n    /** 类型 */\r\n    type?: string\r\n    /** 清扫类型名称 */\r\n    typeName?: string\r\n    /** 任务执行用户 */\r\n    username?: string\r\n    /** 是否可以修改关联用户, 0：不能修改，1：能修改 */\r\n    isUpdateUserName?: string\r\n    /** 操作员 */\r\n    creator?: string\r\n    /** 操作员名称 */\r\n    creatorName?: string\r\n  }\r\n  /** 获得房间任务分页 */\r\n  interface PageResultRoomTaskRespVO {\r\n    /** 获得房间任务数据 */\r\n    list?: RoomTaskRespVO[]\r\n    /** 总量 */\r\n    total?: number\r\n  }\r\n  /** 获得房间任务数据 */\r\n  interface RoomTaskRespVO extends RoomTypeSimpleRespVO {\r\n    /** 打扫状态 pending_clean:待打扫，in_progress:打扫中，pending_inspection:待检查，completed:已干净，cancel:已取消 */\r\n    cleanState?: string\r\n    /** 打扫状态名称 */\r\n    cleanStateName?: string\r\n    /** 发行状态， 0未下发，1已下发 */\r\n    issuanceState?: string\r\n    /** 房间打扫用户 */\r\n    nickname?: string\r\n    /** 房间代码 */\r\n    rCode?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房间清扫类型 */\r\n    roomCleanType?: string\r\n    /** 房间清扫类型名称 */\r\n    roomCleanTypeName?: string\r\n    /** 房态 */\r\n    roomState?: string\r\n    /** 任务代码 */\r\n    taskCode?: string\r\n    /** 房间打扫用户 */\r\n    username?: string\r\n    /** 气泡显示与隐藏 */\r\n    isVisible?: boolean\r\n    /** 是否可以修改关联用户, 0：不能修改，1：能修改 */\r\n    isUpdateUserName?: string\r\n  }\r\n  /** 获得房间匹配分页 */\r\n  interface PageResultRoomMatchRespVO {\r\n    /** 获得房间匹配数据列表 */\r\n    list?: RoomMatchRespVO[]\r\n    /** 总量 */\r\n    total?: number\r\n  }\r\n  /** 获得房间匹配数据列表 */\r\n  interface RoomMatchRespVO extends hgCode, RoomTypeSimpleRespVO {\r\n    /** 楼栋代码 */\r\n    buildingCode?: string\r\n    /** 楼栋名称 */\r\n    buildingName?: string\r\n    /** 楼栋号 */\r\n    buildNo?: string\r\n    /** 楼层代码 */\r\n    floorCode?: string\r\n    /** 楼层名称 */\r\n    floorName?: string\r\n    /** 楼层号 */\r\n    floorNo?: string\r\n    /** id */\r\n    id?: number\r\n    /** 是否匹配 */\r\n    isMatch?: string\r\n    /** 房间关联用户昵称 */\r\n    nickname?: string\r\n    /** 房间代码 */\r\n    rCode?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房间关联用户 */\r\n    username?: string\r\n  }\r\n  /** 获取房间号 */\r\n  interface roomListTypes extends hgCode, RoomTypeSimpleRespVO {\r\n    id?: number\r\n    /** 房间代码 */\r\n    rCode?: string\r\n    /** 房号 */\r\n    rNo?: string\r\n    /** 房间状态代码 */\r\n    state?: string\r\n  }\r\n}\r\nexport = tasksListType\r\n"], "names": ["require_tasksList_d_045", "exports", "module", "tasksListType"], "mappings": "qCAuOAA,KAAA,CAAA,iCAAAC,EAAAC,GAAAA,EAASD,QAAAE,aAAA"}