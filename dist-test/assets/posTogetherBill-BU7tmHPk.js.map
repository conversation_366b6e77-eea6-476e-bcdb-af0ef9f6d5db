{"version": 3, "file": "posTogetherBill-BU7tmHPk.js", "sources": ["../../src/views/print/posTogetherBill.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport { computed, onMounted, ref } from 'vue'\r\n\r\nconst props = defineProps({\r\n  billData: {\r\n    type: Object,\r\n    required: true,\r\n  },\r\n})\r\n\r\nonMounted(() => {})\r\n\r\n// 酒店信息\r\nconst hotelInfo = computed(() => {\r\n  return {\r\n    name: props.billData.hname,\r\n    phone: props.billData.frontPhone || '',\r\n    address: props.billData.address || '',\r\n  }\r\n})\r\n\r\n// 当前时间\r\nconst currentTime = ref(props.billData.printDate || new Date().toLocaleString())\r\n\r\n// 计算总计\r\nconst totalConsume = computed(() => {\r\n  return props.billData.consumeTotalFee || 0\r\n})\r\n\r\nconst totalPay = computed(() => {\r\n  return props.billData.payTotalFee || 0\r\n})\r\n\r\n// 按日期分组消费明细\r\nconst consumeByDate = computed(() => {\r\n  const result = {}\r\n  if (props.billData?.consumptionDetails && props.billData.consumptionDetails.length > 0) {\r\n    props.billData.consumptionDetails.forEach((item) => {\r\n      const date = item.createTime.split(' ')[0]\r\n      if (!result[date]) {\r\n        result[date] = []\r\n      }\r\n      result[date].push(item)\r\n    })\r\n  }\r\n  return result\r\n})\r\n\r\n// 按日期分组付款明细\r\nconst payByDate = computed(() => {\r\n  const result = {}\r\n  if (props.billData?.paymentDetails && props.billData.paymentDetails.length > 0) {\r\n    props.billData.paymentDetails.forEach((item) => {\r\n      const date = item.createTime.split(' ')[0]\r\n      if (!result[date]) {\r\n        result[date] = []\r\n      }\r\n      result[date].push(item)\r\n    })\r\n  }\r\n  return result\r\n})\r\n\r\n// 格式化日期时间，去掉秒\r\nfunction formatDateTime(dateStr) {\r\n  if (!dateStr) {\r\n    return ''\r\n  }\r\n  try {\r\n    // 尝试将日期格式化为 yyyy-MM-dd HH:mm 格式\r\n    const date = new Date(dateStr)\r\n    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\r\n  } catch {\r\n    // 如果格式化失败，返回原始字符串\r\n    return dateStr\r\n  }\r\n}\r\n\r\n// 计算每个日期的消费小计\r\nfunction getDateSubtotal(items) {\r\n  const total = items.reduce((sum, item) => sum + item.fee, 0)\r\n  // 修复浮点数精度问题，保留两位小数\r\n  return Number.parseFloat(total.toFixed(2))\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div id=\"pos-bill-content\" class=\"bill-content\">\r\n    <div class=\"pos-bill-container\">\r\n      <div class=\"hotel-title\">{{ hotelInfo.name }}</div>\r\n      <div class=\"form-title\">宾客账单</div>\r\n      <div class=\"bill-info\">\r\n        <div>NO:{{ props.billData.orderNo }}</div>\r\n        <div>打印时间:{{ currentTime }}</div>\r\n      </div>\r\n\r\n      <table class=\"info-table\">\r\n        <tbody>\r\n          <tr>\r\n            <th>客人姓名</th>\r\n            <td>{{ props.billData.name }}</td>\r\n            <th>房号</th>\r\n            <td>{{ props.billData.rNo }}</td>\r\n          </tr>\r\n          <tr>\r\n            <th>房型</th>\r\n            <td colspan=\"3\">{{ props.billData.rtName }}</td>\r\n          </tr>\r\n          <tr>\r\n            <th>确认金额</th>\r\n            <td>{{ props.billData.balance || '' }}</td>\r\n            <th>积分余额</th>\r\n            <td>{{ props.billData.point || '' }}</td>\r\n          </tr>\r\n          <tr>\r\n            <th>入住时间</th>\r\n            <td colspan=\"3\">{{ formatDateTime(props.billData.checkinTime) }}</td>\r\n          </tr>\r\n          <tr>\r\n            <th>离店时间</th>\r\n            <td colspan=\"3\">{{ formatDateTime(props.billData.checkoutTime) }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <!-- 消费明细表格 -->\r\n      <table class=\"detail-table\">\r\n        <thead>\r\n          <tr>\r\n            <th>项目</th>\r\n            <th>时间</th>\r\n            <th>金额</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <!-- 按日期分组显示消费明细 -->\r\n          <template v-for=\"(items, date, index) in consumeByDate\" :key=\"`consume-date-${index}`\">\r\n            <tr>\r\n              <td colspan=\"3\">{{ date }}</td>\r\n            </tr>\r\n            <template v-for=\"(item, idx) in items\" :key=\"`consume-${index}-${idx}`\">\r\n              <tr>\r\n                <td class=\"project-name\">{{ item?.subName || '' }}</td>\r\n                <td>{{ item.createTime }}</td>\r\n                <td align=\"right\">{{ item.fee }}</td>\r\n              </tr>\r\n            </template>\r\n            <tr>\r\n              <td colspan=\"2\" align=\"right\">小计:</td>\r\n              <td align=\"right\">{{ getDateSubtotal(items) }}</td>\r\n            </tr>\r\n          </template>\r\n          <!-- 消费合计 -->\r\n          <tr class=\"total-row\">\r\n            <td colspan=\"2\" align=\"right\">消费合计:</td>\r\n            <td align=\"right\">{{ totalConsume }}</td>\r\n          </tr>\r\n          <!-- 项目、时间、金额表头 -->\r\n          <tr>\r\n            <th>项目</th>\r\n            <th>时间</th>\r\n            <th>金额</th>\r\n          </tr>\r\n          <!-- 按日期分组显示付款明细 -->\r\n          <template v-for=\"(items, date, index) in payByDate\" :key=\"`pay-date-${index}`\">\r\n            <tr>\r\n              <td colspan=\"3\">{{ date }}</td>\r\n            </tr>\r\n            <template v-for=\"(item, idx) in items\" :key=\"`pay-${index}-${idx}`\">\r\n              <tr>\r\n                <td class=\"project-name\">{{ item?.subName || '' }}</td>\r\n                <td>{{ item.createTime }}</td>\r\n                <td align=\"right\">{{ item.fee }}</td>\r\n              </tr>\r\n            </template>\r\n            <tr>\r\n              <td colspan=\"2\" align=\"right\" class=\"subtotal-row\">小计:</td>\r\n              <td align=\"right\">{{ getDateSubtotal(items) }}</td>\r\n            </tr>\r\n          </template>\r\n          <!-- 付款合计 -->\r\n          <tr class=\"total-row\">\r\n            <td colspan=\"2\" align=\"right\">付款合计:</td>\r\n            <td align=\"right\">{{ totalPay }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <!-- 备注 -->\r\n      <div class=\"remark\">\r\n        <div>备注:</div>\r\n        <div>{{ props.billData.info || '' }}</div>\r\n      </div>\r\n      <div class=\"footer\">\r\n        <div>酒店电话: {{ hotelInfo.phone }}</div>\r\n        <div>酒店地址: {{ hotelInfo.address }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.bill-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 10px;\r\n}\r\n\r\n.pos-bill-container {\r\n  width: 76mm;\r\n  margin: 0 auto;\r\n  padding: 3mm;\r\n  font-size: 12px;\r\n  border: 1px solid #000;\r\n  color: black;\r\n}\r\n\r\n.hotel-title {\r\n  text-align: center;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.form-title {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.bill-info {\r\n  margin-bottom: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.info-table,\r\n.detail-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  margin-bottom: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.project-name {\r\n  font-weight: bold;\r\n}\r\n\r\n.subtotal-row {\r\n  font-weight: 600;\r\n}\r\n\r\n.total-row {\r\n  font-weight: bold;\r\n}\r\n.info-table th,\r\n.info-table td,\r\n.detail-table th,\r\n.detail-table td {\r\n  border: 1px solid #000;\r\n  padding: 2px;\r\n}\r\n\r\n.info-table th {\r\n  width: 25%;\r\n  text-align: left;\r\n}\r\n\r\n.detail-table th {\r\n  text-align: center;\r\n}\r\n\r\n.remark {\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.footer {\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.footer > div {\r\n  margin-bottom: 2px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "onMounted", "hotelInfo", "computed", "name", "billData", "hname", "phone", "frontPhone", "address", "currentTime", "ref", "printDate", "Date", "toLocaleString", "totalConsume", "consumeTotalFee", "totalPay", "payTotalFee", "consumeByDate", "result", "_a", "consumptionDetails", "length", "for<PERSON>ach", "item", "date", "createTime", "split", "push", "payByDate", "paymentDetails", "formatDateTime", "dateStr", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getDateSubtotal", "items", "total", "reduce", "sum", "fee", "Number", "parseFloat", "toFixed"], "mappings": "0uBAGA,MAAMA,EAAQC,EAOdC,GAAU,SAGJ,MAAAC,EAAYC,GAAS,KAClB,CACLC,KAAML,EAAMM,SAASC,MACrBC,MAAOR,EAAMM,SAASG,YAAc,GACpCC,QAASV,EAAMM,SAASI,SAAW,OAKjCC,EAAcC,EAAIZ,EAAMM,SAASO,gBAAiBC,MAAOC,kBAGzDC,EAAeZ,GAAS,IACrBJ,EAAMM,SAASW,iBAAmB,IAGrCC,EAAWd,GAAS,IACjBJ,EAAMM,SAASa,aAAe,IAIjCC,EAAgBhB,GAAS,WAC7B,MAAMiB,EAAS,CAAC,EAUT,OATH,OAAAC,EAAAtB,EAAMM,eAAU,EAAAgB,EAAAC,qBAAsBvB,EAAMM,SAASiB,mBAAmBC,OAAS,GACnFxB,EAAMM,SAASiB,mBAAmBE,SAASC,IACzC,MAAMC,EAAOD,EAAKE,WAAWC,MAAM,KAAK,GACnCR,EAAOM,KACHN,EAAAM,GAAQ,IAEVN,EAAAM,GAAMG,KAAKJ,EAAI,IAGnBL,CAAA,IAIHU,EAAY3B,GAAS,WACzB,MAAMiB,EAAS,CAAC,EAUT,OATH,OAAAC,EAAAtB,EAAMM,eAAU,EAAAgB,EAAAU,iBAAkBhC,EAAMM,SAAS0B,eAAeR,OAAS,GAC3ExB,EAAMM,SAAS0B,eAAeP,SAASC,IACrC,MAAMC,EAAOD,EAAKE,WAAWC,MAAM,KAAK,GACnCR,EAAOM,KACHN,EAAAM,GAAQ,IAEVN,EAAAM,GAAMG,KAAKJ,EAAI,IAGnBL,CAAA,IAIT,SAASY,EAAeC,GACtB,IAAKA,EACI,MAAA,GAEL,IAEI,MAAAP,EAAO,IAAIb,KAAKoB,GACf,MAAA,GAAGP,EAAKQ,iBAAiBC,OAAOT,EAAKU,WAAa,GAAGC,SAAS,EAAG,QAAQF,OAAOT,EAAKY,WAAWD,SAAS,EAAG,QAAQF,OAAOT,EAAKa,YAAYF,SAAS,EAAG,QAAQF,OAAOT,EAAKc,cAAcH,SAAS,EAAG,MAAI,CAC3M,MAEC,OAAAJ,CAAA,CACT,CAIF,SAASQ,EAAgBC,GACjB,MAAAC,EAAQD,EAAME,QAAO,CAACC,EAAKpB,IAASoB,EAAMpB,EAAKqB,KAAK,GAE1D,OAAOC,OAAOC,WAAWL,EAAMM,QAAQ,GAAE"}