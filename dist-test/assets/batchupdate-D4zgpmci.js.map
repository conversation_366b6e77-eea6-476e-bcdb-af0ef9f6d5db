{"version": 3, "file": "batchupdate-D4zgpmci.js", "sources": ["../../src/views/merchant/base/room/components/DetailForm/batchupdate.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"batchEdit\": \"Batch Edit\",\r\n    \"buildingFloor\": \"Building Floor\",\r\n    \"roomNo\": \"Room No\",\r\n    \"roomType\": \"Room Type\",\r\n    \"extension\": \"Extension\",\r\n    \"roomFeature\": \"Room Feature\",\r\n    \"roomDescription\": \"Room Description\",\r\n    \"pleaseSelectFloor\": \"Please select floor\",\r\n    \"pleaseEnterRoomNo\": \"Please enter room no\",\r\n    \"pleaseSelectRoomType\": \"Please select room type\",\r\n    \"editSuccess\": \"Edit successful\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"batchEdit\": \"批量修改\",\r\n    \"buildingFloor\": \"楼栋楼层\",\r\n    \"roomNo\": \"房号\",\r\n    \"roomType\": \"房型\",\r\n    \"extension\": \"分机号\",\r\n    \"roomFeature\": \"房间特征\",\r\n    \"roomDescription\": \"房间描述\",\r\n    \"pleaseSelectFloor\": \"请选择楼层\",\r\n    \"pleaseEnterRoomNo\": \"请输入房号\",\r\n    \"pleaseSelectRoomType\": \"请选择房型\",\r\n    \"editSuccess\": \"编辑成功\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\"\r\n  },\r\n  \"km\": {\r\n    \"batchEdit\": \"កែសម្រួលជាក្រុម\",\r\n    \"buildingFloor\": \"អាគារ និងជាន់\",\r\n    \"roomNo\": \"លេខបន្ទប់\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"extension\": \"លេខផ្ទាល់\",\r\n    \"roomFeature\": \"លក្ខណៈបន្ទប់\",\r\n    \"roomDescription\": \"ការពិពណ៌នាបន្ទប់\",\r\n    \"pleaseSelectFloor\": \"សូមជ្រើសរើសជាន់\",\r\n    \"pleaseEnterRoomNo\": \"សូមបញ្ចូលលេខបន្ទប់\",\r\n    \"pleaseSelectRoomType\": \"សូមជ្រើសរើសប្រភេទបន្ទប់\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { BuildFloorModel, DictDataModel, RoomModel, RoomTypeModel } from '@/models/index'\r\n\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { buildingFloorApi } from '@/api/modules/index'\r\nimport { DICT_TYPE_ROOM_FEATURE } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    rCodes: string[]\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  editRoom: [state: boolean]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  features: [] as string[],\r\n\r\n  formDate: [] as RoomModel[],\r\n})\r\nconst myVisible = ref(props.modelValue)\r\nconst features = ref<DictDataModel[]>([])\r\nconst rts = ref<RoomTypeModel[]>([])\r\nconst buildFloors = ref<BuildFloorModel[]>([])\r\nconst constants: DictDataModel[] = JSON.parse(userStore.constants)\r\nonMounted(() => {\r\n  getBuildFloor()\r\n  // getRts()\r\n  // getFeatures()\r\n  // getRooms()\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  rNo: [{ required: true, message: t('pleaseEnterRoomNo'), trigger: 'blur' }],\r\n  floorCode: [{ required: true, message: t('pleaseSelectFloor'), trigger: 'blur' }],\r\n})\r\n\r\nfunction getBuildFloor() {\r\n  buildingFloorApi.flatList({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {\r\n    buildFloors.value = res.data\r\n  })\r\n}\r\n\r\nfunction getFeatures() {\r\n  constants.forEach((item: DictDataModel) => {\r\n    if (item.typeCode === DICT_TYPE_ROOM_FEATURE) {\r\n      features.value.push({ code: item.code, name: item.name })\r\n    }\r\n  })\r\n}\r\n\r\nfunction getRts() {\r\n  rtApi\r\n    .list({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      isEnable: true,\r\n      isVirtual: false,\r\n    })\r\n    .then((res: any) => {\r\n      rts.value = res.data.list\r\n    })\r\n}\r\n\r\nfunction getRooms() {\r\n  loading.value = true\r\n  const body = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    rCodes: props.rCodes,\r\n  }\r\n  api.listByrCodes(body).then((res: any) => {\r\n    form.value.formDate = res.data\r\n    loading.value = false\r\n  })\r\n}\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        api.edit(form.value.formDate).then(() => {\r\n          ElMessage.success({\r\n            message: t('editSuccess'),\r\n            center: true,\r\n          })\r\n          emits('editRoom', true)\r\n          onCancel()\r\n        })\r\n      }\r\n    })\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"t('batchEdit')\" :close-on-click-modal=\"false\" append-to-body destroy-on-close @closed=\"emits('update:modelValue', false)\">\r\n    <el-form ref=\"formRef\" :model=\"form.formDate\" :rules=\"formRules\">\r\n      <el-table v-loading=\"loading\" class=\"list-table\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" :data=\"form.formDate\" highlight-current-row height=\"100%\">\r\n        <el-table-column :label=\"t('buildingFloor')\">\r\n          <template #default=\"scope\">\r\n            <el-form-item :rules=\"[{ required: true, message: t('pleaseSelectFloor') }]\">\r\n              <el-select v-model=\"scope.row.floorCode\">\r\n                <el-option v-for=\"f in buildFloors\" :key=\"f.code\" :label=\"f.name\" :value=\"f.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('roomNo')\">\r\n          <template #default=\"scope\">\r\n            <el-form-item :rules=\"[{ required: true, message: t('pleaseEnterRoomNo') }]\">\r\n              <el-input v-model=\"scope.row.rNo\" />\r\n            </el-form-item>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('roomType')\">\r\n          <template #default=\"scope\">\r\n            <el-form-item :rules=\"[{ required: true, message: t('pleaseSelectRoomType') }]\">\r\n              <el-select v-model=\"scope.row.rtCode\">\r\n                <el-option v-for=\"rt in rts\" :key=\"rt.rtCode\" :label=\"rt.rtName\" :value=\"rt.rtCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('extension')\">\r\n          <template #default=\"scope\">\r\n            <el-form-item>\r\n              <el-input v-model=\"scope.row.extNum\" maxlength=\"30\" />\r\n            </el-form-item>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('roomFeature')\">\r\n          <template #default=\"scope\">\r\n            <el-form-item>\r\n              <el-select v-model=\"scope.row.feature\" multiple collapse-tags collapse-tags-tooltip>\r\n                <el-option v-for=\"ft in features\" :key=\"ft.code\" :label=\"ft.name\" :value=\"ft.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('roomDescription')\">\r\n          <template #default=\"scope\">\r\n            <el-form-item>\r\n              <el-input v-model=\"scope.row.remark\" :rows=\"2\" type=\"textarea\" maxlength=\"255\" />\r\n            </el-form-item>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" @click=\"onSubmit\">\r\n        {{ t('save') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "features", "formDate", "myVisible", "modelValue", "rts", "buildFloors", "JSON", "parse", "constants", "onMounted", "buildingFloorApi", "flatList", "gcode", "hcode", "then", "res", "value", "data", "formRules", "rNo", "required", "message", "trigger", "floorCode", "onSubmit", "validate", "valid", "api", "edit", "ElMessage", "success", "center", "onCancel"], "mappings": "m7BA0DA,MAAMA,EAAQC,EASRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,SAAU,GAEVC,SAAU,KAENC,EAAYL,EAAIT,EAAMe,YACtBH,EAAWH,EAAqB,IAChCO,EAAMP,EAAqB,IAC3BQ,EAAcR,EAAuB,IACHS,KAAAC,MAAMb,EAAUc,WACxDC,GAAU,KAaRC,EAAiBC,SAAS,CAAEC,MAAOlB,EAAUkB,MAAOC,MAAOnB,EAAUmB,QAASC,MAAMC,IAClFV,EAAYW,MAAQD,EAAIE,IAAA,GAbZ,IAMhB,MAAMC,EAAYrB,EAAe,CAC/BsB,IAAK,CAAC,CAAEC,UAAU,EAAMC,QAAS7B,EAAE,qBAAsB8B,QAAS,SAClEC,UAAW,CAAC,CAAEH,UAAU,EAAMC,QAAS7B,EAAE,qBAAsB8B,QAAS,WA2C1E,SAASE,IACP1B,EAAQkB,OACNlB,EAAQkB,MAAMS,UAAUC,IAClBA,GACFC,IAAIC,KAAK7B,EAAKiB,MAAMf,UAAUa,MAAK,KACjCe,EAAUC,QAAQ,CAChBT,QAAS7B,EAAE,eACXuC,QAAQ,IAEVzC,EAAM,YAAY,GACT0C,GAAA,GACV,GAEJ,CAEL,SAASA,IACP9B,EAAUc,OAAQ,CAAA"}