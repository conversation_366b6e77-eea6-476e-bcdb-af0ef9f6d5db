{"version": 3, "file": "couponConfig.api-DtISSIXY.js", "sources": ["../../src/api/modules/marketing/coupon/couponConfig.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n/**\r\n * @description: 优惠券配置\r\n */\r\nexport default {\r\n  /**\r\n   * 优惠券配置明细\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  // detail: (gcode: string) =>\r\n  //   api.get(\"marketing/coupon/config/detail\", {\r\n  //     params: {\r\n  //       gcode,\r\n  //     },\r\n  //   }),\r\n  detail: (data: any) => api.get('/admin-api/marketing/coupon-config/get', { params: data }),\r\n  /**\r\n   * 修改优惠券配置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  edit: (data: any) => api.put('/admin-api/marketing/coupon-config/update', data),\r\n}\r\n"], "names": ["couponConfigApi", "detail", "data", "api", "get", "params", "edit", "put"], "mappings": "mCAIA,MAAeA,EAAA,CAYbC,OAASC,GAAcC,EAAIC,IAAI,yCAA0C,CAAEC,OAAQH,IAMnFI,KAAOJ,GAAcC,EAAII,IAAI,4CAA6CL"}