{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-CJvwvkTS.js", "sources": ["../../src/assets/images/logo.png", "../../src/layouts/components/Logo/index.vue"], "sourcesContent": ["export default \"__VITE_ASSET__DacSPZ0B__\"", "<script setup lang=\"ts\">\r\nimport imgLogo from '@/assets/images/logo.png'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport storage from '@/utils/storage'\r\nimport eventBus from '@/views/room/realtime/event-bus.ts'\r\nimport dayjs from 'dayjs'\r\nimport { onMounted } from 'vue'\r\nimport ChangeHotel from '../Topbar/Toolbar/changeHotel.vue'\r\n\r\ndefineOptions({\r\n  name: 'Logo',\r\n})\r\n\r\nwithDefaults(\r\n  defineProps<{\r\n    showLogo?: boolean\r\n    showTitle?: boolean\r\n  }>(),\r\n  {\r\n    showLogo: true,\r\n    showTitle: true,\r\n  }\r\n)\r\nconst settingsStore = useSettingsStore()\r\nconst logo = ref(imgLogo)\r\n\r\n// 使用响应式数据来监听 localStorage 的变化\r\nconst fa_shiftName = ref(storage.local.has('shiftName') ? storage.local.get('shiftName') : '') // 班次\r\nconst fa_bizDate = ref(storage.local.has('bizDate') ? storage.local.get('bizDate') : '') // 营业时间\r\n\r\n// 监听 localStorage 变化，实时更新显示\r\nonMounted(() => {\r\n  // 监听 storage 事件（跨标签页同步）\r\n  window.addEventListener('storage', handleStorageChange)\r\n\r\n  // 定时检查 localStorage 变化（同标签页内的变化）\r\n  const interval = setInterval(() => {\r\n    const currentShiftName = storage.local.has('shiftName') ? storage.local.get('shiftName') : ''\r\n    const currentBizDate = storage.local.has('bizDate') ? storage.local.get('bizDate') : ''\r\n\r\n    if (currentShiftName !== fa_shiftName.value) {\r\n      fa_shiftName.value = currentShiftName\r\n    }\r\n    if (currentBizDate !== fa_bizDate.value) {\r\n      fa_bizDate.value = currentBizDate\r\n    }\r\n  }, 1000) // 每秒检查一次\r\n\r\n  // 组件卸载时清理定时器\r\n  onUnmounted(() => {\r\n    clearInterval(interval)\r\n    window.removeEventListener('storage', handleStorageChange)\r\n  })\r\n})\r\n\r\nfunction handleStorageChange(e: StorageEvent) {\r\n  if (e.key === 'shiftName' && e.newValue) {\r\n    fa_shiftName.value = e.newValue\r\n  }\r\n  if (e.key === 'bizDate' && e.newValue) {\r\n    fa_bizDate.value = e.newValue\r\n  }\r\n}\r\n/**\r\n * 上班时间\r\n * @param time 时间\r\n */\r\nfunction fa_bizDateType(time: string | null) {\r\n  if (time) {\r\n    return dayjs(time).format('YYYY/MM/DD')\r\n  }\r\n  return ''\r\n}\r\n// 监听营业日期更新事件\r\nonMounted(() => {\r\n  eventBus.on('bizDateUpdated', (newBizDate: string) => {\r\n    fa_bizDate.value = newBizDate\r\n  })\r\n})\r\n\r\nconst to = computed(() => (settingsStore.settings.home.enable ? settingsStore.settings.home.fullPath : ''))\r\n\r\nconst classComputed = computed(() => ({\r\n  'cursor-pointer': settingsStore.settings.home.enable,\r\n}))\r\n</script>\r\n\r\n<template>\r\n  <div class=\"flex items-center justify-center\">\r\n    <RouterLink :to=\"to\" class=\"h-[var(--g-sidebar-logo-height)] w-inherit flex-center gap-2 px-3 text-inherit no-underline\" :class=\"classComputed\">\r\n      <img v-if=\"showLogo\" :src=\"logo\" class=\"logo h-[30px] w-[30px] object-contain\" />\r\n    </RouterLink>\r\n    <div v-if=\"showTitle\" class=\"ml-[10px] block truncate\">\r\n      <ChangeHotel />\r\n      <div class=\"text-[12px]\">\r\n        {{ fa_bizDateType(fa_bizDate) }}<span class=\"ml-[10px] !text-[12px]\">{{ fa_shiftName }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n"], "names": ["settingsStore", "useSettingsStore", "logo", "ref", "fa_shiftName", "storage", "local", "has", "get", "fa_bizDate", "handleStorageChange", "e", "key", "newValue", "value", "onMounted", "window", "addEventListener", "interval", "setInterval", "currentShiftName", "currentBizDate", "onUnmounted", "clearInterval", "removeEventListener", "eventBus", "on", "newBizDate", "to", "computed", "settings", "home", "enable", "fullPath", "classComputed", "time", "dayjs", "format"], "mappings": "2PAAA,2RCuBA,MAAMA,EAAgBC,IAChBC,EAAOC,EDxBE,6BC2BTC,EAAeD,EAAIE,EAAQC,MAAMC,IAAI,aAAeF,EAAQC,MAAME,IAAI,aAAe,IACrFC,EAAaN,EAAIE,EAAQC,MAAMC,IAAI,WAAaF,EAAQC,MAAME,IAAI,WAAa,IA2BrF,SAASE,EAAoBC,GACb,cAAVA,EAAEC,KAAuBD,EAAEE,WAC7BT,EAAaU,MAAQH,EAAEE,UAEX,YAAVF,EAAEC,KAAqBD,EAAEE,WAC3BJ,EAAWK,MAAQH,EAAEE,SACvB,CA9BFE,GAAU,KAEDC,OAAAC,iBAAiB,UAAWP,GAG7B,MAAAQ,EAAWC,aAAY,KACrB,MAAAC,EAAmBf,EAAQC,MAAMC,IAAI,aAAeF,EAAQC,MAAME,IAAI,aAAe,GACrFa,EAAiBhB,EAAQC,MAAMC,IAAI,WAAaF,EAAQC,MAAME,IAAI,WAAa,GAEjFY,IAAqBhB,EAAaU,QACpCV,EAAaU,MAAQM,GAEnBC,IAAmBZ,EAAWK,QAChCL,EAAWK,MAAQO,EAAA,GAEpB,KAGHC,GAAY,KACVC,cAAcL,GACPF,OAAAQ,oBAAoB,UAAWd,EAAmB,GAC1D,IAsBHK,GAAU,KACCU,EAAAC,GAAG,kBAAmBC,IAC7BlB,EAAWK,MAAQa,CAAA,GACpB,IAGG,MAAAC,EAAKC,GAAS,IAAO7B,EAAc8B,SAASC,KAAKC,OAAShC,EAAc8B,SAASC,KAAKE,SAAW,KAEjGC,EAAgBL,GAAS,KAAO,CACpC,iBAAkB7B,EAAc8B,SAASC,KAAKC,0YAhBxBG,OAClBA,EACKC,EAAMD,GAAME,OAAO,cAErB,8CAJT,IAAwBF"}