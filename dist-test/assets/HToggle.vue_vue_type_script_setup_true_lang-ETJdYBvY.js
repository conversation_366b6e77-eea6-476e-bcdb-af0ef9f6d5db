import{b as e,B as l,d as a,bf as t,ap as n,A as o,y as s,D as u,b9 as i,bb as r,bX as d,ba as f,bd as c,be as v,F as p,bh as m,a1 as b,a2 as y,o as h,e as g,w as k,g as w,a7 as x,R as I,u as V,_ as S}from"./index-CkEhI1Zk.js";import{s as B}from"./use-resolve-button-type-CUES3UUR.js";let C=Symbol("GroupContext"),N=a({name:"Switch",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(a,{emit:b,attrs:y,slots:h,expose:g}){var k;let w=null!=(k=a.id)?k:`headlessui-switch-${t()}`,x=n(C,null),[I,V]=function(a,t,n){let o=e(null==n?void 0:n.value),s=l((()=>void 0!==a.value));return[l((()=>s.value?a.value:o.value)),function(e){return s.value||(o.value=e),null==t?void 0:t(e)}]}(l((()=>a.modelValue)),(e=>b("update:modelValue",e)),l((()=>a.defaultChecked)));function S(){V(!I.value)}let N=e(null),T=null===x?N:x.switchRef,_=B(l((()=>({as:a.as,type:y.type}))),T);function j(e){e.preventDefault(),S()}function D(e){e.key===m.Space?(e.preventDefault(),S()):e.key===m.Enter&&function(e){var l,a;let t=null!=(l=null==e?void 0:e.form)?l:e.closest("form");if(t){for(let l of t.elements)if(l!==e&&("INPUT"===l.tagName&&"submit"===l.type||"BUTTON"===l.tagName&&"submit"===l.type||"INPUT"===l.nodeName&&"image"===l.type))return void l.click();null==(a=t.requestSubmit)||a.call(t)}}(e.currentTarget)}function P(e){e.preventDefault()}g({el:T,$el:T});let U=l((()=>{var e,l;return null==(l=null==(e=o(T))?void 0:e.closest)?void 0:l.call(e,"form")}));return s((()=>{u([U],(()=>{if(U.value&&void 0!==a.defaultChecked)return U.value.addEventListener("reset",e),()=>{var l;null==(l=U.value)||l.removeEventListener("reset",e)};function e(){V(a.defaultChecked)}}),{immediate:!0})})),()=>{let{name:e,value:l,form:t,tabIndex:n,...o}=a,s={checked:I.value},u={id:w,ref:T,role:"switch",type:_.value,tabIndex:-1===n?0:n,"aria-checked":I.value,"aria-labelledby":null==x?void 0:x.labelledby.value,"aria-describedby":null==x?void 0:x.describedby.value,onClick:j,onKeyup:D,onKeypress:P};return i(p,[null!=e&&null!=I.value?i(r,d({features:f.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:I.value,form:t,disabled:o.disabled,name:e,value:l})):null,c({ourProps:u,theirProps:{...y,...v(o,["modelValue","defaultChecked"])},slot:s,attrs:y,slots:h,name:"Switch"})])}}});const T={class:"absolute inset-0 h-full w-full flex items-center justify-center"},_=a({__name:"HToggle",props:b({disabled:{type:Boolean,default:!1},onIcon:{},offIcon:{}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const l=y(e,"modelValue");return(e,a)=>{const t=S;return h(),g(V(N),{modelValue:l.value,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),disabled:e.disabled,class:x(["relative h-5 w-10 inline-flex flex-shrink-0 cursor-pointer border-2 border-transparent rounded-full p-0 vertical-middle disabled-cursor-not-allowed disabled-opacity-50 focus-outline-none focus-visible-ring-2 focus-visible-ring-offset-2 focus-visible-ring-offset-white dark-focus-visible-ring-offset-gray-900",[l.value?"bg-ui-primary":"bg-stone-3 dark-bg-stone-7"]])},{default:k((()=>[w("span",{class:x(["pointer-events-none relative inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition-margin duration-200 ease-in-out dark-bg-dark",[l.value?"ms-5":"ms-0"]])},[w("span",T,[l.value&&e.onIcon||!l.value&&e.offIcon?(h(),g(t,{key:0,name:l.value?e.onIcon:e.offIcon,class:"h-3 w-3 text-stone-7 dark-text-stone-3"},null,8,["name"])):I("",!0)])],2)])),_:1},8,["modelValue","disabled","class"])}}});export{_};
//# sourceMappingURL=HToggle.vue_vue_type_script_setup_true_lang-ETJdYBvY.js.map
