# 预订按钮功能测试

## 修改内容总结

### 1. 添加国际化文本
- 英文: "booking": "Booking"
- 中文: "booking": "预订"  
- 柬埔寨语: "booking": "កក់"

### 2. 导入预订组件
```javascript
import IndividualBooking from '@/views/room/booking/individual.vue'
```

### 3. 添加控制变量和函数
```javascript
// 添加预订弹窗控制变量
const bookingVisible = ref(false)

/** 弹出预订窗口 */
function booking() {
  bookingVisible.value = true
}

/** 预订成功后的处理 */
function handleBookingSuccess() {
  bookingVisible.value = false
  // 可以在这里添加其他成功后的处理逻辑
  ElMessage.success('预订成功')
}
```

### 4. 添加预订按钮
在租借按钮右边添加了预订按钮：
```html
<el-button type="primary" plain style="margin-left: 10px" @click="booking">
  {{ t('booking') }}
</el-button>
```

### 5. 添加预订弹窗组件
```html
<!-- 添加预订弹窗 -->
<IndividualBooking v-model="bookingVisible" @success="handleBookingSuccess" />
```

## 功能说明

1. **预订按钮位置**: 位于租借按钮的右边
2. **显示条件**: 只有在订单状态为 CHECK_IN 时才显示
3. **点击行为**: 点击后弹出个人预订界面
4. **成功回调**: 预订成功后关闭弹窗并显示成功消息

## 测试步骤

1. 打开订单详情页面
2. 确认租借按钮右边出现了"预订"按钮
3. 点击预订按钮，确认弹出预订界面
4. 测试预订功能是否正常工作
5. 确认预订成功后弹窗关闭并显示成功消息

## 注意事项

- 预订按钮只在入住状态(CHECK_IN)下显示
- 使用了现有的个人预订组件，保持了界面的一致性
- 添加了多语言支持
- 遵循了现有的代码风格和结构
