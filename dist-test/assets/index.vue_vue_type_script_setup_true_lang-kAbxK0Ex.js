import{d as e,W as s,u as n,o as r,c as a,f as l,R as i,_ as o}from"./index-CkEhI1Zk.js";import{u as c}from"./index-CDbn0nBx.js";const t=e({name:"Fullscreen",__name:"index",setup(e){const t=s(),{isFullscreen:u,toggle:m}=c();return(e,s)=>{const c=o;return"pc"===n(t).mode?(r(),a("span",{key:0,class:"flex-center cursor-pointer p-2",onClick:s[0]||(s[0]=(...e)=>n(m)&&n(m)(...e))},[l(c,{name:n(u)?"i-ri:fullscreen-exit-line":"i-ri:fullscreen-line"},null,8,["name"])])):i("",!0)}}});export{t as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-kAbxK0Ex.js.map
