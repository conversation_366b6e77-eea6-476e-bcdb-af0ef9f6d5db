import o from"./HDropdown-7tWwMFON.js";import{d as s,o as r,e,w as a,f as n,_ as t}from"./index-CkEhI1Zk.js";import p from"./panel-DLKJVaSe.js";const i=s({name:"Favorites",__name:"index",setup:s=>(s,i)=>{const m=t,f=o;return r(),e(f,{class:"flex-center cursor-pointer p-2"},{dropdown:a((()=>[n(p)])),default:a((()=>[n(m,{name:"i-uiw:star-off"})])),_:1})}});export{i as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-BhI9YChL.js.map
