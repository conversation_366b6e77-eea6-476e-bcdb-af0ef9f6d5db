{"version": 3, "file": "rentReturn-Bn7G8O-o.js", "sources": ["../../src/views/room/goods/rent/components/DetailForm/rentReturn.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"rentInfo\": \"Rent Information\",\r\n    \"returnInfo\": \"Return Information\",\r\n    \"rentVoucherNumber\": \"Rent Voucher No.\",\r\n    \"rentItem\": \"Rent Item\",\r\n    \"roomNumber\": \"Room No.\",\r\n    \"guestName\": \"Guest Name\",\r\n    \"rentPrice\": \"Rent Price\",\r\n    \"rentRemark\": \"Rent Remark\",\r\n    \"returnerName\": \"Returner Name\",\r\n    \"returnRemark\": \"Return Remark\",\r\n    \"yuan\": \"Yuan\",\r\n    \"none\": \"None\",\r\n    \"pleaseEnterReturnerName\": \"Please enter returner name\",\r\n    \"pleaseEnterReturnRemarkPlaceholder\": \"Please enter return remark, such as item condition, damage, etc.\",\r\n    \"returnSuccess\": \"Return successful\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"rentInfo\": \"租借信息\",\r\n    \"returnInfo\": \"归还信息\",\r\n    \"rentVoucherNumber\": \"租借凭证号\",\r\n    \"rentItem\": \"租借物品\",\r\n    \"roomNumber\": \"房号\",\r\n    \"guestName\": \"客人姓名\",\r\n    \"rentPrice\": \"租金\",\r\n    \"rentRemark\": \"租借备注\",\r\n    \"returnerName\": \"归还人姓名\",\r\n    \"returnRemark\": \"归还备注\",\r\n    \"yuan\": \"元\",\r\n    \"none\": \"无\",\r\n    \"pleaseEnterReturnerName\": \"请输入归还人姓名\",\r\n    \"pleaseEnterReturnRemarkPlaceholder\": \"请输入归还备注，如物品状态、损坏情况等\",\r\n    \"returnSuccess\": \"归还成功\"\r\n  },\r\n  \"km\": {\r\n    \"rentInfo\": \"ព័ត៌មានអំពីការជួល\",\r\n    \"returnInfo\": \"ព័ត៌មានអំពីការប្រគល់វិញ\",\r\n    \"rentVoucherNumber\": \"លេខប័ណ្ណជួល\",\r\n    \"rentItem\": \"វត្ថុជួល\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"guestName\": \"ឈ្មោះភ្ញៀវ\",\r\n    \"rentPrice\": \"តម្លៃជួល\",\r\n    \"rentRemark\": \"ចំណាំអំពីការជួល\",\r\n    \"returnerName\": \"ឈ្មោះអ្នកប្រគល់វិញ\",\r\n    \"returnRemark\": \"ចំណាំអំពីការប្រគល់វិញ\",\r\n    \"yuan\": \"យួន\",\r\n    \"none\": \"គ្មាន\",\r\n    \"pleaseEnterReturnerName\": \"សូមបញ្ចូលឈ្មោះអ្នកប្រគល់វិញ\",\r\n    \"pleaseEnterReturnRemarkPlaceholder\": \"សូមបញ្ចូលចំណាំអំពីការប្រគល់វិញ ដូចជាស្ថានភាពវត្ថុ ការខូចខាត ជាដើម\",\r\n    \"returnSuccess\": \"បានប្រគល់វិញដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { rentGoodsApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  id: 0,\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\n\r\n// 租借信息（只读）\r\nconst rentInfo = ref({\r\n  /** 租借凭证号 */\r\n  rentNo: '',\r\n  /** 租借物品名称 */\r\n  rentName: '',\r\n  /** 房号 */\r\n  rNo: '',\r\n  /** 客人姓名 */\r\n  name: '',\r\n  /** 租金 */\r\n  rentPrice: 0,\r\n  /** 租借备注 */\r\n  remark: '',\r\n})\r\n\r\n// 归还信息（可编辑）\r\nconst returnForm = ref({\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n  id: props.id,\r\n  /** 归还人姓名 */\r\n  backName: '',\r\n  /** 归还备注 */\r\n  backRemark: '',\r\n  /** 状态更新为已归还 */\r\n  state: '1',\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  backName: [{ required: true, message: t('pleaseEnterReturnerName'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  if (props.id !== 0) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  rentGoodsApi.getRentGoods(props.id).then((res: any) => {\r\n    loading.value = false\r\n    // 将获取的数据填充到租借信息中（只读）\r\n    Object.assign(rentInfo.value, res.data)\r\n    // 默认将客人姓名设置为归还人姓名\r\n    returnForm.value.backName = res.data.name || ''\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            // 调用归还API\r\n            rentGoodsApi\r\n              .backRentGoods({\r\n                ...returnForm.value,\r\n                id: props.id,\r\n              })\r\n              .then(() => {\r\n                ElMessage.success({\r\n                  message: t('returnSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <!-- 租借信息（只读） -->\r\n    <div class=\"section-title\">\r\n      {{ t('rentInfo') }}\r\n    </div>\r\n    <el-form label-width=\"150px\" label-suffix=\"：\" class=\"readonly-form\">\r\n      <el-form-item :label=\"t('rentVoucherNumber')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rentNo }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rentItem')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rentName }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('roomNumber')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rNo }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('guestName')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.name }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rentPrice')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rentPrice }} {{ t('yuan') }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rentRemark')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.remark || t('none') }}</span>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 分割线 -->\r\n    <el-divider />\r\n\r\n    <!-- 归还信息（可编辑） -->\r\n    <div class=\"section-title\">\r\n      {{ t('returnInfo') }}\r\n    </div>\r\n    <el-form ref=\"formRef\" :model=\"returnForm\" :rules=\"formRules\" label-width=\"150px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('returnerName')\">\r\n        <el-input v-model=\"returnForm.backName\" :placeholder=\"t('pleaseEnterReturnerName')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('returnRemark')\">\r\n        <el-input v-model=\"returnForm.backRemark\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" :placeholder=\"t('pleaseEnterReturnRemarkPlaceholder')\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 16px;\r\n  padding-left: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.readonly-form {\r\n  .readonly-text {\r\n    color: #606266;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.el-divider {\r\n  margin: 24px 0;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "rentInfo", "rentNo", "rentName", "rNo", "name", "rentPrice", "remark", "returnForm", "gcode", "hcode", "id", "backName", "backRemark", "state", "formRules", "required", "message", "trigger", "onMounted", "value", "rentGoodsApi", "getRentGoods", "then", "res", "Object", "assign", "data", "__expose", "submit", "Promise", "resolve", "validate", "valid", "backRentGoods", "ElMessage", "success", "center"], "mappings": "2vBA+DA,MAAMA,EAAQC,GAGRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IAGVE,EAAWF,EAAI,CAEnBG,OAAQ,GAERC,SAAU,GAEVC,IAAK,GAELC,KAAM,GAENC,UAAW,EAEXC,OAAQ,KAIJC,EAAaT,EAAI,CACrBU,MAAOb,EAAUa,MAEjBC,MAAOd,EAAUc,MACjBC,GAAInB,EAAMmB,GAEVC,SAAU,GAEVC,WAAY,GAEZC,MAAO,MAGHC,EAAYhB,EAAe,CAC/Ba,SAAU,CAAC,CAAEI,UAAU,EAAMC,QAASvB,EAAE,2BAA4BwB,QAAS,kBAG/EC,GAAU,KACS,IAAb3B,EAAMmB,KAMVb,EAAQsB,OAAQ,EAChBC,EAAaC,aAAa9B,EAAMmB,IAAIY,MAAMC,IACxC1B,EAAQsB,OAAQ,EAEhBK,OAAOC,OAAOzB,EAASmB,MAAOI,EAAIG,MAElCnB,EAAWY,MAAMR,SAAWY,EAAIG,KAAKtB,MAAQ,EAAA,IAXrC,IAeCuB,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB/B,EAAQoB,OACNpB,EAAQoB,MAAMY,UAAUC,IAClBA,GAEFZ,EACGa,cAAc,IACV1B,EAAWY,MACdT,GAAInB,EAAMmB,KAEXY,MAAK,KACJY,EAAUC,QAAQ,CAChBnB,QAASvB,EAAE,iBACX2C,QAAQ,IAEFN,GAAA,GACT,GAEN"}