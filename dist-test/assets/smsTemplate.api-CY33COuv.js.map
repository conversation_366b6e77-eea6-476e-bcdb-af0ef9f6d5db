{"version": 3, "file": "smsTemplate.api-CY33COuv.js", "sources": ["../../src/api/modules/marketing/sms/smsTemplate.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n/**\r\n * 短信模板\r\n */\r\nexport default {\r\n  /**\r\n   * 短信模板列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  list: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    bizCode?: string\r\n    isSys?: string // 是否系统模板 0：否 1：是\r\n    templateType?: string // 0：普通模板 1：营销模板\r\n    state?: string // 0:待审核 1:启用 2：停用 3:未通过\r\n    from: number\r\n    limit: number\r\n  }) =>\r\n    api.get('marketing/sms/template/list', {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 短信模板明细\r\n   * @param gcode 集团代码\r\n   * @param templateCode  模板代码\r\n   * @returns\r\n   */\r\n  detail: (gcode: string, templateCode: string) =>\r\n    api.get('marketing/sms/template/detail', {\r\n      params: { gcode, templateCode },\r\n    }),\r\n\r\n  create: (data: any) => api.post('marketing/sms/template/create', data, {}),\r\n\r\n  edit: (data: any) => api.post('marketing/sms/template/edit', data, {}),\r\n}\r\n"], "names": ["smsTemplateApi", "list", "data", "api", "get", "params", "detail", "gcode", "templateCode", "create", "post", "edit"], "mappings": "wCAKA,MAAeA,EAAA,CAMbC,KAAOC,GAULC,EAAIC,IAAI,8BAA+B,CACrCC,OAAQH,IASZI,OAAQ,CAACC,EAAeC,IACtBL,EAAIC,IAAI,gCAAiC,CACvCC,OAAQ,CAAEE,QAAOC,kBAGrBC,OAASP,GAAcC,EAAIO,KAAK,gCAAiCR,EAAM,IAEvES,KAAOT,GAAcC,EAAIO,KAAK,8BAA+BR,EAAM,CAAE"}