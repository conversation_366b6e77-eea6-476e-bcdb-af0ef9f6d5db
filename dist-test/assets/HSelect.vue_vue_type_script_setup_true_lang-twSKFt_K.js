import{d as e,a1 as s,a2 as a,B as l,am as o,o as t,e as n,w as d,g as r,c as i,F as u,ag as c,a7 as p,Y as b,u as f,f as g,a6 as m,_ as v}from"./index-CkEhI1Zk.js";const x={class:"w-full inline-flex"},w=["disabled"],k={class:"block truncate"},h={class:"pointer-events-none absolute end-0 inset-y-0 flex items-center pe-2.5"},y={class:"max-h-60 w-full scroll-py-1 overflow-y-auto p-1 lg-w-48 focus-outline-none"},V=["disabled","onClick"],_=e({__name:"HSelect",props:s({options:{},disabled:{type:Boolean,default:!1}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const s=e,_=a(e,"modelValue"),B=l({get:()=>s.options.find((e=>e.value===_.value))??s.options[0],set(e){_.value=e.value}});return(s,a)=>{const l=v,_=o("VMenu");return t(),n(_,m({triggers:["click"],"popper-triggers":["click"],delay:0,disabled:s.disabled},s.$attrs),{popper:d((()=>[r("div",y,[(t(!0),i(u,null,c(s.options,(s=>(t(),i("button",{key:s.value,disabled:s.disabled,class:p(["w-full cursor-pointer truncate border-size-0 rounded-md bg-inherit px-2 py-1.5 text-start text-sm disabled-cursor-not-allowed hover-not-disabled-bg-stone-1 dark-hover-not-disabled-bg-stone-9",{"font-bold":e.modelValue===s.value}]),onClick:e=>B.value=s},b(s.label),11,V)))),128))])])),default:d((()=>[r("div",x,[r("button",{class:"relative block w-full flex cursor-default items-center gap-x-2 border-0 rounded-md bg-white px-2.5 py-1.5 pe-9 text-left text-sm shadow-sm ring-1 ring-stone-2 ring-inset lg-w-48 disabled-cursor-not-allowed dark-bg-dark focus-outline-none focus-ring-2 dark-ring-stone-8 focus-ring-ui-primary",disabled:s.disabled},[r("span",k,b(f(B).label),1),r("span",h,[g(l,{name:"i-carbon:chevron-down",class:"h-5 w-5 flex-shrink-0 text-stone-5"})])],8,w)])])),_:1},16,["disabled"])}}});export{_};
//# sourceMappingURL=HSelect.vue_vue_type_script_setup_true_lang-twSKFt_K.js.map
