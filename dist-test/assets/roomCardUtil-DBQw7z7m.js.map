{"version": 3, "file": "roomCardUtil-DBQw7z7m.js", "sources": ["../../src/utils/roomCardUtil.ts"], "sourcesContent": ["import dayjs from 'dayjs'\r\n\r\n/**\r\n * 生成房卡信息的 HTML 消息\r\n * @param {object} data - 包含 cardInfo 的对象\r\n * @returns {string} - 返回格式化后的 HTML 字符串\r\n */\r\nexport function generateCardInfoHtml(data) {\r\n  if (!data || !data.cardInfo) {\r\n    return '<ul><li>无房卡信息</li></ul>'\r\n  }\r\n\r\n  const { roomNo, lockNo, cardNo, checkin, expire, replaceCard } = data.cardInfo\r\n\r\n  // 生成 HTML 片段\r\n  const htmlParts = [] as any\r\n\r\n  if (roomNo) {\r\n    htmlParts.push(`<li><strong>房间号:</strong> ${roomNo}</li>`)\r\n  }\r\n  if (lockNo) {\r\n    htmlParts.push(`<li><strong>锁号:</strong> ${lockNo}</li>`)\r\n  }\r\n  if (cardNo) {\r\n    htmlParts.push(`<li><strong>卡号:</strong> ${cardNo}</li>`)\r\n  }\r\n  if (checkin) {\r\n    htmlParts.push(`<li><strong>入住时间:</strong> ${dayjs.unix(checkin).format('YYYY-MM-DD HH:mm')}</li>`)\r\n  }\r\n  if (expire) {\r\n    htmlParts.push(`<li><strong>到期时间:</strong> ${dayjs.unix(expire).format('YYYY-MM-DD HH:mm')}</li>`)\r\n  }\r\n  if (replaceCard !== undefined && replaceCard !== null) {\r\n    htmlParts.push(`<li><strong>是否替换前卡:</strong> ${replaceCard ? '是' : '否'}</li>`)\r\n  }\r\n\r\n  // 如果没有有效信息，显示“无房卡信息”\r\n  if (htmlParts.length === 0) {\r\n    return '<ul><li>无房卡信息</li></ul>'\r\n  }\r\n\r\n  return `<ul>${htmlParts.join('')}</ul>`\r\n}\r\n"], "names": ["generateCardInfoHtml", "data", "cardInfo", "roomNo", "lockNo", "cardNo", "checkin", "expire", "replaceCard", "htmlParts", "push", "dayjs", "unix", "format", "length", "join"], "mappings": "yCAOO,SAASA,EAAqBC,GACnC,IAAKA,IAASA,EAAKC,SACV,MAAA,0BAGH,MAAAC,OAAEA,SAAQC,EAAQC,OAAAA,EAAAC,QAAQA,SAASC,EAAQC,YAAAA,GAAgBP,EAAKC,SAGhEO,EAAY,GAsBd,OApBAN,GACQM,EAAAC,KAAK,6BAA6BP,UAE1CC,GACQK,EAAAC,KAAK,4BAA4BN,UAEzCC,GACQI,EAAAC,KAAK,4BAA4BL,UAEzCC,GACQG,EAAAC,KAAK,8BAA8BC,EAAMC,KAAKN,GAASO,OAAO,4BAEtEN,GACQE,EAAAC,KAAK,8BAA8BC,EAAMC,KAAKL,GAAQM,OAAO,4BAErEL,SACFC,EAAUC,KAAK,gCAAgCF,EAAc,IAAM,YAI5C,IAArBC,EAAUK,OACL,0BAGF,OAAOL,EAAUM,KAAK,UAC/B"}