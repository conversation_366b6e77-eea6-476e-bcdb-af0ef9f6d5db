{"version": 3, "file": "CardReader-BeR26SIt.js", "sources": ["../../src/store/websocket/CardReader.ts"], "sourcesContent": ["declare let QWebChannel: any\r\ndeclare let qtClient: any\r\n\r\nexport class CardReader {\r\n  static socket: WebSocket | null = null\r\n  private static reconnectInterval: number = 5000\r\n  static isConnected: boolean = false\r\n  private static messageCallback: ((message: string) => void) | null = null\r\n  private static isConnecting: boolean = false // 新增连接状态标识\r\n\r\n  /** 初始化连接 */\r\n  static initCardReader(callback: (message: string) => void) {\r\n    /** 检查当前连接状态 */\r\n    if (this.socket?.readyState === WebSocket.OPEN && this.isConnected) {\r\n      console.log('initCardReader:', '存在有效连接，无需重新连接')\r\n      return\r\n    }\r\n\r\n    /** 如果正在连接中，等待连接完成 */\r\n    if (this.isConnecting) {\r\n      console.log('initCardReader:', '正在连接中，请稍候')\r\n      return\r\n    }\r\n\r\n    /** 如果WebSocket处于CLOSING或CLOSED状态，先清理 */\r\n    if (this.socket && (this.socket.readyState === WebSocket.CLOSING || this.socket.readyState === WebSocket.CLOSED)) {\r\n      this.socket = null\r\n      this.isConnected = false\r\n    }\r\n\r\n    this.messageCallback = callback // 保存回调函数\r\n    this.isConnecting = true // 设置连接中状态\r\n\r\n    const baseUrl: string = 'ws://localhost:12345'\r\n\r\n    const connect = () => {\r\n      // 设置socket为静态属性\r\n      this.socket = new WebSocket(baseUrl)\r\n\r\n      // socket close\r\n      this.socket.onclose = function () {\r\n        CardReader.isConnected = false\r\n        CardReader.isConnecting = false\r\n        console.log('WebSocket连接已关闭')\r\n      }\r\n\r\n      // socket error\r\n      this.socket.onerror = function (error: Event) {\r\n        console.error('WebSocket channel error: ', error)\r\n        CardReader.isConnected = false\r\n        CardReader.isConnecting = false\r\n      }\r\n\r\n      // socket open\r\n      this.socket.onopen = function () {\r\n        CardReader.isConnected = true\r\n        CardReader.isConnecting = false\r\n        CardReader.reconnectInterval = 1000 // 重置重连间隔\r\n        console.log('WebSocket连接已建立')\r\n\r\n        // 初始化 QWebChannel\r\n        new QWebChannel(CardReader.socket, (channel: any) => {\r\n          ;(window as any).qtClient = channel.objects.qtClient\r\n          // 连接成功后调用 callback\r\n          qtClient.receiveMessage('Client connected, ready to send/receive messages!')\r\n          qtClient.sendMessage.connect((message: string) => {\r\n            if (CardReader.messageCallback) {\r\n              CardReader.messageCallback(message) // 调用传入的 callback 来处理返回的消息\r\n            }\r\n          })\r\n        })\r\n      }\r\n    }\r\n    connect()\r\n  }\r\n\r\n  /** 读取身份证 */\r\n  static readIdCard() {\r\n    if (this.socket) {\r\n      console.log('readIdCard:', '发送读取身份证信息')\r\n      qtClient.receiveMessage('{\"method\":\"readIdCard\"}')\r\n    } else {\r\n      console.error('WebSocket is not connected.')\r\n    }\r\n  }\r\n\r\n  /** 操作房卡 */\r\n  static handleLockCard(json: string) {\r\n    if (this.socket) {\r\n      console.log('handleLockCard:', json)\r\n      qtClient.receiveMessage(json)\r\n    } else {\r\n      console.error('WebSocket is not connected.')\r\n    }\r\n  }\r\n\r\n  /** OTA接口请求 */\r\n  static otaRequest(json: string) {\r\n    if (this.socket) {\r\n      qtClient.receiveMessage(json)\r\n    } else {\r\n      console.error('WebSocket is not connected.')\r\n    }\r\n  }\r\n\r\n  /** 操作登录后信息发送到客户端 */\r\n  static handleLocalStorage(json: string) {\r\n    if (this.socket) {\r\n      qtClient.receiveMessage(json)\r\n    } else {\r\n      console.error('WebSocket is not connected.')\r\n    }\r\n  }\r\n\r\n  static closeSocket() {\r\n    if (this.socket && this.socket.readyState !== WebSocket.CLOSED && this.socket.readyState !== WebSocket.CLOSING) {\r\n      console.log('closeSocket:', 'WebSocket is closing')\r\n      this.socket.close()\r\n    }\r\n    // 延迟清理状态，确保WebSocket完全关闭\r\n    setTimeout(() => {\r\n      CardReader.isConnected = false\r\n      CardReader.isConnecting = false\r\n      if (this.socket && (this.socket.readyState === WebSocket.CLOSED || this.socket.readyState === WebSocket.CLOSING)) {\r\n        this.socket = null\r\n      }\r\n    }, 100)\r\n  }\r\n}\r\n"], "names": ["_<PERSON><PERSON><PERSON>er", "initCardReader", "callback", "_a", "socket", "readyState", "WebSocket", "OPEN", "this", "isConnected", "console", "log", "isConnecting", "CLOSING", "CLOSED", "messageCallback", "onclose", "onerror", "error", "onopen", "reconnectInterval", "QWebChannel", "channel", "window", "qtClient", "objects", "receiveMessage", "sendMessage", "connect", "message", "readIdCard", "handleLockCard", "json", "otaRequest", "handleLocalStorage", "closeSocket", "close", "setTimeout", "__publicField", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "0JAGO,MAAMA,EAAN,MAAMA,EAQX,qBAAOC,CAAeC,GARjB,IAAAC,EAUH,IAAI,OAAAA,OAAKC,aAAL,EAAAD,EAAaE,cAAeC,UAAUC,MAAQC,KAAKC,YAErD,YADQC,QAAAC,IAAI,kBAAmB,iBAKjC,GAAIH,KAAKI,aAEP,YADQF,QAAAC,IAAI,kBAAmB,cAK7BH,KAAKJ,QAAWI,KAAKJ,OAAOC,aAAeC,UAAUO,SAAWL,KAAKJ,OAAOC,aAAeC,UAAUQ,SACvGN,KAAKJ,OAAS,KACdI,KAAKC,aAAc,GAGrBD,KAAKO,gBAAkBb,EACvBM,KAAKI,cAAe,EAIJ,MAETJ,KAAAJ,OAAS,IAAIE,UAJI,wBAOjBE,KAAAJ,OAAOY,QAAU,WACpBhB,EAAWS,aAAc,EACzBT,EAAWY,cAAe,EAC1BF,QAAQC,IAAI,iBACd,EAGKH,KAAAJ,OAAOa,QAAU,SAAUC,GACtBR,QAAAQ,MAAM,4BAA6BA,GAC3ClB,EAAWS,aAAc,EACzBT,EAAWY,cAAe,CAC5B,EAGKJ,KAAAJ,OAAOe,OAAS,WACnBnB,EAAWS,aAAc,EACzBT,EAAWY,cAAe,EAC1BZ,EAAWoB,kBAAoB,IAC/BV,QAAQC,IAAI,kBAGZ,IAAIU,YAAYrB,EAAWI,QAASkB,IACjBC,OAAAC,SAAWF,EAAQG,QAAQD,SAE5CA,SAASE,eAAe,qDACfF,SAAAG,YAAYC,SAASC,IACxB7B,EAAWe,iBACbf,EAAWe,gBAAgBc,EAAO,GAErC,GAEL,CAAA,EAEMD,EAAA,CAIV,iBAAOE,GACDtB,KAAKJ,QACCM,QAAAC,IAAI,cAAe,aAC3Ba,SAASE,eAAe,4BAExBhB,QAAQQ,MAAM,8BAChB,CAIF,qBAAOa,CAAeC,GAChBxB,KAAKJ,QACCM,QAAAC,IAAI,kBAAmBqB,GAC/BR,SAASE,eAAeM,IAExBtB,QAAQQ,MAAM,8BAChB,CAIF,iBAAOe,CAAWD,GACZxB,KAAKJ,OACPoB,SAASE,eAAeM,GAExBtB,QAAQQ,MAAM,8BAChB,CAIF,yBAAOgB,CAAmBF,GACpBxB,KAAKJ,OACPoB,SAASE,eAAeM,GAExBtB,QAAQQ,MAAM,8BAChB,CAGF,kBAAOiB,GACD3B,KAAKJ,QAAUI,KAAKJ,OAAOC,aAAeC,UAAUQ,QAAUN,KAAKJ,OAAOC,aAAeC,UAAUO,UAC7FH,QAAAC,IAAI,eAAgB,wBAC5BH,KAAKJ,OAAOgC,SAGdC,YAAW,KACTrC,EAAWS,aAAc,EACzBT,EAAWY,cAAe,GACtBJ,KAAKJ,QAAWI,KAAKJ,OAAOC,aAAeC,UAAUQ,QAAUN,KAAKJ,OAAOC,aAAeC,UAAUO,UACtGL,KAAKJ,OAAS,KAAA,GAEf,IAAG,GA1HRkC,EADWtC,EACJ,SAA2B,MAClCsC,EAFWtC,EAEI,oBAA4B,KAC3CsC,EAHWtC,EAGJ,eAAuB,GAC9BsC,EAJWtC,EAII,kBAAsD,MACrEsC,EALWtC,EAKI,gBAAwB,GALlC,IAAMuC,EAANvC"}