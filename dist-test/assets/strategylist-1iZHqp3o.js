import{d as t,aj as e,ai as a,b as s,y as i,Q as r,b3 as l,o,c as d,f as n,w as u,u as c,h as p,Y as b,F as y,ag as m,e as g,aM as f,b6 as v,aq as h,R as _,g as S,av as k,a7 as j,aR as w,ax as C,l as T,m as x,j as D,k as P,q as $,aS as M,_ as N,x as E,t as Y,bz as V,bv as U,v as I,aT as O}from"./index-CkEhI1Zk.js";/* empty css                   */import{_ as B}from"./index.vue_vue_type_script_setup_true_lang-DK1kOKr4.js";/* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css                *//* empty css               */import{_ as G}from"./index.vue_vue_type_script_setup_true_lang-DvSmdj52.js";/* empty css                *//* empty css                 *//* empty css                  *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                        */import{p as W}from"./priceStrategy.api-Deh17XpK.js";import{d as F}from"./dictData.api-DUabpYqy.js";import{r as A,B as z,$ as H}from"./constants-Cg3j_uH4.js";import L from"./index-C6hOtDtU.js";import{_ as q}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                   *//* empty css                  */import"./createStrategy-BbXwUKlP.js";/* empty css                          *//* empty css                       */import"./index-DAulSAJI.js";/* empty css                */import"./index-D8c6PuWt.js";/* empty css                      *//* empty css                        *//* empty css                         *//* empty css                */import"./index-CDbn0nBx.js";/* empty css                          *//* empty css                 */import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";/* empty css                   */import"./member.api-2tU9HGvl.js";import"./channel.api-CM6FWEgD.js";import"./generalConfig.api-CEBBd8kx.js";import"./rt.api-5a8-At7-.js";import"./merchant.api-BtmIsRm3.js";import"./timeutils-Ib6GkGcq.js";import"./detail-DK5_oKxe.js";const R={key:0},X={key:1},J={key:2},K={key:3},Q={key:4},Z=t({name:"GroupPricePriceStrategyList2",__name:"strategylist",setup(t){const{t:q}=e(),Z=a(),tt=s({loading:!1,tableAutoHeight:!1,handle:"create",isEdit:!1,formModeProps:{visible:!1,strategyCode:""},search:{strategyName:"",state:"1",hcodes:[],rtCodes:[],guestSourceTypeCodes:""},options:[],dataList:[]});s([]);const et=s([]);i((()=>{st(),F.getDictDataBatch(at).then((t=>{et.value=t.data.filter((t=>t.dictType===A))}))})),r((()=>{}));const at=[A];function st(){tt.value.loading=!0;const t={gcode:Z.gcode,hcode:Z.hcode,isG:z.NO,isEnable:"-1"===tt.value.search.state?"":tt.value.search.state,strategyName:tt.value.search.strategyName,guestSrcType:tt.value.search.guestSourceTypeCodes};W.getPriceStrategyList(t).then((t=>{tt.value.loading=!1,tt.value.dataList=t.data}))}function it(t){let e=q("everyWeek");return t.forEach((t=>{switch(t){case 7:e=`${e} ${q("sunday")}、`;break;case 1:e=`${e} ${q("monday")}、`;break;case 2:e=`${e} ${q("tuesday")}、`;break;case 3:e=`${e} ${q("wednesday")}、`;break;case 4:e=`${e} ${q("thursday")}、`;break;case 5:e=`${e} ${q("friday")}、`;break;case 6:e=`${e} ${q("saturday")}、`}})),e=`${e} ${q("valid")}`,e}function rt(t){const e=t.scope.startDate,a=t.scope.endDate,s=t.isEnable;return k(new Date).isBefore(k(e))?{code:"2",type:"info",label:q("notStarted")}:k(new Date).isAfter(k(a))?{code:"3",type:"danger",label:q("expired")}:s===z.YES?{code:"1",type:"success",label:q("valid")}:{code:"0",type:"warning",label:q("invalid")}}function lt(){tt.value.formModeProps.strategyCode="",tt.value.formModeProps.visible=!0,tt.value.handle="create"}function ot(t){tt.value.isEdit=t}function dt(t){const e={strategyCode:t.strategyCode,isEnable:t.isEnable===z.NO?z.YES:z.NO};W.updatePriceStrategyStatus(e).then((t=>{0===t.code?(w.success({message:q("saveSuccess"),center:!0}),st()):w.error({message:t.msg,center:!0})}))}return(t,e)=>{const a=C,s=T,i=x,r=D,w=P,W=$,F=M,A=N,z=E,Z=G,at=Y,nt=V,ut=U,ct=I,pt=B,bt=l("auth"),yt=O;return o(),d("div",{class:j({"absolute-container":c(tt).tableAutoHeight})},[n(pt,null,{default:u((()=>[n(Z,{fold:c(tt).searchFold,"show-toggle":!1},{default:u((()=>[n(z,{model:c(tt).search,size:"default","label-width":"110px","inline-message":"",inline:"",class:"search-form"},{default:u((()=>[n(i,{label:c(q)("state")},{default:u((()=>[n(s,{modelValue:c(tt).search.state,"onUpdate:modelValue":e[0]||(e[0]=t=>c(tt).search.state=t),onChange:e[1]||(e[1]=t=>st())},{default:u((()=>[n(a,{value:"-1"},{default:u((()=>[p(b(c(q)("all")),1)])),_:1}),n(a,{value:"0"},{default:u((()=>[p(b(c(q)("invalid")),1)])),_:1}),n(a,{value:"1"},{default:u((()=>[p(b(c(q)("valid")),1)])),_:1}),n(a,{value:"2"},{default:u((()=>[p(b(c(q)("expired")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),n(i,{label:c(q)("guestSourceType")},{default:u((()=>[n(w,{modelValue:c(tt).search.guestSourceTypeCodes,"onUpdate:modelValue":e[2]||(e[2]=t=>c(tt).search.guestSourceTypeCodes=t),clearable:"",placeholder:c(q)("selectGuestSourceType"),style:{width:"150px"},onChange:e[3]||(e[3]=t=>st())},{default:u((()=>[(o(!0),d(y,null,m(c(et),(t=>(o(),g(r,{key:t.code,label:t.label,value:t.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),n(i,{label:c(q)("strategyName")},{default:u((()=>[n(F,{modelValue:c(tt).search.strategyName,"onUpdate:modelValue":e[5]||(e[5]=t=>c(tt).search.strategyName=t),placeholder:c(q)("inputStrategyName"),clearable:"",onKeydown:e[6]||(e[6]=f((t=>st()),["enter"])),onClear:e[7]||(e[7]=t=>st())},{append:u((()=>[n(W,{icon:c(v),onClick:e[4]||(e[4]=t=>st())},null,8,["icon"])])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),n(i,{style:{float:"right"}},{default:u((()=>[n(W,{type:"primary",onClick:e[8]||(e[8]=t=>st())},{icon:u((()=>[n(A,{name:"ep:search"})])),default:u((()=>[p(b(c(q)("filter")),1)])),_:1}),h((o(),g(W,{type:"primary",plain:"",onClick:lt},{icon:u((()=>[n(A,{name:"ep:plus"})])),default:u((()=>[p(b(c(q)("createPriceStrategy")),1)])),_:1})),[[bt,"pms:price-strategy:create"]])])),_:1})])),_:1},8,["model"])])),_:1},8,["fold"]),h((o(),g(ct,{"header-cell-style":{background:"#f5f7fa",color:"#606266"},class:"list-table",data:c(tt).dataList,"highlight-current-row":"",height:"100%"},{default:u((()=>[n(at,{prop:"strategyName",label:c(q)("strategyName")},null,8,["label"]),n(at,{prop:"guestSrcType",label:c(q)("guestSourceType")},{default:u((t=>["member"===t.row.condition.guestSrc.guestSrcType?(o(),d("span",R,b(c(q)("member")),1)):_("",!0),"agent"===t.row.condition.guestSrc.guestSrcType?(o(),d("span",X,b(c(q)("agent")),1)):_("",!0),"walk_in"===t.row.condition.guestSrc.guestSrcType?(o(),d("span",J,b(c(q)("walkIn")),1)):_("",!0),"0"===t.row.condition.guestSrc.guestSrcType?(o(),d("span",K,b(c(q)("unlimited")),1)):_("",!0),"protocol"===t.row.condition.guestSrc.guestSrcType?(o(),d("span",Q,b(c(q)("protocolUnit")),1)):_("",!0)])),_:1},8,["label"]),n(at,{label:c(q)("strategyContent")},{default:u((t=>[n(nt,{style:{"margin-right":"5px"}},{default:u((()=>{return[p(b((e=t.row,e.strategy.discountsType?e.strategy.discountsType.type===H.DISCOUNT?`${100*e.strategy.discountsType.value}% ${q("discount")}`:e.strategy.discountsType.type===H.REDUCE?`${q("reduce")}: ${e.strategy.discountsType.value.toFixed(2)} ${q("yuan")}`:e.strategy.discountsType.type===H.FIX?`${q("fixedPrice")}`:"":"")),1)];var e})),_:2},1024),(o(!0),d(y,null,m(t.row.strategy.rightsTypes,(e=>(o(),d("div",{key:e.type},["delay"===e.type?(o(),g(nt,{key:0,style:{"margin-right":"5px"}},{default:u((()=>[p(b(c(q)("delayCheckOut"))+": "+b(e.value)+" "+b(c(q)("minutes")),1)])),_:2},1024)):_("",!0),"multi_point"===e.type&&"member"===t.row.condition.guestSrc.guestSrcType?(o(),g(nt,{key:1,style:{"margin-right":"5px"}},{default:u((()=>[p(b(c(q)("pointsMultiplier"))+": "+b(e.value)+" "+b(c(q)("times")),1)])),_:2},1024)):_("",!0),"breakfast"===e.type?(o(),g(nt,{key:2,style:{"margin-right":"5px"}},{default:u((()=>[p(b(c(q)("giveBreakfast"))+" "+b(e.value),1)])),_:2},1024)):_("",!0)])))),128))])),_:1},8,["label"]),n(at,{label:c(q)("validDate")},{default:u((t=>[S("div",null,b(c(k)(t.row.startDate).format("YYYY/MM/DD"))+"-"+b(c(k)(t.row.endDate).format("YYYY/MM/DD")),1),S("div",null,b(it(t.row.scope.weeks)),1)])),_:1},8,["label"]),n(at,{label:c(q)("creator")},{default:u((t=>[p(b(t.row.creator)+" ",1),e[10]||(e[10]=S("br",null,null,-1)),p(" "+b(c(k)(t.row.createTime).format("YYYY/MM/DD HH:mm:ss")),1)])),_:1},8,["label"]),n(at,{label:c(q)("status"),align:"center"},{default:u((t=>[n(nt,{type:rt(t.row).type},{default:u((()=>[p(b(rt(t.row).label),1)])),_:2},1032,["type"])])),_:1},8,["label"]),n(at,{label:c(q)("operation"),align:"center",fixed:"right"},{default:u((t=>[h((o(),g(ut,{type:"primary",onClick:e=>{return a=t.row,tt.value.formModeProps.strategyCode=a.strategyCode,tt.value.formModeProps.visible=!0,tt.value.handle="edit",void(tt.value.isEdit=!0);var a}},{default:u((()=>[p(b(c(q)("edit")),1)])),_:2},1032,["onClick"])),[[bt,"pms:price-strategy:create"]]),"0"===t.row.isEnable?h((o(),g(ut,{key:0,type:"primary",onClick:e=>dt(t.row)},{default:u((()=>[p(b(c(q)("enable")),1)])),_:2},1032,["onClick"])),[[bt,"pms:price-strategy:create"]]):h((o(),g(ut,{key:1,type:"primary",onClick:e=>dt(t.row)},{default:u((()=>[p(b(c(q)("disable")),1)])),_:2},1032,["onClick"])),[[bt,"pms:price-strategy:create"]])])),_:1},8,["label"])])),_:1},8,["data"])),[[yt,c(tt).loading]])])),_:1}),n(L,{modelValue:c(tt).formModeProps.visible,"onUpdate:modelValue":e[9]||(e[9]=t=>c(tt).formModeProps.visible=t),"strategy-code":c(tt).formModeProps.strategyCode,handle:c(tt).handle,"is-edit":c(tt).isEdit,onEditStatus:ot,onSuccess:st},null,8,["modelValue","strategy-code","handle","is-edit"])],2)}}});function tt(t){const e=t;e.__i18n=e.__i18n||[],e.__i18n.push({locale:"",resource:{en:{state:{t:0,b:{t:2,i:[{t:3}],s:"State"}},all:{t:0,b:{t:2,i:[{t:3}],s:"All"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"Invalid"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"Valid"}},expired:{t:0,b:{t:2,i:[{t:3}],s:"Expired"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source"}},selectGuestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"Please select"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"Strategy"}},inputStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"Please input strategy name, support fuzzy search"}},filter:{t:0,b:{t:2,i:[{t:3}],s:"Filter"}},createPriceStrategy:{t:0,b:{t:2,i:[{t:3}],s:"Create Price Strategy"}},details:{t:0,b:{t:2,i:[{t:3}],s:"Details"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"Edit"}},enable:{t:0,b:{t:2,i:[{t:3}],s:"Enable"}},disable:{t:0,b:{t:2,i:[{t:3}],s:"Disable"}},strategyContent:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Content"}},validDate:{t:0,b:{t:2,i:[{t:3}],s:"Valid Date"}},creator:{t:0,b:{t:2,i:[{t:3}],s:"Creator"}},member:{t:0,b:{t:2,i:[{t:3}],s:"Member"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"Agent"}},walkIn:{t:0,b:{t:2,i:[{t:3}],s:"Walk-In"}},unlimited:{t:0,b:{t:2,i:[{t:3}],s:"Unlimited"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"Protocol Unit"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"Discount"}},delayCheckOut:{t:0,b:{t:2,i:[{t:3}],s:"Delay Check-Out"}},pointsMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"Points Multiplier"}},giveBreakfast:{t:0,b:{t:2,i:[{t:3}],s:"Give Breakfast"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"Actions"}},saveSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Save successful"}},notStarted:{t:0,b:{t:2,i:[{t:3}],s:"Not Started"}},everyWeek:{t:0,b:{t:2,i:[{t:3}],s:"Every Week"}},sunday:{t:0,b:{t:2,i:[{t:3}],s:"Sun"}},monday:{t:0,b:{t:2,i:[{t:3}],s:"Mon"}},tuesday:{t:0,b:{t:2,i:[{t:3}],s:"Tue"}},wednesday:{t:0,b:{t:2,i:[{t:3}],s:"Wed"}},thursday:{t:0,b:{t:2,i:[{t:3}],s:"Thu"}},friday:{t:0,b:{t:2,i:[{t:3}],s:"Fri"}},saturday:{t:0,b:{t:2,i:[{t:3}],s:"Sat"}},minutes:{t:0,b:{t:2,i:[{t:3}],s:"Min"}},times:{t:0,b:{t:2,i:[{t:3}],s:"Times"}},reduce:{t:0,b:{t:2,i:[{t:3}],s:"Reduce"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"Yuan"}},fixedPrice:{t:0,b:{t:2,i:[{t:3}],s:"Fixed price"}}},"zh-cn":{state:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},all:{t:0,b:{t:2,i:[{t:3}],s:"全部"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"无效"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"有效"}},expired:{t:0,b:{t:2,i:[{t:3}],s:"已过期"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},selectGuestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"请选择"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"策略名称"}},inputStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"请输入策略名称，支持模糊查询"}},filter:{t:0,b:{t:2,i:[{t:3}],s:"筛选"}},createPriceStrategy:{t:0,b:{t:2,i:[{t:3}],s:"新增房价策略"}},details:{t:0,b:{t:2,i:[{t:3}],s:"详情"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"编辑"}},enable:{t:0,b:{t:2,i:[{t:3}],s:"启用"}},disable:{t:0,b:{t:2,i:[{t:3}],s:"停用"}},strategyContent:{t:0,b:{t:2,i:[{t:3}],s:"策略内容"}},validDate:{t:0,b:{t:2,i:[{t:3}],s:"有效日期"}},creator:{t:0,b:{t:2,i:[{t:3}],s:"创建人"}},member:{t:0,b:{t:2,i:[{t:3}],s:"会员"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},walkIn:{t:0,b:{t:2,i:[{t:3}],s:"散客"}},unlimited:{t:0,b:{t:2,i:[{t:3}],s:"不限"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"折"}},delayCheckOut:{t:0,b:{t:2,i:[{t:3}],s:"延迟离店"}},pointsMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"积分倍数"}},giveBreakfast:{t:0,b:{t:2,i:[{t:3}],s:"赠送早餐"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},saveSuccess:{t:0,b:{t:2,i:[{t:3}],s:"操作成功"}},notStarted:{t:0,b:{t:2,i:[{t:3}],s:"未开始"}},everyWeek:{t:0,b:{t:2,i:[{t:3}],s:"每周"}},sunday:{t:0,b:{t:2,i:[{t:3}],s:"日"}},monday:{t:0,b:{t:2,i:[{t:3}],s:"一"}},tuesday:{t:0,b:{t:2,i:[{t:3}],s:"二"}},wednesday:{t:0,b:{t:2,i:[{t:3}],s:"三"}},thursday:{t:0,b:{t:2,i:[{t:3}],s:"四"}},friday:{t:0,b:{t:2,i:[{t:3}],s:"五"}},saturday:{t:0,b:{t:2,i:[{t:3}],s:"六"}},minutes:{t:0,b:{t:2,i:[{t:3}],s:"分钟"}},times:{t:0,b:{t:2,i:[{t:3}],s:"倍"}},reduce:{t:0,b:{t:2,i:[{t:3}],s:"房价立减"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"元"}},fixedPrice:{t:0,b:{t:2,i:[{t:3}],s:"固定价格"}}},km:{state:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},all:{t:0,b:{t:2,i:[{t:3}],s:"ទាំងអស់"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"មិនត្រឹមត្រូវ"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"ត្រឹមត្រូវ"}},expired:{t:0,b:{t:2,i:[{t:3}],s:"ផុតកំណត់"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទភ្ញៀវ"}},selectGuestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើស"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"យុទ្ធសាស្ត្រ"}},inputStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ គាំទ្រការស្វែងរកអារម្មណ៍"}},filter:{t:0,b:{t:2,i:[{t:3}],s:"តម្រង"}},createPriceStrategy:{t:0,b:{t:2,i:[{t:3}],s:"បង្កើតយុទ្ធសាស្ត្រតម្លៃ"}},details:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានលម្អិត"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួល"}},enable:{t:0,b:{t:2,i:[{t:3}],s:"បើក"}},disable:{t:0,b:{t:2,i:[{t:3}],s:"បិទ"}},strategyContent:{t:0,b:{t:2,i:[{t:3}],s:"ខ្លឹមសារយុទ្ធសាស្ត្រ"}},validDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទមានសុពលភាព"}},creator:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកបង្កើត"}},member:{t:0,b:{t:2,i:[{t:3}],s:"សមាជិក"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"ភ្នាក់ងារ"}},walkIn:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវដោយផ្ទាល់"}},unlimited:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានដែនកំណត់"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"អង្គភាពព្រមព្រៀង"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចុះតម្លៃ"}},delayCheckOut:{t:0,b:{t:2,i:[{t:3}],s:"ពន្យារពេលចាកចេញ"}},pointsMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"ពិន្ទុគុណ"}},giveBreakfast:{t:0,b:{t:2,i:[{t:3}],s:"ផ្តល់អាហារពេលព្រឹក"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"សកម្មភាព"}},saveSuccess:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុកជោគជ័យ"}},notStarted:{t:0,b:{t:2,i:[{t:3}],s:"មិនទាន់ចាប់ផ្តើម"}},everyWeek:{t:0,b:{t:2,i:[{t:3}],s:"រៀងរាល់សប្តាហ៍"}},sunday:{t:0,b:{t:2,i:[{t:3}],s:"អាទិត្យ"}},monday:{t:0,b:{t:2,i:[{t:3}],s:"ចន្ទ"}},tuesday:{t:0,b:{t:2,i:[{t:3}],s:"អង្គារ"}},wednesday:{t:0,b:{t:2,i:[{t:3}],s:"ពុធ"}},thursday:{t:0,b:{t:2,i:[{t:3}],s:"ព្រហស្បតិ៍"}},friday:{t:0,b:{t:2,i:[{t:3}],s:"សុក្រ"}},saturday:{t:0,b:{t:2,i:[{t:3}],s:"សៅរ៍"}},minutes:{t:0,b:{t:2,i:[{t:3}],s:"នាទី"}},times:{t:0,b:{t:2,i:[{t:3}],s:"ដង"}},reduce:{t:0,b:{t:2,i:[{t:3}],s:"កាត់បន្ថយតម្លៃ"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"យ៉ន"}},fixedPrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃថេរ"}}}}})}tt(Z);const et=q(Z,[["__scopeId","data-v-c7eaa426"]]);export{et as default};
//# sourceMappingURL=strategylist-1iZHqp3o.js.map
