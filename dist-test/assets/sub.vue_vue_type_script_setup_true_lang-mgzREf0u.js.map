{"version": 3, "file": "sub.vue_vue_type_script_setup_true_lang-mgzREf0u.js", "sources": ["../../src/layouts/components/Menu/sub.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { OverlayScrollbarsComponentRef } from 'overlayscrollbars-vue'\r\nimport type { SubMenuProps } from './types'\r\nimport { useTimeoutFn } from '@vueuse/core'\r\nimport { OverlayScrollbarsComponent } from 'overlayscrollbars-vue'\r\nimport Item from './item.vue'\r\nimport { rootMenuInjectionKey } from './types'\r\n\r\ndefineOptions({\r\n  name: 'SubMenu',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<SubMenuProps>(),\r\n  {\r\n    level: 0,\r\n  },\r\n)\r\n\r\nconst index = props.menu.path ?? JSON.stringify(props.menu)\r\nconst itemRef = useTemplateRef('itemRef')\r\nconst subMenuRef = useTemplateRef<OverlayScrollbarsComponentRef>('subMenuRef')\r\nconst rootMenu = inject(rootMenuInjectionKey)!\r\n\r\nconst opened = computed(() => {\r\n  return rootMenu.openedMenus.includes(props.uniqueKey.at(-1)!)\r\n})\r\nconst alwaysOpened = computed(() => {\r\n  return rootMenu.alwaysOpenedsMenus.includes(props.uniqueKey.at(-1)!)\r\n})\r\n\r\nconst transitionEvent = computed(() => {\r\n  return rootMenu.isMenuPopup\r\n    ? {\r\n        enter(el: HTMLElement) {\r\n          if (el.offsetHeight > window.innerHeight) {\r\n            el.style.height = `${window.innerHeight}px`\r\n          }\r\n        },\r\n        afterEnter: () => {},\r\n        beforeLeave: (el: HTMLElement) => {\r\n          el.style.overflow = 'hidden'\r\n          el.style.maxHeight = `${el.offsetHeight}px`\r\n        },\r\n        leave: (el: HTMLElement) => {\r\n          el.style.maxHeight = '0'\r\n        },\r\n        afterLeave(el: HTMLElement) {\r\n          el.style.overflow = ''\r\n          el.style.maxHeight = ''\r\n        },\r\n      }\r\n    : {\r\n        enter(el: HTMLElement) {\r\n          const memorizedHeight = el.offsetHeight\r\n          el.style.maxHeight = '0'\r\n          el.style.overflow = 'hidden'\r\n          void el.offsetHeight\r\n          el.style.maxHeight = `${memorizedHeight}px`\r\n        },\r\n        afterEnter(el: HTMLElement) {\r\n          el.style.overflow = ''\r\n          el.style.maxHeight = ''\r\n        },\r\n        beforeLeave(el: HTMLElement) {\r\n          el.style.overflow = 'hidden'\r\n          el.style.maxHeight = `${el.offsetHeight}px`\r\n        },\r\n        leave(el: HTMLElement) {\r\n          el.style.maxHeight = '0'\r\n        },\r\n        afterLeave(el: HTMLElement) {\r\n          el.style.overflow = ''\r\n          el.style.maxHeight = ''\r\n        },\r\n      }\r\n})\r\n\r\nconst transitionClass = computed(() => {\r\n  return rootMenu.isMenuPopup\r\n    ? {\r\n        enterActiveClass: 'ease-in-out duration-300',\r\n        enterFromClass: 'opacity-0 translate-x-4',\r\n        enterToClass: 'opacity-100',\r\n        leaveActiveClass: 'ease-in-out duration-300',\r\n        leaveFromClass: 'opacity-100',\r\n        leaveToClass: 'opacity-0',\r\n      }\r\n    : {\r\n        enterActiveClass: 'ease-in-out duration-300',\r\n        enterFromClass: 'opacity-0 translate-y-4 scale-95 blur-4',\r\n        enterToClass: 'opacity-100 translate-y-0 scale-100 blur-0',\r\n        leaveActiveClass: 'ease-in-out duration-300',\r\n        leaveFromClass: 'opacity-100 translate-y-0 scale-100 blur-0',\r\n        leaveToClass: 'opacity-0 translate-y-4 scale-95 blur-4',\r\n      }\r\n})\r\n\r\nconst hasChildren = computed(() => {\r\n  let flag = true\r\n  if (props.menu.children) {\r\n    if (props.menu.children.every((item: any) => item.meta?.menu === false)) {\r\n      flag = false\r\n    }\r\n  }\r\n  else {\r\n    flag = false\r\n  }\r\n  return flag\r\n})\r\n\r\nfunction handleClick() {\r\n  if (alwaysOpened.value) {\r\n    return\r\n  }\r\n  if (rootMenu.isMenuPopup && hasChildren.value) {\r\n    return\r\n  }\r\n  if (hasChildren.value) {\r\n    rootMenu.handleSubMenuClick(index, props.uniqueKey)\r\n  }\r\n  else {\r\n    rootMenu.handleMenuItemClick(index)\r\n  }\r\n}\r\n\r\nlet timeout: (() => void) | undefined\r\n\r\nfunction handleMouseenter() {\r\n  if (!rootMenu.isMenuPopup) {\r\n    return\r\n  }\r\n  rootMenu.mouseInMenu = props.uniqueKey\r\n  timeout?.()\r\n  ;({ stop: timeout } = useTimeoutFn(() => {\r\n    if (hasChildren.value) {\r\n      rootMenu.openMenu(index, props.uniqueKey)\r\n      nextTick(() => {\r\n        const el = itemRef.value?.ref\r\n        if (!el) {\r\n          return\r\n        }\r\n        let top = 0\r\n        let left = 0\r\n        if (rootMenu.props.mode === 'vertical' || props.level !== 0) {\r\n          top = el.getBoundingClientRect().top + el.scrollTop\r\n          left = (rootMenu.props.direction === 'ltr' ? el.getBoundingClientRect().left : document.documentElement.clientWidth - el.getBoundingClientRect().right) + el.getBoundingClientRect().width\r\n          if (top + subMenuRef.value!.getElement()!.offsetHeight > window.innerHeight) {\r\n            top = window.innerHeight - subMenuRef.value!.getElement()!.offsetHeight\r\n          }\r\n        }\r\n        else {\r\n          top = el.getBoundingClientRect().top + el.getBoundingClientRect().height\r\n          left = rootMenu.props.direction === 'ltr' ? el.getBoundingClientRect().left : document.documentElement.clientWidth - el.getBoundingClientRect().right\r\n          if (top + subMenuRef.value!.getElement()!.offsetHeight > window.innerHeight) {\r\n            subMenuRef.value!.getElement()!.style.height = `${window.innerHeight - top}px`\r\n          }\r\n        }\r\n        if (left + subMenuRef.value!.getElement()!.offsetWidth > document.documentElement.clientWidth) {\r\n          left = (rootMenu.props.direction === 'ltr' ? el.getBoundingClientRect().left : document.documentElement.clientWidth - el.getBoundingClientRect().right) - el.getBoundingClientRect().width\r\n        }\r\n        subMenuRef.value!.getElement()!.style.top = `${top}px`\r\n        subMenuRef.value!.getElement()!.style.insetInlineStart = `${left}px`\r\n      })\r\n    }\r\n    else {\r\n      const path = props.menu.children ? rootMenu.subMenus[index].indexPath.at(-1)! : rootMenu.items[index].indexPath.at(-1)!\r\n      rootMenu.openMenu(path, rootMenu.subMenus[path].indexPath)\r\n    }\r\n  }, 300))\r\n}\r\n\r\nfunction handleMouseleave() {\r\n  if (!rootMenu.isMenuPopup) {\r\n    return\r\n  }\r\n  rootMenu.mouseInMenu = []\r\n  timeout?.()\r\n  ;({ stop: timeout } = useTimeoutFn(() => {\r\n    if (rootMenu.mouseInMenu.length === 0) {\r\n      rootMenu.closeMenu(props.uniqueKey)\r\n    }\r\n    else {\r\n      if (hasChildren.value) {\r\n        !rootMenu.mouseInMenu.includes(props.uniqueKey.at(-1)!) && rootMenu.closeMenu(props.uniqueKey.at(-1)!)\r\n      }\r\n    }\r\n  }, 300))\r\n}\r\n</script>\r\n\r\n<template>\r\n  <Item ref=\"itemRef\" :unique-key=\"uniqueKey\" :item=\"menu\" :level=\"level\" :sub-menu=\"hasChildren\" :expand=\"opened\" :always-expand=\"alwaysOpened\" @click=\"handleClick\" @mouseenter=\"handleMouseenter\" @mouseleave=\"handleMouseleave\" />\r\n  <Teleport v-if=\"hasChildren\" to=\"body\" :disabled=\"!rootMenu.isMenuPopup\">\r\n    <Transition v-bind=\"transitionClass\" v-on=\"transitionEvent\">\r\n      <OverlayScrollbarsComponent\r\n        v-show=\"opened\" ref=\"subMenuRef\" :options=\"{ scrollbars: { visibility: 'hidden' } }\" defer class=\"sub-menu static\" :class=\"{\r\n          'bg-[var(--g-sub-sidebar-bg)]': rootMenu.isMenuPopup,\r\n          'ring-1 ring-stone-2 dark-ring-stone-8 shadow-xl fixed! z-3000 w-[200px]': rootMenu.isMenuPopup,\r\n          'mx-1': rootMenu.isMenuPopup && (rootMenu.props.mode === 'vertical' || level !== 0),\r\n          'py-1': rootMenu.isMenuPopup,\r\n          'rounded-lg': rootMenu.props.rounded,\r\n        }\"\r\n      >\r\n        <template v-for=\"item in menu.children\" :key=\"item.path ?? JSON.stringify(item)\">\r\n          <SubMenu v-if=\"item.meta?.menu !== false\" :unique-key=\"[...uniqueKey, item.path ?? JSON.stringify(item)]\" :menu=\"item\" :level=\"level + 1\" />\r\n        </template>\r\n      </OverlayScrollbarsComponent>\r\n    </Transition>\r\n  </Teleport>\r\n</template>\r\n"], "names": ["props", "__props", "index", "menu", "path", "JSON", "stringify", "itemRef", "useTemplateRef", "subMenuRef", "rootMenu", "inject", "rootMenuInjectionKey", "opened", "computed", "openedMenus", "includes", "<PERSON><PERSON><PERSON>", "at", "alwaysOpened", "alwaysOpenedsMenus", "transitionEvent", "isMenuPopup", "enter", "el", "offsetHeight", "window", "innerHeight", "style", "height", "afterEnter", "beforeLeave", "overflow", "maxHeight", "leave", "afterLeave", "memorizedHeight", "transitionClass", "enterActiveClass", "enterFromClass", "enterToClass", "leaveActiveClass", "leaveFromClass", "leaveToClass", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flag", "children", "every", "item", "_a", "meta", "handleClick", "value", "handleSubMenuClick", "handleMenuItemClick", "timeout", "handleMouseenter", "mouseInMenu", "stop", "useTimeoutFn", "openMenu", "nextTick", "ref", "top", "left", "mode", "level", "getBoundingClientRect", "scrollTop", "direction", "document", "documentElement", "clientWidth", "right", "width", "getElement", "offsetWidth", "insetInlineStart", "subMenus", "indexPath", "items", "handleMouseleave", "length", "closeMenu"], "mappings": "iWAYA,MAAMA,EAAQC,EAORC,EAAQF,EAAMG,KAAKC,MAAQC,KAAKC,UAAUN,EAAMG,MAChDI,EAAUC,EAAe,WACzBC,EAAaD,EAA8C,cAC3DE,EAAWC,EAAOC,GAElBC,EAASC,GAAS,IACfJ,EAASK,YAAYC,SAAShB,EAAMiB,UAAUC,UAEjDC,EAAeL,GAAS,IACrBJ,EAASU,mBAAmBJ,SAAShB,EAAMiB,UAAUC,UAGxDG,EAAkBP,GAAS,IACxBJ,EAASY,YACZ,CACE,KAAAC,CAAMC,GACAA,EAAGC,aAAeC,OAAOC,cAC3BH,EAAGI,MAAMC,OAAS,GAAGH,OAAOC,gBAEhC,EACAG,WAAY,OACZC,YAAcP,IACZA,EAAGI,MAAMI,SAAW,SACpBR,EAAGI,MAAMK,UAAY,GAAGT,EAAGC,gBAAY,EAEzCS,MAAQV,IACNA,EAAGI,MAAMK,UAAY,GAAA,EAEvB,UAAAE,CAAWX,GACTA,EAAGI,MAAMI,SAAW,GACpBR,EAAGI,MAAMK,UAAY,EAAA,GAGzB,CACE,KAAAV,CAAMC,GACJ,MAAMY,EAAkBZ,EAAGC,aAC3BD,EAAGI,MAAMK,UAAY,IACrBT,EAAGI,MAAMI,SAAW,SACfR,EAAGC,aACLD,EAAAI,MAAMK,UAAY,GAAGG,KAC1B,EACA,UAAAN,CAAWN,GACTA,EAAGI,MAAMI,SAAW,GACpBR,EAAGI,MAAMK,UAAY,EACvB,EACA,WAAAF,CAAYP,GACVA,EAAGI,MAAMI,SAAW,SACpBR,EAAGI,MAAMK,UAAY,GAAGT,EAAGC,gBAC7B,EACA,KAAAS,CAAMV,GACJA,EAAGI,MAAMK,UAAY,GACvB,EACA,UAAAE,CAAWX,GACTA,EAAGI,MAAMI,SAAW,GACpBR,EAAGI,MAAMK,UAAY,EAAA,KAKzBI,EAAkBvB,GAAS,IACxBJ,EAASY,YACZ,CACEgB,iBAAkB,2BAClBC,eAAgB,0BAChBC,aAAc,cACdC,iBAAkB,2BAClBC,eAAgB,cAChBC,aAAc,aAEhB,CACEL,iBAAkB,2BAClBC,eAAgB,0CAChBC,aAAc,6CACdC,iBAAkB,2BAClBC,eAAgB,6CAChBC,aAAc,6CAIhBC,EAAc9B,GAAS,KAC3B,IAAI+B,GAAO,EASJ,OARH7C,EAAMG,KAAK2C,SACT9C,EAAMG,KAAK2C,SAASC,OAAOC,UAAmB,OAAe,KAApB,OAAKC,EAAAD,EAAAE,eAAM/C,KAAS,MACxD0C,GAAA,GAIFA,GAAA,EAEFA,CAAA,IAGT,SAASM,IACHhC,EAAaiC,OAGb1C,EAASY,aAAesB,EAAYQ,QAGpCR,EAAYQ,MACL1C,EAAA2C,mBAAmBnD,EAAOF,EAAMiB,WAGzCP,EAAS4C,oBAAoBpD,GAC/B,CAGE,IAAAqD,EAEJ,SAASC,IACF9C,EAASY,cAGdZ,EAAS+C,YAAczD,EAAMiB,UACnB,MAAAsC,GAAAA,MACNG,KAAMH,GAAYI,GAAa,KACjC,GAAIf,EAAYQ,MACL1C,EAAAkD,SAAS1D,EAAOF,EAAMiB,WAC/B4C,GAAS,WACD,MAAArC,EAAK,OAAAyB,EAAQ1C,EAAA6C,YAAO,EAAAH,EAAAa,IAC1B,IAAKtC,EACH,OAEF,IAAIuC,EAAM,EACNC,EAAO,EACiB,aAAxBtD,EAASV,MAAMiE,MAAuC,IAAhBjE,EAAMkE,OAC9CH,EAAMvC,EAAG2C,wBAAwBJ,IAAMvC,EAAG4C,UAC1CJ,GAAqC,QAA7BtD,EAASV,MAAMqE,UAAsB7C,EAAG2C,wBAAwBH,KAAOM,SAASC,gBAAgBC,YAAchD,EAAG2C,wBAAwBM,OAASjD,EAAG2C,wBAAwBO,MACjLX,EAAMtD,EAAW2C,MAAOuB,aAAclD,aAAeC,OAAOC,cAC9DoC,EAAMrC,OAAOC,YAAclB,EAAW2C,MAAOuB,aAAclD,gBAI7DsC,EAAMvC,EAAG2C,wBAAwBJ,IAAMvC,EAAG2C,wBAAwBtC,OAClEmC,EAAoC,QAA7BtD,EAASV,MAAMqE,UAAsB7C,EAAG2C,wBAAwBH,KAAOM,SAASC,gBAAgBC,YAAchD,EAAG2C,wBAAwBM,MAC5IV,EAAMtD,EAAW2C,MAAOuB,aAAclD,aAAeC,OAAOC,cACnDlB,EAAA2C,MAAOuB,aAAc/C,MAAMC,OAAYH,OAAOC,YAAcoC,EAAxB,OAG/CC,EAAOvD,EAAW2C,MAAOuB,aAAcC,YAAcN,SAASC,gBAAgBC,cAChFR,GAAqC,QAA7BtD,EAASV,MAAMqE,UAAsB7C,EAAG2C,wBAAwBH,KAAOM,SAASC,gBAAgBC,YAAchD,EAAG2C,wBAAwBM,OAASjD,EAAG2C,wBAAwBO,OAEvLjE,EAAW2C,MAAOuB,aAAc/C,MAAMmC,IAAM,GAAGA,MAC/CtD,EAAW2C,MAAOuB,aAAc/C,MAAMiD,iBAAmB,GAAGb,KAAI,QAG/D,CACH,MAAM5D,EAAOJ,EAAMG,KAAK2C,SAAWpC,EAASoE,SAAS5E,GAAO6E,UAAU7D,IAAG,GAAOR,EAASsE,MAAM9E,GAAO6E,UAAU7D,IAAK,GACrHR,EAASkD,SAASxD,EAAMM,EAASoE,SAAS1E,GAAM2E,UAAS,IAE1D,MAAG,CAGR,SAASE,IACFvE,EAASY,cAGdZ,EAAS+C,YAAc,GACb,MAAAF,GAAAA,MACNG,KAAMH,GAAYI,GAAa,KACG,IAAhCjD,EAAS+C,YAAYyB,OACdxE,EAAAyE,UAAUnF,EAAMiB,WAGrB2B,EAAYQ,QACb1C,EAAS+C,YAAYzC,SAAShB,EAAMiB,UAAUC,IAAG,KAASR,EAASyE,UAAUnF,EAAMiB,UAAUC,OAChG,GAED,MAAG"}