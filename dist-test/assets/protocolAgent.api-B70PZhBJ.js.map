{"version": 3, "file": "protocolAgent.api-B70PZhBJ.js", "sources": ["../../src/api/modules/pms/protocolagent/protocolAgent.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/protocol-agent'\r\n/**\r\n * 协议单位、中介\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 创建协议单位、中介\r\n   * @param data\r\n   */\r\n  createProtocolAgent: (data: any) => {\r\n    return api.post(`${BASE_PATH}/create`, data)\r\n  },\r\n\r\n  /**\r\n   * 更新协议单位、中介状态\r\n   * @param data\r\n   */\r\n  updateProtocolAgent: (data: any) => {\r\n    return api.put(`${BASE_PATH}/update`, data)\r\n  },\r\n\r\n  /**\r\n   * 更新协议单位、中介状态\r\n   * @param data\r\n   */\r\n  updateProtocolAgentStatus: (data: { gcode: string, paCode: string, isEnable: string }) => {\r\n    return api.put(`${BASE_PATH}/update-status`, data)\r\n  },\r\n\r\n  /**\r\n   * 获得协议单位、中介\r\n   * @param paCode\r\n   */\r\n  getProtocolAgent: (paCode: string) => {\r\n    return api.get(`${BASE_PATH}/get`, {\r\n      params: { paCode },\r\n    })\r\n  },\r\n\r\n  /**\r\n   * 获得协议单位、中介分页\r\n   * @param data\r\n   */\r\n  getProtocolAgentPage: (data: {\r\n    gcode: string\r\n    belongHcode?: string\r\n    paName?: string\r\n    paType?: string\r\n    isEnable?: string\r\n    pageNo: number\r\n    pageSize: number\r\n  }) => {\r\n    return api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    })\r\n  },\r\n\r\n  /**\r\n   * 获得协议单位、中介列表\r\n   * @param data\r\n   */\r\n  getProtocolAgentList: (data: {\r\n    gcode: string\r\n    belongHcode?: string // 所属酒店代码\r\n    isCredit?: string // 是否允许挂账 0:不允许 1：允许\r\n    paType?: string // 类型;0：协议单位 1：中介\r\n    isEnable?: string // 状态;0：无效 1：有效\r\n    isShare?: string // 0:单店使用 1：集团共享；0时挂账的酒店范围就是单店\r\n  }) => {\r\n    return api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    })\r\n  },\r\n\r\n  /**\r\n   * 获得协议单位、中介列表(只包括代码和名称) 用于下拉列表\r\n   * @param data\r\n   */\r\n  getProtocolAgentListSimple: (data: {\r\n    gcode: string\r\n    belongHcode?: string // 所属酒店代码\r\n    isCredit?: string // 是否允许挂账 0:不允许 1：允许\r\n    paType?: string // 类型;0：协议单位 1：中介\r\n    isEnable?: string // 状态;0：无效 1：有效\r\n    isShare?: string // 0:单店使用 1：集团共享；0时挂账的酒店范围就是单店\r\n  }) => {\r\n    return api.get(`${BASE_PATH}/list-simple`, {\r\n      params: data,\r\n    })\r\n  },\r\n}\r\n"], "names": ["BASE_PATH", "protocolAgentApi", "createProtocolAgent", "data", "api", "post", "updateProtocolAgent", "put", "updateProtocolAgentStatus", "getProtocolAgent", "paCode", "get", "params", "getProtocolAgentPage", "getProtocolAgentList", "getProtocolAgentListSimple"], "mappings": "wCAEA,MAAMA,EAAY,+BAIHC,EAAA,CAMbC,oBAAsBC,GACbC,EAAIC,KAAK,GAAGL,WAAoBG,GAOzCG,oBAAsBH,GACbC,EAAIG,IAAI,GAAGP,WAAoBG,GAOxCK,0BAA4BL,GACnBC,EAAIG,IAAI,GAAGP,kBAA2BG,GAO/CM,iBAAmBC,GACVN,EAAIO,IAAI,GAAGX,QAAiB,CACjCY,OAAQ,CAAEF,YAQdG,qBAAuBV,GASdC,EAAIO,IAAI,GAAGX,SAAkB,CAClCY,OAAQT,IAQZW,qBAAuBX,GAQdC,EAAIO,IAAI,GAAGX,SAAkB,CAClCY,OAAQT,IAQZY,2BAA6BZ,GAQpBC,EAAIO,IAAI,GAAGX,gBAAyB,CACzCY,OAAQT"}