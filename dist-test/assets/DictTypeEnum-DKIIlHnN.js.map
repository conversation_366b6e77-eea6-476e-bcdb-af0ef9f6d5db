{"version": 3, "file": "DictTypeEnum-DKIIlHnN.js", "sources": ["../../src/models/dict/DictTypeEnum.ts"], "sourcesContent": ["export enum DictTypeEnum {\r\n  ID_TYPE = 'id_type',\r\n  SHIFT = 'shift',\r\n  POST = 'post',\r\n  PAY_MODE = 'pay_mode',\r\n  PROTOCOL_LEVEL = 'protocol_level',\r\n  BROKERAGE_LEVEL = 'brokerage_level',\r\n  AGENT_LEVEL = 'agent_level',\r\n  ROOM_STATUS = 'room_status',\r\n  MEMBERCONFIG = 'memberconfig',\r\n  MEMBERRULE = 'memberrule',\r\n  SMS_COMPONENT = 'sms_component',\r\n  SMS_TEMPLATE = 'sms_template',\r\n  DISCOUNT_TYPE = 'discount_type',\r\n  RIGHTS_TYPE = 'rights_type',\r\n  PRICE_HANDLE = 'price_handle',\r\n  ROOM_FEATURE = 'room_feature',\r\n  BED_TYPE = 'bed_type',\r\n  BED_SIZE = 'bed_size',\r\n  DELAY = 'delay',\r\n  MULTI_POINT = 'multi_point',\r\n  HOTEL_LEVEL = 'hotel_level',\r\n  HOTEL_STATUS = 'hotel_status',\r\n  HOTEL_SERVICE = 'service',\r\n  CHANNEL = 'channel',\r\n  HOTEL_TYPE = 'hotel_type',\r\n  CONSUME_ACCOUNT = 'consume_account',\r\n  CONSUME_ACCOUNT_ROOM_FEE = 'room_fee0',\r\n  PAY_ACCOUNT = 'pay_account',\r\n  BANK_TYPE = 'bank_type',\r\n  CONSUME_SCENE = 'consume_scene',\r\n  POINT_SCENE = 'point_scene',\r\n  GUEST_SRC_TYPE = 'guest_src_type',\r\n  CHECKIN_TYPE = 'checkin_type',\r\n  DICT_TYPE_NATION = 'nation',\r\n  BIZ_TYPE = 'biz_type',\r\n  RECHARGE_CHANNEL = 'recharge_channel',\r\n  COUPON_TYPE = 'coupon_type',\r\n  PROMOTION_DISCOUNT_TYPE = 'promotion_discount_type',\r\n  BOOK_STATUS = 'book_status',\r\n  CONTINUE_PRICE = 'continue_price',\r\n}\r\n/** 会员权益枚举 */\r\nexport enum memeberTypeEnum {\r\n  /** 预订折扣 */\r\n  BOOKING_DISCOUNT = 'booking_discount',\r\n  /** 升级优惠券 */\r\n  ROOM_UPGRADE_VOUCHER = 'room_upgrade_voucher',\r\n  /** 赠送早餐 */\r\n  REEE_BREAKFAST = 'free_breakfast',\r\n  /** 延迟退房 */\r\n  LATE_CHECK_OUT = 'late_check_out',\r\n  /** 购卡赠积分 */\r\n  PURCHASE_MEMBERSHIP_POINT = 'purchase_membership_point',\r\n  /** 提前入住 */\r\n  EARLY_CHECK_IN = 'early_check_in ',\r\n  /** 积分抵房费 */\r\n  REDEEM_POINT = 'redeem_point',\r\n  /** 查看全部 */\r\n  VIEW_ALL = 'view_all',\r\n}\r\n"], "names": ["DictTypeEnum", "memeberTypeEnum"], "mappings": "AAAY,IAAAA,GAAAA,IACVA,EAAU,QAAA,UACVA,EAAQ,MAAA,QACRA,EAAO,KAAA,OACPA,EAAW,SAAA,WACXA,EAAiB,eAAA,iBACjBA,EAAkB,gBAAA,kBAClBA,EAAc,YAAA,cACdA,EAAc,YAAA,cACdA,EAAe,aAAA,eACfA,EAAa,WAAA,aACbA,EAAgB,cAAA,gBAChBA,EAAe,aAAA,eACfA,EAAgB,cAAA,gBAChBA,EAAc,YAAA,cACdA,EAAe,aAAA,eACfA,EAAe,aAAA,eACfA,EAAW,SAAA,WACXA,EAAW,SAAA,WACXA,EAAQ,MAAA,QACRA,EAAc,YAAA,cACdA,EAAc,YAAA,cACdA,EAAe,aAAA,eACfA,EAAgB,cAAA,UAChBA,EAAU,QAAA,UACVA,EAAa,WAAA,aACbA,EAAkB,gBAAA,kBAClBA,EAA2B,yBAAA,YAC3BA,EAAc,YAAA,cACdA,EAAY,UAAA,YACZA,EAAgB,cAAA,gBAChBA,EAAc,YAAA,cACdA,EAAiB,eAAA,iBACjBA,EAAe,aAAA,eACfA,EAAmB,iBAAA,SACnBA,EAAW,SAAA,WACXA,EAAmB,iBAAA,mBACnBA,EAAc,YAAA,cACdA,EAA0B,wBAAA,0BAC1BA,EAAc,YAAA,cACdA,EAAiB,eAAA,iBAxCPA,IAAAA,GAAA,CAAA,GA2CAC,GAAAA,IAEVA,EAAmB,iBAAA,mBAEnBA,EAAuB,qBAAA,uBAEvBA,EAAiB,eAAA,iBAEjBA,EAAiB,eAAA,iBAEjBA,EAA4B,0BAAA,4BAE5BA,EAAiB,eAAA,kBAEjBA,EAAe,aAAA,eAEfA,EAAW,SAAA,WAhBDA,IAAAA,GAAA,CAAA"}