import{d as e,aj as t,ai as s,b as a,y as i,aR as l,aq as r,u as n,o,c,f as b,w as m,g as p,h as d,Y as u,i as h,aS as g,m as S,j as f,k as C,x as P,aT as N}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{s as v}from"./sign.api-B2QDiYWD.js";import{_ as T}from"./_plugin-vue_export-helper-BCo6x5W8.js";const y={class:"flex-banner"},_={class:"form-left"},w={class:"form-right"},k=e({__name:"myTemplateEdit",props:{templateCode:{default:""},signCode:{},merchants:{}},setup(e,{expose:T}){const k=e,{t:x}=t(),A=s(),j=a(!1),I=a(),R=a({templateCode:k.templateCode,gcode:A.gcode,hcode:A.hcode,templateName:"",content:"",isSys:"0",templateType:"0",isG:"0",state:"0",reason:"",scene:"",channer:""}),V=a({templateName:[{required:!0,message:x("enterTemplateName"),trigger:"blur"}],content:[{required:!0,message:x("enterTemplateContent"),trigger:"blur"}]});a([]),i((()=>{})),T({submit:()=>new Promise((e=>{""===R.value.templateCode&&I.value&&I.value.validate((e=>{if(e){const e={id:2,cellNumber:"13875935213",description:"申请签名",gcode:"1781282000378556416",hcode:"1781282590420660224",isGroup:0,licenseUrl:"http://sms.yiduohua.net/abc.jpg",proveType:1,sign:"一朵花",website:"pms.yiduohua.net"};v.update(e).then((()=>{l.success({message:x("addSuccess"),center:!0})}))}}))}))}),a(0);const L=a(null),U=a("");function G(e){if(e=`#${e}#`,L.value){const t=L.value.$el.querySelector("textarea");if(t){const s=t.selectionStart,a=t.selectionEnd,i=`${e}`;U.value=U.value.substring(0,s)+i+U.value.substring(a,U.value.length),t.selectionStart=s+e.length,t.selectionEnd=s+e.length}}}function O(e){U.value=e.target.value}return(e,t)=>{const s=g,a=S,i=f,l=C,v=P,T=N;return r((o(),c("div",null,[b(v,{ref_key:"formRef",ref:I,model:n(R),rules:n(V),"label-width":"120px","label-suffix":"："},{default:m((()=>[p("div",y,[p("div",_,[b(a,{label:n(x)("templateName"),prop:"templateName"},{default:m((()=>[b(s,{modelValue:n(R).templateName,"onUpdate:modelValue":t[0]||(t[0]=e=>n(R).templateName=e),placeholder:n(x)("enterTemplateName")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),b(a,{label:n(x)("selectSendingScene")},{default:m((()=>[b(l,{modelValue:n(R).scene,"onUpdate:modelValue":t[1]||(t[1]=e=>n(R).scene=e),"collapse-tags":"","collapse-tags-tooltip":"",placeholder:n(x)("selectSendingScene"),style:{width:"150px"}},{default:m((()=>[b(i,{label:n(x)("addOrder"),value:"1"},null,8,["label"]),b(i,{label:n(x)("cancelOrder"),value:"2"},null,8,["label"])])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),b(a,{label:n(x)("selectApplicationChannel")},{default:m((()=>[b(l,{modelValue:n(R).channer,"onUpdate:modelValue":t[2]||(t[2]=e=>n(R).channer=e),"collapse-tags":"","collapse-tags-tooltip":"",placeholder:n(x)("selectApplicationChannel"),style:{width:"150px"}},{default:m((()=>[b(i,{label:n(x)("store"),value:"1"},null,8,["label"]),b(i,{label:n(x)("wechat"),value:"2"},null,8,["label"])])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]),b(a,{label:n(x)("smsPreview")},{default:m((()=>[d(u(n(U)),1)])),_:1},8,["label"])]),p("div",w,[b(a,{label:n(x)("hotelParameters")},{default:m((()=>[p("div",{class:"label-flex",onClick:t[3]||(t[3]=e=>G("gropName"))},u(n(x)("groupName")),1)])),_:1},8,["label"]),b(a,{label:n(x)("orderParameters")},{default:m((()=>[p("div",{class:"label-flex",onClick:t[4]||(t[4]=e=>G("roomTime"))},u(n(x)("checkInTime")),1)])),_:1},8,["label"]),b(a,{label:n(x)("guestParameters")},{default:m((()=>[p("div",{class:"label-flex",onClick:t[5]||(t[5]=e=>G("memenberCard"))},u(n(x)("membershipCardNumber")),1)])),_:1},8,["label"]),b(a,null,{default:m((()=>[b(s,{ref_key:"textareaRef",ref:L,modelValue:n(U),"onUpdate:modelValue":t[6]||(t[6]=e=>h(U)?U.value=e:null),type:"textarea",rows:6,placeholder:n(x)("signatureContentPlaceholder"),onInput:O},null,8,["modelValue","placeholder"])])),_:1})])])])),_:1},8,["model","rules"])])),[[T,n(j)]])}}});function x(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Edit Successful"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Add Successful"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"Submit"}},templateName:{t:0,b:{t:2,i:[{t:3}],s:"Template Name"}},enterTemplateName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the template name"}},templateContent:{t:0,b:{t:2,i:[{t:3}],s:"Template Content"}},enterTemplateContent:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the template content"}},selectSendingScene:{t:0,b:{t:2,i:[{t:3}],s:"Please select the sending scene"}},selectApplicationChannel:{t:0,b:{t:2,i:[{t:3}],s:"Please select the application channel"}},smsPreview:{t:0,b:{t:2,i:[{t:3}],s:"SMS Preview"}},hotelParameters:{t:0,b:{t:2,i:[{t:3}],s:"Hotel Parameters"}},groupName:{t:0,b:{t:2,i:[{t:3}],s:"Group Name"}},orderParameters:{t:0,b:{t:2,i:[{t:3}],s:"Order Parameters"}},checkInTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Time"}},guestParameters:{t:0,b:{t:2,i:[{t:3}],s:"Guest Parameters"}},membershipCardNumber:{t:0,b:{t:2,i:[{t:3}],s:"Membership Card Number"}},accessibleWebsite:{t:0,b:{t:2,i:[{t:3}],s:"Accessible Website"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"Contact Phone"}},applicationDescription:{t:0,b:{t:2,i:[{t:3}],s:"Application Description"}},isGroupOwned:{t:0,b:{t:2,i:[{t:3}],s:"Is Group Owned"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"Yes"}},no:{t:0,b:{t:2,i:[{t:3}],s:"No"}},associatedStore:{t:0,b:{t:2,i:[{t:3}],s:"Associated Store"}},selectStore:{t:0,b:{t:2,i:[{t:3}],s:"Please select a store"}},licenseImageURL:{t:0,b:{t:2,i:[{t:3}],s:"License Image URL"}},fileType:{t:0,b:{t:2,i:[{t:3}],s:"File Type"}},companyLicense:{t:0,b:{t:2,i:[{t:3}],s:"Company License (Signature is Company Name)"}},appStoreScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"App Store Backend Screenshot (Signature is App Name)"}},icpRecordScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"ICP Record Screenshot (Signature is Website Name)"}},wechatPlatformScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"WeChat Public Platform Screenshot (Signature is Public Account/Mini Program Name)"}},trademarkCertificate:{t:0,b:{t:2,i:[{t:3}],s:"Trademark Registration Certificate/Trademark Soft Copyright Proof (Signature is Trademark Name)"}},signatureContent:{t:0,b:{t:2,i:[{t:3}],s:"Signature Content"}},signatureContentPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the signature content"}},editSignature:{t:0,b:{t:2,i:[{t:3}],s:"Edit Signature"}},addSignature:{t:0,b:{t:2,i:[{t:3}],s:"Add Signature"}},selectFileType:{t:0,b:{t:2,i:[{t:3}],s:"Please select file type"}}},"zh-cn":{editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"编辑成功"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"新增成功"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"提交"}},templateName:{t:0,b:{t:2,i:[{t:3}],s:"模板名称"}},enterTemplateName:{t:0,b:{t:2,i:[{t:3}],s:"请输入模板名称"}},templateContent:{t:0,b:{t:2,i:[{t:3}],s:"模板内容"}},enterTemplateContent:{t:0,b:{t:2,i:[{t:3}],s:"请输入模板内容"}},selectSendingScene:{t:0,b:{t:2,i:[{t:3}],s:"请选择发送场景"}},selectApplicationChannel:{t:0,b:{t:2,i:[{t:3}],s:"请选择应用渠道"}},smsPreview:{t:0,b:{t:2,i:[{t:3}],s:"短信预览"}},hotelParameters:{t:0,b:{t:2,i:[{t:3}],s:"酒店参数"}},groupName:{t:0,b:{t:2,i:[{t:3}],s:"集团名称"}},orderParameters:{t:0,b:{t:2,i:[{t:3}],s:"订单参数"}},checkInTime:{t:0,b:{t:2,i:[{t:3}],s:"入住时间"}},guestParameters:{t:0,b:{t:2,i:[{t:3}],s:"客人参数"}},membershipCardNumber:{t:0,b:{t:2,i:[{t:3}],s:"会员卡号"}},accessibleWebsite:{t:0,b:{t:2,i:[{t:3}],s:"可访问网址"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"联系人电话"}},applicationDescription:{t:0,b:{t:2,i:[{t:3}],s:"申请说明"}},isGroupOwned:{t:0,b:{t:2,i:[{t:3}],s:"是否为集团所有"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"是"}},no:{t:0,b:{t:2,i:[{t:3}],s:"否"}},associatedStore:{t:0,b:{t:2,i:[{t:3}],s:"所属门店"}},selectStore:{t:0,b:{t:2,i:[{t:3}],s:"请选择门店"}},licenseImageURL:{t:0,b:{t:2,i:[{t:3}],s:"营业执照图片URL"}},fileType:{t:0,b:{t:2,i:[{t:3}],s:"文件类型"}},companyLicense:{t:0,b:{t:2,i:[{t:3}],s:"公司营业执照(签名是公司名称)"}},appStoreScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"应用商店APP管理后台全屏截图(签名是APP名称)"}},icpRecordScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"ICP备案截图(签名是网站名称)"}},wechatPlatformScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"微信公众平台管理界面全屏截图(签名是公众号/小程序名称)"}},trademarkCertificate:{t:0,b:{t:2,i:[{t:3}],s:"商标注册证书/商标软著权证明(签名是商标名称)"}},signatureContent:{t:0,b:{t:2,i:[{t:3}],s:"签名内容"}},signatureContentPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"请输入签名内容"}},editSignature:{t:0,b:{t:2,i:[{t:3}],s:"编辑签名"}},addSignature:{t:0,b:{t:2,i:[{t:3}],s:"新增签名"}},selectFileType:{t:0,b:{t:2,i:[{t:3}],s:"请选择文件类型"}}},km:{editSuccess:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួលជោគជ័យ"}},addSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែមជោគជ័យ"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"ដាក់ស្នើ"}},templateName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះគំរូ"}},enterTemplateName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះគំរូ"}},templateContent:{t:0,b:{t:2,i:[{t:3}],s:"មាតិកាគំរូ"}},enterTemplateContent:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលមាតិកាគំរូ"}},selectSendingScene:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឈុតផ្ញើ"}},selectApplicationChannel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឆានែលកម្មវិធី"}},smsPreview:{t:0,b:{t:2,i:[{t:3}],s:"មើលសារមុនផ្ញើ"}},hotelParameters:{t:0,b:{t:2,i:[{t:3}],s:"ប៉ារ៉ាម៉ែត្រសណ្ឋាគារ"}},groupName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះក្រុមហ៊ុន"}},orderParameters:{t:0,b:{t:2,i:[{t:3}],s:"ប៉ារ៉ាម៉ែត្រការបញ្ជាទិញ"}},checkInTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចូលស្នាក់នៅ"}},guestParameters:{t:0,b:{t:2,i:[{t:3}],s:"ប៉ារ៉ាម៉ែត្រភ្ញៀវ"}},membershipCardNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខកាតសមាជិក"}},accessibleWebsite:{t:0,b:{t:2,i:[{t:3}],s:"គេហទំព័រដែលអាចចូលប្រើបាន"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"លេខទូរស័ព្ទទំនាក់ទំនង"}},applicationDescription:{t:0,b:{t:2,i:[{t:3}],s:"ការពិពណ៌នាកម្មវិធី"}},isGroupOwned:{t:0,b:{t:2,i:[{t:3}],s:"ជាកម្មសិទ្ធិរបស់ក្រុមហ៊ុន"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"បាទ/ចាស"}},no:{t:0,b:{t:2,i:[{t:3}],s:"ទេ"}},associatedStore:{t:0,b:{t:2,i:[{t:3}],s:"ហាងដែលភ្ជាប់"}},selectStore:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសហាង"}},licenseImageURL:{t:0,b:{t:2,i:[{t:3}],s:"URL រូបភាពអាជ្ញាប័ណ្ណ"}},fileType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទឯកសារ"}},companyLicense:{t:0,b:{t:2,i:[{t:3}],s:"អាជ្ញាប័ណ្ណក្រុមហ៊ុន (ហត្ថលេខាគឺឈ្មោះក្រុមហ៊ុន)"}},appStoreScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"រូបថតអេក្រង់ផ្នែកខាងក្រោយ App Store (ហត្ថលេខាគឺឈ្មោះកម្មវិធី)"}},icpRecordScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"រូបថតកំណត់ត្រា ICP (ហត្ថលេខាគឺឈ្មោះគេហទំព័រ)"}},wechatPlatformScreenshot:{t:0,b:{t:2,i:[{t:3}],s:"រូបថតអេក្រង់វេទិកាសាធារណៈ WeChat (ហត្ថលេខាគឺឈ្មោះគណនីសាធារណៈ/កម្មវិធីតូច)"}},trademarkCertificate:{t:0,b:{t:2,i:[{t:3}],s:"វិញ្ញាបនប័ត្រកំណត់សញ្ញាពាណិជ្ជកម្ម/ភស្តុតាងសិទ្ធិអ្នកនិពន្ធសញ្ញាពាណិជ្ជកម្ម (ហត្ថលេខាគឺឈ្មោះសញ្ញាពាណិជ្ជកម្ម)"}},signatureContent:{t:0,b:{t:2,i:[{t:3}],s:"មាតិកាហត្ថលេខា"}},signatureContentPlaceholder:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលមាតិកាហត្ថលេខា"}},editSignature:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួលហត្ថលេខា"}},addSignature:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែមហត្ថលេខា"}},selectFileType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទឯកសារ"}}}}})}x(k);const A=T(k,[["__scopeId","data-v-9cdffd41"]]);export{A as default};
//# sourceMappingURL=myTemplateEdit-rZOGbXZG.js.map
