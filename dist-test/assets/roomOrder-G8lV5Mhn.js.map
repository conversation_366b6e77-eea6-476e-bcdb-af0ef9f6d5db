{"version": 3, "file": "roomOrder-G8lV5Mhn.js", "sources": ["../../src/views/room/realtime/components/roomOrder.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"options\":{\r\n      \"meituan\": \"meituan\",\r\n      \"ctrip\": \"ctrip\",\r\n    },\r\n    \"viewDetails\":\"View details\",\r\n    \"lobby\":\"lobby\",\r\n    \"download_hotel_agent\": \"Download Hotel-Agent Client\",\r\n    \"ota_operation_requires_agent\": \"OTA operations can only be performed in Hotel-Agent, please {downloadLink}.\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"options\":{\r\n      \"meituan\": \"美团\",\r\n      \"ctrip\": \"携程\",\r\n    },\r\n    \"viewDetails\":\"查看详情\",\r\n    \"lobby\":\"门店\",\r\n    \"download_hotel_agent\": \"下载Hotel-Agent客户端\",\r\n    \"ota_operation_requires_agent\": \"读取OTA只能在Hotel-Agent中操作，请{downloadLink}。\"\r\n  },\r\n  \"km\": {\r\n    \"options\":{\r\n      \"meituan\": \"មីទួន\",\r\n      \"ctrip\": \"ស៊ីត្រីប\",\r\n    },\r\n    \"viewDetails\":\"មើលព័ត៌មានលម្អិត\",\r\n    \"lobby\":\"ហាងលក់\",\r\n    \"download_hotel_agent\": \"ទាញយក Hotel-Agent Client\",\r\n    \"ota_operation_requires_agent\": \"ប្រតិបត្តិការ OTA អាចធ្វើបានតែនៅក្នុង Hotel-Agent ប៉ុណ្ណោះ សូម{downloadLink}។\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type roomOrderList from './roomOrderList.ts'\r\nimport { roomStatusApi } from '@/api/modules/index'\r\nimport otaApi from '@/api/modules/pms/serviceintegration/serviceintegration.api'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { CardReader } from '@/store/websocket/CardReader'\r\nimport { objectToUrlParams, parseQuery } from '@/utils/index'\r\nimport dayjs from 'dayjs'\r\nimport roomOrderListVue from './roomOrderList.vue'\r\n\r\ndefineOptions({\r\n  name: 'RoomOrder', // 右侧ota订单\r\n})\r\nconst { t } = useI18n()\r\n/** 获取缓存数据 */\r\nconst userStore = useUserStore()\r\n/** gcode、hcode 集合 */\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n/** 订单助手tabs */\r\nconst activeName = ref('1')\r\n/** ota名称 */\r\nconst otaName = ref('')\r\n/** 加载状态 */\r\nconst loading = ref(false)\r\n/** otaList请求接口 */\r\nconst otaApiList = ref()\r\n/** ota列表数据 */\r\nconst dataList = reactive<any[]>([])\r\n/** ota列表总数 */\r\nconst dataListTotal = ref(0)\r\n/** ota radio列表 */\r\nconst otaOptions = reactive<roomOrderList.otaOptions[]>([\r\n  {\r\n    value: 'meituan',\r\n    label: t('options.meituan'),\r\n  },\r\n  {\r\n    value: 'ctrip',\r\n    label: t('options.ctrip'),\r\n  },\r\n])\r\n/**\r\n * 拿先定义的tabs去过滤获取到的tabs\r\n * 并且state===1，因为0是没有开启  1是已开启\r\n */\r\nconst newOtaOptions = ref<roomOrderList.otaOptions[]>([])\r\nonMounted(async () => {\r\n  const { data } = await otaApi.getOtaServiceIntegration(queryParams)\r\n  /** 过滤是否已开通 */\r\n  newOtaOptions.value = otaOptions.filter((itemA) => data.find((itemB) => itemB.state != 0 && itemA.value == itemB.solutionProvider))\r\n  otaName.value = newOtaOptions.value[0].value\r\n  if (newOtaOptions.value.length > 0) {\r\n    await getOtaApiList()\r\n    onClickOta()\r\n    return true\r\n  }\r\n})\r\n/** 获取OTA_api列表 */\r\nasync function getOtaApiList() {\r\n  const { data } = await roomStatusApi.getOtaApiList(queryParams)\r\n  otaApiList.value = data.map((e: any) => {\r\n    e.headers = e.headers.split('|||')\r\n    const arr: any = []\r\n    // headers 字符串转数组\r\n    e.headers.map((item: any) => {\r\n      // 使用正则表达式查找第一个冒号的位置\r\n      const match = item.search(':')\r\n      let parts: any\r\n      if (match !== -1) {\r\n        // 分割字符串成数组\r\n        parts = [item.substring(0, match), item.substring(match + 1)]\r\n      }\r\n      item = parts.reduce((acc, curr, index: number) => {\r\n        if (index % 2 === 0) {\r\n          acc[curr] = parts[index + 1]\r\n        }\r\n        return acc\r\n      }, {})\r\n      arr.push(item)\r\n    })\r\n    // 把数组转成对象\r\n    e.headers = Object.assign({}, ...arr)\r\n    return e\r\n  })\r\n}\r\n/** tabs点击切换前进行判断 */\r\nasync function beforeHandleTabsClick(index: any) {\r\n  if (index === 2) {\r\n    if (typeof (window as any).CallBridge === 'undefined' && typeof (window as any).__RUNNING_IN_PMS_AGENT__ === 'undefined') {\r\n      // 提示没有下载客户端不能打开OTA订单，并提示下载客户端的路径\r\n      const downloadLink = `<a href=\"${import.meta.env.VITE_APP_CLIENT_DOWNLOAD_URL}\" target=\"_blank\">${t('download_hotel_agent')}</a>`\r\n      ElMessage({\r\n        showClose: true,\r\n        message: t('ota_operation_requires_agent', { downloadLink }),\r\n        type: 'warning',\r\n        dangerouslyUseHTMLString: true,\r\n      })\r\n      return false\r\n    }\r\n    const { data } = await otaApi.getOtaServiceIntegration(queryParams)\r\n    /** 过滤是否已开通 */\r\n    newOtaOptions.value = otaOptions.filter((itemA) => data.find((itemB) => itemB.state !== 0 && itemA.value === itemB.solutionProvider))\r\n    otaName.value = newOtaOptions.value[0].value\r\n    if (newOtaOptions.value.length > 0) {\r\n      activeName.value = index\r\n      await getOtaApiList()\r\n      onClickOta()\r\n      return true\r\n    }\r\n    return false\r\n  } else {\r\n    activeName.value = index\r\n    return true\r\n  }\r\n}\r\n/**\r\n * 点击请求第三方的数据\r\n * @param args 从子级传过来的值\r\n */\r\nasync function onClickOta(...args: any[]) {\r\n  const channel = otaName.value\r\n  loading.value = true\r\n  const value = otaApiList.value.filter((e: any) => e.channel === channel && e.method === 'order_list')[0]\r\n  let json = {}\r\n  /** 没有分页时 把datalist数组置空 */\r\n  if (args.length === 0) {\r\n    dataList.length = 0\r\n  }\r\n  const query = value.url.split('?')[0] // 保留url前面https\r\n  /** 解析url */\r\n  const urlObj: {\r\n    /** 美团分页的值 */\r\n    offset?: string | number\r\n    startTime?: string | number\r\n    endTime?: string | number\r\n    filter?: string | number\r\n    searchTimeType?: string | number\r\n    sortField?: string | number\r\n    sortType?: string | number\r\n    orderId?: string | number\r\n  } = parseQuery(value.url)\r\n  urlObj.offset = 0\r\n  /** 获取当天时间戳到毫秒级，0点0分0秒 */\r\n  urlObj.startTime = dayjs().startOf('day').valueOf()\r\n  /** 获取当天时间戳往后推六个月到毫秒级，0点0分0秒 */\r\n  urlObj.endTime = dayjs().add(6, 'month').startOf('day').valueOf()\r\n  /** 往下滚动时增加需要的条数 */\r\n  if (args.length > 0) {\r\n    urlObj.offset += args[0].meituan\r\n  }\r\n  /** 重新拼接成url传给盒子请求美团的接口 */\r\n  const url = `${query}?${objectToUrlParams(urlObj)}`\r\n  const body = JSON.parse(value.body)\r\n  if (body) {\r\n    body.customFilter = 'None'\r\n    body.orderStatus = 2\r\n  }\r\n  json = {\r\n    /** 请求方式 */\r\n    method: 'otaRequest',\r\n    /** api */\r\n    api: {\r\n      /** 请求路径  如果是请求美团的则需要重新编译url请求地址 */\r\n      url: channel === 'meituan' ? url : value.url,\r\n      /** 请求方式 */\r\n      requestMethod: value.requestMethod,\r\n      /** 内容 */\r\n      body: JSON.stringify(body),\r\n      /** 第三方名称 */\r\n      channel,\r\n      /** 请求头 */\r\n      headers: value.headers,\r\n    },\r\n  }\r\n  // 把对象转字符串\r\n  const jsonStr = JSON.stringify(json)\r\n  // 每次点击都要先关闭websocket\r\n  if (CardReader.isConnected) {\r\n    CardReader.closeSocket()\r\n    // 等待WebSocket完全关闭\r\n    await new Promise((resolve) => setTimeout(resolve, 300))\r\n  }\r\n  CardReader.initCardReader((message: string) => {\r\n    // 解析 message 字符串为 JavaScript 对象\r\n    const { response } = JSON.parse(message)\r\n    if (response.data) {\r\n      dataListTotal.value = response.data.total\r\n      if (dataList.length < dataListTotal.value) {\r\n        /** 美团 */\r\n        if (response.data.results?.length > 0) {\r\n          response.data.results.forEach((item: any) => dataList.push(item))\r\n        }\r\n        /** 携程 */\r\n        if (response.data.orders?.length > 0) {\r\n          response.data.orders\r\n            .map((e: any) => {\r\n              // 把入住人分割成数组\r\n              const arr: { name: string; roomIndex: number }[] = []\r\n              e.clientName.split(',').forEach((item: any, index: number) => {\r\n                arr.push({ name: item, roomIndex: index })\r\n              })\r\n              e.clientName = arr\r\n              return e\r\n            })\r\n            .forEach((item: any) => dataList.push(item))\r\n        }\r\n      }\r\n    }\r\n    loading.value = false\r\n  })\r\n  // 触发读取信息\r\n  const timer = setInterval(() => {\r\n    if (CardReader.isConnected) {\r\n      CardReader.otaRequest(jsonStr)\r\n      clearInterval(timer)\r\n    }\r\n  }, 200)\r\n}\r\n/** radio 第三方酒店请求 */\r\nfunction handleClick(value: any) {\r\n  otaName.value = value\r\n  onClickOta()\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"roomOrder\">\r\n    <el-tabs v-model=\"activeName\" :before-leave=\"beforeHandleTabsClick\">\r\n      <!-- <el-tab-pane :label=\"t('lobby')\" name=\"1\">{{ t('lobby') }}</el-tab-pane> -->\r\n      <el-tab-pane label=\"OTA\" name=\"1\">\r\n        <el-radio-group v-model=\"otaName\" size=\"small\" @change=\"handleClick\">\r\n          <el-radio v-for=\"(item, index) in newOtaOptions\" :key=\"index\" border :value=\"item.value\">\r\n            {{ item.label }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n        <div v-loading=\"loading\">\r\n          <roomOrderListVue class=\"roomOrder-row\" :ota-name=\"otaName\" :data-list=\"dataList\" :ota-api-list=\"otaApiList\" :ota-options=\"otaOptions\" :total=\"dataListTotal\" @submit=\"onClickOta\" />\r\n        </div>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.roomOrder {\r\n  :deep(.el-radio) {\r\n    @apply mr-[10px] px-[7px];\r\n    .el-radio__input {\r\n      display: none;\r\n    }\r\n    .el-radio__label {\r\n      padding-left: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "queryParams", "reactive", "gcode", "hcode", "activeName", "ref", "otaName", "loading", "otaApiList", "dataList", "dataListTotal", "otaOptions", "value", "label", "newOtaOptions", "async", "getOtaApiList", "data", "roomStatusApi", "map", "e", "headers", "split", "arr", "item", "match", "search", "parts", "substring", "reduce", "acc", "curr", "index", "push", "Object", "assign", "beforeHandleTabsClick", "window", "CallBridge", "__RUNNING_IN_PMS_AGENT__", "downloadLink", "ElMessage", "showClose", "message", "type", "dangerouslyUseHTMLString", "otaApi", "getOtaServiceIntegration", "filter", "itemA", "find", "itemB", "state", "solutionProvider", "length", "onClickOta", "args", "channel", "method", "json", "query", "url", "url<PERSON>bj", "parse<PERSON><PERSON>y", "offset", "startTime", "dayjs", "startOf", "valueOf", "endTime", "add", "meituan", "objectToUrlParams", "body", "JSON", "parse", "customFilter", "orderStatus", "api", "requestMethod", "stringify", "jsonStr", "<PERSON><PERSON><PERSON><PERSON>", "isConnected", "closeSocket", "Promise", "resolve", "setTimeout", "initCardReader", "response", "total", "_a", "results", "for<PERSON>ach", "_b", "orders", "clientName", "name", "roomIndex", "timer", "setInterval", "otaRequest", "clearInterval", "handleClick", "onMounted"], "mappings": "8nCAgDM,MAAAA,EAAEA,GAAMC,IAERC,EAAYC,IAEZC,EAAcC,EAAiB,CACnCC,MAAOJ,EAAUI,MACjBC,MAAOL,EAAUK,QAGbC,EAAaC,EAAI,KAEjBC,EAAUD,EAAI,IAEdE,EAAUF,GAAI,GAEdG,EAAaH,IAEbI,EAAWR,EAAgB,IAE3BS,EAAgBL,EAAI,GAEpBM,EAAaV,EAAqC,CACtD,CACEW,MAAO,UACPC,MAAOjB,EAAE,oBAEX,CACEgB,MAAO,QACPC,MAAOjB,EAAE,oBAOPkB,EAAgBT,EAAgC,IAatDU,eAAeC,IACb,MAAMC,KAAEA,SAAeC,EAAcF,cAAchB,GACnDQ,EAAWI,MAAQK,EAAKE,KAAKC,IAC3BA,EAAEC,QAAUD,EAAEC,QAAQC,MAAM,OAC5B,MAAMC,EAAW,GAoBV,OAlBLH,EAAAC,QAAQF,KAAKK,IAEP,MAAAC,EAAQD,EAAKE,OAAO,KACtB,IAAAC,GACc,IAAdF,IAEME,EAAA,CAACH,EAAKI,UAAU,EAAGH,GAAQD,EAAKI,UAAUH,EAAQ,KAE5DD,EAAOG,EAAME,QAAO,CAACC,EAAKC,EAAMC,KAC1BA,EAAQ,GAAM,IAChBF,EAAIC,GAAQJ,EAAMK,EAAQ,IAErBF,IACN,IACHP,EAAIU,KAAKT,EAAI,IAGfJ,EAAEC,QAAUa,OAAOC,OAAO,CAAC,KAAMZ,GAC1BH,CAAA,GACR,CAGHL,eAAeqB,EAAsBJ,GACnC,GAAc,IAAVA,EAAa,CACf,QAA0C,IAA9BK,OAAeC,iBAAkF,IAA5CD,OAAeE,yBAA0C,CAElH,MAAAC,EAAe,wHAA6E5C,EAAE,8BAO7F,OANG6C,EAAA,CACRC,WAAW,EACXC,QAAS/C,EAAE,+BAAgC,CAAE4C,iBAC7CI,KAAM,UACNC,0BAA0B,KAErB,CAAA,CAET,MAAM5B,KAAEA,SAAe6B,EAAOC,yBAAyB/C,GAInD,OAFJc,EAAcF,MAAQD,EAAWqC,QAAQC,GAAUhC,EAAKiC,MAAMC,GAA0B,IAAhBA,EAAMC,OAAeH,EAAMrC,QAAUuC,EAAME,qBACnH/C,EAAQM,MAAQE,EAAcF,MAAM,GAAGA,MACnCE,EAAcF,MAAM0C,OAAS,IAC/BlD,EAAWQ,MAAQoB,QACbhB,IACKuC,KACJ,EAEF,CAGA,OADPnD,EAAWQ,MAAQoB,GACZ,CACT,CAMFjB,eAAewC,KAAcC,GAC3B,MAAMC,EAAUnD,EAAQM,MACxBL,EAAQK,OAAQ,EAChB,MAAMA,EAAQJ,EAAWI,MAAMoC,QAAQ5B,GAAWA,EAAEqC,UAAYA,GAAwB,eAAbrC,EAAEsC,SAAyB,GACtG,IAAIC,EAAO,CAAC,EAEQ,IAAhBH,EAAKF,SACP7C,EAAS6C,OAAS,GAEpB,MAAMM,EAAQhD,EAAMiD,IAAIvC,MAAM,KAAK,GAE7BwC,EAUFC,EAAWnD,EAAMiD,KACrBC,EAAOE,OAAS,EAEhBF,EAAOG,UAAYC,IAAQC,QAAQ,OAAOC,UAEnCN,EAAAO,QAAUH,IAAQI,IAAI,EAAG,SAASH,QAAQ,OAAOC,UAEpDZ,EAAKF,OAAS,IACTQ,EAAAE,QAAUR,EAAK,GAAGe,SAG3B,MAAMV,EAAM,GAAGD,KAASY,EAAkBV,KACpCW,EAAOC,KAAKC,MAAM/D,EAAM6D,MAC1BA,IACFA,EAAKG,aAAe,OACpBH,EAAKI,YAAc,GAEdlB,EAAA,CAELD,OAAQ,aAERoB,IAAK,CAEHjB,IAAiB,YAAZJ,EAAwBI,EAAMjD,EAAMiD,IAEzCkB,cAAenE,EAAMmE,cAErBN,KAAMC,KAAKM,UAAUP,GAErBhB,UAEApC,QAAST,EAAMS,UAIb,MAAA4D,EAAUP,KAAKM,UAAUrB,GAE3BuB,EAAWC,cACbD,EAAWE,oBAEL,IAAIC,SAASC,GAAYC,WAAWD,EAAS,QAE1CJ,EAAAM,gBAAgB7C,YAEzB,MAAM8C,SAAEA,GAAaf,KAAKC,MAAMhC,GAC5B8C,EAASxE,OACGP,EAAAE,MAAQ6E,EAASxE,KAAKyE,MAChCjF,EAAS6C,OAAS5C,EAAcE,SAE9B,OAAA+E,EAASF,EAAAxE,KAAK2E,cAAd,EAAAD,EAAuBrC,QAAS,GACzBmC,EAAAxE,KAAK2E,QAAQC,SAASrE,GAAcf,EAASwB,KAAKT,MAGzD,OAAAsE,EAASL,EAAAxE,KAAK8E,aAAd,EAAAD,EAAsBxC,QAAS,GACjCmC,EAASxE,KAAK8E,OACX5E,KAAKC,IAEJ,MAAMG,EAA6C,GAK5C,OAJPH,EAAE4E,WAAW1E,MAAM,KAAKuE,SAAQ,CAACrE,EAAWQ,KAC1CT,EAAIU,KAAK,CAAEgE,KAAMzE,EAAM0E,UAAWlE,GAAO,IAE3CZ,EAAE4E,WAAazE,EACRH,CAAA,IAERyE,SAASrE,GAAcf,EAASwB,KAAKT,OAI9CjB,EAAQK,OAAQ,CAAA,IAGZ,MAAAuF,EAAQC,aAAY,KACpBlB,EAAWC,cACbD,EAAWmB,WAAWpB,GACtBqB,cAAcH,GAAK,GAEpB,IAAG,CAGR,SAASI,EAAY3F,GACnBN,EAAQM,MAAQA,EACL2C,GAAA,QA/KbiD,GAAUzF,UACR,MAAME,KAAEA,SAAe6B,EAAOC,yBAAyB/C,GAInD,GAFJc,EAAcF,MAAQD,EAAWqC,QAAQC,GAAUhC,EAAKiC,MAAMC,GAAyB,GAAfA,EAAMC,OAAcH,EAAMrC,OAASuC,EAAME,qBACjH/C,EAAQM,MAAQE,EAAcF,MAAM,GAAGA,MACnCE,EAAcF,MAAM0C,OAAS,EAGxB,aAFDtC,IACKuC,KACJ,CAAA"}