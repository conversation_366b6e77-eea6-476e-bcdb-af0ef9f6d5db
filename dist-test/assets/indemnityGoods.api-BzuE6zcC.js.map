{"version": 3, "file": "indemnityGoods.api-BzuE6zcC.js", "sources": ["../../src/api/modules/pms/goods/indemnityGoods.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nexport default {\r\n\r\n  // 赔偿物品列表\r\n  list: (data: any) => api.get('/admin-api/pms/indemnity-goods/list', { params: data }),\r\n\r\n  // 赔偿物品修改状态\r\n  changeStatus: (data: any) => api.put('/admin-api/pms/indemnity-goods/update-status', data),\r\n\r\n  // 新增赔偿物品\r\n  create: (data: any) => api.post('/admin-api/pms/indemnity-goods/create', data),\r\n\r\n  // 编辑赔偿物品\r\n  edit: (data: any) => api.put('/admin-api/pms/indemnity-goods/update', data),\r\n\r\n  // 赔偿物品详情\r\n  detail: (data: any) => api.get('/admin-api/pms/indemnity-goods/get', { params: data }),\r\n\r\n  // 删除\r\n  delete: (data: any) => api.delete('/admin-api/pms/indemnity-goods/delete', { params: data }),\r\n}\r\n"], "names": ["indemnityGoodsApi", "list", "data", "api", "get", "params", "changeStatus", "put", "create", "post", "edit", "detail", "delete"], "mappings": "wCAEA,MAAeA,EAAA,CAGbC,KAAOC,GAAcC,EAAIC,IAAI,sCAAuC,CAAEC,OAAQH,IAG9EI,aAAeJ,GAAcC,EAAII,IAAI,+CAAgDL,GAGrFM,OAASN,GAAcC,EAAIM,KAAK,wCAAyCP,GAGzEQ,KAAOR,GAAcC,EAAII,IAAI,wCAAyCL,GAGtES,OAAST,GAAcC,EAAIC,IAAI,qCAAsC,CAAEC,OAAQH,IAG/EU,OAASV,GAAcC,EAAIS,OAAO,wCAAyC,CAAEP,OAAQH"}