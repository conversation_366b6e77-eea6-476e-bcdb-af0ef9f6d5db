import{d as e,B as a,b as t,o as s,c as r,ab as l,f as o,w as n,u,a7 as i,Y as p,R as f,S as b,T as c,U as d}from"./index-CkEhI1Zk.js";const v={class:"relative inline-flex"},g=e({__name:"HBadge",props:{value:{type:[String,Number,Boolean]}},setup(e){const g=e,m=a((()=>{switch(typeof g.value){case"string":return g.value.length>0;case"number":return g.value>0;case"boolean":return g.value;default:return void 0!==g.value&&null!==g.value}})),x=t({enterActiveClass:"ease-in-out duration-500",enterFromClass:"opacity-0",enterToClass:"opacity-100",leaveActiveClass:"ease-in-out duration-500",leaveFromClass:"opacity-100",leaveToClass:"opacity-0"});return(e,a)=>(s(),r("div",v,[l(e.$slots,"default"),o(d,b(c(u(x))),{default:n((()=>[u(m)?(s(),r("span",{key:0,class:i(["absolute start-[50%] top-0 z-20 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light -translate-y-[50%] dark-ring-dark",{"-indent-9999 w-1.5 h-1.5 px-0! start-[100%]! -translate-x-[50%] rtl:translate-x-[50%] before:content-empty before:block before:bg-ui-primary before:w-full before:h-full before:rounded-full before:absolute before:start-0 before:top-0 before:animate-ping":!0===e.value}])},p(e.value),3)):f("",!0)])),_:1},16)]))}});export{g as _};
//# sourceMappingURL=HBadge.vue_vue_type_script_setup_true_lang-DopXwaUZ.js.map
