import{a as t}from"./index-CkEhI1Zk.js";const e="admin-api/pms/protocol-agent",o={createProtocolAgent:o=>t.post(`${e}/create`,o),updateProtocolAgent:o=>t.put(`${e}/update`,o),updateProtocolAgentStatus:o=>t.put(`${e}/update-status`,o),getProtocolAgent:o=>t.get(`${e}/get`,{params:{paCode:o}}),getProtocolAgentPage:o=>t.get(`${e}/page`,{params:o}),getProtocolAgentList:o=>t.get(`${e}/list`,{params:o}),getProtocolAgentListSimple:o=>t.get(`${e}/list-simple`,{params:o})};export{o as p};
//# sourceMappingURL=protocolAgent.api-B70PZhBJ.js.map
