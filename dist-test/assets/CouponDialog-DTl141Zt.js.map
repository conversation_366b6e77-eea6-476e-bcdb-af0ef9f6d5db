{"version": 3, "file": "CouponDialog-DTl141Zt.js", "sources": ["../../src/views/room/components/leavePrice/components/CouponDialog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"selectCoupon\": \"Select Coupon\",\r\n    \"firstDayDiscount\": \"First Day Discount\",\r\n    \"multiDayDiscount\": \"Multi-day Stay Discount\",\r\n    \"multiDayDiscountDisabled\": \"Multi-day discount feature is not enabled. Please enable it in Marketing - Coupon Management - Coupon Settings\",\r\n    \"searchCouponPlaceholder\": \"Enter coupon name to search\",\r\n    \"date\": \"Date\",\r\n    \"roomNumber\": \"Room No\",\r\n    \"type\": \"Type\",\r\n    \"roomFee\": \"Room Fee\",\r\n    \"selectCouponOption\": \"Select Coupon\",\r\n    \"usedCoupon\": \"Used Coupon\",\r\n    \"couponNumber\": \"Coupon No\",\r\n    \"discount\": \"Discount\",\r\n    \"firstDayUsedCoupon\": \"First day room fee has used coupon\",\r\n    \"firstDayUsedCouponDesc\": \"First day room fee has used coupon, cannot use coupon again\",\r\n    \"noCouponsAvailable\": \"No coupons available\",\r\n    \"noCouponsDesc\": \"No coupons available, please check the following:\",\r\n    \"noCouponsCheck1\": \"Are there any eligible coupons\",\r\n    \"noCouponsCheck2\": \"Are coupons within validity period\",\r\n    \"noCouponsCheck3\": \"Do coupons meet usage conditions\",\r\n    \"couponCode\": \"Coupon Code\",\r\n    \"couponType\": \"Coupon Type\",\r\n    \"validPeriod\": \"Valid Period\",\r\n    \"minAmount\": \"Min ¥{amount} required\",\r\n    \"notAvailableInStore\": \"Not available in this store\",\r\n    \"notAvailable\": \"Not available\",\r\n    \"selectedCoupons\": \"Selected Coupons\",\r\n    \"discountAmount\": \"Discount Amount\",\r\n    \"freeRoom\": \"Free Room\",\r\n    \"totalDiscountAmount\": \"Total Discount Amount\",\r\n    \"cancel\": \"Cancel\",\r\n    \"confirmUse\": \"Confirm Use\",\r\n    \"notSelected\": \"(Not Selected)\",\r\n    \"couponNotApplicableToRoomType\": \"This coupon is not applicable to this room type\",\r\n    \"couponStatusNotAvailable\": \"Coupon status not available\",\r\n    \"firstDayRoomFeeUsedCoupon\": \"First day room fee has used coupon\",\r\n    \"minAmountRequired\": \"Min ¥{amount} required\",\r\n    \"couponNotYetValid\": \"Coupon not yet valid\",\r\n    \"couponExpired\": \"Coupon expired\",\r\n    \"couponAlreadyUsedOn\": \"This coupon has been used on {date}\",\r\n    \"dateError\": \"Date error\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"selectCoupon\": \"选择优惠券\",\r\n    \"firstDayDiscount\": \"首日优惠\",\r\n    \"multiDayDiscount\": \"连住多天优惠\",\r\n    \"multiDayDiscountDisabled\": \"连住多天优惠功能未启用，请在营销-优惠券管理-优惠券设置中开启\",\r\n    \"searchCouponPlaceholder\": \"输入优惠券名称搜索\",\r\n    \"date\": \"日期\",\r\n    \"roomNumber\": \"房间号\",\r\n    \"type\": \"类型\",\r\n    \"roomFee\": \"房费\",\r\n    \"selectCouponOption\": \"选择优惠券\",\r\n    \"usedCoupon\": \"已使用优惠券\",\r\n    \"couponNumber\": \"券号\",\r\n    \"discount\": \"优惠\",\r\n    \"firstDayUsedCoupon\": \"首日房费已使用优惠券\",\r\n    \"firstDayUsedCouponDesc\": \"首日房费已使用优惠券，无法再次使用优惠券\",\r\n    \"noCouponsAvailable\": \"没有可用的优惠券\",\r\n    \"noCouponsDesc\": \"没有可用的优惠券，请确认以下几点：\",\r\n    \"noCouponsCheck1\": \"是否有符合条件的优惠券\",\r\n    \"noCouponsCheck2\": \"优惠券是否在有效期内\",\r\n    \"noCouponsCheck3\": \"优惠券是否满足使用条件\",\r\n    \"couponCode\": \"券代码\",\r\n    \"couponType\": \"券类型\",\r\n    \"validPeriod\": \"有效期\",\r\n    \"minAmount\": \"满 ¥{amount} 可用\",\r\n    \"notAvailableInStore\": \"不可在本店使用\",\r\n    \"notAvailable\": \"不可用\",\r\n    \"selectedCoupons\": \"已选择优惠券\",\r\n    \"discountAmount\": \"优惠金额\",\r\n    \"freeRoom\": \"免房\",\r\n    \"totalDiscountAmount\": \"总优惠金额\",\r\n    \"cancel\": \"取消\",\r\n    \"confirmUse\": \"确定使用\",\r\n    \"notSelected\": \"（未选择）\",\r\n    \"couponNotApplicableToRoomType\": \"该优惠券不适用该房型\",\r\n    \"couponStatusNotAvailable\": \"优惠券状态不可用\",\r\n    \"firstDayRoomFeeUsedCoupon\": \"首日房费已使用优惠券\",\r\n    \"minAmountRequired\": \"需满{amount}元才可使用\",\r\n    \"couponNotYetValid\": \"优惠券未到使用时间\",\r\n    \"couponExpired\": \"优惠券已过期\",\r\n    \"couponAlreadyUsedOn\": \"该优惠券已被{date}使用\",\r\n    \"dateError\": \"日期错误\"\r\n  },\r\n  \"km\": {\r\n    \"selectCoupon\": \"ជ្រើសរើសគូប៉ុង\",\r\n    \"firstDayDiscount\": \"ការបញ្ចុះតម្លៃថ្ងៃដំបូង\",\r\n    \"multiDayDiscount\": \"ការបញ្ចុះតម្លៃសម្រាប់ស្នាក់នៅច្រើនថ្ងៃ\",\r\n    \"multiDayDiscountDisabled\": \"មុខងារបញ្ចុះតម្លៃច្រើនថ្ងៃមិនត្រូវបានបើកដំណើរការទេ។ សូមបើកវានៅក្នុងទីផ្សារ - ការគ្រប់គ្រងគូប៉ុង - ការកំណត់គូប៉ុង\",\r\n    \"searchCouponPlaceholder\": \"បញ្ចូលឈ្មោះគូប៉ុងដើម្បីស្វែងរក\",\r\n    \"date\": \"កាលបរិច្ឆេទ\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"type\": \"ប្រភេទ\",\r\n    \"roomFee\": \"ថ្លៃបន្ទប់\",\r\n    \"selectCouponOption\": \"ជ្រើសរើសគូប៉ុង\",\r\n    \"usedCoupon\": \"បានប្រើប្រាស់គូប៉ុង\",\r\n    \"couponNumber\": \"លេខគូប៉ុង\",\r\n    \"discount\": \"បញ្ចុះតម្លៃ\",\r\n    \"firstDayUsedCoupon\": \"ថ្លៃបន្ទប់ថ្ងៃដំបូងបានប្រើប្រាស់គូប៉ុង\",\r\n    \"firstDayUsedCouponDesc\": \"ថ្លៃបន្ទប់ថ្ងៃដំបូងបានប្រើប្រាស់គូប៉ុង មិនអាចប្រើប្រាស់គូប៉ុងម្តងទៀតបានទេ\",\r\n    \"noCouponsAvailable\": \"មិនមានគូប៉ុងដែលអាចប្រើបានទេ\",\r\n    \"noCouponsDesc\": \"មិនមានគូប៉ុងដែលអាចប្រើបានទេ សូមពិនិត្យចំណុចខាងក្រោម៖\",\r\n    \"noCouponsCheck1\": \"តើមានគូប៉ុងដែលមានលក្ខណៈសម្បត្តិគ្រប់គ្រាន់ទេ\",\r\n    \"noCouponsCheck2\": \"តើគូប៉ុងនៅក្នុងរយៈពេលសុពលភាពទេ\",\r\n    \"noCouponsCheck3\": \"តើគូប៉ុងបំពេញលក្ខខណ្ឌប្រើប្រាស់ទេ\",\r\n    \"couponCode\": \"កូដគូប៉ុង\",\r\n    \"couponType\": \"ប្រភេទគូប៉ុង\",\r\n    \"validPeriod\": \"រយៈពេលសុពលភាព\",\r\n    \"minAmount\": \"ចាំបាច់ ¥{amount} ឬច្រើនជាងនេះ\",\r\n    \"notAvailableInStore\": \"មិនអាចប្រើបាននៅហាងនេះទេ\",\r\n    \"notAvailable\": \"មិនអាចប្រើបានទេ\",\r\n    \"selectedCoupons\": \"គូប៉ុងដែលបានជ្រើសរើស\",\r\n    \"discountAmount\": \"ចំនួនទឹកប្រាក់បញ្ចុះតម្លៃ\",\r\n    \"freeRoom\": \"បន្ទប់ឥតគិតថ្លៃ\",\r\n    \"totalDiscountAmount\": \"ចំនួនទឹកប្រាក់បញ្ចុះតម្លៃសរុប\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"confirmUse\": \"បញ្ជាក់ការប្រើប្រាស់\",\r\n    \"notSelected\": \"(មិនបានជ្រើសរើស)\",\r\n    \"couponNotApplicableToRoomType\": \"គូប៉ុងនេះមិនអាចអនុវត្តចំពោះប្រភេទបន្ទប់នេះបានទេ\",\r\n    \"couponStatusNotAvailable\": \"ស្ថានភាពគូប៉ុងមិនអាចប្រើបានទេ\",\r\n    \"firstDayRoomFeeUsedCoupon\": \"ថ្លៃបន្ទប់ថ្ងៃដំបូងបានប្រើប្រាស់គូប៉ុង\",\r\n    \"minAmountRequired\": \"ត្រូវការ{amount}យ៉ាន់ដើម្បីអាចប្រើបាន\",\r\n    \"couponNotYetValid\": \"គូប៉ុងមិនទាន់ដល់ពេលប្រើប្រាស់ទេ\",\r\n    \"couponExpired\": \"គូប៉ុងផុតកំណត់\",\r\n    \"couponAlreadyUsedOn\": \"គូប៉ុងនេះត្រូវបានប្រើប្រាស់នៅ{date}\",\r\n    \"dateError\": \"កំហុសកាលបរិច្ឆេទ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script lang=\"ts\" setup>\r\nimport { accountApi, couponApi } from '@/api/modules'\r\n\r\nimport useUserStore from '@/store/modules/user'\r\nimport { Check, Search } from '@element-plus/icons-vue'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\ninterface Coupon {\r\n  id: string\r\n  templateCode: string // 券代码\r\n  name: string\r\n  couponTypeName: string // 券类型名称\r\n  description: string\r\n  discount: number\r\n  minAmount: number\r\n  startTime: string\r\n  endTime: string\r\n  status: 'available' | 'used' | 'expired' | 'invalid'\r\n  type: 'amount' | 'percent'\r\n  couponType: string // 券类型: voucher(代金券), discount(折扣券), free(免房券)\r\n  rebate?: number // 折扣率\r\n  matchCurrentHcode?: boolean // 是否可在当前门店使用\r\n  rtCodes?: string[] // 适用的房型代码列表\r\n  couponCode?: string // 券号\r\n}\r\n\r\ninterface RoomFee {\r\n  id: number\r\n  gcode: string\r\n  hcode: string\r\n  bizDate: string\r\n  no: string\r\n  subCode: string\r\n  subName: string\r\n  subType: string\r\n  fee: number\r\n  accNo: string // 添加唯一标识字段\r\n  isUsed?: string // 新增：是否已使用优惠券 \"1\"表示已使用\r\n  couponCode?: string | null // 新增：已使用的优惠券券号\r\n  rNo?: string // 新增：房间号\r\n  state?: string\r\n}\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    orderAmount: number\r\n    selectedCoupon?: Coupon | null\r\n    checkinType?: string\r\n    rtCode?: string\r\n    channelCode?: string\r\n    phone?: string\r\n    guestCode?: string // 新增：会员编码\r\n    togetherCodes?: string[] // 新增：联房码列表\r\n    isWalkinMoreEnabled?: boolean // 是否允许连住多天优惠\r\n    selectedDailyCouponIds?: Record<string, string> // 新增：已选择的每日优惠券ID映射 (accNo -> couponId)\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    orderAmount: 0,\r\n    selectedCoupon: null,\r\n    checkinType: '',\r\n    rtCode: '',\r\n    channelCode: '',\r\n    phone: '',\r\n    guestCode: '',\r\n    togetherCodes: () => [],\r\n    isWalkinMoreEnabled: false,\r\n    selectedDailyCouponIds: () => ({}),\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  confirm: [coupon: Coupon | null, discount: number, dailyCoupons?: Record<string, Coupon>, isFinalConfirm?: boolean]\r\n  cancel: []\r\n}>()\r\n\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange } = usePagination()\r\nconst { t } = useI18n()\r\n\r\nconst dialogVisible = computed({\r\n  get: () => props.modelValue,\r\n  set: (val) => emits('update:modelValue', val),\r\n})\r\n\r\nconst loading = ref(false)\r\nconst searchKeyword = ref('')\r\nconst coupons = ref<Coupon[]>([])\r\nconst tempSelectedCoupons = ref<Coupon[]>([])\r\nconst discountMode = ref('first_day') // 'first_day' 或 'multi_day'\r\nconst roomFeeList = ref<RoomFee[]>([])\r\nconst selectedDailyCoupons = ref<Record<string, string>>({}) // accNo -> 优惠券ID\r\nconst availableCoupons = ref<Coupon[]>([])\r\n\r\n// 计算属性\r\nconst filteredCoupons = computed(() => {\r\n  if (!searchKeyword.value) {\r\n    return coupons.value\r\n  }\r\n\r\n  return coupons.value.filter((coupon) => coupon.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) || coupon.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))\r\n})\r\n\r\n// 首日房费计算属性\r\nconst firstDayFee = computed(() => {\r\n  if (roomFeeList.value.length === 0) {\r\n    console.log('房费列表为空，无法计算首日房费')\r\n    return 0 // 返回0表示没有有效的房费数据\r\n  }\r\n\r\n  // 按日期排序，取最早的房费\r\n  const sortedFees = [...roomFeeList.value].sort((a, b) => new Date(a.bizDate).getTime() - new Date(b.bizDate).getTime())\r\n\r\n  // 检查最早的房费是否已使用优惠券，如果已使用，则尝试查找同一天未使用优惠券的房费\r\n  const earliestDate = sortedFees[0]?.bizDate\r\n  const sameDay = sortedFees.filter((fee) => fee.bizDate === earliestDate)\r\n\r\n  // 优先选择未使用优惠券的房费\r\n  const unusedFee = sameDay.find((fee) => fee.isUsed !== '1')\r\n  if (unusedFee) {\r\n    console.log('找到首日未使用优惠券的房费:', {\r\n      date: unusedFee.bizDate,\r\n      fee: unusedFee.fee,\r\n      subName: unusedFee.subName,\r\n    })\r\n    return unusedFee.fee\r\n  }\r\n\r\n  // 如果所有首日房费都已使用优惠券，则返回0，表示无法使用优惠券\r\n  if (sameDay.every((fee) => fee.isUsed === '1')) {\r\n    console.log('首日所有房费都已使用优惠券，无法使用优惠券')\r\n    return 0\r\n  }\r\n\r\n  const fee = sortedFees[0]?.fee || 0\r\n  console.log('计算首日房费:', {\r\n    date: sortedFees[0]?.bizDate,\r\n    fee,\r\n  })\r\n\r\n  return fee\r\n})\r\n\r\nconst totalDiscount = computed(() => {\r\n  if (tempSelectedCoupons.value.length === 0) {\r\n    return 0\r\n  }\r\n\r\n  const coupon = tempSelectedCoupons.value[0]\r\n\r\n  // 确保使用正确的首日房费\r\n  const actualFirstDayFee = firstDayFee.value\r\n\r\n  // 记录首日房费计算过程，帮助调试\r\n  console.log('totalDiscount 计算优惠金额:', {\r\n    couponName: coupon.name,\r\n    couponType: coupon.couponType,\r\n    firstDayFee: actualFirstDayFee,\r\n    roomFeeListLength: roomFeeList.value.length,\r\n    orderAmount: props.orderAmount,\r\n    使用金额: roomFeeList.value.length > 0 ? '房费列表首日房费' : '订单总金额',\r\n  })\r\n\r\n  if (coupon.couponType === 'discount') {\r\n    // 折扣券：计算折扣后的金额与原价的差值，保留两位小数\r\n    return Number.parseFloat((actualFirstDayFee * (1 - (coupon.rebate || 0))).toFixed(2))\r\n  } else if (coupon.couponType === 'free') {\r\n    // 免房券：返回首日房费金额，保留两位小数\r\n    return Number.parseFloat(actualFirstDayFee.toFixed(2))\r\n  } else {\r\n    // 代金券：直接返回优惠金额，保留两位小数\r\n    return Number.parseFloat(Math.min(coupon.discount, actualFirstDayFee).toFixed(2))\r\n  }\r\n})\r\n\r\nconst hasSelectedDailyCoupons = computed(() => {\r\n  return Object.values(selectedDailyCoupons.value).some((id) => id)\r\n})\r\n\r\nconst totalMultiDayDiscount = computed(() => {\r\n  let total = 0\r\n\r\n  // 计算每个房费的优惠金额总和\r\n  for (const accNo in selectedDailyCoupons.value) {\r\n    const couponId = selectedDailyCoupons.value[accNo]\r\n    if (!couponId) {\r\n      continue\r\n    }\r\n\r\n    const coupon = getDailyCoupon(couponId)\r\n    if (!coupon) {\r\n      continue\r\n    }\r\n\r\n    // 找到对应的房费\r\n    const roomFee = roomFeeList.value.find((fee) => fee.accNo === accNo)\r\n    if (!roomFee) {\r\n      continue\r\n    }\r\n\r\n    total += calculateDailyDiscount(coupon, roomFee.fee)\r\n  }\r\n\r\n  // 确保总金额保留两位小数\r\n  return Number.parseFloat(total.toFixed(2))\r\n})\r\n\r\n// 方法\r\nfunction formatDate(dateStr: string) {\r\n  return new Date(dateStr).toLocaleDateString()\r\n}\r\n\r\nfunction formatDateShort(dateStr: string) {\r\n  if (!dateStr) {\r\n    return ''\r\n  }\r\n\r\n  try {\r\n    const date = new Date(dateStr)\r\n    if (isNaN(date.getTime())) {\r\n      return ''\r\n    }\r\n    return `${date.getMonth() + 1}月${date.getDate()}日`\r\n  } catch (error) {\r\n    return ''\r\n  }\r\n}\r\n\r\nfunction isSelected(coupon: Coupon) {\r\n  return tempSelectedCoupons.value.some((c) => c.id === coupon.id)\r\n}\r\n\r\nfunction isAvailable(coupon: Coupon) {\r\n  // 如果房费列表为空，则不可用\r\n  if (roomFeeList.value.length === 0) {\r\n    return false\r\n  }\r\n\r\n  if (coupon.status !== 'available') {\r\n    return false\r\n  }\r\n\r\n  // 检查是否可在当前门店使用\r\n  if (coupon.matchCurrentHcode === false) {\r\n    return false\r\n  }\r\n\r\n  // 检查房型是否适用\r\n  if (props.rtCode && coupon.rtCodes && coupon.rtCodes.length > 0) {\r\n    if (!coupon.rtCodes.includes(props.rtCode)) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  // 检查首日房费是否已全部使用优惠券\r\n  const earliestDate = [...roomFeeList.value].sort((a, b) => new Date(a.bizDate).getTime() - new Date(b.bizDate).getTime())[0]?.bizDate\r\n\r\n  const firstDayFees = roomFeeList.value.filter((fee) => fee.bizDate === earliestDate)\r\n  const allFirstDayUsed = firstDayFees.length > 0 && firstDayFees.every((fee) => fee.isUsed === '1')\r\n\r\n  // 如果首日房费已全部使用优惠券，则不可用\r\n  if (allFirstDayUsed) {\r\n    return false\r\n  }\r\n\r\n  // 使用首日房费判断是否满足使用门槛\r\n  if (coupon.minAmount > 0 && firstDayFee.value < coupon.minAmount) {\r\n    return false\r\n  }\r\n\r\n  const now = new Date()\r\n  const startTime = new Date(coupon.startTime)\r\n  const endTime = new Date(coupon.endTime)\r\n\r\n  return now >= startTime && now <= endTime\r\n}\r\n\r\nfunction isAvailableForFee(coupon: Coupon, fee: number) {\r\n  if (coupon.status !== 'available') {\r\n    return false\r\n  }\r\n  if (coupon.matchCurrentHcode === false) {\r\n    return false\r\n  }\r\n\r\n  // 检查房型是否适用\r\n  if (props.rtCode && coupon.rtCodes && coupon.rtCodes.length > 0) {\r\n    if (!coupon.rtCodes.includes(props.rtCode)) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  if (coupon.minAmount > 0 && fee < coupon.minAmount) {\r\n    return false\r\n  }\r\n\r\n  const now = new Date()\r\n  const startTime = new Date(coupon.startTime)\r\n  const endTime = new Date(coupon.endTime)\r\n\r\n  return now >= startTime && now <= endTime\r\n}\r\n\r\n// 获取优惠券不可用的原因\r\nfunction getUnavailableReason(coupon: Coupon) {\r\n  if (coupon.status !== 'available') {\r\n    return t('couponStatusNotAvailable')\r\n  }\r\n\r\n  if (coupon.matchCurrentHcode === false) {\r\n    return t('notAvailableInStore')\r\n  }\r\n\r\n  // 检查房型是否适用\r\n  if (props.rtCode && coupon.rtCodes && coupon.rtCodes.length > 0) {\r\n    if (!coupon.rtCodes.includes(props.rtCode)) {\r\n      return t('couponNotApplicableToRoomType')\r\n    }\r\n  }\r\n\r\n  // 检查首日房费是否已全部使用优惠券\r\n  const earliestDate = [...roomFeeList.value].sort((a, b) => new Date(a.bizDate).getTime() - new Date(b.bizDate).getTime())[0]?.bizDate\r\n  const firstDayFees = roomFeeList.value.filter((fee) => fee.bizDate === earliestDate)\r\n  const allFirstDayUsed = firstDayFees.length > 0 && firstDayFees.every((fee) => fee.isUsed === '1')\r\n\r\n  if (allFirstDayUsed) {\r\n    return t('firstDayRoomFeeUsedCoupon')\r\n  }\r\n\r\n  // 使用首日房费判断是否满足使用门槛\r\n  if (coupon.minAmount > 0 && firstDayFee.value < coupon.minAmount) {\r\n    return t('minAmountRequired', { amount: coupon.minAmount })\r\n  }\r\n\r\n  const now = new Date()\r\n  const startTime = new Date(coupon.startTime)\r\n  const endTime = new Date(coupon.endTime)\r\n\r\n  if (now < startTime) {\r\n    return t('couponNotYetValid')\r\n  }\r\n\r\n  if (now > endTime) {\r\n    return t('couponExpired')\r\n  }\r\n\r\n  return t('notAvailable')\r\n}\r\n\r\n// 检查房费是否已使用优惠券\r\nfunction isRoomFeeUsedCoupon(roomFee: RoomFee): boolean {\r\n  // 检查房费的状态标志或是否有优惠券编码\r\n  return roomFee.isUsed === '1' || roomFee.state === 'settled' || roomFee.state === '1' || !!roomFee.couponCode\r\n}\r\n\r\nfunction isCouponAlreadySelected(couponId: string, currentAccNo: string): boolean {\r\n  // 遍历所有已选择的优惠券，检查是否有其他房费已选择了该优惠券\r\n  for (const accNo in selectedDailyCoupons.value) {\r\n    // 跳过当前房费\r\n    if (accNo === currentAccNo) {\r\n      continue\r\n    }\r\n\r\n    // 如果其他房费已选择了该优惠券，则返回true\r\n    if (selectedDailyCoupons.value[accNo] === couponId) {\r\n      return true\r\n    }\r\n  }\r\n  return false\r\n}\r\n\r\nfunction getDateUsingCoupon(couponId: string): string {\r\n  for (const accNo in selectedDailyCoupons.value) {\r\n    if (selectedDailyCoupons.value[accNo] === couponId) {\r\n      const roomFee = getRoomFeeByAccNo(accNo)\r\n      if (roomFee && roomFee.bizDate) {\r\n        return formatDateShort(roomFee.bizDate)\r\n      }\r\n    }\r\n  }\r\n  return t('dateError')\r\n}\r\n\r\nfunction toggleCoupon(coupon: Coupon) {\r\n  if (!isAvailable(coupon)) {\r\n    return\r\n  }\r\n\r\n  const index = tempSelectedCoupons.value.findIndex((c) => c.id === coupon.id)\r\n  if (index > -1) {\r\n    tempSelectedCoupons.value = []\r\n  } else {\r\n    tempSelectedCoupons.value = [coupon]\r\n  }\r\n}\r\n\r\nfunction handleSearch() {\r\n  // 重新获取数据\r\n  fetchCoupons()\r\n}\r\n\r\nfunction handleConfirm() {\r\n  // 清除会话存储中的临时数据\r\n  window.sessionStorage.removeItem('temp_selected_coupons')\r\n  window.sessionStorage.removeItem('selected_daily_coupons')\r\n\r\n  if (discountMode.value === 'first_day') {\r\n    // 首日优惠模式\r\n    // 检查首日房费是否已全部使用优惠券\r\n    const earliestDate = [...roomFeeList.value].sort((a, b) => new Date(a.bizDate).getTime() - new Date(b.bizDate).getTime())[0]?.bizDate\r\n\r\n    const firstDayFees = roomFeeList.value.filter((fee) => fee.bizDate === earliestDate)\r\n    const allFirstDayUsed = firstDayFees.length > 0 && firstDayFees.every((fee) => fee.isUsed === '1')\r\n\r\n    if (allFirstDayUsed) {\r\n      ElMessage.warning(t('firstDayUsedCouponDesc'))\r\n      return\r\n    }\r\n\r\n    // 添加第四个参数 true 表示这是最终确认\r\n    emits('confirm', tempSelectedCoupons.value.length > 0 ? tempSelectedCoupons.value[0] : null, totalDiscount.value, undefined, true)\r\n  } else {\r\n    // 连住多天优惠模式\r\n    const dailyCoupons: Record<string, any> = {}\r\n\r\n    // 构建每日优惠券对象\r\n    for (const accNo in selectedDailyCoupons.value) {\r\n      const couponId = selectedDailyCoupons.value[accNo]\r\n      if (!couponId) {\r\n        continue\r\n      }\r\n\r\n      const coupon = getDailyCoupon(couponId)\r\n      if (!coupon) {\r\n        continue\r\n      }\r\n\r\n      // 找到对应的房费，获取其日期作为键\r\n      const roomFee = getRoomFeeByAccNo(accNo)\r\n      if (!roomFee || !roomFee.bizDate) {\r\n        continue\r\n      }\r\n\r\n      // 检查房费是否已使用优惠券\r\n      if (isRoomFeeUsedCoupon(roomFee)) {\r\n        continue\r\n      }\r\n\r\n      const date = roomFee.bizDate\r\n\r\n      // 使用日期和accNo作为组合键，保留同一天的多个优惠券\r\n      if (!dailyCoupons[date]) {\r\n        dailyCoupons[date] = {}\r\n      }\r\n\r\n      dailyCoupons[date][accNo] = {\r\n        ...coupon,\r\n        accNo,\r\n        fee: roomFee.fee,\r\n      }\r\n    }\r\n\r\n    // 检查是否有选择的优惠券\r\n    if (Object.keys(dailyCoupons).length === 0) {\r\n      ElMessage.warning('请至少选择一个可用的优惠券')\r\n      return\r\n    }\r\n\r\n    // 添加第四个参数 true 表示这是最终确认\r\n    emits('confirm', null, totalMultiDayDiscount.value, dailyCoupons, true)\r\n  }\r\n}\r\n\r\nfunction handleCancel() {\r\n  // 清除会话存储中的临时数据\r\n  window.sessionStorage.removeItem('temp_selected_coupons')\r\n  window.sessionStorage.removeItem('selected_daily_coupons')\r\n  emits('cancel')\r\n}\r\n\r\nfunction handleDiscountModeChange() {\r\n  // 保存当前选择，而不是清空\r\n  const previousMode = discountMode.value === 'first_day' ? 'multi_day' : 'first_day'\r\n\r\n  // 如果切换到首日模式，但房费列表为空，则提示错误\r\n  if (discountMode.value === 'first_day' && roomFeeList.value.length === 0) {\r\n    ElMessage.warning('未获取到房费数据，无法使用首日优惠')\r\n    // 重置回之前的模式\r\n    discountMode.value = previousMode\r\n    return\r\n  }\r\n\r\n  // 切换到连住多天模式时，如果没有已选择的每日优惠券，但有首日优惠券\r\n  if (discountMode.value === 'multi_day' && tempSelectedCoupons.value.length > 0) {\r\n    // 保存首日优惠券的选择，以便切换回来时恢复\r\n    window.sessionStorage.setItem('temp_selected_coupons', JSON.stringify(tempSelectedCoupons.value))\r\n    console.log('保存首日优惠券选择:', tempSelectedCoupons.value[0]?.name)\r\n    // 清空首日选择，避免两种模式同时有选择\r\n    tempSelectedCoupons.value = []\r\n  } else if (discountMode.value === 'first_day' && Object.keys(selectedDailyCoupons.value).length > 0) {\r\n    // 保存连住多天优惠券的选择，以便切换回来时恢复\r\n    window.sessionStorage.setItem('selected_daily_coupons', JSON.stringify(selectedDailyCoupons.value))\r\n    console.log('保存连住多天优惠券选择:', Object.keys(selectedDailyCoupons.value).length)\r\n    // 清空多日选择，避免两种模式同时有选择\r\n    selectedDailyCoupons.value = {}\r\n  }\r\n\r\n  // 记录模式切换时的房费数据\r\n  console.log('模式切换:', {\r\n    从: previousMode,\r\n    到: discountMode.value,\r\n    房费列表长度: roomFeeList.value.length,\r\n    首日房费: firstDayFee.value,\r\n  })\r\n}\r\n\r\nfunction getDailyCoupon(id: string): Coupon | undefined {\r\n  return availableCoupons.value.find((c) => c.id === id)\r\n}\r\n\r\nfunction calculateDailyDiscount(coupon: Coupon | undefined, fee: number): number {\r\n  if (!coupon) {\r\n    return 0\r\n  }\r\n\r\n  if (coupon.couponType === 'discount') {\r\n    // 折扣券：计算折扣后的金额与原价的差值\r\n    return Number.parseFloat((fee * (1 - (coupon.rebate || 0))).toFixed(2))\r\n  } else if (coupon.couponType === 'free') {\r\n    // 免房券：返回房费金额\r\n    return Number.parseFloat(fee.toFixed(2))\r\n  } else {\r\n    // 代金券：直接返回优惠金额\r\n    return Number.parseFloat(Math.min(coupon.discount, fee).toFixed(2))\r\n  }\r\n}\r\n\r\nfunction handleDailyCouponSelect(couponId: string, roomFee: RoomFee) {\r\n  if (!couponId) {\r\n    // 清除选择 - 只清除当前房费的优惠券\r\n    delete selectedDailyCoupons.value[roomFee.accNo]\r\n\r\n    // 如果当前模式是连住多天，且是移除操作，则更新总折扣金额\r\n    // 这将触发父组件的 watch 来调用 statAccount 接口\r\n    if (discountMode.value === 'multi_day') {\r\n      // 重新计算总折扣\r\n      let newTotal = 0\r\n      for (const accNo in selectedDailyCoupons.value) {\r\n        const couponId = selectedDailyCoupons.value[accNo]\r\n        if (!couponId) {\r\n          continue\r\n        }\r\n\r\n        const coupon = getDailyCoupon(couponId)\r\n        if (!coupon) {\r\n          continue\r\n        }\r\n\r\n        const roomFee = roomFeeList.value.find((fee) => fee.accNo === accNo)\r\n        if (!roomFee) {\r\n          continue\r\n        }\r\n\r\n        newTotal += calculateDailyDiscount(coupon, roomFee.fee)\r\n      }\r\n\r\n      // 实时通知父组件更新\r\n      if (Object.keys(selectedDailyCoupons.value).length > 0) {\r\n        // 如果还有选择的优惠券，触发更新但不关闭弹窗\r\n        emits('confirm', null, Number.parseFloat(newTotal.toFixed(2)), getDailyCouponsMap(), false)\r\n        // 重置 dailyCoupons 映射以便继续编辑\r\n        return\r\n      }\r\n    }\r\n\r\n    return\r\n  }\r\n\r\n  // 先检查该优惠券是否已被其他房费选择\r\n  for (const accNo in selectedDailyCoupons.value) {\r\n    // 跳过当前房费\r\n    if (accNo === roomFee.accNo) {\r\n      continue\r\n    }\r\n\r\n    // 如果其他房费已选择了该优惠券，则清除该选择\r\n    if (selectedDailyCoupons.value[accNo] === couponId) {\r\n      // 可以添加提示信息\r\n      ElMessage({\r\n        message: `已将${formatDateShort(getRoomFeeByAccNo(accNo)?.bizDate || '')}的相同优惠券移除`,\r\n        type: 'warning',\r\n        duration: 2000,\r\n      })\r\n      delete selectedDailyCoupons.value[accNo]\r\n    }\r\n  }\r\n\r\n  // 设置选择的优惠券 - 只设置当前房费的优惠券\r\n  selectedDailyCoupons.value[roomFee.accNo] = couponId\r\n\r\n  // 如果是添加操作，也实时更新父组件状态\r\n  if (discountMode.value === 'multi_day') {\r\n    // 重新计算总折扣\r\n    let newTotal = 0\r\n    for (const accNo in selectedDailyCoupons.value) {\r\n      const couponId = selectedDailyCoupons.value[accNo]\r\n      if (!couponId) {\r\n        continue\r\n      }\r\n\r\n      const coupon = getDailyCoupon(couponId)\r\n      if (!coupon) {\r\n        continue\r\n      }\r\n\r\n      const roomFee = roomFeeList.value.find((fee) => fee.accNo === accNo)\r\n      if (!roomFee) {\r\n        continue\r\n      }\r\n\r\n      newTotal += calculateDailyDiscount(coupon, roomFee.fee)\r\n    }\r\n\r\n    // 实时通知父组件更新\r\n    emits('confirm', null, Number.parseFloat(newTotal.toFixed(2)), getDailyCouponsMap(), false)\r\n  }\r\n}\r\n\r\n// 辅助方法，获取当前选择的每日优惠券映射\r\nfunction getDailyCouponsMap(): Record<string, any> {\r\n  const dailyCoupons: Record<string, any> = {}\r\n\r\n  // 构建每日优惠券对象\r\n  for (const accNo in selectedDailyCoupons.value) {\r\n    const couponId = selectedDailyCoupons.value[accNo]\r\n    if (!couponId) {\r\n      continue\r\n    }\r\n\r\n    const coupon = getDailyCoupon(couponId)\r\n    if (!coupon) {\r\n      continue\r\n    }\r\n\r\n    // 找到对应的房费，获取其日期作为键\r\n    const roomFee = getRoomFeeByAccNo(accNo)\r\n    if (!roomFee || !roomFee.bizDate) {\r\n      continue\r\n    }\r\n\r\n    // 检查房费是否已使用优惠券\r\n    if (isRoomFeeUsedCoupon(roomFee)) {\r\n      continue\r\n    }\r\n\r\n    const date = roomFee.bizDate\r\n\r\n    // 使用日期和accNo作为组合键，保留同一天的多个优惠券\r\n    if (!dailyCoupons[date]) {\r\n      dailyCoupons[date] = {}\r\n    }\r\n\r\n    dailyCoupons[date][accNo] = {\r\n      ...coupon,\r\n      accNo,\r\n      fee: roomFee.fee,\r\n    }\r\n  }\r\n\r\n  return dailyCoupons\r\n}\r\n\r\n// 获取优惠券列表 - 真实接口调用\r\nasync function fetchCoupons() {\r\n  // 如果房费列表为空，则不允许获取优惠券\r\n  if (roomFeeList.value.length === 0 && discountMode.value === 'first_day') {\r\n    coupons.value = []\r\n    availableCoupons.value = []\r\n    ElMessage.warning('未获取到房费数据，无法使用优惠券')\r\n    return\r\n  }\r\n\r\n  loading.value = true\r\n  try {\r\n    const params = {\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      checkinType: props.checkinType,\r\n      rtCode: props.rtCode,\r\n      channelCode: props.channelCode,\r\n      // 不再传递belong参数\r\n      // belong: props.phone,\r\n      // 改为传递mCode参数\r\n      mCode: props.guestCode,\r\n      templateName: searchKeyword.value, // 搜索关键词\r\n    }\r\n\r\n    // 使用计算属性获取首日房费\r\n    const actualFirstDayFee = firstDayFee.value\r\n\r\n    // 检查是否所有首日房费都已使用优惠券，但不阻止获取优惠券列表\r\n    const allFirstDayUsed =\r\n      roomFeeList.value.length > 0 && roomFeeList.value.filter((fee) => fee.bizDate === roomFeeList.value.sort((a, b) => new Date(a.bizDate).getTime() - new Date(b.bizDate).getTime())[0].bizDate).every((fee) => fee.isUsed === '1')\r\n\r\n    if (allFirstDayUsed && discountMode.value === 'first_day') {\r\n      console.log('首日所有房费都已使用优惠券，但仍然获取优惠券列表')\r\n      // 这里不返回，继续获取优惠券列表\r\n    }\r\n\r\n    console.log('获取优惠券前的房费信息:', {\r\n      首日房费: actualFirstDayFee,\r\n      房费列表长度: roomFeeList.value.length,\r\n      首日房费已全部使用优惠券: allFirstDayUsed,\r\n      guestCode: props.guestCode, // 使用props中的guestCode\r\n    })\r\n\r\n    const res = await couponApi.memberList(params)\r\n    console.log('优惠券接口返回数据:', res)\r\n\r\n    if (res.code === 0) {\r\n      // 检查接口返回的数据是否为空\r\n      if (!res.data || !Array.isArray(res.data) || res.data.length === 0) {\r\n        coupons.value = []\r\n        availableCoupons.value = []\r\n\r\n        // 如果之前有选择的优惠券，现在接口返回空数据，清空选择\r\n        if (tempSelectedCoupons.value.length > 0 || Object.keys(selectedDailyCoupons.value).length > 0) {\r\n          tempSelectedCoupons.value = []\r\n          selectedDailyCoupons.value = {}\r\n          ElMessage.warning('没有可用的优惠券')\r\n        }\r\n        return\r\n      }\r\n\r\n      // 将接口返回的数据转换为组件需要的格式\r\n      const couponsList = res.data.map((item: any) => {\r\n        let discountValue = 0\r\n        // 根据券类型处理优惠金额\r\n        if (item.couponType === 'discount') {\r\n          discountValue = item.rebate || 0 // 折扣券使用rebate值\r\n        } else if (item.couponType === 'free') {\r\n          // 即使首日房费已使用优惠券，也计算优惠金额\r\n          // 查找未使用优惠券的房费，如果没有，则使用第一天的房费\r\n          const unusedFee = roomFeeList.value.find((fee) => fee.isUsed !== '1')?.fee\r\n          discountValue = unusedFee || roomFeeList.value[0]?.fee || 0\r\n        } else {\r\n          discountValue = item.money || 0 // 代金券使用money值\r\n        }\r\n\r\n        return {\r\n          id: item.couponCode, // 使用couponCode作为唯一标识符，而不是templateCode\r\n          templateCode: item.templateCode,\r\n          name: item.templateName,\r\n          templateName: item.templateName,\r\n          couponCode: item.couponCode,\r\n          couponTypeName: item.couponTypeName,\r\n          description: item.intro || '',\r\n          discount: discountValue,\r\n          minAmount: item.consume || 0, // 使用门槛（后端已转换为元）\r\n          startTime: item.effDate || '',\r\n          endTime: item.expDate || '',\r\n          status: item.state === '1' ? 'available' : 'invalid',\r\n          type: item.couponType === 'voucher' ? 'amount' : 'percent',\r\n          couponType: item.couponType,\r\n          rebate: item.rebate,\r\n          money: item.money || 0,\r\n          matchCurrentHcode: item.matchCurrentHcode, // 添加是否可在当前门店使用的标志\r\n          rtCodes: item.rtCodes || [], // 添加适用的房型代码列表\r\n        }\r\n      })\r\n\r\n      coupons.value = couponsList\r\n      availableCoupons.value = couponsList\r\n    } else {\r\n      // 接口返回错误\r\n      coupons.value = []\r\n      availableCoupons.value = []\r\n      ElMessage.error(res.msg || '获取优惠券列表失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取优惠券失败:', error)\r\n    ElMessage.error('获取优惠券列表失败')\r\n    coupons.value = []\r\n    availableCoupons.value = []\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 获取房费列表\r\nasync function fetchRoomFeeList() {\r\n  if (!props.togetherCodes || props.togetherCodes.length === 0) {\r\n    console.log('未提供联房码信息，无法获取房费数据')\r\n    ElMessage.warning('未提供联房码信息，无法使用优惠券')\r\n    roomFeeList.value = []\r\n    return Promise.reject('未提供联房码信息')\r\n  }\r\n\r\n  loading.value = true\r\n  try {\r\n    const params = {\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      togetherCodes: props.togetherCodes.join(','),\r\n    }\r\n\r\n    const res = await accountApi.accountRoomFee(params)\r\n    console.log('房费接口返回数据:', res)\r\n\r\n    if (res.code === 0) {\r\n      if (!res.data || !Array.isArray(res.data) || res.data.length === 0) {\r\n        console.warn('房费接口返回空数据，无法使用优惠券')\r\n        ElMessage.warning('未获取到房费数据，无法使用优惠券')\r\n        roomFeeList.value = []\r\n        return Promise.reject('房费数据为空')\r\n      }\r\n\r\n      // 处理已结账的房费 - 直接从API返回数据中检查状态\r\n      const processedData = res.data.map((fee) => {\r\n        // 根据API返回的状态或特定字段判断房费是否已结账\r\n        if (fee.state === 'settled' || fee.state === '1' || fee.isSettled === '1' || fee.isSettled === true) {\r\n          return { ...fee, state: 'settled' }\r\n        }\r\n        return fee\r\n      })\r\n\r\n      // 按日期排序\r\n      roomFeeList.value = processedData.sort((a: RoomFee, b: RoomFee) => {\r\n        return new Date(a.bizDate).getTime() - new Date(b.bizDate).getTime()\r\n      })\r\n\r\n      console.log('房费列表数据:', {\r\n        count: roomFeeList.value.length,\r\n        firstDay: roomFeeList.value[0]?.bizDate,\r\n        firstDayFee: roomFeeList.value[0]?.fee,\r\n      })\r\n      return Promise.resolve()\r\n    } else {\r\n      console.error('获取房费列表失败:', res.msg)\r\n      ElMessage.warning('获取房费列表失败，无法使用优惠券')\r\n      roomFeeList.value = []\r\n      return Promise.reject(res.msg)\r\n    }\r\n  } catch (error) {\r\n    console.error('获取房费列表失败:', error)\r\n    ElMessage.warning('获取房费列表失败，无法使用优惠券')\r\n    roomFeeList.value = []\r\n    return Promise.reject(error)\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 分页相关方法\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => fetchCoupons())\r\n}\r\n\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => fetchCoupons())\r\n}\r\n\r\n// 监听弹框显示\r\nwatch(\r\n  () => props.modelValue,\r\n  (newVal) => {\r\n    if (newVal) {\r\n      // 如果已有选中的优惠券，则保留之前的选择\r\n      if (props.selectedCoupon) {\r\n        tempSelectedCoupons.value = [props.selectedCoupon]\r\n        discountMode.value = 'first_day'\r\n      }\r\n\r\n      // 如果传入了已选择的每日优惠券ID映射，则恢复之前的选择\r\n      if (props.selectedDailyCouponIds && Object.keys(props.selectedDailyCouponIds).length > 0) {\r\n        discountMode.value = 'multi_day'\r\n\r\n        // 重置分页到第一页\r\n        pagination.value.pageNo = 1\r\n\r\n        // 先获取房费数据和优惠券列表\r\n        fetchRoomFeeList()\r\n          .then(() => {\r\n            fetchCoupons().then(() => {\r\n              // 获取数据后恢复选择\r\n              selectedDailyCoupons.value = { ...props.selectedDailyCouponIds }\r\n            })\r\n          })\r\n          .catch(() => {\r\n            // 获取房费失败，清空选择\r\n            selectedDailyCoupons.value = {}\r\n          })\r\n      } else {\r\n        // 如果没有传入每日优惠券，但有传入单个优惠券，则设置为首日模式\r\n        if (props.selectedCoupon) {\r\n          discountMode.value = 'first_day'\r\n        } else {\r\n          // 默认首日优惠模式\r\n          discountMode.value = 'first_day'\r\n        }\r\n\r\n        selectedDailyCoupons.value = {}\r\n\r\n        // 重置分页到第一页\r\n        pagination.value.pageNo = 1\r\n\r\n        // 无论哪种模式，都先获取房费数据\r\n        fetchRoomFeeList()\r\n          .then(() => {\r\n            // 获取房费数据后再获取优惠券列表\r\n            fetchCoupons()\r\n          })\r\n          .catch(() => {\r\n            // 获取房费失败，清空选择\r\n            tempSelectedCoupons.value = []\r\n          })\r\n      }\r\n    } else {\r\n      searchKeyword.value = ''\r\n    }\r\n  }\r\n)\r\n\r\nfunction calculateDateTotalDiscount(accNo: string, couponId: string): number {\r\n  const coupon = getDailyCoupon(couponId)\r\n  if (!coupon) {\r\n    return 0\r\n  }\r\n\r\n  // 找到对应的房费\r\n  const roomFee = roomFeeList.value.find((fee) => fee.accNo === accNo)\r\n  if (!roomFee) {\r\n    return 0\r\n  }\r\n\r\n  // 计算优惠金额\r\n  return calculateDailyDiscount(coupon, roomFee.fee)\r\n}\r\n\r\nfunction getRoomFeeByAccNo(accNo: string): RoomFee | undefined {\r\n  return roomFeeList.value.find((fee) => fee.accNo === accNo)\r\n}\r\n\r\n// 在 watch 函数中添加从 sessionStorage 恢复选择的代码\r\nwatch(\r\n  () => discountMode.value,\r\n  (newMode) => {\r\n    if (newMode === 'first_day') {\r\n      // 从连住多天切换到首日模式\r\n      // 尝试恢复之前保存的首日优惠券选择\r\n      const savedCoupons = window.sessionStorage.getItem('temp_selected_coupons')\r\n      if (savedCoupons) {\r\n        try {\r\n          const parsedCoupons = JSON.parse(savedCoupons)\r\n          if (Array.isArray(parsedCoupons) && parsedCoupons.length > 0) {\r\n            tempSelectedCoupons.value = parsedCoupons\r\n          }\r\n        } catch (e) {\r\n          console.error('恢复首日优惠券选择失败:', e)\r\n        }\r\n      }\r\n    } else if (newMode === 'multi_day') {\r\n      // 从首日切换到连住多天模式\r\n      // 尝试恢复之前保存的每日优惠券选择\r\n      const savedDailyCoupons = window.sessionStorage.getItem('selected_daily_coupons')\r\n      if (savedDailyCoupons) {\r\n        try {\r\n          const parsedDailyCoupons = JSON.parse(savedDailyCoupons)\r\n          if (typeof parsedDailyCoupons === 'object' && Object.keys(parsedDailyCoupons).length > 0) {\r\n            selectedDailyCoupons.value = parsedDailyCoupons\r\n          }\r\n        } catch (e) {\r\n          console.error('恢复每日优惠券选择失败:', e)\r\n        }\r\n      }\r\n    }\r\n  }\r\n)\r\n\r\n// 检查是否有可用的房费（未使用优惠券）\r\nconst hasAvailableRoomFees = computed(() => {\r\n  return roomFeeList.value.some((fee) => fee.isUsed !== '1')\r\n})\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"dialogVisible\" :title=\"t('selectCoupon')\" width=\"700px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close class=\"coupon-dialog-container\">\r\n    <div class=\"coupon-dialog\">\r\n      <!-- 优惠模式选择 -->\r\n      <div class=\"discount-mode-selector\">\r\n        <el-radio-group v-model=\"discountMode\" @change=\"handleDiscountModeChange\">\r\n          <el-radio label=\"first_day\"> {{ t('firstDayDiscount') }} </el-radio>\r\n          <el-radio label=\"multi_day\" :disabled=\"!props.isWalkinMoreEnabled\"> {{ t('multiDayDiscount') }} </el-radio>\r\n        </el-radio-group>\r\n        <div v-if=\"!props.isWalkinMoreEnabled\" class=\"mode-disabled-tip\">{{ t('multiDayDiscountDisabled') }}</div>\r\n      </div>\r\n\r\n      <!-- 搜索区域 -->\r\n      <div class=\"search-area\">\r\n        <el-input v-model=\"searchKeyword\" :placeholder=\"t('searchCouponPlaceholder')\" prefix-icon=\"Search\" clearable @keydown.enter=\"handleSearch\" @clear=\"handleSearch\">\r\n          <template #append>\r\n            <el-button :icon=\"Search\" @click=\"handleSearch\" />\r\n          </template>\r\n        </el-input>\r\n      </div>\r\n\r\n      <!-- 多天房费列表 -->\r\n      <div v-if=\"discountMode === 'multi_day'\" class=\"room-fee-list-container\">\r\n        <!-- 添加空数据提示 -->\r\n        <el-empty v-if=\"availableCoupons.length === 0 && !loading\" :description=\"t('noCouponsAvailable')\" :image-size=\"100\">\r\n          <template #description>\r\n            <p>{{ t('noCouponsDesc') }}</p>\r\n            <ul class=\"empty-tips\">\r\n              <li>{{ t('noCouponsCheck1') }}</li>\r\n              <li>{{ t('noCouponsCheck2') }}</li>\r\n              <li>{{ t('noCouponsCheck3') }}</li>\r\n            </ul>\r\n          </template>\r\n        </el-empty>\r\n\r\n        <!-- 添加所有房费都已使用优惠券的提示 -->\r\n        <!--        <el-empty\r\n          v-else-if=\"roomFeeList.length > 0 && !hasAvailableRoomFees\"\r\n          description=\"所有房费都已使用优惠券\"\r\n          :image-size=\"100\"\r\n        >\r\n          <template #description>\r\n            <p>所有房费都已使用优惠券，无法再次使用优惠券</p>\r\n          </template>\r\n        </el-empty> -->\r\n\r\n        <el-table\r\n          v-else\r\n          :data=\"roomFeeList\"\r\n          border\r\n          style=\"width: 100%\"\r\n          height=\"300\"\r\n          :header-cell-style=\"{\r\n            background: '#f5f7fa',\r\n            color: '#606266',\r\n          }\"\r\n        >\r\n          <el-table-column prop=\"bizDate\" :label=\"t('date')\" width=\"120\" fixed>\r\n            <template #default=\"scope\">\r\n              {{ formatDateShort(scope.row.bizDate) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"rNo\" :label=\"t('roomNumber')\" width=\"80\" />\r\n          <el-table-column prop=\"subName\" :label=\"t('type')\" width=\"100\" />\r\n          <el-table-column prop=\"fee\" :label=\"t('roomFee')\" width=\"100\">\r\n            <template #default=\"scope\"> ¥{{ scope.row.fee.toFixed(2) }} </template>\r\n          </el-table-column>\r\n          <el-table-column :label=\"t('selectCouponOption')\">\r\n            <template #default=\"scope\">\r\n              <div class=\"coupon-selector\">\r\n                <!-- 已使用优惠券状态，显示\"已使用优惠券\" -->\r\n                <template v-if=\"scope.row.state === 'settled' || scope.row.isUsed === '1' || scope.row.couponCode\">\r\n                  <div class=\"coupon-used-tag\">\r\n                    <el-tag type=\"info\" size=\"small\"> {{ t('usedCoupon') }} </el-tag>\r\n                    <div v-if=\"scope.row.couponCode\" class=\"used-coupon-code\">{{ t('couponNumber') }}: {{ scope.row.couponCode }}</div>\r\n                  </div>\r\n                </template>\r\n                <!-- 可选择优惠券的房费 -->\r\n                <template v-else>\r\n                  <el-select v-model=\"selectedDailyCoupons[scope.row.accNo]\" :placeholder=\"t('selectCouponOption')\" clearable @change=\"(val) => handleDailyCouponSelect(val, scope.row)\">\r\n                    <el-option v-for=\"coupon in availableCoupons\" :key=\"coupon.id\" :label=\"coupon.name\" :value=\"coupon.id\" :disabled=\"!isAvailableForFee(coupon, scope.row.fee) || isCouponAlreadySelected(coupon.id, scope.row.accNo)\">\r\n                      <el-tooltip\r\n                        v-if=\"isCouponAlreadySelected(coupon.id, scope.row.accNo)\"\r\n                        :content=\"t('couponAlreadyUsedOn', { date: getDateUsingCoupon(coupon.id) })\"\r\n                        placement=\"top\"\r\n                        effect=\"light\"\r\n                      >\r\n                        <div class=\"coupon-option\">\r\n                          <span>{{ coupon.name }}</span>\r\n                          <span class=\"coupon-discount\">\r\n                            <template v-if=\"coupon.couponType === 'discount'\">{{ ((coupon.rebate || 0) * 100).toFixed(0) }}{{ t('discountUnit') }}</template>\r\n                            <template v-else-if=\"coupon.couponType === 'free'\">{{ t('freeRoom') }}</template>\r\n                            <template v-else>¥{{ coupon.discount }}</template>\r\n                          </span>\r\n                        </div>\r\n                      </el-tooltip>\r\n                      <el-tooltip\r\n                        v-else-if=\"!isAvailableForFee(coupon, scope.row.fee)\"\r\n                        :content=\"getUnavailableReason(coupon)\"\r\n                        placement=\"top\"\r\n                        effect=\"light\"\r\n                      >\r\n                        <div class=\"coupon-option disabled\">\r\n                          <span>{{ coupon.name }}</span>\r\n                          <span class=\"coupon-discount\">\r\n                            <template v-if=\"coupon.couponType === 'discount'\">{{ ((coupon.rebate || 0) * 100).toFixed(0) }}{{ t('discountUnit') }}</template>\r\n                            <template v-else-if=\"coupon.couponType === 'free'\">{{ t('freeRoom') }}</template>\r\n                            <template v-else>¥{{ coupon.discount }}</template>\r\n                          </span>\r\n                        </div>\r\n                      </el-tooltip>\r\n                      <div v-else class=\"coupon-option\">\r\n                        <span>{{ coupon.name }}</span>\r\n                        <span class=\"coupon-discount\">\r\n                          <template v-if=\"coupon.couponType === 'discount'\">{{ ((coupon.rebate || 0) * 100).toFixed(0) }}{{ t('discountUnit') }}</template>\r\n                          <template v-else-if=\"coupon.couponType === 'free'\">{{ t('freeRoom') }}</template>\r\n                          <template v-else>¥{{ coupon.discount }}</template>\r\n                        </span>\r\n                      </div>\r\n                    </el-option>\r\n                  </el-select>\r\n                  <div v-if=\"selectedDailyCoupons[scope.row.accNo]\" class=\"selected-daily-coupon\">\r\n                    <span>优惠: </span>\r\n                    <span class=\"discount-amount\">-¥{{ calculateDailyDiscount(getDailyCoupon(selectedDailyCoupons[scope.row.accNo]), scope.row.fee).toFixed(2) }}</span>\r\n                  </div>\r\n                </template>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 优惠券列表 (首日优惠模式) -->\r\n      <div v-if=\"discountMode === 'first_day'\" class=\"coupon-list-container\">\r\n        <div v-loading=\"loading\" class=\"coupon-list\">\r\n          <!-- 添加所有首日房费都已使用优惠券的提示 -->\r\n          <el-empty v-if=\"firstDayFee === 0 && roomFeeList.length > 0\" :description=\"t('firstDayUsedCoupon')\" :image-size=\"100\">\r\n            <template #description>\r\n              <p>{{ t('firstDayUsedCouponDesc') }}</p>\r\n            </template>\r\n          </el-empty>\r\n\r\n          <!-- 添加空数据提示 -->\r\n          <el-empty v-else-if=\"coupons.length === 0 && !loading\" :description=\"t('noCouponsAvailable')\" :image-size=\"100\">\r\n            <template #description>\r\n              <p>{{ t('noCouponsDesc') }}</p>\r\n              <ul class=\"empty-tips\">\r\n                <li>{{ t('noCouponsCheck1') }}</li>\r\n                <li>{{ t('noCouponsCheck2') }}</li>\r\n                <li>{{ t('noCouponsCheck3') }}</li>\r\n              </ul>\r\n            </template>\r\n          </el-empty>\r\n\r\n          <el-tooltip\r\n            v-for=\"coupon in coupons\"\r\n            :key=\"coupon.id\"\r\n            :content=\"!isAvailable(coupon) ? getUnavailableReason(coupon) : ''\"\r\n            :disabled=\"isAvailable(coupon)\"\r\n            placement=\"top\"\r\n            effect=\"light\"\r\n          >\r\n            <div\r\n              class=\"coupon-item\"\r\n              :class=\"{\r\n                selected: isSelected(coupon),\r\n                disabled: !isAvailable(coupon),\r\n              }\"\r\n              @click=\"toggleCoupon(coupon)\"\r\n            >\r\n              <div class=\"coupon-content\">\r\n                <div class=\"coupon-header\">\r\n                  <div class=\"coupon-name\">\r\n                    {{ coupon.name }}\r\n                  </div>\r\n                  <div class=\"coupon-amount\">\r\n                    <template v-if=\"coupon.couponType === 'discount'\"> {{ ((coupon.rebate || 0) * 100).toFixed(0) }}{{ t('discountUnit') }} </template>\r\n                    <template v-else-if=\"coupon.couponType === 'free'\"> {{ t('freeRoom') }} </template>\r\n                    <template v-else> ¥{{ coupon.discount }} </template>\r\n                  </div>\r\n                </div>\r\n                <div class=\"coupon-info-row\">\r\n                  <span class=\"coupon-code\">{{ t('couponCode') }}：{{ coupon.templateCode }}</span>\r\n                  <span class=\"coupon-type\">{{ coupon.couponTypeName }}</span>\r\n                </div>\r\n                <div class=\"coupon-info-row\">\r\n                  <span class=\"coupon-code\">{{ t('couponNumber') }}：{{ coupon.couponCode }}</span>\r\n                  <span v-if=\"!isAvailable(coupon)\" class=\"unavailable-text\">\r\n                    {{ getUnavailableReason(coupon) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"coupon-desc\">\r\n                  {{ coupon.description }}\r\n                </div>\r\n                <div class=\"coupon-info\">\r\n                  <span class=\"valid-time\"> {{ t('validPeriod') }}：{{ formatDate(coupon.startTime) }} ~ {{ formatDate(coupon.endTime) }} </span>\r\n                  <span v-if=\"coupon.minAmount > 0\" class=\"min-amount\"> {{ t('minAmount', { amount: coupon.minAmount }) }} </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"coupon-status\">\r\n                <el-icon v-if=\"isSelected(coupon)\" class=\"selected-icon\">\r\n                  <Check />\r\n                </el-icon>\r\n                <!--                <span v-else-if=\"!isAvailable(coupon)\" class=\"unavailable-text\">-->\r\n                <!--                  {{ getUnavailableReason(coupon) }}-->\r\n                <!--                </span>-->\r\n              </div>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分页组件 -->\r\n      <el-pagination\r\n        v-if=\"pagination.total > 0 && discountMode === 'first_day'\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"coupon-pagination\"\r\n        background\r\n        small\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n\r\n      <!-- 已选择的优惠券汇总 -->\r\n      <div v-if=\"(tempSelectedCoupons.length > 0 && discountMode === 'first_day') || hasSelectedDailyCoupons\" class=\"selected-summary\">\r\n        <div class=\"summary-title\">{{ t('selectedCoupons') }}</div>\r\n\r\n        <!-- 首日优惠模式 -->\r\n        <template v-if=\"discountMode === 'first_day' && tempSelectedCoupons.length > 0\">\r\n          <div class=\"selected-coupon-info\">\r\n            <span class=\"coupon-name\">{{ tempSelectedCoupons[0].name }}</span>\r\n            <span class=\"coupon-code\">{{ tempSelectedCoupons[0].templateCode }}</span>\r\n          </div>\r\n          <div class=\"summary-detail\">\r\n            {{ t('discountAmount') }}：\r\n            <template v-if=\"tempSelectedCoupons[0]?.couponType === 'discount'\"> {{ ((tempSelectedCoupons[0].rebate || 0) * 100).toFixed(0) }}{{ t('discountUnit') }} (¥{{ totalDiscount.toFixed(2) }}) </template>\r\n            <template v-else-if=\"tempSelectedCoupons[0]?.couponType === 'free'\"> {{ t('freeRoom') }} (¥{{ totalDiscount.toFixed(2) }}) </template>\r\n            <template v-else> ¥{{ totalDiscount.toFixed(2) }} </template>\r\n          </div>\r\n        </template>\r\n\r\n        <!-- 连住多天优惠模式 -->\r\n        <template v-if=\"discountMode === 'multi_day' && hasSelectedDailyCoupons\">\r\n          <div class=\"multi-day-summary\">\r\n            <template v-for=\"(couponId, accNo) in selectedDailyCoupons\" :key=\"accNo\">\r\n              <div v-if=\"couponId\" class=\"daily-coupon-item\">\r\n                <div class=\"daily-date\">\r\n                  {{ formatDateShort(getRoomFeeByAccNo(accNo)?.bizDate || '') }}\r\n                  <span v-if=\"!getRoomFeeByAccNo(accNo)?.bizDate\" class=\"date-error\">日期错误</span>\r\n                </div>\r\n                <div class=\"daily-coupon-name\">\r\n                  {{ getDailyCoupon(couponId)?.name }}\r\n                </div>\r\n                <div class=\"daily-discount\">\r\n                  <template v-if=\"getDailyCoupon(couponId)?.couponType === 'discount'\"> {{ ((getDailyCoupon(couponId)?.rebate || 0) * 100).toFixed(0) }}{{ t('discountUnit') }} (¥{{ calculateDateTotalDiscount(accNo, couponId).toFixed(2) }}) </template>\r\n                  <template v-else-if=\"getDailyCoupon(couponId)?.couponType === 'free'\"> {{ t('freeRoom') }} (¥{{ calculateDateTotalDiscount(accNo, couponId).toFixed(2) }}) </template>\r\n                  <template v-else> ¥{{ calculateDateTotalDiscount(accNo, couponId).toFixed(2) }} </template>\r\n                </div>\r\n              </div>\r\n            </template>\r\n            <div class=\"total-multi-discount\">{{ t('totalDiscountAmount') }}：¥{{ totalMultiDayDiscount.toFixed(2) }}</div>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button @click=\"handleCancel\"> {{ t('cancel') }} </el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :disabled=\"\r\n            (discountMode === 'first_day' && (coupons.length === 0 || firstDayFee === 0)) ||\r\n            (discountMode === 'first_day' && tempSelectedCoupons.length === 0) ||\r\n            (discountMode === 'multi_day' && !hasSelectedDailyCoupons) ||\r\n            (discountMode === 'multi_day' && !hasAvailableRoomFees)\r\n          \"\r\n          @click=\"handleConfirm\"\r\n        >\r\n          {{ t('confirmUse') }}{{ tempSelectedCoupons.length === 0 && !hasSelectedDailyCoupons ? t('notSelected') : '' }}\r\n        </el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.coupon-dialog-container) {\r\n  .el-dialog__body {\r\n    max-height: 70vh;\r\n    overflow-y: auto;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.coupon-dialog {\r\n  .discount-mode-selector {\r\n    margin-bottom: 20px;\r\n    padding: 10px;\r\n    background-color: #f5f7fa;\r\n    border-radius: 4px;\r\n\r\n    .mode-disabled-tip {\r\n      margin-top: 5px;\r\n      font-size: 12px;\r\n      color: #f56c6c;\r\n    }\r\n  }\r\n\r\n  .search-area {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .room-fee-list-container {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .empty-tips {\r\n    text-align: left;\r\n    padding-left: 20px;\r\n    margin: 5px 0;\r\n    font-size: 12px;\r\n    color: #909399;\r\n\r\n    li {\r\n      margin-bottom: 3px;\r\n    }\r\n  }\r\n\r\n  .coupon-selector {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 5px;\r\n\r\n    .selected-daily-coupon {\r\n      font-size: 12px;\r\n\r\n      .discount-amount {\r\n        color: #f56c6c;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n\r\n    .coupon-used-tag {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 5px;\r\n\r\n      .used-coupon-code {\r\n        font-size: 12px;\r\n        color: #909399;\r\n      }\r\n    }\r\n\r\n    .coupon-option {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n\r\n      .coupon-discount {\r\n        color: #f56c6c;\r\n        font-weight: bold;\r\n      }\r\n\r\n      &.disabled {\r\n        opacity: 0.5;\r\n        color: #c0c4cc;\r\n\r\n        .coupon-discount {\r\n          color: #c0c4cc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .coupon-list-container {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .coupon-list {\r\n    max-height: 350px;\r\n    overflow-y: auto;\r\n\r\n    .coupon-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 16px;\r\n      margin-bottom: 12px;\r\n      border: 1px solid #e4e7ed;\r\n      border-radius: 8px;\r\n      cursor: pointer;\r\n      transition: all 0.3s;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n      }\r\n\r\n      &.selected {\r\n        border-color: #409eff;\r\n        background-color: #f0f9ff;\r\n      }\r\n\r\n      &.disabled {\r\n        opacity: 0.5;\r\n        cursor: not-allowed;\r\n\r\n        &:hover {\r\n          border-color: #e4e7ed;\r\n          box-shadow: none;\r\n        }\r\n      }\r\n\r\n      .coupon-content {\r\n        flex: 1;\r\n\r\n        .coupon-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .coupon-name {\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            color: #303133;\r\n          }\r\n\r\n          .coupon-amount {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #f56c6c;\r\n          }\r\n        }\r\n\r\n        .coupon-info-row {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 8px;\r\n\r\n          .coupon-code {\r\n            font-size: 12px;\r\n            color: #909399;\r\n          }\r\n\r\n          .coupon-type {\r\n            font-size: 12px;\r\n            color: #909399;\r\n          }\r\n\r\n          .unavailable-text {\r\n            font-size: 12px;\r\n            color: #f56c6c;\r\n            font-weight: bold;\r\n            text-align: right;\r\n          }\r\n        }\r\n\r\n        .coupon-desc {\r\n          font-size: 14px;\r\n          color: #606266;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .coupon-info {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          font-size: 12px;\r\n          color: #909399;\r\n\r\n          .min-amount {\r\n            color: #e6a23c;\r\n            text-align: right;\r\n          }\r\n        }\r\n      }\r\n\r\n      .coupon-status {\r\n        margin-left: 16px;\r\n\r\n        .selected-icon {\r\n          color: #409eff;\r\n          font-size: 20px;\r\n        }\r\n\r\n        .unavailable-text {\r\n          font-size: 12px;\r\n          color: #f56c6c;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .coupon-pagination {\r\n    margin: 20px 0;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .selected-summary {\r\n    margin-top: 20px;\r\n    padding: 16px;\r\n    background-color: #f5f7fa;\r\n    border-radius: 8px;\r\n\r\n    .summary-title {\r\n      font-size: 14px;\r\n      color: #606266;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .selected-coupon-info {\r\n      margin-bottom: 8px;\r\n\r\n      .coupon-name {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #303133;\r\n      }\r\n\r\n      .coupon-code {\r\n        font-size: 12px;\r\n        color: #909399;\r\n      }\r\n    }\r\n\r\n    .summary-detail {\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      color: #67c23a;\r\n    }\r\n\r\n    .multi-day-summary {\r\n      .daily-coupon-item {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: 8px 0;\r\n        border-bottom: 1px dashed #e4e7ed;\r\n\r\n        .daily-date {\r\n          font-weight: bold;\r\n          width: 80px;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .date-error {\r\n            color: #f56c6c;\r\n            font-size: 12px;\r\n            margin-left: 5px;\r\n          }\r\n        }\r\n\r\n        .daily-coupon-name {\r\n          flex: 1;\r\n          margin: 0 10px;\r\n        }\r\n\r\n        .daily-discount {\r\n          color: #f56c6c;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n\r\n      .total-multi-discount {\r\n        margin-top: 10px;\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #67c23a;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "usePagination", "t", "useI18n", "dialogVisible", "computed", "get", "modelValue", "set", "val", "loading", "ref", "searchKeyword", "coupons", "tempSelectedCoupons", "discountMode", "roomFeeList", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "availableCoupons", "value", "filter", "coupon", "name", "toLowerCase", "includes", "description", "firstDayFee", "length", "console", "log", "sortedFees", "sort", "a", "b", "Date", "bizDate", "getTime", "earliestDate", "_a", "sameDay", "fee", "unusedFee", "find", "isUsed", "date", "subName", "every", "_b", "_c", "totalDiscount", "actualFirstDayFee", "couponName", "couponType", "roomFeeList<PERSON><PERSON><PERSON>", "orderAmount", "Number", "parseFloat", "rebate", "toFixed", "Math", "min", "discount", "hasSelectedDailyCoupons", "Object", "values", "some", "id", "totalMultiDayDiscount", "total", "accNo", "couponId", "getDailyCoupon", "roomFee", "calculateDailyDiscount", "formatDate", "dateStr", "toLocaleDateString", "formatDateShort", "isNaN", "getMonth", "getDate", "error", "isSelected", "c", "isAvailable", "status", "matchCurrentHcode", "rtCode", "rtCodes", "firstDayFees", "minAmount", "now", "startTime", "endTime", "isAvailableForFee", "getUnavailableReason", "amount", "isRoomFeeUsedCoupon", "state", "couponCode", "isCouponAlreadySelected", "currentAccNo", "getDateUsingCoupon", "getRoomFeeByAccNo", "handleSearch", "fetchCoupons", "handleConfirm", "window", "sessionStorage", "removeItem", "ElMessage", "warning", "dailyCoupons", "keys", "handleCancel", "handleDiscountModeChange", "previousMode", "setItem", "JSON", "stringify", "getDailyCouponsMap", "async", "params", "gcode", "hcode", "checkinType", "channelCode", "mCode", "guest<PERSON><PERSON>", "templateName", "allFirstDayUsed", "res", "couponApi", "memberList", "code", "data", "Array", "isArray", "couponsList", "map", "item", "discountValue", "money", "templateCode", "couponTypeName", "intro", "consume", "effDate", "expDate", "type", "msg", "fetchRoomFeeList", "togetherCodes", "Promise", "reject", "join", "accountApi", "accountRoomFee", "warn", "processedData", "isSettled", "count", "firstDay", "resolve", "sizeChange", "size", "then", "currentChange", "page", "calculateDateTotalDiscount", "watch", "newVal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedDailyCouponIds", "pageNo", "catch", "newMode", "savedCoupons", "getItem", "parsedCoupons", "parse", "e", "savedDailyCoupons", "parsedDailyCoupons", "hasAvailableRoomFees", "message", "duration", "newTotal", "index", "findIndex"], "mappings": "64FAiLA,MAAMA,GAAQC,EA6BRC,GAAQC,EAMRC,GAAYC,KACZC,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,GAAcC,gBAAAA,IAAoBC,KAC3DC,EAAEA,IAAMC,IAERC,GAAgBC,EAAS,CAC7BC,IAAK,IAAMf,GAAMgB,WACjBC,IAAMC,GAAQhB,GAAM,oBAAqBgB,KAGrCC,GAAUC,GAAI,GACdC,GAAgBD,EAAI,IACpBE,GAAUF,EAAc,IACxBG,GAAsBH,EAAc,IACpCI,GAAeJ,EAAI,aACnBK,GAAcL,EAAe,IAC7BM,GAAuBN,EAA4B,IACnDO,GAAmBP,EAAc,IAGfN,GAAS,IAC1BO,GAAcO,MAIZN,GAAQM,MAAMC,QAAQC,GAAWA,EAAOC,KAAKC,cAAcC,SAASZ,GAAcO,MAAMI,gBAAkBF,EAAOI,YAAYF,cAAcC,SAASZ,GAAcO,MAAMI,iBAHtKV,GAAQM,QAOb,MAAAO,GAAcrB,GAAS,eACvB,GAA6B,IAA7BW,GAAYG,MAAMQ,OAEb,OADPC,QAAQC,IAAI,mBACL,EAIH,MAAAC,EAAa,IAAId,GAAYG,OAAOY,MAAK,CAACC,EAAGC,IAAM,IAAIC,KAAKF,EAAEG,SAASC,UAAY,IAAIF,KAAKD,EAAEE,SAASC,YAGvGC,EAAe,OAAAC,EAAAR,EAAW,SAAI,EAAAQ,EAAAH,QAC9BI,EAAUT,EAAWV,QAAQoB,GAAQA,EAAIL,UAAYE,IAGrDI,EAAYF,EAAQG,MAAMF,GAAuB,MAAfA,EAAIG,SAC5C,GAAIF,EAMF,OALAb,QAAQC,IAAI,iBAAkB,CAC5Be,KAAMH,EAAUN,QAChBK,IAAKC,EAAUD,IACfK,QAASJ,EAAUI,UAEdJ,EAAUD,IAInB,GAAID,EAAQO,OAAON,GAAuB,MAAfA,EAAIG,SAEtB,OADPf,QAAQC,IAAI,yBACL,EAGT,MAAMW,GAAM,OAAAO,EAAAjB,EAAW,aAAIU,MAAO,EAM3B,OALPZ,QAAQC,IAAI,UAAW,CACrBe,KAAM,OAAAI,EAAAlB,EAAW,SAAI,EAAAkB,EAAAb,QACrBK,QAGKA,CAAA,IAGHS,GAAgB5C,GAAS,KACzB,GAAqC,IAArCS,GAAoBK,MAAMQ,OACrB,OAAA,EAGH,MAAAN,EAASP,GAAoBK,MAAM,GAGnC+B,EAAoBxB,GAAYP,MAYlC,OATJS,QAAQC,IAAI,wBAAyB,CACnCsB,WAAY9B,EAAOC,KACnB8B,WAAY/B,EAAO+B,WACnB1B,YAAawB,EACbG,kBAAmBrC,GAAYG,MAAMQ,OACrC2B,YAAa/D,GAAM+D,YACnB,OAAMtC,GAAYG,MAAMQ,OAAS,EAAI,WAAa,UAG1B,aAAtBN,EAAO+B,WAEFG,OAAOC,YAAYN,GAAqB,GAAK7B,EAAOoC,QAAU,KAAKC,QAAQ,IACnD,SAAtBrC,EAAO+B,WAETG,OAAOC,WAAWN,EAAkBQ,QAAQ,IAG5CH,OAAOC,WAAWG,KAAKC,IAAIvC,EAAOwC,SAAUX,GAAmBQ,QAAQ,GAAE,IAI9EI,GAA0BzD,GAAS,IAChC0D,OAAOC,OAAO/C,GAAqBE,OAAO8C,MAAMC,GAAOA,MAG1DC,GAAwB9D,GAAS,KACrC,IAAI+D,EAAQ,EAGD,IAAA,MAAAC,KAASpD,GAAqBE,MAAO,CACxC,MAAAmD,EAAWrD,GAAqBE,MAAMkD,GAC5C,IAAKC,EACH,SAGI,MAAAjD,EAASkD,GAAeD,GAC9B,IAAKjD,EACH,SAII,MAAAmD,EAAUxD,GAAYG,MAAMuB,MAAMF,GAAQA,EAAI6B,QAAUA,IACzDG,IAIIJ,GAAAK,GAAuBpD,EAAQmD,EAAQhC,KAAG,CAIrD,OAAOe,OAAOC,WAAWY,EAAMV,QAAQ,GAAE,IAI3C,SAASgB,GAAWC,GAClB,OAAO,IAAIzC,KAAKyC,GAASC,oBAAmB,CAG9C,SAASC,GAAgBF,GACvB,IAAKA,EACI,MAAA,GAGL,IACI,MAAA/B,EAAO,IAAIV,KAAKyC,GACtB,OAAIG,MAAMlC,EAAKR,WACN,GAEF,GAAGQ,EAAKmC,WAAa,KAAKnC,EAAKoC,mBAC/BC,GACA,MAAA,EAAA,CACT,CAGF,SAASC,GAAW7D,GACX,OAAAP,GAAoBK,MAAM8C,MAAMkB,GAAMA,EAAEjB,KAAO7C,EAAO6C,IAAE,CAGjE,SAASkB,GAAY/D,SAEf,GAA6B,IAA7BL,GAAYG,MAAMQ,OACb,OAAA,EAGL,GAAkB,cAAlBN,EAAOgE,OACF,OAAA,EAIL,IAA6B,IAA7BhE,EAAOiE,kBACF,OAAA,EAIT,GAAI/F,GAAMgG,QAAUlE,EAAOmE,SAAWnE,EAAOmE,QAAQ7D,OAAS,IACvDN,EAAOmE,QAAQhE,SAASjC,GAAMgG,QAC1B,OAAA,EAKL,MAAAlD,EAAe,OAAAC,EAAC,IAAGtB,GAAYG,OAAOY,MAAK,CAACC,EAAGC,IAAM,IAAIC,KAAKF,EAAEG,SAASC,UAAY,IAAIF,KAAKD,EAAEE,SAASC,YAAW,SAAI,EAAAE,EAAAH,QAExHsD,EAAezE,GAAYG,MAAMC,QAAQoB,GAAQA,EAAIL,UAAYE,IAIvE,GAHwBoD,EAAa9D,OAAS,GAAK8D,EAAa3C,OAAON,GAAuB,MAAfA,EAAIG,SAI1E,OAAA,EAIT,GAAItB,EAAOqE,UAAY,GAAKhE,GAAYP,MAAQE,EAAOqE,UAC9C,OAAA,EAGH,MAAAC,MAAUzD,KACV0D,EAAY,IAAI1D,KAAKb,EAAOuE,WAC5BC,EAAU,IAAI3D,KAAKb,EAAOwE,SAEzB,OAAAF,GAAOC,GAAaD,GAAOE,CAAA,CAG3B,SAAAC,GAAkBzE,EAAgBmB,GACrC,GAAkB,cAAlBnB,EAAOgE,OACF,OAAA,EAEL,IAA6B,IAA7BhE,EAAOiE,kBACF,OAAA,EAIT,GAAI/F,GAAMgG,QAAUlE,EAAOmE,SAAWnE,EAAOmE,QAAQ7D,OAAS,IACvDN,EAAOmE,QAAQhE,SAASjC,GAAMgG,QAC1B,OAAA,EAIX,GAAIlE,EAAOqE,UAAY,GAAKlD,EAAMnB,EAAOqE,UAChC,OAAA,EAGH,MAAAC,MAAUzD,KACV0D,EAAY,IAAI1D,KAAKb,EAAOuE,WAC5BC,EAAU,IAAI3D,KAAKb,EAAOwE,SAEzB,OAAAF,GAAOC,GAAaD,GAAOE,CAAA,CAIpC,SAASE,GAAqB1E,SACxB,GAAkB,cAAlBA,EAAOgE,OACT,OAAOnF,GAAE,4BAGP,IAA6B,IAA7BmB,EAAOiE,kBACT,OAAOpF,GAAE,uBAIX,GAAIX,GAAMgG,QAAUlE,EAAOmE,SAAWnE,EAAOmE,QAAQ7D,OAAS,IACvDN,EAAOmE,QAAQhE,SAASjC,GAAMgG,QACjC,OAAOrF,GAAE,iCAKP,MAAAmC,EAAe,OAAAC,EAAC,IAAGtB,GAAYG,OAAOY,MAAK,CAACC,EAAGC,IAAM,IAAIC,KAAKF,EAAEG,SAASC,UAAY,IAAIF,KAAKD,EAAEE,SAASC,YAAW,SAAI,EAAAE,EAAAH,QACxHsD,EAAezE,GAAYG,MAAMC,QAAQoB,GAAQA,EAAIL,UAAYE,IAGvE,GAFwBoD,EAAa9D,OAAS,GAAK8D,EAAa3C,OAAON,GAAuB,MAAfA,EAAIG,SAGjF,OAAOzC,GAAE,6BAIX,GAAImB,EAAOqE,UAAY,GAAKhE,GAAYP,MAAQE,EAAOqE,UACrD,OAAOxF,GAAE,oBAAqB,CAAE8F,OAAQ3E,EAAOqE,YAG3C,MAAAC,MAAUzD,KACV0D,EAAY,IAAI1D,KAAKb,EAAOuE,WAC5BC,EAAU,IAAI3D,KAAKb,EAAOwE,SAEhC,OACS3F,GADLyF,EAAMC,EACC,oBAGPD,EAAME,EACC,gBAGF,eAAc,CAIzB,SAASI,GAAoBzB,GAEpB,MAAmB,MAAnBA,EAAQ7B,QAAoC,YAAlB6B,EAAQ0B,OAAyC,MAAlB1B,EAAQ0B,SAAmB1B,EAAQ2B,UAAA,CAG5F,SAAAC,GAAwB9B,EAAkB+B,GAEtC,IAAA,MAAAhC,KAASpD,GAAqBE,MAEvC,GAAIkD,IAAUgC,GAKVpF,GAAqBE,MAAMkD,KAAWC,EACjC,OAAA,EAGJ,OAAA,CAAA,CAGT,SAASgC,GAAmBhC,GACf,IAAA,MAAAD,KAASpD,GAAqBE,MACvC,GAAIF,GAAqBE,MAAMkD,KAAWC,EAAU,CAC5C,MAAAE,EAAU+B,GAAkBlC,GAC9B,GAAAG,GAAWA,EAAQrC,QACd,OAAA0C,GAAgBL,EAAQrC,QACjC,CAGJ,OAAOjC,GAAE,YAAW,CAgBtB,SAASsG,KAEMC,IAAA,CAGf,SAASC,WAKH,GAHGC,OAAAC,eAAeC,WAAW,yBAC1BF,OAAAC,eAAeC,WAAW,0BAEN,cAAvB9F,GAAaI,MAAuB,CAGhC,MAAAkB,EAAe,OAAAC,EAAC,IAAGtB,GAAYG,OAAOY,MAAK,CAACC,EAAGC,IAAM,IAAIC,KAAKF,EAAEG,SAASC,UAAY,IAAIF,KAAKD,EAAEE,SAASC,YAAW,SAAI,EAAAE,EAAAH,QAExHsD,EAAezE,GAAYG,MAAMC,QAAQoB,GAAQA,EAAIL,UAAYE,IAGvE,GAFwBoD,EAAa9D,OAAS,GAAK8D,EAAa3C,OAAON,GAAuB,MAAfA,EAAIG,SAIjF,YADUmE,EAAAC,QAAQ7G,GAAE,2BAKtBT,GAAM,UAAWqB,GAAoBK,MAAMQ,OAAS,EAAIb,GAAoBK,MAAM,GAAK,KAAM8B,GAAc9B,WAAO,GAAW,EAAI,KAC5H,CAEL,MAAM6F,EAAoC,CAAC,EAGhC,IAAA,MAAA3C,KAASpD,GAAqBE,MAAO,CACxC,MAAAmD,EAAWrD,GAAqBE,MAAMkD,GAC5C,IAAKC,EACH,SAGI,MAAAjD,EAASkD,GAAeD,GAC9B,IAAKjD,EACH,SAII,MAAAmD,EAAU+B,GAAkBlC,GAClC,IAAKG,IAAYA,EAAQrC,QACvB,SAIE,GAAA8D,GAAoBzB,GACtB,SAGF,MAAM5B,EAAO4B,EAAQrC,QAGhB6E,EAAapE,KACHoE,EAAApE,GAAQ,CAAC,GAGXoE,EAAApE,GAAMyB,GAAS,IACvBhD,EACHgD,QACA7B,IAAKgC,EAAQhC,IACf,CAIF,GAAyC,IAArCuB,OAAOkD,KAAKD,GAAcrF,OAE5B,YADAmF,EAAUC,QAAQ,iBAKpBtH,GAAM,UAAW,KAAM0E,GAAsBhD,MAAO6F,GAAc,EAAI,CACxE,CAGF,SAASE,KAEAP,OAAAC,eAAeC,WAAW,yBAC1BF,OAAAC,eAAeC,WAAW,0BACjCpH,GAAM,SAAQ,CAGhB,SAAS0H,WAEP,MAAMC,EAAsC,cAAvBrG,GAAaI,MAAwB,YAAc,YAGxE,GAA2B,cAAvBJ,GAAaI,OAAsD,IAA7BH,GAAYG,MAAMQ,OAI1D,OAHAmF,EAAUC,QAAQ,0BAElBhG,GAAaI,MAAQiG,GAKI,cAAvBrG,GAAaI,OAAyBL,GAAoBK,MAAMQ,OAAS,GAE3EgF,OAAOC,eAAeS,QAAQ,wBAAyBC,KAAKC,UAAUzG,GAAoBK,QAC1FS,QAAQC,IAAI,aAAc,OAAAS,EAAAxB,GAAoBK,MAAM,aAAIG,MAExDR,GAAoBK,MAAQ,IACI,cAAvBJ,GAAaI,OAAyB4C,OAAOkD,KAAKhG,GAAqBE,OAAOQ,OAAS,IAEhGgF,OAAOC,eAAeS,QAAQ,yBAA0BC,KAAKC,UAAUtG,GAAqBE,QAC5FS,QAAQC,IAAI,eAAgBkC,OAAOkD,KAAKhG,GAAqBE,OAAOQ,QAEpEV,GAAqBE,MAAQ,CAAC,GAIhCS,QAAQC,IAAI,QAAS,CACnB,IAAGuF,EACH,IAAGrG,GAAaI,MAChB,SAAQH,GAAYG,MAAMQ,OAC1B,OAAMD,GAAYP,OACnB,CAGH,SAASoD,GAAeL,GACtB,OAAOhD,GAAiBC,MAAMuB,MAAMyC,GAAMA,EAAEjB,KAAOA,GAAE,CAG9C,SAAAO,GAAuBpD,EAA4BmB,GAC1D,OAAKnB,EAIqB,aAAtBA,EAAO+B,WAEFG,OAAOC,YAAYhB,GAAO,GAAKnB,EAAOoC,QAAU,KAAKC,QAAQ,IACrC,SAAtBrC,EAAO+B,WAETG,OAAOC,WAAWhB,EAAIkB,QAAQ,IAG9BH,OAAOC,WAAWG,KAAKC,IAAIvC,EAAOwC,SAAUrB,GAAKkB,QAAQ,IAXzD,CAYT,CA+FF,SAAS8D,KACP,MAAMR,EAAoC,CAAC,EAGhC,IAAA,MAAA3C,KAASpD,GAAqBE,MAAO,CACxC,MAAAmD,EAAWrD,GAAqBE,MAAMkD,GAC5C,IAAKC,EACH,SAGI,MAAAjD,EAASkD,GAAeD,GAC9B,IAAKjD,EACH,SAII,MAAAmD,EAAU+B,GAAkBlC,GAClC,IAAKG,IAAYA,EAAQrC,QACvB,SAIE,GAAA8D,GAAoBzB,GACtB,SAGF,MAAM5B,EAAO4B,EAAQrC,QAGhB6E,EAAapE,KACHoE,EAAApE,GAAQ,CAAC,GAGXoE,EAAApE,GAAMyB,GAAS,IACvBhD,EACHgD,QACA7B,IAAKgC,EAAQhC,IACf,CAGK,OAAAwE,CAAA,CAITS,eAAehB,KAEb,GAAiC,IAA7BzF,GAAYG,MAAMQ,QAAuC,cAAvBZ,GAAaI,MAIjD,OAHAN,GAAQM,MAAQ,GAChBD,GAAiBC,MAAQ,QACzB2F,EAAUC,QAAQ,oBAIpBrG,GAAQS,OAAQ,EACZ,IACF,MAAMuG,EAAS,CACbC,MAAOhI,GAAUgI,MACjBC,MAAOjI,GAAUiI,MACjBC,YAAatI,GAAMsI,YACnBtC,OAAQhG,GAAMgG,OACduC,YAAavI,GAAMuI,YAInBC,MAAOxI,GAAMyI,UACbC,aAAcrH,GAAcO,OAIxB+B,EAAoBxB,GAAYP,MAGhC+G,EACJlH,GAAYG,MAAMQ,OAAS,GAAKX,GAAYG,MAAMC,QAAQoB,GAAQA,EAAIL,UAAYnB,GAAYG,MAAMY,MAAK,CAACC,EAAGC,IAAM,IAAIC,KAAKF,EAAEG,SAASC,UAAY,IAAIF,KAAKD,EAAEE,SAASC,YAAW,GAAGD,UAASW,OAAON,GAAuB,MAAfA,EAAIG,SAE/MuF,GAA0C,cAAvBnH,GAAaI,OAClCS,QAAQC,IAAI,4BAIdD,QAAQC,IAAI,eAAgB,CAC1B,OAAMqB,EACN,SAAQlC,GAAYG,MAAMQ,OAC1B,eAAcuG,EACdF,UAAWzI,GAAMyI,YAGnB,MAAMG,QAAYC,EAAUC,WAAWX,GAGnC,GAFI9F,QAAAC,IAAI,aAAcsG,GAET,IAAbA,EAAIG,KAAY,CAElB,IAAKH,EAAII,OAASC,MAAMC,QAAQN,EAAII,OAA6B,IAApBJ,EAAII,KAAK5G,OAUpD,OATAd,GAAQM,MAAQ,GAChBD,GAAiBC,MAAQ,SAGrBL,GAAoBK,MAAMQ,OAAS,GAAKoC,OAAOkD,KAAKhG,GAAqBE,OAAOQ,OAAS,KAC3Fb,GAAoBK,MAAQ,GAC5BF,GAAqBE,MAAQ,CAAC,EAC9B2F,EAAUC,QAAQ,cAMtB,MAAM2B,EAAcP,EAAII,KAAKI,KAAKC,YAChC,IAAIC,EAAgB,EAEhB,GAAoB,aAApBD,EAAKxF,WACPyF,EAAgBD,EAAKnF,QAAU,OAAA,GACF,SAApBmF,EAAKxF,WAAuB,CAIrCyF,GADkB,OAAAvG,EAAYtB,GAAAG,MAAMuB,MAAMF,GAAuB,MAAfA,EAAIG,eAAiB,EAAAL,EAAAE,OAC1C,OAAAO,EAAY/B,GAAAG,MAAM,aAAIqB,MAAO,CAAA,MAE1DqG,EAAgBD,EAAKE,OAAS,EAGzB,MAAA,CACL5E,GAAI0E,EAAKzC,WACT4C,aAAcH,EAAKG,aACnBzH,KAAMsH,EAAKX,aACXA,aAAcW,EAAKX,aACnB9B,WAAYyC,EAAKzC,WACjB6C,eAAgBJ,EAAKI,eACrBvH,YAAamH,EAAKK,OAAS,GAC3BpF,SAAUgF,EACVnD,UAAWkD,EAAKM,SAAW,EAC3BtD,UAAWgD,EAAKO,SAAW,GAC3BtD,QAAS+C,EAAKQ,SAAW,GACzB/D,OAAuB,MAAfuD,EAAK1C,MAAgB,YAAc,UAC3CmD,KAA0B,YAApBT,EAAKxF,WAA2B,SAAW,UACjDA,WAAYwF,EAAKxF,WACjBK,OAAQmF,EAAKnF,OACbqF,MAAOF,EAAKE,OAAS,EACrBxD,kBAAmBsD,EAAKtD,kBACxBE,QAASoD,EAAKpD,SAAW,GAC3B,IAGF3E,GAAQM,MAAQuH,EAChBxH,GAAiBC,MAAQuH,CAAA,MAGzB7H,GAAQM,MAAQ,GAChBD,GAAiBC,MAAQ,GACf2F,EAAA7B,MAAMkD,EAAImB,KAAO,mBAEtBrE,GACCrD,QAAAqD,MAAM,WAAYA,GAC1B6B,EAAU7B,MAAM,aAChBpE,GAAQM,MAAQ,GAChBD,GAAiBC,MAAQ,EAAC,CAC1B,QACAT,GAAQS,OAAQ,CAAA,CAClB,CAIFsG,eAAe8B,aACb,IAAKhK,GAAMiK,eAAgD,IAA/BjK,GAAMiK,cAAc7H,OAIvC,OAHPC,QAAQC,IAAI,qBACZiF,EAAUC,QAAQ,oBAClB/F,GAAYG,MAAQ,GACbsI,QAAQC,OAAO,YAGxBhJ,GAAQS,OAAQ,EACZ,IACF,MAAMuG,EAAS,CACbC,MAAOhI,GAAUgI,MACjBC,MAAOjI,GAAUiI,MACjB4B,cAAejK,GAAMiK,cAAcG,KAAK,MAGpCxB,QAAYyB,EAAWC,eAAenC,GAGxC,GAFI9F,QAAAC,IAAI,YAAasG,GAER,IAAbA,EAAIG,KAAY,CAClB,IAAKH,EAAII,OAASC,MAAMC,QAAQN,EAAII,OAA6B,IAApBJ,EAAII,KAAK5G,OAI7C,OAHPC,QAAQkI,KAAK,qBACbhD,EAAUC,QAAQ,oBAClB/F,GAAYG,MAAQ,GACbsI,QAAQC,OAAO,UAIxB,MAAMK,EAAgB5B,EAAII,KAAKI,KAAKnG,GAEhB,YAAdA,EAAI0D,OAAqC,MAAd1D,EAAI0D,OAAmC,MAAlB1D,EAAIwH,YAAuC,IAAlBxH,EAAIwH,UACxE,IAAKxH,EAAK0D,MAAO,WAEnB1D,IAaT,OATAxB,GAAYG,MAAQ4I,EAAchI,MAAK,CAACC,EAAYC,IAC3C,IAAIC,KAAKF,EAAEG,SAASC,UAAY,IAAIF,KAAKD,EAAEE,SAASC,YAG7DR,QAAQC,IAAI,UAAW,CACrBoI,MAAOjJ,GAAYG,MAAMQ,OACzBuI,SAAU,OAAA5H,EAAAtB,GAAYG,MAAM,SAAI,EAAAmB,EAAAH,QAChCT,YAAa,OAAAqB,EAAA/B,GAAYG,MAAM,SAAI,EAAA4B,EAAAP,MAE9BiH,QAAQU,SAAQ,CAKhB,OAHCvI,QAAAqD,MAAM,YAAakD,EAAImB,KAC/BxC,EAAUC,QAAQ,oBAClB/F,GAAYG,MAAQ,GACbsI,QAAQC,OAAOvB,EAAImB,WAErBrE,GAIA,OAHCrD,QAAAqD,MAAM,YAAaA,GAC3B6B,EAAUC,QAAQ,oBAClB/F,GAAYG,MAAQ,GACbsI,QAAQC,OAAOzE,EAAK,CAC3B,QACAvE,GAAQS,OAAQ,CAAA,CAClB,CAIF,SAASiJ,GAAWC,GAClBtK,GAAasK,GAAMC,MAAK,IAAM7D,MAAc,CAGrC,SAAA8D,GAAcC,EAAO,GAC5BxK,GAAgBwK,GAAMF,MAAK,IAAM7D,MAAc,CAgExC,SAAAgE,GAA2BpG,EAAeC,GAC3C,MAAAjD,EAASkD,GAAeD,GAC9B,IAAKjD,EACI,OAAA,EAIH,MAAAmD,EAAUxD,GAAYG,MAAMuB,MAAMF,GAAQA,EAAI6B,QAAUA,IAC9D,OAAKG,EAKEC,GAAuBpD,EAAQmD,EAAQhC,KAJrC,CAIwC,CAGnD,SAAS+D,GAAkBlC,GACzB,OAAOrD,GAAYG,MAAMuB,MAAMF,GAAQA,EAAI6B,QAAUA,GAAK,CA7E5DqG,GACE,IAAMnL,GAAMgB,aACXoK,IACKA,GAEEpL,GAAMqL,iBACY9J,GAAAK,MAAQ,CAAC5B,GAAMqL,gBACnC7J,GAAaI,MAAQ,aAInB5B,GAAMsL,wBAA0B9G,OAAOkD,KAAK1H,GAAMsL,wBAAwBlJ,OAAS,GACrFZ,GAAaI,MAAQ,YAGrBtB,GAAWsB,MAAM2J,OAAS,EAGTvB,KACde,MAAK,KACS7D,KAAE6D,MAAK,KAElBrJ,GAAqBE,MAAQ,IAAK5B,GAAMsL,uBAAuB,GAChE,IAEFE,OAAM,KAEL9J,GAAqBE,MAAQ,CAAC,CAAA,MAI9B5B,GAAMqL,eACR7J,GAAaI,MAAQ,YAMvBF,GAAqBE,MAAQ,CAAC,EAG9BtB,GAAWsB,MAAM2J,OAAS,EAGTvB,KACde,MAAK,KAES7D,IAAA,IAEdsE,OAAM,KAELjK,GAAoBK,MAAQ,EAAC,MAInCP,GAAcO,MAAQ,EAAA,IA0B5BuJ,GACE,IAAM3J,GAAaI,QAClB6J,IACC,GAAgB,cAAZA,EAAyB,CAG3B,MAAMC,EAAetE,OAAOC,eAAesE,QAAQ,yBACnD,GAAID,EACE,IACI,MAAAE,EAAgB7D,KAAK8D,MAAMH,GAC7BzC,MAAMC,QAAQ0C,IAAkBA,EAAcxJ,OAAS,IACzDb,GAAoBK,MAAQgK,SAEvBE,GACCzJ,QAAAqD,MAAM,eAAgBoG,EAAC,CAEnC,MAAA,GACqB,cAAZL,EAAyB,CAGlC,MAAMM,EAAoB3E,OAAOC,eAAesE,QAAQ,0BACxD,GAAII,EACE,IACI,MAAAC,EAAqBjE,KAAK8D,MAAME,GACJ,iBAAvBC,GAAmCxH,OAAOkD,KAAKsE,GAAoB5J,OAAS,IACrFV,GAAqBE,MAAQoK,SAExBF,GACCzJ,QAAAqD,MAAM,eAAgBoG,EAAC,CAEnC,KAMA,MAAAG,GAAuBnL,GAAS,IAC7BW,GAAYG,MAAM8C,MAAMzB,GAAuB,MAAfA,EAAIG,wlFA5bpC,SAAwB2B,EAAkBE,SACjD,GAAKF,EAAL,CAyCW,IAAA,MAAAD,KAASpD,GAAqBE,MAEnCkD,IAAUG,EAAQH,OAKlBpD,GAAqBE,MAAMkD,KAAWC,IAE9BwC,EAAA,CACR2E,QAAS,KAAK5G,IAAgB,OAAAvC,EAAAiE,GAAkBlC,SAAlB,EAAA/B,EAA0BH,UAAW,cACnEkH,KAAM,UACNqC,SAAU,aAELzK,GAAqBE,MAAMkD,IAQlC,GAHiBpD,GAAAE,MAAMqD,EAAQH,OAASC,EAGjB,cAAvBvD,GAAaI,MAAuB,CAEtC,IAAIwK,EAAW,EACJ,IAAA,MAAAtH,KAASpD,GAAqBE,MAAO,CACxCmD,MAAAA,EAAWrD,GAAqBE,MAAMkD,GAC5C,IAAKC,EACH,SAGI,MAAAjD,EAASkD,GAAeD,GAC9B,IAAKjD,EACH,SAGImD,MAAAA,EAAUxD,GAAYG,MAAMuB,MAAMF,GAAQA,EAAI6B,QAAUA,IACzDG,IAIOmH,GAAAlH,GAAuBpD,EAAQmD,EAAQhC,KAAG,CAIlD/C,GAAA,UAAW,KAAM8D,OAAOC,WAAWmI,EAASjI,QAAQ,IAAK8D,MAAsB,EAAK,CAjD1F,MA/BI,UAJGvG,GAAqBE,MAAMqD,EAAQH,OAIf,cAAvBtD,GAAaI,MAAuB,CAEtC,IAAIwK,EAAW,EACJ,IAAA,MAAAtH,KAASpD,GAAqBE,MAAO,CACxCmD,MAAAA,EAAWrD,GAAqBE,MAAMkD,GAC5C,IAAKC,EACH,SAGI,MAAAjD,EAASkD,GAAeD,GAC9B,IAAKjD,EACH,SAGImD,MAAAA,EAAUxD,GAAYG,MAAMuB,MAAMF,GAAQA,EAAI6B,QAAUA,IACzDG,IAIOmH,GAAAlH,GAAuBpD,EAAQmD,EAAQhC,KAAG,CAIxD,GAAIuB,OAAOkD,KAAKhG,GAAqBE,OAAOQ,OAAS,EAInD,YAFMlC,GAAA,UAAW,KAAM8D,OAAOC,WAAWmI,EAASjI,QAAQ,IAAK8D,MAAsB,EAGvF,CAqDJ,8tFAlPF,SAAsBnG,GAChB,IAAC+D,GAAY/D,GACf,OAGI,MAAAuK,EAAQ9K,GAAoBK,MAAM0K,WAAW1G,GAAMA,EAAEjB,KAAO7C,EAAO6C,KAEvEpD,GAAoBK,MADlByK,GAAY,EACc,GAEA,CAACvK,EAC/B"}