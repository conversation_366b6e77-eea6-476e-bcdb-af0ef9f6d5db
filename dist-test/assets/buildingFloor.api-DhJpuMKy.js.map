{"version": 3, "file": "buildingFloor.api-DhJpuMKy.js", "sources": ["../../src/api/modules/pms/room/buildingFloor.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/build-floor'\r\n/**\r\n * 楼栋楼层相关api\r\n */\r\nexport default {\r\n  // 楼栋楼层列表\r\n  list: (data: any) => api.get(`${BASE_PATH}/list`, { params: data }),\r\n\r\n  /**\r\n   * 获取楼栋楼层树\r\n   * @param data\r\n   */\r\n  getBuildFloorTree: (data: any) => api.get(`${BASE_PATH}/tree`, { params: data }),\r\n\r\n  // 创建楼栋楼层\r\n  create: (data: any) => api.post(`${BASE_PATH}/create`, data),\r\n\r\n  // 删除楼栋楼层\r\n  delete: (data: any) => api.delete(`${BASE_PATH}/delete`, { params: data }),\r\n\r\n  // 修改楼栋楼层\r\n  update: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n}\r\n"], "names": ["BASE_PATH", "buildingFloorApi", "list", "data", "api", "get", "params", "getBuildFloorTree", "create", "post", "delete", "update", "put"], "mappings": "wCAEA,MAAMA,EAAY,4BAIHC,EAAA,CAEbC,KAAOC,GAAcC,EAAIC,IAAI,GAAGL,SAAkB,CAAEM,OAAQH,IAM5DI,kBAAoBJ,GAAcC,EAAIC,IAAI,GAAGL,SAAkB,CAAEM,OAAQH,IAGzEK,OAASL,GAAcC,EAAIK,KAAK,GAAGT,WAAoBG,GAGvDO,OAASP,GAAcC,EAAIM,OAAO,GAAGV,WAAoB,CAAEM,OAAQH,IAGnEQ,OAASR,GAAcC,EAAIQ,IAAI,GAAGZ,WAAoBG"}