{"version": 3, "file": "accSet.api-DGpfcw2k.js", "sources": ["../../src/api/modules/pms/accset/accSet.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/acc-set'\r\n/**\r\n * @description: 现付账套\r\n */\r\nexport default {\r\n  /**\r\n   * 获取账套列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getAccSetList: (data: {\r\n    gcode: string\r\n    isEnable?: string // 状态\r\n  }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获取账套详情\r\n   * @param accCode\r\n   * @returns\r\n   */\r\n  getAccSet: (accCode: string) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: { accCode },\r\n    }),\r\n\r\n  /**\r\n   * 创建现付账套\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createAccSet: (data: any) => api.post(`${BASE_PATH}/create`, data),\r\n\r\n  /**\r\n   * 修改现付账套\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateAccSet: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n\r\n  /**\r\n   * 修改现付账套状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateAccSetStatus: (data: { id: number, isEnable: string }) =>\r\n    api.put(`${BASE_PATH}/update-status`, data),\r\n\r\n  /**\r\n   * 修改门店现付账套状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateAccSetAccStatus: (data: { id: number, isEnable: string }) =>\r\n    api.put(`${BASE_PATH}/update-merchant-acc-status`, data),\r\n\r\n  /**\r\n   * 获得门店的所有的现付账套列表\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getAllMerchantAccSetList: (data: any) => api.get(`${BASE_PATH}/list/merchant/all`, { params: data }),\r\n\r\n  /**\r\n   * 获得门店的现付账套列表\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getMerchantAccSetList: (data: any) => api.get('/admin-api/pms/acc-set/list/merchant', { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "accSetApi", "getAccSetList", "data", "api", "get", "params", "getAccSet", "accCode", "createAccSet", "post", "updateAccSet", "put", "updateAccSetStatus", "updateAccSetAccStatus", "getAllMerchantAccSetList", "getMerchantAccSetList"], "mappings": "wCAEA,MAAMA,EAAY,wBAIHC,EAAA,CAMbC,cAAgBC,GAIdC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAQZI,UAAYC,GACVJ,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CAAEE,aAQdC,aAAeN,GAAcC,EAAIM,KAAK,GAAGV,WAAoBG,GAO7DQ,aAAeR,GAAcC,EAAIQ,IAAI,GAAGZ,WAAoBG,GAO5DU,mBAAqBV,GACnBC,EAAIQ,IAAI,GAAGZ,kBAA2BG,GAOxCW,sBAAwBX,GACtBC,EAAIQ,IAAI,GAAGZ,+BAAwCG,GAOrDY,yBAA2BZ,GAAcC,EAAIC,IAAI,GAAGL,sBAA+B,CAAEM,OAAQH,IAO7Fa,sBAAwBb,GAAcC,EAAIC,IAAI,uCAAwC,CAAEC,OAAQH"}