{"version": 3, "file": "moneyUtil-WV1a_Ods.js", "sources": ["../../src/utils/moneyUtil.ts"], "sourcesContent": ["/**\r\n * 格式化金额，默认保留两位小数，如：100,000.00\r\n * @param money\r\n */\r\nexport function formattedMoney(money: any) {\r\n  return new Intl.NumberFormat('en-US', {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2,\r\n  }).format(money)\r\n}\r\n"], "names": ["formattedMoney", "money", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format"], "mappings": "AAIO,SAASA,EAAeC,GACtB,OAAA,IAAIC,KAAKC,aAAa,QAAS,CACpCC,sBAAuB,EACvBC,sBAAuB,IACtBC,OAAOL,EACZ"}