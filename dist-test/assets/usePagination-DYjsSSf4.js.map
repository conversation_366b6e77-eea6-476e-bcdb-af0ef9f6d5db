{"version": 3, "file": "usePagination-DYjsSSf4.js", "sources": ["../../src/utils/composables/usePagination.ts"], "sourcesContent": ["export default function usePagination() {\r\n  const pagination = ref({\r\n    pageNo: 1,\r\n    pageSize: 10,\r\n    total: 0,\r\n    sizes: [10, 20, 50, 100],\r\n    layout: 'total, sizes, ->, prev, pager, next, jumper',\r\n    sort: null as string | null,\r\n    order: null as string | null,\r\n  })\r\n\r\n  function getParams() {\r\n    return {\r\n      pageNo: pagination.value.pageNo,\r\n      pageSize: pagination.value.pageSize,\r\n      from: (pagination.value.pageNo - 1) * pagination.value.pageSize,\r\n      // limit: pagination.value.pageSize,\r\n      ...(pagination.value.sort &&\r\n        pagination.value.order && {\r\n          sort: pagination.value.sort,\r\n          order: pagination.value.order,\r\n        }),\r\n    }\r\n  }\r\n\r\n  async function onSizeChange(size: number) {\r\n    pagination.value.pageSize = size\r\n  }\r\n\r\n  async function onCurrentChange(pageNo: number) {\r\n    pagination.value.pageNo = pageNo\r\n  }\r\n\r\n  async function onSortChange(prop: string, order: string) {\r\n    pagination.value.sort = prop\r\n    pagination.value.order = order === 'ascending' ? 'asc' : 'desc'\r\n  }\r\n\r\n  return {\r\n    pagination,\r\n    getParams,\r\n    onSizeChange,\r\n    onCurrentChange,\r\n    onSortChange,\r\n  }\r\n}\r\n"], "names": ["usePagination", "pagination", "ref", "pageNo", "pageSize", "total", "sizes", "layout", "sort", "order", "getParams", "value", "from", "onSizeChange", "async", "size", "onCurrentChange", "onSortChange", "prop"], "mappings": "wCAAA,SAAwBA,IACtB,MAAMC,EAAaC,EAAI,CACrBC,OAAQ,EACRC,SAAU,GACVC,MAAO,EACPC,MAAO,CAAC,GAAI,GAAI,GAAI,KACpBC,OAAQ,8CACRC,KAAM,KACNC,MAAO,OA8BF,MAAA,CACLR,aACAS,UA7BF,WACS,MAAA,CACLP,OAAQF,EAAWU,MAAMR,OACzBC,SAAUH,EAAWU,MAAMP,SAC3BQ,MAAOX,EAAWU,MAAMR,OAAS,GAAKF,EAAWU,MAAMP,YAEnDH,EAAWU,MAAMH,MACnBP,EAAWU,MAAMF,OAAS,CACxBD,KAAMP,EAAWU,MAAMH,KACvBC,MAAOR,EAAWU,MAAMF,OAE9B,EAmBAI,aAhBFC,eAA4BC,GAC1Bd,EAAWU,MAAMP,SAAWW,CAAA,EAgB5BC,gBAbFF,eAA+BX,GAC7BF,EAAWU,MAAMR,OAASA,CAAA,EAa1Bc,aAVaH,eAAaI,EAAcT,GACxCR,EAAWU,MAAMH,KAAOU,EACxBjB,EAAWU,MAAMF,MAAkB,cAAVA,EAAwB,MAAQ,MAAA,EAU7D"}