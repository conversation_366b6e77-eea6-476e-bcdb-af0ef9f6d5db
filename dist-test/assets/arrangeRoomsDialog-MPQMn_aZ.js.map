{"version": 3, "file": "arrangeRoomsDialog-MPQMn_aZ.js", "sources": ["../../src/views/room/components/arrangeRooms/arrangeRoomsDialog.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { DetailFormProps } from './types'\r\nimport ArrangeRooms from './arrangeRooms.vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n  } & DetailFormProps>(),\r\n  {\r\n    modelValue: false,\r\n  },\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [\r\n    value: boolean,\r\n  ]\r\n  'success': [\r\n    data: any,\r\n  ]\r\n}>()\r\n\r\nconst formRef = ref()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nfunction onSubmit() {\r\n  emits('success', formRef.value.data)\r\n  onCancel()\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" title=\"排房\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <ArrangeRooms ref=\"formRef\" v-bind=\"props\" />\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          确定\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "emits", "__emit", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "onSubmit", "value", "data", "onCancel"], "mappings": "yiCAIA,MAAMA,EAAQC,EASRC,EAAQC,EASRC,EAAUC,IAEVC,EAAYC,EAAS,CACzBC,IAAM,IACGR,EAAMS,WAEf,GAAAC,CAAIC,GACFT,EAAM,oBAAqBS,EAAG,IAIlC,SAASC,IACDV,EAAA,UAAWE,EAAQS,MAAMC,MACtBC,GAAA,CAGX,SAASA,IACPT,EAAUO,OAAQ,CAAA"}