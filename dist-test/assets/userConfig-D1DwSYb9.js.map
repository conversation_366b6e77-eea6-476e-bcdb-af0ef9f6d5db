{"version": 3, "file": "userConfig-D1DwSYb9.js", "sources": ["../../src/api/modules/pms/config/userParamConfig.api.ts", "../../src/views/room/realtime/components/userConfig.vue"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/user-param-config'\r\n/**\r\n * 用户参数设置\r\n */\r\nexport default {\r\n  /**\r\n   * 获得用户参数设置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getUserParamConfig: (data: {\r\n    gcode: string\r\n    username: string\r\n  }) =>\r\n    api.get(`${BASE_PATH}/get/panel`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 更新用户参数设置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateUserParamConfig: (data: any) =>\r\n    api.put(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 创建用户参数设置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createUserParamConfig: (data: any) =>\r\n    api.post(`${BASE_PATH}/create`, data, {}),\r\n}\r\n", "<i18n>\r\n{\r\n  \"en\": {\r\n    \"saveSuccess\": \"Save Successful\",\r\n    \"roomStatusCustomization\": \"Room Status Customization\",\r\n    \"roomArrangement\": \"Room Arrangement\",\r\n    \"byRoomNumber\": \"By Room Number\",\r\n    \"byFloor\": \"By Floor\",\r\n    \"byRoomType\": \"By Room Type\",\r\n    \"roomCellSize\": \"Room Cell Size\",\r\n    \"small\": \"Small\",\r\n    \"medium\": \"Medium\",\r\n    \"large\": \"Large\",\r\n    \"roomCellFontSize\": \"Room Cell Font Size\",\r\n    \"roomNumber\": \"Room Number\",\r\n    \"roomType\": \"Room Type\",\r\n    \"guest\": \"Guest\",\r\n    \"customizeRoomCellLabels\": \"Customize Room Cell Labels\",\r\n    \"guestSource\": \"Guest Source\",\r\n    \"walkin\": \"Walk-in\",\r\n    \"member\": \"Member\",\r\n    \"company\": \"Company\",\r\n    \"agent\": \"Agent\",\r\n    \"checkinType\": \"Check-in Type\",\r\n    \"allDay\": \"Full Day\",\r\n    \"hour\": \"Hourly\",\r\n    \"longStay\": \"Long Stay\",\r\n    \"selfUse\": \"Self Use\",\r\n    \"free\": \"Free\",\r\n    \"serviceType\": \"Service Type\",\r\n    \"awaken\": \"Wake-up\",\r\n    \"noDisturbing\": \"Do Not Disturb\",\r\n    \"secrecy\": \"Secrecy\",\r\n    \"birthday\": \"Birthday\",\r\n    \"financeType\": \"Finance Type\",\r\n    \"arrearage\": \"Arrearage\",\r\n    \"deposit\": \"Deposit\",\r\n    \"planCheckinOut\": \"Planned Check-in/Out in N Days\",\r\n    \"displayGuestName\": \"Display Guest Name on Room Cell\",\r\n    \"display\": \"Display\",\r\n    \"hide\": \"Hide\",\r\n    \"roomStateColorsDisplay\": \"Room State Colors Display\",\r\n    \"VCVacantClean\": \"VC\",\r\n    \"VDVacantDirty\": \"VD\",\r\n    \"OCOccupiedClean\": \"OC\",\r\n    \"ODOccupiedDirty\": \"OD\",\r\n    \"OORoomOutOfOrder\": \"OO\",\r\n    \"customizeFontColor\": \"Customize Font Color\",\r\n    \"close\": \"Close\",\r\n    \"save\": \"Save\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"saveSuccess\": \"保存成功\",\r\n    \"roomStatusCustomization\": \"房态个性化设置\",\r\n    \"roomArrangement\": \"房间排列方式\",\r\n    \"byRoomNumber\": \"按房号\",\r\n    \"byFloor\": \"按楼层\",\r\n    \"byRoomType\": \"按房型\",\r\n    \"roomCellSize\": \"房态格大小\",\r\n    \"small\": \"小\",\r\n    \"medium\": \"中\",\r\n    \"large\": \"大\",\r\n    \"roomCellFontSize\": \"房态格字体大小\",\r\n    \"roomNumber\": \"房号\",\r\n    \"roomType\": \"房型\",\r\n    \"guest\": \"住客\",\r\n    \"customizeRoomCellLabels\": \"房态格标签自定义\",\r\n    \"guestSource\": \"客源信息\",\r\n    \"walkin\": \"散客\",\r\n    \"member\": \"会员\",\r\n    \"company\": \"单位\",\r\n    \"agent\": \"中介(OTA名称)\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"allDay\": \"全天\",\r\n    \"hour\": \"钟点\",\r\n    \"longStay\": \"长包\",\r\n    \"selfUse\": \"自用\",\r\n    \"free\": \"免费\",\r\n    \"serviceType\": \"服务类型\",\r\n    \"awaken\": \"叫醒\",\r\n    \"noDisturbing\": \"免扰\",\r\n    \"secrecy\": \"保密\",\r\n    \"birthday\": \"生日\",\r\n    \"financeType\": \"财务类型\",\r\n    \"arrearage\": \"欠费\",\r\n    \"deposit\": \"押金\",\r\n    \"planCheckinOut\": \"N天预抵预离\",\r\n    \"displayGuestName\": \"房态格是否显示客人姓名\",\r\n    \"display\": \"显示\",\r\n    \"hide\": \"隐藏\",\r\n    \"roomStateColorsDisplay\": \"房态颜色展示\",\r\n    \"VCVacantClean\": \"VC空净\",\r\n    \"VDVacantDirty\": \"VD空脏\",\r\n    \"OCOccupiedClean\": \"OC住净\",\r\n    \"ODOccupiedDirty\": \"OD住脏\",\r\n    \"OORoomOutOfOrder\": \"OO维修\",\r\n    \"customizeFontColor\": \"房态字体颜色自定义\",\r\n    \"close\": \"关闭\",\r\n    \"save\": \"保存\"\r\n  },\r\n  \"km\": {\r\n    \"saveSuccess\": \"រក្សាទុកជោគជ័យ\",\r\n    \"roomStatusCustomization\": \"ការប្ដូរទម្រង់ស្ថានភាពបន្ទប់\",\r\n    \"roomArrangement\": \"របៀបរៀបចំបន្ទប់\",\r\n    \"byRoomNumber\": \"តាមលេខបន្ទប់\",\r\n    \"byFloor\": \"តាមជាន់\",\r\n    \"byRoomType\": \"តាមប្រភេទបន្ទប់\",\r\n    \"roomCellSize\": \"ទំហំក្រឡាស្ថានភាពបន្ទប់\",\r\n    \"small\": \"តូច\",\r\n    \"medium\": \"មធ្យម\",\r\n    \"large\": \"ធំ\",\r\n    \"roomCellFontSize\": \"ទំហំអក្សរក្រឡាស្ថានភាពបន្ទប់\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"guest\": \"ភ្ញៀវ\",\r\n    \"customizeRoomCellLabels\": \"ការប្ដូរទម្រង់ស្លាកក្រឡាស្ថានភាពបន្ទប់\",\r\n    \"guestSource\": \"ប្រភពភ្ញៀវ\",\r\n    \"walkin\": \"ភ្ញៀវដែលមកដោយខ្លួនឯង\",\r\n    \"member\": \"សមាជិក\",\r\n    \"company\": \"ក្រុមហ៊ុន\",\r\n    \"agent\": \"ភ្នាក់ងារ\",\r\n    \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n    \"allDay\": \"ពេញមួយថ្ងៃ\",\r\n    \"hour\": \"ម៉ោង\",\r\n    \"longStay\": \"ស្នាក់នៅយូរ\",\r\n    \"selfUse\": \"ប្រើប្រាស់ខ្លួនឯង\",\r\n    \"free\": \"ឥតគិតថ្លៃ\",\r\n    \"serviceType\": \"ប្រភេទសេវាកម្ម\",\r\n    \"awaken\": \"ដាស់\",\r\n    \"noDisturbing\": \"កុំរំខាន\",\r\n    \"secrecy\": \"ភាពសម្ងាត់\",\r\n    \"birthday\": \"ថ្ងៃកំណើត\",\r\n    \"financeType\": \"ប្រភេទហិរញ្ញវត្ថុ\",\r\n    \"arrearage\": \"បំណុល\",\r\n    \"deposit\": \"ប្រាក់កក់\",\r\n    \"planCheckinOut\": \"គ្រោងចូល/ចេញក្នុង N ថ្ងៃ\",\r\n    \"displayGuestName\": \"បង្ហាញឈ្មោះភ្ញៀវនៅលើក្រឡាស្ថានភាពបន្ទប់\",\r\n    \"display\": \"បង្ហាញ\",\r\n    \"hide\": \"លាក់\",\r\n    \"roomStateColorsDisplay\": \"ការបង្ហាញពណ៌ស្ថានភាពបន្ទប់\",\r\n    \"VCVacantClean\": \"VC\",\r\n    \"VDVacantDirty\": \"VD\",\r\n    \"OCOccupiedClean\": \"OC\",\r\n    \"ODOccupiedDirty\": \"OD\",\r\n    \"OORoomOutOfOrder\": \"OO\",\r\n    \"customizeFontColor\": \"ការប្ដូរទម្រង់ពណ៌អក្សរ\",\r\n    \"close\": \"បិទ\",\r\n    \"save\": \"រក្សាទុក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { UserConfigModel } from '@/models/index'\r\nimport { userParamConfigApi } from '@/api/modules/index'\r\nimport { ParamConfigTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user.ts'\r\n\r\n/**\r\n * 房态个性化设置\r\n */\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    userConfig: UserConfigModel\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [value: UserConfigModel]\r\n  change: [value: UserConfigModel]\r\n}>()\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst userConfig = ref()\r\nconst predefineColors = ref(['#ff4500', '#ff8c00', '#ffd700', '#90ee90', '#00ced1', '#1e90ff', '#c71585', '#ffffff'])\r\nconst formRef = ref()\r\n\r\nonMounted(() => {\r\n  userConfig.value = props.userConfig\r\n  userConfig.value.gcode = userStore.gcode\r\n  userConfig.value.username = userStore.username\r\n  userConfig.value.paramType = ParamConfigTypeEnum.USER_ROOM_PANEL\r\n})\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nfunction onSubmit() {\r\n  return new Promise<void>((resolve) => {\r\n    formRef.value &&\r\n      formRef.value.validate((valid: any) => {\r\n        if (valid) {\r\n          userParamConfigApi.updateUserParamConfig(userConfig.value).then((res: any) => {\r\n            if (res.code === 0) {\r\n              emits('success', userConfig.value)\r\n              ElMessage({\r\n                message: t('saveSuccess'),\r\n                type: 'success',\r\n              })\r\n            }\r\n            resolve()\r\n          })\r\n        }\r\n      })\r\n  })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-drawer v-model=\"myVisible\" :z-index=\"2000\" :title=\"t('roomStatusCustomization')\" size=\"650\" :close-on-click-modal=\"true\" destroy-on-close :modal=\"false\">\r\n      <el-form ref=\"formRef\" :model=\"userConfig\" label-position=\"top\" label-width=\"150px\" label-suffix=\"：\">\r\n        <el-form-item :label=\"t('roomArrangement')\">\r\n          <el-radio-group v-model=\"userConfig.roomStateConfig.sortMode\">\r\n            <el-radio value=\"0\">\r\n              {{ t('byRoomNumber') }}\r\n            </el-radio>\r\n            <el-radio value=\"1\">\r\n              {{ t('byFloor') }}\r\n            </el-radio>\r\n            <el-radio value=\"2\">\r\n              {{ t('byRoomType') }}\r\n            </el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('roomCellSize')\">\r\n          <el-radio-group v-model=\"userConfig.roomStateConfig.cellSize\">\r\n            <el-radio value=\"small\">\r\n              {{ t('small') }}\r\n            </el-radio>\r\n            <el-radio value=\"medium\">\r\n              {{ t('medium') }}\r\n            </el-radio>\r\n            <el-radio value=\"large\">\r\n              {{ t('large') }}\r\n            </el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('roomCellFontSize')\">\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              {{ t('roomNumber') }}\r\n              <el-radio-group v-model=\"userConfig.roomStateConfig.cellFontSize.rNo\">\r\n                <el-radio value=\"14px\">\r\n                  <span style=\"font-size: 14px\">14px</span>\r\n                </el-radio>\r\n                <el-radio value=\"16px\">\r\n                  <span style=\"font-size: 16px\">16px</span>\r\n                </el-radio>\r\n                <el-radio value=\"18px\">\r\n                  <span style=\"font-size: 18px\">18px</span>\r\n                </el-radio>\r\n                <el-radio value=\"20px\">\r\n                  <span style=\"font-size: 20px\">20px</span>\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              {{ t('roomType') }}\r\n              <el-radio-group v-model=\"userConfig.roomStateConfig.cellFontSize.rtName\">\r\n                <el-radio value=\"14px\">\r\n                  <span style=\"font-size: 14px\">14px</span>\r\n                </el-radio>\r\n                <el-radio value=\"16px\">\r\n                  <span style=\"font-size: 16px\">16px</span>\r\n                </el-radio>\r\n                <el-radio value=\"18px\">\r\n                  <span style=\"font-size: 18px\">18px</span>\r\n                </el-radio>\r\n                <el-radio value=\"20px\">\r\n                  <span style=\"font-size: 20px\">20px</span>\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              {{ t('guest') }}\r\n              <el-radio-group v-model=\"userConfig.roomStateConfig.cellFontSize.guestName\">\r\n                <el-radio value=\"14px\">\r\n                  <span style=\"font-size: 14px\">14px</span>\r\n                </el-radio>\r\n                <el-radio value=\"16px\">\r\n                  <span style=\"font-size: 16px\">16px</span>\r\n                </el-radio>\r\n                <el-radio value=\"18px\">\r\n                  <span style=\"font-size: 18px\">18px</span>\r\n                </el-radio>\r\n                <el-radio value=\"20px\">\r\n                  <span style=\"font-size: 20px\">20px</span>\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('customizeRoomCellLabels')\">\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              {{ t('guestSource') }}：\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.guestSrcType.walkin\" true-value=\"1\" false-value=\"0\" :label=\"t('walkin')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.guestSrcType.member\" true-value=\"1\" false-value=\"0\" :label=\"t('member')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.guestSrcType.protocol\" true-value=\"1\" false-value=\"0\" :label=\"t('company')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.guestSrcType.agent\" true-value=\"1\" false-value=\"0\" :label=\"t('agent')\" />\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              {{ t('checkinType') }}：\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.checkinType.allDay\" true-value=\"1\" false-value=\"0\" :label=\"t('allDay')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.checkinType.hour\" true-value=\"1\" false-value=\"0\" :label=\"t('hour')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.checkinType.longStay\" true-value=\"1\" false-value=\"0\" :label=\"t('longStay')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.checkinType.selfUse\" true-value=\"1\" false-value=\"0\" :label=\"t('selfUse')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.checkinType.free\" true-value=\"1\" false-value=\"0\" :label=\"t('free')\" />\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              {{ t('serviceType') }}：\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.serverType.awaken\" true-value=\"1\" false-value=\"0\" :label=\"t('awaken')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.serverType.noDisturbing\" true-value=\"1\" false-value=\"0\" :label=\"t('noDisturbing')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.serverType.secrecy\" true-value=\"1\" false-value=\"0\" :label=\"t('secrecy')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.serverType.birthday\" true-value=\"1\" false-value=\"0\" :label=\"t('birthday')\" />\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"display: contents\">\r\n            <el-col :span=\"24\">\r\n              {{ t('financeType') }}：\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.financeType.arrearage\" true-value=\"1\" false-value=\"0\" :label=\"t('arrearage')\" />\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.financeType.deposit\" true-value=\"1\" false-value=\"0\" :label=\"t('deposit')\" />\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"display: contents\">\r\n            <el-col :span=\"24\">\r\n              {{ t('planCheckinOut') }}：\r\n              <el-checkbox v-model=\"userConfig.roomStateConfig.planCheckinOut\" true-value=\"1\" false-value=\"0\" :label=\"t('planCheckinOut')\" />\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"24\">\r\n              {{ t('displayGuestName') }}：\r\n              <el-switch v-model=\"userConfig.roomStateConfig.cellGuestName\" :active-text=\"t('display')\" :inactive-text=\"t('hide')\" inline-prompt active-value=\"1\" inactive-value=\"0\" />\r\n            </el-col>\r\n          </el-row>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('roomStateColorsDisplay')\">\r\n          <el-row>\r\n            <el-col :span=\"24\" style=\"display: flex; justify-content: space-between; gap: 10px; align-items: center\">\r\n              <div style=\"text-align: center\">\r\n                <span :style=\"{ backgroundColor: userConfig.roomStateConfig.colors.VC, display: 'inline-block', width: '24px', height: '24px', borderRadius: '4px', border: '1px solid #ddd' }\" />\r\n                <div>{{ t('VCVacantClean') }}</div>\r\n              </div>\r\n              <div style=\"text-align: center\">\r\n                <span :style=\"{ backgroundColor: userConfig.roomStateConfig.colors.VD, display: 'inline-block', width: '24px', height: '24px', borderRadius: '4px', border: '1px solid #ddd' }\" />\r\n                <div>{{ t('VDVacantDirty') }}</div>\r\n              </div>\r\n              <div style=\"text-align: center\">\r\n                <span :style=\"{ backgroundColor: userConfig.roomStateConfig.colors.OC, display: 'inline-block', width: '24px', height: '24px', borderRadius: '4px', border: '1px solid #ddd' }\" />\r\n                <div>{{ t('OCOccupiedClean') }}</div>\r\n              </div>\r\n              <div style=\"text-align: center\">\r\n                <span :style=\"{ backgroundColor: userConfig.roomStateConfig.colors.OD, display: 'inline-block', width: '24px', height: '24px', borderRadius: '4px', border: '1px solid #ddd' }\" />\r\n                <div>{{ t('ODOccupiedDirty') }}</div>\r\n              </div>\r\n              <div style=\"text-align: center\">\r\n                <span :style=\"{ backgroundColor: userConfig.roomStateConfig.colors.OO, display: 'inline-block', width: '24px', height: '24px', borderRadius: '4px', border: '1px solid #ddd' }\" />\r\n                <div>{{ t('OORoomOutOfOrder') }}</div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('customizeFontColor')\">\r\n          <el-color-picker v-model=\"userConfig.roomStateConfig.fontColor\" :predefine=\"predefineColors\" color-format=\"hex\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel\">\r\n          {{ t('close') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          {{ t('save') }}\r\n        </el-button>\r\n      </template>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// :deep(.el-radio) {\r\n//   width: 0 !important;\r\n//   margin-right: 16px !important;\r\n// }\r\n\r\n// :deep(.el-checkbox) {\r\n//   width: 0 !important;\r\n//   margin-right: 16px !important;\r\n// }\r\n</style>\r\n"], "names": ["BASE_PATH", "userParamConfigApi", "getUserParamConfig", "data", "api", "get", "params", "updateUserParamConfig", "put", "createUserParamConfig", "post", "props", "__props", "emits", "__emit", "userStore", "useUserStore", "t", "useI18n", "userConfig", "ref", "predefineColors", "formRef", "onMounted", "value", "gcode", "username", "paramType", "ParamConfigTypeEnum", "USER_ROOM_PANEL", "myVisible", "computed", "modelValue", "set", "val", "onSubmit", "Promise", "resolve", "validate", "valid", "then", "res", "code", "ElMessage", "message", "type", "onCancel"], "mappings": "8wBAEA,MAAMA,EAAY,kCAIHC,EAAA,CAMbC,mBAAqBC,GAInBC,EAAIC,IAAI,GAAGL,cAAuB,CAChCM,OAAQH,IAQZI,sBAAwBJ,GACtBC,EAAII,IAAI,GAAGR,WAAoBG,EAAM,IAOvCM,sBAAwBN,GACtBC,EAAIM,KAAK,GAAGV,WAAoBG,EAAM,CAAE,mUC+H5C,MAAMQ,EAAQC,EASRC,EAAQC,EAKRC,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAaC,IACbC,EAAkBD,EAAI,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,YACpGE,EAAUF,IAEhBG,GAAU,KACRJ,EAAWK,MAAQb,EAAMQ,WACdA,EAAAK,MAAMC,MAAQV,EAAUU,MACxBN,EAAAK,MAAME,SAAWX,EAAUW,SAC3BP,EAAAK,MAAMG,UAAYC,EAAoBC,eAAA,IAGnD,MAAMC,EAAYC,EAAS,CACzB1B,IAAM,IACGM,EAAMqB,WAEf,GAAAC,CAAIC,GACFrB,EAAM,oBAAqBqB,EAAG,IAIlC,SAASC,IACA,OAAA,IAAIC,SAAeC,IACxBf,EAAQE,OACNF,EAAQE,MAAMc,UAAUC,IAClBA,GACFtC,EAAmBM,sBAAsBY,EAAWK,OAAOgB,MAAMC,IAC9C,IAAbA,EAAIC,OACA7B,EAAA,UAAWM,EAAWK,OAClBmB,EAAA,CACRC,QAAS3B,EAAE,eACX4B,KAAM,aAGFR,GAAA,GACT,GAEJ,GACJ,CAGH,SAASS,IACPhB,EAAUN,OAAQ,CAAA"}