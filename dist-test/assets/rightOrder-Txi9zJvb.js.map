{"version": 3, "file": "rightOrder-Txi9zJvb.js", "sources": ["../../src/views/room/realtime/components/rightOrder.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"orderAssistant\": \"Order Assistant\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"orderAssistant\": \"订单助手\"\r\n  },\r\n  \"km\": {\r\n    \"orderAssistant\": \"អ្នកជំនួយការកម្មង់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport roomOrderListVue from './rightOrderList.vue'\r\n\r\ndefineOptions({\r\n  name: 'RoomOrder', // 右侧ota订单\r\n})\r\nconst { t } = useI18n()\r\n/** 订单助手tabs */\r\nconst activeName = ref('1')\r\n\r\n/** tabs点击切换前进行判断 */\r\nasync function beforeHandleTabsClick(index: any) {\r\n  activeName.value = index\r\n  return true\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"roomOrder\">\r\n    <el-tabs v-model=\"activeName\" :before-leave=\"beforeHandleTabsClick\">\r\n      <el-tab-pane :label=\"t('orderAssistant')\" name=\"1\">\r\n        <roomOrderListVue />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.roomOrder {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  :deep(.el-tabs__content) {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n\r\n  :deep(.el-tab-pane) {\r\n    height: 100%;\r\n  }\r\n\r\n  :deep(.el-radio) {\r\n    @apply mr-[10px] px-[7px];\r\n    .el-radio__input {\r\n      display: none;\r\n    }\r\n    .el-radio__label {\r\n      padding-left: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "activeName", "ref", "async", "beforeHandleTabsClick", "index", "value"], "mappings": "k5JAoBM,MAAAA,EAAEA,GAAMC,IAERC,EAAaC,EAAI,KAGvBC,eAAeC,EAAsBC,GAE5B,OADPJ,EAAWK,MAAQD,GACZ,CAAA"}