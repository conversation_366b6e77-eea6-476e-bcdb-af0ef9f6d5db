{"version": 3, "file": "coupon.api-aMKZ7FC4.js", "sources": ["../../src/api/modules/marketing/coupon/coupon.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n/**\r\n * @description: 优惠券\r\n */\r\nexport default {\r\n  /**\r\n   * @description: 优惠券列表\r\n   */\r\n  // list: (data: {\r\n  //   gcode: string;\r\n  //   hcode?: string;\r\n  //   couponType?: string; // 优惠券类型\r\n  //   dateStart?: string; // 发放日期开始\r\n  //   dateEnd?: string; // 发放日期结束\r\n  //   templateCode?: string; //模板代码\r\n  //   phone?: string;\r\n  //   name?: string;\r\n  //   from: number;\r\n  //   limit: number;\r\n  // }) => api.post(\"marketing/coupon/list\", data, {}),\r\n  list: (data: any) => api.get('/admin-api/marketing/coupon/page', { params: data }),\r\n  /**\r\n   * 优惠券明细\r\n   * @param gcode\r\n   * @param couponCode 优惠券代码\r\n   * @returns\r\n   */\r\n  // detail: (gcode: string, couponCode: string) =>\r\n  //   api.get(\"marketing/coupon/detail\", {\r\n  //     params: {\r\n  //       gcode,\r\n  //       couponCode,\r\n  //     },\r\n  //   }),\r\n  detail: (data: any) => api.get('/admin-api/marketing/coupon-template/get', { params: data }),\r\n  /**\r\n   * 创建优惠券\r\n   * @param data\r\n   * @returns\r\n   */\r\n  create: (data: any) => api.post('marketing/coupon/create', data, {}),\r\n\r\n\t/**\r\n\t *  获得会员优惠券列表\r\n\t * @param data\r\n\t */\r\n\tmemberList: (data: any) => api.get('/admin-api/marketing/coupon/list/coupon', { params: data }),\r\n}\r\n"], "names": ["couponApi", "list", "data", "api", "get", "params", "detail", "create", "post", "memberList"], "mappings": "mCAIA,MAAeA,EAAA,CAgBbC,KAAOC,GAAcC,EAAIC,IAAI,mCAAoC,CAAEC,OAAQH,IAc3EI,OAASJ,GAAcC,EAAIC,IAAI,2CAA4C,CAAEC,OAAQH,IAMrFK,OAASL,GAAcC,EAAIK,KAAK,0BAA2BN,EAAM,IAMlEO,WAAaP,GAAcC,EAAIC,IAAI,0CAA2C,CAAEC,OAAQH"}