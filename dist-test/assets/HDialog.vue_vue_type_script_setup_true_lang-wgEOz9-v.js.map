{"version": 3, "file": "HDialog.vue_vue_type_script_setup_true_lang-wgEOz9-v.js", "sources": ["../../src/layouts/ui-kit/HDialog.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport { Dialog, DialogDescription, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'\r\n\r\nwithDefaults(\r\n  defineProps<{\r\n    appear?: boolean\r\n    title?: string\r\n    preventClose?: boolean\r\n    overlay?: boolean\r\n  }>(),\r\n  {\r\n    appear: false,\r\n    preventClose: false,\r\n    overlay: false,\r\n  },\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  close: []\r\n}>()\r\n\r\nconst isOpen = defineModel<boolean>({\r\n  default: false,\r\n})\r\n\r\nconst slots = useSlots()\r\n\r\nconst overlayTransitionClass = ref({\r\n  enter: 'ease-in-out duration-500',\r\n  enterFrom: 'opacity-0',\r\n  enterTo: 'opacity-100',\r\n  leave: 'ease-in-out duration-500',\r\n  leaveFrom: 'opacity-100',\r\n  leaveTo: 'opacity-0',\r\n})\r\n\r\nconst transitionClass = computed(() => {\r\n  return {\r\n    enter: 'ease-out duration-300',\r\n    enterFrom: 'opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95',\r\n    enterTo: 'opacity-100 translate-y-0 lg-scale-100',\r\n    leave: 'ease-in duration-200',\r\n    leaveFrom: 'opacity-100 translate-y-0 lg-scale-100',\r\n    leaveTo: 'opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95',\r\n  }\r\n})\r\n\r\nfunction close() {\r\n  isOpen.value = false\r\n  emits('close')\r\n}\r\n</script>\r\n\r\n<template>\r\n  <TransitionRoot as=\"template\" :appear=\"appear\" :show=\"isOpen\">\r\n    <Dialog class=\"fixed inset-0 z-2000 flex\" @close=\"!preventClose && close()\">\r\n      <TransitionChild as=\"template\" :appear=\"appear\" v-bind=\"overlayTransitionClass\">\r\n        <div class=\"fixed inset-0 bg-stone-2/75 transition-opacity dark-bg-stone-8/75\" :class=\"{ 'backdrop-blur-sm': overlay }\" />\r\n      </TransitionChild>\r\n      <div class=\"fixed inset-0 overflow-y-auto\">\r\n        <div class=\"min-h-full flex items-end justify-center p-4 text-center lg-items-center\">\r\n          <TransitionChild as=\"template\" :appear=\"appear\" v-bind=\"transitionClass\">\r\n            <DialogPanel class=\"relative w-full flex flex-col overflow-hidden rounded-xl bg-white text-left shadow-xl lg-my-8 lg-max-w-lg dark-bg-stone-8\">\r\n              <div flex=\"~ items-center justify-between\" px-4 py-3 border-b=\"~ solid stone/15\" text-6>\r\n                <DialogTitle m-0 text-lg text-dark dark-text-white>\r\n                  {{ title }}\r\n                </DialogTitle>\r\n                <SvgIcon name=\"i-carbon:close\" cursor-pointer @click=\"close\" />\r\n              </div>\r\n              <DialogDescription m-0 overflow-y-auto p-4 text-start>\r\n                <slot />\r\n              </DialogDescription>\r\n              <div v-if=\"!!slots.footer\" flex=\"~ items-center justify-end\" px-4 py-3 border-t=\"~ solid stone/15\">\r\n                <slot name=\"footer\" />\r\n              </div>\r\n            </DialogPanel>\r\n          </TransitionChild>\r\n        </div>\r\n      </div>\r\n    </Dialog>\r\n  </TransitionRoot>\r\n</template>\r\n"], "names": ["emits", "__emit", "isOpen", "_useModel", "__props", "slots", "useSlots", "overlayTransitionClass", "ref", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "transitionClass", "computed", "close", "value"], "mappings": "szBAiBA,MAAMA,EAAQC,EAIRC,EAASC,EAAoBC,EAAA,cAI7BC,EAAQC,IAERC,EAAyBC,EAAI,CACjCC,MAAO,2BACPC,UAAW,YACXC,QAAS,cACTC,MAAO,2BACPC,UAAW,cACXC,QAAS,cAGLC,EAAkBC,GAAS,KACxB,CACLP,MAAO,wBACPC,UAAW,uDACXC,QAAS,yCACTC,MAAO,uBACPC,UAAW,yCACXC,QAAS,2DAIb,SAASG,IACPf,EAAOgB,OAAQ,EACflB,EAAM,QAAO"}