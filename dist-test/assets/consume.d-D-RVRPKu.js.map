{"version": 3, "file": "consume.d-D-RVRPKu.js", "sources": ["../../src/views/room/components/entryAccount/consume.d.ts"], "sourcesContent": ["/** 产品分类类型集合 */\r\ndeclare namespace consume {\r\n  /** 产品编号 */\r\n  interface idTypes {\r\n    /** 产品编号 */\r\n    id?: number\r\n  }\r\n  /** 商品列表类型 */\r\n  interface ErpProductRespVO extends hgCode, idTypes {\r\n    /** 产品条码 */\r\n    barCode?: string\r\n    /** 产品分类 */\r\n    categoryName?: string\r\n    /** 创建时间 */\r\n    createTime?: string\r\n    /** 保质期天数 */\r\n    expiryDay?: number\r\n    /** 产品名称 */\r\n    goodsName?: string\r\n    /** 最低价格，单位：分 */\r\n    minPrice?: number\r\n    /** 数量 */\r\n    num?: number\r\n    /** 库存 */\r\n    stock?: number\r\n    /** 是否销售 */\r\n    pay?: number\r\n    /** 销售价格，单位：分 */\r\n    price?: number\r\n    /** 销售价格，单位：分 */\r\n    sumPrice?: number\r\n    /** 采购价格，单位：分 */\r\n    purchasePrice?: number\r\n    /** 产品备注 */\r\n    remark?: string\r\n    /** 产品规格 */\r\n    standard?: string\r\n    /** 产品状态 */\r\n    status?: number\r\n    /** 产品分类编号 */\r\n    thingCode?: number\r\n    /** 单位编号 */\r\n    unitId?: number\r\n    /** 单位 */\r\n    unitName?: string\r\n    /** 基础重量（kg） */\r\n    weight?: number\r\n  }\r\n  /** 商品列表搜索类型 */\r\n  interface formTypes extends PageQuery, hgCode {\r\n    /** 分类id */\r\n    categoryId: number\r\n    /** 物品名称 */\r\n    name: string\r\n  }\r\n  interface AccountDetail {\r\n    arSetCode: string\r\n    arSetName: string\r\n    creditAccType: string\r\n    availableAmount: number | null | undefined\r\n    creditPayDays: string\r\n    creditPayFix: number | null | undefined\r\n  }\r\n  /**\r\n   * 获根据手机号获取会员详情\r\n   */\r\n  interface MemberDetail {\r\n    mtName: string\r\n    phone: string\r\n    storeCardBalance: number\r\n  }\r\n}\r\nexport = consume\r\n"], "names": ["require_consume_d_042", "exports", "module", "consume"], "mappings": "qCAwEAA,KAAA,CAAA,+BAAAC,EAAAC,GAAAA,EAASD,QAAAE,OAAA"}