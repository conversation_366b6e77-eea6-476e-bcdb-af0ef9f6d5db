import{d as e,W as t,o as s,c as o,u as a,f as r,R as l,_ as i}from"./index-CkEhI1Zk.js";import{_ as n}from"./tools.vue_vue_type_script_setup_true_lang-CiXRxFW5.js";const p={class:"flex items-center"},c=e({name:"ToolbarLeftSide",__name:"leftSide",setup(e){const c=t();return(e,t)=>{const d=i;return s(),o("div",p,["mobile"===a(c).mode?(s(),o("div",{key:0,class:"flex-center cursor-pointer px-2 py-1 -rotate-z-180",onClick:t[0]||(t[0]=e=>a(c).toggleSidebarCollapse())},[r(d,{name:"toolbar-collapse",rotate:"rtl"===a(c).settings.app.direction?180:0},null,8,["rotate"])])):l("",!0),r(n,{mode:"left-side"})])}}});export{c as _};
//# sourceMappingURL=leftSide.vue_vue_type_script_setup_true_lang-PikQfkj2.js.map
