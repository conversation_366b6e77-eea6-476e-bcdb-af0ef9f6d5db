import{r as e,I as a}from"./item-4L_tAnSD.js";import{_ as l}from"./sub.vue_vue_type_script_setup_true_lang-mgzREf0u.js";import{d as n,b as u,B as i,D as t,af as s,r as o,o as r,c as d,F as c,ag as v,a7 as p,u as h,ah as f,e as m,R as y}from"./index-CkEhI1Zk.js";const x=n({name:"MainMenu",__name:"index",props:{menu:{},value:{},accordion:{type:Boolean,default:!0},defaultOpeneds:{default:()=>[]},alwaysOpeneds:{default:()=>[]},mode:{default:"vertical"},collapse:{type:Boolean,default:!1},showCollapseName:{type:Boolean,default:!1},rounded:{type:Boolean,default:!1},direction:{default:"ltr"}},setup(n){const x=n,k=u(x.value),O=u({}),M=u({}),w=u(Array.from(new Set([...x.alwaysOpeneds.slice(0),...x.defaultOpeneds.slice(0)]))),_=u(x.alwaysOpeneds.slice(0)),g=u([]),P=i((()=>"horizontal"===x.mode||"vertical"===x.mode&&x.collapse));function S(e,a=[]){e.forEach((e=>{const l=e.path??JSON.stringify(e);if(e.children){const n=[...a,l];M.value[l]={index:l,indexPath:n,active:!1},S(e.children,n)}else O.value[l]={index:l,indexPath:a}}))}const N=(e,a)=>{w.value.includes(e)||(x.accordion&&(w.value=w.value.filter((e=>a.includes(e)||_.value.includes(e)))),w.value.push(e))},b=e=>{Array.isArray(e)?f((()=>{b(e.at(-1)),e.length>1&&b(e.slice(0,-1))})):Object.keys(M.value).forEach((a=>{M.value[a].indexPath.includes(e)&&(w.value=w.value.filter((a=>a!==e)))}))};function B(e){var a,l;for(const n in M.value)M.value[n].active=!1;null==(a=M.value[e])||a.indexPath.forEach((e=>{M.value[e].active=!0})),null==(l=O.value[e])||l.indexPath.forEach((e=>{M.value[e].active=!0}))}const C=e=>{("horizontal"===x.mode||x.collapse)&&(w.value=[]),B(e)};function E(){const e=k.value&&O.value[k.value];B(k.value),!e||P.value||x.collapse||e.indexPath.forEach((e=>{const a=M.value[e];a&&N(e,a.indexPath)}))}return t((()=>x.menu),(e=>{S(e),E()}),{deep:!0,immediate:!0}),t((()=>x.value),(e=>{O.value[e]||(k.value="");const a=O.value[e]||k.value&&O.value[k.value]||O.value[x.value];k.value=a?a.index:e,E()})),t((()=>x.collapse),(e=>{w.value=e?[]:x.alwaysOpeneds.slice(0),E()})),s(e,o({props:x,items:O,subMenus:M,activeIndex:k,openedMenus:w,alwaysOpenedsMenus:_,mouseInMenu:g,isMenuPopup:P,openMenu:N,closeMenu:b,handleMenuItemClick:C,handleSubMenuClick:(e,a)=>{w.value.includes(e)?b(e):N(e,a)}})),(e,n)=>(r(),d("div",{class:p(["h-full w-full flex flex-col of-hidden transition-all",{"flex-row! w-auto!":h(P)&&"horizontal"===x.mode,"py-1":"vertical"===x.mode}])},[(r(!0),d(c,null,v(e.menu,(e=>{var n,u,i;return r(),d(c,{key:e.path??JSON.stringify(e)},[!1!==(null==(n=e.meta)?void 0:n.menu)?(r(),d(c,{key:0},[(null==(u=e.children)?void 0:u.length)?(r(),m(l,{key:0,menu:e,"unique-key":[e.path??(e.children.every((e=>{var a;return!1===(null==(a=e.meta)?void 0:a.menu)}))?e.children[0].path:JSON.stringify(e))]},null,8,["menu","unique-key"])):(r(),m(a,{key:1,item:e,"unique-key":[e.path??((null==(i=e.children)?void 0:i.every((e=>{var a;return!1===(null==(a=e.meta)?void 0:a.menu)})))?e.children[0].path:JSON.stringify(e))],onClick:a=>{var l;return C(e.path??((null==(l=e.children)?void 0:l.every((e=>{var a;return!1===(null==(a=e.meta)?void 0:a.menu)})))?e.children[0].path:JSON.stringify(e)))}},null,8,["item","unique-key","onClick"]))],64)):y("",!0)],64)})),128))],2))}});export{x as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-BPhPOOVj.js.map
