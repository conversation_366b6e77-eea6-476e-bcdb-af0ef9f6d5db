{"version": 3, "file": "createroom-BXveq4lJ.js", "sources": ["../../src/views/merchant/base/room/components/DetailForm/createroom.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"floor\": \"Floor\",\r\n    \"roomType\": \"Room Type\",\r\n    \"roomNo\": \"Room No\",\r\n    \"extensionNo\": \"Extension No\",\r\n    \"bedCount\": \"Bed Count\",\r\n    \"isEnabled\": \"Is Enabled\",\r\n    \"roomFeatures\": \"Features\",\r\n    \"roomDescription\": \"Remark\",\r\n    \"pleaseSelectFloor\": \"Please select floor\",\r\n    \"pleaseSelectRoomType\": \"Please select room type\",\r\n    \"pleaseEnterRoomNo\": \"Please enter room no\",\r\n    \"pleaseEnterExtensionNo\": \"Please enter extension no\",\r\n    \"createSuccess\": \"Create successful\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"lockNo\": \"Lock No\",\r\n    \"owner\": \"Owner\",\r\n    \"isNetworkOpenDoor\": \"Network Open\",\r\n    \"isNetworkLock\": \"Network Lock\",\r\n    \"networkLockNo\": \"Lock No\",\r\n    \"isGuestControl\": \"Guest Control\",\r\n    \"guestControlAccount\": \"Account\",\r\n    \"isNetworkPowerControl\": \"Power Control\",\r\n    \"networkPowerAddress\": \"Power Addr\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"floor\": \"楼层\",\r\n    \"roomType\": \"房型\",\r\n    \"roomNo\": \"房号\",\r\n    \"extensionNo\": \"分机号\",\r\n    \"bedCount\": \"床位数\",\r\n    \"isEnabled\": \"是否启用\",\r\n    \"roomFeatures\": \"房间特征\",\r\n    \"roomDescription\": \"房间描述\",\r\n    \"pleaseSelectFloor\": \"请选择楼层\",\r\n    \"pleaseSelectRoomType\": \"请选择房型\",\r\n    \"pleaseEnterRoomNo\": \"请输入房号\",\r\n    \"pleaseEnterExtensionNo\": \"请输入分机号\",\r\n    \"createSuccess\": \"新增成功\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"lockNo\": \"房间锁号\",\r\n    \"owner\": \"所属业主\",\r\n    \"isNetworkOpenDoor\": \"是否网络开门\",\r\n    \"isNetworkLock\": \"是否网络锁门\",\r\n    \"networkLockNo\": \"网络锁号\",\r\n    \"isGuestControl\": \"是否房间客控\",\r\n    \"guestControlAccount\": \"客控账号\",\r\n    \"isNetworkPowerControl\": \"是否网络取电\",\r\n    \"networkPowerAddress\": \"网络取电地址\"\r\n  },\r\n  \"km\": {\r\n    \"floor\": \"ជាន់\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"roomNo\": \"លេខបន្ទប់\",\r\n    \"extensionNo\": \"លេខផ្ទាល់\",\r\n    \"bedCount\": \"ចំនួនគ្រែ\",\r\n    \"isEnabled\": \"ត្រូវបានអនុញ្ញាត\",\r\n    \"roomFeatures\": \"លក្ខណៈបន្ទប់\",\r\n    \"roomDescription\": \"ការពិពណ៌នា\",\r\n    \"pleaseSelectFloor\": \"សូមជ្រើសរើសជាន់\",\r\n    \"pleaseSelectRoomType\": \"សូមជ្រើសរើសប្រភេទបន្ទប់\",\r\n    \"pleaseEnterRoomNo\": \"សូមបញ្ចូលលេខបន្ទប់\",\r\n    \"pleaseEnterExtensionNo\": \"សូមបញ្ចូលលេខផ្ទាល់\",\r\n    \"createSuccess\": \"បង្កើតដោយជោគជ័យ\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"lockNo\": \"លេខសោ\",\r\n    \"owner\": \"ម្ចាស់\",\r\n    \"isNetworkOpenDoor\": \"បើកទ្វារតាមបណ្ដាញ\",\r\n    \"isNetworkLock\": \"ចាក់សោតាមបណ្ដាញ\",\r\n    \"networkLockNo\": \"លេខសោបណ្ដាញ\",\r\n    \"isGuestControl\": \"ការគ្រប់គ្រងភ្ញៀវ\",\r\n    \"guestControlAccount\": \"គណនី\",\r\n    \"isNetworkPowerControl\": \"ការគ្រប់គ្រងថាមពលតាមបណ្ដាញ\",\r\n    \"networkPowerAddress\": \"អាសយដ្ឋានថាមពល\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { buildingFloorApi, generalConfigApi, roomApi, rtApi } from '@/api/modules/index'\r\nimport { BooleanEnum, DictTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { handleTree } from '@/utils/tree'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  rCode: '',\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n  /** 房间代码 */\r\n  rCode: '',\r\n  /** 房号 */\r\n  rNo: '',\r\n  /** 房型代码 */\r\n  rtCode: '',\r\n  rtName: '',\r\n  /** 楼栋楼层 */\r\n  floorCodeList: [],\r\n  /** 楼栋代码 */\r\n  buildingCode: '',\r\n  buildingName: '',\r\n  /** 楼层代码 */\r\n  floorCode: '',\r\n  floorName: '',\r\n  /** 分机号 */\r\n  extNum: '',\r\n  /** 房间特性 json */\r\n  feature: [] as string[],\r\n  /** 是否启用 */\r\n  isEnable: '1',\r\n  /** 床位数 */\r\n  bedNum: 1,\r\n  /** 备注 */\r\n  remark: '',\r\n  /** 房间图片 */\r\n  urls: [],\r\n  /** 房间锁号 */\r\n  lockNo: '',\r\n  /** 所属业主 */\r\n  owner: '',\r\n  /** 是否网络开门 */\r\n  isNetworkOpenDoor: '0',\r\n  /** 是否网络锁门 */\r\n  isNetworkLock: '0',\r\n  /** 网络锁号 */\r\n  networkLockNo: '',\r\n  /** 是否房间客控 */\r\n  isGuestControl: '0',\r\n  /** 客控账号 */\r\n  guestControlAccount: '',\r\n  /** 是否网络取电 */\r\n  isNetworkPowerControl: '0',\r\n  /** 网络取电地址 */\r\n  networkPowerAddress: '',\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  rNo: [{ required: true, message: t('pleaseEnterRoomNo'), trigger: 'blur' }],\r\n  floorCodeList: [{ required: true, message: t('pleaseSelectFloor'), trigger: 'blur' }],\r\n  rtCode: [{ required: true, message: t('pleaseSelectRoomType'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  getBuildFloor()\r\n  getConstants()\r\n  getRoomDataList()\r\n})\r\nconst myTree = ref([])\r\nfunction getBuildFloor() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  buildingFloorApi.list(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      let list = []\r\n      list = handleTree(res.data)\r\n      list.forEach((ls) => {\r\n        if (ls.children) {\r\n          myTree.value.push(ls)\r\n        }\r\n      })\r\n    }\r\n  })\r\n}\r\n// 房型列表\r\nconst roomTypeList = ref<{ rtCode: string; rtName: string }[]>([])\r\nfunction getRoomDataList() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isVirtual: BooleanEnum.NO,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      roomTypeList.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n// 获取房间特性\r\nconst roomFeatureList = ref<{ code: string; name: string }[]>([])\r\nfunction getConstants() {\r\n  generalConfigApi.list({ gcode: userStore.gcode, hcode: userStore.hcode, type: DictTypeEnum.ROOM_FEATURE.toString(), isEnable: BooleanEnum.YES.toString() }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      roomFeatureList.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            form.value.buildingCode = form.value.floorCodeList[0]\r\n            form.value.floorCode = form.value.floorCodeList[1]\r\n            roomApi.createRoom(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('createSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"120px\" label-suffix=\"：\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('floor')\" prop=\"floorCodeList\">\r\n            <el-cascader v-model=\"form.floorCodeList\" clearable :options=\"myTree\" :props=\"{ value: 'code', label: 'name' }\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('roomType')\" prop=\"rtCode\">\r\n            <el-select v-model=\"form.rtCode\" clearable :placeholder=\"t('pleaseSelectRoomType')\">\r\n              <el-option v-for=\"item in roomTypeList\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('roomNo')\" prop=\"rNo\">\r\n            <el-input v-model=\"form.rNo\" maxlength=\"20\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('extensionNo')\" prop=\"extNum\">\r\n            <el-input v-model=\"form.extNum\" maxlength=\"30\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('bedCount')\" prop=\"bedNum\">\r\n            <el-input-number v-model=\"form.bedNum\" :min=\"0\" :max=\"99\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('isEnabled')\" prop=\"isEnable\">\r\n            <el-switch v-model=\"form.isEnable\" inline-prompt :active-text=\"t('yes')\" :inactive-text=\"t('no')\" active-value=\"1\" inactive-value=\"0\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('roomFeatures')\">\r\n            <el-checkbox-group v-model=\"form.feature\">\r\n              <el-checkbox v-for=\"ft in roomFeatureList\" :key=\"ft.code\" :value=\"ft.code\">\r\n                {{ ft.name }}\r\n              </el-checkbox>\r\n            </el-checkbox-group>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('lockNo')\">\r\n            <el-input v-model=\"form.lockNo\" :disabled=\"props.handle === 'detail'\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('isNetworkOpenDoor')\" label-width=\"140px\">\r\n            <el-radio-group v-model=\"form.isNetworkOpenDoor\" :disabled=\"props.handle === 'detail'\">\r\n              <el-radio label=\"1\">\r\n                {{ t('yes') }}\r\n              </el-radio>\r\n              <el-radio label=\"0\">\r\n                {{ t('no') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('isNetworkLock')\" label-width=\"140px\">\r\n            <el-radio-group v-model=\"form.isNetworkLock\" :disabled=\"props.handle === 'detail'\">\r\n              <el-radio label=\"1\">\r\n                {{ t('yes') }}\r\n              </el-radio>\r\n              <el-radio label=\"0\">\r\n                {{ t('no') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col v-if=\"form.isNetworkLock === '1'\" :md=\"12\">\r\n          <el-form-item :label=\"t('networkLockNo')\">\r\n            <el-input v-model=\"form.networkLockNo\" :disabled=\"props.handle === 'detail'\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('isGuestControl')\" label-width=\"140px\">\r\n            <el-radio-group v-model=\"form.isGuestControl\" :disabled=\"props.handle === 'detail'\">\r\n              <el-radio label=\"1\">\r\n                {{ t('yes') }}\r\n              </el-radio>\r\n              <el-radio label=\"0\">\r\n                {{ t('no') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col v-if=\"form.isGuestControl === '1'\" :md=\"12\">\r\n          <el-form-item :label=\"t('guestControlAccount')\">\r\n            <el-input v-model=\"form.guestControlAccount\" :disabled=\"props.handle === 'detail'\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('isNetworkPowerControl')\" label-width=\"140px\">\r\n            <el-radio-group v-model=\"form.isNetworkPowerControl\" :disabled=\"props.handle === 'detail'\">\r\n              <el-radio label=\"1\">\r\n                {{ t('yes') }}\r\n              </el-radio>\r\n              <el-radio label=\"0\">\r\n                {{ t('no') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"24\">\r\n          <el-form-item v-if=\"form.isNetworkPowerControl === '1'\" :label=\"t('networkPowerAddress')\" label-width=\"140px\">\r\n            <el-input v-model=\"form.networkPowerAddress\" :disabled=\"props.handle === 'detail'\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('roomDescription')\">\r\n            <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"4\" maxlength=\"255\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <!--      <el-row :gutter=\"20\"> -->\r\n      <!--        <el-col :md=\"24\"> -->\r\n      <!--          <el-form-item label=\"房间图片\"> -->\r\n      <!--            <images-upload v-model=\"form.urls\" :url=\"form.pics\" -->\r\n      <!--              action=\"https://console-mock.apipost.cn/app/mock/project/1f50f1da-5189-4282-d3c7-c133a514c5a8/upload/image\" -->\r\n      <!--              name=\"image\" :width=\"200\" /> -->\r\n      <!--          </el-form-item> -->\r\n      <!--        </el-col> -->\r\n      <!--      </el-row> -->\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "hcode", "rCode", "rNo", "rtCode", "rtName", "floorCodeList", "buildingCode", "buildingName", "floorCode", "floorName", "extNum", "feature", "isEnable", "bedNum", "remark", "urls", "lockNo", "owner", "isNetworkOpenDoor", "isNetworkLock", "networkLockNo", "isGuestControl", "guest<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "isNetworkPowerControl", "networkPowerAddress", "formRules", "required", "message", "trigger", "onMounted", "params", "buildingFloorApi", "list", "then", "res", "code", "handleTree", "data", "for<PERSON>ach", "ls", "children", "myTree", "value", "push", "getBuildFloor", "generalConfigApi", "type", "DictTypeEnum", "ROOM_FEATURE", "toString", "BooleanEnum", "YES", "roomFeatureList", "isVirtual", "NO", "rtApi", "getRoomTypeSimpleList", "roomTypeList", "getRoomDataList", "__expose", "submit", "Promise", "resolve", "validate", "valid", "roomApi", "createRoom", "ElMessage", "success", "center", "error", "msg"], "mappings": "k2CA2FA,MAAMA,EAAQC,GAGRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,MAAON,EAAUM,MAEjBC,MAAOP,EAAUO,MAEjBC,MAAO,GAEPC,IAAK,GAELC,OAAQ,GACRC,OAAQ,GAERC,cAAe,GAEfC,aAAc,GACdC,aAAc,GAEdC,UAAW,GACXC,UAAW,GAEXC,OAAQ,GAERC,QAAS,GAETC,SAAU,IAEVC,OAAQ,EAERC,OAAQ,GAERC,KAAM,GAENC,OAAQ,GAERC,MAAO,GAEPC,kBAAmB,IAEnBC,cAAe,IAEfC,cAAe,GAEfC,eAAgB,IAEhBC,oBAAqB,GAErBC,sBAAuB,IAEvBC,oBAAqB,KAGjBC,EAAY7B,EAAe,CAC/BM,IAAK,CAAC,CAAEwB,UAAU,EAAMC,QAASpC,EAAE,qBAAsBqC,QAAS,SAClEvB,cAAe,CAAC,CAAEqB,UAAU,EAAMC,QAASpC,EAAE,qBAAsBqC,QAAS,SAC5EzB,OAAQ,CAAC,CAAEuB,UAAU,EAAMC,QAASpC,EAAE,wBAAyBqC,QAAS,WAG1EC,GAAU,MAMV,WACE,MAAMC,EAAS,CACb/B,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,OAEnB+B,EAAiBC,KAAKF,GAAQG,MAAMC,IAC9B,GAAa,IAAbA,EAAIC,KAAY,CAClB,IAAIH,EAAO,GACJA,EAAAI,EAAWF,EAAIG,MACjBL,EAAAM,SAASC,IACRA,EAAGC,UACEC,EAAAC,MAAMC,KAAKJ,EAAE,GAEvB,IAEJ,CApBaK,GAyCGC,EAAAb,KAAK,CAAEjC,MAAON,EAAUM,MAAOC,MAAOP,EAAUO,MAAO8C,KAAMC,EAAaC,aAAaC,WAAYrC,SAAUsC,EAAYC,IAAIF,aAAchB,MAAMC,IAC/I,IAAbA,EAAIC,OACNiB,EAAgBV,MAAQR,EAAIG,KAAA,IAnBlC,WACE,MAAMP,EAAS,CACb/B,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,MACjBqD,UAAWH,EAAYI,GACvB1C,SAAUsC,EAAYC,KAExBI,EAAMC,sBAAsB1B,GAAQG,MAAMC,IACvB,IAAbA,EAAIC,OACNsB,EAAaf,MAAQR,EAAIG,KAAA,GAE5B,CAjCeqB,EAAA,IAEZ,MAAAjB,EAAS7C,EAAI,IAmBb,MAAA6D,EAAe7D,EAA0C,IAgBzD,MAAAwD,EAAkBxD,EAAsC,WASjD+D,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBjE,EAAQ6C,OACN7C,EAAQ6C,MAAMqB,UAAUC,IAClBA,IACFlE,EAAK4C,MAAMpC,aAAeR,EAAK4C,MAAMrC,cAAc,GACnDP,EAAK4C,MAAMlC,UAAYV,EAAK4C,MAAMrC,cAAc,GAChD4D,EAAQC,WAAWpE,EAAK4C,OAAOT,MAAMC,IAClB,IAAbA,EAAIC,MACNgC,EAAUC,QAAQ,CAChBzC,QAASpC,EAAE,iBACX8E,QAAQ,IAEFP,KAERK,EAAUG,MAAM,CACd3C,QAASO,EAAIqC,IACbF,QAAQ,GACT,IAEJ,GAEJ"}