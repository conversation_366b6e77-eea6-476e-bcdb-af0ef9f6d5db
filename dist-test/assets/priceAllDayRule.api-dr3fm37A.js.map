{"version": 3, "file": "priceAllDayRule.api-dr3fm37A.js", "sources": ["../../src/api/modules/pms/pricerule/priceAllDayRule.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/price-all-day-rule'\r\n/**\r\n * 全天房计费规则\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 更新全天房计费规则\r\n   * @param data\r\n   */\r\n  // updatePriceAllDayRule: (data: any) => {\r\n  //     return api.put(`${BASE_PATH}/update`, data);\r\n  // },\r\n  updatePriceAllDayRule: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n\r\n  /**\r\n   * 获得全天房计费规则\r\n   * @param data\r\n   */\r\n  // getPriceAllDayRule: (data: { gcode: string; hcode: string; }) => {\r\n  //     return api.get(`${BASE_PATH}/get`, {\r\n  //         params: data\r\n  //     });\r\n  // },\r\n  getPriceAllDayRule: (data: any) => api.get(`${BASE_PATH}/get`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "priceAllDayRuleApi", "updatePriceAllDayRule", "data", "api", "put", "getPriceAllDayRule", "get", "params"], "mappings": "mCAEA,MAAMA,EAAY,mCAIHC,EAAA,CASbC,sBAAwBC,GAAcC,EAAIC,IAAI,GAAGL,WAAoBG,GAWrEG,mBAAqBH,GAAcC,EAAIG,IAAI,GAAGP,QAAiB,CAAEQ,OAAQL"}