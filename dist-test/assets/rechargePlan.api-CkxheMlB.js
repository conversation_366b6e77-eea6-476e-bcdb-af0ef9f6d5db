import{a}from"./index-CkEhI1Zk.js";const t={list:t=>a.get("/admin-api/member/recharge-activity/list",{params:t}),create:t=>a.post("/admin-api/member/recharge-activity/create",t),updateActivityState:t=>a.put("/admin-api/member/recharge-activity/update-status",t),updateActivity:t=>a.put("/admin-api/member/recharge-activity/update",t),detail:t=>a.get("/admin-api/member/recharge-activity/get",{params:t})};export{t as r};
//# sourceMappingURL=rechargePlan.api-CkxheMlB.js.map
