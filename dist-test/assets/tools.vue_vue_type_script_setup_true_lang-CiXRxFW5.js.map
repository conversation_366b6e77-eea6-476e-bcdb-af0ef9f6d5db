{"version": 3, "file": "tools.vue_vue_type_script_setup_true_lang-CiXRxFW5.js", "sources": ["../../node_modules/scule/dist/index.mjs", "../../src/layouts/components/Topbar/Toolbar/tools.vue"], "sourcesContent": ["const NUMBER_CHAR_RE = /\\d/;\nconst STR_SPLITTERS = [\"-\", \"_\", \"/\", \".\"];\nfunction isUppercase(char = \"\") {\n  if (NUMBER_CHAR_RE.test(char)) {\n    return void 0;\n  }\n  return char !== char.toLowerCase();\n}\nfunction splitByCase(str, separators) {\n  const splitters = separators ?? STR_SPLITTERS;\n  const parts = [];\n  if (!str || typeof str !== \"string\") {\n    return parts;\n  }\n  let buff = \"\";\n  let previousUpper;\n  let previousSplitter;\n  for (const char of str) {\n    const isSplitter = splitters.includes(char);\n    if (isSplitter === true) {\n      parts.push(buff);\n      buff = \"\";\n      previousUpper = void 0;\n      continue;\n    }\n    const isUpper = isUppercase(char);\n    if (previousSplitter === false) {\n      if (previousUpper === false && isUpper === true) {\n        parts.push(buff);\n        buff = char;\n        previousUpper = isUpper;\n        continue;\n      }\n      if (previousUpper === true && isUpper === false && buff.length > 1) {\n        const lastChar = buff.at(-1);\n        parts.push(buff.slice(0, Math.max(0, buff.length - 1)));\n        buff = lastChar + char;\n        previousUpper = isUpper;\n        continue;\n      }\n    }\n    buff += char;\n    previousUpper = isUpper;\n    previousSplitter = isSplitter;\n  }\n  parts.push(buff);\n  return parts;\n}\nfunction upperFirst(str) {\n  return str ? str[0].toUpperCase() + str.slice(1) : \"\";\n}\nfunction lowerFirst(str) {\n  return str ? str[0].toLowerCase() + str.slice(1) : \"\";\n}\nfunction pascalCase(str, opts) {\n  return str ? (Array.isArray(str) ? str : splitByCase(str)).map((p) => upperFirst(opts?.normalize ? p.toLowerCase() : p)).join(\"\") : \"\";\n}\nfunction camelCase(str, opts) {\n  return lowerFirst(pascalCase(str || \"\", opts));\n}\nfunction kebabCase(str, joiner) {\n  return str ? (Array.isArray(str) ? str : splitByCase(str)).map((p) => p.toLowerCase()).join(joiner ?? \"-\") : \"\";\n}\nfunction snakeCase(str) {\n  return kebabCase(str || \"\", \"_\");\n}\nfunction flatCase(str) {\n  return kebabCase(str || \"\", \"\");\n}\nfunction trainCase(str, opts) {\n  return (Array.isArray(str) ? str : splitByCase(str)).filter(Boolean).map((p) => upperFirst(opts?.normalize ? p.toLowerCase() : p)).join(\"-\");\n}\nconst titleCaseExceptions = /^(a|an|and|as|at|but|by|for|if|in|is|nor|of|on|or|the|to|with)$/i;\nfunction titleCase(str, opts) {\n  return (Array.isArray(str) ? str : splitByCase(str)).filter(Boolean).map(\n    (p) => titleCaseExceptions.test(p) ? p.toLowerCase() : upperFirst(opts?.normalize ? p.toLowerCase() : p)\n  ).join(\" \");\n}\n\nexport { camelCase, flatCase, isUppercase, kebabCase, lowerFirst, pascalCase, snakeCase, splitByCase, titleCase, trainCase, upperFirst };\n", "<script setup lang=\"ts\">\r\nimport type { Settings } from '#/global'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport { pascalCase } from 'scule'\r\n\r\ndefineOptions({\r\n  name: 'ToolbarTools',\r\n})\r\n\r\nconst props = defineProps<{\r\n  mode: 'left-side' | 'right-side'\r\n}>()\r\n\r\nconst modules = import.meta.glob('./*/index.vue', {\r\n  import: 'default',\r\n  eager: true,\r\n})\r\n\r\nconst settingsStore = useSettingsStore()\r\n\r\nconst tools = computed(() => {\r\n  const index = settingsStore.settings.toolbar.layout.findIndex(\r\n    (item) => item === '->'\r\n  )\r\n  const tools: Exclude<keyof Settings.toolbar, 'layout'>[] = []\r\n  if (props.mode === 'left-side') {\r\n    settingsStore.settings.toolbar.layout.forEach((item, i) => {\r\n      if (i < index && item !== '->') {\r\n        tools.push(item)\r\n      }\r\n    })\r\n  } else {\r\n    settingsStore.settings.toolbar.layout.forEach((item, i) => {\r\n      if (i > index && item !== '->') {\r\n        tools.push(item)\r\n      }\r\n    })\r\n  }\r\n  return tools\r\n  // return tools.filter((item) => item != 'pageReload')\r\n})\r\n</script>\r\n\r\n<template>\r\n  <template v-for=\"item in tools\" :key=\"item\">\r\n    <Component\r\n      :is=\"modules[`./${pascalCase(item)}/index.vue`]\"\r\n      v-if=\"settingsStore.settings.toolbar[item]\"\r\n    />\r\n  </template>\r\n</template>\r\n"], "names": ["NUMBER_CHAR_RE", "STR_SPLITTERS", "isUppercase", "char", "test", "toLowerCase", "pascalCase", "str", "opts", "Array", "isArray", "splitters", "parts", "previousUpper", "previousSplitter", "buff", "isSplitter", "includes", "push", "isUpper", "length", "lastChar", "at", "slice", "Math", "max", "splitByCase", "map", "p", "toUpperCase", "upperFirst", "normalize", "join", "props", "__props", "modules", "assign", "__vite_glob_0_0", "__vite_glob_0_1", "__vite_glob_0_2", "__vite_glob_0_3", "__vite_glob_0_4", "__vite_glob_0_5", "__vite_glob_0_6", "__vite_glob_0_7", "settingsStore", "useSettingsStore", "tools", "computed", "index", "settings", "toolbar", "layout", "findIndex", "item", "mode", "for<PERSON>ach", "i"], "mappings": "4iBAAA,MAAMA,EAAiB,KACjBC,EAAgB,CAAC,IAAK,IAAK,IAAK,KACtC,SAASC,EAAYC,EAAO,IACtB,IAAAH,EAAeI,KAAKD,GAGjB,OAAAA,IAASA,EAAKE,aACvB,CA+CA,SAASC,EAAWC,EAAKC,GAChB,OAAAD,GAAOE,MAAMC,QAAQH,GAAOA,EA/CrC,SAAqBA,GACnB,MAAMI,EAA0BV,EAC1BW,EAAQ,GACd,IAAKL,GAAsB,iBAARA,EACV,OAAAK,EAET,IACIC,EACAC,EAFAC,EAAO,GAGX,IAAA,MAAWZ,KAAQI,EAAK,CAChB,MAAAS,EAAaL,EAAUM,SAASd,GACtC,IAAmB,IAAfa,EAAqB,CACvBJ,EAAMM,KAAKH,GACJA,EAAA,GACSF,OAAA,EAChB,QACN,CACU,MAAAM,EAAUjB,EAAYC,GAC5B,IAAyB,IAArBW,EAA4B,CAC1B,IAAkB,IAAlBD,IAAuC,IAAZM,EAAkB,CAC/CP,EAAMM,KAAKH,GACJA,EAAAZ,EACSU,EAAAM,EAChB,QACR,CACM,IAAsB,IAAlBN,IAAsC,IAAZM,GAAqBJ,EAAKK,OAAS,EAAG,CAC5D,MAAAC,EAAWN,EAAKO,IAAK,GACrBV,EAAAM,KAAKH,EAAKQ,MAAM,EAAGC,KAAKC,IAAI,EAAGV,EAAKK,OAAS,KACnDL,EAAOM,EAAWlB,EACFU,EAAAM,EAChB,QACR,CACA,CACYJ,GAAAZ,EACQU,EAAAM,EACGL,EAAAE,CACvB,CAES,OADPJ,EAAMM,KAAKH,GACJH,CACT,CAQ2Cc,CAAYnB,IAAMoB,KAAKC,GAPlE,SAAoBrB,GACX,OAAAA,EAAMA,EAAI,GAAGsB,cAAgBtB,EAAIgB,MAAM,GAAK,EACrD,CAKwEO,EAAW,MAAAtB,OAAA,EAAAA,EAAMuB,WAAYH,EAAEvB,cAAgBuB,KAAII,KAAK,IAAM,EACtI,wEC/CA,MAAMC,EAAQC,EAIRC,SAAUC,OAAA,CAAA,yBAAAC,EAAA,0BAAAC,EAAA,wBAAAC,EAAA,yBAAAC,EAAA,mBAAAC,EAAA,wBAAAC,EAAA,2BAAAC,EAAA,yBAAAC,IAKVC,EAAgBC,IAEhBC,EAAQC,GAAS,KACrB,MAAMC,EAAQJ,EAAcK,SAASC,QAAQC,OAAOC,WACjDC,GAAkB,OAATA,IAENP,EAAqD,GAcpDA,MAbY,cAAfd,EAAMsB,KACRV,EAAcK,SAASC,QAAQC,OAAOI,SAAQ,CAACF,EAAMG,KAC/CA,EAAIR,GAAkB,OAATK,GACfP,EAAM7B,KAAKoC,EAAI,IAInBT,EAAcK,SAASC,QAAQC,OAAOI,SAAQ,CAACF,EAAMG,KAC/CA,EAAIR,GAAkB,OAATK,GACfP,EAAM7B,KAAKoC,EAAI,IAIdP,CAAAA", "x_google_ignoreList": [0]}