{"version": 3, "file": "grantModal-S8hNNB6E.js", "sources": ["../../src/views/order/info/components/orderdetail/grantModal.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"authDialog\": {\r\n      \"dialogTitle\": \"Pre-Authorization\",\r\n      \"table\": {\r\n        \"type\": \"Authorization Type\",\r\n        \"amount\": \"Authorized Amount\",\r\n        \"received\": \"Received Amount\",\r\n        \"status\": \"Status\",\r\n        \"operation\": \"Operation\"\r\n      },\r\n      \"status\": {\r\n        \"open\": \"Open\",\r\n        \"cancel\": \"Cancelled\",\r\n        \"closed\": \"Completed\"\r\n      },\r\n      \"operation\": {\r\n        \"cancel\": \"Cancel\",\r\n        \"finish\": \"Complete\"\r\n      },\r\n      \"confirm\": {\r\n        \"cancel\": \"Are you sure you want to cancel this authorization?\",\r\n        \"finish\": \"Are you sure you want to complete this authorization?\"\r\n      },\r\n      \"success\": \"Authorization operation successful\",\r\n      \"close\": \"Close\"\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"authDialog\": {\r\n      \"dialogTitle\": \"预授权\",\r\n      \"table\": {\r\n        \"type\": \"预授权类型\",\r\n        \"amount\": \"预授权金额\",\r\n        \"received\": \"实收金额\",\r\n        \"status\": \"状态\",\r\n        \"operation\": \"操作\"\r\n      },\r\n      \"status\": {\r\n        \"open\": \"预授\",\r\n        \"cancel\": \"取消\",\r\n        \"closed\": \"完成\"\r\n      },\r\n      \"operation\": {\r\n        \"cancel\": \"取消\",\r\n        \"finish\": \"完成\"\r\n      },\r\n      \"confirm\": {\r\n        \"cancel\": \"确认取消该授权?\",\r\n        \"finish\": \"确认完成该授权?\"\r\n      },\r\n      \"success\": \"授权操作成功\",\r\n      \"close\": \"关闭\"\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"authDialog\": {\r\n      \"dialogTitle\": \"ការអនុញ្ញាតជាមុន\",\r\n      \"table\": {\r\n        \"type\": \"ប្រភេទការអនុញ្ញាត\",\r\n        \"amount\": \"ចំនួនទឹកប្រាក់ដែលបានអនុញ្ញាត\",\r\n        \"received\": \"ចំនួនទឹកប្រាក់ដែលបានទទួល\",\r\n        \"status\": \"ស្ថានភាព\",\r\n        \"operation\": \"ប្រតិបត្តិការ\"\r\n      },\r\n      \"status\": {\r\n        \"open\": \"បើក\",\r\n        \"cancel\": \"បានលុបចោល\",\r\n        \"closed\": \"បានបញ្ចប់\"\r\n      },\r\n      \"operation\": {\r\n        \"cancel\": \"លុបចោល\",\r\n        \"finish\": \"បញ្ចប់\"\r\n      },\r\n      \"confirm\": {\r\n        \"cancel\": \"តើអ្នកប្រាកដថាចង់លុបចោលការអនុញ្ញាតនេះទេ?\",\r\n        \"finish\": \"តើអ្នកប្រាកដថាចង់បញ្ចប់ការអនុញ្ញាតនេះទេ?\"\r\n      },\r\n      \"success\": \"ប្រតិបត្តិការអនុញ្ញាតជោគជ័យ\",\r\n      \"close\": \"បិទ\"\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { accountApi } from '@/api/modules'\r\nimport { AccountState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    orderTogetherCode: number | string\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    orderTogetherCode: '',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  refresh: []\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nonMounted(() => {\r\n  getData()\r\n})\r\n\r\nconst dataList = ref<any[]>([])\r\n\r\nfunction getData() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    togetherCodeList: [props.orderTogetherCode],\r\n  }\r\n  accountApi.getPreauth(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      dataList.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n// 预授权取消与确认\r\nfunction cancel(val: any) {\r\n  ElMessageBox.confirm(t('authDialog.confirm.cancel'))\r\n    .then(() => {\r\n      const params = {\r\n        gcode: userStore.gcode,\r\n        hcode: userStore.hcode,\r\n        accNo: val.accNo,\r\n      }\r\n      accountApi.cancelPreauth(params).then((res: any) => {\r\n        if (res.code === 0) {\r\n          ElMessage.success(t('authDialog.success'))\r\n          val.state = 'cancel'\r\n        }\r\n      })\r\n    })\r\n    .catch(() => {})\r\n}\r\n\r\nfunction finish(val: any) {\r\n  ElMessageBox.confirm(t('authDialog.confirm.finish'))\r\n    .then(() => {\r\n      const params = {\r\n        gcode: userStore.gcode,\r\n        hcode: userStore.hcode,\r\n        accNo: val.accNo,\r\n        fee: val.fee,\r\n      }\r\n      accountApi.confirmPreauth(params).then((res: any) => {\r\n        if (res.code === 0) {\r\n          ElMessage.success(t('authDialog.success'))\r\n          val.state = 'closed'\r\n        }\r\n      })\r\n    })\r\n    .catch(() => {})\r\n}\r\n\r\nfunction handleClose(done: () => void) {\r\n  done()\r\n  emits('refresh')\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n  emits('refresh')\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" width=\"800px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\" :before-close=\"handleClose\">\r\n    <el-table\r\n      :data=\"dataList\"\r\n      style=\"width: 800px; margin: auto\"\r\n      :cell-style=\"{ textAlign: 'center' }\"\r\n      :header-cell-style=\"{\r\n        background: '#f5f7fa',\r\n        color: '#606266',\r\n        textAlign: 'center',\r\n      }\"\r\n      class=\"list-table\"\r\n      height=\"100%\"\r\n    >\r\n      <el-table-column :label=\"t('authDialog.table.type')\" prop=\"subName\" />\r\n      <el-table-column :label=\"t('authDialog.table.amount')\" prop=\"preAuth\" />\r\n      <el-table-column :label=\"t('authDialog.table.received')\">\r\n        <template #default=\"scope\">\r\n          <div style=\"display: flex\">\r\n            <el-input-number v-if=\"scope.row.state === AccountState.UNCLOSED\" v-model=\"scope.row.fee\" :controls=\"false\" style=\"text-align: left\" />\r\n            <span v-else style=\"padding-left: 15px\">{{ scope.row.fee }}</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('authDialog.table.status')\" prop=\"state\">\r\n        <template #default=\"scope\">\r\n          <span v-if=\"scope.row.state === AccountState.UNCLOSED\">{{ t('authDialog.status.open') }}</span>\r\n          <span v-else-if=\"scope.row.state === 'cancel'\">{{ t('authDialog.status.cancel') }}</span>\r\n          <span v-else-if=\"scope.row.state === 'closed'\">{{ t('authDialog.status.closed') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('authDialog.table.operation')\">\r\n        <template #default=\"scope\">\r\n          <div v-if=\"scope.row.state === AccountState.UNCLOSED\" style=\"display: flex\">\r\n            <el-button key=\"primary\" type=\"primary\" style=\"padding: 0; margin-left: 16px\" text @click=\"cancel(scope.row)\">\r\n              {{ t('authDialog.operation.cancel') }}\r\n            </el-button>\r\n            <el-button key=\"primary\" type=\"primary\" style=\"padding: 0; margin-left: 28px\" text @click=\"finish(scope.row)\">\r\n              {{ t('authDialog.operation.finish') }}\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <template #footer>\r\n      <el-button @click=\"onCancel\">\r\n        {{ t('authDialog.close') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n.box {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "myVisible", "computed", "get", "modelValue", "set", "val", "onMounted", "params", "gcode", "hcode", "togetherCodeList", "orderTogetherCode", "accountApi", "<PERSON><PERSON><PERSON><PERSON>", "then", "res", "code", "dataList", "value", "data", "getData", "ref", "handleClose", "done", "onCancel", "ElMessageBox", "confirm", "accNo", "cancelPreauth", "ElMessage", "success", "state", "catch", "fee", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "6hCA2FA,MAAMA,EAAQC,EAURC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,EAAYC,EAAS,CACzBC,IAAM,IACGV,EAAMW,WAEf,GAAAC,CAAIC,GACFX,EAAM,oBAAqBW,EAAG,IAIlCC,GAAU,MAMV,WACE,MAAMC,EAAS,CACbC,MAAOV,EAAUU,MACjBC,MAAOX,EAAUW,MACjBC,iBAAkB,CAAClB,EAAMmB,oBAE3BC,EAAWC,WAAWN,GAAQO,MAAMC,IACjB,IAAbA,EAAIC,OACNC,EAASC,MAAQH,EAAII,KAAA,GAExB,CAfOC,EAAA,IAGJ,MAAAH,EAAWI,EAAW,IAqD5B,SAASC,EAAYC,GACdA,IACL7B,EAAM,UAAS,CAGjB,SAAS8B,IACPxB,EAAUkB,OAAQ,EAClBxB,EAAM,UAAS,yjDA5CDW,aACdoB,EAAaC,QAAQ9B,EAAE,8BACpBkB,MAAK,KACJ,MAAMP,EAAS,CACbC,MAAOV,EAAUU,MACjBC,MAAOX,EAAUW,MACjBkB,MAAOtB,EAAIsB,OAEbf,EAAWgB,cAAcrB,GAAQO,MAAMC,IACpB,IAAbA,EAAIC,OACIa,EAAAC,QAAQlC,EAAE,uBACpBS,EAAI0B,MAAQ,SAAA,GAEf,IAEFC,OAAM,SAfX,IAAgB3B,iMAkBAA,aACdoB,EAAaC,QAAQ9B,EAAE,8BACpBkB,MAAK,KACJ,MAAMP,EAAS,CACbC,MAAOV,EAAUU,MACjBC,MAAOX,EAAUW,MACjBkB,MAAOtB,EAAIsB,MACXM,IAAK5B,EAAI4B,KAEXrB,EAAWsB,eAAe3B,GAAQO,MAAMC,IACrB,IAAbA,EAAIC,OACIa,EAAAC,QAAQlC,EAAE,uBACpBS,EAAI0B,MAAQ,SAAA,GAEf,IAEFC,OAAM,SAhBX,IAAgB3B"}