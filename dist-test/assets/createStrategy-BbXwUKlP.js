import{d as e,aj as t,ai as l,b as a,av as i,y as s,b9 as o,s as r,aR as n,D as u,aq as d,u as c,o as p,c as b,f as y,w as m,h as g,Y as f,F as h,ag as v,e as T,g as S,R as P,i as k,aD as _,aS as V,m as D,E as C,l as x,j,k as w,n as R,ax as E,bv as U,bK as N,p as M,x as O,aT as A}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css                          *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                */import{_ as L}from"./index-DAulSAJI.js";/* empty css                        *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                 */import"./el-form-item-l0sNRNKZ.js";/* empty css                   */import{m as I}from"./member.api-2tU9HGvl.js";import{c as q}from"./channel.api-CM6FWEgD.js";import{g as Y}from"./generalConfig.api-CEBBd8kx.js";import{p as B}from"./priceStrategy.api-Deh17XpK.js";import{r as G}from"./rt.api-5a8-At7-.js";import{d as F}from"./dictData.api-DUabpYqy.js";import{m as H}from"./merchant.api-BtmIsRm3.js";import{$,a0 as W,a1 as X,F as K,B as z,r as J,a2 as Q,q as Z,j as ee,v as te,u as le,a3 as ae,a4 as ie,G as se,A as oe}from"./constants-Cg3j_uH4.js";import{y as re}from"./timeutils-Ib6GkGcq.js";import{_ as ne}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                */import"./index-D8c6PuWt.js";/* empty css                      *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                        *//* empty css                         *//* empty css                */import"./index-CDbn0nBx.js";/* empty css                          */import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";const ue={class:"equity"},de={class:"equity"},ce={class:"equity"},pe={class:"equity"},be={key:0},ye={key:1},me={key:2},ge=e({__name:"createStrategy",props:{strategyCode:{default:""},handle:{},isEdit:{type:Boolean},id:{}},setup(e,{expose:ne}){const{t:ge}=t(),fe=l(),he=a(!1),ve=a(),Te=a({gcode:fe.gcode,hcode:fe.hcode,strategyCode:"",strategyName:"",condition:{guestSrc:{guestSrcType:"0",guestSrcCodes:[]},rt:{rtType:"0",isLimited:!1,rtCodes:[]},checkinType:{type:"0",checkinTypes:[]},orderSource:["lobby"]},strategy:{discountsType:{type:$.DISCOUNT,value:0,priceHandle:W.ROUND,fixedPrices:[]},rightsTypes:[{type:X.DELAY,value:0,fee:0}]},scope:{weeks:["1","2","3","4","5","6","7"],channels:[K.LOBBY],effectDate:[new Date,i(new Date).add(1,"year")],startDate:"",endDate:""},isEnable:z.YES,isG:z.NO,hotelCodes:[fe.hcode]}),Se=a({strategyName:[{required:!0,message:ge("enterStrategyName"),trigger:"blur"}],"scope.effectDate":[{required:!0,message:ge("enterEffectDate"),trigger:"blur"}]}),Pe=a([]),ke=a([]),_e=a([]),Ve=a([]),De=a([]),Ce=a([]);s((()=>{I.listMemberType(fe.gcode,"1").then((e=>{0===e.code&&(De.value=e.data)})),function(){const e={gcode:fe.gcode,hcode:fe.hcode,isEnable:z.YES};q.getChannelSimpleList(e).then((e=>{0===e.code&&(_e.value=e.data)}))}(),function(){const e={gcode:fe.gcode,hcode:fe.hcode,isVirtual:z.NO,isEnable:z.YES};G.getRoomTypeSimpleList(e).then((e=>{0===e.code&&(Me.value=e.data)}))}(),function(){const e={gcode:fe.gcode};H.list(e).then((e=>{0===e.code&&(Ce.value=e.data.list)}))}(),F.getDictDataBatch(xe).then((e=>{je.value=e.data.filter((e=>e.dictType===J)),we.value=e.data.filter((e=>e.dictType===Q)),Re.value=e.data.filter((e=>e.dictType===Z&&[ee.ALL_DAY,ee.LONG_STAY,ee.TRAVEL_GROUP,ee.MEETING_GROUP].includes(e.code))),Ee.value=e.data.filter((e=>e.dictType===te)),Pe.value=e.data.filter((e=>e.dictType===le)),Ve.value=e.data.filter((e=>e.dictType===ae)),ke.value=e.data.filter((e=>e.dictType===ie))}))}));const xe=[J,Q,Z,te,le,ae,ie],je=a([]),we=a([]),Re=a([]),Ee=a([]),Ue=a({maxHeight:"200px"}),Ne=a([{label:ge("roomType"),prop:"room",render:({row:e})=>Me.value.filter((t=>t.rtCode==e.rtCode))[0].rtName},{label:ge("salePrice"),prop:"fee",render:({row:e})=>o(r,{modelValue:e.fee,style:"width:140px",min:0,"onUpdate:modelValue":t=>{e.fee=t}})}]);const Me=a([]);function Oe(e){return e.getTime()<Date.now()-864e5}function Ae(){Te.value.condition.checkinType.checkinTypes=[]}function Le(e){e.includes("0")&&e.length<1?Te.value.condition.orderSource=["0"]:e.includes("0")&&e.length>1&&(Te.value.condition.orderSource=e.filter((e=>"0"!==e)))}function Ie(e){Te.value.condition.guestSrc.guestSrcCodes=[],e!==se.AGENT&&e!==se.PROTOCOL||function(e){const t=e===se.PROTOCOL?Q:oe;Y.list({gcode:fe.gcode,type:t}).then((e=>{qe.value=e.data}))}(e)}ne({submit:()=>new Promise(((e,t)=>{ve.value&&ve.value.validate((t=>{if(t){const t=Te.value.strategy.discountsType.fixedPrices;if(Te.value.strategy.discountsType.type==$.FIX)for(let e=0;e<t.length;e++){if(0==t[e].fee)return void n.error({message:ge("priceCannot"),center:!0})}else Te.value.strategy.discountsType.fixedPrices=[];Te.value.scope.startDate=re(Te.value.scope.effectDate[0]),Te.value.scope.endDate=re(Te.value.scope.effectDate[1]),B.createPriceStrategy(Te.value).then((t=>{0===t.code?(n.success({message:ge("modificationSuccess"),center:!0}),e()):n.error({message:t.msg,center:!0})}))}}))}))});const qe=a([]);const Ye=a(!1),Be=a(!1);function Ge(e){Be.value=!1,Te.value.condition.rt.rtCodes=e?Me.value.map((e=>e.rtCode)):[]}function Fe(){const e=Te.value.strategy.rightsTypes.map((e=>e.type)),t=ke.value.filter((t=>!e.includes(t.code)));Te.value.strategy.rightsTypes.push({type:t[0].code,value:0,fee:0})}return u((()=>Te.value.condition.rt.rtCodes),(e=>{0===e.length?(Ye.value=!1,Be.value=!1):e.length===Me.value.length?(Ye.value=!0,Be.value=!1):Be.value=!0;const t=[...new Set(e)],l=[];t.forEach((e=>{l.push({rtCode:e,fee:0})})),Te.value.strategy.discountsType.fixedPrices=l})),(e,t)=>{const l=_,a=V,i=D,s=C,o=x,n=j,u=w,I=R,q=E,Y=L,B=U,G=N,F=M,H=O,W=A;return d((p(),b("div",null,[y(H,{ref_key:"formRef",ref:ve,model:c(Te),rules:c(Se),"label-width":"160px","label-suffix":"："},{default:m((()=>[y(l,{"content-position":"left"},{default:m((()=>[g(f(c(ge)("strategyInfo")),1)])),_:1}),y(i,{label:c(ge)("priceStrategyName"),prop:"strategyName"},{default:m((()=>[y(a,{modelValue:c(Te).strategyName,"onUpdate:modelValue":t[0]||(t[0]=e=>c(Te).strategyName=e),placeholder:c(ge)("enterPriceStrategyName")},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),y(l,{"content-position":"left"},{default:m((()=>[g(f(c(ge)("rule")),1)])),_:1}),y(i,{label:c(ge)("guestSourceType")},{default:m((()=>[y(o,{modelValue:c(Te).condition.guestSrc.guestSrcType,"onUpdate:modelValue":t[1]||(t[1]=e=>c(Te).condition.guestSrc.guestSrcType=e),onChange:Ie},{default:m((()=>[(p(!0),b(h,null,v(c(je),(e=>(p(),T(s,{key:e.code,value:e.code,label:c(ge)(e.code)},null,8,["value","label"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),"0"!==c(Te).condition.guestSrc.guestSrcType&&"walk_in"!==c(Te).condition.guestSrc.guestSrcType&&""!==c(Te).condition.guestSrc.guestSrcType?(p(),T(i,{key:0,label:c(ge)("level")},{default:m((()=>[S("div",ue,[y(u,{modelValue:c(Te).condition.guestSrc.guestSrcCodes,"onUpdate:modelValue":t[2]||(t[2]=e=>c(Te).condition.guestSrc.guestSrcCodes=e),placeholder:c(ge)("selectLevel"),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",style:{width:"240px"}},{default:m((()=>[c(Te).condition.guestSrc.guestSrcType===c(se).MEMBER?(p(!0),b(h,{key:0},v(c(De),(e=>(p(),T(n,{key:e.mtCode,label:e.mtName,value:e.mtCode},null,8,["label","value"])))),128)):P("",!0),c(Te).condition.guestSrc.guestSrcType===c(se).AGENT||c(Te).condition.guestSrc.guestSrcType===c(se).PROTOCOL?(p(!0),b(h,{key:1},v(c(qe),(e=>(p(),T(n,{key:e.code,label:e.name,value:e.code},null,8,["label","value"])))),128)):P("",!0)])),_:1},8,["modelValue","placeholder"])])])),_:1},8,["label"])):P("",!0),y(i,{label:c(ge)("roomType")},{default:m((()=>[S("div",de,[y(u,{modelValue:c(Te).condition.rt.rtType,"onUpdate:modelValue":t[3]||(t[3]=e=>c(Te).condition.rt.rtType=e),style:{width:"120px"}},{default:m((()=>[y(n,{label:c(ge)("store"),value:"0"},null,8,["label"]),y(n,{label:c(ge)("group"),value:"1"},null,8,["label"])])),_:1},8,["modelValue"]),y(u,{modelValue:c(Te).condition.rt.rtCodes,"onUpdate:modelValue":t[5]||(t[5]=e=>c(Te).condition.rt.rtCodes=e),placeholder:c(ge)("selectRoomType"),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",style:{width:"240px"}},{header:m((()=>[y(I,{modelValue:c(Ye),"onUpdate:modelValue":t[4]||(t[4]=e=>k(Ye)?Ye.value=e:null),indeterminate:c(Be),onChange:Ge},{default:m((()=>[g(f(c(ge)("selectAll")),1)])),_:1},8,["modelValue","indeterminate"])])),default:m((()=>[(p(!0),b(h,null,v(c(Me),(e=>(p(),T(n,{key:e.rtCode,label:e.rtName,value:e.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])])),_:1},8,["label"]),y(i,{label:c(ge)("checkinType")},{default:m((()=>[S("div",ce,[y(o,{modelValue:c(Te).condition.checkinType.type,"onUpdate:modelValue":t[6]||(t[6]=e=>c(Te).condition.checkinType.type=e),onChange:Ae},{default:m((()=>[y(q,{value:"0",border:""},{default:m((()=>[g(f(c(ge)("allDayRoom")),1)])),_:1}),y(q,{value:"1",border:""},{default:m((()=>[g(f(c(ge)("hourlyRoom")),1)])),_:1})])),_:1},8,["modelValue"]),y(u,{modelValue:c(Te).condition.checkinType.checkinTypes,"onUpdate:modelValue":t[7]||(t[7]=e=>c(Te).condition.checkinType.checkinTypes=e),placeholder:c(ge)("selectCheckinType"),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",style:{width:"240px"}},{default:m((()=>["1"===c(Te).condition.checkinType.type?(p(!0),b(h,{key:0},v(c(Ee),(e=>(p(),T(n,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128)):(p(!0),b(h,{key:1},v(c(Re),(e=>(p(),T(n,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])])),_:1},8,["label"]),y(i,{label:c(ge)("orderSource")},{default:m((()=>[y(u,{modelValue:c(Te).condition.orderSource,"onUpdate:modelValue":t[8]||(t[8]=e=>c(Te).condition.orderSource=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"240px"},clearable:"",onChange:Le},{default:m((()=>[y(n,{label:c(ge)("unlimitedSource"),value:"0"},null,8,["label"]),(p(!0),b(h,null,v(c(Pe),(e=>(p(),T(n,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),y(l,{"content-position":"left"},{default:m((()=>[g(f(c(ge)("priceRights")),1)])),_:1}),y(i,{label:c(ge)("discountType")},{default:m((()=>[S("div",pe,[y(u,{modelValue:c(Te).strategy.discountsType.type,"onUpdate:modelValue":t[9]||(t[9]=e=>c(Te).strategy.discountsType.type=e),style:{width:"120px"}},{default:m((()=>[y(n,{label:c(ge)("discount"),value:"discount"},null,8,["label"]),y(n,{label:c(ge)("reduce"),value:"reduce"},null,8,["label"]),y(n,{label:c(ge)("fixedPrice"),value:"fixed"},null,8,["label"])])),_:1},8,["modelValue"]),c(Te).strategy.discountsType.type!=c($).FIX?(p(),b(h,{key:0},[y(c(r),{modelValue:c(Te).strategy.discountsType.value,"onUpdate:modelValue":t[10]||(t[10]=e=>c(Te).strategy.discountsType.value=e),placeholder:c(ge)("enterDiscountValue"),max:c(Te).strategy.discountsType.type===c($).DISCOUNT?1:1e7,min:0,step:.01,precision:2,"controls-position":"right",style:{width:"200px","margin-right":"8px"}},null,8,["modelValue","placeholder","max"]),g(" "+f(c(Te).strategy.discountsType.type===c($).DISCOUNT?c(ge)("0.88 is 88"):c(ge)("yuan")),1)],64)):P("",!0)])])),_:1},8,["label"]),c(Te).strategy.discountsType.type==c($).FIX?(p(),T(i,{key:1,"label-width":"0"},{default:m((()=>[y(Y,{style:{width:"100%"},columns:c(Ne),"table-data":c(Te).strategy.discountsType.fixedPrices,options:c(Ue)},null,8,["columns","table-data","options"])])),_:1})):(p(),T(i,{key:2,label:c(ge)("priceMethod")},{default:m((()=>[y(u,{modelValue:c(Te).strategy.discountsType.priceHandle,"onUpdate:modelValue":t[11]||(t[11]=e=>c(Te).strategy.discountsType.priceHandle=e),"collapse-tags":"","collapse-tags-tooltip":"",style:{width:"240px"}},{default:m((()=>[(p(!0),b(h,null,v(c(Ve),(e=>(p(),T(n,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])),y(i,{label:c(ge)("rightsType")},{default:m((()=>[(p(!0),b(h,null,v(c(Te).strategy.rightsTypes,((e,l)=>(p(),b("div",{key:l,class:"equity"},[y(u,{modelValue:e.type,"onUpdate:modelValue":t=>e.type=t,style:{width:"150px"}},{default:m((()=>[(p(!0),b(h,null,v(c(ke),(e=>(p(),T(n,{key:e.code,label:c(ge)(e.code),value:e.code,disabled:e.code===c(X).MULTI_POINT&&c(Te).condition.guestSrc.guestSrcType!==c(se).MEMBER},null,8,["label","value","disabled"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"]),e.type===c(X).DELAY?(p(),b("div",be,[y(c(r),{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,placeholder:c(ge)("enterMinutesRange"),min:0,step:1,precision:0,"controls-position":"right",style:{width:"120px","margin-right":"8px"}},null,8,["modelValue","onUpdate:modelValue","placeholder"]),g(" "+f(c(ge)("minute")),1)])):P("",!0),e.type===c(X).MULTI_POINT?(p(),b("div",ye,[y(c(r),{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,placeholder:c(ge)("enterMultiplier"),min:0,step:1,precision:1,"controls-position":"right",style:{width:"120px","margin-right":"8px"}},null,8,["modelValue","onUpdate:modelValue","placeholder"]),g(" "+f(c(ge)("times")),1)])):P("",!0),e.type===c(X).BREAKFAST?(p(),b("div",me,[y(u,{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,style:{width:"120px"}},{default:m((()=>[y(n,{label:`1${c(ge)("pieces")}`,value:"1"},null,8,["label"]),y(n,{label:`2${c(ge)("pieces")}`,value:"2"},null,8,["label"]),y(n,{label:`3${c(ge)("pieces")}`,value:"3"},null,8,["label"]),y(n,{label:`4${c(ge)("pieces")}`,value:"4"},null,8,["label"])])),_:2},1032,["modelValue","onUpdate:modelValue"]),y(c(r),{modelValue:e.fee,"onUpdate:modelValue":t=>e.fee=t,placeholder:c(ge)("pleaseEnterPricePerPortion"),max:60,min:1,step:1,precision:0,"controls-position":"right",style:{width:"120px","margin-right":"8px"}},null,8,["modelValue","onUpdate:modelValue","placeholder"]),g(" "+f(c(ge)("yuan")),1)])):P("",!0),t[15]||(t[15]=g("     ")),c(Te).strategy.rightsTypes.length>1?(p(),T(B,{key:3,type:"primary",underline:!1,onClick:e=>function(e){Te.value.strategy.rightsTypes.splice(e,1)}(l)},{default:m((()=>[g(f(c(ge)("delete")),1)])),_:2},1032,["onClick"])):P("",!0),t[16]||(t[16]=g("     ")),c(Te).strategy.rightsTypes.length<c(ke).length&&c(Te).strategy.rightsTypes.length===l+1?(p(),T(B,{key:4,type:"primary",underline:!1,onClick:Fe},{default:m((()=>[g(f(c(ge)("add")),1)])),_:1})):P("",!0)])))),128))])),_:1},8,["label"]),y(l,{"content-position":"left"},{default:m((()=>[g(f(c(ge)("applicationScope")),1)])),_:1}),y(i,{label:c(ge)("effectDate"),prop:"scope.effectDate"},{default:m((()=>[y(G,{modelValue:c(Te).scope.effectDate,"onUpdate:modelValue":t[12]||(t[12]=e=>c(Te).scope.effectDate=e),"disabled-date":Oe,"end-placeholder":c(ge)("endDate"),"range-separator":"至","start-placeholder":c(ge)("startDate"),type:"daterange"},null,8,["modelValue","end-placeholder","start-placeholder"])])),_:1},8,["label"]),y(i,{label:c(ge)("week")},{default:m((()=>[y(F,{modelValue:c(Te).scope.weeks,"onUpdate:modelValue":t[13]||(t[13]=e=>c(Te).scope.weeks=e)},{default:m((()=>[y(I,{key:"7",value:"7"},{default:m((()=>[g(f(c(ge)("sunday")),1)])),_:1}),y(I,{key:"1",value:"1"},{default:m((()=>[g(f(c(ge)("monday")),1)])),_:1}),y(I,{key:"2",value:"2"},{default:m((()=>[g(f(c(ge)("tuesday")),1)])),_:1}),y(I,{key:"3",value:"3"},{default:m((()=>[g(f(c(ge)("wednesday")),1)])),_:1}),y(I,{key:"4",value:"4"},{default:m((()=>[g(f(c(ge)("thursday")),1)])),_:1}),y(I,{key:"5",value:"5"},{default:m((()=>[g(f(c(ge)("friday")),1)])),_:1}),y(I,{key:"6",value:"6"},{default:m((()=>[g(f(c(ge)("saturday")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),y(i,{label:c(ge)("applicationChannel")},{default:m((()=>[y(u,{modelValue:c(Te).scope.channels,"onUpdate:modelValue":t[14]||(t[14]=e=>c(Te).scope.channels=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:c(ge)("select"),style:{width:"240px"},clearable:""},{default:m((()=>[(p(!0),b(h,null,v(c(_e),(e=>(p(),T(n,{key:e.channelCode,label:e.channelName,value:e.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1},8,["model","rules"])])),[[W,c(he)]])}}});function fe(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Info"}},priceStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Name"}},enterPriceStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the price strategy name"}},rule:{t:0,b:{t:2,i:[{t:3}],s:"Rule"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source Type"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"Please select level"}},level:{t:0,b:{t:2,i:[{t:3}],s:"Level"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},store:{t:0,b:{t:2,i:[{t:3}],s:"Hotel"}},group:{t:0,b:{t:2,i:[{t:3}],s:"Group"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Please select room type"}},selectAll:{t:0,b:{t:2,i:[{t:3}],s:"Select All"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"Check-In Type"}},allDayRoom:{t:0,b:{t:2,i:[{t:3}],s:"All-Day Room"}},hourlyRoom:{t:0,b:{t:2,i:[{t:3}],s:"Hourly Room"}},selectCheckinType:{t:0,b:{t:2,i:[{t:3}],s:"Please select check-in type"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"Order Source"}},unlimitedSource:{t:0,b:{t:2,i:[{t:3}],s:"Unlimited Source"}},priceRights:{t:0,b:{t:2,i:[{t:3}],s:"Price Rights"}},discountType:{t:0,b:{t:2,i:[{t:3}],s:"Discount Type"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"Discount"}},reduce:{t:0,b:{t:2,i:[{t:3}],s:"Reduce"}},fixedPrice:{t:0,b:{t:2,i:[{t:3}],s:"Fixed price"}},enterDiscountValue:{t:0,b:{t:2,i:[{t:3}],s:"Please enter discount value"}},priceMethod:{t:0,b:{t:2,i:[{t:3}],s:"Price Method"}},rightsType:{t:0,b:{t:2,i:[{t:3}],s:"Rights Type"}},delay:{t:0,b:{t:2,i:[{t:3}],s:"Delay"}},multi_point:{t:0,b:{t:2,i:[{t:3}],s:"Multiplier Points"}},enterMinutesRange:{t:0,b:{t:2,i:[{t:3}],s:"Please enter minutes range"}},multiPoint:{t:0,b:{t:2,i:[{t:3}],s:"Multi-Point"}},enterMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"Please enter multiplier"}},breakfast:{t:0,b:{t:2,i:[{t:3}],s:"Breakfast"}},numberOfPortions:{t:0,b:{t:2,i:[{t:3}],s:"Number of Portions"}},pricePerPortion:{t:0,b:{t:2,i:[{t:3}],s:"Price per Portion"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"Yuan"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"Delete"}},add:{t:0,b:{t:2,i:[{t:3}],s:"Add"}},applicationScope:{t:0,b:{t:2,i:[{t:3}],s:"Scope"}},effectDate:{t:0,b:{t:2,i:[{t:3}],s:"Effective Date"}},week:{t:0,b:{t:2,i:[{t:3}],s:"Week"}},sunday:{t:0,b:{t:2,i:[{t:3}],s:"Sun"}},monday:{t:0,b:{t:2,i:[{t:3}],s:"Mon"}},tuesday:{t:0,b:{t:2,i:[{t:3}],s:"Tue"}},wednesday:{t:0,b:{t:2,i:[{t:3}],s:"Wed"}},thursday:{t:0,b:{t:2,i:[{t:3}],s:"Thu"}},friday:{t:0,b:{t:2,i:[{t:3}],s:"Fri"}},saturday:{t:0,b:{t:2,i:[{t:3}],s:"Sat"}},applicationChannel:{t:0,b:{t:2,i:[{t:3}],s:"Channels"}},select:{t:0,b:{t:2,i:[{t:3}],s:"Please select"}},"0.88 is 88":{t:0,b:{t:2,i:[{t:3}],s:"0.88 means 88% discount"}},enterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter strategy name"}},enterEffectDate:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the effective date"}},modificationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Added successfully"}},pleaseEnterDiscountValue:{t:0,b:{t:2,i:[{t:3}],s:"Please enter discount value"}},pleaseEnterMinutesRange:{t:0,b:{t:2,i:[{t:3}],s:"Please enter minutes range"}},pleaseEnterMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"Please enter multiplier"}},pleaseEnterPricePerPortion:{t:0,b:{t:2,i:[{t:3}],s:"Please enter price per portion"}},yuanPerPortion:{t:0,b:{t:2,i:[{t:3}],s:"Yuan per portion"}},minute:{t:0,b:{t:2,i:[{t:3}],s:"Min"}},times:{t:0,b:{t:2,i:[{t:3}],s:"Times"}},pieces:{t:0,b:{t:2,i:[{t:3}],s:"Pieces"}},member:{t:0,b:{t:2,i:[{t:3}],s:"Member"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"Agent"}},protocol:{t:0,b:{t:2,i:[{t:3}],s:"Protocol"}},0:{t:0,b:{t:2,i:[{t:3}],s:"Unlimited"}},walk_in:{t:0,b:{t:2,i:[{t:3}],s:"Walk-in"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"Start Date"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"End Date"}},to:{t:0,b:{t:2,i:[{t:3}],s:"To"}},salePrice:{t:0,b:{t:2,i:[{t:3}],s:"Sale Price"}},priceCannot:{t:0,b:{t:2,i:[{t:3}],s:"The room type price cannot be empty!"}}},"zh-cn":{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"策略信息"}},priceStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"价格策略名称"}},enterPriceStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"请输入房价策略名称"}},rule:{t:0,b:{t:2,i:[{t:3}],s:"规则"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"请选择级别"}},level:{t:0,b:{t:2,i:[{t:3}],s:"级别"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},store:{t:0,b:{t:2,i:[{t:3}],s:"门店"}},group:{t:0,b:{t:2,i:[{t:3}],s:"集团"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"请选择房型"}},selectAll:{t:0,b:{t:2,i:[{t:3}],s:"选择全部"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},allDayRoom:{t:0,b:{t:2,i:[{t:3}],s:"全天房"}},hourlyRoom:{t:0,b:{t:2,i:[{t:3}],s:"钟点房"}},selectCheckinType:{t:0,b:{t:2,i:[{t:3}],s:"请选择入住类型"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"订单来源"}},unlimitedSource:{t:0,b:{t:2,i:[{t:3}],s:"不限来源"}},priceRights:{t:0,b:{t:2,i:[{t:3}],s:"价格权益"}},discountType:{t:0,b:{t:2,i:[{t:3}],s:"优惠类"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"房价折扣"}},reduce:{t:0,b:{t:2,i:[{t:3}],s:"房价立减"}},fixedPrice:{t:0,b:{t:2,i:[{t:3}],s:"固定价格"}},enterDiscountValue:{t:0,b:{t:2,i:[{t:3}],s:"请输入折扣值"}},priceMethod:{t:0,b:{t:2,i:[{t:3}],s:"价格方式"}},rightsType:{t:0,b:{t:2,i:[{t:3}],s:"权益类"}},delay:{t:0,b:{t:2,i:[{t:3}],s:"延迟离店"}},multi_point:{t:0,b:{t:2,i:[{t:3}],s:"积分倍数"}},enterMinutesRange:{t:0,b:{t:2,i:[{t:3}],s:"请输入分钟范围"}},multiPoint:{t:0,b:{t:2,i:[{t:3}],s:"积分倍数"}},enterMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"请输入倍数"}},breakfast:{t:0,b:{t:2,i:[{t:3}],s:"早餐"}},numberOfPortions:{t:0,b:{t:2,i:[{t:3}],s:"份数"}},pricePerPortion:{t:0,b:{t:2,i:[{t:3}],s:"每份价格"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"元"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"删除"}},add:{t:0,b:{t:2,i:[{t:3}],s:"添加"}},applicationScope:{t:0,b:{t:2,i:[{t:3}],s:"应用范围"}},effectDate:{t:0,b:{t:2,i:[{t:3}],s:"生效日期"}},week:{t:0,b:{t:2,i:[{t:3}],s:"星期"}},sunday:{t:0,b:{t:2,i:[{t:3}],s:"周日"}},monday:{t:0,b:{t:2,i:[{t:3}],s:"周一"}},tuesday:{t:0,b:{t:2,i:[{t:3}],s:"周二"}},wednesday:{t:0,b:{t:2,i:[{t:3}],s:"周三"}},thursday:{t:0,b:{t:2,i:[{t:3}],s:"周四"}},friday:{t:0,b:{t:2,i:[{t:3}],s:"周五"}},saturday:{t:0,b:{t:2,i:[{t:3}],s:"周六"}},applicationChannel:{t:0,b:{t:2,i:[{t:3}],s:"应用渠道"}},select:{t:0,b:{t:2,i:[{t:3}],s:"请选择"}},"0.88 is 88":{t:0,b:{t:2,i:[{t:3}],s:"0.88表示88折"}},enterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"请输入策略名称"}},enterEffectDate:{t:0,b:{t:2,i:[{t:3}],s:"请输入生效日期"}},modificationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"新增成功"}},pleaseEnterDiscountValue:{t:0,b:{t:2,i:[{t:3}],s:"请输入折扣值"}},pleaseEnterMinutesRange:{t:0,b:{t:2,i:[{t:3}],s:"请输入分钟范围"}},pleaseEnterMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"请输入倍数"}},pleaseEnterPricePerPortion:{t:0,b:{t:2,i:[{t:3}],s:"请输入每份价格"}},yuanPerPortion:{t:0,b:{t:2,i:[{t:3}],s:"元/份"}},minute:{t:0,b:{t:2,i:[{t:3}],s:"分钟"}},times:{t:0,b:{t:2,i:[{t:3}],s:"倍"}},pieces:{t:0,b:{t:2,i:[{t:3}],s:"份"}},member:{t:0,b:{t:2,i:[{t:3}],s:"会员"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},protocol:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}},0:{t:0,b:{t:2,i:[{t:3}],s:"不限"}},walk_in:{t:0,b:{t:2,i:[{t:3}],s:"散客"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"开始日期"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"结束日期"}},to:{t:0,b:{t:2,i:[{t:3}],s:"至"}},salePrice:{t:0,b:{t:2,i:[{t:3}],s:"售价"}},priceCannot:{t:0,b:{t:2,i:[{t:3}],s:"房型售价不能为空！"}}},km:{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានអំពីយុទ្ធសាស្ត្រ"}},priceStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះយុទ្ធសាស្ត្រតម្លៃ"}},enterPriceStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រតម្លៃ"}},rule:{t:0,b:{t:2,i:[{t:3}],s:"ច្បាប់"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទប្រភពភ្ញៀវ"}},selectLevel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកម្រិត"}},level:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិត"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},store:{t:0,b:{t:2,i:[{t:3}],s:"ហាង"}},group:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុម"}},selectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទបន្ទប់"}},selectAll:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសទាំងអស់"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលស្នាក់នៅ"}},allDayRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ពេញមួយថ្ងៃ"}},hourlyRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ម៉ោង"}},selectCheckinType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទចូលស្នាក់នៅ"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពការបញ្ជាទិញ"}},unlimitedSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពគ្មានដែនកំណត់"}},priceRights:{t:0,b:{t:2,i:[{t:3}],s:"សិទ្ធិតម្លៃ"}},discountType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបញ្ចុះតម្លៃ"}},discount:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចុះតម្លៃ"}},reduce:{t:0,b:{t:2,i:[{t:3}],s:"កាត់បន្ថយតម្លៃ"}},fixedPrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃថេរ"}},enterDiscountValue:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលតម្លៃបញ្ចុះតម្លៃ"}},priceMethod:{t:0,b:{t:2,i:[{t:3}],s:"វិធីសាស្ត្រតម្លៃ"}},rightsType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទសិទ្ធិ"}},delay:{t:0,b:{t:2,i:[{t:3}],s:"ពន្យាពេលចាកចេញ"}},multi_point:{t:0,b:{t:2,i:[{t:3}],s:"ពិន្ទុគុណ"}},enterMinutesRange:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលរយៈពេលជានាទី"}},multiPoint:{t:0,b:{t:2,i:[{t:3}],s:"ពិន្ទុគុណ"}},enterMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលពិន្ទុគុណ"}},breakfast:{t:0,b:{t:2,i:[{t:3}],s:"អាហារពេលព្រឹក"}},numberOfPortions:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនចំណែក"}},pricePerPortion:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃក្នុងមួយចំណែក"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"យ៉ាន"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"លុប"}},add:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែម"}},applicationScope:{t:0,b:{t:2,i:[{t:3}],s:"វិសាលភាព"}},effectDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទដែលមានប្រសិទ្ធភាព"}},week:{t:0,b:{t:2,i:[{t:3}],s:"សប្តាហ៍"}},sunday:{t:0,b:{t:2,i:[{t:3}],s:"អាទិត្យ"}},monday:{t:0,b:{t:2,i:[{t:3}],s:"ច័ន្ទ"}},tuesday:{t:0,b:{t:2,i:[{t:3}],s:"អង្គារ"}},wednesday:{t:0,b:{t:2,i:[{t:3}],s:"ពុធ"}},thursday:{t:0,b:{t:2,i:[{t:3}],s:"ព្រហស្បតិ៍"}},friday:{t:0,b:{t:2,i:[{t:3}],s:"សុក្រ"}},saturday:{t:0,b:{t:2,i:[{t:3}],s:"សៅរ៍"}},applicationChannel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},select:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើស"}},"0.88 is 88":{t:0,b:{t:2,i:[{t:3}],s:"0.88 មានន័យថា 88% បញ្ចុះតម្លៃ"}},enterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ"}},enterEffectDate:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលកាលបរិច្ឆេទដែលមានប្រសិទ្ធភាព"}},modificationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានបន្ថែមដោយជោគជ័យ"}},pleaseEnterDiscountValue:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលតម្លៃបញ្ចុះតម្លៃ"}},pleaseEnterMinutesRange:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលរយៈពេលជានាទី"}},pleaseEnterMultiplier:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលពិន្ទុគុណ"}},pleaseEnterPricePerPortion:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលតម្លៃក្នុងមួយចំណែក"}},yuanPerPortion:{t:0,b:{t:2,i:[{t:3}],s:"យ៉ានក្នុងមួយចំណែក"}},minute:{t:0,b:{t:2,i:[{t:3}],s:"នាទី"}},times:{t:0,b:{t:2,i:[{t:3}],s:"ដង"}},pieces:{t:0,b:{t:2,i:[{t:3}],s:"ចំណែក"}},member:{t:0,b:{t:2,i:[{t:3}],s:"សមាជិក"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកដើរតួ"}},protocol:{t:0,b:{t:2,i:[{t:3}],s:"អង្គការសន្យា"}},0:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានដែនកំណត់"}},walk_in:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវដែលមិនបានកក់ទុក"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទចាប់ផ្តើម"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទបញ្ចប់"}},to:{t:0,b:{t:2,i:[{t:3}],s:"ដល់"}},salePrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃលក់"}},priceCannot:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃបន្ទប់មិនអាចទទេបានទេ!"}}}}})}fe(ge);const he=ne(ge,[["__scopeId","data-v-74b87f6c"]]);export{he as default};
//# sourceMappingURL=createStrategy-BbXwUKlP.js.map
