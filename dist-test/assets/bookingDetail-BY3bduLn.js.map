{"version": 3, "file": "bookingDetail-BY3bduLn.js", "sources": ["../../src/views/order/info/components/orderdetail/bookingDetail.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"orderModify\": \"Modify Order\",\r\n    \"cancelOrder\": \"Cancel Order\",\r\n    \"sendSMS\": \"Send SMS\",\r\n    \"confirmModify\": \"Confirm Modification\",\r\n    \"cancelModify\": \"Cancel Modification\",\r\n    \"orderInfo\": \"Order Information\",\r\n    \"bookingNumber\": \"Pre-order No\",\r\n    \"channel\": \"Channel\",\r\n    \"checkinType\": \"CheckIn Type\",\r\n    \"hourDuration\": \"Hourly Duration\",\r\n    \"guestSource\": \"Guest Source\",\r\n    \"name\": \"Name\",\r\n    \"arrivalTime\": \"Arrival\",\r\n    \"departureTime\": \"Departure\",\r\n    \"bookingDays\": \"Days\",\r\n    \"retain\": \"Retain\",\r\n    \"orderSource\": \"Order Source\",\r\n    \"guestInfo\": \"Guest Information\",\r\n    \"booker\": \"<PERSON>\",\r\n    \"bookerPhone\": \"Booker Phone\",\r\n    \"checkinPerson\": \"Person\",\r\n    \"checkinPhone\": \"Phone\",\r\n    \"OTAExternal\": \"External\",\r\n    \"guarantyMethod\": \"Guaranty\",\r\n    \"salesperson\": \"Salesperson\",\r\n    \"orderRemark\": \"Remark\",\r\n    \"inputRemark\": \"Please enter remarks\",\r\n    \"externalOrderRemark\": \"Ext. Remark\",\r\n    \"checkin\": \"Check-in\",\r\n    \"rooms\": \"Rooms\",\r\n    \"roomUnit\": \"roomUnit\",\r\n    \"autoArrangeRoom\": \"Auto Arrange Room\",\r\n    \"roomUpgrade\": \"Room Upgrade\",\r\n    \"roomNumber\": \"Room No\",\r\n    \"arrangeRoom\": \"Arr. Room\",\r\n    \"roomType\": \"Room Type\",\r\n    \"roomPrice\": \"Room Price\",\r\n    \"breakfastCopies\": \"Breakfast Copies\",\r\n    \"operation\": \"Actions\",\r\n    \"viewDetails\": \"View Details\",\r\n    \"noArrangeRoom\": \"No rooms arranged\",\r\n    \"discountPrice\": \"Discount Price\",\r\n    \"salePrice\": \"Sale Price\",\r\n    \"discountSalePrice\": \"Disc. Price/Sale Price\",\r\n    \"availableRooms\": \"Avail. Rooms\",\r\n    \"availableOverbook\": \"OBR\",\r\n    \"bookingRooms\": \"Booking Rooms\",\r\n    \"changePrice\": \"Change Price\",\r\n    \"orderUpdateSuccess\": \"Order updated successfully\",\r\n    \"reduceRoomWarning\": \"Already arranged {roomCount} rooms. To reduce the number of bookings, please remove arranged rooms first.\",\r\n    \"setCheckinCheckoutTime\": \"Please set the arrival and departure times first, and ensure that the departure time is later than the arrival time.\",\r\n    \"selectRoomNumber\": \"Please select the number of rooms for the current room type first.\",\r\n    \"confirmAutoArrange\": \"Are you sure you want to auto-arrange rooms?\",\r\n    \"confirm\": \"Confirm\",\r\n    \"cancel\": \"Cancel\",\r\n    \"arrangeSuccess\": \"Rooms arranged successfully\",\r\n    \"joinRoom\": \"Join Room\",\r\n    \"quitJoinRoom\": \"Quit Join Room\",\r\n    \"outJoinRoom\": \"Quit Join Room\",\r\n    \"selectedRoom\": \"Selected Room:\",\r\n    \"confirmOutJoinRoom\": \"Are you sure you want to quit the joint room?\",\r\n    \"unlinkRoomSuccess\": \"Successfully quit joint room\",\r\n    \"save\": \"Save\",\r\n    \"edit\": \"Edit\",\r\n    \"orderRemarkUpdateSuccess\": \"Order remark updated successfully\",\r\n    \"externalOrderNoUpdateSuccess\": \"External order number updated successfully\",\r\n    \"externalOrderRemarkUpdateSuccess\": \"External order remark updated successfully\",\r\n    \"orderPastCheckoutTimeWarning\": \"Order has passed the latest checkout time, please modify the order arrival and departure times\",\r\n    \"roomUpgradeSuccess\": \"Room upgrade successful!\",\r\n    \"freeUpgrade\": \"Free Upgrade\",\r\n    \"currentRoomType\": \"Current Room Type\",\r\n    \"upgradeRoomType\": \"Upgrade Room Type\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"orderModify\": \"订单修改\",\r\n    \"cancelOrder\": \"取消订单\",\r\n    \"sendSMS\": \"发送短信\",\r\n    \"confirmModify\": \"确认修改\",\r\n    \"cancelModify\": \"取消修改\",\r\n    \"orderInfo\": \"订单信息\",\r\n    \"bookingNumber\": \"预订单号\",\r\n    \"channel\": \"渠道\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"hourDuration\": \"钟点时长\",\r\n    \"guestSource\": \"客源\",\r\n    \"name\": \"姓名\",\r\n    \"arrivalTime\": \"预抵时间\",\r\n    \"departureTime\": \"预离时间\",\r\n    \"bookingDays\": \"预订天数\",\r\n    \"retain\": \"保留\",\r\n    \"orderSource\": \"订单来源\",\r\n    \"guestInfo\": \"客人信息\",\r\n    \"booker\": \"预订人\",\r\n    \"bookerPhone\": \"预订人电话\",\r\n    \"checkinPerson\": \"入住人\",\r\n    \"checkinPhone\": \"入住人电话\",\r\n    \"OTAExternal\": \"外部订单号\",\r\n    \"guarantyMethod\": \"担保方式\",\r\n    \"salesperson\": \"销售员\",\r\n    \"orderRemark\": \"订单备注\",\r\n    \"inputRemark\": \"请输入备注\",\r\n    \"externalOrderRemark\": \"外部订单备注\",\r\n    \"checkin\": \"办理入住\",\r\n    \"rooms\": \"房间\",\r\n    \"roomUnit\": \"间\",\r\n    \"autoArrangeRoom\": \"AI自动排房\",\r\n    \"roomUpgrade\": \"房型升级\",\r\n    \"roomNumber\": \"房号\",\r\n    \"arrangeRoom\": \"排房\",\r\n    \"roomType\": \"房型\",\r\n    \"roomPrice\": \"房价\",\r\n    \"breakfastCopies\": \"赠早/份\",\r\n    \"operation\": \"操作\",\r\n    \"viewDetails\": \"查看详单\",\r\n    \"noArrangeRoom\": \"未排房\",\r\n    \"discountPrice\": \"优惠价\",\r\n    \"salePrice\": \"售价\",\r\n    \"discountSalePrice\": \"优惠价/销售价\",\r\n    \"availableRooms\": \"可售数\",\r\n    \"availableOverbook\": \"可超数\",\r\n    \"bookingRooms\": \"预订间数\",\r\n    \"changePrice\": \"改价\",\r\n    \"orderUpdateSuccess\": \"修改预订单成功\",\r\n    \"reduceRoomWarning\": \"已排房{roomCount}间,若要减少预订间数，请先删减排房\",\r\n    \"setCheckinCheckoutTime\": \"请先设置预抵和预离时间，并使预离时间晚于预抵时间.\",\r\n    \"selectRoomNumber\": \"请先选择当前房型预订间数\",\r\n    \"confirmAutoArrange\": \"确定AI自动排房吗?\",\r\n    \"confirm\": \"确认\",\r\n    \"cancel\": \"取消\",\r\n    \"arrangeSuccess\": \"排房成功\",\r\n    \"joinRoom\": \"加入联房\",\r\n    \"quitJoinRoom\": \"退出联房\",\r\n    \"outJoinRoom\": \"退出联房\",\r\n    \"selectedRoom\": \"选中房间：\",\r\n    \"confirmOutJoinRoom\": \"是否确定退出联房？\",\r\n    \"unlinkRoomSuccess\": \"退出联房成功\",\r\n    \"save\": \"保存\",\r\n    \"edit\": \"修改\",\r\n    \"orderRemarkUpdateSuccess\": \"订单备注修改成功\",\r\n    \"externalOrderNoUpdateSuccess\": \"外部订单号修改成功\",\r\n    \"externalOrderRemarkUpdateSuccess\": \"外部订单备注修改成功\",\r\n    \"orderPastCheckoutTimeWarning\": \"订单已过最晚预离时间，请修改订单预抵预离时间\",\r\n    \"roomUpgradeSuccess\": \"房型升级成功！\",\r\n    \"freeUpgrade\": \"免费升级\",\r\n    \"currentRoomType\": \"当前房型\",\r\n    \"upgradeRoomType\": \"升级房型\"\r\n  },\r\n  \"km\": {\r\n    \"orderModify\": \"កែប្រែការកម្មង់\",\r\n    \"cancelOrder\": \"បោះបង់ការកម្មង់\",\r\n    \"sendSMS\": \"ផ្ញើសារ SMS\",\r\n    \"confirmModify\": \"បញ្ជាក់ការកែប្រែ\",\r\n    \"cancelModify\": \"បោះបង់ការកែប្រែ\",\r\n    \"orderInfo\": \"ព័ត៌មានការកម្មង់\",\r\n    \"bookingNumber\": \"លេខការកម្មង់\",\r\n    \"channel\": \"ឆានែល\",\r\n    \"checkinType\": \"ប្រភេទចូលស្នាក់\",\r\n    \"hourDuration\": \"រយៈពេលជាម៉ោង\",\r\n    \"guestSource\": \"ប្រភពភ្ញៀវ\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"arrivalTime\": \"ពេលវេលាមកដល់\",\r\n    \"departureTime\": \"ពេលចាកចេញ\",\r\n    \"bookingDays\": \"ចំនួនថ្ងៃ\",\r\n    \"retain\": \"រក្សាទុក\",\r\n    \"orderSource\": \"ប្រភពការកម្មង់\",\r\n    \"guestInfo\": \"ព័ត៌មានភ្ញៀវ\",\r\n    \"booker\": \"អ្នកកម្មង់\",\r\n    \"bookerPhone\": \"ទូរស័ព្ទអ្នកកម្មង់\",\r\n    \"checkinPerson\": \"អ្នកចូលស្នាក់\",\r\n    \"checkinPhone\": \"ទូរស័ព្ទចូលស្នាក់\",\r\n    \"OTAExternal\": \"លេខកម្មង់ក្រៅ\",\r\n    \"guarantyMethod\": \"វិធីធានា\",\r\n    \"salesperson\": \"អ្នកលក់\",\r\n    \"orderRemark\": \"ចំណាំការកម្មង់\",\r\n    \"inputRemark\": \"សូមបញ្ចូលចំណាំ\",\r\n    \"externalOrderRemark\": \"ចំណាំការកម្មង់ខាងក្រៅ\",\r\n    \"checkin\": \"ចូលស្នាក់នៅ\",\r\n    \"rooms\": \"បន្ទប់\",\r\n    \"roomUnit\": \"បន្ទប់\",\r\n    \"autoArrangeRoom\": \"រៀបចំបន្ទប់ដោយស្វ័យប្រវត្តិ\",\r\n    \"roomUpgrade\": \"ធ្វើឱ្យបន្ទប់ប្រសើរឡើង\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"arrangeRoom\": \"រៀបចំបន្ទប់\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"roomPrice\": \"តម្លៃបន្ទប់\",\r\n    \"breakfastCopies\": \"អាហារព្រឹក/ចំនួន\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"viewDetails\": \"មើលព័ត៌មានលម្អិត\",\r\n    \"noArrangeRoom\": \"មិនមានការរៀបចំបន្ទប់\",\r\n    \"discountPrice\": \"តម្លៃបញ្ចុះតម្លៃ\",\r\n    \"salePrice\": \"តម្លៃលក់\",\r\n    \"discountSalePrice\": \"តម្លៃបញ្ចុះតម្លៃ/តម្លៃលក់\",\r\n    \"availableRooms\": \"បន្ទប់អាចលក់\",\r\n    \"availableOverbook\": \"អាចលក់លើស\",\r\n    \"bookingRooms\": \"បន្ទប់កម្មង់\",\r\n    \"changePrice\": \"ផ្លាស់ប្តូរតម្លៃ\",\r\n    \"orderUpdateSuccess\": \"កែប្រែការកម្មង់ដោយជោគជ័យ\",\r\n    \"reduceRoomWarning\": \"បានរៀបចំបន្ទប់ {roomCount} បន្ទប់ ប្រសិនបើចង់កាត់បន្ថយចំនួនបន្ទប់ដែលបានកម្មង់ សូមលុបការរៀបចំបន្ទប់ជាមុនសិន។\",\r\n    \"setCheckinCheckoutTime\": \"សូមកំណត់ពេលវេលាមកដល់ និងពេលវេលាចាកចេញជាមុនសិន ហើយធ្វើឱ្យពេលវេលាចាកចេញក្រោយពេលវេលាមកដល់។\",\r\n    \"selectRoomNumber\": \"សូមជ្រើសរើសចំនួនបន្ទប់សម្រាប់ប្រភេទបន្ទប់បច្ចុប្បន្នជាមុនសិន។\",\r\n    \"confirmAutoArrange\": \"តើអ្នកប្រាកដថាចង់រៀបចំបន្ទប់ដោយស្វ័យប្រវត្តិមែនទេ?\",\r\n    \"confirm\": \"បញ្ជាក់\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"arrangeSuccess\": \"រៀបចំបន្ទប់ដោយជោគជ័យ\",\r\n    \"joinRoom\": \"ចូលរួមបន្ទប់\",\r\n    \"quitJoinRoom\": \"ចាកចេញពីបន្ទប់រួម\",\r\n    \"outJoinRoom\": \"ចាកចេញពីបន្ទប់រួម\",\r\n    \"selectedRoom\": \"បន្ទប់ដែលបានជ្រើសរើស៖\",\r\n    \"confirmOutJoinRoom\": \"តើអ្នកប្រាកដថាចង់ចាកចេញពីបន្ទប់រួមទេ?\",\r\n    \"unlinkRoomSuccess\": \"ចាកចេញពីបន្ទប់រួមដោយជោគជ័យ\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"edit\": \"កែប្រែ\",\r\n    \"orderRemarkUpdateSuccess\": \"ចំណាំការកម្មង់ត្រូវបានកែប្រែដោយជោគជ័យ\",\r\n    \"externalOrderNoUpdateSuccess\": \"លេខកម្មង់ខាងក្រៅត្រូវបានកែប្រែដោយជោគជ័យ\",\r\n    \"externalOrderRemarkUpdateSuccess\": \"ចំណាំការកម្មង់ខាងក្រៅត្រូវបានកែប្រែដោយជោគជ័យ\",\r\n    \"orderPastCheckoutTimeWarning\": \"ការកម្មង់បានកន្លងផុតពេលវេលាចាកចេញចុងក្រោយ សូមកែប្រែពេលវេលាមកដល់ និងចាកចេញនៃការកម្មង់\",\r\n    \"roomUpgradeSuccess\": \"ការធ្វើឱ្យបន្ទប់ប្រសើរឡើងបានជោគជ័យ!\",\r\n    \"freeUpgrade\": \"ធ្វើឱ្យប្រសើរឡើងដោយឥតគិតថ្លៃ\",\r\n    \"currentRoomType\": \"ប្រភេទបន្ទប់បច្ចុប្បន្ន\",\r\n    \"upgradeRoomType\": \"ប្រភេទបន្ទប់ដែលធ្វើឱ្យប្រសើរឡើង\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel } from '@/models'\r\nimport { bookApi, dictDataApi, orderApi, serverTimeApi } from '@/api/modules/index'\r\nimport { CheckinType, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_ID_TYPE, HOUR_TYPE_NAME, OrderType } from '@/models'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { deduplicateByProperty, getAuth, removePropertyFromArray } from '@/utils'\r\nimport ymdate, { ymdateHMS } from '@/utils/timeutils'\r\nimport CancelPopUP from '@/views/order/component/cancelPopUP.vue'\r\nimport ArrangeRoomsDialog from '@/views/room/components/arrangeRooms/arrangeRooms.vue'\r\nimport LianFang from '@/views/room/components/lianFang/index.vue'\r\nimport RoomEditPrice from '@/views/room/components/roomEditPrice/index.vue'\r\nimport dayjs from 'dayjs'\r\n\r\nimport CheckModal from './checkModal.vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    bookNo: string[] // 预订单号\r\n    state: string // 状态\r\n    isEntryAccount: string // 是否可以入账操作\r\n  }>(),\r\n  {\r\n    bookNo: () => [],\r\n    state: '',\r\n    isEntryAccount: '0',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n  refresh: []\r\n  seeDetailed: [value: object]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n/** 订单详情赋值 */\r\nconst dt = ref({\r\n  /** 订单号 */\r\n  orderNo: '',\r\n  /** 外部订单号 */\r\n  outOrderNo: '',\r\n  /** 客人姓名 */\r\n  guestName: '',\r\n  /** 如果是团队订单，这里写团队名称 */\r\n  teamName: '',\r\n  /** 拼音 */\r\n  pinyin: '',\r\n  /** 电话 */\r\n  phone: '',\r\n  /** 性别 */\r\n  sex: '',\r\n  /** 证件类型 */\r\n  idType: '',\r\n  /** 证件号码 */\r\n  idNo: '',\r\n  /** 渠道代码 */\r\n  channelCode: '',\r\n  /** 渠道名称 */\r\n  channelName: '',\r\n  /** 订单来源 */\r\n  orderSource: '',\r\n  /** 地址 */\r\n  address: '',\r\n  /** 民族 */\r\n  nation: '',\r\n  orderSourceName: '',\r\n  /** 入住类型 */\r\n  checkinType: '0',\r\n  /** 入住类型名称 */\r\n  checkinTypeName: '',\r\n  /** 入住时间 */\r\n  planCheckinTime: '',\r\n  /** 预离时间 */\r\n  planCheckoutTime: '',\r\n  /** 房型代码 */\r\n  rtCode: '',\r\n  rtName: '',\r\n  /** 房间代码 */\r\n  rCode: '',\r\n  /** 房号 */\r\n  rNo: '',\r\n  /** 客源类型 */\r\n  guestSrcType: '',\r\n  guestSrcTypeName: '',\r\n  /** 预订单号 */\r\n  bookNo: '',\r\n  /** 订单类型 */\r\n  orderType: '',\r\n  /** 绑定代码 */\r\n  bindCode: '',\r\n  /** 是否主订单 是否为主订单（0否 1是） */\r\n  isMain: '',\r\n  /** 账务状态 账务状态（1已结  2未结） */\r\n  accState: '',\r\n  /** 是否已开发票 */\r\n  isInvoice: '',\r\n  /** 早餐券数量 */\r\n  bkTicketNum: 0,\r\n  /** 活动代码 */\r\n  activityCode: '',\r\n  /** 房价策略代码 */\r\n  priceStrategyCode: '',\r\n  /** 订单状态 */\r\n  state: '',\r\n  /** 离店状态 */\r\n  checkoutState: '',\r\n  /** 是否已制卡 */\r\n  isMadeCard: '',\r\n  /** 开房班次 */\r\n  checkinShiftNo: '',\r\n  /** 开房操作者 */\r\n  checkinOperator: '',\r\n  /** 退房班次 */\r\n  checkoutShiftNo: '',\r\n  /** 退房时间 */\r\n  checkoutTime: '',\r\n  /** 退房操作者 */\r\n  checkoutOperator: '',\r\n  /** 结账班次 */\r\n  payShiftNo: '',\r\n  /** 结账时间 */\r\n  payTime: '',\r\n  /** 结账操作者 */\r\n  payOperator: '',\r\n  /** 销售者 */\r\n  seller: '',\r\n  /** 总消费 */\r\n  totalFee: 0,\r\n  /** 金额(余额) */\r\n  balance: 0,\r\n  bookRooms: [] as any,\r\n  prices: [] as {\r\n    /** 房型代码 */\r\n    rtCode: string\r\n    /** 房型名称 */\r\n    rtName: string\r\n    /** 房间代码 */\r\n    rCode: string\r\n    /** 房号 */\r\n    rNo: string\r\n    /** 原始价格 */\r\n    price: number\r\n    /** 调整后的价格 */\r\n    vipPrice: number\r\n    /** 价格类型 0 放盘价 1 手工价 */\r\n    priceType: string\r\n    /** 价格开始时间 */\r\n    priceStartTime: string\r\n    /** 价格结束时间 */\r\n    priceEndTime: string\r\n    /** 是否过夜审 0:否 1：是 */\r\n    isNight: string\r\n  }[],\r\n  /** 欠费状态 */\r\n  arrState: '',\r\n  /** 是否交押金 */\r\n  deposit: '',\r\n  /** 订单备注 */\r\n  remark: '',\r\n  /** 天数 */\r\n  days: '',\r\n  /** 标签 */\r\n  tags: {\r\n    /** 叫醒服务 isEnable: 0:否 1：是 time:叫醒时间 */\r\n    awaken: { isEnable: '', time: '' },\r\n    /** 是否保密房 0:否 1：是 */\r\n    secrecy: '',\r\n    /** 是否免打扰 0:否 1：是 */\r\n    notDisturbing: '',\r\n  },\r\n  summary: {\r\n    /** 房价 */\r\n    price: 0,\r\n    /** 消费 */\r\n    consume: 0,\r\n    /** 付款 */\r\n    payment: 0,\r\n    /** 预授权 */\r\n    preAuth: 0,\r\n    /** 优惠券 */\r\n    discounts: 0,\r\n    /** 余额 */\r\n    balance: 0,\r\n  },\r\n  bookType: '',\r\n  hourCode: '',\r\n  contact: '',\r\n  checkinPerson: '',\r\n  checkinPhone: '',\r\n  retainTime: '',\r\n  guarantyStyleName: '',\r\n  outOrderRemark: '',\r\n  guestCode: '',\r\n})\r\n/** 列表查询条件 */\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n/** 服务器时间 */\r\nconst serverTime = ref('')\r\n/** 今天价格 */\r\nconst todayPrice = ref('')\r\n/** 入离时间 */\r\nconst checkInOutTime = ref('')\r\n/** 证件类型列表 */\r\nconst idTypes = ref<DictDataModel[]>([])\r\n/** 入住类型列表 */\r\nconst checkinTypes = ref<DictDataModel[]>([])\r\n/** 客源类型列表 */\r\nconst guestSrcTypes = ref<DictDataModel[]>([])\r\n/** 取消订单弹窗 */\r\nconst upVisible = ref(false)\r\n/** 房型 */\r\nconst rooms = ref([])\r\n/** 修改订单 */\r\nconst editDetil = ref(false)\r\n/** 改价 */\r\nconst changePrice = ref(true)\r\n/** 打开的日期选择器计数 */\r\nconst openDatePickersCount = ref(0)\r\n/** 日期选择器是否处于未确认状态 */\r\nconst datePickerUnconfirmed = computed(() => openDatePickersCount.value > 0)\r\nconst form = ref({\r\n  dataList: [] as any[],\r\n})\r\n/** 是否排房 */\r\nconst isAlone = ref(false)\r\n/** 修改表格加载状态 */\r\nconst tableLoading = ref(false)\r\n/** 办理入住表格加载状态 */\r\nconst bookRoomsLoading = ref(false)\r\n/** 展开办理入住行的 keys 数组 */\r\nconst expandedRows = ref([])\r\n/** 升级房型弹窗 */\r\nconst easyDialogRef = ref()\r\n/** 获取form表格ref */\r\nconst easyFormRef = ref()\r\n/** 弹窗内容(初始化) */\r\nconst _modelForm = reactive({\r\n  ...queryParams,\r\n  /** 升级方式 */\r\n  upgradeMethod: 0,\r\n  /** 预订单号 */\r\n  bookNo: props.bookNo[0],\r\n  /** 房型代码 */\r\n  rtCode: '',\r\n  /** 升级房型代码 */\r\n  upgradeRtCode: '',\r\n})\r\n/** 弹窗内容 */\r\nconst modelForm = ref({ ..._modelForm })\r\n/** 当前房型 */\r\nconst roomOptions = ref([])\r\ninterface updateRoomTypes {\r\n  /** 可超数 */\r\n  canOverNum?: number\r\n  /** 可售数 */\r\n  canSellNum?: number\r\n  /** 房型代码 */\r\n  rtCode?: string\r\n  /** 房型名称 */\r\n  rtName?: string\r\n}\r\n/** 升级房型 */\r\nconst updateRoomOptions = ref<updateRoomTypes[]>([])\r\n\r\n/** form表单 */\r\nconst ruleFieldList = reactive<Form.FieldItem[]>([\r\n  {\r\n    label: t('roomUpgrade'),\r\n    type: 'radio',\r\n    field: 'upgradeMethod',\r\n    options: {\r\n      data: [\r\n        { value: 0, label: t('freeUpgrade') },\r\n        // { value: 1, label: '补差价' },\r\n      ],\r\n      radioOptions: {\r\n        'onUpdate:modelValue': (newValue) => {\r\n          getRoomtype()\r\n        },\r\n      },\r\n    },\r\n  },\r\n  {\r\n    label: t('currentRoomType'),\r\n    field: 'rtCode',\r\n    slot: 'currentRoomType',\r\n  },\r\n  {\r\n    label: t('upgradeRoomType'),\r\n    field: 'upgradeRtCode',\r\n    slot: 'upgradeRtCode',\r\n  },\r\n  { slot: 'show', field: '' },\r\n])\r\n\r\nonMounted(async () => {\r\n  await Promise.all([initConstants(), getOrderDetail(), getServerTime()])\r\n  tp()\r\n  checkInOutTime.value = dayjs(dt.value?.planCheckinTime).format('MM/DD HH:mm').concat(' - ').concat(dayjs(dt.value?.planCheckoutTime).format('MM/DD HH:mm'))\r\n  bookingRoomsList()\r\n})\r\n\r\n/** 将常量初始化 */\r\nasync function initConstants() {\r\n  dictDataApi.getDictDataBatch([DICT_TYPE_ID_TYPE, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE]).then((res: any) => {\r\n    idTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_ID_TYPE)\r\n    checkinTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n    guestSrcTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n  })\r\n}\r\n/** 获取服务器时间 */\r\nasync function getServerTime() {\r\n  const res = await serverTimeApi.serverTime(userStore.gcode, '0')\r\n  serverTime.value = res.data\r\n}\r\n\r\nwatch(\r\n  () => ({ _orderNo: props.bookNo }),\r\n  ({ _orderNo }) => {\r\n    getOrderDetail()\r\n  }\r\n)\r\n/** 预订单详情 */\r\nasync function getOrderDetail() {\r\n  bookRoomsLoading.value = true\r\n  const res = await orderApi.generalDetail({\r\n    ...queryParams,\r\n    bookNo: props.bookNo[0],\r\n  })\r\n  bookRoomsLoading.value = false\r\n  dt.value = res.data\r\n}\r\n\r\n/** 当天房价 */\r\nfunction tp() {\r\n  if (dt.value.order && dt.value.order?.prices.length > 0) {\r\n    const matchingPrice = dt.value.order.prices.find((item: any) => {\r\n      return dayjs(item.priceStartTime).format('YYYY-MM-DD') === dayjs(serverTime.value).format('YYYY-MM-DD')\r\n    })\r\n    todayPrice.value = matchingPrice ? matchingPrice.vipPrice.toString() : ''\r\n  }\r\n}\r\n\r\nconst arrangeRoomsProps = ref({\r\n  visible: false,\r\n  /** 预订单号 */\r\n  bookNo: '',\r\n  /** 批次号 */\r\n  batchNo: '',\r\n  /** 订单号 */\r\n  orderNo: '',\r\n  /** 房间类型 */\r\n  rtCode: '',\r\n  /** 房间类型名称 */\r\n  rtName: '',\r\n  /** 房间状态 */\r\n  rtState: '',\r\n  /** 房间号列表 */\r\n  rNos: [] as string[],\r\n  /** 预抵时间 */\r\n  planCheckinTime: '',\r\n  /** 预离时间 */\r\n  planCheckoutTime: '',\r\n  roomNum: 0,\r\n})\r\n/** 排房 */\r\nfunction paifang(row: any) {\r\n  isAlone.value = true\r\n  arrangeRoomsProps.value.rNos = []\r\n  arrangeRoomsProps.value.rtCode = row.rtCode\r\n  arrangeRoomsProps.value.rtName = row.rtName\r\n  arrangeRoomsProps.value.orderNo = row.orderNo\r\n  arrangeRoomsProps.value.batchNo = row.batchNo\r\n  arrangeRoomsProps.value.roomNum = 1\r\n  arrangeRoomsProps.value.planCheckinTime = dayjs(row.planCheckinTime).format('YYYY-MM-DD HH:mm')\r\n  arrangeRoomsProps.value.planCheckoutTime = dayjs(row.planCheckoutTime).format('YYYY-MM-DD HH:mm')\r\n  if (row.rNo) {\r\n    arrangeRoomsProps.value.rNos.push(row.rNo)\r\n  }\r\n  arrangeRoomsProps.value.visible = true\r\n}\r\n\r\nfunction rowKey(row: any) {\r\n  return row.rtCode\r\n}\r\nconst expandKeys = ref({\r\n  expandRowKeys: [] as number[],\r\n})\r\n/** 订单修改 */\r\nfunction editOrder() {\r\n  editDetil.value = true\r\n  bookingRoomsList()\r\n}\r\n/** 取消订单修改 */\r\nasync function reduction() {\r\n  editDetil.value = false\r\n  getOrderDetail()\r\n}\r\n/** 确认修改 */\r\nfunction onEdit() {\r\n  const bookRoomTypes = form.value.dataList.filter((item: { roomNum: number }) => item.roomNum > 0)\r\n  // 检查 retainTime 是否为有效日期，否则设置为空字符串\r\n  const retainTime = dt.value.retainTime && dayjs(dt.value.retainTime).isValid() ? dayjs(dt.value.retainTime).format('YYYY-MM-DD HH:mm') : ''\r\n\r\n  const params = {\r\n    ...queryParams,\r\n    bookNo: dt.value.bookNo,\r\n    channelCode: dt.value.channelCode,\r\n    checkinType: dt.value.checkinType,\r\n    orderSource: dt.value.orderSource,\r\n    guestSrcType: dt.value.guestSrcType,\r\n    guestCode: dt.value.guestCode,\r\n    bookType: dt.value.bookType,\r\n    hourCode: dt.value.hourCode,\r\n    planCheckinTime: dayjs(dt.value.planCheckinTime).format('YYYY-MM-DD HH:mm'),\r\n    planCheckoutTime: dayjs(dt.value.planCheckoutTime).format('YYYY-MM-DD HH:mm'),\r\n    retainTime, // 新增保留时间保存\r\n    contact: dt.value.contact,\r\n    phone: dt.value.phone,\r\n    checkinPerson: dt.value.checkinPerson,\r\n    checkinPhone: dt.value.checkinPhone,\r\n    remark: dt.value.remark,\r\n    isSendSms: '0',\r\n    batches: [\r\n      {\r\n        batchNo: `${ymdate(dt.value.planCheckinTime)}/${ymdate(dt.value.planCheckoutTime)}`,\r\n        days: dayjs(dayjs(dt.value.planCheckoutTime).format('YYYY-MM-DD')).diff(dayjs(dayjs(dayjs(dt.value.planCheckinTime).format('YYYY-MM-DD'))), 'day'),\r\n        planCheckinTime: `${ymdate(dt.value.planCheckinTime)} ${ymdateHMS(dt.value.planCheckinTime).substring(11, 16)}`,\r\n        planCheckoutTime: `${ymdate(dt.value.planCheckoutTime)} ${ymdateHMS(dt.value.planCheckoutTime).substring(11, 16)}`,\r\n        bookRoomTypes,\r\n      },\r\n    ], // 批次列表\r\n  }\r\n  bookApi.updateGeneralBook(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success(t('orderUpdateSuccess'))\r\n      reduction()\r\n      bookingRoomsList()\r\n    } else {\r\n      ElMessage.error(res.msg)\r\n    }\r\n  })\r\n}\r\n/** 取消订单 */\r\nfunction upCelck() {\r\n  upVisible.value = true\r\n}\r\n\r\nconst rowData = ref<any>({})\r\nconst checkModalRef = ref()\r\n/** 办理入住 */\r\nfunction checkIn(val: any) {\r\n  rowData.value = val\r\n  checkModalRef.value.open({\r\n    rNos: val.rNo ? [val.rNo] : [],\r\n    rtCode: val.rtCode,\r\n    rtName: val.rtName,\r\n    orderNo: val.orderNo,\r\n    batchNo: val.batchNo,\r\n    roomNum: 1,\r\n    planCheckinTime: dayjs(val.planCheckinTime).format('YYYY-MM-DD HH:mm'),\r\n    planCheckoutTime: dayjs(val.planCheckoutTime).format('YYYY-MM-DD HH:mm'),\r\n    bkNum: val.roomPkPrices[0].bkNum,\r\n    bookNo: dt.value.bookNo,\r\n    data: [\r\n      {\r\n        rNo: val.rNo,\r\n        rtName: val.rtName,\r\n      },\r\n    ],\r\n    rooms: val.bookRooms,\r\n  })\r\n}\r\nasync function refreshRoom() {\r\n  emits('refresh')\r\n  await getOrderDetail()\r\n  emits('success')\r\n  if (rowData.value.orderNo) {\r\n    const data = dt.value.bookRooms.find((item: any) => rowData.value.orderNo === item.orderNo)\r\n    checkModalRef.value.refresh([\r\n      {\r\n        rNo: data.rNo,\r\n        rtName: data.rtName,\r\n      },\r\n    ])\r\n  }\r\n}\r\nfunction checkSuccess() {\r\n  rowData.value = {}\r\n  refreshRoom()\r\n}\r\n\r\nfunction onReload() {\r\n  bookingRoomsList()\r\n  getOrderDetail()\r\n}\r\n\r\n/** 时间相关 */\r\nfunction disabledDate(time: any) {\r\n  return time.getTime() < Date.now() - 8.64e7\r\n}\r\n/** 禁用预抵时间 */\r\nfunction disabledCheckoutTime(time: any) {\r\n  const checkInDateTimeMp = new Date(dt.value.planCheckinTime).getTime()\r\n  return time.getTime() <= checkInDateTimeMp\r\n}\r\n/** 禁用保留时间 */\r\nfunction disabledretainTime(time: any) {\r\n  const start = new Date(dt.value.planCheckinTime).getTime()\r\n  const end = new Date(dt.value.planCheckoutTime).getTime()\r\n  return start > time.getTime() + 8.64e7 || time.getTime() > end\r\n}\r\n/** 小时范围选择 */\r\nfunction disabledHours() {\r\n  const list = [] as number[]\r\n  const nowTime = new Date()\r\n  // 转化日期格式用于比较\r\n  const formatTime: any = dayjs(dt.value.planCheckinTime).format('YYYY-MM-DD') // 选择日期\r\n  const startT: any = dayjs(nowTime).format('YYYY-MM-DD') // 当前日期\r\n  if (formatTime === startT) {\r\n    for (let i = 0; i < 24; i++) {\r\n      if (i >= nowTime.getHours()) {\r\n        continue\r\n      }\r\n      list.push(i)\r\n    }\r\n  }\r\n  return list\r\n}\r\n/** 分钟范围选择 */\r\nfunction disabledMinutes(hour: number, role: string, comparingDate?: any) {\r\n  const list = [] as number[]\r\n  const nowTime = new Date()\r\n  // 转化日期格式用于比较\r\n  const formatTime: any = dayjs(dt.value.planCheckinTime).format('YYYY-MM-DD') // 选择日期\r\n  const startT: any = dayjs(nowTime).format('YYYY-MM-DD') // 当前日期\r\n  const startH: any = dayjs(nowTime).format('H')\r\n  if (formatTime === startT && hour <= startH) {\r\n    for (let i = 0; i < 60; i++) {\r\n      if (nowTime.getMinutes() <= i) {\r\n        continue\r\n      }\r\n      list.push(i)\r\n    }\r\n  }\r\n  return list\r\n}\r\n/** 选择预抵时间 */\r\nfunction CheckinTime() {\r\n  if (dt.value.checkinType !== CheckinType.HOUR_ROOM) {\r\n    const time = dayjs(dt.value.planCheckoutTime).format('HH:mm')\r\n    if (dayjs(dt.value.planCheckinTime).format('YYYY-MM-DD') >= dayjs(dt.value.planCheckoutTime).format('YYYY-MM-DD')) {\r\n      dt.value.planCheckoutTime = `${dayjs(dt.value.planCheckinTime).add(1, 'day').format('YYYY-MM-DD')} ${time}`\r\n    }\r\n  } else {\r\n    const hourNum = HOUR_TYPE_NAME.find((i) => {\r\n      return i.key === dt.value.hourCode\r\n    })?.value as number\r\n    dt.value.planCheckoutTime = `${dayjs(dt.value.planCheckinTime).add(hourNum, 'hour').format('YYYY-MM-DD HH:mm')}`\r\n  }\r\n  // 时间已确认，重置未确认状态\r\n  datePickerUnconfirmed.value = false\r\n  bookingRoomsList()\r\n}\r\n\r\n/** 预离时间选择器打开时 */\r\nfunction onCheckoutTimeOpen() {\r\n  datePickerUnconfirmed.value = true\r\n}\r\n\r\n/** 日期选择器打开/关闭时 */\r\nfunction onDatePickerVisibleChange(visible: boolean) {\r\n  if (visible) {\r\n    // 弹窗打开时，计数器+1\r\n    openDatePickersCount.value++\r\n  } else {\r\n    // 弹窗关闭时，计数器-1（确保不小于0）\r\n    setTimeout(() => {\r\n      openDatePickersCount.value = Math.max(0, openDatePickersCount.value - 1)\r\n    }, 100)\r\n  }\r\n}\r\n\r\ninterface ParameterMaps {\r\n  bkNum: number\r\n  price: number\r\n  priceDate: string\r\n  priceStrategyCode: string\r\n  roomBkNum: number\r\n  vipPrice: number\r\n  week: number\r\n}\r\nconst roomPriceProps = ref({\r\n  visible: false,\r\n  rtCode: '',\r\n  initialPriceList: [\r\n    {\r\n      bkNum: 0,\r\n      price: 0,\r\n      priceDate: '',\r\n      priceStrategyCode: '',\r\n      roomBkNum: 0,\r\n      vipPrice: 0,\r\n      week: 0,\r\n    },\r\n  ] as ParameterMaps[],\r\n})\r\nfunction handleClose(row: any, rNo: number) {\r\n  const x = form.value.dataList.findIndex((item: any) => item.rtCode === row.rtCode)\r\n  const index = form.value.dataList[x].bookRooms.indexOf(rNo)\r\n  form.value.dataList[x].bookRooms.splice(index, 1)\r\n  getExpanded()\r\n}\r\nfunction checkType(value: any) {\r\n  roomPriceProps.value.rtCode = value.rtCode\r\n  roomPriceProps.value.initialPriceList = value.dayPrices\r\n  roomPriceProps.value.visible = true\r\n}\r\nfunction onChange(val: any) {\r\n  val.dayPrices = val.dayPrices.map((item) => {\r\n    item.vipPrice = val.vipPrice\r\n    return item\r\n  })\r\n}\r\nfunction echoList(value: ParameterMaps[], echoId: string) {\r\n  form.value.dataList.forEach((item: any) => {\r\n    if (item.rtCode === echoId) {\r\n      item.dayPrices = value\r\n      item.vipPrice = value[0].vipPrice\r\n    }\r\n  })\r\n}\r\n\r\nfunction changeRoomNum(value: any) {\r\n  if (value.bookRooms && value.bookRooms.length > value.roomNum) {\r\n    value.roomNum = value.bookRooms.length\r\n    ElMessage.warning(t('reduceRoomWarning', { roomCount: value.bookRooms.length }))\r\n  }\r\n}\r\n/** 预订房型排房 */\r\nfunction addRoom(row: any) {\r\n  arrangeRoomsProps.value.rNos = []\r\n  if (dt.value.planCheckinTime === '' || dt.value.planCheckoutTime === '' || dayjs(dt.value.planCheckoutTime).isAfter(dayjs(dt.value.planCheckinTime)) === false) {\r\n    return ElMessage.warning(t('setCheckinCheckoutTime'))\r\n  }\r\n  if (row.roomNum <= 0) {\r\n    return ElMessage.warning(t('selectRoomNumber'))\r\n  }\r\n  isAlone.value = false\r\n  arrangeRoomsProps.value.rtCode = row.rtCode\r\n  arrangeRoomsProps.value.rtName = row.rtName\r\n  arrangeRoomsProps.value.roomNum = row.roomNum\r\n  arrangeRoomsProps.value.planCheckinTime = `${ymdate(dt.value.planCheckinTime)} ${ymdateHMS(dt.value.planCheckinTime).substring(11, 16)}`\r\n  arrangeRoomsProps.value.planCheckoutTime = `${ymdate(dt.value.planCheckoutTime)} ${ymdateHMS(dt.value.planCheckoutTime).substring(11, 16)}`\r\n  if (row.bookRooms) {\r\n    row.bookRooms.forEach((ls: any) => {\r\n      arrangeRoomsProps.value.rNos.push(ls.rNo)\r\n    })\r\n  }\r\n  rooms.value = row.bookRooms\r\n  arrangeRoomsProps.value.visible = true\r\n}\r\n/** 房型列表 */\r\nfunction bookingRoomsList() {\r\n  const prms = {\r\n    ...queryParams,\r\n    bookNo: dt.value.bookNo,\r\n    channelCode: dt.value.channelCode,\r\n    checkinType: dt.value.checkinType,\r\n    guestSrcType: dt.value.guestSrcType,\r\n    guestCode: dt.value.guestCode,\r\n    delayMinute: 0,\r\n    orderSource: dt.value.orderSource,\r\n    hourCode: dt.value.checkinType === CheckinType.HOUR_ROOM ? dt.value.hourCode : '',\r\n    planCheckinTime: dayjs(dt.value.planCheckinTime).format('YYYY-MM-DD HH:mm'),\r\n    planCheckoutTime: dayjs(dt.value.planCheckoutTime).format('YYYY-MM-DD HH:mm'),\r\n  }\r\n  tableLoading.value = true\r\n  bookApi.roomtypeList(prms).then((res: any) => {\r\n    tableLoading.value = false\r\n    if (res.code === 0) {\r\n      form.value.dataList = res.data.map((item: { vipPrice: string | number; bookRooms: string | number; dayPrices: { vipPrice: string | number }[] }) => {\r\n        item.vipPrice = item.dayPrices[0].vipPrice\r\n        item.bookRooms = item.bookRooms ?? []\r\n        return item\r\n      })\r\n      addRooms()\r\n    }\r\n  })\r\n}\r\n/** 遍历房型早餐 */\r\nfunction addRooms() {\r\n  form.value.dataList.map((ele: any) => {\r\n    // 初始化房间数为0，避免累加问题\r\n    ele.roomNum = 0\r\n    for (let index = 0; index < dt.value.bookRooms.length; index++) {\r\n      const dtBookRooms = dt.value.bookRooms[index]\r\n      if (ele.rtCode === dtBookRooms.rtCode) {\r\n        if (dtBookRooms.rCode) {\r\n          ele.bookRooms.push({\r\n            preOccupied: '0',\r\n            state: dtBookRooms.rtState,\r\n            rNo: dtBookRooms.rNo,\r\n            rCode: dtBookRooms.rCode,\r\n          })\r\n        }\r\n        ele.roomNum += 1\r\n        ele.bkNum = dtBookRooms.roomPkPrices[0].bkNum\r\n      }\r\n    }\r\n    ele.oldRoomNum = JSON.parse(JSON.stringify(ele.roomNum))\r\n    return ele\r\n  })\r\n  getExpanded()\r\n}\r\n/** 展开办理入住行处理 */\r\nfunction getExpanded() {\r\n  const list = [] as any\r\n  form.value.dataList.forEach((item: { bookRooms: Array<object>; rtCode: string }) => {\r\n    if (item.bookRooms.length > 0) {\r\n      list.push(item.rtCode)\r\n    }\r\n  })\r\n  expandedRows.value = list\r\n}\r\nfunction selectRooms(data: any) {\r\n  form.value.dataList.forEach((item: any) => {\r\n    if (item.rtCode === data.rtCode) {\r\n      item.bookRooms = data.bookRooms\r\n    }\r\n  })\r\n  getExpanded()\r\n}\r\n\r\n/** 自动排房 */\r\nfunction automaticRoom() {\r\n  ElMessageBox.confirm(t('confirmAutoArrange'), {\r\n    confirmButtonText: t('confirm'),\r\n    cancelButtonText: t('cancel'),\r\n    type: 'warning',\r\n  }).then(() => {\r\n    bookApi\r\n      .autoArrangeRooms({\r\n        ...queryParams,\r\n        bookNo: dt.value.bookNo,\r\n        batchNo: dt.value.bookRooms[0].batchNo,\r\n      })\r\n      .then((res: any) => {\r\n        if (res.code === 0) {\r\n          ElMessage.success(t('arrangeSuccess'))\r\n          getOrderDetail()\r\n        }\r\n      })\r\n  })\r\n}\r\n/** 查看详单 */\r\nfunction viewDetailed(row: any) {\r\n  emits('seeDetailed', {\r\n    orderNo: row.orderNo,\r\n    state: row.state,\r\n    togetherCode: row.togetherCode,\r\n  })\r\n}\r\n\r\n// 在 script 部分添加以下变量\r\nconst editOutOrderNoMode = ref(false)\r\nconst editRemarkMode = ref(false)\r\nconst editOutOrderRemarkMode = ref(false)\r\n\r\n// 保存订单备注\r\nfunction saveRemark() {\r\n  bookApi\r\n    .updateRemarkOutOrderNo({\r\n      ...queryParams,\r\n      bookNo: dt.value.bookNo,\r\n      remark: dt.value.remark,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('orderRemarkUpdateSuccess'))\r\n        editRemarkMode.value = false\r\n      }\r\n    })\r\n}\r\n\r\n// 保存外部订单号\r\nfunction saveOutOrderNo() {\r\n  bookApi\r\n    .updateRemarkOutOrderNo({\r\n      ...queryParams,\r\n      bookNo: dt.value.bookNo,\r\n      outOrderNo: dt.value.outOrderNo,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('externalOrderNoUpdateSuccess'))\r\n        editOutOrderNoMode.value = false\r\n      }\r\n    })\r\n}\r\n\r\n// 保存外部订单备注\r\nfunction saveOutOrderRemark() {\r\n  bookApi\r\n    .updateRemarkOutOrderNo({\r\n      ...queryParams,\r\n      bookNo: dt.value.bookNo,\r\n      outOrderRemark: dt.value.outOrderRemark,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('externalOrderRemarkUpdateSuccess'))\r\n        editOutOrderRemarkMode.value = false\r\n      }\r\n    })\r\n}\r\n\r\n/** ==============================升级房型开始====================================== */\r\n/** 户型升级 */\r\nfunction roomUpgradeClick() {\r\n  const specificTime = dayjs(dt.value.planCheckoutTime) // 包含时间的字符串格式\r\n  const currentTime = dayjs(serverTime.value)\r\n  if (specificTime.isBefore(currentTime)) {\r\n    return ElMessage.warning(t('orderPastCheckoutTimeWarning'))\r\n  } else {\r\n    easyDialogRef.value.show()\r\n    const unassignedRoomTypes =\r\n      deduplicateByProperty(\r\n        (dt.value?.bookRooms || []).filter((item) => !item.rNo),\r\n        'rtCode'\r\n      ) || []\r\n\r\n    roomOptions.value = removePropertyFromArray(unassignedRoomTypes, 'roomPkPrices')\r\n    // 重置表单数据\r\n    modelForm.value = { ..._modelForm }\r\n    // 如果有多个房型，设置第一个房型为默认值\r\n    if (roomOptions.value.length > 0) {\r\n      modelForm.value.rtCode = roomOptions.value[0].rtCode\r\n      getRoomtype()\r\n    }\r\n  }\r\n}\r\n/** 加载当前房型数据 */\r\nfunction getRoomtype() {\r\n  const params = { ...modelForm.value }\r\n  delete params.upgradeRtCode\r\n  bookApi.getRoomtype(params).then(({ data }) => {\r\n    updateRoomOptions.value = data\r\n    modelForm.value.upgradeRtCode = ''\r\n  })\r\n}\r\n/** 提交form */\r\nfunction formSubmit() {\r\n  if (!easyFormRef.value.formRef) {\r\n    return\r\n  }\r\n  easyFormRef.value.formRef.validate(async (valid: boolean) => {\r\n    if (valid) {\r\n      const { code } = await bookApi.putUpgrade(modelForm.value)\r\n      if (code === 0) {\r\n        ElMessage.success(t('roomUpgradeSuccess'))\r\n        onReload()\r\n        formClose()\r\n      }\r\n    }\r\n  })\r\n}\r\n/** 取消弹窗 */\r\nfunction formClose() {\r\n  // 清空校验\r\n  if (easyFormRef.value) {\r\n    easyFormRef.value.formRef.resetFields()\r\n  }\r\n  // 赋值给弹窗的值\r\n  for (const key in modelForm.value) {\r\n    modelForm.value[key] = _modelForm[key]\r\n  }\r\n  easyDialogRef.value.loading = false\r\n  easyDialogRef.value.visible = false\r\n}\r\n/** ==============================升级房型结束====================================== */\r\n\r\n/** ==============================联房功能开始====================================== */\r\n/** 判断预订单是否包含联房订单 */\r\nconst hasJoinRoomOrder = computed(() => {\r\n  return dt.value.bookRooms && dt.value.bookRooms.some((room: any) => room.orderType === OrderType.JOIN)\r\n})\r\n\r\n/** 展示加入联房弹窗 */\r\nconst entryJoinRoomVisible = ref(false)\r\n\r\nfunction entryJoinRoom() {\r\n  entryJoinRoomVisible.value = true\r\n}\r\n\r\nfunction onJoinRoomSuccess() {\r\n  entryJoinRoomVisible.value = false\r\n  getOrderDetail()\r\n  emits('refresh')\r\n}\r\n\r\n/** 展示退出联房弹窗 */\r\nconst outJoinRoomVisible = ref(false)\r\n\r\n/** 提交退出联房 */\r\nfunction outSubmit() {\r\n  // 找到联房的订单号\r\n  const joinRoomOrder = dt.value.bookRooms.find((room: any) => room.orderType === OrderType.JOIN)\r\n  if (!joinRoomOrder) {\r\n    ElMessage.error('未找到联房订单')\r\n    return\r\n  }\r\n\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    orderNo: joinRoomOrder.orderNo,\r\n  }\r\n  orderApi.quitMergeRoom(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success(t('unlinkRoomSuccess'))\r\n      getOrderDetail()\r\n      emits('refresh')\r\n      outJoinRoomVisible.value = false\r\n    } else {\r\n      ElMessage.error(res.msg)\r\n    }\r\n  })\r\n}\r\n/** ==============================联房功能结束====================================== */\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-card v-if=\"['no_check_in'].includes(props.state)\" class=\"my-[10px]\" shadow=\"never\">\r\n      <el-row>\r\n        <el-col :span=\"24\" class=\"dropdown-container\">\r\n          <div>\r\n            <div v-if=\"!editDetil\">\r\n              <el-button type=\"primary\" plain @click=\"editOrder\">\r\n                {{ t('orderModify') }}\r\n              </el-button>\r\n              <el-button v-if=\"dt.state !== 'cancel'\" type=\"primary\" plain @click=\"upCelck\">\r\n                {{ t('cancelOrder') }}\r\n              </el-button>\r\n              <el-button type=\"primary\" plain>\r\n                {{ t('sendSMS') }}\r\n              </el-button>\r\n              <el-button v-if=\"props.state === 'no_check_in'\" type=\"primary\" plain @click=\"entryJoinRoom\">\r\n                {{ t('joinRoom') }}\r\n              </el-button>\r\n              <el-button v-if=\"props.state === 'no_check_in' && hasJoinRoomOrder\" type=\"primary\" plain @click=\"outJoinRoomVisible = true\">\r\n                {{ t('quitJoinRoom') }}\r\n              </el-button>\r\n            </div>\r\n            <div v-else>\r\n              <el-tooltip :content=\"datePickerUnconfirmed ? '请先确认日期时间选择' : ''\" :disabled=\"!datePickerUnconfirmed\" placement=\"top\">\r\n                <el-button type=\"primary\" plain :disabled=\"datePickerUnconfirmed\" @click=\"onEdit\">\r\n                  {{ t('confirmModify') }}\r\n                </el-button>\r\n              </el-tooltip>\r\n              <el-button type=\"primary\" plain @click=\"reduction\">\r\n                {{ t('cancelModify') }}\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n    <el-form size=\"default\" label-width=\"140px\" inline-message inline label-suffix=\"：\">\r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"10\" style=\"padding-right: 0\">\r\n          <el-card shadow=\"never\">\r\n            <template #header>\r\n              <span>{{ t('orderInfo') }}</span>\r\n            </template>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('bookingNumber')\">\r\n                  {{ dt.bookNo }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('channel')\">\r\n                  {{ dt.channelName }}\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('checkinType')\">\r\n                  {{ dt.checkinTypeName }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col v-if=\"dt.checkinType === CheckinType.HOUR_ROOM\" :span=\"12\">\r\n                <el-form-item :label=\"t('hourDuration')\">\r\n                  {{\r\n                    HOUR_TYPE_NAME.find((i) => {\r\n                      return i.key === dt.hourCode\r\n                    })?.name\r\n                  }}\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('guestSource')\">\r\n                  {{ dt.guestSrcTypeName }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('name')\">\r\n                  <span>{{ dt.guestName }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('arrivalTime')\">\r\n                  <el-date-picker\r\n                    v-if=\"editDetil\"\r\n                    v-model=\"dt.planCheckinTime\"\r\n                    type=\"datetime\"\r\n                    :clearable=\"false\"\r\n                    :disabled-date=\"disabledDate\"\r\n                    :disabled-hours=\"disabledHours\"\r\n                    :disabled-minutes=\"disabledMinutes\"\r\n                    format=\"YYYY-MM-DD HH:mm\"\r\n                    date-format=\"MMM DD, YYYY\"\r\n                    time-format=\"HH:mm\"\r\n                    style=\"width: 160px\"\r\n                    @change=\"CheckinTime\"\r\n                    @visible-change=\"onDatePickerVisibleChange\"\r\n                  />\r\n                  <span v-else>\r\n                    {{ dayjs(dt.planCheckinTime).format('YYYY-MM-DD HH:mm') }}\r\n                  </span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('departureTime')\">\r\n                  <el-date-picker\r\n                    v-if=\"editDetil\"\r\n                    v-model=\"dt.planCheckoutTime\"\r\n                    type=\"datetime\"\r\n                    :clearable=\"false\"\r\n                    :disabled=\"dt.checkinType === CheckinType.HOUR_ROOM\"\r\n                    :disabled-date=\"disabledCheckoutTime\"\r\n                    format=\"YYYY-MM-DD HH:mm\"\r\n                    date-format=\"MMM DD, YYYY\"\r\n                    time-format=\"HH:mm\"\r\n                    style=\"width: 160px\"\r\n                    @change=\"bookingRoomsList\"\r\n                    @visible-change=\"onDatePickerVisibleChange\"\r\n                  />\r\n                  <span v-else>\r\n                    {{ dayjs(dt.planCheckoutTime).format('YYYY-MM-DD HH:mm') }}\r\n                  </span>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col v-if=\"dt.checkinType !== CheckinType.HOUR_ROOM\" :span=\"12\">\r\n                <el-form-item :label=\"t('bookingDays')\">\r\n                  <span>{{ dayjs(dayjs(dt.planCheckoutTime).format('YYYY-MM-DD')).diff(dayjs(dayjs(dt.planCheckinTime).format('YYYY-MM-DD')), 'day') }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('retain')\">\r\n                  <el-date-picker v-if=\"editDetil\" v-model=\"dt.retainTime\" type=\"datetime\" format=\"YYYY-MM-DD HH:mm\" :disabled-date=\"disabledretainTime\" date-format=\"MMM DD, YYYY\" time-format=\"HH:mm\" style=\"width: 160px\" />\r\n                  <span v-else>\r\n                    {{ dt.retainTime ? dayjs(dt.retainTime).format('YYYY-MM-DD HH:mm') : '' }}\r\n                  </span>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('orderSource')\">\r\n                  {{ dt.orderSourceName }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <!--              <el-col :span=\"12\"> -->\r\n              <!--                <el-form-item label=\"支付方式\"> -->\r\n              <!--                  <el-select v-if=\"editDetil\" style=\"width: 160px;\"> -->\r\n              <!--                    <el-option label=\"支付方式1\" value=\"0\"/> -->\r\n              <!--                    <el-option label=\"支付方式2\" value=\"1\"/> -->\r\n              <!--                  </el-select> -->\r\n              <!--                  <span v-else>现付</span> -->\r\n              <!--                </el-form-item> -->\r\n              <!--              </el-col> -->\r\n            </el-row>\r\n            <!--            <el-row> -->\r\n            <!--              <el-col :span=\"24\"> -->\r\n            <!--                <el-form-item label=\"市场活动\"> -->\r\n            <!--                  未参与市场活动 -->\r\n            <!--                </el-form-item> -->\r\n            <!--              </el-col> -->\r\n            <!--            </el-row> -->\r\n          </el-card>\r\n          <el-card shadow=\"never\" class=\"mt-[12px]\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>{{ t('guestInfo') }}</span>\r\n              </div>\r\n            </template>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('booker')\">\r\n                  <el-input v-if=\"editDetil\" v-model=\"dt.contact\" style=\"width: 160px\" />\r\n                  <span v-else>{{ dt.contact }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('bookerPhone')\">\r\n                  <el-input v-if=\"editDetil\" v-model=\"dt.phone\" style=\"width: 160px\" />\r\n                  <span v-else>{{ dt.phone }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('checkinPerson')\">\r\n                  <el-input v-if=\"editDetil\" v-model=\"dt.checkinPerson\" style=\"width: 160px\" />\r\n                  <span v-else>{{ dt.checkinPerson }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('checkinPhone')\">\r\n                  <el-input v-if=\"editDetil\" v-model=\"dt.checkinPhone\" style=\"width: 160px\" />\r\n                  <span v-else>{{ dt.checkinPhone }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('OTAExternal')\">\r\n                  <div v-if=\"editOutOrderNoMode\">\r\n                    <el-input v-model=\"dt.outOrderNo\" style=\"width: 160px\" />\r\n                    <div class=\"mt-2 flex\">\r\n                      <el-button type=\"primary\" link @click=\"saveOutOrderNo\">\r\n                        {{ t('save') }}\r\n                      </el-button>\r\n                      <el-button link @click=\"editOutOrderNoMode = false\">\r\n                        {{ t('cancel') }}\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-else class=\"flex items-center\">\r\n                    <span>{{ dt.outOrderNo }}</span>\r\n                    <el-button v-if=\"!editDetil\" type=\"primary\" link @click=\"editOutOrderNoMode = true\">\r\n                      {{ t('edit') }}\r\n                    </el-button>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('guarantyMethod')\">\r\n                  {{ dt.guarantyStyleName }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item :label=\"t('salesperson')\">\r\n                  {{ dt.seller }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item :label=\"t('orderRemark')\" style=\"width: 100%\">\r\n                  <div v-if=\"editRemarkMode\" style=\"width: 100%\">\r\n                    <el-input v-model=\"dt.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('inputRemark')\" />\r\n                    <div class=\"mt-2\">\r\n                      <el-button type=\"primary\" link @click=\"saveRemark\">\r\n                        {{ t('save') }}\r\n                      </el-button>\r\n                      <el-button link @click=\"editRemarkMode = false\">\r\n                        {{ t('cancel') }}\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-else class=\"flex items-center\">\r\n                    <span>{{ dt.remark }}</span>\r\n                    <el-button v-if=\"!editDetil\" type=\"primary\" link @click=\"editRemarkMode = true\">\r\n                      {{ t('edit') }}\r\n                    </el-button>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item :label=\"t('externalOrderRemark')\" style=\"width: 100%\">\r\n                  <div v-if=\"editOutOrderRemarkMode\" style=\"width: 100%\">\r\n                    <el-input v-model=\"dt.outOrderRemark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('externalOrderRemark')\" />\r\n                    <div class=\"mt-2\">\r\n                      <el-button type=\"primary\" link @click=\"saveOutOrderRemark\">\r\n                        {{ t('save') }}\r\n                      </el-button>\r\n                      <el-button link @click=\"editOutOrderRemarkMode = false\">\r\n                        {{ t('cancel') }}\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-else class=\"flex items-center\">\r\n                    <span>{{ dt.outOrderRemark }}</span>\r\n                    <el-button v-if=\"!editDetil\" type=\"primary\" link @click=\"editOutOrderRemarkMode = true\">\r\n                      {{ t('edit') }}\r\n                    </el-button>\r\n                  </div>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :span=\"14\">\r\n          <!-- 其他 -->\r\n          <el-card shadow=\"never\">\r\n            <template #header>\r\n              {{ t('checkin') }}\r\n            </template>\r\n            <template v-if=\"!editDetil\">\r\n              <div class=\"flex-between mb-[7px]\">\r\n                <div>{{ t('rooms') }}：{{ dt.bookRooms.length }}{{ t('roomUnit') }}</div>\r\n                <div v-if=\"['no_check_in'].includes(props.state)\" v-auth=\"'pms:book-room:update:arrange'\">\r\n                  <el-button @click=\"roomUpgradeClick\">\r\n                    {{ t('roomUpgrade') }}\r\n                  </el-button>\r\n                  <el-button @click=\"automaticRoom\">\r\n                    {{ t('autoArrangeRoom') }}\r\n                  </el-button>\r\n                  <!-- <el-button>批量入住</el-button> -->\r\n                </div>\r\n              </div>\r\n              <el-table ref=\"singleTableRef\" v-loading=\"bookRoomsLoading\" :data=\"dt.bookRooms\" highlight-current-row style=\"width: 100%\">\r\n                <el-table-column :label=\"t('roomNumber')\">\r\n                  <template #default=\"scope\">\r\n                    <span v-if=\"scope.row.rNo && scope.row.state === 'check_in'\">\r\n                      {{ scope.row.rNo }}\r\n                    </span>\r\n                    <el-button v-if=\"scope.row.state === 'no_check_in'\" :disabled=\"getAuth('pms:book-room:update:arrange')\" link type=\"primary\" @click=\"paifang(scope.row)\">\r\n                      {{ scope.row.rNo ? scope.row.rNo : t('arrangeRoom') }}\r\n                    </el-button>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column prop=\"rtName\" :label=\"t('roomType')\" />\r\n                <el-table-column :label=\"t('roomPrice')\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.roomPkPrices[0].vipPrice }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('breakfastCopies')\" align=\"center\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.roomPkPrices[0].bkNum }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('operation')\" align=\"center\">\r\n                  <template #default=\"scope\">\r\n                    <div>\r\n                      <el-button v-if=\"scope.row.state === 'no_check_in'\" v-auth=\"'pms:order:create:check-in'\" link type=\"primary\" @click=\"checkIn(scope.row)\">\r\n                        {{ t('checkin') }}\r\n                      </el-button>\r\n                      <el-button v-if=\"scope.row.state === 'check_in'\" v-auth=\"'pms:order:query:get-order-detail'\" link type=\"primary\" @click=\"viewDetailed(scope.row)\">\r\n                        {{ t('viewDetails') }}\r\n                      </el-button>\r\n                    </div>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </template>\r\n            <el-table v-else v-loading=\"tableLoading\" :data=\"form.dataList\" height=\"100%\" row-key=\"rtCode\" :expand-row-keys=\"expandedRows\" border>\r\n              <el-table-column type=\"expand\">\r\n                <template #default=\"scope\">\r\n                  <div v-if=\"scope.row.bookRooms && scope.row.bookRooms.length > 0\">\r\n                    <el-tag v-for=\"(iem, index) in scope.row.bookRooms\" :key=\"iem.rtCode\" style=\"margin-right: 5px\" closable @close=\"handleClose(scope.row, index)\">\r\n                      {{ iem.rNo }}\r\n                    </el-tag>\r\n                  </div>\r\n                  <span v-else>{{ t('noArrangeRoom') }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"rtName\" :label=\"t('roomType')\" />\r\n              <el-table-column :label=\"t('discountSalePrice')\" align=\"left\" min-width=\"180px\">\r\n                <template #default=\"scope\">\r\n                  <div v-if=\"changePrice\" class=\"vipPrice\">\r\n                    <el-input-number v-model=\"scope.row.vipPrice\" :min=\"0\" :precision=\"2\" :controls=\"false\" style=\"width: 100px; margin-right: 5px\" @change=\"onChange(scope.row)\" />\r\n                    / {{ scope.row.price }}\r\n                    <el-button type=\"primary\" text @click=\"checkType(scope.row)\">\r\n                      {{ t('changePrice') }}\r\n                    </el-button>\r\n                  </div>\r\n                  <div v-else class=\"vipPrice\">\r\n                    <el-input-number v-model=\"scope.row.vipPrice\" :min=\"0\" :precision=\"2\" :controls=\"false\" style=\"width: 100px; margin-right: 5px\" disabled />\r\n                    / {{ scope.row.price }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column :label=\"t('availableRooms')\" prop=\"canSellNum\" align=\"right\" width=\"90px\" />\r\n              <el-table-column :label=\"t('availableOverbook')\" prop=\"canOverNum\" align=\"right\" width=\"90px\" />\r\n              <el-table-column :label=\"t('bookingRooms')\" align=\"center\" width=\"145px\">\r\n                <template #default=\"scope\">\r\n                  <el-input-number v-model=\"scope.row.roomNum\" :min=\"0\" :max=\"scope.row.canSellNum + scope.row.canOverNum + scope.row.oldRoomNum\" :precision=\"0\" :value-on-clear=\"0\" style=\"width: 120px\" @change=\"changeRoomNum(scope.row)\" />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column :label=\"t('breakfastCopies')\" align=\"center\" width=\"150px\">\r\n                <template #default=\"scope\">\r\n                  <el-input-number v-model=\"scope.row.bkNum\" :min=\"0\" :precision=\"0\" :value-on-clear=\"0\" :controls=\"false\" style=\"width: 80px\" />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column :label=\"t('operation')\" width=\"120px\" align=\"center\">\r\n                <template #default=\"scope\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"addRoom(scope.row)\">\r\n                    {{ t('arrangeRoom') }}\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n    <!--  排房弹窗 -->\r\n    <ArrangeRoomsDialog v-if=\"arrangeRoomsProps.visible\" v-model=\"arrangeRoomsProps.visible\" v-bind=\"arrangeRoomsProps\" :is-alone=\"isAlone\" :book-no=\"dt.bookNo\" :rooms=\"rooms\" @success=\"selectRooms\" @reload=\"onReload\" />\r\n    <!-- 改价窗口 -->\r\n    <RoomEditPrice v-if=\"roomPriceProps.visible\" v-model=\"roomPriceProps.visible\" :room-list-price=\"roomPriceProps.initialPriceList\" :rt-code=\"roomPriceProps.rtCode\" @success=\"echoList\" />\r\n    <CheckModal ref=\"checkModalRef\" @success=\"checkSuccess\" @reverts=\"getOrderDetail\" />\r\n    <!-- 取消订单 -->\r\n    <CancelPopUP v-if=\"upVisible\" v-model=\"upVisible\" :book-no=\"dt.bookNo\" @success=\"emits('refresh')\" />\r\n    <!-- 升级房型 -->\r\n    <EasyDialog ref=\"easyDialogRef\" :title=\"t('roomUpgrade')\" is-body dialog-width=\"500\" show-cancel-button :show-confirm-button=\"roomOptions.length > 0\" @submit=\"formSubmit()\" @close=\"formClose()\">\r\n      <EasyForm\r\n        v-if=\"roomOptions.length > 0\"\r\n        ref=\"easyFormRef\"\r\n        :field-list=\"ruleFieldList\"\r\n        :model=\"modelForm\"\r\n        :options=\"{\r\n          labelSuffix: '：',\r\n        }\"\r\n      >\r\n        <template #show>\r\n          <div class=\"bg-[var(--g-sub-sidebar-menu-hover-bg)] text-[var(--el-color-primary)]\" style=\"text-align: center; font-size: 13px\">该预订单下，当前房型中所有【未排房】的房间都会被升级</div>\r\n        </template>\r\n        <template #upgradeRtCode>\r\n          <el-form-item label=\"升级房型\" prop=\"upgradeRtCode\" :rules=\"[{ required: true, message: '升级房型不能为空！' }]\">\r\n            <el-select v-model=\"modelForm.upgradeRtCode\" placeholder=\"请选择升级房型\">\r\n              <el-option v-for=\"item in updateRoomOptions\" :key=\"item.rtCode\" :label=\"item.rtName\" :disabled=\"item.canSellNum === 0 && item.canOverNum === 0\" :value=\"item.rtCode\">\r\n                <div class=\"flex-between\">\r\n                  <div>{{ item.rtName }}</div>\r\n                  <div class=\"flex-center\" style=\"color: var(--el-text-color-secondary); font-size: 13px\">\r\n                    <span class=\"w-[75px]\">\r\n                      {{ t('availableRooms') }}：<span :class=\"item.canSellNum > 0 ? 'text-[var(--el-color-primary)]' : ''\">\r\n                        {{ item.canSellNum }}\r\n                      </span>\r\n                    </span>\r\n                    <span class=\"w-[75px]\">\r\n                      {{ t('availableOverbook') }}：<span :class=\"item.canOverNum > 0 ? 'text-[var(--el-color-primary)]' : ''\">{{ item.canOverNum }}</span>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n        <template #currentRoomType>\r\n          <el-form-item label=\"当前房型\">\r\n            <el-select v-model=\"modelForm.rtCode\" placeholder=\"请选择当前房型\">\r\n              <el-option v-for=\"item in roomOptions\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </template>\r\n      </EasyForm>\r\n      <el-empty v-else description=\"订单已全部排房，无法升级，请取消排房后再操作\" />\r\n    </EasyDialog>\r\n    <!-- 加入联房 -->\r\n    <LianFang v-if=\"entryJoinRoomVisible\" v-model=\"entryJoinRoomVisible\" :order-no=\"''\" :r-no=\"''\" :book-rooms=\"dt.bookRooms\" @success=\"onJoinRoomSuccess\" />\r\n\r\n    <!-- 退出联房 -->\r\n    <el-dialog v-model=\"outJoinRoomVisible\" :title=\"t('outJoinRoom')\" width=\"500\">\r\n      <div style=\"padding: 15px; font-size: 16px\">{{ t('selectedRoom') }} {{ dt.bookRooms.find(room => room.orderType === OrderType.JOIN)?.rNo || dt.bookNo }}</div>\r\n      <div style=\"padding: 15px; font-size: 16px\">\r\n        {{ t('confirmOutJoinRoom') }}\r\n      </div>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"outJoinRoomVisible = false\">\r\n            {{ t('cancel') }}\r\n          </el-button>\r\n          <el-button type=\"primary\" @click=\"outSubmit\">\r\n            {{ t('confirm') }}\r\n          </el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.blance {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 80px;\r\n  height: 60px;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-align: center;\r\n  background-color: red;\r\n  border: 1px solid transparent;\r\n  border-radius: 4px;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.custom-width {\r\n  width: 50px !important;\r\n}\r\n\r\n.el-form-item {\r\n  // margin-right: 0;\r\n  margin-top: 5px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.prices {\r\n  padding: 5px;\r\n  border: 1px solid rgb(228 224 224);\r\n  border-radius: 4px;\r\n  transition: border-color 0.3s ease;\r\n\r\n  .center-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.dropdown-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.dropdown-container .spaced-dropdown:not(:last-child) {\r\n  margin-right: 5px;\r\n}\r\n\r\n:deep(.el-card__body) {\r\n  padding: 12px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "dt", "ref", "orderNo", "outOrderNo", "<PERSON><PERSON><PERSON>", "teamName", "pinyin", "phone", "sex", "idType", "idNo", "channelCode", "channelName", "orderSource", "address", "nation", "orderSourceName", "checkinType", "checkinTypeName", "planCheckinTime", "planCheckoutTime", "rtCode", "rtName", "rCode", "rNo", "guestSrcType", "guestSrcTypeName", "bookNo", "orderType", "bindCode", "is<PERSON><PERSON>", "accState", "isInvoice", "bkTicketNum", "activityCode", "priceStrategyCode", "state", "checkoutState", "isMadeCard", "checkinShiftNo", "checkinOperator", "checkoutShiftNo", "checkoutTime", "checkoutOperator", "payShiftNo", "payTime", "payOperator", "seller", "totalFee", "balance", "bookRooms", "prices", "arrState", "deposit", "remark", "days", "tags", "awaken", "isEnable", "time", "secrecy", "notDisturbing", "summary", "price", "consume", "payment", "preAuth", "discounts", "bookType", "hourCode", "contact", "checkin<PERSON><PERSON>", "checkinPhone", "retainTime", "guarantyStyleName", "outOrderRemark", "guest<PERSON><PERSON>", "queryParams", "reactive", "gcode", "hcode", "serverTime", "todayPrice", "checkInOutTime", "idTypes", "checkinTypes", "guestSrcTypes", "upVisible", "rooms", "editDetil", "changePrice", "openDatePickersCount", "datePickerUnconfirmed", "computed", "value", "form", "dataList", "isAlone", "tableLoading", "bookRoomsLoading", "expandedRows", "easyDialogRef", "easyFormRef", "_modelForm", "upgradeMethod", "upgradeRtCode", "modelForm", "roomOptions", "updateRoomOptions", "ruleFieldList", "label", "type", "field", "options", "data", "radioOptions", "newValue", "getRoomtype", "slot", "async", "initConstants", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "DICT_TYPE_ID_TYPE", "DICT_TYPE_CHECKIN_TYPE", "DICT_TYPE_GUEST_SRC_TYPE", "then", "res", "filter", "item", "dictType", "getServerTime", "serverTimeApi", "getOrderDetail", "orderApi", "generalDetail", "onMounted", "Promise", "all", "order", "_a", "length", "matchingPrice", "find", "dayjs", "priceStartTime", "format", "vipPrice", "toString", "tp", "concat", "_b", "bookingRoomsList", "watch", "_orderNo", "arrangeRoomsProps", "visible", "batchNo", "rtState", "rNos", "roomNum", "editOrder", "reduction", "onEdit", "bookRoomTypes", "<PERSON><PERSON><PERSON><PERSON>", "params", "isSendSms", "batches", "ymdate", "diff", "ymdateHMS", "substring", "bookApi", "updateGeneralBook", "code", "ElMessage", "success", "error", "msg", "upCelck", "expandRowKeys", "rowData", "checkModalRef", "checkSuccess", "refresh", "refreshRoom", "onReload", "disabledDate", "getTime", "Date", "now", "disabledCheckoutTime", "checkInDateTimeMp", "disabledretainTime", "start", "end", "disabledHours", "list", "nowTime", "i", "getHours", "push", "disabledMinutes", "hour", "role", "comparingDate", "formatTime", "startT", "startH", "getMinutes", "CheckinTime", "CheckinType", "HOUR_ROOM", "add", "hourNum", "HOUR_TYPE_NAME", "key", "onDatePickerVisibleChange", "setTimeout", "Math", "max", "roomPriceProps", "initialPriceList", "bkNum", "priceDate", "roomBkNum", "week", "echoList", "echoId", "for<PERSON>ach", "dayPrices", "prms", "delayMinute", "roomtypeList", "map", "ele", "index", "dtBookRooms", "preOccupied", "roomPkPrices", "oldRoomNum", "JSON", "parse", "stringify", "getExpanded", "selectRooms", "automaticRoom", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "autoArrangeRooms", "editOutOrderNoMode", "editRemarkMode", "editOutOrderRemarkMode", "saveRemark", "updateRemarkOutOrderNo", "saveOutOrderNo", "saveOutOrderRemark", "roomUpgradeClick", "specificTime", "currentTime", "isBefore", "warning", "show", "unassignedRoomTypes", "deduplicateByProperty", "removePropertyFromArray", "formClose", "formRef", "resetFields", "loading", "hasJoinRoomOrder", "some", "room", "OrderType", "JOIN", "entryJoinRoomVisible", "entryJoin<PERSON>oom", "onJoinRoomSuccess", "outJoinRoomVisible", "outSubmit", "joinRoomOrder", "quitMergeRoom", "row", "x", "findIndex", "indexOf", "splice", "val", "roomCount", "isAfter", "ls", "open", "togetherCode", "validate", "valid", "putUpgrade"], "mappings": "69GAkPA,MAAMA,GAAQC,EAYRC,GAAQC,IAMRC,EAAEA,IAAMC,IACRC,GAAYC,IAEZC,GAAKC,EAAI,CAEbC,QAAS,GAETC,WAAY,GAEZC,UAAW,GAEXC,SAAU,GAEVC,OAAQ,GAERC,MAAO,GAEPC,IAAK,GAELC,OAAQ,GAERC,KAAM,GAENC,YAAa,GAEbC,YAAa,GAEbC,YAAa,GAEbC,QAAS,GAETC,OAAQ,GACRC,gBAAiB,GAEjBC,YAAa,IAEbC,gBAAiB,GAEjBC,gBAAiB,GAEjBC,iBAAkB,GAElBC,OAAQ,GACRC,OAAQ,GAERC,MAAO,GAEPC,IAAK,GAELC,aAAc,GACdC,iBAAkB,GAElBC,OAAQ,GAERC,UAAW,GAEXC,SAAU,GAEVC,OAAQ,GAERC,SAAU,GAEVC,UAAW,GAEXC,YAAa,EAEbC,aAAc,GAEdC,kBAAmB,GAEnBC,MAAO,GAEPC,cAAe,GAEfC,WAAY,GAEZC,eAAgB,GAEhBC,gBAAiB,GAEjBC,gBAAiB,GAEjBC,aAAc,GAEdC,iBAAkB,GAElBC,WAAY,GAEZC,QAAS,GAETC,YAAa,GAEbC,OAAQ,GAERC,SAAU,EAEVC,QAAS,EACTC,UAAW,GACXC,OAAQ,GAuBRC,SAAU,GAEVC,QAAS,GAETC,OAAQ,GAERC,KAAM,GAENC,KAAM,CAEJC,OAAQ,CAAEC,SAAU,GAAIC,KAAM,IAE9BC,QAAS,GAETC,cAAe,IAEjBC,QAAS,CAEPC,MAAO,EAEPC,QAAS,EAETC,QAAS,EAETC,QAAS,EAETC,UAAW,EAEXlB,QAAS,GAEXmB,SAAU,GACVC,SAAU,GACVC,QAAS,GACTC,cAAe,GACfC,aAAc,GACdC,WAAY,GACZC,kBAAmB,GACnBC,eAAgB,GAChBC,UAAW,KAGPC,GAAcC,EAAiB,CACnCC,MAAOjF,GAAUiF,MACjBC,MAAOlF,GAAUkF,QAGbC,GAAahF,EAAI,IAEjBiF,GAAajF,EAAI,IAEjBkF,GAAiBlF,EAAI,IAErBmF,GAAUnF,EAAqB,IAE/BoF,GAAepF,EAAqB,IAEpCqF,GAAgBrF,EAAqB,IAErCsF,GAAYtF,GAAI,GAEhBuF,GAAQvF,EAAI,IAEZwF,GAAYxF,GAAI,GAEhByF,GAAczF,GAAI,GAElB0F,GAAuB1F,EAAI,GAE3B2F,GAAwBC,GAAS,IAAMF,GAAqBG,MAAQ,IACpEC,GAAO9F,EAAI,CACf+F,SAAU,KAGNC,GAAUhG,GAAI,GAEdiG,GAAejG,GAAI,GAEnBkG,GAAmBlG,GAAI,GAEvBmG,GAAenG,EAAI,IAEnBoG,GAAgBpG,IAEhBqG,GAAcrG,IAEdsG,GAAazB,EAAS,IACvBD,GAEH2B,cAAe,EAEf7E,OAAQnC,GAAMmC,OAAO,GAErBN,OAAQ,GAERoF,cAAe,KAGXC,GAAYzG,EAAI,IAAKsG,KAErBI,GAAc1G,EAAI,IAYlB2G,GAAoB3G,EAAuB,IAG3C4G,GAAgB/B,EAA2B,CAC/C,CACEgC,MAAOlH,GAAE,eACTmH,KAAM,QACNC,MAAO,gBACPC,QAAS,CACPC,KAAM,CACJ,CAAEpB,MAAO,EAAGgB,MAAOlH,GAAE,iBAGvBuH,aAAc,CACZ,sBAAwBC,IACVC,IAAA,KAKpB,CACEP,MAAOlH,GAAE,mBACToH,MAAO,SACPM,KAAM,mBAER,CACER,MAAOlH,GAAE,mBACToH,MAAO,gBACPM,KAAM,iBAER,CAAEA,KAAM,OAAQN,MAAO,MAWzBO,eAAeC,KACDC,EAAAC,iBAAiB,CAACC,GAAmBC,GAAwBC,KAA2BC,MAAMC,IAChG3C,GAAAU,MAAQiC,EAAIb,KAAKc,QAAQC,GAAcA,EAAKC,WAAaP,KACpDtC,GAAAS,MAAQiC,EAAIb,KAAKc,QAAQC,GAAcA,EAAKC,WAAaN,KACxDtC,GAAAQ,MAAQiC,EAAIb,KAAKc,QAAQC,GAAcA,EAAKC,WAAaL,IAAwB,GAChG,CAGHN,eAAeY,KACb,MAAMJ,QAAYK,GAAcnD,WAAWnF,GAAUiF,MAAO,KAC5DE,GAAWa,MAAQiC,EAAIb,IAAA,CAUzBK,eAAec,KACblC,GAAiBL,OAAQ,EACnB,MAAAiC,QAAYO,EAASC,cAAc,IACpC1D,GACHlD,OAAQnC,GAAMmC,OAAO,KAEvBwE,GAAiBL,OAAQ,EACzB9F,GAAG8F,MAAQiC,EAAIb,IAAA,CAnCjBsB,GAAUjB,wBACFkB,QAAQC,IAAI,CAAClB,KAAiBa,KAAkBF,OAsCxD,iBACM,GAAAnI,GAAG8F,MAAM6C,QAAS,OAAAC,EAAA5I,GAAG8F,MAAM6C,YAAT,EAAAC,EAAgBzF,OAAO0F,QAAS,EAAG,CACvD,MAAMC,EAAgB9I,GAAG8F,MAAM6C,MAAMxF,OAAO4F,MAAMd,GACzCe,EAAMf,EAAKgB,gBAAgBC,OAAO,gBAAkBF,EAAM/D,GAAWa,OAAOoD,OAAO,gBAE5FhE,GAAWY,MAAQgD,EAAgBA,EAAcK,SAASC,WAAa,EAAA,CACzE,CA3CGC,GACYlE,GAAAW,MAAQkD,EAAM,OAAAJ,EAAG5I,GAAA8F,gBAAO3E,iBAAiB+H,OAAO,eAAeI,OAAO,OAAOA,OAAON,EAAM,OAAAO,EAAGvJ,GAAA8F,gBAAO1E,kBAAkB8H,OAAO,gBAC3HM,IAAA,IAiBnBC,GACE,KAAO,CAAEC,SAAUlK,GAAMmC,WACzB,EAAG+H,eACcrB,IAAA,IAwBnB,MAAMsB,GAAoB1J,EAAI,CAC5B2J,SAAS,EAETjI,OAAQ,GAERkI,QAAS,GAET3J,QAAS,GAETmB,OAAQ,GAERC,OAAQ,GAERwI,QAAS,GAETC,KAAM,GAEN5I,gBAAiB,GAEjBC,iBAAkB,GAClB4I,QAAS,IA0BX,SAASC,KACPxE,GAAUK,OAAQ,EACD0D,IAAA,CAGnBjC,eAAe2C,KACbzE,GAAUK,OAAQ,EACHuC,IAAA,CAGjB,SAAS8B,KACD,MAAAC,EAAgBrE,GAAKD,MAAME,SAASgC,QAAQC,GAA8BA,EAAK+B,QAAU,IAEzFvF,EAAazE,GAAG8F,MAAMrB,YAAcuE,EAAMhJ,GAAG8F,MAAMrB,YAAY4F,UAAYrB,EAAMhJ,GAAG8F,MAAMrB,YAAYyE,OAAO,oBAAsB,GAEnIoB,EAAS,IACVzF,GACHlD,OAAQ3B,GAAG8F,MAAMnE,OACjBhB,YAAaX,GAAG8F,MAAMnF,YACtBM,YAAajB,GAAG8F,MAAM7E,YACtBJ,YAAab,GAAG8F,MAAMjF,YACtBY,aAAczB,GAAG8F,MAAMrE,aACvBmD,UAAW5E,GAAG8F,MAAMlB,UACpBR,SAAUpE,GAAG8F,MAAM1B,SACnBC,SAAUrE,GAAG8F,MAAMzB,SACnBlD,gBAAiB6H,EAAMhJ,GAAG8F,MAAM3E,iBAAiB+H,OAAO,oBACxD9H,iBAAkB4H,EAAMhJ,GAAG8F,MAAM1E,kBAAkB8H,OAAO,oBAC1DzE,aACAH,QAAStE,GAAG8F,MAAMxB,QAClB/D,MAAOP,GAAG8F,MAAMvF,MAChBgE,cAAevE,GAAG8F,MAAMvB,cACxBC,aAAcxE,GAAG8F,MAAMtB,aACvBlB,OAAQtD,GAAG8F,MAAMxC,OACjBiH,UAAW,IACXC,QAAS,CACP,CACEX,QAAS,GAAGY,GAAOzK,GAAG8F,MAAM3E,oBAAoBsJ,GAAOzK,GAAG8F,MAAM1E,oBAChEmC,KAAMyF,EAAMA,EAAMhJ,GAAG8F,MAAM1E,kBAAkB8H,OAAO,eAAewB,KAAK1B,EAAMA,EAAMA,EAAMhJ,GAAG8F,MAAM3E,iBAAiB+H,OAAO,gBAAiB,OAC5I/H,gBAAiB,GAAGsJ,GAAOzK,GAAG8F,MAAM3E,oBAAoBwJ,GAAU3K,GAAG8F,MAAM3E,iBAAiByJ,UAAU,GAAI,MAC1GxJ,iBAAkB,GAAGqJ,GAAOzK,GAAG8F,MAAM1E,qBAAqBuJ,GAAU3K,GAAG8F,MAAM1E,kBAAkBwJ,UAAU,GAAI,MAC7GR,mBAINS,EAAQC,kBAAkBR,GAAQxC,MAAMC,IACrB,IAAbA,EAAIgD,MACIC,EAAAC,QAAQrL,GAAE,uBACVsK,KACOV,MAEPwB,EAAAE,MAAMnD,EAAIoD,IAAG,GAE1B,CAGH,SAASC,KACP7F,GAAUO,OAAQ,CAAA,CA5DG7F,EAAA,CACrBoL,cAAe,KA8DX,MAAAC,GAAUrL,EAAS,IACnBsL,GAAgBtL,IAsCtB,SAASuL,KACPF,GAAQxF,MAAQ,CAAC,EAfnByB,iBAIM,GAHJ7H,GAAM,iBACA2I,KACN3I,GAAM,WACF4L,GAAQxF,MAAM5F,QAAS,CACnB,MAAAgH,EAAOlH,GAAG8F,MAAM5C,UAAU6F,MAAMd,GAAcqD,GAAQxF,MAAM5F,UAAY+H,EAAK/H,UACnFqL,GAAczF,MAAM2F,QAAQ,CAC1B,CACEjK,IAAK0F,EAAK1F,IACVF,OAAQ4F,EAAK5F,SAEhB,CACH,CAIYoK,EAAA,CAGd,SAASC,KACUnC,KACFnB,IAAA,CAIjB,SAASuD,GAAajI,GACpB,OAAOA,EAAKkI,UAAYC,KAAKC,MAAQ,KAAA,CAGvC,SAASC,GAAqBrI,GAC5B,MAAMsI,EAAoB,IAAIH,KAAK9L,GAAG8F,MAAM3E,iBAAiB0K,UACtD,OAAAlI,EAAKkI,WAAaI,CAAA,CAG3B,SAASC,GAAmBvI,GAC1B,MAAMwI,EAAQ,IAAIL,KAAK9L,GAAG8F,MAAM3E,iBAAiB0K,UAC3CO,EAAM,IAAIN,KAAK9L,GAAG8F,MAAM1E,kBAAkByK,UAChD,OAAOM,EAAQxI,EAAKkI,UAAY,OAAUlI,EAAKkI,UAAYO,CAAA,CAG7D,SAASC,KACP,MAAMC,EAAO,GACPC,MAAcT,KAIpB,GAFwB9C,EAAMhJ,GAAG8F,MAAM3E,iBAAiB+H,OAAO,gBAC3CF,EAAMuD,GAASrD,OAAO,cAExC,IAAA,IAASsD,EAAI,EAAGA,EAAI,GAAIA,IAClBA,GAAKD,EAAQE,YAGjBH,EAAKI,KAAKF,GAGP,OAAAF,CAAA,CAGA,SAAAK,GAAgBC,EAAcC,EAAcC,GACnD,MAAMR,EAAO,GACPC,MAAcT,KAEdiB,EAAkB/D,EAAMhJ,GAAG8F,MAAM3E,iBAAiB+H,OAAO,cACzD8D,EAAchE,EAAMuD,GAASrD,OAAO,cACpC+D,EAAcjE,EAAMuD,GAASrD,OAAO,KACtC,GAAA6D,IAAeC,GAAUJ,GAAQK,EACnC,IAAA,IAAST,EAAI,EAAGA,EAAI,GAAIA,IAClBD,EAAQW,cAAgBV,GAG5BF,EAAKI,KAAKF,GAGP,OAAAF,CAAA,CAGT,SAASa,WACP,GAAInN,GAAG8F,MAAM7E,cAAgBmM,GAAYC,UAAW,CAClD,MAAM1J,EAAOqF,EAAMhJ,GAAG8F,MAAM1E,kBAAkB8H,OAAO,SACjDF,EAAMhJ,GAAG8F,MAAM3E,iBAAiB+H,OAAO,eAAiBF,EAAMhJ,GAAG8F,MAAM1E,kBAAkB8H,OAAO,gBAClGlJ,GAAG8F,MAAM1E,iBAAmB,GAAG4H,EAAMhJ,GAAG8F,MAAM3E,iBAAiBmM,IAAI,EAAG,OAAOpE,OAAO,iBAAiBvF,IACvG,KACK,CACL,MAAM4J,EAAU,OAAA3E,EAAA4E,GAAezE,MAAMyD,GAC5BA,EAAEiB,MAAQzN,GAAG8F,MAAMzB,iBACxB,EAAAuE,EAAA9C,MACJ9F,GAAG8F,MAAM1E,iBAAmB,GAAG4H,EAAMhJ,GAAG8F,MAAM3E,iBAAiBmM,IAAIC,EAAS,QAAQrE,OAAO,qBAAmB,CAGhHtD,GAAsBE,OAAQ,EACb0D,IAAA,CASnB,SAASkE,GAA0B9D,GAC7BA,EAEmBjE,GAAAG,QAGrB6H,YAAW,KACThI,GAAqBG,MAAQ8H,KAAKC,IAAI,EAAGlI,GAAqBG,MAAQ,EAAC,GACtE,IACL,CAYF,MAAMgI,GAAiB7N,EAAI,CACzB2J,SAAS,EACTvI,OAAQ,GACR0M,iBAAkB,CAChB,CACEC,MAAO,EACPjK,MAAO,EACPkK,UAAW,GACX9L,kBAAmB,GACnB+L,UAAW,EACX/E,SAAU,EACVgF,KAAM,MAqBH,SAAAC,GAAStI,EAAwBuI,GACxCtI,GAAKD,MAAME,SAASsI,SAASrG,IACvBA,EAAK5G,SAAWgN,IAClBpG,EAAKsG,UAAYzI,EACZmC,EAAAkB,SAAWrD,EAAM,GAAGqD,SAAA,GAE5B,CAiCH,SAASK,KACP,MAAMgF,EAAO,IACR3J,GACHlD,OAAQ3B,GAAG8F,MAAMnE,OACjBhB,YAAaX,GAAG8F,MAAMnF,YACtBM,YAAajB,GAAG8F,MAAM7E,YACtBQ,aAAczB,GAAG8F,MAAMrE,aACvBmD,UAAW5E,GAAG8F,MAAMlB,UACpB6J,YAAa,EACb5N,YAAab,GAAG8F,MAAMjF,YACtBwD,SAAUrE,GAAG8F,MAAM7E,cAAgBmM,GAAYC,UAAYrN,GAAG8F,MAAMzB,SAAW,GAC/ElD,gBAAiB6H,EAAMhJ,GAAG8F,MAAM3E,iBAAiB+H,OAAO,oBACxD9H,iBAAkB4H,EAAMhJ,GAAG8F,MAAM1E,kBAAkB8H,OAAO,qBAE5DhD,GAAaJ,OAAQ,EACrB+E,EAAQ6D,aAAaF,GAAM1G,MAAMC,IAC/B7B,GAAaJ,OAAQ,EACJ,IAAbiC,EAAIgD,OACNhF,GAAKD,MAAME,SAAW+B,EAAIb,KAAKyH,KAAK1G,IAClCA,EAAKkB,SAAWlB,EAAKsG,UAAU,GAAGpF,SAC7BlB,EAAA/E,UAAY+E,EAAK/E,WAAa,GAC5B+E,KAQblC,GAAKD,MAAME,SAAS2I,KAAKC,IAEvBA,EAAI5E,QAAU,EACd,IAAA,IAAS6E,EAAQ,EAAGA,EAAQ7O,GAAG8F,MAAM5C,UAAU2F,OAAQgG,IAAS,CAC9D,MAAMC,EAAc9O,GAAG8F,MAAM5C,UAAU2L,GACnCD,EAAIvN,SAAWyN,EAAYzN,SACzByN,EAAYvN,OACdqN,EAAI1L,UAAUwJ,KAAK,CACjBqC,YAAa,IACb3M,MAAO0M,EAAYhF,QACnBtI,IAAKsN,EAAYtN,IACjBD,MAAOuN,EAAYvN,QAGvBqN,EAAI5E,SAAW,EACf4E,EAAIZ,MAAQc,EAAYE,aAAa,GAAGhB,MAC1C,CAGK,OADPY,EAAIK,WAAaC,KAAKC,MAAMD,KAAKE,UAAUR,EAAI5E,UACxC4E,CAAA,IAEGS,KA3BC,GAEZ,CA4BH,SAASA,KACP,MAAM/C,EAAO,GACbvG,GAAKD,MAAME,SAASsI,SAASrG,IACvBA,EAAK/E,UAAU2F,OAAS,GACrByD,EAAAI,KAAKzE,EAAK5G,OAAM,IAGzB+E,GAAaN,MAAQwG,CAAA,CAEvB,SAASgD,GAAYpI,GACnBnB,GAAKD,MAAME,SAASsI,SAASrG,IACvBA,EAAK5G,SAAW6F,EAAK7F,SACvB4G,EAAK/E,UAAYgE,EAAKhE,UAAA,IAGdmM,IAAA,CAId,SAASE,KACMC,EAAAC,QAAQ7P,GAAE,sBAAuB,CAC5C8P,kBAAmB9P,GAAE,WACrB+P,iBAAkB/P,GAAE,UACpBmH,KAAM,YACLe,MAAK,KACN+C,EACG+E,iBAAiB,IACb/K,GACHlD,OAAQ3B,GAAG8F,MAAMnE,OACjBkI,QAAS7J,GAAG8F,MAAM5C,UAAU,GAAG2G,UAEhC/B,MAAMC,IACY,IAAbA,EAAIgD,OACIC,EAAAC,QAAQrL,GAAE,mBACLyI,KAAA,GAElB,GACJ,CAYG,MAAAwH,GAAqB5P,GAAI,GACzB6P,GAAiB7P,GAAI,GACrB8P,GAAyB9P,GAAI,GAGnC,SAAS+P,KACPnF,EACGoF,uBAAuB,IACnBpL,GACHlD,OAAQ3B,GAAG8F,MAAMnE,OACjB2B,OAAQtD,GAAG8F,MAAMxC,SAElBwE,MAAMC,IACY,IAAbA,EAAIgD,OACIC,EAAAC,QAAQrL,GAAE,6BACpBkQ,GAAehK,OAAQ,EAAA,GAE1B,CAIL,SAASoK,KACPrF,EACGoF,uBAAuB,IACnBpL,GACHlD,OAAQ3B,GAAG8F,MAAMnE,OACjBxB,WAAYH,GAAG8F,MAAM3F,aAEtB2H,MAAMC,IACY,IAAbA,EAAIgD,OACIC,EAAAC,QAAQrL,GAAE,iCACpBiQ,GAAmB/J,OAAQ,EAAA,GAE9B,CAIL,SAASqK,KACPtF,EACGoF,uBAAuB,IACnBpL,GACHlD,OAAQ3B,GAAG8F,MAAMnE,OACjBgD,eAAgB3E,GAAG8F,MAAMnB,iBAE1BmD,MAAMC,IACY,IAAbA,EAAIgD,OACIC,EAAAC,QAAQrL,GAAE,qCACpBmQ,GAAuBjK,OAAQ,EAAA,GAElC,CAKL,SAASsK,WACP,MAAMC,EAAerH,EAAMhJ,GAAG8F,MAAM1E,kBAC9BkP,EAActH,EAAM/D,GAAWa,OACjC,GAAAuK,EAAaE,SAASD,GACxB,OAAOtF,EAAUwF,QAAQ5Q,GAAE,iCACtB,CACLyG,GAAcP,MAAM2K,OACpB,MAAMC,EACJC,IACG,OAAA/H,EAAA5I,GAAG8F,YAAH,EAAA8C,EAAU1F,YAAa,IAAI8E,QAAQC,IAAUA,EAAKzG,MACnD,WACG,GAEKmF,GAAAb,MAAQ8K,EAAwBF,EAAqB,gBAEvDhK,GAAAZ,MAAQ,IAAKS,IAEnBI,GAAYb,MAAM+C,OAAS,IAC7BnC,GAAUZ,MAAMzE,OAASsF,GAAYb,MAAM,GAAGzE,OAClCgG,KACd,CACF,CAGF,SAASA,KACP,MAAMiD,EAAS,IAAK5D,GAAUZ,cACvBwE,EAAO7D,cACdoE,EAAQxD,YAAYiD,GAAQxC,MAAK,EAAGZ,WAClCN,GAAkBd,MAAQoB,EAC1BR,GAAUZ,MAAMW,cAAgB,EAAA,GACjC,CAmBH,SAASoK,KAEHvK,GAAYR,OACFQ,GAAAR,MAAMgL,QAAQC,cAGjB,IAAA,MAAAtD,KAAO/G,GAAUZ,MAC1BY,GAAUZ,MAAM2H,GAAOlH,GAAWkH,GAEpCpH,GAAcP,MAAMkL,SAAU,EAC9B3K,GAAcP,MAAM8D,SAAU,CAAA,CAM1B,MAAAqH,GAAmBpL,GAAS,IACzB7F,GAAG8F,MAAM5C,WAAalD,GAAG8F,MAAM5C,UAAUgO,MAAMC,GAAcA,EAAKvP,YAAcwP,GAAUC,SAI7FC,GAAuBrR,GAAI,GAEjC,SAASsR,KACPD,GAAqBxL,OAAQ,CAAA,CAG/B,SAAS0L,KACPF,GAAqBxL,OAAQ,EACduC,KACf3I,GAAM,UAAS,CAIX,MAAA+R,GAAqBxR,GAAI,GAG/B,SAASyR,KAED,MAAAC,EAAgB3R,GAAG8F,MAAM5C,UAAU6F,MAAMoI,GAAcA,EAAKvP,YAAcwP,GAAUC,OAC1F,IAAKM,EAEH,YADA3G,EAAUE,MAAM,WAIlB,MAAMZ,EAAS,CACbvF,MAAOjF,GAAUiF,MACjBC,MAAOlF,GAAUkF,MACjB9E,QAASyR,EAAczR,SAEzBoI,EAASsJ,cAActH,GAAQxC,MAAMC,IAClB,IAAbA,EAAIgD,MACIC,EAAAC,QAAQrL,GAAE,sBACLyI,KACf3I,GAAM,WACN+R,GAAmB3L,OAAQ,GAEjBkF,EAAAE,MAAMnD,EAAIoD,IAAG,GAE1B,kgSAhUM,SAAY0G,EAAUrQ,GACvB,MAAAsQ,EAAI/L,GAAKD,MAAME,SAAS+L,WAAW9J,GAAcA,EAAK5G,SAAWwQ,EAAIxQ,SACrEwN,EAAQ9I,GAAKD,MAAME,SAAS8L,GAAG5O,UAAU8O,QAAQxQ,GACvDuE,GAAKD,MAAME,SAAS8L,GAAG5O,UAAU+O,OAAOpD,EAAO,GACnCQ,IAAA,sdAOd,IAAkB6C,YACZ3D,UAAY2D,EAAI3D,UAAUI,KAAK1G,IACjCA,EAAKkB,SAAW+I,EAAI/I,SACblB,4IARQnC,QACFgI,GAAAhI,MAAMzE,OAASyE,EAAMzE,OACrByM,GAAAhI,MAAMiI,iBAAmBjI,EAAMyI,eAC9CT,GAAehI,MAAM8D,SAAU,GAHjC,IAAmB9D,8zBAoBnB,IAAuBA,YACX5C,WAAa4C,EAAM5C,UAAU2F,OAAS/C,EAAMkE,UAC9ClE,EAAAkE,QAAUlE,EAAM5C,UAAU2F,OACtBmC,EAAAwF,QAAQ5Q,GAAE,oBAAqB,CAAEuS,UAAWrM,EAAM5C,UAAU2F,+fAIzDgJ,QACGlI,GAAA7D,MAAMiE,KAAO,GACE,KAA7B/J,GAAG8F,MAAM3E,iBAAwD,KAA9BnB,GAAG8F,MAAM1E,mBAAyG,IAA9E4H,EAAMhJ,GAAG8F,MAAM1E,kBAAkBgR,QAAQpJ,EAAMhJ,GAAG8F,MAAM3E,kBAC1H6J,EAAUwF,QAAQ5Q,GAAE,2BAEzBiS,EAAI7H,SAAW,EACVgB,EAAUwF,QAAQ5Q,GAAE,sBAE7BqG,GAAQH,OAAQ,EACE6D,GAAA7D,MAAMzE,OAASwQ,EAAIxQ,OACnBsI,GAAA7D,MAAMxE,OAASuQ,EAAIvQ,OACnBqI,GAAA7D,MAAMkE,QAAU6H,EAAI7H,QACtCL,GAAkB7D,MAAM3E,gBAAkB,GAAGsJ,GAAOzK,GAAG8F,MAAM3E,oBAAoBwJ,GAAU3K,GAAG8F,MAAM3E,iBAAiByJ,UAAU,GAAI,MACnIjB,GAAkB7D,MAAM1E,iBAAmB,GAAGqJ,GAAOzK,GAAG8F,MAAM1E,qBAAqBuJ,GAAU3K,GAAG8F,MAAM1E,kBAAkBwJ,UAAU,GAAI,MAClIiH,EAAI3O,WACF2O,EAAA3O,UAAUoL,SAAS+D,IACrB1I,GAAkB7D,MAAMiE,KAAK2C,KAAK2F,EAAG7Q,IAAG,IAG5CgE,GAAMM,MAAQ+L,EAAI3O,eAClByG,GAAkB7D,MAAM8D,SAAU,IApBpC,IAAiBiI,m4BAlRAA,QACf5L,GAAQH,OAAQ,EACE6D,GAAA7D,MAAMiE,KAAO,GACbJ,GAAA7D,MAAMzE,OAASwQ,EAAIxQ,OACnBsI,GAAA7D,MAAMxE,OAASuQ,EAAIvQ,OACnBqI,GAAA7D,MAAM5F,QAAU2R,EAAI3R,QACpByJ,GAAA7D,MAAM+D,QAAUgI,EAAIhI,QACtCF,GAAkB7D,MAAMkE,QAAU,EAClCL,GAAkB7D,MAAM3E,gBAAkB6H,EAAM6I,EAAI1Q,iBAAiB+H,OAAO,oBAC5ES,GAAkB7D,MAAM1E,iBAAmB4H,EAAM6I,EAAIzQ,kBAAkB8H,OAAO,oBAC1E2I,EAAIrQ,KACNmI,GAAkB7D,MAAMiE,KAAK2C,KAAKmF,EAAIrQ,UAExCmI,GAAkB7D,MAAM8D,SAAU,GAbpC,IAAiBiI,olBAqFAK,QACf5G,GAAQxF,MAAQoM,OAChB3G,GAAczF,MAAMwM,KAAK,CACvBvI,KAAMmI,EAAI1Q,IAAM,CAAC0Q,EAAI1Q,KAAO,GAC5BH,OAAQ6Q,EAAI7Q,OACZC,OAAQ4Q,EAAI5Q,OACZpB,QAASgS,EAAIhS,QACb2J,QAASqI,EAAIrI,QACbG,QAAS,EACT7I,gBAAiB6H,EAAMkJ,EAAI/Q,iBAAiB+H,OAAO,oBACnD9H,iBAAkB4H,EAAMkJ,EAAI9Q,kBAAkB8H,OAAO,oBACrD8E,MAAOkE,EAAIlD,aAAa,GAAGhB,MAC3BrM,OAAQ3B,GAAG8F,MAAMnE,OACjBuF,KAAM,CACJ,CACE1F,IAAK0Q,EAAI1Q,IACTF,OAAQ4Q,EAAI5Q,SAGhBkE,MAAO0M,EAAIhP,YAnBf,IAAiBgP,2MAiTKL,aACpBnS,GAAM,cAAe,CACnBQ,QAAS2R,EAAI3R,QACbkC,MAAOyP,EAAIzP,MACXmQ,aAAcV,EAAIU,eAJtB,IAAsBV,+rCAiGfvL,GAAYR,MAAMgL,SAGvBxK,GAAYR,MAAMgL,QAAQ0B,UAASjL,MAAOkL,IACxC,GAAIA,EAAO,CACT,MAAM1H,KAAEA,SAAeF,EAAQ6H,WAAWhM,GAAUZ,OACvC,IAATiF,IACQC,EAAAC,QAAQrL,GAAE,uBACX+L,KACCkF,KACZ"}