{"version": 3, "file": "breakfast-DocqaGfe.js", "sources": ["../../src/views/merchant/system/config/components/breakfast.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"enableBreakfastTicket\": \"Enable Breakfast Ticket\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"breakfastCost\": \"Breakfast Cost\",\r\n    \"enterBreakfastCost\": \"Please enter breakfast cost\",\r\n    \"breakfastPrice\": \"Breakfast Price\",\r\n    \"enterBreakfastPrice\": \"Please enter breakfast sale price\",\r\n    \"breakfastTime\": \"Breakfast Serving Time\",\r\n    \"startTime\": \"Start Time\",\r\n    \"endTime\": \"End Time\",\r\n    \"currencyUnit\": \"Yuan (CNY)\",\r\n    \"edit\": \"Edit\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"editSuccess\": \"Edit successful\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"enableBreakfastTicket\": \"启用早餐卷管理\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"breakfastCost\": \"早餐成本价\",\r\n    \"enterBreakfastCost\": \"请输入早餐成本价\",\r\n    \"breakfastPrice\": \"早餐售价\",\r\n    \"enterBreakfastPrice\": \"请输入早餐售卖价格\",\r\n    \"breakfastTime\": \"早餐用餐时间\",\r\n    \"startTime\": \"开始时间\",\r\n    \"endTime\": \"结束时间\",\r\n    \"currencyUnit\": \"元\",\r\n    \"edit\": \"编辑\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"editSuccess\": \"修改成功\"\r\n  },\r\n  \"km\": {\r\n    \"enableBreakfastTicket\": \"បើកការគ្រប់គ្រងប័ណ្ណអាហារពេលព្រឹក\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"breakfastCost\": \"តម្លៃដើមអាហារពេលព្រឹក\",\r\n    \"enterBreakfastCost\": \"សូមបញ្ចូលតម្លៃដើមអាហារពេលព្រឹក\",\r\n    \"breakfastPrice\": \"តម្លៃលក់អាហារពេលព្រឹក\",\r\n    \"enterBreakfastPrice\": \"សូមបញ្ចូលតម្លៃលក់អាហារពេលព្រឹក\",\r\n    \"breakfastTime\": \"ពេលវេលាបរិភោគអាហារពេលព្រឹក\",\r\n    \"startTime\": \"ពេលវេលាចាប់ផ្តើម\",\r\n    \"endTime\": \"ពេលវេលាបញ្ចប់\",\r\n    \"currencyUnit\": \"រៀល\",\r\n    \"edit\": \"កែសម្រួល\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { hotelParamConfigApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst loading = ref(false)\r\nconst isEdit = ref(false)\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  paramType: '',\r\n  value: {\r\n    open: '',\r\n    cost: 0,\r\n    price: 0,\r\n    startTime: '',\r\n    endTime: '',\r\n  },\r\n})\r\n\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n  }\r\n  hotelParamConfigApi.getHotelParamConfigBreakfastTicket(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      loading.value = false\r\n      form.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nfunction onEdit() {\r\n  return new Promise<void>(() => {\r\n    hotelParamConfigApi.edit(form.value).then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success({\r\n          message: t('editSuccess'),\r\n          center: true,\r\n        })\r\n        getInfo()\r\n        isEdit.value = false\r\n      } else {\r\n        ElMessage.error({\r\n          message: res.msg,\r\n          center: true,\r\n        })\r\n      }\r\n    })\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div style=\"margin-left: 20px\">\r\n    <el-form :model=\"form\" label-suffix=\"：\" label-width=\"210px\">\r\n      <el-form-item :label=\"t('enableBreakfastTicket')\">\r\n        <el-switch v-if=\"isEdit\" v-model=\"form.value.open\" active-value=\"1\" inactive-value=\"0\" :active-text=\"t('yes')\" :inactive-text=\"t('no')\" inline-prompt />\r\n        <span v-else>{{ form.value.open === '1' ? t('yes') : t('no') }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('breakfastCost')\">\r\n        <span v-if=\"isEdit\">\r\n          <el-input-number v-model=\"form.value.cost\" :max=\"100000\" :min=\"0\" :placeholder=\"t('enterBreakfastCost')\" :precision=\"2\" controls-position=\"right\" style=\"width: 150px; margin-left: 10px\" />\r\n          {{ t('currencyUnit') }}\r\n        </span>\r\n        <span v-else>{{ form.value.cost }}{{ t('currencyUnit') }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('breakfastPrice')\">\r\n        <span v-if=\"isEdit\">\r\n          <el-input-number v-model=\"form.value.price\" :max=\"100000\" :min=\"0\" :placeholder=\"t('enterBreakfastPrice')\" :precision=\"2\" controls-position=\"right\" style=\"width: 150px; margin-left: 10px\" />\r\n          {{ t('currencyUnit') }}\r\n        </span>\r\n        <span v-else>{{ form.value.price }}{{ t('currencyUnit') }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('breakfastTime')\">\r\n        <span v-if=\"isEdit\">\r\n          <el-time-select v-model=\"form.value.startTime\" :placeholder=\"t('startTime')\" start=\"00:00\" step=\"00:15\" end=\"24:00\" style=\"width: 150px\" />-\r\n          <el-time-select v-model=\"form.value.endTime\" :placeholder=\"t('endTime')\" start=\"00:00\" step=\"00:15\" end=\"24:00\" style=\"width: 150px\" />\r\n        </span>\r\n        <span v-else>{{ form.value.startTime }}-{{ form.value.endTime }}</span>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button v-if=\"!isEdit\" v-auth=\"'pms:hotel-param-config:update:breakfast-ticket'\" type=\"primary\" plain @click=\"isEdit = true\">\r\n          {{ t('edit') }}\r\n        </el-button>\r\n        <el-button v-if=\"isEdit\" @click=\"isEdit = !isEdit\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button v-if=\"isEdit\" type=\"primary\" @click=\"onEdit\">\r\n          {{ t('save') }}\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "isEdit", "form", "gcode", "hcode", "paramType", "value", "open", "cost", "price", "startTime", "endTime", "getInfo", "params", "hotelParamConfigApi", "getHotelParamConfigBreakfastTicket", "then", "res", "code", "data", "onEdit", "Promise", "edit", "ElMessage", "success", "message", "center", "error", "msg", "onMounted"], "mappings": "gzBA4DA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAUC,GAAI,GACdC,EAASD,GAAI,GACbE,EAAOF,EAAI,CACfG,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,MACjBC,UAAW,GACXC,MAAO,CACLC,KAAM,GACNC,KAAM,EACNC,MAAO,EACPC,UAAW,GACXC,QAAS,MAQb,SAASC,IACPb,EAAQO,OAAQ,EAChB,MAAMO,EAAS,CACbV,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,OAEnBU,EAAoBC,mCAAmCF,GAAQG,MAAMC,IAClD,IAAbA,EAAIC,OACNnB,EAAQO,OAAQ,EAChBJ,EAAKI,MAAQW,EAAIE,KAAA,GAEpB,CAGH,SAASC,IACA,OAAA,IAAIC,SAAc,KACvBP,EAAoBQ,KAAKpB,EAAKI,OAAOU,MAAMC,IACxB,IAAbA,EAAIC,MACNK,EAAUC,QAAQ,CAChBC,QAAS5B,EAAE,eACX6B,QAAQ,IAEFd,IACRX,EAAOK,OAAQ,GAEfiB,EAAUI,MAAM,CACdF,QAASR,EAAIW,IACbF,QAAQ,GACT,GAEJ,GACF,QAnCHG,GAAU,KACAjB,GAAA"}