import{d as e,aj as t,ai as a,b as n,y as o,aR as s,aq as i,o as m,c as r,g as l,Y as c,u,f as p,w as b,m as d,x as f,aD as N,aS as h,aT as v}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                 *//* empty css                   *//* empty css                */import"./el-form-item-l0sNRNKZ.js";import{r as R}from"./rentGoods.api-IR0dWMfk.js";import{_}from"./_plugin-vue_export-helper-BCo6x5W8.js";const g={class:"section-title"},k={class:"readonly-text"},y={class:"readonly-text"},x={class:"readonly-text"},P={class:"readonly-text"},I={class:"readonly-text"},C={class:"readonly-text"},A={class:"section-title"},V={class:"readonly-text"},j={class:"readonly-text"},E=e({__name:"rentCompensation",props:{id:{default:0},handle:{},isEdit:{type:Boolean},orderNo:{}},setup(e,{expose:_}){const E=e,{t:M}=t(),w=a(),S=n(!1),G=n(),q=n({rentNo:"",rentName:"",rNo:"",name:"",rentPrice:0,remark:"",indemnityPrice:0}),U=n({gcode:w.gcode,hcode:w.hcode,id:E.id,compensationName:"",accountingMethod:M("roomAccount"),compensationPrice:0,compensationRemark:"",state:"2"}),Y=n({compensationName:[{required:!0,message:M("pleaseEnterCompensationPersonName"),trigger:"blur"}]});return o((()=>{0!==E.id&&(S.value=!0,R.getRentGoods(E.id).then((e=>{S.value=!1,Object.assign(q.value,e.data),U.value.compensationPrice=e.data.indemnityPrice||0,U.value.compensationName=e.data.name||""})))})),_({submit:()=>new Promise((e=>{G.value&&G.value.validate((t=>{t&&R.indemnityRentGoods({...U.value,id:E.id}).then((()=>{s.success({message:M("compensationSuccess"),center:!0}),e()}))}))}))}),(e,t)=>{const a=d,n=f,o=N,s=h,R=v;return i((m(),r("div",null,[l("div",g,c(u(M)("rentInformation")),1),p(n,{"label-width":"150px","label-suffix":"：",class:"readonly-form"},{default:b((()=>[p(a,{label:u(M)("rentVoucherNumber")},{default:b((()=>[l("span",k,c(q.value.rentNo),1)])),_:1},8,["label"]),p(a,{label:u(M)("rentItem")},{default:b((()=>[l("span",y,c(q.value.rentName),1)])),_:1},8,["label"]),p(a,{label:u(M)("roomNumber")},{default:b((()=>[l("span",x,c(q.value.rNo),1)])),_:1},8,["label"]),p(a,{label:u(M)("guestName")},{default:b((()=>[l("span",P,c(q.value.name),1)])),_:1},8,["label"]),p(a,{label:u(M)("rent")},{default:b((()=>[l("span",I,c(q.value.rentPrice)+" "+c(u(M)("yuan")),1)])),_:1},8,["label"]),p(a,{label:u(M)("rentRemark")},{default:b((()=>[l("span",C,c(q.value.remark||u(M)("none")),1)])),_:1},8,["label"])])),_:1}),p(o),l("div",A,c(u(M)("compensationInformation")),1),p(n,{ref_key:"formRef",ref:G,model:U.value,rules:Y.value,"label-width":"160px","label-suffix":"："},{default:b((()=>[p(a,{label:u(M)("compensationPersonName"),prop:"compensationName"},{default:b((()=>[p(s,{modelValue:U.value.compensationName,"onUpdate:modelValue":t[0]||(t[0]=e=>U.value.compensationName=e),placeholder:u(M)("pleaseEnterCompensationPersonName"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),p(a,{label:u(M)("compensationAmount")},{default:b((()=>[l("span",V,c(q.value.indemnityPrice)+" "+c(u(M)("yuan")),1)])),_:1},8,["label"]),p(a,{label:u(M)("accountingMethod")},{default:b((()=>[l("span",j,c(U.value.accountingMethod),1)])),_:1},8,["label"]),p(a,{label:u(M)("compensationRemark")},{default:b((()=>[p(s,{modelValue:U.value.compensationRemark,"onUpdate:modelValue":t[1]||(t[1]=e=>U.value.compensationRemark=e),type:"textarea",rows:3,maxlength:"200",placeholder:u(M)("pleaseEnterCompensationRemark")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1},8,["model","rules"])])),[[R,S.value]])}}});function M(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{rentInformation:{t:0,b:{t:2,i:[{t:3}],s:"Rent Information"}},compensationInformation:{t:0,b:{t:2,i:[{t:3}],s:"Compensation Information"}},rentVoucherNumber:{t:0,b:{t:2,i:[{t:3}],s:"Rent Voucher No"}},rentItem:{t:0,b:{t:2,i:[{t:3}],s:"Rent Item"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"Room Number"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"Guest Name"}},rent:{t:0,b:{t:2,i:[{t:3}],s:"Rent"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"Yuan"}},rentRemark:{t:0,b:{t:2,i:[{t:3}],s:"Rent Remark"}},none:{t:0,b:{t:2,i:[{t:3}],s:"None"}},compensationPersonName:{t:0,b:{t:2,i:[{t:3}],s:"Compensation Person Name"}},compensationAmount:{t:0,b:{t:2,i:[{t:3}],s:"Compensation Amount"}},accountingMethod:{t:0,b:{t:2,i:[{t:3}],s:"Accounting Method"}},compensationRemark:{t:0,b:{t:2,i:[{t:3}],s:"Compensation Remark"}},roomAccount:{t:0,b:{t:2,i:[{t:3}],s:"Room Account"}},pleaseEnterCompensationPersonName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter compensation person name"}},pleaseEnterCompensationRemark:{t:0,b:{t:2,i:[{t:3}],s:"Please enter compensation remark, such as item damage condition, compensation reason, etc."}},compensationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Compensation successful"}}},"zh-cn":{rentInformation:{t:0,b:{t:2,i:[{t:3}],s:"租借信息"}},compensationInformation:{t:0,b:{t:2,i:[{t:3}],s:"赔偿信息"}},rentVoucherNumber:{t:0,b:{t:2,i:[{t:3}],s:"租借凭证号"}},rentItem:{t:0,b:{t:2,i:[{t:3}],s:"租借物品"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"客人姓名"}},rent:{t:0,b:{t:2,i:[{t:3}],s:"租金"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"元"}},rentRemark:{t:0,b:{t:2,i:[{t:3}],s:"租借备注"}},none:{t:0,b:{t:2,i:[{t:3}],s:"无"}},compensationPersonName:{t:0,b:{t:2,i:[{t:3}],s:"赔偿人姓名"}},compensationAmount:{t:0,b:{t:2,i:[{t:3}],s:"赔偿金额"}},accountingMethod:{t:0,b:{t:2,i:[{t:3}],s:"入账方式"}},compensationRemark:{t:0,b:{t:2,i:[{t:3}],s:"赔偿备注"}},roomAccount:{t:0,b:{t:2,i:[{t:3}],s:"挂房账"}},pleaseEnterCompensationPersonName:{t:0,b:{t:2,i:[{t:3}],s:"请输入赔偿人姓名"}},pleaseEnterCompensationRemark:{t:0,b:{t:2,i:[{t:3}],s:"请输入赔偿备注，如物品损坏情况、赔偿原因等"}},compensationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"赔偿成功"}}},km:{rentInformation:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានអំពីការជួល"}},compensationInformation:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានអំពីការបង់ប្រាក់សំណង"}},rentVoucherNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខប័ណ្ណជួល"}},rentItem:{t:0,b:{t:2,i:[{t:3}],s:"វត្ថុជួល"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះភ្ញៀវ"}},rent:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃជួល"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"យួន"}},rentRemark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំអំពីការជួល"}},none:{t:0,b:{t:2,i:[{t:3}],s:"គ្មាន"}},compensationPersonName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះអ្នកបង់សំណង"}},compensationAmount:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនទឹកប្រាក់សំណង"}},accountingMethod:{t:0,b:{t:2,i:[{t:3}],s:"វិធីសារពើភ័ណ្ឌ"}},compensationRemark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំអំពីការបង់សំណង"}},roomAccount:{t:0,b:{t:2,i:[{t:3}],s:"គណនីបន្ទប់"}},pleaseEnterCompensationPersonName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះអ្នកបង់សំណង"}},pleaseEnterCompensationRemark:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលចំណាំអំពីការបង់សំណង ដូចជាស្ថានភាពវត្ថុខូច ឬមូលហេតុសំណងជាដើម"}},compensationSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បានបង់សំណងដោយជោគជ័យ"}}}}})}M(E);const w=_(E,[["__scopeId","data-v-85a647d7"]]);export{w as default};
//# sourceMappingURL=rentCompensation-DGZqTwko.js.map
