{"version": 3, "file": "nightAudiSet.api-Daa5Oyqn.js", "sources": ["../../src/api/modules/pms/nightaudi/nightAudiSet.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/night-audi-set'\r\n/** 集团夜审设置 */\r\nexport default {\r\n  /**\r\n   * 获得夜审设置\r\n   * @param gcode 集团代码\r\n   */\r\n  getNightAudiSet: (gcode: string) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        gcode,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 更新夜审设置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateNightAudiSet: (data: any) =>\r\n    api.put(`${BASE_PATH}/update`, data),\r\n}\r\n"], "names": ["BASE_PATH", "nightAudiSetApi", "getNightAudiSet", "gcode", "api", "get", "params", "updateNightAudiSet", "data", "put"], "mappings": "wCAEA,MAAMA,EAAY,+BAEHC,EAAA,CAKbC,gBAAkBC,GAChBC,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CACNH,WASNI,mBAAqBC,GACnBJ,EAAIK,IAAI,GAAGT,WAAoBQ"}