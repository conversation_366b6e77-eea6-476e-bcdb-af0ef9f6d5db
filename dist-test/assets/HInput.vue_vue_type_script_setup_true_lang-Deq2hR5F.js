import{d as e,a1 as a,a2 as l,ao as o,o as d,c as s,aq as r,aE as t,g as i}from"./index-CkEhI1Zk.js";const n={class:"relative w-full lg-w-48"},p=["placeholder","disabled"],u=e({__name:"HInput",props:a({placeholder:{},disabled:{type:Boolean,default:!1}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(e,{expose:a}){const u=l(e,"modelValue"),c=o("inputRef");return a({ref:c}),(e,a)=>(d(),s("div",n,[r(i("input",{ref_key:"inputRef",ref:c,"onUpdate:modelValue":a[0]||(a[0]=e=>u.value=e),type:"text",placeholder:e.placeholder,disabled:e.disabled,class:"relative block w-full border-0 rounded-md bg-white px-2.5 py-1.5 text-sm shadow-sm ring-1 ring-stone-2 ring-inset disabled-cursor-not-allowed dark-bg-dark disabled-opacity-50 focus-outline-none focus-ring-2 dark-ring-stone-8 focus-ring-ui-primary placeholder-stone-4 dark-placeholder-stone-5"},null,8,p),[[t,u.value]])]))}});export{u as _};
//# sourceMappingURL=HInput.vue_vue_type_script_setup_true_lang-Deq2hR5F.js.map
