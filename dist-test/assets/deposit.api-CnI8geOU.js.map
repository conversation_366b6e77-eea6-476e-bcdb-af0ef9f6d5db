{"version": 3, "file": "deposit.api-CnI8geOU.js", "sources": ["../../src/api/modules/pms/goods/deposit.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/deposit'\r\n/**\r\n * @description: 寄存物品\r\n */\r\nexport default {\r\n  /**\r\n   * 寄存物品列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  list: (data: {\r\n    gcode: string\r\n    hcode: string\r\n    state?: string // 状态\r\n    depositTimeStart?: string // 寄存开始时间\r\n    depositTimeEnd?: string // 寄存结束时间\r\n    claimTimeStart?: string // 领取开始时间\r\n    claimTimeEnd?: string // 领取结束时间\r\n    pageNo: number\r\n    pageSize: number\r\n  }) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 寄存物品明细\r\n   * @param id\r\n   * @returns\r\n   */\r\n  detail: (id: number) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        id,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 编辑寄存物品\r\n   * @param data\r\n   * @returns\r\n   */\r\n  edit: (data: any) => api.put(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 创建寄存物品\r\n   * @param data\r\n   * @returns\r\n   */\r\n  create: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n\r\n  /**\r\n   * 删除寄存物品\r\n   * @param id\r\n   * @returns\r\n   */\r\n  delete: (id: number) =>\r\n    api.delete(`${BASE_PATH}/delete`, {\r\n      params: {\r\n        id,\r\n      },\r\n    }),\r\n}\r\n"], "names": ["BASE_PATH", "depositApi", "list", "data", "api", "get", "params", "detail", "id", "edit", "put", "create", "post", "delete"], "mappings": "wCAEA,MAAMA,EAAY,wBAIHC,EAAA,CAMbC,KAAOC,GAWLC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAQZI,OAASC,GACPJ,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CACNE,QASNC,KAAON,GAAcC,EAAIM,IAAI,GAAGV,WAAoBG,EAAM,IAO1DQ,OAASR,GAAcC,EAAIQ,KAAK,GAAGZ,WAAoBG,EAAM,IAO7DU,OAASL,GACPJ,EAAIS,OAAO,GAAGb,WAAoB,CAChCM,OAAQ,CACNE"}