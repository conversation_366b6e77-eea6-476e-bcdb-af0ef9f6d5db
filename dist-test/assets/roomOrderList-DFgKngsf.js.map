{"version": 3, "file": "roomOrderList-DFgKngsf.js", "sources": ["../../src/views/room/realtime/components/roomOrderList.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n\t\t\"viewDetails\":\"View details\",\r\n\t\t\"more\":\"Load more\",\r\n  },\r\n  \"zh-cn\": {\r\n\t\t\"viewDetails\":\"查看详情\",\r\n\t\t\"more\":\"加载更多\",\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type roomOrderList from './roomOrderList.ts'\r\nimport { bookApi, customerApi, generalConfigApi, roomStatusApi } from '@/api/modules/index'\r\nimport otaApi from '@/api/modules/pms/serviceintegration/serviceintegration.api'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { CardReader } from '@/store/websocket/CardReader'\r\nimport { isArray } from '@/utils/is/index'\r\nimport dayjs from 'dayjs'\r\nimport { ElLoading } from 'element-plus'\r\n\r\ndefineOptions({\r\n  name: 'RoomOrderList', // ota订单列表\r\n})\r\n/**\r\n * 传入的props变量\r\n */\r\nconst props = defineProps<orderListProps>()\r\n/**\r\n * @param 发射给父组件的方法\r\n * @param 用于子组件给父组件传值或调用父组件方法\r\n */\r\nconst emits = defineEmits(['submit'])\r\nconst { t } = useI18n()\r\n/** 获取缓存数据 */\r\nconst userStore = useUserStore()\r\n/**\r\n * 传入的props变量类型\r\n */\r\ninterface orderListProps {\r\n  /** 订单列表 */\r\n  dataList: Record<string, any>[]\r\n  /** 第三方酒店名称 */\r\n  otaName: string\r\n  /** 总数 */\r\n  total?: number\r\n  /** ota请求接口 */\r\n  otaApiList?: Record<string, any>[]\r\n  /** ota radio列表 */\r\n  otaOptions?: roomOrderList.otaOptions[]\r\n}\r\n/** 是否显示加载更多 */\r\nconst isLoadmore = computed(() => props.dataList.length == props.total)\r\n/** 订单详情 */\r\nconst details = ref()\r\n/** ota订单状态是否创建 */\r\nconst isStatus = ref(false)\r\n/** 加载状态 */\r\nconst loading = ref()\r\n/** gcode、hcode 集合 */\r\nconst queryParams = reactive({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\n/** 酒店图标 */\r\nconst channelImageList = ref([\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/tiktok.png',\r\n    channelCode: 'tiktok',\r\n    channelName: '抖音',\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/fliggy.png',\r\n    channelCode: 'fliggy',\r\n    channelName: '飞猪',\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/meituan.png',\r\n    channelCode: 'meituan',\r\n    channelName: '美团',\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/store.png',\r\n    channelCode: 'lobby',\r\n    channelName: '门店',\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/logo.png',\r\n    channelCode: 'elong',\r\n    channelName: '艺龙',\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/ctrip.png',\r\n    channelCode: 'ctrip',\r\n    channelName: '携程',\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/logo.png',\r\n    channelCode: 'mini_app',\r\n    channelName: '小程序',\r\n  },\r\n])\r\n/** 详情弹窗 */\r\nconst easyDialogRef = ref()\r\n/** 携程钟点房专用 */\r\nconst ctripHour = ref<string[]>([])\r\n/** 获取中介列表 */\r\nconst simple = ref<roomOrderList.simpleType[]>([])\r\n/** 离店时间 */\r\nconst outTime = ref()\r\n/** 加载更多 */\r\nfunction load() {\r\n  emits('submit', { meituan: 10 })\r\n}\r\n/** 计算入住时间格式 */\r\nfunction startTimeType() {\r\n  let text: string\r\n  if (props.otaName == 'ctrip' && details.value.liveDays == -1) {\r\n    // 携程钟点房\r\n    text = `${details.value.arrival} ${ctripHour.value[0]}`\r\n  } else if (props.otaName == 'ctrip' && details.value.liveDays >= 1) {\r\n    // 携程非钟点房\r\n    const arrivalEarlyAndLatestTime = details.value.arrivalEarlyAndLatestTime.split(' - ').map((e: string) => e.split(' '))\r\n    const arrival = details.value.arrivalAndDeparture.split(' - ')[0]\r\n    /** 拼接小时 */\r\n    const hour: string = arrivalEarlyAndLatestTime[0][1] == '00:00' ? `${arrivalEarlyAndLatestTime[0][1]}12:00` : arrivalEarlyAndLatestTime[0][1]\r\n    text = `${arrival} ${hour}`.replace(new RegExp('/', 'g'), '-')\r\n  } else {\r\n    /** 如果预低时间是00：00的话，就设定时间为12：00 */\r\n    const time: string = dayjs(details.value.arriveTime).format('YYYY-MM-DD HH:mm')\r\n    text = time.split(' ')[1] == '00:00' ? `${time.split(' ')[0]} 12:00` : time\r\n  }\r\n  return text\r\n}\r\n/** 计算预离时间格式 */\r\nfunction endTimeType() {\r\n  let text: string\r\n  if (props.otaName == 'ctrip' && details.value.liveDays == -1) {\r\n    // 携程钟点房\r\n    text = `${details.value.arrival} ${ctripHour.value[1]}`\r\n  } else if (props.otaName == 'ctrip' && details.value.liveDays >= 1) {\r\n    // 携程非钟点房\r\n    const arrivalEarlyAndLatestTime = details.value.arrivalEarlyAndLatestTime.split(' - ').map((e: string) => e.split(' '))\r\n    const arrival = details.value.arrivalAndDeparture.split(' - ')[1]\r\n    /** 拼接小时 */\r\n    const hour: string = arrivalEarlyAndLatestTime[1][1] == '00:00' ? `${arrivalEarlyAndLatestTime[1][1]}12:00` : arrivalEarlyAndLatestTime[1][1]\r\n    text = `${arrival} ${hour}`.replace(new RegExp('/', 'g'), '-')\r\n  } else {\r\n    /** 如果预离时间是00：00的话，就设定时间为酒店设定的离店时间 */\r\n    const time: string = dayjs(details.value.checkOutDate).format('YYYY-MM-DD HH:mm')\r\n    text = time.split(' ')[1] == '00:00' ? `${time.split(' ')[0]} ${outTime.value.value}` : time\r\n  }\r\n  return text\r\n}\r\n/** 计算入住天数 */\r\nfunction totaltime(startDate: string, endDate: string) {\r\n  /** 到店时间 */\r\n  const start = dayjs(startDate)\r\n  /** 离店时间 */\r\n  const end = dayjs(endDate)\r\n  /** 得到入住天数 */\r\n  const diffInDays = end.diff(start, 'day')\r\n  return diffInDays\r\n}\r\n/** 酒店房型 */\r\nconst roomTypeRefList = ref<roomOrderList.roomTypeRefList[]>([])\r\n/** 获得OTA房型关联表;ota房型可以关联多个酒店房型 */\r\nasync function getRoomTypeRefList() {\r\n  const { data } = await otaApi.getRoomTypeRefList({\r\n    ...queryParams,\r\n    channel: props.otaName,\r\n  })\r\n  roomTypeRefList.value = data\r\n}\r\n/**\r\n * 数字转汉字\r\n * @param num 需转换的数字\r\n */\r\nfunction numberToChinese(num: string | number) {\r\n  const day = dayjs(num).day()\r\n  const digits = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\r\n  return digits[day]\r\n}\r\n/** 获得协议单位,中介列表,只包括代码和名称 */\r\nasync function simpleList() {\r\n  const { data } = await customerApi.simpleList({\r\n    gcode: userStore.gcode,\r\n    belongHcode: userStore.hcode,\r\n    paType: 1,\r\n    isEnable: 1,\r\n  })\r\n  simple.value = data\r\n}\r\n/**\r\n * 最晚退房时间\r\n */\r\nasync function getCheckOutTime() {\r\n  const { data } = await generalConfigApi.getCheckOutTime({\r\n    gcode: userStore.gcode,\r\n  })\r\n  outTime.value = data\r\n}\r\n/**\r\n * 查看详情\r\n * @param rows 当前行内容\r\n */\r\nasync function detailsClick(rows: any) {\r\n  await getRoomTypeRefList()\r\n  await simpleList()\r\n  await getCheckOutTime()\r\n  const { data } = await roomStatusApi.getExistOtaOrder({\r\n    ...queryParams,\r\n    outOrderNo: rows.orderId || rows.orderID,\r\n  })\r\n  isStatus.value = data\r\n  const channel = 'ctrip'\r\n  // 携程单独请求详情接口\r\n  if (props.otaName == channel) {\r\n    loading.value = ElLoading.service({\r\n      lock: false,\r\n      text: 'Loading',\r\n      background: 'rgba(0, 0, 0, 0.7)',\r\n    })\r\n    const value = props.otaApiList?.filter((e: any) => e.channel == channel && e.method == 'order_detail')[0]\r\n    const body: {\r\n      /** 订单号 */\r\n      orderID: string\r\n      /** 表单id */\r\n      formID: string\r\n      /** 请求token */\r\n      token: string\r\n    } = JSON.parse(value?.body)\r\n    body.orderID = rows.OrderID // 订单号 '999237425993778' ||'999237308360158' ||\r\n    body.formID = rows.FormID // 创建id 4693613361 ||4666127969 ||\r\n    body.token = rows.Token // token '70C60850CC3FFBA6031FB80494757DC3' ||'24B6318E4489A63DE1FE336039CEE5F0' ||\r\n    const json = {\r\n      /** 请求方式 */\r\n      method: 'otaRequest',\r\n      /** api */\r\n      api: {\r\n        /** 请求路径  如果是请求美团的则需要重新编译url请求地址 */\r\n        url: value?.url,\r\n        /** 请求方式 */\r\n        requestMethod: value?.requestMethod,\r\n        /** 内容 */\r\n        body: JSON.stringify(body),\r\n        /** 第三方名称 */\r\n        channel,\r\n        /** 请求头 */\r\n        headers: value?.headers,\r\n      },\r\n    }\r\n    // 把对象转字符串\r\n    const jsonStr = JSON.stringify(json)\r\n    if ((typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') && CardReader.isConnected) {\r\n      // 每次点击都要先关闭websocket\r\n      if (CardReader.isConnected) {\r\n        CardReader.closeSocket()\r\n        // 等待WebSocket完全关闭\r\n        await new Promise((resolve) => setTimeout(resolve, 300))\r\n      }\r\n    }\r\n    CardReader.initCardReader((message: string) => {\r\n      // 解析 message 字符串为 JavaScript 对象\r\n      const { response } = JSON.parse(message)\r\n      details.value = response.data\r\n      loading.value.close()\r\n      // 没有值 则不能打开详情界面\r\n      if (details.value) {\r\n        easyDialogRef.value.show()\r\n        const arr: { name: string; roomIndex: number }[] = []\r\n        response.data.clientName.split(',').forEach((item: any, index) => {\r\n          arr.push({ name: item, roomIndex: index })\r\n        })\r\n        details.value.clientName = arr\r\n        ctripHour.value = details.value.arrivalEarlyAndLatestTime.split('-')\r\n      }\r\n    })\r\n    // 触发读取信息\r\n    const timer = setInterval(() => {\r\n      if (CardReader.isConnected) {\r\n        CardReader.otaRequest(jsonStr)\r\n        clearInterval(timer)\r\n      }\r\n    }, 200)\r\n  } else {\r\n    easyDialogRef.value.show()\r\n    details.value = rows\r\n  }\r\n}\r\n/**\r\n * 查找携程酒店房型中的产品id\r\n * @param data 酒店房型\r\n * @param productCode 产品id\r\n */\r\nfunction findCtripOtaRoomTypeCodeByProductCode(data: roomOrderList.roomTypeRefList[], productCode: string) {\r\n  for (const item of data) {\r\n    for (const otaRoomType of item.otaRoomTypes || []) {\r\n      for (const otaProduct of otaRoomType.otaProducts || []) {\r\n        if (otaProduct.productCode == productCode) {\r\n          return item\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return null // 如果没有找到匹配的productCode，则返回null\r\n}\r\n/**\r\n * 查找美团酒店房型中的房型代码\r\n * @param data 酒店房型\r\n * @param otaRoomTypeCode 房型代码\r\n */\r\nfunction findEeituanOtaRoomTypeCodeByProductCode(data: roomOrderList.roomTypeRefList[], otaRoomTypeCode: string) {\r\n  for (const item of data) {\r\n    for (const otaRoomType of item.otaRoomTypes || []) {\r\n      if (otaRoomType.otaRoomTypeCode == otaRoomTypeCode) {\r\n        return item\r\n      }\r\n    }\r\n  }\r\n  return null // 如果没有找到匹配的otaRoomTypeCode，则返回null\r\n}\r\n\r\n/**\r\n * 确认弹窗\r\n * @ 前面是美团 || 后面是携程参数\r\n */\r\nasync function formSubmit() {\r\n  /** 预低时间（用以判断）分割成数组 前面是年月日 后面是时分 */\r\n  const arriveTime: string[] = startTimeType().split(' ')\r\n  /** 预离时间（用以判断）分割成数组 前面是年月日 后面是时分 */\r\n  const checkOutDate: string[] = endTimeType().split(' ')\r\n  /** 批次号 */\r\n  const batchNo: string = `${arriveTime[0]}/${checkOutDate[0]}`\r\n  /** 赠早餐份数,前端提交赠早数  暂时先写死 */\r\n  const bkNum: number = props.otaName === 'meituan' ? 0 : 0\r\n  /** 每日价格 */\r\n  const dayPrices: roomOrderList.DayPrice[] = []\r\n  /** 美团 */\r\n  isArray(details.value.priceInfo) &&\r\n    details.value.priceInfo.forEach((item: any) => {\r\n      dayPrices.push({\r\n        price: Number.parseFloat((item.floorPrice / 100).toFixed(2)),\r\n        priceDate: dayjs(item.date).format('YYYY-MM-DD'),\r\n        vipPrice: Number.parseFloat((item.floorPrice / 100).toFixed(2)),\r\n      })\r\n    })\r\n  /** 携程 */\r\n  isArray(details.value.orderRoomPrices) &&\r\n    details.value.orderRoomPrices.forEach((item: any) => {\r\n      dayPrices.push({\r\n        price: item.roomPrice,\r\n        priceDate: dayjs(item.originLivingDate).format('YYYY-MM-DD'),\r\n        vipPrice: item.roomPrice,\r\n      })\r\n    })\r\n  /** 预订房间数 */\r\n  const roomNum: number = details.value.roomCount || details.value.Quantity\r\n  /** 房型代码 */\r\n  let rtCode: string = ''\r\n  // 美团\r\n  if (props.otaName === 'meituan') {\r\n    rtCode = findEeituanOtaRoomTypeCodeByProductCode(roomTypeRefList.value, details.value.roomId)?.roomTypeCode || ''\r\n    // 携程\r\n  } else if (props.otaName === 'ctrip') {\r\n    rtCode = findCtripOtaRoomTypeCodeByProductCode(roomTypeRefList.value, details.value.roomID)?.roomTypeCode || ''\r\n  }\r\n  // 如果没有绑定pms房型则不让创建订单\r\n  if (!rtCode) {\r\n    const label: string = props.otaOptions?.filter((e) => e.value == props.otaName)[0].label!\r\n    return ElMessage({\r\n      showClose: true,\r\n      message: `当前《${label}》产品未关联 pms房型，请前往 渠道-订单同步助手-${label}渠道-进行渠道房型绑定`,\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n  }\r\n\r\n  /** 天数 */\r\n  const days: number = totaltime(details.value.checkInDate || details.value.Arrival, details.value.checkOutDate || details.value.Departure)\r\n\r\n  /** 预低时间 */\r\n  const planCheckinTime: string = startTimeType()\r\n  /** 预离时间 */\r\n  const planCheckoutTime: string = endTimeType()\r\n\r\n  /** 入住人姓名 */\r\n  const checkinPerson: string = (isArray(details.value.guests) && details.value.guests.map((e: any) => e.name).join(',')) || (isArray(details.value.clientName) && details.value.clientName.map((e: any) => e.name).join(','))\r\n  /** 入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队 */\r\n  const checkinType: string = (props.otaName === 'meituan' && !details.value.hasPromotion) || details.value.liveDays === -1 ? 'hour_room' : 'all_day'\r\n  /** 小时房代码;入住类型为时租房时，该字段有值 */\r\n  const hour: string | number = props.otaName === 'ctrip' && details.value.liveDays === -1 ? dayjs(planCheckoutTime).diff(planCheckinTime, 'hours') : ''\r\n  const hourCode: string = (props.otaName === 'meituan' && !details.value.hasPromotion) || details.value.liveDays === -1 ? `${details.value.hourRoomTime || hour}hour` : ''\r\n  /** 外部订单号 */\r\n  const outOrderNo: string = details.value.orderId || details.value.orderID\r\n  /** 渠道代码 */\r\n  const channelCode: string = details.value.allinanceName || props.otaName\r\n  /** 预订人(联系人) */\r\n  const contact: string = (details.value.contacts && details.value.contacts[0].name) || (details.value.clientName && details.value.clientName[0].name)\r\n  /** 客人代码;会员代码、协议单位、中介代码 */\r\n  const guestCode: string = simple.value.filter((item: any) => item.channel === props.otaName && item.paName.includes('预付'))[0].paCode\r\n\r\n  /** 预订人(联系人);如果是团队预订 */\r\n  const params: roomOrderList.Request = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    batches: [\r\n      {\r\n        batchNo,\r\n        bookRoomTypes: [\r\n          {\r\n            bkNum,\r\n            dayPrices,\r\n            roomNum,\r\n            rtCode,\r\n          },\r\n        ],\r\n        days,\r\n        planCheckinTime,\r\n        planCheckoutTime,\r\n      },\r\n    ],\r\n    guestName: checkinPerson,\r\n    planCheckinTime,\r\n    planCheckoutTime,\r\n    checkinPerson,\r\n    checkinType,\r\n    hourCode, // 如果是小时房，必定要传此值\r\n    bookType: 'general',\r\n    orderSource: 'agent',\r\n    outOrderNo,\r\n    guestSrcType: 'agent',\r\n    isSendSms: '0',\r\n    channelCode,\r\n    contact,\r\n    guestCode,\r\n  }\r\n  easyDialogRef.value.loading = true\r\n  bookApi\r\n    .createBook(params)\r\n    .then(() => {\r\n      ElMessage.success('预定成功')\r\n      easyDialogRef.value.visible = false\r\n    })\r\n    .finally(() => {\r\n      easyDialogRef.value.loading = false\r\n    })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"meituan\">\r\n    <el-row v-for=\"(item, index) in dataList\" :key=\"index\" class=\"meituan-row\">\r\n      <el-col :span=\"4\" class=\"tag\">\r\n        <el-tag :type=\"item.OrderType === 'D' ? 'primary' : 'success'\" effect=\"dark\">\r\n          {{ item.orderDisplayLabel || item.orderTypeDisplay }}\r\n        </el-tag>\r\n      </el-col>\r\n      <!-- 订单号 -->\r\n      <el-col :span=\"10\">\r\n        {{ item.orderId || item.orderID }}\r\n      </el-col>\r\n      <el-col :span=\"2\">\r\n        <el-image class=\"h-[15px] w-[15px]\" :src=\"channelImageList.filter((item) => item.channelCode === otaName)[0].image\" fit=\"cover\" />\r\n      </el-col>\r\n      <el-col :span=\"5\">\r\n        {{ dayjs(item.bookSucTime || item.FormDate).format('MM/DD HH:mm') }}\r\n      </el-col>\r\n      <el-col :span=\"3\" class=\"text-right\"> 已接单 </el-col>\r\n      <el-col :span=\"4\" class=\"line\"> {{ item.roomCount || item.Quantity }}间 </el-col>\r\n      <!-- 酒店名称 -->\r\n      <el-col :span=\"16\" class=\"roomName\">\r\n        <el-tooltip v-if=\"item.roomName.length > 23\" effect=\"dark\" placement=\"top-start\" :content=\"item.roomName\">\r\n          <span>\r\n            {{ item.roomName }}\r\n          </span>\r\n        </el-tooltip>\r\n        <template v-else>\r\n          {{ item.roomName }}\r\n        </template>\r\n      </el-col>\r\n      <!-- 入住人姓名 -->\r\n      <el-col :span=\"4\" class=\"text-right\">\r\n        <!-- 美团 -->\r\n        <el-tooltip v-if=\"item.guests?.length > 1 && otaName === 'meituan'\" effect=\"dark\" placement=\"top-start\">\r\n          <template #content>\r\n            <div v-for=\"ii in item.guests\" :key=\"ii.roomIndex\">\r\n              {{ ii.name }}\r\n            </div>\r\n          </template>\r\n          <span class=\"cursor-pointer\">\r\n            {{ item.guests[0]?.name }}\r\n            <el-icon class=\"ml-[2px]\"><QuestionFilled /></el-icon>\r\n          </span>\r\n        </el-tooltip>\r\n        <!-- 携程 -->\r\n        <el-tooltip v-else-if=\"item.clientName?.length > 1 && otaName === 'ctrip'\" effect=\"dark\" placement=\"top-start\">\r\n          <template #content>\r\n            <div v-for=\"ii in item.clientName\" :key=\"ii.roomIndex\">\r\n              {{ ii.name }}\r\n            </div>\r\n          </template>\r\n          <span class=\"cursor-pointer\">\r\n            {{ item.clientName[0]?.name }}\r\n            <el-icon class=\"ml-[2px]\"><QuestionFilled /></el-icon>\r\n          </span>\r\n        </el-tooltip>\r\n        <span v-else class=\"cursor-pointer\">\r\n          {{ item.guests[0]?.name || item.clientName[0]?.name }}\r\n        </span>\r\n      </el-col>\r\n      <el-col :span=\"4\" class=\"line\"> {{ totaltime(item.checkInDate || item.Arrival, item.checkOutDate || item.Departure) }}晚 </el-col>\r\n      <!-- 入住与入离时间 -->\r\n      <el-col :span=\"16\">\r\n        {{ dayjs(item.checkInDate || item.Arrival).format('YYYY/MM/DD') }} -\r\n        {{ dayjs(item.checkOutDate || item.Departure).format('YYYY/MM/DD') }}\r\n      </el-col>\r\n      <!-- 查看详情 -->\r\n      <el-col :span=\"4\" class=\"text-right\">\r\n        <el-button type=\"primary\" size=\"small\" link @click=\"detailsClick(item)\">\r\n          {{ t('viewDetails') }}\r\n        </el-button>\r\n      </el-col>\r\n    </el-row>\r\n    <el-divider v-if=\"!isLoadmore\">\r\n      <el-button type=\"primary\" size=\"small\" link @click=\"load\">\r\n        {{ t('more') }}\r\n      </el-button>\r\n    </el-divider>\r\n\r\n    <EasyDialog ref=\"easyDialogRef\" :title=\"t('viewDetails')\" is-body show-cancel-button :show-confirm-button=\"!isStatus\" confirm-text=\"创建订单\" cance-text=\"关闭\" dialog-width=\"600\" @submit=\"formSubmit\">\r\n      <el-descriptions v-if=\"details\" :column=\"1\" border>\r\n        <el-descriptions-item label=\"订单号\" label-align=\"right\" label-class-name=\"my-label\">\r\n          {{ details.orderId || details.orderID }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"PMS订单同步状态\" label-align=\"right\" label-class-name=\"my-label\">\r\n          <el-tag v-if=\"isStatus\" type=\"success\"> 已创建 </el-tag>\r\n          <el-tag v-else type=\"danger\"> 未创建 </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"酒店名称\" label-align=\"right\" label-class-name=\"my-label\">\r\n          {{ details.poiName || details.HotelName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"客人姓名\" label-align=\"right\" label-class-name=\"my-label\" class-name=\"my-box\">\r\n          <!-- 美团 -->\r\n          <template v-if=\"details.guests?.length > 0\">\r\n            <el-text v-for=\"(ii, index) in details.guests\" :key=\"index\" class=\"mx-1\">\r\n              {{ ii.name }}\r\n            </el-text>\r\n          </template>\r\n          <!-- 携程 -->\r\n          <template v-if=\"details.clientName?.length > 0\">\r\n            <el-text v-for=\"(ii, index) in details.clientName\" :key=\"index\" class=\"mx-1\">\r\n              {{ ii.name }}\r\n            </el-text>\r\n          </template>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"房型名称\" label-align=\"right\" label-class-name=\"my-label\">\r\n          {{ details.roomName || details.BedType }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"间数\" label-align=\"right\" label-class-name=\"my-label\">\r\n          <el-tag>{{ details.roomCount || details.Quantity }}间</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"入住时间\" label-align=\"right\" label-class-name=\"my-label\">\r\n          {{ startTimeType() }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"离店时间\" label-align=\"right\" label-class-name=\"my-label\">\r\n          {{ endTimeType() }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item v-if=\"(otaName === 'meituan' && !details.hasPromotion) || details.liveDays === -1\" label=\"入住类型\" label-align=\"right\" label-class-name=\"my-label\">\r\n          <el-tag type=\"danger\"> 钟点房 </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"天数\" label-align=\"right\" label-class-name=\"my-label\"> {{ totaltime(details.checkInDate || details.Arrival, details.checkOutDate || details.Departure) }}晚 </el-descriptions-item>\r\n        <el-descriptions-item label=\"下单时间\" label-align=\"right\" label-class-name=\"my-label\">\r\n          {{ details.bookingTimeString || details.formDate }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"底价构成\" label-align=\"right\" label-class-name=\"my-label\">\r\n          <el-row v-if=\"otaName === 'meituan'\">\r\n            <el-col v-for=\"(ii, index) in details.priceInfo\" :key=\"index\" :span=\"24\">\r\n              <el-text type=\"danger\">\r\n                {{ `￥${parseFloat((ii.floorPrice / 100).toFixed(2))}` }}\r\n              </el-text>\r\n              <el-text class=\"!ml-[20px]\">\r\n                {{ dayjs(ii.date).format('YYYY-MM-DD') }}\r\n              </el-text>\r\n              <el-text class=\"!ml-[20px]\">\r\n                {{ `(${numberToChinese(ii.date)})` }}\r\n              </el-text>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row v-if=\"otaName === 'ctrip'\" style=\"max-height: 200px; overflow-y: auto\">\r\n            <el-col v-for=\"(ii, index) in details.orderRoomPrices\" :key=\"index\" :span=\"24\">\r\n              <el-text type=\"danger\">\r\n                {{ `￥${ii.roomPrice}` }}\r\n              </el-text>\r\n              <el-text class=\"!ml-[20px]\">\r\n                {{ dayjs(ii.originLivingDate).format('YYYY-MM-DD') }}\r\n              </el-text>\r\n              <el-text class=\"!ml-[20px]\">\r\n                {{ `(${numberToChinese(ii.originLivingDate)})` }}\r\n              </el-text>\r\n              <el-text class=\"!ml-[20px]\">\r\n                {{ ii.mealInfo }}\r\n              </el-text>\r\n            </el-col>\r\n          </el-row>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n    </EasyDialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.meituan {\r\n  @apply h-[68vh];\r\n  overflow: auto;\r\n  .meituan-row {\r\n    font-size: 12px;\r\n    line-height: 24px;\r\n    margin-top: 15px;\r\n    .tag {\r\n      line-height: normal;\r\n    }\r\n    .roomName {\r\n      @include text-overflow(1);\r\n    }\r\n    .line {\r\n      text-align: center;\r\n      position: relative;\r\n      &:after {\r\n        background-color: var(--el-border-color-light);\r\n        top: 50%;\r\n        content: '';\r\n        height: 70%;\r\n        right: 10px;\r\n        position: absolute;\r\n        width: 1px;\r\n        transform: translateY(-35%);\r\n        z-index: var(--el-index-normal);\r\n      }\r\n    }\r\n    & + .meituan-row {\r\n      border-top: 1px solid var(--el-border-color);\r\n      padding-top: 15px;\r\n    }\r\n  }\r\n}\r\n:deep(.my-label) {\r\n  width: 150px;\r\n}\r\n:deep(.my-box) {\r\n  .mx-1 {\r\n    & + .mx-1 {\r\n      &::before {\r\n        content: '、';\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "isLoadmore", "computed", "dataList", "length", "total", "details", "ref", "isStatus", "loading", "queryParams", "reactive", "gcode", "hcode", "channelImageList", "image", "channelCode", "channelName", "easyDialogRef", "ctripHour", "simple", "outTime", "load", "meituan", "startTimeType", "text", "otaName", "value", "liveDays", "arrival", "arrivalEarlyAndLatestTime", "split", "map", "e", "arrivalAndDeparture", "replace", "RegExp", "time", "dayjs", "arriveTime", "format", "endTimeType", "checkOutDate", "totaltime", "startDate", "endDate", "start", "diff", "roomTypeRefList", "numberToChinese", "num", "day", "async", "detailsClick", "rows", "data", "otaApi", "getRoomTypeRefList", "channel", "customerApi", "simpleList", "belongHcode", "paType", "isEnable", "generalConfigApi", "getCheckOutTime", "roomStatusApi", "getExistOtaOrder", "outOrderNo", "orderId", "orderID", "ElLoading", "service", "lock", "background", "_a", "otaApiList", "filter", "method", "body", "JSON", "parse", "OrderID", "formID", "FormID", "token", "Token", "json", "api", "url", "requestMethod", "stringify", "headers", "jsonStr", "window", "CallBridge", "__RUNNING_IN_PMS_AGENT__", "<PERSON><PERSON><PERSON><PERSON>", "isConnected", "closeSocket", "Promise", "resolve", "setTimeout", "initCardReader", "message", "response", "close", "show", "arr", "clientName", "for<PERSON>ach", "item", "index", "push", "name", "roomIndex", "timer", "setInterval", "otaRequest", "clearInterval", "formSubmit", "batchNo", "bkNum", "dayPrices", "isArray", "priceInfo", "price", "Number", "parseFloat", "floorPrice", "toFixed", "priceDate", "date", "vipPrice", "orderRoomPrices", "roomPrice", "originLivingDate", "roomNum", "roomCount", "Quantity", "rtCode", "otaRoomTypeCode", "otaRoomType", "otaRoomTypes", "findEeituanOtaRoomTypeCodeByProductCode", "roomId", "roomTypeCode", "_b", "productCode", "otaProduct", "otaProducts", "findCtripOtaRoomTypeCodeByProductCode", "roomID", "label", "_c", "otaOptions", "ElMessage", "showClose", "type", "dangerouslyUseHTMLString", "days", "checkInDate", "Arrival", "Departure", "planCheckinTime", "planCheckoutTime", "checkin<PERSON><PERSON>", "guests", "join", "checkinType", "hasPromotion", "hour", "hourCode", "hourRoomTime", "allinanceName", "contact", "contacts", "guest<PERSON><PERSON>", "paName", "includes", "paCode", "params", "batches", "bookRoomTypes", "<PERSON><PERSON><PERSON>", "bookType", "orderSource", "guestSrcType", "isSendSms", "bookApi", "createBook", "then", "success", "visible", "finally"], "mappings": "+uCA6BA,MAAMA,EAAQC,EAKRC,EAAQC,GACRC,EAAEA,GAAMC,IAERC,EAAYC,IAiBZC,EAAaC,GAAS,IAAMT,EAAMU,SAASC,QAAUX,EAAMY,QAE3DC,EAAUC,IAEVC,EAAWD,GAAI,GAEfE,EAAUF,IAEVG,GAAcC,EAAS,CAC3BC,MAAOb,EAAUa,MACjBC,MAAOd,EAAUc,QAGbC,GAAmBP,EAAI,CAC3B,CACEQ,MAAO,mFACPC,YAAa,SACbC,YAAa,MAEf,CACEF,MAAO,mFACPC,YAAa,SACbC,YAAa,MAEf,CACEF,MAAO,oFACPC,YAAa,UACbC,YAAa,MAEf,CACEF,MAAO,kFACPC,YAAa,QACbC,YAAa,MAEf,CACEF,MAAO,iFACPC,YAAa,QACbC,YAAa,MAEf,CACEF,MAAO,kFACPC,YAAa,QACbC,YAAa,MAEf,CACEF,MAAO,iFACPC,YAAa,WACbC,YAAa,SAIXC,GAAgBX,IAEhBY,GAAYZ,EAAc,IAE1Ba,GAASb,EAAgC,IAEzCc,GAAUd,IAEhB,SAASe,KACP3B,EAAM,SAAU,CAAE4B,QAAS,IAAI,CAGjC,SAASC,KACH,IAAAC,EACJ,GAAqB,SAAjBhC,EAAMiC,UAAoD,GAA9BpB,EAAQqB,MAAMC,SAErCH,EAAA,GAAGnB,EAAQqB,MAAME,WAAWV,GAAUQ,MAAM,aACzB,SAAjBlC,EAAMiC,SAAsBpB,EAAQqB,MAAMC,UAAY,EAAG,CAElE,MAAME,EAA4BxB,EAAQqB,MAAMG,0BAA0BC,MAAM,OAAOC,KAAKC,GAAcA,EAAEF,MAAM,OAI3GN,EAAA,GAHSnB,EAAQqB,MAAMO,oBAAoBH,MAAM,OAAO,MAEP,SAAnCD,EAA0B,GAAG,GAAgB,GAAGA,EAA0B,GAAG,UAAYA,EAA0B,GAAG,KAC/GK,QAAQ,IAAIC,OAAO,IAAK,KAAM,IAAG,KACxD,CAEL,MAAMC,EAAeC,EAAMhC,EAAQqB,MAAMY,YAAYC,OAAO,oBAC5Df,EAA6B,SAAtBY,EAAKN,MAAM,KAAK,GAAgB,GAAGM,EAAKN,MAAM,KAAK,WAAaM,CAAA,CAElE,OAAAZ,CAAA,CAGT,SAASgB,KACH,IAAAhB,EACJ,GAAqB,SAAjBhC,EAAMiC,UAAoD,GAA9BpB,EAAQqB,MAAMC,SAErCH,EAAA,GAAGnB,EAAQqB,MAAME,WAAWV,GAAUQ,MAAM,aACzB,SAAjBlC,EAAMiC,SAAsBpB,EAAQqB,MAAMC,UAAY,EAAG,CAElE,MAAME,EAA4BxB,EAAQqB,MAAMG,0BAA0BC,MAAM,OAAOC,KAAKC,GAAcA,EAAEF,MAAM,OAI3GN,EAAA,GAHSnB,EAAQqB,MAAMO,oBAAoBH,MAAM,OAAO,MAEP,SAAnCD,EAA0B,GAAG,GAAgB,GAAGA,EAA0B,GAAG,UAAYA,EAA0B,GAAG,KAC/GK,QAAQ,IAAIC,OAAO,IAAK,KAAM,IAAG,KACxD,CAEL,MAAMC,EAAeC,EAAMhC,EAAQqB,MAAMe,cAAcF,OAAO,oBAC9Df,EAA6B,SAAtBY,EAAKN,MAAM,KAAK,GAAgB,GAAGM,EAAKN,MAAM,KAAK,MAAMV,GAAQM,MAAMA,QAAUU,CAAA,CAEnF,OAAAZ,CAAA,CAGA,SAAAkB,GAAUC,EAAmBC,GAE9B,MAAAC,EAAQR,EAAMM,GAKb,OAHKN,EAAMO,GAEKE,KAAKD,EAAO,MAC5B,CAGH,MAAAE,GAAkBzC,EAAqC,IAa7D,SAAS0C,GAAgBC,GAGvB,MADe,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAD9CZ,EAAMY,GAAKC,MAEN,CAyBnBC,eAAeC,GAAaC,eAvC5BF,iBACE,MAAMG,KAAEA,SAAeC,EAAOC,mBAAmB,IAC5C/C,GACHgD,QAASjE,EAAMiC,UAEjBsB,GAAgBrB,MAAQ4B,CAAA,CAmClBE,SAvBRL,iBACE,MAAMG,KAAEA,SAAeI,EAAYC,WAAW,CAC5ChD,MAAOb,EAAUa,MACjBiD,YAAa9D,EAAUc,MACvBiD,OAAQ,EACRC,SAAU,IAEZ3C,GAAOO,MAAQ4B,CAAA,CAiBTK,SAZRR,iBACE,MAAMG,KAAEA,SAAeS,EAAiBC,gBAAgB,CACtDrD,MAAOb,EAAUa,QAEnBS,GAAQM,MAAQ4B,CAAA,CASVU,GACN,MAAMV,KAAEA,SAAeW,EAAcC,iBAAiB,IACjDzD,GACH0D,WAAYd,EAAKe,SAAWf,EAAKgB,UAEnC9D,EAASmB,MAAQ4B,EACjB,MAAMG,EAAU,QAEZ,GAAAjE,EAAMiC,SAAWgC,EAAS,CACpBjD,EAAAkB,MAAQ4C,EAAUC,QAAQ,CAChCC,MAAM,EACNhD,KAAM,UACNiD,WAAY,uBAEd,MAAM/C,EAAQ,OAAAgD,EAAAlF,EAAMmF,iBAAN,EAAAD,EAAkBE,QAAQ5C,GAAWA,EAAEyB,SAAWA,GAAuB,gBAAZzB,EAAE6C,SAA0B,GACjGC,EAOFC,KAAKC,MAAM,MAAAtD,OAAA,EAAAA,EAAOoD,MACtBA,EAAKT,QAAUhB,EAAK4B,QACpBH,EAAKI,OAAS7B,EAAK8B,OACnBL,EAAKM,MAAQ/B,EAAKgC,MAClB,MAAMC,EAAO,CAEXT,OAAQ,aAERU,IAAK,CAEHC,IAAY,MAAP9D,OAAO,EAAAA,EAAA8D,IAEZC,cAAsB,MAAP/D,OAAO,EAAAA,EAAA+D,cAEtBX,KAAMC,KAAKW,UAAUZ,GAErBrB,UAEAkC,QAAgB,MAAPjE,OAAO,EAAAA,EAAAiE,UAIdC,EAAUb,KAAKW,UAAUJ,QACY,IAA9BO,OAAeC,iBAAkF,IAA5CD,OAAeE,2BAA6CC,EAAWC,aAEnID,EAAWC,cACbD,EAAWE,oBAEL,IAAIC,SAASC,GAAYC,WAAWD,EAAS,QAG5CJ,EAAAM,gBAAgBC,IAEzB,MAAMC,SAAEA,GAAazB,KAAKC,MAAMuB,GAIhC,GAHAlG,EAAQqB,MAAQ8E,EAASlD,KACzB9C,EAAQkB,MAAM+E,QAEVpG,EAAQqB,MAAO,CACjBT,GAAcS,MAAMgF,OACpB,MAAMC,EAA6C,GAC1CH,EAAAlD,KAAKsD,WAAW9E,MAAM,KAAK+E,SAAQ,CAACC,EAAWC,KACtDJ,EAAIK,KAAK,CAAEC,KAAMH,EAAMI,UAAWH,GAAO,IAE3C1G,EAAQqB,MAAMkF,WAAaD,EAC3BzF,GAAUQ,MAAQrB,EAAQqB,MAAMG,0BAA0BC,MAAM,IAAG,KAIjE,MAAAqF,EAAQC,aAAY,KACpBpB,EAAWC,cACbD,EAAWqB,WAAWzB,GACtB0B,cAAcH,GAAK,GAEpB,IAAG,MAENlG,GAAcS,MAAMgF,OACpBrG,EAAQqB,MAAQ2B,CAClB,CAuCFF,eAAeoE,eAEb,MAAMjF,EAAuBf,KAAgBO,MAAM,KAE7CW,EAAyBD,KAAcV,MAAM,KAE7C0F,EAAkB,GAAGlF,EAAW,MAAMG,EAAa,KAEnDgF,GAAgBjI,EAAMiC,QAAwB,GAE9CiG,EAAsC,GAEpCC,EAAAtH,EAAQqB,MAAMkG,YACpBvH,EAAQqB,MAAMkG,UAAUf,SAASC,IAC/BY,EAAUV,KAAK,CACba,MAAOC,OAAOC,YAAYjB,EAAKkB,WAAa,KAAKC,QAAQ,IACzDC,UAAW7F,EAAMyE,EAAKqB,MAAM5F,OAAO,cACnC6F,SAAUN,OAAOC,YAAYjB,EAAKkB,WAAa,KAAKC,QAAQ,KAC7D,IAGGN,EAAAtH,EAAQqB,MAAM2G,kBACpBhI,EAAQqB,MAAM2G,gBAAgBxB,SAASC,IACrCY,EAAUV,KAAK,CACba,MAAOf,EAAKwB,UACZJ,UAAW7F,EAAMyE,EAAKyB,kBAAkBhG,OAAO,cAC/C6F,SAAUtB,EAAKwB,WAChB,IAGL,MAAME,EAAkBnI,EAAQqB,MAAM+G,WAAapI,EAAQqB,MAAMgH,SAEjE,IAAIC,EAAiB,GASrB,GAPsB,YAAlBnJ,EAAMiC,QACCkH,GAAA,OAAAjE,EAlDJ,SAAwCpB,EAAuCsF,GACtF,IAAA,MAAW9B,KAAQxD,EACjB,IAAA,MAAWuF,KAAe/B,EAAKgC,cAAgB,GACzC,GAAAD,EAAYD,iBAAmBA,EAC1B,OAAA9B,EAIN,OAAA,IAAA,CA0CIiC,CAAwChG,GAAgBrB,MAAOrB,EAAQqB,MAAMsH,kBAASC,eAAgB,GAEpF,UAAlBzJ,EAAMiC,UACNkH,GAAA,OAAAO,EAtEJ,SAAsC5F,EAAuC6F,GACpF,IAAA,MAAWrC,KAAQxD,EACjB,IAAA,MAAWuF,KAAe/B,EAAKgC,cAAgB,GAC7C,IAAA,MAAWM,KAAcP,EAAYQ,aAAe,GAC9C,GAAAD,EAAWD,aAAeA,EACrB,OAAArC,EAKR,OAAA,IAAA,CA4DIwC,CAAsCvG,GAAgBrB,MAAOrB,EAAQqB,MAAM6H,kBAASN,eAAgB,KAG1GN,EAAQ,CACL,MAAAa,EAAgB,OAAAC,EAAMjK,EAAAkK,iBAAY,EAAAD,EAAA7E,QAAQ5C,GAAMA,EAAEN,OAASlC,EAAMiC,UAAS,GAAG+H,MACnF,OAAOG,EAAU,CACfC,WAAW,EACXrD,QAAS,MAAMiD,+BAAmCA,eAClDK,KAAM,UACNC,0BAA0B,GAC3B,CAIH,MAAMC,EAAerH,GAAUrC,EAAQqB,MAAMsI,aAAe3J,EAAQqB,MAAMuI,QAAS5J,EAAQqB,MAAMe,cAAgBpC,EAAQqB,MAAMwI,WAGzHC,EAA0B5I,KAE1B6I,EAA2B5H,KAG3B6H,EAAyB1C,EAAQtH,EAAQqB,MAAM4I,SAAWjK,EAAQqB,MAAM4I,OAAOvI,KAAKC,GAAWA,EAAEiF,OAAMsD,KAAK,MAAU5C,EAAQtH,EAAQqB,MAAMkF,aAAevG,EAAQqB,MAAMkF,WAAW7E,KAAKC,GAAWA,EAAEiF,OAAMsD,KAAK,KAEjNC,EAAyC,YAAlBhL,EAAMiC,UAA0BpB,EAAQqB,MAAM+I,eAA4C,IAA3BpK,EAAQqB,MAAMC,SAAkB,YAAc,UAEpI+I,EAA0C,UAAlBlL,EAAMiC,UAAuD,IAAhCpB,EAAQqB,MAAMC,SAAkBU,EAAM+H,GAAkBtH,KAAKqH,EAAiB,SAAW,GAC9IQ,EAAsC,YAAlBnL,EAAMiC,UAA0BpB,EAAQqB,MAAM+I,eAAiD,IAAhCpK,EAAQqB,MAAMC,SAAkB,GAAGtB,EAAQqB,MAAMkJ,cAAgBF,QAAa,GAEjKvG,EAAqB9D,EAAQqB,MAAM0C,SAAW/D,EAAQqB,MAAM2C,QAE5DtD,EAAsBV,EAAQqB,MAAMmJ,eAAiBrL,EAAMiC,QAE3DqJ,EAAmBzK,EAAQqB,MAAMqJ,UAAY1K,EAAQqB,MAAMqJ,SAAS,GAAG9D,MAAU5G,EAAQqB,MAAMkF,YAAcvG,EAAQqB,MAAMkF,WAAW,GAAGK,KAEzI+D,EAAoB7J,GAAOO,MAAMkD,QAAQkC,GAAcA,EAAKrD,UAAYjE,EAAMiC,SAAWqF,EAAKmE,OAAOC,SAAS,QAAO,GAAGC,OAGxHC,EAAgC,CACpCzK,MAAOb,EAAUa,MACjBC,MAAOd,EAAUc,MACjByK,QAAS,CACP,CACE7D,UACA8D,cAAe,CACb,CACE7D,QACAC,YACAc,UACAG,WAGJoB,OACAI,kBACAC,qBAGJmB,UAAWlB,EACXF,kBACAC,mBACAC,gBACAG,cACAG,WACAa,SAAU,UACVC,YAAa,QACbtH,aACAuH,aAAc,QACdC,UAAW,IACX5K,cACA+J,UACAE,aAEF/J,GAAcS,MAAMlB,SAAU,EAC9BoL,EACGC,WAAWT,GACXU,MAAK,KACJnC,EAAUoC,QAAQ,QAClB9K,GAAcS,MAAMsK,SAAU,CAAA,IAE/BC,SAAQ,KACPhL,GAAcS,MAAMlB,SAAU,CAAA,GAC/B"}