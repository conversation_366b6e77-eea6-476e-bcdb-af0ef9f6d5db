{"version": 3, "file": "changeHotel-CAzJ7qUQ.js", "sources": ["../../src/layouts/components/Topbar/Toolbar/changeHotel.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"toolbar\": {\r\n      \"store\": \"Hotel\",\r\n      \"selectStore\": \"Please select a hotel\",\r\n      \"shift\": \"Shift\",\r\n      \"selectShift\": \"Please select a shift\",\r\n      \"cancel\": \"Cancel\",\r\n      \"confirm\": \"Confirm\",\r\n      \"switchStore\": \"Switch Store\",\r\n      \"preferences\": \"Preferences\",\r\n      \"hotkeys\": \"Hotkeys\",\r\n      \"logout\": \"Logout\",\r\n    },\r\n    \"早班\": \"Morning Shift\",\r\n    \"中班\": \"Middle Shift\",\r\n    \"晚班\": \"Night Shift\",\r\n    \"凌晨班\": \"Early Shift\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"toolbar\": {\r\n      \"store\": \"门店\",\r\n      \"selectStore\": \"请选择门店\",\r\n      \"shift\": \"班次\",\r\n      \"selectShift\": \"请选择班次\",\r\n      \"cancel\": \"取消\",\r\n      \"confirm\": \"确认\",\r\n      \"switchStore\": \"切换门店\",\r\n      \"preferences\": \"偏好设置\",\r\n      \"hotkeys\": \"快捷键\",\r\n      \"logout\": \"退出登录\",\r\n    },\r\n    \"早班\": \"早班\",\r\n    \"中班\": \"中班\",\r\n    \"晚班\": \"晚班\",\r\n    \"凌晨班\": \"凌晨班\"\r\n  },\r\n  \"km\": {\r\n    \"toolbar\": {\r\n      \"store\": \"សណ្ឋាគារ\",\r\n      \"selectStore\": \"សូមជ្រើសរើសសណ្ឋាគារ\",\r\n      \"shift\": \"វេន\",\r\n      \"selectShift\": \"សូមជ្រើសរើសវេន\",\r\n      \"cancel\": \"បោះបង់\",\r\n      \"confirm\": \"បញ្ជាក់\",\r\n      \"switchStore\": \"ប្តូរសណ្ឋាគារ\",\r\n      \"preferences\": \"ចំណូលចិត្ត\",\r\n      \"hotkeys\": \"គន្លឹះរហ័ស\",\r\n      \"logout\": \"ចាកចេញ\"\r\n    },\r\n    \"早班\": \"វេនព្រឹក\",\r\n    \"中班\": \"វេនថ្ងៃត្រង់\",\r\n    \"晚班\": \"វេនយប់\",\r\n    \"凌晨班\": \"វេនព្រលឹម\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { generalConfigApi, merchantApi, shiftTimeApi } from '@/api/modules/index'\r\nimport userInfo from '@/api/modules/user.api'\r\nimport useMenuStore from '@/store/modules/menu'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\nimport storage from '@/utils/storage'\r\nimport { ArrowDown } from '@element-plus/icons-vue'\r\nimport { ElMessage, ClickOutside as vClickOutside } from 'element-plus'\r\n\r\nimport { computed, ref, unref, watch, onMounted, onUnmounted, nextTick } from 'vue'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\n// 在文件顶部添加类型定义\r\ninterface ErrorResponse {\r\n  code?: number\r\n  msg?: string\r\n  data?: any\r\n  response?: {\r\n    data?: {\r\n      code?: number\r\n      msg?: string\r\n    }\r\n  }\r\n}\r\n\r\ndefineOptions({\r\n  name: 'ToolbarRightSide',\r\n})\r\n\r\n// 确保以下两行代码存在\r\nconst { t } = useI18n()\r\n\r\nconst settingsStore = useSettingsStore()\r\nconst userStore = useUserStore()\r\n\r\n// const avatarError = ref(false)\r\n// watch(\r\n//   () => userStore.avatar,\r\n//   () => {\r\n//     if (avatarError.value) {\r\n//       avatarError.value = false\r\n//     }\r\n//   },\r\n// )\r\nconst merchantData = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  hname: userStore.hname,\r\n  bizDate: userStore.bizDate,\r\n  shiftCode: userStore.shiftCode,\r\n  shiftName: userStore.shiftName,\r\n  token: userStore.token,\r\n  userId: userStore.userId,\r\n  username: userStore.username,\r\n  nickname: userStore.nickname,\r\n  deptId: userStore.deptId,\r\n  subCode: userStore.subCode,\r\n  failure_time: Math.ceil(new Date().getTime() / 1000) + 24 * 60 * 60,\r\n})\r\nconst merchantDataRules = ref<FormRules>({\r\n  hcode: [{ required: true, trigger: 'blur', message: t('toolbar.selectStore') }],\r\n  shiftCode: [{ required: true, trigger: 'blur', message: t('toolbar.selectShift') }],\r\n})\r\n// 班次列表\r\nconst shiftList = ref<{ shiftCode: string; shiftName: string; startTime: string; endTime: string }[]>([])\r\nconst localizedShiftList = computed(() =>\r\n  shiftList.value.map((item) => ({\r\n    ...item,\r\n    shiftName: t(`${item.shiftName}`),\r\n  }))\r\n)\r\nconst merchantRef = ref<FormInstance>()\r\nonMounted(() => {\r\n  getMerchantList()\r\n})\r\nconst merchantList = ref<any>([])\r\nconst showPopover = ref(false)\r\n\r\n// 监听弹窗打开，获取当前门店的班次数据\r\nwatch(showPopover, (newVal, oldVal) => {\r\n  if (newVal && !oldVal && merchantData.value.hcode) {\r\n    // 只有在弹窗从关闭状态变为打开状态时，才获取班次数据\r\n    getShiftList(merchantData.value.hcode)\r\n  }\r\n})\r\n/**\r\n * 获取门店列表\r\n */\r\nfunction getMerchantList() {\r\n  const params = {\r\n    userId: userStore.userId,\r\n    gcode: userStore.gcode,\r\n  }\r\n  merchantApi.getDepartmentSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      merchantList.value = res.data\r\n      // 不再在这里获取班次列表，班次列表会在弹窗打开时获取\r\n    }\r\n  })\r\n}\r\n/**\r\n * 获取班次列表\r\n * @param code\r\n */\r\nfunction getShiftList(code: string) {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: code,\r\n  }\r\n\r\n  // 为 getChangeShiftByShiftSet 接口添加请求头\r\n  const config = {\r\n    headers: {\r\n      'Authorization': `Bearer ${userStore.token}`,\r\n      'Content-Type': 'application/json'\r\n    }\r\n  }\r\n\r\n  shiftTimeApi.getChangeShiftByShiftSet(params, config).then((res: any) => {\r\n    if (res.code === 0) {\r\n      shiftList.value = res.data\r\n      // 如果有数据，设置第一个班次为默认选中\r\n      if (res.data && res.data.length > 0) {\r\n        merchantData.value.shiftCode = res.data[0].shiftCode\r\n      }\r\n    }\r\n  }).catch((error) => {\r\n    console.error('获取班次列表失败:', error)\r\n    ElMessage.error('获取班次列表失败')\r\n  })\r\n}\r\n\r\n// 获取营业日期 generalConfigApi\r\nfunction getData(code: string) {\r\n  const params = {\r\n    hcode: code,\r\n  }\r\n  generalConfigApi.getBizData(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      merchantData.value.bizDate = res.data.bizDate\r\n    }\r\n  })\r\n}\r\n\r\nfunction merchantName(templateCode: string) {\r\n  const selectedTicketData = merchantList.value.find((item: any) => item.hcode === templateCode)\r\n  if (selectedTicketData) {\r\n    merchantData.value.hname = selectedTicketData.name\r\n    merchantData.value.subCode = selectedTicketData.subCode\r\n\r\n    // 获取门店数据\r\n    getData(selectedTicketData.hcode)\r\n\r\n    // 切换门店时，获取新门店的班次列表\r\n    getShiftList(selectedTicketData.hcode)\r\n  } else {\r\n    merchantData.value.hcode = ''\r\n    merchantData.value.hname = ''\r\n    merchantData.value.shiftCode = ''\r\n    shiftList.value = []\r\n  }\r\n}\r\nfunction shiftName(templateCode: string) {\r\n  // 只设置班次名称，不触发其他操作\r\n  const selectedTicketData = shiftList.value.find((item: any) => item.shiftCode === templateCode)\r\n  if (selectedTicketData) {\r\n    merchantData.value.shiftName = selectedTicketData.shiftName\r\n  } else {\r\n    merchantData.value.shiftName = ''\r\n  }\r\n}\r\nconst menuStore = useMenuStore()\r\nfunction submit() {\r\n  merchantRef.value &&\r\n    merchantRef.value.validate((valid) => {\r\n      if (valid) {\r\n        // 获取当前选择的门店数据\r\n        const currentMerchant = merchantList.value.find((item: any) => item.hcode === merchantData.value.hcode)\r\n        userStore\r\n          .login(merchantData.value)\r\n          .then((res) => {\r\n            console.log('userStore.login成功:', res)\r\n\r\n            showPopover.value = false\r\n            menuStore.actived = 0\r\n            settingsStore.settings.menu.menuMode = 'only-side'\r\n            checkMerchant()\r\n          })\r\n          .catch((error) => {\r\n            // 处理错误\r\n            console.error('登录错误:', error)\r\n\r\n            // 将error转换为any类型\r\n            const err: any = error\r\n\r\n            // 尝试从不同的错误结构中获取错误信息\r\n            let errorCode: number | undefined\r\n            let errorMsg: string = ''\r\n\r\n            if (err.response && err.response.data) {\r\n              errorCode = err.response.data.code\r\n              errorMsg = err.response.data.msg || ''\r\n              console.log('从error.response.data中获取错误信息:', errorCode, errorMsg)\r\n            } else if (err.data) {\r\n              errorCode = err.data.code\r\n              errorMsg = err.data.msg || ''\r\n              console.log('从error.data中获取错误信息:', errorCode, errorMsg)\r\n            } else if (err.code !== undefined) {\r\n              errorCode = err.code\r\n              errorMsg = err.msg || ''\r\n              console.log('从error直接获取错误信息:', errorCode, errorMsg)\r\n            }\r\n\r\n            console.log('最终错误信息:', errorCode, errorMsg)\r\n\r\n            // 显示一般错误消息\r\n            ElMessage.error(errorMsg || '操作失败，请稍后重试')\r\n          })\r\n      }\r\n    })\r\n}\r\n// 酒店名称 - 使用响应式数据\r\nconst title = ref(storage.local.has('hname') ? storage.local.get('hname') : '')\r\n\r\n// 监听 localStorage 变化，实时更新酒店名称显示\r\nonMounted(() => {\r\n  // 监听 storage 事件（跨标签页同步）\r\n  window.addEventListener('storage', handleStorageChange)\r\n\r\n  // 定时检查 localStorage 变化（同标签页内的变化）\r\n  const interval = setInterval(() => {\r\n    const currentHname = storage.local.has('hname') ? storage.local.get('hname') : ''\r\n    if (currentHname !== title.value) {\r\n      title.value = currentHname\r\n    }\r\n  }, 1000) // 每秒检查一次\r\n\r\n  // 组件卸载时清理\r\n  onUnmounted(() => {\r\n    clearInterval(interval)\r\n    window.removeEventListener('storage', handleStorageChange)\r\n  })\r\n})\r\n\r\nfunction handleStorageChange(e: StorageEvent) {\r\n  if (e.key === 'hname' && e.newValue) {\r\n    title.value = e.newValue\r\n  }\r\n}\r\n\r\n/**\r\n * 切换班次\r\n */\r\nasync function checkMerchant() {\r\n  const params = {\r\n    gcode: merchantData.value.gcode,\r\n    hcode: merchantData.value.hcode,\r\n    userId: merchantData.value.userId,\r\n    shiftCode: merchantData.value.shiftCode,\r\n    subCode: merchantData.value.subCode,\r\n    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,\r\n  }\r\n  try {\r\n    console.log('调用switchMerchant开始，参数:', params)\r\n    const res: any = await userInfo.switchMerchant(params)\r\n    console.log('switchMerchant返回结果:', res)\r\n\r\n    if (res.code !== 0) {\r\n      // 处理业务错误\r\n      console.error('业务错误:', res)\r\n\r\n      // 处理服务到期错误\r\n      if (res.code === 175000001) {\r\n        console.log('检测到服务到期错误:', res)\r\n        // 通过退出登录跳转到登录界面\r\n        userStore\r\n          .logout()\r\n          .then(() => {\r\n            console.log('已调用退出登录方法，即将跳转到登录页面')\r\n          })\r\n          .catch((logoutErr) => {\r\n            console.error('退出登录失败:', logoutErr)\r\n            // 如果退出登录失败，则直接刷新页面\r\n            setTimeout(() => {\r\n              window.location.reload()\r\n            }, 2000)\r\n          })\r\n        return\r\n      } else {\r\n        // 其他业务错误\r\n        ElMessage.error(res.msg || '操作失败')\r\n        return\r\n      }\r\n    }\r\n\r\n    // 正常处理\r\n    const { data } = res\r\n\r\n    // 使用接口返回的数据更新 localStorage\r\n    if (data.bizDate) {\r\n      storage.local.set('bizDate', data.bizDate)\r\n    }\r\n    if (data.hname) {\r\n      storage.local.set('hname', data.hname)\r\n    }\r\n    if (data.shiftName) {\r\n      storage.local.set('shiftName', data.shiftName)\r\n    }\r\n    if (data.shiftCode) {\r\n      storage.local.set('shiftCode', data.shiftCode)\r\n    }\r\n\r\n    console.log('checkMerchant 更新 localStorage 完成:', {\r\n      bizDate: data.bizDate,\r\n      hname: data.hname,\r\n      shiftName: data.shiftName,\r\n      shiftCode: data.shiftCode\r\n    })\r\n\r\n    location.reload()\r\n  } catch (error) {\r\n    // 处理网络错误或其他异常\r\n    console.error('切换门店失败 (异常):', error)\r\n\r\n    // 将error转换为any类型\r\n    const err: any = error\r\n\r\n    // 尝试从不同的错误结构中获取错误信息\r\n    let errorCode: number | undefined\r\n    let errorMsg: string = ''\r\n\r\n    if (err.response && err.response.data) {\r\n      errorCode = err.response.data.code\r\n      errorMsg = err.response.data.msg || ''\r\n      console.log('从error.response.data中获取错误信息:', errorCode, errorMsg)\r\n    } else if (err.data) {\r\n      errorCode = err.data.code\r\n      errorMsg = err.data.msg || ''\r\n      console.log('从error.data中获取错误信息:', errorCode, errorMsg)\r\n    } else if (err.code !== undefined) {\r\n      errorCode = err.code\r\n      errorMsg = err.msg || ''\r\n      console.log('从error直接获取错误信息:', errorCode, errorMsg)\r\n    }\r\n\r\n    console.log('最终错误信息:', errorCode, errorMsg)\r\n\r\n    // 显示一般错误消息\r\n    ElMessage.error(errorMsg || '操作失败，请稍后重试')\r\n  }\r\n}\r\n\r\nconst popoverRef = ref()\r\nfunction onClickOutside(e: Event) {\r\n  if (showPopover.value && popoverRef.value && popoverRef.value.popperRef && popoverRef.value.popperRef.contentRef && !unref(popoverRef).popperRef.contentRef.contains(e.target as Node)) {\r\n    showPopover.value = false\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"flex items-center justify-center\">\r\n    <el-popover\r\n      ref=\"popoverRef\"\r\n      placement=\"left-start\"\r\n      :width=\"300\"\r\n      v-model:visible=\"showPopover\"\r\n      trigger=\"manual\"\r\n      :popper-options=\"{\r\n        modifiers: [{\r\n          name: 'eventListeners',\r\n          options: { scroll: false, resize: false }\r\n        }]\r\n      }\"\r\n    >\r\n      <div style=\"padding: 15px\">\r\n        <el-form ref=\"merchantRef\" :model=\"merchantData\" :rules=\"merchantDataRules\">\r\n          <el-form-item :label=\"t('toolbar.store')\" prop=\"hcode\">\r\n            <el-select\r\n              v-model=\"merchantData.hcode\"\r\n              :placeholder=\"t('toolbar.selectStore')\"\r\n              @change=\"merchantName($event)\"\r\n              :teleported=\"false\"\r\n              popper-class=\"select-dropdown-in-popover\"\r\n            >\r\n              <el-option v-for=\"item in merchantList\" :key=\"item.hcode\" :label=\"item.name\" :value=\"item.hcode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('toolbar.shift')\" prop=\"shiftCode\">\r\n            <el-select\r\n              v-model=\"merchantData.shiftCode\"\r\n              :placeholder=\"t('toolbar.selectShift')\"\r\n              @change=\"shiftName($event)\"\r\n              :teleported=\"false\"\r\n              popper-class=\"select-dropdown-in-popover\"\r\n            >\r\n              <el-option v-for=\"item in localizedShiftList\" :key=\"item.shiftCode\" :label=\"item.shiftName\" :value=\"item.shiftCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <div class=\"mb-4\" style=\"text-align: center\">\r\n            <el-button @click=\"showPopover = false\">\r\n              {{ t('toolbar.cancel') }}\r\n            </el-button>\r\n            <el-button type=\"primary\" @click=\"submit\">\r\n              {{ t('toolbar.confirm') }}\r\n            </el-button>\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n      <template #reference>\r\n        <div v-click-outside=\"onClickOutside\" class=\"flex cursor-[pointer] items-center justify-center text-[16px]\" @click=\"showPopover = true\">\r\n          {{ title }}\r\n          <el-icon class=\"ml-[5px]\">\r\n            <ArrowDown />\r\n          </el-icon>\r\n        </div>\r\n      </template>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 保持样式不变 */\r\n</style>\r\n"], "names": ["t", "useI18n", "settingsStore", "useSettingsStore", "userStore", "useUserStore", "merchantData", "ref", "gcode", "hcode", "hname", "bizDate", "shiftCode", "shiftName", "token", "userId", "username", "nickname", "deptId", "subCode", "failure_time", "Math", "ceil", "Date", "getTime", "merchantDataRules", "required", "trigger", "message", "shiftList", "localizedShiftList", "computed", "value", "map", "item", "merchantRef", "onMounted", "params", "merchantApi", "getDepartmentSimpleList", "then", "res", "code", "merchantList", "data", "getMerchantList", "showPopover", "getShiftList", "config", "headers", "Authorization", "shiftTimeApi", "getChangeShiftByShiftSet", "length", "catch", "error", "console", "ElMessage", "merchantName", "templateCode", "selectedTicketData", "find", "name", "generalConfigApi", "getBizData", "getData", "watch", "newVal", "oldVal", "menuStore", "useMenuStore", "submit", "validate", "valid", "login", "log", "actived", "settings", "menu", "menuMode", "async", "timeZone", "Intl", "DateTimeFormat", "resolvedOptions", "userInfo", "switchMerchant", "logout", "logoutErr", "setTimeout", "window", "location", "reload", "msg", "storage", "local", "set", "err", "errorCode", "errorMsg", "response", "checkMerchant", "title", "has", "get", "handleStorageChange", "e", "key", "newValue", "addEventListener", "interval", "setInterval", "currentHname", "onUnmounted", "clearInterval", "removeEventListener", "popoverRef", "onClickOutside", "popperRef", "contentRef", "unref", "contains", "target"], "mappings": "g6BA2FM,MAAAA,EAAEA,GAAMC,IAERC,EAAgBC,IAChBC,EAAYC,IAWZC,EAAeC,EAAI,CACvBC,MAAOJ,EAAUI,MACjBC,MAAOL,EAAUK,MACjBC,MAAON,EAAUM,MACjBC,QAASP,EAAUO,QACnBC,UAAWR,EAAUQ,UACrBC,UAAWT,EAAUS,UACrBC,MAAOV,EAAUU,MACjBC,OAAQX,EAAUW,OAClBC,SAAUZ,EAAUY,SACpBC,SAAUb,EAAUa,SACpBC,OAAQd,EAAUc,OAClBC,QAASf,EAAUe,QACnBC,aAAcC,KAAKC,MAAA,IAASC,MAAOC,UAAY,KAAQ,QAEnDC,EAAoBlB,EAAe,CACvCE,MAAO,CAAC,CAAEiB,UAAU,EAAMC,QAAS,OAAQC,QAAS5B,EAAE,yBACtDY,UAAW,CAAC,CAAEc,UAAU,EAAMC,QAAS,OAAQC,QAAS5B,EAAE,2BAGtD6B,EAAYtB,EAAoF,IAChGuB,EAAqBC,GAAS,IAClCF,EAAUG,MAAMC,KAAKC,IAAU,IAC1BA,EACHrB,UAAWb,EAAE,GAAGkC,EAAKrB,mBAGnBsB,EAAc5B,IACpB6B,GAAU,MAgBV,WACE,MAAMC,EAAS,CACbtB,OAAQX,EAAUW,OAClBP,MAAOJ,EAAUI,OAEnB8B,EAAYC,wBAAwBF,GAAQG,MAAMC,IAC/B,IAAbA,EAAIC,OACNC,EAAaX,MAAQS,EAAIG,KAAA,GAG5B,CAzBeC,EAAA,IAEZ,MAAAF,EAAepC,EAAS,IACxBuC,EAAcvC,GAAI,GA4BxB,SAASwC,EAAaL,GACpB,MAAML,EAAS,CACb7B,MAAOJ,EAAUI,MACjBC,MAAOiC,GAIHM,EAAS,CACbC,QAAS,CACPC,cAAiB,UAAU9C,EAAUU,QACrC,eAAgB,qBAIpBqC,EAAaC,yBAAyBf,EAAQW,GAAQR,MAAMC,IACzC,IAAbA,EAAIC,OACNb,EAAUG,MAAQS,EAAIG,KAElBH,EAAIG,MAAQH,EAAIG,KAAKS,OAAS,IAChC/C,EAAa0B,MAAMpB,UAAY6B,EAAIG,KAAK,GAAGhC,WAC7C,IAED0C,OAAOC,IACAC,QAAAD,MAAM,YAAaA,GAC3BE,EAAUF,MAAM,WAAU,GAC3B,CAeH,SAASG,EAAaC,GACd,MAAAC,EAAqBjB,EAAaX,MAAM6B,MAAM3B,GAAcA,EAAKzB,QAAUkD,IAC7EC,GACWtD,EAAA0B,MAAMtB,MAAQkD,EAAmBE,KACjCxD,EAAA0B,MAAMb,QAAUyC,EAAmBzC,QAfpD,SAAiBuB,GACf,MAAML,EAAS,CACb5B,MAAOiC,GAETqB,EAAiBC,WAAW3B,GAAQG,MAAMC,IACvB,IAAbA,EAAIC,OACOpC,EAAA0B,MAAMrB,QAAU8B,EAAIG,KAAKjC,QAAA,GAEzC,CAUCsD,CAAQL,EAAmBnD,OAG3BsC,EAAaa,EAAmBnD,SAEhCH,EAAa0B,MAAMvB,MAAQ,GAC3BH,EAAa0B,MAAMtB,MAAQ,GAC3BJ,EAAa0B,MAAMpB,UAAY,GAC/BiB,EAAUG,MAAQ,GACpB,CAjFIkC,EAAApB,GAAa,CAACqB,EAAQC,KACtBD,IAAWC,GAAU9D,EAAa0B,MAAMvB,OAE7BsC,EAAAzC,EAAa0B,MAAMvB,MAAK,IAyFzC,MAAM4D,EAAYC,IAClB,SAASC,IACPpC,EAAYH,OACVG,EAAYH,MAAMwC,UAAUC,IACtBA,IAEmC9B,EAAAX,MAAM6B,MAAM3B,GAAcA,EAAKzB,QAAUH,EAAa0B,MAAMvB,QACjGL,EACGsE,MAAMpE,EAAa0B,OACnBQ,MAAMC,IACGe,QAAAmB,IAAI,qBAAsBlC,GAElCK,EAAYd,OAAQ,EACpBqC,EAAUO,QAAU,EACN1E,EAAA2E,SAASC,KAAKC,SAAW,YAoEnDC,iBACE,MAAM3C,EAAS,CACb7B,MAAOF,EAAa0B,MAAMxB,MAC1BC,MAAOH,EAAa0B,MAAMvB,MAC1BM,OAAQT,EAAa0B,MAAMjB,OAC3BH,UAAWN,EAAa0B,MAAMpB,UAC9BO,QAASb,EAAa0B,MAAMb,QAC5B8D,SAAUC,KAAKC,iBAAiBC,kBAAkBH,UAEhD,IACMzB,QAAAmB,IAAI,yBAA0BtC,GACtC,MAAMI,QAAiB4C,EAASC,eAAejD,GAG3C,GAFImB,QAAAmB,IAAI,sBAAuBlC,GAElB,IAAbA,EAAIC,KAKF,OAHIc,QAAAD,MAAM,QAASd,GAGN,YAAbA,EAAIC,MACEc,QAAAmB,IAAI,aAAclC,QAGvBrC,EAAAmF,SACA/C,MAAK,KACJgB,QAAQmB,IAAI,sBAAqB,IAElCrB,OAAOkC,IACEhC,QAAAD,MAAM,UAAWiC,GAEzBC,YAAW,KACTC,OAAOC,SAASC,QAAO,GACtB,IAAI,UAKDnC,EAAAF,MAAMd,EAAIoD,KAAO,QAMzB,MAAAjD,KAAEA,GAASH,EAGbG,EAAKjC,SACPmF,EAAQC,MAAMC,IAAI,UAAWpD,EAAKjC,SAEhCiC,EAAKlC,OACPoF,EAAQC,MAAMC,IAAI,QAASpD,EAAKlC,OAE9BkC,EAAK/B,WACPiF,EAAQC,MAAMC,IAAI,YAAapD,EAAK/B,WAElC+B,EAAKhC,WACPkF,EAAQC,MAAMC,IAAI,YAAapD,EAAKhC,WAGtC4C,QAAQmB,IAAI,oCAAqC,CAC/ChE,QAASiC,EAAKjC,QACdD,MAAOkC,EAAKlC,MACZG,UAAW+B,EAAK/B,UAChBD,UAAWgC,EAAKhC,YAGlB+E,SAASC,eACFrC,GAECC,QAAAD,MAAM,eAAgBA,GAG9B,MAAM0C,EAAW1C,EAGb,IAAA2C,EACAC,EAAmB,GAEnBF,EAAIG,UAAYH,EAAIG,SAASxD,MACnBsD,EAAAD,EAAIG,SAASxD,KAAKF,KACnByD,EAAAF,EAAIG,SAASxD,KAAKiD,KAAO,GAC5BrC,QAAAmB,IAAI,+BAAgCuB,EAAWC,IAC9CF,EAAIrD,MACbsD,EAAYD,EAAIrD,KAAKF,KACVyD,EAAAF,EAAIrD,KAAKiD,KAAO,GACnBrC,QAAAmB,IAAI,sBAAuBuB,EAAWC,SACxB,IAAbF,EAAIvD,OACbwD,EAAYD,EAAIvD,KAChByD,EAAWF,EAAIJ,KAAO,GACdrC,QAAAmB,IAAI,kBAAmBuB,EAAWC,IAGpC3C,QAAAmB,IAAI,UAAWuB,EAAWC,GAGxB1C,EAAAF,MAAM4C,GAAY,aAAY,CAC1C,CAnKwBE,EAAA,IAEf/C,OAAOC,IAEEC,QAAAD,MAAM,QAASA,GAGvB,MAAM0C,EAAW1C,EAGb,IAAA2C,EACAC,EAAmB,GAEnBF,EAAIG,UAAYH,EAAIG,SAASxD,MACnBsD,EAAAD,EAAIG,SAASxD,KAAKF,KACnByD,EAAAF,EAAIG,SAASxD,KAAKiD,KAAO,GAC5BrC,QAAAmB,IAAI,+BAAgCuB,EAAWC,IAC9CF,EAAIrD,MACbsD,EAAYD,EAAIrD,KAAKF,KACVyD,EAAAF,EAAIrD,KAAKiD,KAAO,GACnBrC,QAAAmB,IAAI,sBAAuBuB,EAAWC,SACxB,IAAbF,EAAIvD,OACbwD,EAAYD,EAAIvD,KAChByD,EAAWF,EAAIJ,KAAO,GACdrC,QAAAmB,IAAI,kBAAmBuB,EAAWC,IAGpC3C,QAAAmB,IAAI,UAAWuB,EAAWC,GAGxB1C,EAAAF,MAAM4C,GAAY,aAAY,IACzC,GAEN,CAGL,MAAMG,GAAQ/F,EAAIuF,EAAQC,MAAMQ,IAAI,SAAWT,EAAQC,MAAMS,IAAI,SAAW,IAsB5E,SAASC,GAAoBC,GACb,UAAVA,EAAEC,KAAmBD,EAAEE,WACzBN,GAAMtE,MAAQ0E,EAAEE,SAClB,CAtBFxE,GAAU,KAEDsD,OAAAmB,iBAAiB,UAAWJ,IAG7B,MAAAK,EAAWC,aAAY,KACrB,MAAAC,EAAelB,EAAQC,MAAMQ,IAAI,SAAWT,EAAQC,MAAMS,IAAI,SAAW,GAC3EQ,IAAiBV,GAAMtE,QACzBsE,GAAMtE,MAAQgF,EAAA,GAEf,KAGHC,GAAY,KACVC,cAAcJ,GACPpB,OAAAyB,oBAAoB,UAAWV,GAAmB,GAC1D,IA+GH,MAAMW,GAAa7G,IACnB,SAAS8G,GAAeX,GAClB5D,EAAYd,OAASoF,GAAWpF,OAASoF,GAAWpF,MAAMsF,WAAaF,GAAWpF,MAAMsF,UAAUC,aAAeC,EAAMJ,IAAYE,UAAUC,WAAWE,SAASf,EAAEgB,UACrK5E,EAAYd,OAAQ,EACtB,4zCAlMF,SAAmB2B,GAEX,MAAAC,EAAqB/B,EAAUG,MAAM6B,MAAM3B,GAAcA,EAAKtB,YAAc+C,IAEnErD,EAAA0B,MAAMnB,UADjB+C,EAC6BA,EAAmB/C,UAEnB,EACjC"}