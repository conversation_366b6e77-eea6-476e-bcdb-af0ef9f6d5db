{"version": 3, "file": "item.vue_vue_type_script_setup_true_lang-fTC7BlVv.js", "sources": ["../../src/layouts/components/Breadcrumb/item.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { RouteLocationRaw } from 'vue-router'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    to?: RouteLocationRaw\r\n    replace?: boolean\r\n    separator?: string\r\n  }>(),\r\n  {\r\n    separator: '/',\r\n  },\r\n)\r\n\r\nconst router = useRouter()\r\n\r\nfunction onClick() {\r\n  if (props.to) {\r\n    props.replace ? router.replace(props.to) : router.push(props.to)\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"breadcrumb-item flex items-center text-dark dark-text-white\">\r\n    <span class=\"separator mx-2\">\r\n      {{ separator }}\r\n    </span>\r\n    <span\r\n      class=\"text flex items-center opacity-60\"\r\n      :class=\"{\r\n        'is-link cursor-pointer transition-opacity hover-opacity-100': !!props.to,\r\n      }\" @click=\"onClick\"\r\n    >\r\n      <slot />\r\n    </span>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "router", "useRouter", "onClick", "to", "replace", "push"], "mappings": "8RAGA,MAAMA,EAAQC,EAWRC,EAASC,IAEf,SAASC,IACHJ,EAAMK,KACFL,EAAAM,QAAUJ,EAAOI,QAAQN,EAAMK,IAAMH,EAAOK,KAAKP,EAAMK,IAC/D"}