import{d as e,B as s,am as a,o as t,e as o,w as r,c as d,F as l,ag as i,u as n,Y as b,ab as p,a6 as u}from"./index-CkEhI1Zk.js";const c=["disabled","onClick"],h=e({__name:"HDropdownMenu",props:{items:{}},setup(e){const h=e,g=s((()=>h.items.map((e=>e.filter((e=>!e.hide)))).filter((e=>e.length))));return(e,s)=>{const h=a("VMenu");return t(),o(h,u({"show-triggers":["hover"],"auto-hide":!1,"popper-triggers":["hover","click"],delay:200},e.$attrs),{popper:r((()=>[(t(!0),d(l,null,i(n(g),((e,s)=>(t(),d("div",{key:s,class:"b-b-1 b-b-stone-2 b-b-solid p-1 dark-b-b-1 dark-b-b-stone-7 last-b-b-size-0"},[(t(!0),d(l,null,i(e,((e,s)=>(t(),d("button",{key:s,disabled:e.disabled,class:"w-full flex cursor-pointer items-center gap-2 border-size-0 rounded-md bg-inherit px-2 py-1.5 text-sm text-dark disabled-cursor-not-allowed dark-text-white disabled-opacity-50 hover-not-disabled-bg-stone-1 dark-hover-not-disabled-bg-stone-9",onClick:e.handle},b(e.label),9,c)))),128))])))),128))])),default:r((()=>[p(e.$slots,"default")])),_:3},16)}}});export{h as _};
//# sourceMappingURL=HDropdownMenu.vue_vue_type_script_setup_true_lang-BvbgENOL.js.map
