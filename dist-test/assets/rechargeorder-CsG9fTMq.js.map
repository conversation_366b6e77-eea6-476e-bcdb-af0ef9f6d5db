{"version": 3, "file": "rechargeorder-CsG9fTMq.js", "sources": ["../../src/views/marketing/sms/stat/components/rechargeorder.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"dataStatistics\": \"Data Statistics\",\r\n    \"rechargeDate\": \"Recharge Date\",\r\n    \"startDate\": \"Start Date\",\r\n    \"endDate\": \"End Date\",\r\n    \"orderStatus\": \"Order Status\",\r\n    \"all\": \"All\",\r\n    \"paid\": \"Paid\",\r\n    \"unpaid\": \"Unpaid\",\r\n    \"expired\": \"Expired\",\r\n    \"filter\": \"Filter\",\r\n    \"orderNumber\": \"Order Number\",\r\n    \"rechargeTime\": \"Recharge Time\",\r\n    \"rechargeAmount\": \"Recharge Amount\",\r\n    \"smsCount\": \"SMS Count\",\r\n    \"includeGifted\": \"(Including {giveNum} gifted messages)\",\r\n    \"paymentMethod\": \"Payment Method\",\r\n    \"rechargeStore\": \"Recharge Store\",\r\n    \"paymentSerialNumber\": \"Payment Serial Number\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"dataStatistics\": \"数据统计\",\r\n    \"rechargeDate\": \"充值日期\",\r\n    \"startDate\": \"开始日期\",\r\n    \"endDate\": \"结束日期\",\r\n    \"orderStatus\": \"订单状态\",\r\n    \"all\": \"全部\",\r\n    \"paid\": \"已支付\",\r\n    \"unpaid\": \"未支付\",\r\n    \"expired\": \"已过期\",\r\n    \"filter\": \"筛选\",\r\n    \"orderNumber\": \"订单号\",\r\n    \"rechargeTime\": \"充值时间\",\r\n    \"rechargeAmount\": \"充值金额\",\r\n    \"smsCount\": \"短信条数\",\r\n    \"includeGifted\": \"(含赠送{giveNum}条)\",\r\n    \"paymentMethod\": \"支付方式\",\r\n    \"rechargeStore\": \"充值门店\",\r\n    \"paymentSerialNumber\": \"支付流水号\"\r\n  },\r\n  \"km\": {\r\n    \"dataStatistics\": \"ស្ថិតិទិន្នន័យ\",\r\n    \"rechargeDate\": \"កាលបរិច្ឆេទបញ្ចូលប្រាក់\",\r\n    \"startDate\": \"កាលបរិច្ឆេទចាប់ផ្តើម\",\r\n    \"endDate\": \"កាលបរិច្ឆេទបញ្ចប់\",\r\n    \"orderStatus\": \"ស្ថានភាពការបញ្ជាទិញ\",\r\n    \"all\": \"ទាំងអស់\",\r\n    \"paid\": \"បានបង់\",\r\n    \"unpaid\": \"មិនទាន់បានបង់\",\r\n    \"expired\": \"ផុតកំណត់\",\r\n    \"filter\": \"តម្រង\",\r\n    \"orderNumber\": \"លេខការបញ្ជាទិញ\",\r\n    \"rechargeTime\": \"ពេលវេលាបញ្ចូលប្រាក់\",\r\n    \"rechargeAmount\": \"ចំនួនប្រាក់បញ្ចូល\",\r\n    \"smsCount\": \"ចំនួនសារ\",\r\n    \"includeGifted\": \"(រួមទាំងសារដែលបានផ្តល់ជូន {giveNum})\",\r\n    \"paymentMethod\": \"វិធីសារបង់ប្រាក់\",\r\n    \"rechargeStore\": \"ហាងបញ្ចូលប្រាក់\",\r\n    \"paymentSerialNumber\": \"លេខសៀវភៅបង់ប្រាក់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { SmsRechargeOrderModel } from '@/models/index'\r\nimport { smsRechargeOrderApi } from '@/api/modules/index'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\ndefineOptions({\r\n  name: 'MarketingSmsRechargeOrderList',\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst router = useRouter()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\nconst tabbar = useTabbar()\r\nconst settingsStore = useSettingsStore()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n\r\n  // 搜索\r\n  search: {\r\n    /** 发送方式 -1：全部 0：自动 1：群发 */\r\n    sendMode: '-1',\r\n    hcode: '',\r\n    dateStart: '',\r\n    dateEnd: '',\r\n    /**\r\n     * 状态\r\n     * -1: 全部\r\n     * 0: 未支付\r\n     * 1: 已支付\r\n     * 2: 已过期\r\n     */\r\n    state: '-1',\r\n  },\r\n\r\n  // 列表数据\r\n  dataList: [] as SmsRechargeOrderModel[],\r\n})\r\n\r\nonMounted(() => {\r\n  getDataList()\r\n})\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    startTime: data.value.search.dateStart ? dayjs(data.value.search.dateStart).format('YYYY-MM-DD') : '',\r\n    endTime: data.value.search.dateEnd ? dayjs(data.value.search.dateEnd).format('YYYY-MM-DD') : '',\r\n    state: data.value.search.state === '-1' ? '' : data.value.search.state,\r\n  }\r\n  smsRechargeOrderApi.pageList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data.list\r\n    pagination.value.total = res.data.total\r\n  })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <search-bar :show-toggle=\"false\">\r\n        <el-form :model=\"data.search\" size=\"default\" label-width=\"100px\" inline-message inline class=\"search-form\">\r\n          <el-form-item :label=\"t('rechargeDate')\">\r\n            <el-date-picker v-model=\"data.search.startTime\" type=\"date\" :placeholder=\"t('startDate')\" style=\"width: 150px\" />&nbsp;-&nbsp;\r\n            <el-date-picker v-model=\"data.search.endTime\" type=\"date\" :placeholder=\"t('endDate')\" style=\"width: 150px\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"t('orderStatus')\">\r\n            <el-radio-group v-model=\"data.search.state\">\r\n              <el-radio-button value=\"-1\">\r\n                {{ t('all') }}\r\n              </el-radio-button>\r\n              <el-radio-button value=\"1\">\r\n                {{ t('paid') }}\r\n              </el-radio-button>\r\n              <el-radio-button value=\"0\">\r\n                {{ t('unpaid') }}\r\n              </el-radio-button>\r\n              <el-radio-button value=\"2\">\r\n                {{ t('expired') }}\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item style=\"float: right\">\r\n            <el-button type=\"primary\" @click=\"currentChange()\">\r\n              <template #icon>\r\n                <svg-icon name=\"ep:search\" />\r\n              </template>\r\n              {{ t('filter') }}\r\n            </el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </search-bar>\r\n      <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\" @sort-change=\"sortChange\">\r\n        <el-table-column prop=\"orderNo\" :label=\"t('orderNumber')\" />\r\n        <el-table-column prop=\"payTime\" :label=\"t('rechargeTime')\" />\r\n        <el-table-column :label=\"t('rechargeAmount')\" align=\"right\">\r\n          <template #default=\"{ row }\">\r\n            <span>￥{{ row.fee }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('smsCount')\" align=\"right\">\r\n          <template #default=\"{ row }\">\r\n            <span>{{ row.smsNum + row.giveNum }}{{ t('includeGifted', { giveNum: row.giveNum }) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"payMode\" :label=\"t('paymentMethod')\" />\r\n        <el-table-column prop=\"hname\" :label=\"t('rechargeStore')\" />\r\n        <el-table-column :label=\"t('orderStatus')\">\r\n          <template #default=\"{ row }\">\r\n            <el-tag :type=\"row.state === '1' ? 'success' : row.state === '0' ? 'info' : 'danger'\">\r\n              {{ row.state === '1' ? t('paid') : row.state === '0' ? t('unpaid') : t('expired') }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"paySerialNo\" :label=\"t('paymentSerialNumber')\" />\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n    </page-main>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "useRouter", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "useTabbar", "useSettingsStore", "data", "ref", "loading", "tableAutoHeight", "search", "sendMode", "hcode", "dateStart", "dateEnd", "state", "dataList", "getDataList", "value", "params", "gcode", "startTime", "dayjs", "format", "endTime", "smsRechargeOrderApi", "pageList", "then", "res", "list", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "onMounted"], "mappings": "iuCA4EM,MAAAA,EAAEA,GAAMC,IAERC,EAAYC,IACOC,IACzB,MAAMC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IACtDC,IACcC,IAEvC,MAAMC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAGjBC,OAAQ,CAENC,SAAU,KACVC,MAAO,GACPC,UAAW,GACXC,QAAS,GAQTC,MAAO,MAITC,SAAU,KAOZ,SAASC,IACPX,EAAKY,MAAMV,SAAU,EACrB,MAAMW,EAAS,IACVpB,IACHqB,MAAOzB,EAAUyB,MACjBR,MAAOjB,EAAUiB,MACjBS,UAAWf,EAAKY,MAAMR,OAAOG,UAAYS,EAAMhB,EAAKY,MAAMR,OAAOG,WAAWU,OAAO,cAAgB,GACnGC,QAASlB,EAAKY,MAAMR,OAAOI,QAAUQ,EAAMhB,EAAKY,MAAMR,OAAOI,SAASS,OAAO,cAAgB,GAC7FR,MAAmC,OAA5BT,EAAKY,MAAMR,OAAOK,MAAiB,GAAKT,EAAKY,MAAMR,OAAOK,OAEnEU,EAAoBC,SAASP,GAAQQ,MAAMC,IACzCtB,EAAKY,MAAMV,SAAU,EAChBF,EAAAY,MAAMF,SAAWY,EAAItB,KAAKuB,KACpB/B,EAAAoB,MAAMY,MAAQF,EAAItB,KAAKwB,KAAA,GACnC,CAIH,SAASC,EAAWC,GAClBhC,EAAagC,GAAML,MAAK,IAAMV,KAAa,CAIpC,SAAAgB,EAAcC,EAAO,GAC5BjC,EAAgBiC,GAAMP,MAAK,IAAMV,KAAa,CAIhD,SAASkB,GAAWC,KAAEA,EAAMC,MAAAA,IAC1BnC,EAAakC,EAAMC,GAAOV,MAAK,IAAMV,KAAa,QAjCpDqB,GAAU,KACIrB,GAAA"}