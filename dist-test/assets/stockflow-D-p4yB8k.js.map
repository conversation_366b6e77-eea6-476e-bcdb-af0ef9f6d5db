{"version": 3, "file": "stockflow-D-p4yB8k.js", "sources": ["../../src/views/merchant/system/config/components/stockflow.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"activationStatus\": \"Activation Status Stockflow Management\",\r\n    \"edit\": \"Edit\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"editSuccess\": \"Edit successful\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"activationStatus\": \"是否启用进销存管理\",\r\n    \"edit\": \"编辑\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"editSuccess\": \"修改成功\"\r\n  },\r\n  \"km\": {\r\n    \"activationStatus\": \"ស្ថានភាពដំណើរការគ្រប់គ្រងស្តុក\",\r\n    \"edit\": \"កែសម្រួល\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type Form from '@/types/table/EasyForm'\r\nimport { hotelParamConfigApi } from '@/api/modules/index'\r\nimport { BooleanEnum } from '@/models'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst isEdit = ref(false)\r\n/** 查询条件 */\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\nconst form = ref({\r\n  ...queryParams,\r\n  cleanAudit: BooleanEnum.NO, // 进销存管理;0:否 1:是\r\n})\r\n/** form内容(初始化) */\r\nconst _modelForm = reactive({\r\n  ...queryParams,\r\n  /** 值 */\r\n  value: '',\r\n})\r\n/** form内容(初始化) */\r\nconst _modelForm1 = reactive({\r\n  ...queryParams,\r\n  /** 名称 */\r\n  name: '',\r\n})\r\n/** form内容 */\r\nconst modelForm = ref({ ..._modelForm })\r\n/** form内容 */\r\nconst modelForm1 = ref({ ..._modelForm1 })\r\n/** form配置(初始化) */\r\nconst _options = reactive<Form.Options>({\r\n  labelSuffix: '：',\r\n  labelWidth: 300,\r\n  showSubmitButton: true,\r\n  submitButtonText: t('edit'),\r\n  submitButtonOptions: {\r\n    plain: true,\r\n    type: '',\r\n    loading: false,\r\n  },\r\n})\r\n/** form配置(编辑) */\r\nconst _editOptions = reactive<Form.Options>({\r\n  labelSuffix: '：',\r\n  labelWidth: 300,\r\n  showCancelButton: true,\r\n  showSubmitButton: true,\r\n  submitButtonText: t('save'),\r\n  cancelButtonText: t('cancel'),\r\n  submitButtonOptions: {\r\n    plain: true,\r\n    type: 'primary',\r\n    loading: false,\r\n  },\r\n})\r\n/** form配置(编辑) */\r\nconst options = ref<Form.Options>({ ..._editOptions })\r\n/** form配置 */\r\nconst options1 = ref<Form.Options>({ ..._options })\r\n/** 分类form表单(编辑) */\r\nconst ruleFieldList = reactive<Form.FieldItem[]>([\r\n  {\r\n    label: t('activationStatus'),\r\n    field: 'value',\r\n    type: 'switch',\r\n    options: {\r\n      switchOptions: {\r\n        activeValue: '1',\r\n        inactiveValue: '0',\r\n        inlinePrompt: true,\r\n        activeText: t('yes'),\r\n        inactiveText: t('no'),\r\n      },\r\n    },\r\n  },\r\n])\r\n/** 分类form表单 */\r\nconst ruleFieldList1 = reactive<Form.FieldItem[]>([\r\n  {\r\n    label: t('activationStatus'),\r\n    field: 'name',\r\n    type: 'show',\r\n  },\r\n])\r\nonMounted(() => {\r\n  getInfo()\r\n})\r\n\r\nasync function getInfo() {\r\n  const { code, data } = await hotelParamConfigApi.getWarehouse(queryParams)\r\n  if (code === 0) {\r\n    modelForm.value.value = data\r\n    modelForm1.value.name = data == 1 ? '是' : '否'\r\n  }\r\n}\r\n/** 编辑保存 */\r\nasync function submitForm(val) {\r\n  if (isEdit.value) {\r\n    await hotelParamConfigApi.updateWarehouse(val)\r\n    ElMessage.success(t('editSuccess'))\r\n    getInfo()\r\n  }\r\n  isEdit.value = !isEdit.value\r\n}\r\n/** 取消保存 */\r\nfunction cancelForm(formEl: FormInstance | undefined) {\r\n  formEl.resetFields()\r\n  isEdit.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"ml-[20px]\">\r\n    <EasyForm v-if=\"isEdit\" :field-list=\"ruleFieldList\" :model=\"modelForm\" :options=\"options\" @cancel=\"cancelForm\" @submit=\"submitForm\" />\r\n    <EasyForm v-else :field-list=\"ruleFieldList1\" :model=\"modelForm1\" :options=\"options1\" @submit=\"submitForm\" />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "isEdit", "ref", "queryParams", "reactive", "gcode", "hcode", "clean<PERSON><PERSON><PERSON>", "BooleanEnum", "NO", "_modelForm", "value", "_modelForm1", "name", "modelForm", "modelForm1", "_options", "labelSuffix", "labelWidth", "showSubmitButton", "submitButtonText", "submitButtonOptions", "plain", "type", "loading", "_editOptions", "showCancelButton", "cancelButtonText", "options", "options1", "ruleFieldList", "label", "field", "switchOptions", "activeValue", "inactiveValue", "inlinePrompt", "activeText", "inactiveText", "ruleFieldList1", "async", "getInfo", "code", "data", "hotelParamConfigApi", "getWarehouse", "submitForm", "val", "updateWarehouse", "ElMessage", "success", "cancelForm", "formEl", "resetFields", "onMounted"], "mappings": "u8BAsCA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAASC,GAAI,GAEbC,EAAcC,EAAiB,CACnCC,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,QAEFJ,EAAA,IACZC,EACHI,WAAYC,EAAYC,KAG1B,MAAMC,EAAaN,EAAS,IACvBD,EAEHQ,MAAO,KAGHC,EAAcR,EAAS,IACxBD,EAEHU,KAAM,KAGFC,EAAYZ,EAAI,IAAKQ,IAErBK,EAAab,EAAI,IAAKU,IAEtBI,EAAWZ,EAAuB,CACtCa,YAAa,IACbC,WAAY,IACZC,kBAAkB,EAClBC,iBAAkBrB,EAAE,QACpBsB,oBAAqB,CACnBC,OAAO,EACPC,KAAM,GACNC,SAAS,KAIPC,EAAerB,EAAuB,CAC1Ca,YAAa,IACbC,WAAY,IACZQ,kBAAkB,EAClBP,kBAAkB,EAClBC,iBAAkBrB,EAAE,QACpB4B,iBAAkB5B,EAAE,UACpBsB,oBAAqB,CACnBC,OAAO,EACPC,KAAM,UACNC,SAAS,KAIPI,EAAU1B,EAAkB,IAAKuB,IAEjCI,EAAW3B,EAAkB,IAAKc,IAElCc,EAAgB1B,EAA2B,CAC/C,CACE2B,MAAOhC,EAAE,oBACTiC,MAAO,QACPT,KAAM,SACNK,QAAS,CACPK,cAAe,CACbC,YAAa,IACbC,cAAe,IACfC,cAAc,EACdC,WAAYtC,EAAE,OACduC,aAAcvC,EAAE,WAMlBwC,EAAiBnC,EAA2B,CAChD,CACE2B,MAAOhC,EAAE,oBACTiC,MAAO,OACPT,KAAM,UAOViB,eAAeC,IACb,MAAMC,KAAEA,EAAMC,KAAAA,SAAeC,EAAoBC,aAAa1C,GACjD,IAATuC,IACF5B,EAAUH,MAAMA,MAAQgC,EACxB5B,EAAWJ,MAAME,KAAe,GAAR8B,EAAY,IAAM,IAC5C,CAGFH,eAAeM,EAAWC,GACpB9C,EAAOU,cACHiC,EAAoBI,gBAAgBD,GAChCE,EAAAC,QAAQnD,EAAE,gBACZ0C,KAEHxC,EAAAU,OAASV,EAAOU,KAAA,CAGzB,SAASwC,EAAWC,GAClBA,EAAOC,cACPpD,EAAOU,OAAQ,CAAA,QAvBjB2C,GAAU,KACAb,GAAA"}