{"version": 3, "file": "createBrokerage-C9mgeCKI.js", "sources": ["../../src/views/sell/channels/strategy/components/DetailForm/createBrokerage.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"strategyInformation\": \"Strategy Info\",\r\n    \"companyType\": \"Company Type\",\r\n    \"agreementUnit\": \"Protocol Unit\",\r\n    \"intermediary\": \"Agent\",\r\n    \"strategyName\": \"Strategy Name\",\r\n    \"enterStrategyName\": \"Please enter the strategy name\",\r\n    \"brokerageLevel\": \"Commission Level\",\r\n    \"selectLevel\": \"Please select a level\",\r\n    \"strategyRules\": \"Strategy Rules\",\r\n    \"commissionType\": \"Commission Type\",\r\n    \"byNight\": \"By Night\",\r\n    \"commissionMethod\": \"Commission Method\",\r\n    \"fixedNightCommission\": \"Fixed Night Commission\",\r\n    \"percentageCommission\": \"Percentage Commission\",\r\n    \"commissionAmount\": \"Commission Amount\",\r\n    \"fixedNightCommissionUnit\": \"Yuan\",\r\n    \"percentageCommissionUnit\": \"%\",\r\n    \"strategyApplicationScope\": \"Strategy Scope\",\r\n    \"appliedChannels\": \"Channels\",\r\n    \"selectChannels\": \"Please select channels\",\r\n    \"appliedRoomTypes\": \"Room Types\",\r\n    \"hotelRoomTypes\": \"Hotel Room Types\",\r\n    \"groupRoomTypes\": \"Group Room Types\",\r\n    \"selectRoomTypes\": \"Please select room types\",\r\n    \"appliedHotel\": \"Hotel\",\r\n    \"status\": \"Status\",\r\n    \"valid\": \"Valid\",\r\n    \"invalid\": \"Invalid\",\r\n    \"remark\": \"Remark\",\r\n    \"enterRemark\": \"Please enter remarks\",\r\n    \"submitSuccess\": \"Submission successful\",\r\n    \"editSuccess\": \"Edit successful\",\r\n    \"enableSuccess\": \"Enable successful\",\r\n    \"disableSuccess\": \"Disable successful\",\r\n    \"unknownError\": \"Unknown error\",\r\n    \"networkError\": \"Network error\",\r\n    \"noData\": \"No data available\",\r\n    \"numberOfRoomTypes\": \"{count} Room Types\",\r\n    \"save\": \"Save\",\r\n    \"cancel\": \"Cancel\",\r\n    \"addSuccess\": \"Added successfully\",\r\n    \"commissionTypePlaceholder\": \"Please select commission type\",\r\n    \"brokerageLevelPlaceholder\": \"Please select brokerage level\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"strategyInformation\": \"策略信息\",\r\n    \"companyType\": \"公司类型\",\r\n    \"agreementUnit\": \"协议单位\",\r\n    \"intermediary\": \"中介\",\r\n    \"strategyName\": \"策略名称\",\r\n    \"enterStrategyName\": \"请输入策略名称\",\r\n    \"brokerageLevel\": \"佣金级别\",\r\n    \"selectLevel\": \"请选择级别\",\r\n    \"strategyRules\": \"策略规则\",\r\n    \"commissionType\": \"返佣类型\",\r\n    \"byNight\": \"按间夜\",\r\n    \"commissionMethod\": \"返佣方式\",\r\n    \"fixedNightCommission\": \"间夜定额返佣\",\r\n    \"percentageCommission\": \"百分比返佣\",\r\n    \"commissionAmount\": \"返佣金额\",\r\n    \"fixedNightCommissionUnit\": \"元\",\r\n    \"percentageCommissionUnit\": \"%\",\r\n    \"strategyApplicationScope\": \"策略应用范围\",\r\n    \"appliedChannels\": \"应用渠道\",\r\n    \"selectChannels\": \"请选择渠道\",\r\n    \"appliedRoomTypes\": \"应用房型\",\r\n    \"hotelRoomTypes\": \"酒店房型\",\r\n    \"groupRoomTypes\": \"集团房型\",\r\n    \"selectRoomTypes\": \"请选择房型\",\r\n    \"appliedHotel\": \"应用酒店\",\r\n    \"status\": \"状态\",\r\n    \"valid\": \"有效\",\r\n    \"invalid\": \"无效\",\r\n    \"remark\": \"备注\",\r\n    \"enterRemark\": \"请输入备注\",\r\n    \"submitSuccess\": \"提交成功\",\r\n    \"editSuccess\": \"编辑成功\",\r\n    \"enableSuccess\": \"启用成功\",\r\n    \"disableSuccess\": \"停用成功\",\r\n    \"unknownError\": \"未知错误\",\r\n    \"networkError\": \"网络错误\",\r\n    \"noData\": \"暂无数据\",\r\n    \"numberOfRoomTypes\": \"{count}个房型\",\r\n    \"save\": \"保存\",\r\n    \"cancel\": \"取消\",\r\n    \"addSuccess\": \"新增成功\",\r\n    \"commissionTypePlaceholder\": \"请选择返佣类型\",\r\n    \"brokerageLevelPlaceholder\": \"请选择佣金级别\"\r\n  },\r\n  \"km\": {\r\n    \"strategyInformation\": \"ព័ត៌មានយុទ្ធសាស្ត្រ\",\r\n    \"companyType\": \"ប្រភេទក្រុមហ៊ុន\",\r\n    \"agreementUnit\": \"អង្គភាពកិច្ចព្រមព្រៀង\",\r\n    \"intermediary\": \"អ្នកកាត់ដណ្ដឹង\",\r\n    \"strategyName\": \"ឈ្មោះយុទ្ធសាស្ត្រ\",\r\n    \"enterStrategyName\": \"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ\",\r\n    \"brokerageLevel\": \"កម្រិតគណនេយ្យ\",\r\n    \"selectLevel\": \"សូមជ្រើសរើសកម្រិត\",\r\n    \"strategyRules\": \"ច្បាប់យុទ្ធសាស្ត្រ\",\r\n    \"commissionType\": \"ប្រភេទគណនេយ្យ\",\r\n    \"byNight\": \"តាមយប់\",\r\n    \"commissionMethod\": \"វិធីសាស្ត្រគណនេយ្យ\",\r\n    \"fixedNightCommission\": \"គណនេយ្យថេរក្នុងមួយយប់\",\r\n    \"percentageCommission\": \"គណនេយ្យជាភាគរយ\",\r\n    \"commissionAmount\": \"ចំនួនគណនេយ្យ\",\r\n    \"fixedNightCommissionUnit\": \"ដុល្លារ\",\r\n    \"percentageCommissionUnit\": \"%\",\r\n    \"strategyApplicationScope\": \"វិសាលភាពយុទ្ធសាស្ត្រ\",\r\n    \"appliedChannels\": \"ឆានែល\",\r\n    \"selectChannels\": \"សូមជ្រើសរើសឆានែល\",\r\n    \"appliedRoomTypes\": \"ប្រភេទបន្ទប់\",\r\n    \"hotelRoomTypes\": \"ប្រភេទបន្ទប់សណ្ឋាគារ\",\r\n    \"groupRoomTypes\": \"ប្រភេទបន្ទប់ក្រុម\",\r\n    \"selectRoomTypes\": \"សូមជ្រើសរើសប្រភេទបន្ទប់\",\r\n    \"appliedHotel\": \"សណ្ឋាគារ\",\r\n    \"status\": \"ស្ថានភាព\",\r\n    \"valid\": \"មានសុពលភាព\",\r\n    \"invalid\": \"គ្មានសុពលភាព\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"enterRemark\": \"សូមបញ្ចូលចំណាំ\",\r\n    \"submitSuccess\": \"បានដាក់ស្នើដោយជោគជ័យ\",\r\n    \"editSuccess\": \"កែសម្រួលដោយជោគជ័យ\",\r\n    \"enableSuccess\": \"បានបើកដោយជោគជ័យ\",\r\n    \"disableSuccess\": \"បានបិទដោយជោគជ័យ\",\r\n    \"unknownError\": \"កំហុសមិនស្គាល់\",\r\n    \"networkError\": \"កំហុសបណ្ដាញ\",\r\n    \"noData\": \"គ្មានទិន្នន័យ\",\r\n    \"numberOfRoomTypes\": \"{count}ប្រភេទបន្ទប់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"addSuccess\": \"បានបន្ថែមដោយជោគជ័យ\",\r\n    \"commissionTypePlaceholder\": \"សូមជ្រើសរើសប្រភេទគណនេយ្យ\",\r\n    \"brokerageLevelPlaceholder\": \"សូមជ្រើសរើសកម្រិតគណនេយ្យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { ChannelModel, RoomTypeModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { brokerageStrategyApi, channelApi, dictDataApi, rtApi } from '@/api/modules/index'\r\nimport { BROKERAGE_LEVEL } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {})\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  // 集团代码\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  // 是否集团佣金策略\r\n  isG: '0',\r\n  // 策略应用酒店列表 code:酒店代码 hname:酒店名称\r\n  hotels: [userStore.hcode],\r\n  // 策略代码\r\n  strategyCode: '',\r\n  // 策略名称\r\n  strategyName: '',\r\n  // 公司类型 0:中介 1:协议单位\r\n  companyType: '0',\r\n  // 应用房型 0:酒店房型 1:房型\r\n  isGrt: '0',\r\n  // 佣金等级代码\r\n  brokerageLevelCode: '',\r\n  // 房型列表\r\n  rts: [],\r\n  // 返佣名称的类型 0:间夜定额返佣 1:百分比返佣\r\n  brokerageType: '0',\r\n  // 目标值\r\n  brokerageValue: 0.5,\r\n  // 渠道代码\r\n  channels: [],\r\n  // 是否有效\r\n  isEnable: '1',\r\n  // 备注\r\n  remark: '',\r\n})\r\nconst mission = ref('1')\r\nconst formRules = ref<FormRules>({\r\n  strategyName: [{ required: true, message: t('enterStrategyName'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  getChannels()\r\n  getRts()\r\n  getConstants()\r\n})\r\n\r\nconst dictTypes = [BROKERAGE_LEVEL]\r\nconst commissionLevel = ref<{ code: string; label: string }[]>([])\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    commissionLevel.value = res.data.filter((item: any) => item.dictType === BROKERAGE_LEVEL)\r\n  })\r\n}\r\n\r\nconst channels = ref<ChannelModel[]>([])\r\n/** 获取渠道列表 */\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isEnable: 1,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst rts = ref<RoomTypeModel[]>([])\r\n/** 获取房型 */\r\nfunction getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isVirtual: '0',\r\n    isEnable: '1',\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            brokerageStrategyApi.createBrokerageStrategy(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('addSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg || t('unknownError'),\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n\r\nfunction changeType() {\r\n  form.value.brokerageValue = 0\r\n}\r\n\r\nfunction changeGrt() {\r\n  form.value.rts = []\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    isVirtual: '0',\r\n    isEnable: '1',\r\n  }\r\n  if (form.value.isGrt === '1') {\r\n    rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n      if (res.code === 0) {\r\n        rts.value = res.data\r\n      }\r\n    })\r\n  } else {\r\n    const hParams = {\r\n      ...params,\r\n      hcode: userStore.hcode,\r\n    }\r\n    rtApi.getRoomTypeSimpleList(hParams).then((res: any) => {\r\n      if (res.code === 0) {\r\n        rts.value = res.data\r\n      }\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"180px\" label-suffix=\"：\">\r\n      <el-divider content-position=\"left\">\r\n        {{ t('strategyInformation') }}\r\n      </el-divider>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('companyType')\">\r\n            <el-radio-group v-model=\"form.companyType\">\r\n              <el-radio value=\"0\" size=\"large\">\r\n                {{ t('intermediary') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('strategyName')\" prop=\"strategyName\">\r\n            <el-input v-model=\"form.strategyName\" :placeholder=\"t('strategyName')\" maxlength=\"30\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('brokerageLevel')\">\r\n            <el-select v-model=\"form.brokerageLevelCode\" collapse-tags collapse-tags-tooltip :placeholder=\"t('selectLevel')\" style=\"width: 300px\">\r\n              <el-option v-for=\"item in commissionLevel\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('strategyRules') }}\r\n      </el-divider>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"20\">\r\n          <el-form-item :label=\"t('commissionType')\">\r\n            <el-radio-group v-model=\"mission\">\r\n              <el-radio value=\"1\" size=\"large\">\r\n                {{ t('byNight') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-form-item>\r\n          <div style=\"display: flex\">\r\n            <el-select v-model=\"form.brokerageType\" style=\"width: 230px\" :placeholder=\"t('commissionTypePlaceholder')\" @change=\"changeType\">\r\n              <el-option :label=\"t('fixedNightCommission')\" value=\"0\" />\r\n              <el-option :label=\"t('percentageCommission')\" value=\"1\" />\r\n            </el-select>\r\n            <el-input-number v-if=\"form.brokerageType === '0'\" v-model=\"form.brokerageValue\" :max=\"1000000\" :min=\"0\" controls-position=\"right\" />\r\n            <span v-if=\"form.brokerageType === '0'\" style=\"margin-left: 5px\">{{ t('fixedNightCommissionUnit') }}</span>\r\n            <el-input-number v-if=\"form.brokerageType === '1'\" v-model=\"form.brokerageValue\" :max=\"1\" :min=\"0\" :step=\"0.01\" :precision=\"2\" controls-position=\"right\" />\r\n            <span v-if=\"form.brokerageType === '1'\" style=\"margin-left: 5px\">{{ t('percentageCommissionUnit') }}</span>\r\n          </div>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('strategyApplicationScope') }}\r\n      </el-divider>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('appliedChannels')\">\r\n            <el-select v-model=\"form.channels\" multiple collapse-tags-tooltip clearable :placeholder=\"t('selectChannels')\" style=\"width: 240px\">\r\n              <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('appliedRoomTypes')\">\r\n            <el-radio-group v-model=\"form.isGrt\" @change=\"changeGrt\">\r\n              <el-radio value=\"0\" size=\"large\">\r\n                {{ t('hotelRoomTypes') }}\r\n              </el-radio>\r\n              <el-radio value=\"1\" size=\"large\">\r\n                {{ t('groupRoomTypes') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item>\r\n            <el-select v-model=\"form.rts\" multiple collapse-tags collapse-tags-tooltip clearable :placeholder=\"t('selectRoomTypes')\">\r\n              <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('appliedHotel')\">\r\n            <span>{{ userStore.hname }}</span>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('status')\">\r\n            <el-switch v-model=\"form.isEnable\" inline-prompt :active-text=\"t('valid')\" :inactive-text=\"t('invalid')\" active-value=\"1\" inactive-value=\"0\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('remark')\">\r\n            <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('enterRemark')\" maxlength=\"250\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "formRef", "form", "gcode", "hcode", "isG", "hotels", "strategyCode", "strategyName", "companyType", "isGrt", "brokerageLevelCode", "rts", "brokerageType", "brokerageValue", "channels", "isEnable", "remark", "mission", "formRules", "required", "message", "trigger", "onMounted", "params", "channelApi", "getChannelSimpleList", "then", "res", "code", "value", "data", "getChannels", "isVirtual", "rtApi", "getRoomTypeSimpleList", "getRts", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "commissionLevel", "filter", "item", "dictType", "BROKERAGE_LEVEL", "changeType", "changeGrt", "hParams", "__expose", "submit", "Promise", "resolve", "validate", "valid", "brokerageStrategyApi", "createBrokerageStrategy", "ElMessage", "success", "center", "error", "msg"], "mappings": "svCAsJA,MAAMA,EAAYC,KACZC,EAAEA,GAAMC,IAERC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CAEfG,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,MAEjBC,IAAK,IAELC,OAAQ,CAACX,EAAUS,OAEnBG,aAAc,GAEdC,aAAc,GAEdC,YAAa,IAEbC,MAAO,IAEPC,mBAAoB,GAEpBC,IAAK,GAELC,cAAe,IAEfC,eAAgB,GAEhBC,SAAU,GAEVC,SAAU,IAEVC,OAAQ,KAEJC,EAAUlB,EAAI,KACdmB,EAAYnB,EAAe,CAC/BQ,aAAc,CAAC,CAAEY,UAAU,EAAMC,QAASxB,EAAE,qBAAsByB,QAAS,WAG7EC,GAAU,MAgBV,WACE,MAAMC,EAAS,CACbrB,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,MACjBY,SAAU,GAEZS,EAAWC,qBAAqBF,GAAQG,MAAMC,IAC3B,IAAbA,EAAIC,OACNd,EAASe,MAAQF,EAAIG,KAAA,GAExB,CAzBWC,GA8Bd,WACE,MAAMR,EAAS,CACbrB,MAAOR,EAAUQ,MACjBC,MAAOT,EAAUS,MACjB6B,UAAW,IACXjB,SAAU,KAEZkB,EAAMC,sBAAsBX,GAAQG,MAAMC,IACvB,IAAbA,EAAIC,OACNjB,EAAIkB,MAAQF,EAAIG,KAAA,GAEnB,CAxCMK,GAOPC,EAAYC,iBAAiBC,GAAWZ,MAAMC,IAC5BY,EAAAV,MAAQF,EAAIG,KAAKU,QAAQC,GAAcA,EAAKC,WAAaC,GAAe,GAP7E,IAGT,MAAAL,EAAY,CAACK,GACbJ,EAAkBxC,EAAuC,IAOzD,MAAAe,EAAWf,EAAoB,IAe/B,MAAAY,EAAMZ,EAAqB,IA0CjC,SAAS6C,IACP3C,EAAK4B,MAAMhB,eAAiB,CAAA,CAG9B,SAASgC,KACF5C,EAAA4B,MAAMlB,IAAM,GACjB,MAAMY,EAAS,CACbrB,MAAOR,EAAUQ,MACjB8B,UAAW,IACXjB,SAAU,KAER,GAAqB,MAArBd,EAAK4B,MAAMpB,MACbwB,EAAMC,sBAAsBX,GAAQG,MAAMC,IACvB,IAAbA,EAAIC,OACNjB,EAAIkB,MAAQF,EAAIG,KAAA,QAGf,CACL,MAAMgB,EAAU,IACXvB,EACHpB,MAAOT,EAAUS,OAEnB8B,EAAMC,sBAAsBY,GAASpB,MAAMC,IACxB,IAAbA,EAAIC,OACNjB,EAAIkB,MAAQF,EAAIG,KAAA,GAEnB,CACH,QArDWiB,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBlD,EAAQ6B,OACN7B,EAAQ6B,MAAMsB,UAAUC,IAClBA,GACFC,EAAqBC,wBAAwBrD,EAAK4B,OAAOH,MAAMC,IAC5C,IAAbA,EAAIC,MACN2B,EAAUC,QAAQ,CAChBpC,QAASxB,EAAE,cACX6D,QAAQ,IAEFP,KAERK,EAAUG,MAAM,CACdtC,QAASO,EAAIgC,KAAO/D,EAAE,gBACtB6D,QAAQ,GACT,GAEJ,GAEJ"}