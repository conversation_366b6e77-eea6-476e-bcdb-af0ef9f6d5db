<i18n>
{
	"en": {
		"selectAccount": "Choose Account for Checkout",
		"confirmTotalAmount": "Confirm Total Amount",
		"completePreAuthorization": "Complete Pre-auth",
		"completeCheckout": "Complete Check-out",
		"settleCheckOut": "Check-out with <PERSON>",
		"roomNumber": "Room No",
		"name": "Name",
		"guestStatus": "Guest Status",
		"checkedIn": "Checked In",
    "over": "Over",
		"credit": "Credit",
		"checkedOut": "Checked Out",
		"cancel": "Cancel",
		"nextStep": "Next",
		"confirmationMethod": "Confirmation Method",
		"systemAutoBilling": "System Auto Billing",
		"manualRoomFee": "Manual Room Fee",
		"noAdditionalFee": "No Additional Fee",
		"addHalfDay": "Add Half Day",
		"addFullDay": "Add Full Day",
		"checkinType": "Check-in Type",
		"hourRoom": "Hourly Room",
		"allDayRoom": "Full Day Room",
		"longStay": "Long Stay",
		"selfUse": "Self Use",
		"free": "Free",
		"travelGroup": "Travel Group",
		"meetingGroup": "Meeting Group",
		"checkinCheckoutTime": "Check-in / Check-out Time",
		"amount": "Amount",
		"noGenerateRoomFee": "Confirm Not to Generate Room Fee",
		"generateRoomFee": "Confirm to Generate Room Fee",
		"preAuthType": "Pre-authorization Type",
		"preAuthAmount": "Pre-authorization Amount",
		"actualAmount": "Actual Amount",
		"status": "Status",
		"operation": "Operation",
		"complete": "Complete",
		"confirmCancelAuthorization": "Are you sure you want to cancel this authorization?",
		"confirmFinishAuthorization": "Are you sure you want to complete this authorization?",
		"authorizationOperationSuccessful": "Authorization operation successful",
		"preAuthPending": "Rooms have pending pre-authorizations. Please handle them before checkout.",
		"cash": "Cash",
		"storeCard": "StoreCard",
		"bankCard": "BankCard",
		"scanGun": "ScanGun",
		"arAccount": "ArAccount",
		"groupCard": "Group Card",
		"balance": "Balance",
		"phone": "Phone",
		"mtName": "Name",
		"checkoutAccount": "Checkout Account",
		"paymentMethod": "Payment Method",
		"paymentAmount": "Payment Amount",
		"bankCardNumber": "Bank Card Number",
		"bankCardType": "Bank Card Type",
		"account": "Account",
		"remark": "Remark",
		"scanBarcode": "Scan Barcode",
		"barcode": "Barcode",
		"useCoupon": "Use Coupon",
		"availableAmount": "Available Amount",
		"accountType": "Account Type",
		"settlementPeriod": "Settlement Period",
		"creditAccount": "Credit Account",
		"prepaidAccount": "Prepaid Account",
		"permanentPeriod": "Permanent Period",
		"preAuth": "Pre-authorization",
		"select": "Please select",
		"smsReminder": "Sms Send",
		"enterBarcode": "Please enter barcode",
		"chooseBankCardType": "Please select bank card type",
		"enterBankCardNumber": "Please enter bank card number",
		"enterRemark": "Please enter remark",
		"enterPhoneNumber": "Please enter phone number",
		"enterPassword": "Please enter password",
		"pleaseSelectCheckoutAccount": "Please select a checkout account",
		"pleaseEnterPhoneNumber": "Please enter phone number",
		"pleaseEnterPassword": "Please enter password",
		"accountCannotBeEmpty": "Account cannot be empty",
		"operationSuccessful": "Operation successful",
		"operationFailed": "Operation failed",
		"selectDepartment": "Please select a department",
		"changeDepartment": "Change Department",
		"changeDepartmentSuccess": "Department changed successfully",
		"sendCode": "Send Code",
		"smsSendSuccess": "Send Success",
		"enterSmsCode": "Enter Send Code",
		"password": "Password",
    "roomFeeMustGenerateMode": "Mandatory room fee generation mode is enabled",
    "memberBenefitTooltip": "Members enjoy more benefits",
    "selectedCoupons": "Selected Coupons",
    "originalRefundAmount": "Original Refund Amount",
    "originalPaymentAmount": "Original Payment Amount",
    "discountAmount": "Discount Amount",
    "refundAmount": "Refund Amount",
    "paymentAmount2": "Payment Amount",
    "multiDayDiscount": "Multi-day Stay Discount",
    "removeAll": "Remove All",
    "discountUnit": "% off",
    "freeRoom": "Free Room",
    "assignCleaningTask": "Assign room cleaning task",
    "printOrderBill": "Print Order Bill",
    "generatedRoomFee": "Generated Room Fee",
    "expectedAdditionalFee": "Expected Additional Fee"
	},
	"zh-cn": {
		"selectAccount": "请选择结账账号",
		"confirmTotalAmount": "确认总房费",
		"completePreAuthorization": "完成预授权",
		"completeCheckout": "完成结账",
		"settleCheckOut": "结账退房",
		"roomNumber": "房号",
		"name": "姓名",
		"guestStatus": "客人状态",
		"checkedIn": "在住",
    "over": "完成",
		"credit": "挂账",
		"checkedOut": "离店",
		"cancel": "取消",
		"nextStep": "下一步",
		"confirmationMethod": "确认方式",
		"systemAutoBilling": "系统自动计费",
		"manualRoomFee": "手工加收房费",
		"noAdditionalFee": "不加收房费",
		"addHalfDay": "加收半天",
		"addFullDay": "加收全天",
		"checkinType": "入住类型",
		"hourRoom": "钟点房",
		"allDayRoom": "全天房",
		"longStay": "长包",
		"selfUse": "自用",
		"free": "免费",
		"travelGroup": "旅行团",
		"meetingGroup": "会议团",
		"checkinCheckoutTime": "抵离时间",
		"amount": "金额",
		"noGenerateRoomFee": "确定不生成房费",
		"generateRoomFee": "确定生成房费",
		"preAuthType": "预授权类型",
		"preAuthAmount": "预授权金额",
		"actualAmount": "实收金额",
		"status": "状态",
		"smsReminder": "是否发送短信",
		"operation": "操作",
		"complete": "完成",
		"confirmCancelAuthorization": "确认取消该授权吗？",
		"confirmFinishAuthorization": "确认完成该授权吗？",
		"authorizationOperationSuccessful": "授权操作成功",
		"preAuthPending": "房间存在末处理的预授权，请处理后再结账。",
		"cash": "现金",
		"storeCard": "储值卡",
		"bankCard": "银行卡",
		"scanGun": "扫码枪",
		"arAccount": "AR账",
		"groupCard": "集团卡",
		"balance": "余额",
		"phone": "电话",
		"mtName": "姓名",
		"checkoutAccount": "入账账号",
		"paymentMethod": "付款方式",
		"paymentAmount": "付款金额",
		"bankCardNumber": "卡号",
		"bankCardType": "银行卡类型",
		"account": "账户",
		"remark": "备注",
		"scanBarcode": "扫条形码",
		"barcode": "条形码",
		"useCoupon": "使用优惠卷",
		"availableAmount": "可用额度",
		"accountType": "账户类型",
		"settlementPeriod": "结算帐期",
		"creditAccount": "信用账户",
		"prepaidAccount": "预付账户",
		"permanentPeriod": "永久账期",
		"preAuth": "预授",
		"select": "请选择",
		"enterBarcode": "请输入条形码",
		"chooseBankCardType": "请选择银行卡类型",
		"enterBankCardNumber": "请输入银行卡号",
		"enterRemark": "请输入备注",
		"enterPhoneNumber": "请输入手机号码",
		"enterPassword": "请输入密码",
		"pleaseSelectCheckoutAccount": "请选择结账账号",
		"pleaseEnterPhoneNumber": "请输入手机号码",
		"pleaseEnterPassword": "请输入密码",
		"accountCannotBeEmpty": "入账账号不能为空",
		"operationSuccessful": "操作成功",
		"operationFailed": "操作失败",
		"selectDepartment": "请选择部门",
		"changeDepartment": "变更部门",
		"changeDepartmentSuccess": "变更部门成功",
		"sendCode": "验证码",
		"smsSendSuccess": "发送成功",
		"enterSmsCode": "输入验证码",
		"password": "密码",
    "roomFeeMustGenerateMode": "开启了必生成房费模式",
    "memberBenefitTooltip": "会员入住享受更多权益",
    "selectedCoupons": "已选优惠券",
    "originalRefundAmount": "原退款金额",
    "originalPaymentAmount": "原付款金额",
    "discountAmount": "优惠金额",
    "refundAmount": "应退金额",
    "paymentAmount2": "应付金额",
    "multiDayDiscount": "连住多天优惠",
    "removeAll": "移除全部",
    "discountUnit": "折",
    "freeRoom": "免房",
    "assignCleaningTask": "下发客房清扫任务",
    "printOrderBill": "打印结账单",
    "generatedRoomFee": "已生成房费",
    "expectedAdditionalFee": "预计加收房费"
	},
  "km": {
    "selectAccount": "សូមជ្រើសរើសគណនីដើម្បីគិតប្រាក់",
    "confirmTotalAmount": "បញ្ជាក់ថ្លៃបន្ទប់សរុប",
    "completePreAuthorization": "បញ្ចប់ការអនុញ្ញាតជាមុន",
    "completeCheckout": "បញ្ចប់ការគិតប្រាក់",
    "settleCheckOut": "គិតប្រាក់និងចាកចេញ",
    "roomNumber": "លេខបន្ទប់",
    "name": "ឈ្មោះ",
    "guestStatus": "ស្ថានភាពភ្ញៀវ",
    "checkedIn": "កំពុងស្នាក់នៅ",
    "over": "បានបញ្ចប់",
    "credit": "គិតប្រាក់ជាបន្ត",
    "checkedOut": "បានចាកចេញ",
    "cancel": "បោះបង់",
    "nextStep": "ជំហានបន្ទាប់",
    "confirmationMethod": "វិធីសាកសម",
    "systemAutoBilling": "ប្រព័ន្ធគិតប្រាក់ស្វ័យប្រវត្តិ",
    "manualRoomFee": "បន្ថែមថ្លៃបន្ទប់ដោយដៃ",
    "noAdditionalFee": "មិនបន្ថែមថ្លៃបន្ទប់",
    "addHalfDay": "បន្ថែមកន្លះថ្ងៃ",
    "addFullDay": "បន្ថែមមួយថ្ងៃ",
    "checkinType": "ប្រភេទការស្នាក់នៅ",
    "hourRoom": "បន្ទប់ម៉ោង",
    "allDayRoom": "បន្ទប់មួយថ្ងៃ",
    "longStay": "ស្នាក់នៅយូរ",
    "selfUse": "ប្រើប្រាស់ផ្ទាល់ខ្លួន",
    "free": "ឥតគិតថ្លៃ",
    "travelGroup": "ក្រុមអ្នកធ្វើដំណើរ",
    "meetingGroup": "ក្រុមប្រជុំ",
    "checkinCheckoutTime": "ពេលវេលាមកដល់/ចាកចេញ",
    "amount": "ចំនួនទឹកប្រាក់",
    "noGenerateRoomFee": "បញ្ជាក់មិនបង្កើតថ្លៃបន្ទប់",
    "generateRoomFee": "បញ្ជាក់បង្កើតថ្លៃបន្ទប់",
    "preAuthType": "ប្រភេទការអនុញ្ញាតជាមុន",
    "preAuthAmount": "ចំនួនទឹកប្រាក់អនុញ្ញាតជាមុន",
    "actualAmount": "ចំនួនទឹកប្រាក់ដែលបានទទួល",
    "status": "ស្ថានភាព",
    "operation": "ប្រតិបត្តិការ",
    "complete": "បញ្ចប់",
    "confirmCancelAuthorization": "តើអ្នកប្រាកដថាចង់បោះបង់ការអនុញ្ញាតនេះទេ?",
    "confirmFinishAuthorization": "តើអ្នកប្រាកដថាចង់បញ្ចប់ការអនុញ្ញាតនេះទេ?",
    "authorizationOperationSuccessful": "ប្រតិបត្តិការអនុញ្ញាតជោគជ័យ",
    "preAuthPending": "មានការអនុញ្ញាតជាមុនដែលមិនទាន់បានដោះស្រាយនៅក្នុងបន្ទប់។ សូមដោះស្រាយវាមុនពេលគិតប្រាក់។",
    "cash": "សាច់ប្រាក់",
    "storeCard": "កាតស្តុក",
    "bankCard": "កាតធនាគារ",
    "scanGun": "ម៉ាស៊ីនស្កេន",
    "arAccount": "គណនី AR",
    "groupCard": "កាតក្រុម",
    "balance": "សមតុល្យ",
    "phone": "ទូរស័ព្ទ",
    "mtName": "ឈ្មោះ",
    "checkoutAccount": "គណនីគិតប្រាក់",
    "paymentMethod": "វិធីបង់ប្រាក់",
    "paymentAmount": "ចំនួនទឹកប្រាក់បង់",
    "bankCardNumber": "លេខកាតធនាគារ",
    "bankCardType": "ប្រភេទកាតធនាគារ",
    "account": "គណនី",
    "remark": "កំណត់សម្គាល់",
    "scanBarcode": "ស្កេនបាកូដ",
    "barcode": "បាកូដ",
    "useCoupon": "ប្រើប្រាស់គូប៉ុង",
    "availableAmount": "ចំនួនទឹកប្រាក់ដែលអាចប្រើបាន",
    "accountType": "ប្រភេទគណនី",
    "settlementPeriod": "រយៈពេលទូទាត់",
    "creditAccount": "គណនីឥណទាន",
    "prepaidAccount": "គណនីបង់ប្រាក់ជាមុន",
    "permanentPeriod": "រយៈពេលទូទាត់អចិន្ត្រៃយ៍",
    "preAuth": "ការអនុញ្ញាតជាមុន",
    "select": "សូមជ្រើសរើស",
    "smsReminder": "ផ្ញើសារ SMS",
    "enterBarcode": "សូមបញ្ចូលបាកូដ",
    "chooseBankCardType": "សូមជ្រើសរើសប្រភេទកាតធនាគារ",
    "enterBankCardNumber": "សូមបញ្ចូលលេខកាតធនាគារ",
    "enterRemark": "សូមបញ្ចូលកំណត់សម្គាល់",
    "enterPhoneNumber": "សូមបញ្ចូលលេខទូរស័ព្ទ",
    "enterPassword": "សូមបញ្ចូលពាក្យសម្ងាត់",
    "pleaseSelectCheckoutAccount": "សូមជ្រើសរើសគណនីគិតប្រាក់",
    "pleaseEnterPhoneNumber": "សូមបញ្ចូលលេខទូរស័ព្ទ",
    "pleaseEnterPassword": "សូមបញ្ចូលពាក្យសម្ងាត់",
    "accountCannotBeEmpty": "គណនីគិតប្រាក់មិនអាចទទេបានទេ",
    "operationSuccessful": "ប្រតិបត្តិការជោគជ័យ",
    "operationFailed": "ប្រតិបត្តិការបរាជ័យ",
    "selectDepartment": "សូមជ្រើសរើសផ្នែក",
    "changeDepartment": "ផ្លាស់ប្តូរផ្នែក",
    "changeDepartmentSuccess": "ផ្លាស់ប្តូរផ្នែកជោគជ័យ",
    "sendCode": "លេខកូដ",
    "smsSendSuccess": "ផ្ញើជោគជ័យ",
    "enterSmsCode": "បញ្ចូលលេខកូដ",
    "password": "ពាក្យសម្ងាត់",
    "roomFeeMustGenerateMode": "បានបើករបៀបបង្កើតថ្លៃបន្ទប់ដោយចាំបាច់",
    "memberBenefitTooltip": "សមាជិកភាពអាចទទួលបានអត្ថប្រយោជន៍បន្ថែម",
    "selectedCoupons": "គូប៉ុងដែលបានជ្រើសរើស",
    "originalRefundAmount": "ចំនួនទឹកប្រាក់សងដើម",
    "originalPaymentAmount": "ចំនួនទឹកប្រាក់បង់ដើម",
    "discountAmount": "ចំនួនទឹកប្រាក់បញ្ចុះតម្លៃ",
    "refundAmount": "ចំនួនទឹកប្រាក់ដែលត្រូវសង",
    "paymentAmount2": "ចំនួនទឹកប្រាក់ដែលត្រូវបង់",
    "multiDayDiscount": "ការបញ្ចុះតម្លៃសម្រាប់ស្នាក់នៅច្រើនថ្ងៃ",
    "removeAll": "យកចេញទាំងអស់",
    "discountUnit": "បញ្ចុះតម្លៃ",
    "freeRoom": "បន្ទប់ឥតគិតថ្លៃ",
    "assignCleaningTask": "ចាត់ចែងការងារសំអាតបន្ទប់",
    "printOrderBill": "បោះពុម្ពវិក័យប័ត្រគិតប្រាក់",
    "generatedRoomFee": "ថ្លៃបន្ទប់ដែលបានបង្កើត",
    "expectedAdditionalFee": "ថ្លៃបន្ទប់បន្ថែមដែលរំពឹងទុក"
  }
}
</i18n>

<script lang="ts" setup>
import { accountApi, arSetApi, couponConfigApi, dictDataApi, generalConfigApi, hotelParamConfigApi, memberApi } from '@/api/modules'

import authApi from '@/api/modules/system/auth/auth.api.ts'
import { ConsumeAccountEnum, DICT_TYPE_ROOM_CLEAN_TASK } from '@/models/dict/constants'
import { AccountState, BooleanEnum, CheckinType, DICT_TYPE_BANK_TYPE, NoType, OrderState, PayModelCode } from '@/models/index'
import useUserStore from '@/store/modules/user'
import { deduplicateByProperty } from '@/utils'
import PrintOrderBill from '@/views/print/orderBill.vue'
import { Close, Search } from '@element-plus/icons-vue'
import CouponDialog from './components/CouponDialog.vue'

const props = withDefaults(
  defineProps<{
    modelValue: boolean
    tabId?: string
    noType: string
    /** 订单号 */
    orderNo?: string
    orderTogetherCode: string | number
  }>(),
  {
    modelValue: false,
    tabId: '0',
    noType: '',
    orderNo: '',
    orderTogetherCode: '',
  }
)
const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()
const smsButtonDisabled = ref(false)
let smsTimer: number | null = null
const smsCount = ref(60)
// 验证类型
const verifyMode = ref('0')
/* 短信按钮倒计时 */
const smsButtonText = computed(() => (smsButtonDisabled.value ? `${smsCount.value}s` : t('sendCode')))
const { t } = useI18n()
const memberCardDetail = ref<any>({
  mtName: '',
  phone: '',
  storeCardBalance: 0,
})
interface cardType {
  mtName?: string
  storeCardNo?: string
  isG?: string
  phone?: string
  balance?: number
}
const cardState = ref<boolean>(false)
const cardList = ref<cardType[]>([])
function onClick(v: cardType) {
  memberCardDetail.value = v
  cardState.value = false
}

const multipleTableRef = ref()
const userStore = useUserStore()
const step = ref<number>(0)
const subCode = ref() // AR账code

// 优惠券相关状态
const couponDialogVisible = ref(false)
const selectedCoupon = ref<any>(null)
const couponDiscount = ref(0) // 优惠券折扣金额
const dailyCoupons = ref<Record<string, any>>({}) // 每日优惠券 - 日期 -> 优惠券对象
const isWalkinMoreEnabled = ref(false) // 是否允许连住多天优惠
const selectedDailyCouponIds = ref<Record<string, string>>({}) // 存储 accNo -> couponId 的映射，用于传递给 CouponDialog
// 房费列表数据
const roomFeeList = ref<any[]>([])
const originalFee = ref(0) // 添加原始金额变量
const originalPayMode = ref('') // 添加原始付款方式变量
const currentPayMode = ref('') // 添加当前支付模式变量
const judge = ref(false)

function getRowKey(row: any) {
  return row.orderNo
}

const myVisible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  },
})

const loading = ref(false)
/** 是否自动创建任务 */
const isAutoTask = ref()
/** 是否打印结账单 */
const isPrintOrderBill = ref(false)
/** 打印结账单弹窗显示状态 */
const printOrderBillVisible = ref(false)
/** 打印加载状态 */
const printLoading = ref(false)

const selectedList = ref<any>([])
function handleSelectionChange(val: any) {
  selectedList.value = val
}

const signCode = ref(false)
interface payModesType {
  code: string
  name: string
  mode: string
  value: string
}
const controls = ref(false)
// 可选账号列表
const form = ref({
  bindCode: '',
  orderType: '',
  /** 是否发送短信 */
  isSendSms: '0',
  /** 验证码 */
  smsCode: '',
  orderTogethers: [] as any[],
  dataList: [] as any[],
  cashierForm: {
    /** 只用于前端显示，不传后台 */
    isNo: '',
    /** 账号 */
    no: '',
    /** 付款账号（房账） */
    account: '',
    orderTogetherCode: '',
    orderNo: '',
    /** 金额 */
    fee: 0,
    /** 付款方式 */
    paymentMethod: userStore.subCode,
    /** 付款方式状态 */
    paymentType: '',
    /** 银行卡 */
    bankCard: '',
    /** 银行卡卡号 */
    bankCardNo: '',
    /** 条形码 */
    barCode: '',

    /** 查询条件 */
    search: {
      phone: '',
      password: '',
    },
  },
  payModes: [] as payModesType[],
  /** 备注 */
  remark: '',
})
const arSetDetail = ref<any>({
  arSetCode: '',
  arSetName: '',
  creditAccType: '',
  availableAmount: 0,
  creditPayDays: '',
  creditPayFix: 0,
})
// 默认付款方式
const comPayment = ref<{ code: string; name: string; mode: string }[]>([
  { code: 'rmb_receipt', name: t('cash'), mode: 'rmb' },
  { code: 'store_card', name: t('storeCard'), mode: 'card' },
  { code: 'bank_card', name: t('bankCard'), mode: 'bank' },
  { code: 'scan_gun', name: t('scanGun'), mode: 'scan' },
  { code: 'credit_s_account', name: t('arAccount'), mode: 'credit' },
])
/**
 * 切换类型
 * @param item
 */
function handPayClick(item: any) {
  verifyMode.value = ''
  form.value.cashierForm.paymentMethod = item.code
  form.value.cashierForm.paymentType = item.mode
  initArSet()
  initMember()
  form.value.cashierForm.bankCard = ''
  form.value.cashierForm.bankCardNo = ''
  form.value.cashierForm.barCode = ''
  signCode.value = false
  if (item.mode === 'rmb') {
    // 现金
    form.value.cashierForm.paymentMethod = userStore.subCode
  } else if (item.mode === 'card') {
    // 给储值卡赋值手机号
    const phone = selectedList.value.filter((item) => item.isMain == form.value.cashierForm.no)[0].phone
    form.value.cashierForm.search.phone = phone
    // 储值卡
  } else if (item.mode === 'bank') {
    // 银行卡
  } else if (item.mode === 'scan') {
    // 扫码枪
  } else {
    subCode.value = item.code
    // 选择AR账时需重新获取下统计付款金额
    getStatAccountData()
    // AR账
    getArSetList()
    // 判断是否还能挂AR账务
    getArAccount()
  }
}
const arSetDataList = ref<any[]>([])
// 初始化
function initArSet() {
  arSetDataList.value = []
  form.value.cashierForm.account = ''
  form.value.cashierForm.orderTogetherCode = ''
  form.value.cashierForm.orderNo = ''
  arSetDetail.value = {
    arSetCode: '',
    arSetName: '',
    creditAccType: '',
    availableAmount: 0,
    creditPayDays: '',
    creditPayFix: 0,
  }
}
function initMember() {
  form.value.cashierForm.search.phone = ''
  form.value.cashierForm.search.password = ''
  memberCardDetail.value = {
    mtName: '',
    phone: '',
    storeCardBalance: 0,
  }
}

function getPayModes() {
  generalConfigApi.getPayAccountList(userStore.gcode).then((res: any) => {
    if (res.code === 0) {
      const filterPre = res.data.filter((item: any) => item.mode !== 'pre')
      if (filterPre) {
        form.value.payModes = filterPre
      }
    }
  })
}

function getAccountList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    no: props.orderTogetherCode,
    noType: props.noType,
    all: '1',
  }
  accountApi.checkoutOrderList(params).then((res: any) => {
    if (res.code === 0) {
      form.value.bindCode = res.data.bindCode
      form.value.orderType = res.data.orderType
      form.value.orderTogethers = res.data.orderTogethers
      nextTick(() => {
        // 如果是接待，接待下所有的客单
        if (props.noType === NoType.ORDERLIST || props.noType === NoType.TEAMRECEPTION) {
          // 全选
          const arrList = []
          selectedList.value = []
          form.value.orderTogethers.forEach((item) => {
            multipleTableRef.value && multipleTableRef.value!.toggleRowSelection(item, true)
            arrList.push(item)
          })
          selectedList.value = deduplicateByProperty(arrList, 'name')
        } else {
          const arrList = []
          selectedList.value = []
          form.value.orderTogethers.forEach((item) => {
            if (item.no === props.orderNo) {
              multipleTableRef.value!.toggleRowSelection(item, true)
              arrList.push(item)
            }
          })
          selectedList.value = arrList
        }
      })
    }
  })
}
/** 加收房费列表 */
const data = ref({
  search: {
    state: 'room_fee',
  },
  dataList: [] as any[],
  remark: '',
})

const isManual = ref(false)
function onSumbit(value: string) {
  isManual.value = value === ConsumeAccountEnum.HAND_INPUT_ROOM_FEE
  if (selectedList.value.length > 0) {
    // 判断是否只选中了团队主单
    if (selectedList.value.length === 1 && selectedList.value[0].isTeam === '1') {
      step.value = 3
      form.value.cashierForm.no = selectedList.value[0].isMain
      getStatAccountData()
      return
    }

    const params = {
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      subCode: value,
      orderType: form.value.orderType,
      orderTogethers: selectedList.value,
      isCreditCheckout: '0',
    }
    accountApi.getConfirmRoomFee(params).then((res: any) => {
      if (res.code === 0) {
        data.value.dataList = res.data
        if (res.data && res.data.length === 0) {
          step.value = 2
          getPreAuthData()
        } else {
          step.value = 1
        }
      }
    })
  } else {
    ElMessage.error(t('pleaseSelectCheckoutAccount'))
  }
}
const feeLoading = ref(false)
/** 确定生成房费 */
function generate() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    orderType: form.value.orderType,
    orderTogethers: data.value.dataList,
    subCode: data.value.search.state,
  }
  feeLoading.value = true
  accountApi.confirmRoomFee(params).then((res: any) => {
    feeLoading.value = false
    if (res.code === 0) {
      step.value = 2
      getPreAuthData()
    }
  })
}

function onNext(val: number) {
  switch (val) {
    case 1:
      step.value = 2
      getPreAuthData()
      break
    case 2:
      if (
        form.value.dataList.some((i) => {
          return i.state === AccountState.UNCLOSED
        })
      ) {
        ElMessage.error(t('preAuthPending'))
      } else {
        step.value = 3
        form.value.cashierForm.no = selectedList.value.filter((item: any) => item.isMain === 1)[0].isMain
        getStatAccountData()
      }
      break
  }
}

/** 预授权列表 */
function getPreAuthData() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    togetherCodeList: selectedList.value.map((_item: { togetherCode: string }) => _item.togetherCode),
  }
  accountApi.getPreauth(params).then((res: any) => {
    if (res.code === 0) {
      form.value.dataList = res.data
      if (
        res.data.length === 0 ||
        !res.data.some((i: { state: string }) => {
          return i.state === AccountState.UNCLOSED
        })
      ) {
        step.value = 3
        // 判断 selectedList.value 中是否有 isMain 为 1 的项
        const mainItem = selectedList.value.find((item: any) => item.isMain === 1)
        if (mainItem) {
          // 如果有 isMain 为 1 的项，则使用该项
          form.value.cashierForm.no = mainItem.isMain
        } else {
          // 如果 isMain 都为 0，则默认选择第一个项
          form.value.cashierForm.no = selectedList.value[0].isMain
        }
        getStatAccountData()
      }
    }
  })
}
/** 预授权取消与确认 */
function cancel(val: any) {
  ElMessageBox.confirm(t('confirmCancelAuthorization'), t('confirmationMethod'))
    .then(() => {
      const params = {
        gcode: userStore.gcode,
        hcode: userStore.hcode,
        accNo: val.accNo,
      }
      accountApi.cancelPreauth(params).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success(t('authorizationOperationSuccessful'))
          val.state = 'cancel'

          // 重新获取预授权列表
          const preAuthParams = {
            gcode: userStore.gcode,
            hcode: userStore.hcode,
            togetherCodeList: selectedList.value.map((_item: { togetherCode: string }) => _item.togetherCode),
          }
          accountApi.getPreauth(preAuthParams).then((preAuthRes: any) => {
            if (preAuthRes.code === 0) {
              form.value.dataList = preAuthRes.data

              // 检查是否还有未处理的预授权
              const hasUnprocessedPreAuth = form.value.dataList.some((i: { state: string }) => {
                return i.state === AccountState.UNCLOSED
              })

              if (!hasUnprocessedPreAuth) {
                // 如果没有未处理的预授权，则自动进入下一步
                step.value = 3
                // 设置结账账号
                const mainItem = selectedList.value.find((item: any) => item.isMain === 1)
                if (mainItem) {
                  form.value.cashierForm.no = mainItem.isMain
                } else {
                  form.value.cashierForm.no = selectedList.value[0].isMain
                }
                // 获取统计付款金额
                getStatAccountData()
              }
            }
          })
        }
      })
    })
    .catch(() => {})
}
function finish(val: any) {
  ElMessageBox.confirm(t('confirmFinishAuthorization'), t('confirmationMethod'))
    .then(() => {
      const params = {
        gcode: userStore.gcode,
        hcode: userStore.hcode,
        accNo: val.accNo,
        fee: val.fee,
      }
      accountApi.confirmPreauth(params).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success(t('authorizationOperationSuccessful'))
          val.state = 'closed'

          // 重新获取预授权列表
          const preAuthParams = {
            gcode: userStore.gcode,
            hcode: userStore.hcode,
            togetherCodeList: selectedList.value.map((_item: { togetherCode: string }) => _item.togetherCode),
          }
          accountApi.getPreauth(preAuthParams).then((preAuthRes: any) => {
            if (preAuthRes.code === 0) {
              form.value.dataList = preAuthRes.data

              // 检查是否还有未处理的预授权
              const hasUnprocessedPreAuth = form.value.dataList.some((i: { state: string }) => {
                return i.state === AccountState.UNCLOSED
              })

              if (!hasUnprocessedPreAuth) {
                // 如果没有未处理的预授权，则自动进入下一步
                step.value = 3
                // 设置结账账号
                const mainItem = selectedList.value.find((item: any) => item.isMain === 1)
                if (mainItem) {
                  form.value.cashierForm.no = mainItem.isMain
                } else {
                  form.value.cashierForm.no = selectedList.value[0].isMain
                }
                // 获取统计付款金额
                getStatAccountData()
              }
            }
          })
        }
      })
    })
    .catch(() => {})
}
/** 银行卡类型列表 */
const dictTypes = [DICT_TYPE_BANK_TYPE]
const bankCardList = ref<{ code: string; label: string }[]>([])
function getConstants() {
  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {
    bankCardList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_BANK_TYPE)
  })
}
/** 条形码 */
const signCodeRef = ref()
function scanCode() {
  signCode.value = true
  nextTick(() => {
    signCodeRef.value.focus() // 条形码获取聚焦
  })
}
/** 储值卡搜索会员 */
function getMemberList() {
  if (form.value.cashierForm.search.phone) {
    const params = {
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      phone: form.value.cashierForm.search.phone,
    }
    memberApi.detail(params).then((res: any) => {
      verifyMode.value = res.data.verifyMode
      cardList.value = res.data.storeCards.map((_item: { storeCardNo: any; balance: any }) => {
        return {
          ..._item,
          mtName: res.data.mtName,
          phone: res.data.phone,
          mcode: res.data.mcode,
        }
      })
      cardState.value = true
    })
  } else {
    ElMessage.error(t('pleaseEnterPhoneNumber'))
  }
}
/** 获取账套列表 */
function getArSetList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  arSetApi.getArSetList(params).then((res: any) => {
    if (res.code === 0 && res.data) {
      arSetDataList.value = res.data.filter((item: any) => item.isEnable === BooleanEnum.YES)
    }
  })
}
/** 判断是否还有AR账务可挂 */
function getArAccount() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    orderType: form.value.orderType,
    orderTogethers: selectedList.value,
    confirmRoomFeeList: form.value.dataList,
    subCode: subCode.value,
  }
  accountApi.judgeARAccount(params).then((res: any) => {
    if (res.code === 0) {
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function handAccountChange(value: any) {
  const foundAccount = arSetDataList.value.find((item: any) => item.arSetCode === value)
  if (foundAccount) {
    arSetDetail.value = foundAccount
  }
}
/** 完成结账 */
async function save() {
  const payMode = form.value.payModes.find((v) => {
    return v.code === form.value.cashierForm.paymentMethod
  })?.value
  const paymentMethod = form.value.cashierForm.paymentMethod || form.value.cashierForm.paymentType
  const orderTogethers = selectedList.value.map((_item: any) => {
    return {
      ..._item,
      isRecord: _item.isMain === form.value.cashierForm.no ? '1' : '0',
    }
  })

  // 构建请求参数
  const params = {
    isAutoTask: isAutoTask.value,
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    orderType: form.value.orderType,
    remark: form.value.remark,
    isSendSms: form.value.isSendSms,
    verifyMode: verifyMode.value,
    smsCode: form.value.smsCode,
    pay: {
      subCode: paymentMethod,
      payMode,
      fee: form.value.cashierForm.fee,
      payCode: '',
      bankType: '',
      bankCardNo: '',
      phone: '',
      pwd: '',
      mcode: '',
      storeCardNo: '',
    },
    orderTogethers,
    payInfoList: [], // 添加优惠券付款信息列表
  }

  // 处理优惠券信息
  if ((selectedCoupon.value || Object.keys(dailyCoupons.value).length > 0) && couponDiscount.value > 0) {
    try {
      // 从选中的订单中找到主单信息
      const mainOrder = selectedList.value.find((item) => item.isMain === form.value.cashierForm.no)
      if (!mainOrder) {
        ElMessage.error('未找到对应的订单信息')
        return
      }

      // 如果需要，获取房费数据
      let roomFees = []
      if (Object.keys(dailyCoupons.value).length > 0 || selectedCoupon.value) {
        const roomFeeParams = {
          gcode: userStore.gcode,
          hcode: userStore.hcode,
          togetherCodes: mainOrder.togetherCode,
        }

        const roomFeeRes = await accountApi.accountRoomFee(roomFeeParams)
        if (roomFeeRes.code !== 0) {
          ElMessage.error('获取房费数据失败')
          return
        }

        roomFees = roomFeeRes.data
      }

      if (selectedCoupon.value) {
        // 首日优惠模式
        // 找到首日房费
        const sortedFees = [...roomFees].sort((a, b) => new Date(a.bizDate).getTime() - new Date(b.bizDate).getTime())

        const firstDayFee = sortedFees[0]
        if (!firstDayFee) {
          ElMessage.error('未找到首日房费数据')
          return
        }

        // 构建优惠券详情文本
        let couponTypeText = ''
        let discountValueText = ''

        if (selectedCoupon.value.couponType === 'discount') {
          couponTypeText = '折扣券'
          discountValueText = `折扣值:${(selectedCoupon.value.rebate * 100).toFixed(0)}`
        } else if (selectedCoupon.value.couponType === 'free') {
          couponTypeText = '免房券'
          discountValueText = `金额:${firstDayFee.fee.toFixed(2)}`
        } else {
          couponTypeText = '代金券'
          discountValueText = `金额:${selectedCoupon.value.discount.toFixed(2)}`
        }

        const accDetailText = `优惠券号:${selectedCoupon.value.templateCode}\n折扣类型:${couponTypeText}，${discountValueText}\n适用日期:${firstDayFee.bizDate}`

        // 添加到付款信息列表
        params.payInfoList.push({
          remark: `使用优惠券：${selectedCoupon.value.name}（${firstDayFee.bizDate}）`,
          fee: Number.parseFloat(couponDiscount.value.toFixed(2)),
          subCode: 'coupon',
          accDetail: accDetailText,
          payValue: selectedCoupon.value.couponType === 'discount' ? selectedCoupon.value.rebate.toString() : '1',
          accNo: firstDayFee.accNo || '', // 添加账务号
          templateCode: selectedCoupon.value.templateCode,
          templateName: selectedCoupon.value.templateName,
          couponCode: selectedCoupon.value.couponCode,
          couponType: selectedCoupon.value.couponType,
          money: selectedCoupon.value.money,
          rebate: selectedCoupon.value.rebate,
        })
      } else if (Object.keys(dailyCoupons.value).length > 0) {
        // 连住多天优惠模式
        // 遍历每一天的优惠券
        for (const date in dailyCoupons.value) {
          const dayCoupons = dailyCoupons.value[date]

          // 遍历该日期下的所有优惠券
          for (const accNo in dayCoupons) {
            const coupon = dayCoupons[accNo]
            if (!coupon) {
              continue
            }

            // 计算该优惠券的优惠金额
            let dailyDiscount = 0
            if (coupon.couponType === 'discount') {
              dailyDiscount = Number.parseFloat((coupon.fee * (1 - coupon.rebate)).toFixed(2))
            } else if (coupon.couponType === 'free') {
              dailyDiscount = Number.parseFloat(coupon.fee.toFixed(2))
            } else {
              dailyDiscount = Number.parseFloat(Math.min(coupon.discount, coupon.fee).toFixed(2))
            }

            // 构建优惠券详情文本
            let couponTypeText = ''
            let discountValueText = ''

            if (coupon.couponType === 'discount') {
              couponTypeText = '折扣券'
              discountValueText = `折扣值:${(coupon.rebate * 100).toFixed(0)}`
            } else if (coupon.couponType === 'free') {
              couponTypeText = '免房券'
              discountValueText = `金额:${dailyDiscount.toFixed(2)}`
            } else {
              couponTypeText = '代金券'
              discountValueText = `金额:${dailyDiscount.toFixed(2)}`
            }

            const accDetailText = `优惠券号:${coupon.templateCode}\n折扣类型:${couponTypeText}，${discountValueText}\n适用日期:${date}`

            // 添加到付款信息列表
            params.payInfoList.push({
              remark: `使用优惠券：${coupon.name}（${date}）`,
              fee: dailyDiscount,
              subCode: 'coupon',
              accDetail: accDetailText,
              payValue: coupon.couponType === 'discount' ? coupon.rebate.toString() : '1',
              accNo: coupon.accNo || '', // 使用优惠券中保存的accNo
              templateCode: coupon.templateCode,
              templateName: coupon.templateName,
              couponCode: coupon.couponCode,
              couponType: coupon.couponType,
              money: coupon.money,
              rebate: coupon.rebate,
            })
          }
        }
      }
    } catch (error) {
      ElMessage.error('处理优惠券信息失败')
      return
    }
  }

  switch (paymentMethod) {
    case PayModelCode.STORE_CARD:
    case PayModelCode.CARD:
    case PayModelCode.STORE_CARD_REFUND:
      params.pay.payCode = memberCardDetail.value.storeCardNo
      params.pay.phone = form.value.cashierForm.search.phone
      params.pay.pwd = form.value.cashierForm.search.password
      params.pay.storeCardNo = memberCardDetail.value.storeCardNo
      params.pay.mcode = memberCardDetail.value.mcode
      break
    case PayModelCode.BANK_CARD:
    case PayModelCode.BANK:
      params.pay.bankType = form.value.cashierForm.bankCard
      params.pay.bankCardNo = form.value.cashierForm.bankCardNo
      break
    case PayModelCode.SCAN_GUN:
    case PayModelCode.SCAN:
      params.pay.payCode = form.value.cashierForm.barCode
      break
    case PayModelCode.CREDIT_S_ACCOUNT:
    case PayModelCode.CREDIT:
      params.pay.payCode = form.value.cashierForm.account
      break
    default:
      break
  }
  consAccount(params)
}

function consAccount(value: any) {
  if (form.value.cashierForm.no === '' || form.value.cashierForm.no == null || form.value.cashierForm.no === undefined) {
    return ElMessage.error(t('accountCannotBeEmpty'))
  }
  loading.value = true
  accountApi
    .payCheckOut(value)
    .then((res: any) => {
      loading.value = false
      if (res.code === 0) {
        ElMessage.success(t('operationSuccessful'))

        // 设置标志位，表示已提交账务
        judge.value = true

        // 调用 getStatAccountData 获取更新后的数据，清空优惠券信息
        getStatAccountData()
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch(() => {
      loading.value = false
    })
}
/** 发送验证码方法 */
function sendMobileSmsCode(scene: any, mobile: any) {
  const payload = {
    scene,
    mobile,
    // 设置签名类型为默认门店
    sendId: 0,
    hcode: userStore.hcode,
  }
  authApi.sendLoginSmsCode(payload).then((res: any) => {
    if (res && res.code === 0) {
      ElMessage.success(t('smsSendSuccess'))
      startSmsTimer()
    } else {
      ElMessage.warning(res.message || t('sendFailed'))
    }
  })
}
/** 新增：启动短信倒计时 */
function startSmsTimer() {
  smsButtonDisabled.value = true
  smsCount.value = 60
  smsTimer = window.setInterval(() => {
    smsCount.value--
    if (smsCount.value <= 0) {
      if (smsTimer) {
        clearInterval(smsTimer)
      }
      smsButtonDisabled.value = false
      smsCount.value = 60
    }
  }, 1000)
}
/** 付款方式下拉 */
function handPaymentChange(value: any) {
  form.value.cashierForm.paymentType = ''
  verifyMode.value = ''
  const selectedOption = form.value.payModes.find((item: any) => item.code === value)
  if (selectedOption) {
    form.value.cashierForm.paymentType = selectedOption.mode
    if (form.value.cashierForm.paymentType === 'rmb') {
      // 现金
    } else if (form.value.cashierForm.paymentType === 'card') {
      // 给储值卡赋值手机号
      const phone = selectedList.value.filter((item) => item.isMain == form.value.cashierForm.no)[0].phone
      form.value.cashierForm.search.phone = phone
      // 储值卡
    } else if (form.value.cashierForm.paymentType === 'bank') {
      // 银行卡
    } else if (form.value.cashierForm.paymentType === 'scan') {
      // 扫码枪
    } else {
      // AR账
      getArSetList()
    }
  }
}
/** 获取字典类型 */
async function getRoomCleanType() {
  const params = { gcode: userStore.gcode, types: [DICT_TYPE_ROOM_CLEAN_TASK].join(',') }
  const { data } = await generalConfigApi.list(params)
  data.forEach((item) => {
    if (item.code == OrderState.CHECK_OUT) {
      isAutoTask.value = item.value
    }
  })
}

// 在 script 部分添加 isMemberSource 计算属性
const isMemberSource = computed(() => {
  // 从选中的订单中检查是否为会员入住
  return selectedList.value.length > 0 && selectedList.value.some((item) => item.guestSrcType === 'member')
})

/** 统计付款金额 */
function getStatAccountData(discountFee = 0, keepOriginalPayMode = false) {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    orderType: form.value.orderType,
    orderTogethers: selectedList.value,
    confirmRoomFeeList: form.value.dataList,
    subCode: subCode.value,
    discountFee, // 传递优惠券金额到后端
  }
  accountApi.statAccount(params).then((res: any) => {
    if (res.code === 0) {
      // 首次调用或没有使用优惠券时，保存原始数据
      if (discountFee === 0 || originalFee.value === 0) {
        originalFee.value = res.data.fee
        originalPayMode.value = res.data.payMode
      }

      // 设置当前金额和付款方式
      form.value.cashierForm.fee = res.data.fee

      // 记录当前的支付模式
      currentPayMode.value = res.data.payMode

      // 即使设置了保持原始支付模式，也要更新支付方式代码（subCode）
      // 因为付款和退款的subCode是不同的
      form.value.cashierForm.paymentMethod = res.data.subCode

      // 结账成功后的处理
      if (judge.value && res.data.isCloseAccount === '1') {
        // 如果勾选了打印结账单，则显示打印加载动画并打开打印弹窗
        if (isPrintOrderBill.value) {
          printLoading.value = true
          // 延迟一下再创建打印组件，确保加载动画可见
          setTimeout(() => {
            printOrderBillVisible.value = true
          }, 100)
        } else {
          onCancel()
        }
      } else if (judge.value && res.data.isCloseAccount === '0') {
        // 账务已提交但窗口不关闭的情况
        ElMessage.success('账务已提交')

        // 清空优惠券信息
        if (selectedCoupon.value || Object.keys(dailyCoupons.value).length > 0) {
          selectedCoupon.value = null
          dailyCoupons.value = {}
          selectedDailyCouponIds.value = {}
          couponDiscount.value = 0
          originalFee.value = 0
          originalPayMode.value = ''
          currentPayMode.value = ''
        }

        // 重置判断标志，允许继续操作
        judge.value = false
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

/** 使用优惠券 */
function useCoupon() {
  // 检查是否为会员入住
  if (!isMemberSource.value) {
    ElMessage.warning('会员入住享受更多权益')
    return
  }

  // 确保有原始数据记录
  if (originalFee.value === 0) {
    originalFee.value = form.value.cashierForm.fee
  }
  if (originalPayMode.value === '') {
    // 尝试获取当前的payMode
    const currentPayMode = form.value.payModes.find((item) => item.code === form.value.cashierForm.paymentMethod)?.mode
    originalPayMode.value = currentPayMode || '1' // 默认付款模式为1
  }

  // 先检查优惠券配置
  checkCouponConfig().then(() => {
    // 获取主单的联房码
    const mainOrder = selectedList.value.find((item) => item.isMain === form.value.cashierForm.no)
    if (mainOrder) {
      // 先获取房费数据，再打开弹窗
      fetchRoomFeeList(mainOrder.togetherCode)
        .then(() => {
          couponDialogVisible.value = true
        })
        .catch(() => {
          ElMessage.error('获取房费数据失败，无法使用优惠券')
        })
    } else {
      ElMessage.error('未找到主单信息，无法使用优惠券')
    }
  })
}

/** 检查优惠券配置 */
async function checkCouponConfig() {
  try {
    const params = {
      gcode: userStore.gcode,
      hcode: userStore.hcode,
    }

    const res = await couponConfigApi.detail(params)

    if (res.code === 0 && res.data && res.data.length > 0) {
      // 检查是否允许连住多天优惠
      isWalkinMoreEnabled.value = res.data[0].isWalkinMore === '1'
    } else {
      isWalkinMoreEnabled.value = false
    }

    return Promise.resolve()
  } catch (error) {
    isWalkinMoreEnabled.value = false
    return Promise.resolve()
  }
}

/** 优惠券选择确认 */
function onCouponConfirm(coupon: any, discount: number, dailyCouponsMap?: Record<string, any>, isFinalConfirm = false) {
  // 保存优惠券数据
  if (dailyCouponsMap) {
    // 连住多天优惠模式
    dailyCoupons.value = dailyCouponsMap
    selectedCoupon.value = null
    couponDiscount.value = Number.parseFloat(discount.toFixed(2))

    // 保存每日优惠券ID映射，用于下次打开弹窗时恢复选择
    selectedDailyCouponIds.value = {}
    for (const date in dailyCouponsMap) {
      for (const accNo in dailyCouponsMap[date]) {
        selectedDailyCouponIds.value[accNo] = dailyCouponsMap[date][accNo].id
      }
    }
  } else {
    // 首日优惠模式
    selectedCoupon.value = coupon
    dailyCoupons.value = {}
    selectedDailyCouponIds.value = {} // 清空每日优惠券ID映射
    couponDiscount.value = Number.parseFloat(discount.toFixed(2)) // 确保折扣金额保留两位小数
  }

  // 调用statAccount接口，传递优惠金额
  getStatAccountData(couponDiscount.value, false)

  // 仅当是最终确认时才关闭弹窗
  if (isFinalConfirm) {
    couponDialogVisible.value = false
  }
}

/** 移除优惠券 */
function removeCoupon() {
  if (selectedCoupon.value || Object.keys(dailyCoupons.value).length > 0) {
    // 清空优惠券选择
    selectedCoupon.value = null
    dailyCoupons.value = {}
    selectedDailyCouponIds.value = {}
    couponDiscount.value = 0

    // 调用statAccount接口，不传递优惠金额，恢复原状态
    getStatAccountData(0, false)
  }
}

/** 移除指定日期的所有优惠券 */
function removeCouponsByDate(date) {
  if (dailyCoupons.value[date]) {
    // 获取该日期优惠券的折扣金额
    const discountAmount = calculateDateTotalDiscount(date)

    // 从总折扣中减去
    couponDiscount.value = Number.parseFloat((couponDiscount.value - discountAmount).toFixed(2))

    // 删除该日期的优惠券
    delete dailyCoupons.value[date]

    // 更新selectedDailyCouponIds，移除该日期对应的优惠券ID
    for (const accNo in selectedDailyCouponIds.value) {
      const roomFee = roomFeeList.value.find((fee) => fee.accNo === accNo)
      if (roomFee && roomFee.bizDate === date) {
        delete selectedDailyCouponIds.value[accNo]
      }
    }

    // 调用statAccount接口重新计算金额，传递剩余的优惠券折扣金额
    getStatAccountData(couponDiscount.value, false)
  }
}

/** 获取入住类型 */
function getCheckinType() {
  // 从选中的订单中获取入住类型，取第一个作为代表
  return selectedList.value.length > 0 ? selectedList.value[0].checkinType || '' : ''
}

/** 获取房型代码 */
function getRtCode() {
  // 从选中的订单中获取房型代码，取第一个作为代表
  return selectedList.value.length > 0 ? selectedList.value[0].rtCode || '' : ''
}

/** 获取渠道代码 */
function getChannelCode() {
  // 从选中的订单中获取渠道代码，取第一个作为代表
  return selectedList.value.length > 0 ? selectedList.value[0].channelCode || '' : ''
}

/** 获取手机号 */
function getPhone() {
  // 从选中的订单中获取手机号，取第一个作为代表
  return selectedList.value.length > 0 ? selectedList.value[0].phone || '' : ''
}

/** 获取会员编码 */
function getGuestCode() {
  // 从选中的订单中获取会员编码，取第一个作为代表
  return selectedList.value.length > 0 ? selectedList.value[0].guestCode || '' : ''
}

/** 获取联房码列表 */
function getTogetherCodes() {
  // 从选中的订单中获取联房码列表
  return selectedList.value.map((item) => item.togetherCode)
}

/** 格式化日期为短格式 (例如: 5月20日) */
function formatDateShort(dateStr: string) {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

/** 获取指定日期的所有优惠券 */
function getDayCoupons(date) {
  if (!dailyCoupons.value[date]) {
    return {}
  }
  return dailyCoupons.value[date]
}

/** 计算指定日期的总优惠金额 */
function calculateDateTotalDiscount(date) {
  if (!dailyCoupons.value[date]) {
    return 0
  }

  let total = 0
  const dayCoupons = dailyCoupons.value[date]

  // 计算该日期下所有优惠券的优惠金额总和
  for (const accNo in dayCoupons) {
    const coupon = dayCoupons[accNo]

    // 获取房费对象，检查状态
    const roomFee = roomFeeList.value.find((fee) => fee.accNo === accNo)

    // 如果房费已结算，则跳过计算
    if (roomFee && (roomFee.state === 'settled' || roomFee.isUsed === '1')) {
      continue
    }

    if (coupon.couponType === 'discount') {
      // 折扣券：计算折扣后的金额与原价的差值
      total += Number.parseFloat((coupon.fee * (1 - coupon.rebate)).toFixed(2))
    } else if (coupon.couponType === 'free') {
      // 免房券：返回房费金额
      total += Number.parseFloat(coupon.fee.toFixed(2))
    } else {
      // 代金券：直接返回优惠金额（不超过房费）
      total += Number.parseFloat(Math.min(coupon.discount, coupon.fee).toFixed(2))
    }
  }

  return total
}

/** 获取唯一日期列表 */
function getUniqueDates() {
  return Object.keys(dailyCoupons.value)
}

onMounted(() => {
  getAccountList()
  getConstants()
  getPayModes()
  getRoomCleanType()
  checkCouponConfig() // 检查优惠券配置
  getFrontConfig()

  // 添加键盘事件监听器
  window.addEventListener('keydown', handleKeyDown)
})

onBeforeUnmount(() => {
  // 移除键盘事件监听器
  window.removeEventListener('keydown', handleKeyDown)
})

// 处理键盘事件
function handleKeyDown(event: KeyboardEvent) {
  // 检查是否按下了 Ctrl + Enter
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()

    // 根据当前步骤执行相应的操作
    switch (step.value) {
      case 0:
        // 步骤0: 触发"下一步"按钮
        if (selectedList.value.length > 0) {
          onSumbit(data.value.search.state)
        } else {
          ElMessage.error(t('pleaseSelectCheckoutAccount'))
        }
        break
      case 1:
        // 步骤1: 触发"生成房费"按钮
        generate()
        break
      case 2:
        // 步骤2: 触发"下一步"按钮
        onNext(2)
        break
      case 3:
        // 步骤3: 触发"完成"按钮
        save()
        break
    }
  }
}

// 是否必须要求点确定生成房费按钮
const roomFeeMustGenerate = ref(false)
function getFrontConfig() {
  hotelParamConfigApi
    .getHotelParamConfigFront({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
    })
    .then((res: any) => {
      if (res.code === 0) {
        roomFeeMustGenerate.value = res.data.value.roomFeeMustGenerate === '1'
      }
    })
}

/** 优惠券选择取消 */
function onCouponCancel() {
  couponDialogVisible.value = false
}

// 监听优惠券选择变化
watch(
  () => couponDiscount.value,
  (newValue, oldValue) => {
    // 只有在值确实发生变化，且不是初始化阶段时才调用
    if (newValue !== oldValue && oldValue !== 0) {
      // 调用statAccount接口重新计算金额
      getStatAccountData(newValue, false)
    }
  }
)

// 获取房费列表（用于计算每日优惠）
async function fetchRoomFeeList(togetherCode) {
  try {
    const params = {
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      togetherCodes: togetherCode,
    }

    const res = await accountApi.accountRoomFee(params)

    if (res.code === 0) {
      roomFeeList.value = res.data
    }
    return Promise.resolve()
  } catch (error) {
    console.error('获取房费列表失败:', error)
    return Promise.reject(error)
  }
}

/** 取消弹窗 */
function onCancel() {
  if (step.value > 2) {
    emits('success')
  }
  step.value = 0
  myVisible.value = false
}

// 处理打印结账单弹窗关闭
function onPrintOrderBillClose() {
  printOrderBillVisible.value = false
  printLoading.value = false
  onCancel()
}

// 处理打印开始事件
function onPrintStarted() {
  printLoading.value = false
}
</script>

<template>
  <el-dialog v-model="myVisible" width="900px" :title="t('settleCheckOut')" :close-on-click-modal="false" append-to-body destroy-on-close :show-close="true" top="30vh" @close="onCancel()">
    <PageMain>
      <ElRow>
        <ElCol>
          <ElSteps :active="step" align-center finish-status="success" style="margin: 0 0 10px">
            <ElStep :title="t('selectAccount')" />
            <ElStep :title="t('confirmTotalAmount')" />
            <ElStep :title="t('completePreAuthorization')" />
            <ElStep :title="t('completeCheckout')" />
          </ElSteps>
          <el-form :model="form" label-width="100px">
            <div v-if="step === 0">
              <el-table
                ref="multipleTableRef"
                :data="form.orderTogethers"
                height="300"
                style="width: 600px; margin: auto"
                :header-cell-style="{
                  background: '#f5f7fa',
                  color: '#606266',
                }"
                class="list-table"
                :row-key="getRowKey"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" />
                <el-table-column :label="t('roomNumber')" prop="rNo" />
                <el-table-column :label="t('name')" prop="name" />
                <el-table-column :label="t('guestStatus')" prop="state">
                  <template #default="scope">
                    <span v-if="scope.row.state === OrderState.CHECK_IN">{{ t('checkedIn') }}</span>
                    <span v-if="scope.row.state === OrderState.CREDIT">{{ t('credit') }}</span>
                    <span v-if="scope.row.state === OrderState.CHECK_OUT">{{ t('checkedOut') }}</span>
                    <span v-if="scope.row.state === OrderState.OVER">{{ t('over') }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <div class="center-button">
                <el-button @click="onCancel">
                  {{ t('cancel') }}
                </el-button>
                <el-button type="primary" @click="onSumbit(data.search.state)">
                  {{ t('nextStep') }}
                </el-button>
              </div>
            </div>
            <div v-else-if="step === 1">
              <el-form-item :label="t('confirmationMethod')" style="margin-left: 20px">
                <el-radio-group v-model="data.search.state" size="large" @change="onSumbit(data.search.state)">
                  <el-radio :value="ConsumeAccountEnum.ROOM_FEE">
                    {{ t('systemAutoBilling') }}
                  </el-radio>
                  <el-radio :value="ConsumeAccountEnum.HAND_INPUT_ROOM_FEE">
                    {{ t('manualRoomFee') }}
                  </el-radio>
                  <el-radio value="0" :disabled="roomFeeMustGenerate">
                    {{ t('noAdditionalFee') }}
                  </el-radio>
                  <el-radio :value="ConsumeAccountEnum.ADD_HALF_DAY">
                    {{ t('addHalfDay') }}
                  </el-radio>
                  <el-radio :value="ConsumeAccountEnum.ADD_ALL_DAY">
                    {{ t('addFullDay') }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-table
                :data="data.dataList"
                style="width: calc(100% - 90px); margin: 0 0 0 45px"
                :header-cell-style="{
                  background: '#f5f7fa',
                  color: '#606266',
                }"
                class="list-table"
                height="100%"
              >
                <el-table-column :label="t('roomNumber')" prop="rNo" />
                <el-table-column :label="t('name')" prop="name" />
                <el-table-column :label="t('checkinType')">
                  <template #default="scope">
                    <span v-if="scope.row.checkinType === CheckinType.HOUR_ROOM">{{ t('hourRoom') }}</span>
                    <span v-if="scope.row.checkinType === CheckinType.ALL_DAY">{{ t('allDayRoom') }}</span>
                    <span v-if="scope.row.checkinType === CheckinType.LONG_STAY">{{ t('longStay') }}</span>
                    <span v-if="scope.row.checkinType === CheckinType.SELF_USE">{{ t('selfUse') }}</span>
                    <span v-if="scope.row.checkinType === CheckinType.FREE">{{ t('free') }}</span>
                    <span v-if="scope.row.checkinType === CheckinType.TRAVEL_GROUP">{{ t('travelGroup') }}</span>
                    <span v-if="scope.row.checkinType === CheckinType.MEETING_GROUP">{{ t('meetingGroup') }}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="t('checkinCheckoutTime')" min-width="190" align="center">
                  <template #default="scope">
                    <span style="white-space: nowrap"> {{ scope.row.checkinTime.substring(5, 10) }} {{ scope.row.checkinTime.substring(11, 16) }} / {{ scope.row.checkoutTime.substring(5, 10) }} {{ scope.row.checkoutTime.substring(11, 16) }} </span>
                  </template>
                </el-table-column>
                <el-table-column :label="t('generatedRoomFee')" align="right" min-width="120">
                  <template #default="scope">
                    {{ scope.row.roomFee || 0 }}
                  </template>
                </el-table-column>
                <el-table-column :label="t('expectedAdditionalFee')" align="right" min-width="120">
                  <template #default="scope">
                    <span v-if="isManual">
                      <el-input-number v-model="scope.row.fee" :controls="false" :min="0" style="width: 90px" />
                    </span>
                    <span v-else>{{ scope.row.fee }}</span>
                  </template>
                </el-table-column>
              </el-table>

              <div style="display: flex; justify-content: flex-end; margin-top: 30px">
                <el-button @click="onCancel">
                  {{ t('cancel') }}
                </el-button>
                <el-button type="danger" :disabled="roomFeeMustGenerate" :title="roomFeeMustGenerate ? t('tooltips.roomFeeMustGenerateMode') : ''" @click="onNext(1)">
                  {{ t('noGenerateRoomFee') }}
                </el-button>
                <el-button type="primary" :loading="feeLoading" @click="generate">
                  {{ t('generateRoomFee') }}
                </el-button>
              </div>
            </div>

            <div v-else-if="step === 2">
              <el-table
                :data="form.dataList"
                style="width: 800px; margin: auto"
                :header-cell-style="{
                  background: '#f5f7fa',
                  color: '#606266',
                }"
                class="list-table"
                height="100%"
              >
                <el-table-column :label="t('preAuthType')" prop="subName" />
                <el-table-column :label="t('preAuthAmount')" prop="preAuth" />
                <el-table-column :label="t('actualAmount')">
                  <template #default="scope">
                    <div style="display: flex">
                      <el-input-number v-if="scope.row.state === 'open'" v-model="scope.row.fee" :controls="controls" style="text-align: left" />
                      <span v-else style="padding-left: 15px">{{ scope.row.fee }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="t('status')" prop="state">
                  <template #default="scope">
                    <span v-if="scope.row.state === 'open'">
                      {{ t('preAuth') }}
                    </span>
                    <span v-else-if="scope.row.state === 'cancel'">
                      {{ t('cancel') }}
                    </span>
                    <span v-else-if="scope.row.state === 'closed'">
                      {{ t('complete') }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column :label="t('operation')">
                  <template #default="scope">
                    <div v-if="scope.row.state === 'open'" style="display: flex">
                      <el-button key="primary" type="primary" style="padding: 0" text @click="cancel(scope.row)">
                        {{ t('cancel') }}
                      </el-button>
                      <el-button key="primary" type="primary" style="padding: 0" text @click="finish(scope.row)">
                        {{ t('complete') }}
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="center-button" style="width: 800px">
                <el-button @click="onCancel">
                  {{ t('cancel') }}
                </el-button>
                <el-button type="primary" @click="onNext(2)">
                  {{ t('nextStep') }}
                </el-button>
              </div>
            </div>
            <div v-else>
              <el-form :model="form.cashierForm" style="width: 600px; margin: 0 auto" size="default" label-width="130px">
                <el-form-item :label="t('checkoutAccount')">
                  <el-select v-model="form.cashierForm.no">
                    <el-option v-for="(item, index) in selectedList" :key="index" :label="`${item.rNo}-${item.name}`" :value="item.isMain" />
                  </el-select>
                </el-form-item>
                <el-form-item :label="t('paymentMethod')">
                  <el-select v-model="form.cashierForm.paymentMethod" filterable :placeholder="t('select')" @change="handPaymentChange">
                    <el-option v-for="item in form.payModes" :key="item.code" :label="item.name" :value="item.code" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <div class="subject">
                    <span v-for="item in comPayment" :key="item.code" @click="handPayClick(item)">{{ item.name }}</span>
                  </div>
                </el-form-item>
                <el-form-item :label="t('paymentAmount')">
                  <el-input-number v-model="form.cashierForm.fee" :controls="controls" style="flex: 1" />
                  <el-tooltip :content="isMemberSource ? '' : t('memberBenefitTooltip')" :disabled="isMemberSource" placement="top">
                    <el-button style="width: 120px; margin-left: 10px" :disabled="!isMemberSource" @click="useCoupon">
                      {{ t('useCoupon') }}
                    </el-button>
                  </el-tooltip>
                </el-form-item>
                <!-- 显示已选择的优惠券信息 -->
                <el-form-item v-if="selectedCoupon || Object.keys(dailyCoupons).length > 0" :label="t('selectedCoupons')">
                  <div class="selected-coupon">
                    <!-- 首日优惠模式 -->
                    <template v-if="selectedCoupon">
                      <el-tag :key="selectedCoupon.id" type="success" closable @close="removeCoupon"> {{ selectedCoupon.name }} (-¥{{ couponDiscount.toFixed(2) }}) </el-tag>
                      <div class="coupon-amounts">
                        <div class="original-amount">{{ originalPayMode === '-1' ? t('originalRefundAmount') : t('originalPaymentAmount') }}：¥{{ Math.abs(originalFee).toFixed(2) }}</div>
                        <div class="discount-amount">{{ t('discountAmount') }}：¥{{ couponDiscount.toFixed(2) }}</div>
                        <div class="final-amount">{{ currentPayMode === '-1' ? t('refundAmount') : t('paymentAmount2') }}：¥{{ Math.abs(form.cashierForm.fee).toFixed(2) }}</div>
                      </div>
                    </template>

                    <!-- 连住多天优惠模式 -->
                    <template v-else-if="Object.keys(dailyCoupons).length > 0">
                      <div class="multi-day-coupons">
                        <div class="multi-day-header">
                          <span>{{ t('multiDayDiscount') }}</span>
                          <el-button type="text" class="remove-all" @click="removeCoupon">
                            {{ t('removeAll') }}
                          </el-button>
                        </div>
                        <div class="multi-day-list">
                          <div v-for="date in getUniqueDates()" :key="date" class="multi-day-item">
                            <div class="date">
                              {{ formatDateShort(date) }}
                            </div>
                            <div class="coupon-tags">
                              <el-tag v-for="(coupon, accNo) in getDayCoupons(date)" :key="accNo" size="small" type="success" class="coupon-tag">
                                {{ coupon.name }}
                                <span class="coupon-discount">
                                  <template v-if="coupon.couponType === 'discount'"> {{ (coupon.rebate * 100).toFixed(0) }}{{ t('discountUnit') }} </template>
                                  <template v-else-if="coupon.couponType === 'free'"> {{ t('freeRoom') }} </template>
                                  <template v-else> ¥{{ coupon.discount.toFixed(2) }} </template>
                                </span>
                              </el-tag>
                            </div>
                            <div class="discount">¥{{ calculateDateTotalDiscount(date).toFixed(2) }}</div>
                            <el-button type="text" class="remove-btn" @click="removeCouponsByDate(date)">
                              <el-icon><Close /></el-icon>
                            </el-button>
                          </div>
                        </div>
                        <div class="coupon-amounts">
                          <div class="original-amount">{{ originalPayMode === '-1' ? t('originalRefundAmount') : t('originalPaymentAmount') }}：¥{{ Math.abs(originalFee).toFixed(2) }}</div>
                          <div class="discount-amount">{{ t('discountAmount') }}：¥{{ couponDiscount.toFixed(2) }}</div>
                          <div class="final-amount">{{ currentPayMode === '-1' ? t('refundAmount') : t('paymentAmount') }}：¥{{ Math.abs(form.cashierForm.fee).toFixed(2) }}</div>
                        </div>
                      </div>
                    </template>
                  </div>
                </el-form-item>
                <!-- 扫码枪 -->
                <div v-if="form.cashierForm.paymentMethod === 'scan_gun'">
                  <el-form-item :label="t('scanBarcode')">
                    <el-button :disabled="form.cashierForm.fee === 0" @click="scanCode">
                      {{ t('scanBarcode') }}
                    </el-button>
                  </el-form-item>
                  <el-form-item v-if="signCode && form.cashierForm.fee !== 0" :label="t('barcode')">
                    <el-input ref="signCodeRef" v-model="form.cashierForm.barCode" :placeholder="t('enterBarcode')" clearable />
                  </el-form-item>
                </div>
                <!-- 储值卡 -->
                <div v-if="form.cashierForm.paymentMethod === 'store_card' || form.cashierForm.paymentMethod === 'store_card_refund'">
                  <el-form-item :label="t('storeCard')">
                    <el-input v-model="form.cashierForm.search.phone" :placeholder="t('enterPhoneNumber')" clearable class="!w-[70%]" @keydown.enter="getMemberList">
                      <template #append>
                        <el-button :icon="Search" @click="getMemberList" />
                      </template>
                    </el-input>
                    <el-card v-show="cardState === true && cardList.length > 0" style="width: 100%">
                      <span>
                        <p v-for="(o, index) in cardList" :key="index" class="text" @click="onClick(o)">{{ o.mtName }}&nbsp;&nbsp;&nbsp;{{ o.isG === '1' ? t('groupCard') : t('storeCard') }}</p>
                      </span>
                    </el-card>

                    <div v-if="memberCardDetail.mtName" class="el-form-item-msg" style="font-size: 12px; color: #999">
                      <span> {{ t('name') }}：{{ memberCardDetail.mtName }} </span>
                      <span style="margin-left: 8px"> {{ t('phone') }}：{{ memberCardDetail.phone }} </span>
                      <span style="margin-left: 8px"> {{ t('balance') }}：{{ memberCardDetail.balance }} </span>
                    </div>
                    &nbsp;&nbsp;&nbsp;
                    <el-checkbox v-model="form.isSendSms" true-value="1" false-value="0" :label="t('smsReminder')" />&nbsp;
                  </el-form-item>
                  <el-form-item v-if="verifyMode === '2'">
                    <el-input v-model="form.smsCode" :placeholder="t('enterSmsCode')" style="width: 30%" />
                    <el-button style="margin-left: 20px" :disabled="smsButtonDisabled" @click="sendMobileSmsCode(5, form.cashierForm.search.phone)">
                      {{ smsButtonText }}
                    </el-button>
                  </el-form-item>
                  <el-form-item v-if="memberCardDetail.mtName && verifyMode === '1'" :label="t('password')">
                    <el-input v-model="form.cashierForm.search.password" style="width: 240px" type="password" :placeholder="t('enterPassword')" />
                  </el-form-item>
                </div>
                <!-- 银行卡 -->
                <div v-if="form.cashierForm.paymentMethod === 'bank_card'">
                  <el-form-item :label="t('bankCard')">
                    <el-select v-model="form.cashierForm.bankCard" clearable filterable :placeholder="t('chooseBankCardType')">
                      <el-option v-for="item in bankCardList" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="t('bankCardNumber')">
                    <el-input v-model="form.cashierForm.bankCardNo" :placeholder="t('enterBankCardNumber')" />
                  </el-form-item>
                </div>
                <!-- AR账 -->
                <div v-if="form.cashierForm.paymentMethod === 'credit_s_account'">
                  <el-form-item :label="t('account')">
                    <el-select v-model="form.cashierForm.account" filterable :placeholder="t('select')" @change="handAccountChange">
                      <el-option v-for="item in arSetDataList" :key="item.arSetCode" :label="item.arSetName" :value="item.arSetCode" />
                    </el-select>
                    <div v-if="form.cashierForm.account" class="el-form-item-msg" style="font-size: 12px; color: #999">
                      <span
                        >{{ t('availableAmount') }}：
                        {{ arSetDetail.creditAccType === '0' ? arSetDetail.availableAmount || 0 : arSetDetail.balance }}
                      </span>
                      <span style="margin-left: 8px">{{ t('accountType') }}：{{ arSetDetail.creditAccType === '0' ? t('creditAccount') : t('prepaidAccount') }}</span>
                      <span style="margin-left: 8px">{{ t('settlementPeriod') }}：{{ arSetDetail.creditPayDays === '0' ? arSetDetail.creditPayFix : t('permanentPeriod') }}</span>
                    </div>
                  </el-form-item>
                </div>
                <el-form-item :label="t('remark')">
                  <el-input v-model="form.remark" type="textarea" :rows="3" :placeholder="t('enterRemark')" />
                </el-form-item>
              </el-form>
              <div class="center-button">
                <div style="display: flex; align-items: center; margin-bottom: 20px">
                  <div style="display: flex; gap: 20px; align-items: center; flex: 1">
                    <el-checkbox v-model="isPrintOrderBill" :label="t('printOrderBill')" />
                    <el-checkbox v-model="isAutoTask" true-value="1" false-value="0" :label="t('assignCleaningTask')" />
                  </div>
                  <div style="display: flex; gap: 10px; margin-left: 40px">
                    <el-button @click="onCancel">
                      {{ t('cancel') }}
                    </el-button>
                    <el-button type="primary" :loading="loading" @click="save">
                      {{ t('complete') }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
        </ElCol>
      </ElRow>
    </PageMain>
  </el-dialog>

  <!-- 优惠券选择弹框 -->
  <CouponDialog
    v-model="couponDialogVisible"
    :order-amount="form.cashierForm.fee + couponDiscount"
    :selected-coupon="selectedCoupon"
    :checkin-type="getCheckinType()"
    :rt-code="getRtCode()"
    :channel-code="getChannelCode()"
    :phone="getPhone()"
    :guest-code="getGuestCode()"
    :together-codes="getTogetherCodes()"
    :is-walkin-more-enabled="isWalkinMoreEnabled"
    :selected-daily-coupon-ids="selectedDailyCouponIds"
    @confirm="onCouponConfirm"
    @cancel="onCouponCancel"
  />

  <!-- 打印结账单弹窗 -->
  <PrintOrderBill v-if="printOrderBillVisible" v-model="printOrderBillVisible" :order-no="props.orderNo" :auto-show-print="true" :hide-dialog="true" @update:model-value="onPrintOrderBillClose" @print-started="onPrintStarted" />

  <!-- 打印加载动画 - 使用 Teleport 传送到 body -->
  <Teleport to="body">
    <div v-if="printLoading" class="print-loading-overlay">
      <div class="print-loading-content">
        <div class="loading-spinner" />
        <div class="loading-text">正在准备打印...</div>
        <div class="loading-subtext">请稍候，即将跳转到打印预览</div>
      </div>
    </div>
  </Teleport>
</template>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}

.table-from {
  padding: 10px 16px;
}

.subject {
  display: flex;
  align-items: center;

  span {
    display: inline-block;
    margin-right: 5px;
    font-size: 14px;
    color: var(--el-color-primary);
    cursor: pointer;
  }
}

.text {
  padding-left: 16px;
  margin: 0;
}

.text:hover {
  cursor: pointer;
  background-color: #f2f2f2;
}

.center-button {
  display: flex;
  @apply items-center;
  justify-content: flex-end;
  width: 600px;
  margin: 0 auto;
  margin-top: 30px;
}

:deep(.el-input-number .el-input .el-input__inner) {
  text-align: left;
}

.selected-coupon {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;

  .el-tag {
    margin: 0;
  }

  .coupon-amounts {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .original-amount {
      font-size: 14px;
      color: #909399;
      text-decoration: line-through;
    }

    .discount-amount {
      font-size: 14px;
      color: #e6a23c;
      font-weight: bold;
    }

    .final-amount {
      font-size: 16px;
      color: #67c23a;
      font-weight: bold;
    }
  }

  .multi-day-coupons {
    width: 100%;

    .multi-day-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: bold;

      .remove-all {
        color: #f56c6c;
        padding: 0;
      }
    }

    .multi-day-list {
      max-height: 150px;
      overflow-y: auto;
      margin-bottom: 10px;
      padding: 5px;
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .multi-day-item {
        display: flex;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px dashed #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .date {
          width: 80px;
          font-weight: bold;
        }

        .coupon-tags {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          align-items: center;
          margin: 0 10px;
        }

        .coupon-tag {
          display: flex;
          align-items: center;

          .coupon-discount {
            margin-left: 5px;
            font-weight: bold;
            color: #e6a23c;
          }
        }

        .discount {
          width: 120px;
          text-align: right;
          color: #f56c6c;
          font-weight: bold;
        }

        .remove-btn {
          padding: 2px;
          margin-left: 5px;
          color: #909399;

          &:hover {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

/* 打印加载动画样式 */
.print-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  backdrop-filter: blur(2px);
}

.print-loading-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 320px;
  width: 90%;
  position: relative;
  z-index: 100000;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.loading-subtext {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
</style>
