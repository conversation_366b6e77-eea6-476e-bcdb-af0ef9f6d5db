{"version": 3, "file": "memberType.api-Cpg3heHv.js", "sources": ["../../src/api/modules/member/type/memberType.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/member/type'\r\n/**\r\n * 会员类型接口\r\n */\r\nexport default {\r\n  /**\r\n   * 获取会员类型列表 返回数据需要按照级别正序\r\n   * @param data gcode:集团代码\r\n   * @returns\r\n   */\r\n  getTypeList: (data: { gcode: string, mtCode?: string, mtName?: string, level?: string, isEnable?: string }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n  /**\r\n   * 获得会员类型简单列表 包括 mtCode  mtName\r\n   * @param data\r\n   */\r\n  getTypeSimpleList: (data: { gcode: string, mtCode?: string, mtName?: string, level?: string, isEnable?: string }) =>\r\n    api.get(`${BASE_PATH}/simple-list`, {\r\n      params: data,\r\n    }),\r\n  /**\r\n   * 获得会员类型\r\n   * @param data\r\n   */\r\n  listMemberType: (gcode: string) => api.get('/admin-api/member/type/list', { params: { gcode } }),\r\n\r\n  /**\r\n   * 获得会员等级\r\n   * @param data\r\n   */\r\n  getType: (data: { gcode: string, mtCode: string }) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: data,\r\n    }),\r\n  /**\r\n   * 修改状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateTypeStatus: (data: { id: number, isEnable: string }) =>\r\n    api.put(`${BASE_PATH}/update-status`, data, {}),\r\n  /**\r\n   * 创建会员类型\r\n   * @param data\r\n   */\r\n  createType: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n  /**\r\n   * 更新会员类型\r\n   * @param data\r\n   */\r\n  updateType: (data: any) => api.put(`${BASE_PATH}/update`, data, {}),\r\n}\r\n"], "names": ["BASE_PATH", "memberTypeApi", "getTypeList", "data", "api", "get", "params", "getTypeSimpleList", "listMemberType", "gcode", "getType", "updateTypeStatus", "put", "createType", "post", "updateType"], "mappings": "wCAEA,MAAMA,EAAY,wBAIHC,EAAA,CAMbC,YAAcC,GACZC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAMZI,kBAAoBJ,GAClBC,EAAIC,IAAI,GAAGL,gBAAyB,CAClCM,OAAQH,IAMZK,eAAiBC,GAAkBL,EAAIC,IAAI,8BAA+B,CAAEC,OAAQ,CAAEG,WAMtFC,QAAUP,GACRC,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQH,IAOZQ,iBAAmBR,GACjBC,EAAIQ,IAAI,GAAGZ,kBAA2BG,EAAM,IAK9CU,WAAaV,GAAcC,EAAIU,KAAK,GAAGd,WAAoBG,EAAM,IAKjEY,WAAaZ,GAAcC,EAAIQ,IAAI,GAAGZ,WAAoBG,EAAM,CAAE"}