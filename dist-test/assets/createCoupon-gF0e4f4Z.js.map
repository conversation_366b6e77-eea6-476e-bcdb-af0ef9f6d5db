{"version": 3, "file": "createCoupon-gF0e4f4Z.js", "sources": ["../../src/views/marketing/coupon/template/components/DetailForm/createCoupon.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"pleaseEnterTitle\": \"Please enter a title\",\r\n    \"addSuccess\": \"Added successfully\",\r\n    \"basicInformation\": \"Basic Info\",\r\n    \"couponType\": \"Coupon Type\",\r\n    \"couponName\": \"Coupon Name\",\r\n    \"pleaseEnterCouponName\": \"Please enter coupon name\",\r\n    \"discountMethod\": \"Discount Method\",\r\n    \"pleaseEnterAmount\": \"Please enter amount\",\r\n    \"yuan\": \"$\",\r\n    \"discount\": \"Discount\",\r\n    \"discountPlaceholder\": \"0.88=88% off\",\r\n    \"discountUnit\": \"%\",\r\n    \"usageThreshold\": \"Usage Threshold\",\r\n    \"spendOver\": \"Spend over\",\r\n    \"pleaseEnterThresholdAmount\": \"Please enter threshold amount\",\r\n    \"couponNumberSetting\": \"Coupon Number Setting\",\r\n    \"useNumericCouponNumber\": \"Use Numeric Coupon Number\",\r\n    \"financialInformation\": \"Financial Info\",\r\n    \"accountingMethod\": \"Accounting Method\",\r\n    \"addPayment\": \"Add a Payment\",\r\n    \"deductConsumption\": \"Deduct a Consumption\",\r\n    \"coupon\": \"Coupon\",\r\n    \"couponDeduction\": \"Coupon Deduction\",\r\n    \"costType\": \"Cost Type\",\r\n    \"store\": \"Hotel\",\r\n    \"group\": \"Group\",\r\n    \"applicableScope\": \"Scope\",\r\n    \"applicableStores\": \"Hotels\",\r\n    \"pleaseSelectApplicableStores\": \"Select hotels\",\r\n    \"applicableChannels\": \"Channels\",\r\n    \"pleaseSelectApplicableChannels\": \"Select channels\",\r\n    \"applicableRoomTypes\": \"Room Types\",\r\n    \"pleaseSelectApplicableRoomTypes\": \"Select room types\",\r\n    \"storeRoomType\": \"Hotel Room Type\",\r\n    \"groupRoomType\": \"Group Room Type\",\r\n    \"checkinType\": \"Check-in Type\",\r\n    \"allDayRoom\": \"All-day Room\",\r\n    \"hourlyRoom\": \"Hourly Room\",\r\n    \"validityPeriod\": \"Validity Period\",\r\n    \"fixedValidity\": \"Fixed Validity\",\r\n    \"permanentValidity\": \"Permanent Validity\",\r\n    \"dynamicValidity\": \"Dynamic Validity\",\r\n    \"startDate\": \"Start Date\",\r\n    \"endDate\": \"End Date\",\r\n    \"startFromDay\": \"Start from day\",\r\n    \"dayStart\": \"\",\r\n    \"validWithinDays\": \"Valid within\",\r\n    \"days\": \"days\",\r\n    \"validityExcludesEndDate\": \"Validity period does not include the end date\",\r\n    \"other\": \"Other\",\r\n    \"usageLimit\": \"Usage Limit\",\r\n    \"numberAllowedPerOrder\": \"Number allowed per order\",\r\n    \"unitPiece\": \"pieces\",\r\n    \"enabled\": \"Enabled\",\r\n    \"enable\": \"Enable\",\r\n    \"disable\": \"Disable\",\r\n    \"instructions\": \"Instructions\",\r\n    \"pleaseEnterInstructions\": \"Please enter instructions\",\r\n    \"adjustRoomFee\": \"Adjust on Original Fee\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"pleaseEnterTitle\": \"请输入标题\",\r\n    \"addSuccess\": \"新增成功\",\r\n    \"basicInformation\": \"基础信息\",\r\n    \"couponType\": \"优惠券类型\",\r\n    \"couponName\": \"优惠券名称\",\r\n    \"pleaseEnterCouponName\": \"请输入券名称\",\r\n    \"discountMethod\": \"优惠方式\",\r\n    \"pleaseEnterAmount\": \"请输入金额\",\r\n    \"yuan\": \"元\",\r\n    \"discount\": \"折扣\",\r\n    \"discountPlaceholder\": \"0.88=88折\",\r\n    \"discountUnit\": \"折\",\r\n    \"usageThreshold\": \"使用门槛\",\r\n    \"spendOver\": \"消费满\",\r\n    \"pleaseEnterThresholdAmount\": \"请输入适用门槛金额\",\r\n    \"couponNumberSetting\": \"券号设置\",\r\n    \"useNumericCouponNumber\": \"券号使用数字\",\r\n    \"financialInformation\": \"财务信息\",\r\n    \"accountingMethod\": \"入账方式\",\r\n    \"addPayment\": \"增加一笔付款\",\r\n    \"deductConsumption\": \"抵扣一笔消费\",\r\n    \"coupon\": \"优惠券\",\r\n    \"couponDeduction\": \"优惠券抵扣\",\r\n    \"costType\": \"成本类型\",\r\n    \"store\": \"门店\",\r\n    \"group\": \"集团\",\r\n    \"applicableScope\": \"适用范围\",\r\n    \"applicableStores\": \"适用门店\",\r\n    \"pleaseSelectApplicableStores\": \"请选择适用门店\",\r\n    \"applicableChannels\": \"适用渠道\",\r\n    \"pleaseSelectApplicableChannels\": \"请选择适用渠道\",\r\n    \"applicableRoomTypes\": \"适用房型\",\r\n    \"pleaseSelectApplicableRoomTypes\": \"请选择适用房型\",\r\n    \"storeRoomType\": \"门店房型\",\r\n    \"groupRoomType\": \"集团房型\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"allDayRoom\": \"全天房\",\r\n    \"hourlyRoom\": \"钟点房\",\r\n    \"validityPeriod\": \"有效期\",\r\n    \"fixedValidity\": \"固定有效\",\r\n    \"permanentValidity\": \"永久有效\",\r\n    \"dynamicValidity\": \"动态有效\",\r\n    \"startDate\": \"开始日期\",\r\n    \"endDate\": \"结束日期\",\r\n    \"startFromDay\": \"从下发第\",\r\n    \"dayStart\": \"日开始\",\r\n    \"validWithinDays\": \"天内有效\",\r\n    \"days\": \"天\",\r\n    \"validityExcludesEndDate\": \"有效日期不包含结束日期\",\r\n    \"other\": \"其他\",\r\n    \"usageLimit\": \"使用限制\",\r\n    \"numberAllowedPerOrder\": \"同一个订单允许使用张数\",\r\n    \"unitPiece\": \"张\",\r\n    \"enabled\": \"是否启用\",\r\n    \"enable\": \"启用\",\r\n    \"disable\": \"禁用\",\r\n    \"instructions\": \"使用说明\",\r\n    \"pleaseEnterInstructions\": \"请输入使用说明\",\r\n    \"adjustRoomFee\": \"在原费用上冲减\"\r\n  },\r\n  \"km\": {\r\n    \"pleaseEnterTitle\": \"សូមបញ្ចូលចំណងជើង\",\r\n    \"addSuccess\": \"បន្ថែមដោយជោគជ័យ\",\r\n    \"basicInformation\": \"ព័ត៌មានមូលដ្ឋាន\",\r\n    \"couponType\": \"ប្រភេទគូប៉ុង\",\r\n    \"couponName\": \"ឈ្មោះគូប៉ុង\",\r\n    \"pleaseEnterCouponName\": \"សូមបញ្ចូលឈ្មោះគូប៉ុង\",\r\n    \"discountMethod\": \"វិធីសារពើភ័ណ្ឌ\",\r\n    \"pleaseEnterAmount\": \"សូមបញ្ចូលចំនួនទឹកប្រាក់\",\r\n    \"yuan\": \"រៀល\",\r\n    \"discount\": \"ការបញ្ចុះតម្លៃ\",\r\n    \"discountPlaceholder\": \"0.88=88% បញ្ចុះតម្លៃ\",\r\n    \"discountUnit\": \"%\",\r\n    \"usageThreshold\": \"ព្រំដែនការប្រើប្រាស់\",\r\n    \"spendOver\": \"ចំណាយលើស\",\r\n    \"pleaseEnterThresholdAmount\": \"សូមបញ្ចូលចំនួនទឹកប្រាក់ព្រំដែន\",\r\n    \"couponNumberSetting\": \"ការកំណត់លេខគូប៉ុង\",\r\n    \"useNumericCouponNumber\": \"ប្រើលេខគូប៉ុងលេខ\",\r\n    \"financialInformation\": \"ព័ត៌មានហិរញ្ញវត្ថុ\",\r\n    \"accountingMethod\": \"វិធីសាស្ត្រគណនេយ្យ\",\r\n    \"addPayment\": \"បន្ថែមការទូទាត់\",\r\n    \"deductConsumption\": \"កាត់បន្ថយការប្រើប្រាស់\",\r\n    \"coupon\": \"គូប៉ុង\",\r\n    \"couponDeduction\": \"គូប៉ុងកាត់បន្ថយ\",\r\n    \"costType\": \"ប្រភេទថ្លៃដើម\",\r\n    \"store\": \"ហាង\",\r\n    \"group\": \"ក្រុម\",\r\n    \"applicableScope\": \"វិសាលភាពអាចអនុវត្តបាន\",\r\n    \"applicableStores\": \"ហាងដែលអាចអនុវត្តបាន\",\r\n    \"pleaseSelectApplicableStores\": \"សូមជ្រើសរើសហាងដែលអាចអនុវត្តបាន\",\r\n    \"applicableChannels\": \"ឆានែលដែលអាចអនុវត្តបាន\",\r\n    \"pleaseSelectApplicableChannels\": \"សូមជ្រើសរើសឆានែលដែលអាចអនុវត្តបាន\",\r\n    \"applicableRoomTypes\": \"ប្រភេទបន្ទប់ដែលអាចអនុវត្តបាន\",\r\n    \"pleaseSelectApplicableRoomTypes\": \"សូមជ្រើសរើសប្រភេទបន្ទប់ដែលអាចអនុវត្តបាន\",\r\n    \"storeRoomType\": \"ប្រភេទបន្ទប់ហាង\",\r\n    \"groupRoomType\": \"ប្រភេទបន្ទប់ក្រុម\",\r\n    \"checkinType\": \"ប្រភេទការចុះឈ្មោះ\",\r\n    \"allDayRoom\": \"បន្ទប់ពេញមួយថ្ងៃ\",\r\n    \"hourlyRoom\": \"បន្ទប់ម៉ោង\",\r\n    \"validityPeriod\": \"រយៈពេលសុពលភាព\",\r\n    \"fixedValidity\": \"សុពលភាពថេរ\",\r\n    \"permanentValidity\": \"សុពលភាពអចិន្ត្រៃយ៍\",\r\n    \"dynamicValidity\": \"សុពលភាពឌីណាមិច\",\r\n    \"startDate\": \"កាលបរិច្ឆេទចាប់ផ្តើម\",\r\n    \"endDate\": \"កាលបរិច្ឆេទបញ្ចប់\",\r\n    \"startFromDay\": \"ចាប់ផ្តើមពីថ្ងៃទី\",\r\n    \"dayStart\": \"ថ្ងៃចាប់ផ្តើម\",\r\n    \"validWithinDays\": \"សុពលភាពក្នុងរយៈពេល\",\r\n    \"days\": \"ថ្ងៃ\",\r\n    \"validityExcludesEndDate\": \"រយៈពេលសុពលភាពមិនរាប់បញ្ចូលកាលបរិច្ឆេទបញ្ចប់\",\r\n    \"other\": \"ផ្សេងៗ\",\r\n    \"usageLimit\": \"ការកំណត់ការប្រើប្រាស់\",\r\n    \"numberAllowedPerOrder\": \"ចំនួនដែលអនុញ្ញាតក្នុងមួយការបញ្ជាទិញ\",\r\n    \"unitPiece\": \"សន្លឹក\",\r\n    \"enabled\": \"ត្រូវបានបើកឬទេ\",\r\n    \"enable\": \"បើក\",\r\n    \"disable\": \"បិទ\",\r\n    \"instructions\": \"ការណែនាំ\",\r\n    \"pleaseEnterInstructions\": \"សូមបញ្ចូលការណែនាំ\",\r\n    \"adjustRoomFee\": \"កែតម្រូវលើថ្លៃដើម\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { channelApi, couponTemplateApi, dictDataApi, merchantApi, rtApi } from '@/api/modules/index'\r\nimport { BooleanEnum, DictTypeEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  templateCode: '',\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  templateCode: props.templateCode,\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 创建活动的门店代码 */\r\n  hcode: userStore.hcode,\r\n  hname: '',\r\n  /** 活动名称 */\r\n  templateName: '',\r\n  /** 券类型 0：代金券 1:折扣券 2：免房券 */\r\n  couponType: '',\r\n  /** 消费满多少元 */\r\n  consume: 0,\r\n  /** 金额 */\r\n  money: 0,\r\n  /** 折扣 coupon_type=1, 表示折扣，0.88表示88折，当其他类型时，值为1 */\r\n  rebate: 0,\r\n  /** 入账方式 1:增加一笔付款 2:抵扣一笔消费 */\r\n  accMode: '1',\r\n  /** 子类型代码 */\r\n  subCode: 'coupon',\r\n  /** 成本类型 0：集团 1：门店 */\r\n  costType: '0',\r\n  /** 成本价 */\r\n  cost: null as unknown as number | undefined,\r\n  /** 适用范围 json对象，包括房型、应用渠道 */\r\n  scope: {\r\n    rts: [] as { rtCode: string; rtName: string }[],\r\n    channels: [] as { channelCode: string; channelName: string }[],\r\n  },\r\n  /** 房型代码列表 */\r\n  rtCodes: [] as string[],\r\n  /** 0:门店房型 1:集团房型 */\r\n  rtType: '0',\r\n  /** 渠道代码列表 */\r\n  channels: [] as string[],\r\n  /** 是否集团活动 0:否 1：是 */\r\n  isG: '1',\r\n  /** 入住类型 0:全天房 1:钟点房 */\r\n  checkinType: 'all_day',\r\n  /** 有效日期类型 0：固定有效 1：永久有效 2:动态有效 */\r\n  effeType: '0',\r\n  /** 有效日期开始 effe_type=0 时需要设置 */\r\n  effeBeginDate: '',\r\n  /** 有效日期结束 effe_type=0 时需要设置 */\r\n  effeEndDate: '',\r\n  /** 第N日开始 动态有效时，从下发第N日开始 */\r\n  nstart: 0,\r\n  /** X天内有效 动态有效时，到X天内有效 */\r\n  xday: 365,\r\n  /** 是否启动 0:否 1：是 */\r\n  isEnable: '1',\r\n  /** 一个订单可用张数 当为代金券时才有值，-1表示不限制 */\r\n  numOrder: 1,\r\n  /** 使用须知 */\r\n  intro: '',\r\n  /** 状态 */\r\n  state: '1',\r\n  /** 适用门店列表 */\r\n  couponTemplateMerchants: [],\r\n})\r\nconst formRules = ref<FormRules>({\r\n  title: [{ required: true, message: t('pleaseEnterTitle'), trigger: 'blur' }],\r\n})\r\n\r\nconst rts = ref<{ rtCode: string; rtName: string }[]>([])\r\nconst channelList = ref<{ channelCode: string; channelName: string }[]>([])\r\nconst merchants = ref<{ hcode: string; hname: string }[]>([])\r\n\r\n// 根据入账方式计算子类型选项\r\nconst subCodeOptions = computed(() => {\r\n  if (form.value.accMode === '1') {\r\n    // 增加一笔付款\r\n    return [\r\n      { label: t('coupon'), value: 'coupon' }\r\n    ]\r\n  } else if (form.value.accMode === '2') {\r\n    // 抵扣一笔消费\r\n    return [\r\n      { label: t('couponDeduction'), value: 'coupon_deduction' },\r\n      { label: t('adjustRoomFee'), value: 'adjust_room_fee' }\r\n    ]\r\n  }\r\n  return []\r\n})\r\n\r\n// 监听入账方式变化，自动设置默认的subCode（仅在用户主动切换时）\r\nwatch(() => form.value.accMode, (newAccMode, oldAccMode) => {\r\n  // 只有在用户主动切换时才重置subCode，避免覆盖从后端获取的数据\r\n  if (oldAccMode !== undefined) {\r\n    if (newAccMode === '1') {\r\n      form.value.subCode = 'coupon'\r\n    } else if (newAccMode === '2') {\r\n      form.value.subCode = 'coupon_deduction'\r\n    }\r\n  }\r\n})\r\n\r\nonMounted(() => {\r\n  getRts()\r\n  getChannels()\r\n  getMerchants()\r\n  getConstants()\r\n})\r\n\r\n// 赠送券列表\r\nconst couponTypeList = ref<{ code: string; label: string }[]>([])\r\n// 优惠方式\r\nconst discountTypeList = ref<{ code: string; label: string }[]>([])\r\n// 常量里包括多个\r\nconst dictTypes = [DictTypeEnum.COUPON_TYPE, DictTypeEnum.PROMOTION_DISCOUNT_TYPE]\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    couponTypeList.value = res.data.filter((item) => item.dictType === DictTypeEnum.COUPON_TYPE)\r\n    discountTypeList.value = res.data.filter((item) => item.dictType === DictTypeEnum.PROMOTION_DISCOUNT_TYPE)\r\n  })\r\n}\r\n\r\nfunction getRts() {\r\n  form.value.rtCodes = []\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isVirtual: '0',\r\n    isGRt: form.value.rtType,\r\n    isEnable: '1',\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n// 适用渠道\r\nfunction getChannels() {\r\n  channelApi\r\n    .getChannelSimpleList({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      isEnable: BooleanEnum.YES,\r\n    })\r\n    .then((res: any) => {\r\n      channelList.value = res.data\r\n    })\r\n}\r\n\r\n// 适用门店\r\nfunction getMerchants() {\r\n  merchantApi.getSimpleList(userStore.gcode).then((res: any) => {\r\n    merchants.value = res.data\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      if (form.value.templateCode === '') {\r\n        formRef.value &&\r\n          formRef.value.validate((valid) => {\r\n            if (valid) {\r\n              couponTemplateApi.create(form.value).then((res: any) => {\r\n                if (res.code === 0) {\r\n                  ElMessage.success({\r\n                    message: t('addSuccess'),\r\n                    center: true,\r\n                  })\r\n                  resolve()\r\n                } else {\r\n                  ElMessage.error({\r\n                    message: res.msg,\r\n                    center: true,\r\n                  })\r\n                }\r\n              })\r\n            }\r\n          })\r\n      }\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"180px\" label-suffix=\"：\">\r\n      <el-divider content-position=\"left\">\r\n        {{ t('basicInformation') }}\r\n      </el-divider>\r\n      <el-form-item :label=\"t('couponType')\">\r\n        <el-radio-group v-model=\"form.couponType\">\r\n          <el-radio-button v-for=\"item in couponTypeList\" :key=\"item.code\" :value=\"item.code\" :disabled=\"item.code === 'breakfast'\">\r\n            {{ item.label }}\r\n          </el-radio-button>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('couponName')\" prop=\"templateName\">\r\n        <el-input v-model=\"form.templateName\" :placeholder=\"t('pleaseEnterCouponName')\" style=\"width: 293px\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <div v-if=\"form.couponType !== 'breakfast'\">\r\n        <el-form-item v-if=\"form.couponType === 'voucher'\" :label=\"t('discountMethod')\" prop=\"money\" label-width=\"140px\">\r\n          <el-input-number v-model=\"form.money\" :min=\"0\" :placeholder=\"t('pleaseEnterAmount')\" controls-position=\"right\" :precision=\"2\" style=\"margin-right: 8px\" />\r\n          {{ t('yuan') }}\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.couponType === 'discount'\" :label=\"t('discount')\" prop=\"rebate\">\r\n          <el-input-number v-model=\"form.rebate\" :min=\"0\" :max=\"1\" :placeholder=\"t('discountPlaceholder')\" controls-position=\"right\" :precision=\"2\" :step=\"0.01\" style=\"margin-right: 8px\" />\r\n          {{ t('discountUnit') }}\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('usageThreshold')\" prop=\"consume\">\r\n          {{ t('spendOver') }}\r\n          <el-input-number v-model=\"form.consume\" class=\"mx-4\" :min=\"0\" :placeholder=\"t('pleaseEnterThresholdAmount')\" controls-position=\"right\" :precision=\"2\" style=\"margin: 0 8px\" />\r\n          {{ t('yuan') }}\r\n        </el-form-item>\r\n      </div>\r\n      <div v-else>\r\n        <el-form-item :label=\"t('couponNumberSetting')\" prop=\"state\">\r\n          <el-checkbox v-model=\"form.state\" true-value=\"1\" false-value=\"0\" :label=\"t('useNumericCouponNumber')\" />\r\n        </el-form-item>\r\n      </div>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('financialInformation') }}\r\n      </el-divider>\r\n      <el-form-item :label=\"t('accountingMethod')\">\r\n        <el-select v-model=\"form.accMode\" style=\"width: 200px; margin-right: 8px\" placeholder=\"请选择入账方式\">\r\n          <el-option :label=\"t('addPayment')\" value=\"1\" />\r\n          <el-option :label=\"t('deductConsumption')\" value=\"2\" />\r\n        </el-select>\r\n        <el-select v-model=\"form.subCode\" style=\"width: 160px\">\r\n          <el-option v-for=\"option in subCodeOptions\" :key=\"option.value\" :label=\"option.label\" :value=\"option.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('costType')\">\r\n        <el-radio-group v-model=\"form.costType\">\r\n          <el-radio-button value=\"1\">\r\n            {{ t('store') }}\r\n          </el-radio-button>\r\n          <el-radio-button value=\"0\">\r\n            {{ t('group') }}\r\n          </el-radio-button>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('applicableScope') }}\r\n      </el-divider>\r\n      <el-form-item :label=\"t('applicableStores')\">\r\n        <el-select v-model=\"form.couponTemplateMerchants\" multiple collapse-tags clearable collapse-tags-tooltip :placeholder=\"t('pleaseSelectApplicableStores')\">\r\n          <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.hname\" :value=\"item.hcode\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <div v-if=\"form.couponType !== 'breakfast'\">\r\n        <el-form-item :label=\"t('applicableChannels')\">\r\n          <el-select v-model=\"form.channels\" multiple collapse-tags clearable collapse-tags-tooltip :placeholder=\"t('pleaseSelectApplicableChannels')\">\r\n            <el-option v-for=\"item in channelList\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('applicableRoomTypes')\">\r\n          <el-select v-model=\"form.rtType\" style=\"width: 200px\" @change=\"getRts()\">\r\n            <el-option :label=\"t('storeRoomType')\" value=\"0\" />\r\n            <el-option :label=\"t('groupRoomType')\" value=\"1\" />\r\n          </el-select>\r\n          <el-select v-model=\"form.rtCodes\" multiple collapse-tags clearable collapse-tags-tooltip :placeholder=\"t('pleaseSelectApplicableRoomTypes')\" style=\"width: 250px\">\r\n            <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('checkinType')\">\r\n          <el-radio-group v-model=\"form.checkinType\">\r\n            <el-radio-button value=\"all_day\">\r\n              {{ t('allDayRoom') }}\r\n            </el-radio-button>\r\n            <el-radio-button value=\"hour_room\">\r\n              {{ t('hourlyRoom') }}\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('validityPeriod')\">\r\n          <el-select v-model=\"form.effeType\" style=\"width: 200px; margin-right: 8px\">\r\n            <el-option :label=\"t('fixedValidity')\" value=\"0\" />\r\n            <el-option :label=\"t('permanentValidity')\" value=\"1\" />\r\n            <el-option :label=\"t('dynamicValidity')\" value=\"2\" />\r\n          </el-select>\r\n          <div v-if=\"form.effeType === '0'\">\r\n            <el-date-picker v-model=\"form.effeBeginDate\" type=\"date\" :placeholder=\"t('startDate')\" style=\"width: 150px\" />&nbsp;-&nbsp;\r\n            <el-date-picker v-model=\"form.effeEndDate\" type=\"date\" :placeholder=\"t('endDate')\" style=\"width: 150px\" />\r\n          </div>\r\n          <div v-if=\"form.effeType === '2'\">\r\n            {{ t('startFromDay') }}\r\n            <el-input-number v-model=\"form.nstart\" :min=\"0\" :step=\"1\" :step-strictly=\"true\" controls-position=\"right\" style=\"width: 100px; margin: 0 5px\" />\r\n            {{ t('dayStart') }}\r\n            <el-input-number v-model=\"form.xday\" :min=\"0\" :step=\"1\" :step-strictly=\"true\" controls-position=\"right\" style=\"width: 100px; margin: 0 5px\" />\r\n            {{ t('validWithinDays') }}\r\n          </div>\r\n          <div class=\"el-form-item-msg\">\r\n            {{ t('validityExcludesEndDate') }}\r\n          </div>\r\n        </el-form-item>\r\n      </div>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('other') }}\r\n      </el-divider>\r\n      <el-form-item v-if=\"form.couponType !== 'breakfast'\" :label=\"t('usageLimit')\">\r\n        {{ t('numberAllowedPerOrder') }}\r\n        <el-input-number v-model=\"form.numOrder\" :min=\"0\" :step=\"1\" :step-strictly=\"true\" controls-position=\"right\" style=\"width: 100px; margin: 0 5px\" />\r\n        {{ t('unitPiece') }}\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('enabled')\">\r\n        <el-switch v-model=\"form.state\" :active-text=\"t('enable')\" :inactive-text=\"t('disable')\" inline-prompt active-value=\"1\" inactive-value=\"0\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('instructions')\">\r\n        <el-input v-model=\"form.intro\" type=\"textarea\" :rows=\"4\" :placeholder=\"t('pleaseEnterInstructions')\" maxlength=\"250\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-form-item-msg {\r\n  width: 100%;\r\n  clear: both;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "templateCode", "gcode", "hcode", "hname", "templateName", "couponType", "consume", "money", "rebate", "accMode", "subCode", "costType", "cost", "scope", "rts", "channels", "rtCodes", "rtType", "isG", "checkinType", "effeType", "effeBeginDate", "effeEndDate", "nstart", "xday", "isEnable", "numOrder", "intro", "state", "couponTemplateMerchants", "formRules", "title", "required", "message", "trigger", "channelList", "merchants", "subCodeOptions", "computed", "value", "label", "watch", "newAccMode", "oldAccMode", "onMounted", "getRts", "channelApi", "getChannelSimpleList", "BooleanEnum", "YES", "then", "res", "data", "merchantApi", "getSimpleList", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "couponTypeList", "filter", "item", "dictType", "DictTypeEnum", "COUPON_TYPE", "discountTypeList", "PROMOTION_DISCOUNT_TYPE", "params", "isVirtual", "isGRt", "rtApi", "getRoomTypeSimpleList", "code", "__expose", "submit", "Promise", "resolve", "validate", "valid", "couponTemplateApi", "create", "ElMessage", "success", "center", "error", "msg"], "mappings": "i3CAmMA,MAAMA,EAAQC,GAIRC,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,aAAcV,EAAMU,aAEpBC,MAAOP,EAAUO,MAEjBC,MAAOR,EAAUQ,MACjBC,MAAO,GAEPC,aAAc,GAEdC,WAAY,GAEZC,QAAS,EAETC,MAAO,EAEPC,OAAQ,EAERC,QAAS,IAETC,QAAS,SAETC,SAAU,IAEVC,KAAM,KAENC,MAAO,CACLC,IAAK,GACLC,SAAU,IAGZC,QAAS,GAETC,OAAQ,IAERF,SAAU,GAEVG,IAAK,IAELC,YAAa,UAEbC,SAAU,IAEVC,cAAe,GAEfC,YAAa,GAEbC,OAAQ,EAERC,KAAM,IAENC,SAAU,IAEVC,SAAU,EAEVC,MAAO,GAEPC,MAAO,IAEPC,wBAAyB,KAErBC,GAAYjC,EAAe,CAC/BkC,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAASzC,EAAE,oBAAqB0C,QAAS,WAG/DpB,GAAMjB,EAA0C,IAChDsC,GAActC,EAAoD,IAClEuC,GAAYvC,EAAwC,IAGpDwC,GAAiBC,GAAS,IACH,MAAvBvC,EAAKwC,MAAM9B,QAEN,CACL,CAAE+B,MAAOhD,EAAE,UAAW+C,MAAO,WAEC,MAAvBxC,EAAKwC,MAAM9B,QAEb,CACL,CAAE+B,MAAOhD,EAAE,mBAAoB+C,MAAO,oBACtC,CAAEC,MAAOhD,EAAE,iBAAkB+C,MAAO,oBAGjC,KAITE,GAAM,IAAM1C,EAAKwC,MAAM9B,UAAS,CAACiC,EAAYC,UAExB,IAAfA,IACiB,MAAfD,EACF3C,EAAKwC,MAAM7B,QAAU,SACG,MAAfgC,IACT3C,EAAKwC,MAAM7B,QAAU,oBACvB,IAIJkC,GAAU,KACDC,KAqCPC,EACGC,qBAAqB,CACpB9C,MAAOP,EAAUO,MACjBC,MAAOR,EAAUQ,MACjBuB,SAAUuB,EAAYC,MAEvBC,MAAMC,IACLhB,GAAYI,MAAQY,EAAIC,IAAA,IAM5BC,EAAYC,cAAc5D,EAAUO,OAAOiD,MAAMC,IAC/Cf,GAAUG,MAAQY,EAAIC,IAAA,IAtCxBG,EAAYC,iBAAiBC,IAAWP,MAAMC,IAC7BO,GAAAnB,MAAQY,EAAIC,KAAKO,QAAQC,GAASA,EAAKC,WAAaC,EAAaC,cAC/DC,GAAAzB,MAAQY,EAAIC,KAAKO,QAAQC,GAASA,EAAKC,WAAaC,EAAaG,yBAAuB,GAZ9F,IAIT,MAAAP,GAAiB7D,EAAuC,IAExDmE,GAAmBnE,EAAuC,IAE1D4D,GAAY,CAACK,EAAaC,YAAaD,EAAaG,yBAQ1D,SAASpB,KACF9C,EAAAwC,MAAMvB,QAAU,GACrB,MAAMkD,EAAS,CACbjE,MAAOP,EAAUO,MACjBC,MAAOR,EAAUQ,MACjBiE,UAAW,IACXC,MAAOrE,EAAKwC,MAAMtB,OAClBQ,SAAU,KAEZ4C,EAAMC,sBAAsBJ,GAAQhB,MAAMC,IACvB,IAAbA,EAAIoB,OACNzD,GAAIyB,MAAQY,EAAIC,KAAA,GAEnB,QAuBUoB,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACQ,KAA5B5E,EAAKwC,MAAMvC,cACbF,EAAQyC,OACNzC,EAAQyC,MAAMqC,UAAUC,IAClBA,GACFC,EAAkBC,OAAOhF,EAAKwC,OAAOW,MAAMC,IACxB,IAAbA,EAAIoB,MACNS,EAAUC,QAAQ,CAChBhD,QAASzC,EAAE,cACX0F,QAAQ,IAEFP,KAERK,EAAUG,MAAM,CACdlD,QAASkB,EAAIiC,IACbF,QAAQ,GACT,GAEJ,GAEJ"}