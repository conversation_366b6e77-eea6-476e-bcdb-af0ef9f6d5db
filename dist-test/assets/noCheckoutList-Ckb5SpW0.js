import{d as t,aj as e,ai as o,b as i,y as s,b3 as a,o as r,c as l,f as n,w as c,u as m,g as p,F as u,ag as d,e as b,b6 as h,h as j,Y as g,aq as y,av as f,R as k,i as _,a7 as v,j as C,k as T,m as w,q as N,aS as x,b5 as S,x as P,t as M,bz as O,v as L,bv as D,bt as R,aT as V}from"./index-CkEhI1Zk.js";/* empty css                   */import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-DK1kOKr4.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";import{_ as H}from"./index.vue_vue_type_script_setup_true_lang-DvSmdj52.js";/* empty css                *//* empty css                  *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";import{c as z}from"./channel.api-CM6FWEgD.js";import{o as G}from"./order.api-B-JCVvq6.js";import{r as I}from"./rt.api-5a8-At7-.js";import{d as U}from"./dictData.api-DUabpYqy.js";import{B as W,r as q,q as B,N as F,O as A}from"./constants-Cg3j_uH4.js";import K from"./order-ChSzi_-7.js";import Y from"./index-M2JMYKA8.js";import{u as $}from"./usePagination-DYjsSSf4.js";import{_ as Q}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                  *//* empty css                   *//* empty css                         */import"./index-i2MX-1er.js";/* empty css                    */import"./account-Dw8d3GK0.js";import"./index-3RMLzyhA.js";import"./index-ADu0XAHG.js";/* empty css                        *//* empty css                          *//* empty css                 *//* empty css                       *//* empty css                          *//* empty css                       *//* empty css                        *//* empty css                *//* empty css               *//* empty css               *//* empty css                        *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-CJlU19fC.js";import"./account.api-CSMEUacF.js";import"./rentGoods.api-IR0dWMfk.js";import"./orderBill-CgM-5HXN.js";/* empty css                 */import"./print.api-DEqPqKcI.js";import"./printForm.api-BOg76CjP.js";import"./posBillForm-Cigy56-i.js";import"./togetherBill-ByLEtbff.js";import"./index-BqUz2moK.js";import"./generalConfig.api-CEBBd8kx.js";import"./index-Dlhx8lGo.js";import"./index-K7z-WsFs.js";/* empty css                */import"./couponConfig.api-DtISSIXY.js";import"./member.api-2tU9HGvl.js";import"./arSet.api-BZHDDSla.js";import"./hotelParamConfig.api-CbdvhUfn.js";import"./auth.api-C96jzWEY.js";import"./CouponDialog-DTl141Zt.js";import"./coupon.api-aMKZ7FC4.js";/* empty css                       */import"./index-B36WBY8p.js";import"./index-C6K_fo9Y.js";import"./index-_28SpMym.js";/* empty css                   */import"./index-DnGZTrHX.js";import"./list-cSBPeYXE.js";/* empty css                      */import"./index-Cjr3dIX4.js";import"./detail-Dh370UMq.js";import"./rentCompensation-DGZqTwko.js";/* empty css                   */import"./rentCreate-DgokBdtt.js";import"./rent.api-DzgTHAr9.js";import"./rentEdit-kpM-6Ev1.js";import"./rentReturn-Bn7G8O-o.js";import"./goodsModal-DNVgoATn.js";import"./index-DAulSAJI.js";import"./index-D8c6PuWt.js";/* empty css                */import"./index-CDbn0nBx.js";import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";import"./decimal-gPLAeiS8.js";import"./grantModal-S8hNNB6E.js";import"./invoicedModal-XEk1BZXP.js";import"./remark-D99iiFr3.js";import"./splitAccount-DpqmmImE.js";import"./consume-DgDuQkgE.js";import"./indemnityGoods.api-BzuE6zcC.js";import"./retailGoods.api-CPINo1es.js";import"./payment-vLdXRLoR.js";import"./checkinForm-DTcWmPmJ.js";import"./posCheckInForm-BJfHKK6-.js";import"./preAuth-CG1cg58P.js";import"./bookingDetail-BY3bduLn.js";import"./book.api-ERXvEXQF.js";import"./serverTime.api-D89oCqKL.js";import"./timeutils-Ib6GkGcq.js";import"./cancelPopUP-BbPXaQdi.js";import"./arrangeRooms-CPfs5GXR.js";import"./index-CkWKDwTG.js";import"./index-Eu7Cs0xe.js";import"./checkModal-tyH9Ceqi.js";/* empty css                        */import"./DictTypeEnum-DKIIlHnN.js";import"./CardReader-BeR26SIt.js";import"./myStringUtil-D79vpWmP.js";import"./sensitive-la8iBfdn.js";import"./log-BF-F0S6C.js";import"./roomLog.api-D1y-EVTQ.js";import"./user.api-BYl7ypOS.js";import"./orderDetail-B1c5B-Jp.js";import"./customer.api-dB3m63zZ.js";import"./device.api-BsgckoMw.js";import"./roomCardLog.api-pw0J1hl7.js";import"./roomCardUtil-DBQw7z7m.js";import"./index-CYIY_xy7.js";import"./utils-S8-xpbSs.js";import"./index-DcUuNG9v.js";import"./route-block-B_A1xBdJ.js";import"./roomContinue-Cwa93jZh.js";/* empty css                       */import"./roomExchange-DyqICf4D.js";import"./arrangeRooms-DLQ6Ij2m.js";import"./roomCardLogList-DIxcpNbT.js";import"./orderList-DtQU5x9j.js";import"./mergeForm-C0XQeWLX.js";import"./teamBookDetail-CaYBewxN.js";import"./arrangeRts-C83hWsgy.js";import"./GeneralConfigTypeEnum-DERqowgl.js";import"./teamMainOrder-RmJelicD.js";import"./teamReception-BVmeD-Jb.js";const X={class:"filter-row"},J={class:"filter-row"},Z={class:"switch-container"},tt={class:"expand-content"},et={key:1,class:"no-data"},ot={class:"guest-info"},it={key:0},st={class:"order-info"},at={class:"label"},rt={key:0},lt={class:"label"},nt={class:"room-info"},ct={class:"room-type"},mt={class:"price"},pt={class:"time-info"},ut={class:"label"},dt={class:"label"},bt=t({name:"OrderInHandNoCheckoutList",__name:"noCheckoutList",setup(t){const{t:Q}=e(),bt=o(),{pagination:ht,getParams:jt,onSizeChange:gt,onCurrentChange:yt,onSortChange:ft}=$(),kt=i({loading:!1,tableAutoHeight:!1,formMode:"dialog",formModeProps:{visible:!1,no:"",orderTogetherCode:""},search:{channelCode:"-1",rtCode:"",guestSrcType:"",checkinType:"",searchType:"0",searchContent:"",displayWay:"0"},dataList:[]});s((()=>{!function(){const t={gcode:bt.gcode,hcode:bt.hcode,isEnable:W.YES};z.getChannelSimpleList(t).then((t=>{0===t.code&&(_t.value=t.data)}))}(),function(){const t={gcode:bt.gcode,hcode:bt.hcode,isVirtual:W.NO,isEnable:W.YES};I.getRoomTypeSimpleList(t).then((t=>{0===t.code&&(vt.value=t.data)}))}(),U.getDictDataBatch(Ct).then((t=>{Tt.value=t.data.filter((t=>t.dictType===q)),wt.value=t.data.filter((t=>t.dictType===B))})),Nt()}));const _t=i([]);const vt=i([]);const Ct=[q,B],Tt=i([]),wt=i([]);function Nt(){kt.value.loading=!0;const t={...jt(),gcode:bt.gcode,hcode:bt.hcode,keyWords:kt.value.search.searchContent,channelCode:"-1"===kt.value.search.channelCode?"":kt.value.search.channelCode,guestSrcType:kt.value.search.guestSrcType,rtCode:kt.value.search.rtCode,checkinType:kt.value.search.checkinType,displayWay:kt.value.search.displayWay};G.notLeaveList(t).then((t=>{kt.value.loading=!1,t.data.list&&(kt.value.dataList=t.data.list,ht.value.total=t.data.total)}))}function xt(t){gt(t).then((()=>Nt()))}function St(t=1){yt(t).then((()=>Nt()))}function Pt({prop:t,order:e}){ft(t,e).then((()=>Nt()))}const Mt=i(!1),Ot=i("detail"),Lt=i("individual");const Dt=i(!1),Rt=i("");function Vt(t){switch(t){case A.CHECK_IN:return"success";case A.CHECK_OUT:return"info";case A.IN_BOOKING:return"warning";case A.CANCEL:case A.REFUSE:case A.NOSHOW:return"danger";case A.OVER:case A.BE_CONFIRM:return"";case A.CREDIT:case A.CONTINUE:return"warning";default:return""}}return(t,e)=>{const o=C,i=T,s=w,z=N,G=x,I=S,U=P,W=H,q=M,B=O,A=L,$=D,bt=R,jt=E,gt=a("auth"),yt=V;return r(),l("div",{class:v({"absolute-container":m(kt).tableAutoHeight})},[n(jt,null,{default:c((()=>[n(W,{"show-toggle":!1},{default:c((()=>[n(U,{model:m(kt).search,size:"default","label-width":"80px","inline-message":"",inline:"",class:"search-form"},{default:c((()=>[p("div",X,[n(s,{label:m(Q)("channels")},{default:c((()=>[n(i,{modelValue:m(kt).search.channelCode,"onUpdate:modelValue":e[0]||(e[0]=t=>m(kt).search.channelCode=t),clearable:"",class:"filter-select"},{default:c((()=>[n(o,{label:m(Q)("all"),value:"-1"},null,8,["label"]),(r(!0),l(u,null,d(m(_t),(t=>(r(),b(o,{key:t.channelCode,label:t.channelName,value:t.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),n(s,{label:m(Q)("guestSource")},{default:c((()=>[n(i,{modelValue:m(kt).search.guestSrcType,"onUpdate:modelValue":e[1]||(e[1]=t=>m(kt).search.guestSrcType=t),clearable:"",class:"filter-select"},{default:c((()=>[(r(!0),l(u,null,d(m(Tt),(t=>(r(),b(o,{key:t.code,label:t.label,value:t.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),n(s,{label:m(Q)("roomType")},{default:c((()=>[n(i,{modelValue:m(kt).search.rtCode,"onUpdate:modelValue":e[2]||(e[2]=t=>m(kt).search.rtCode=t),clearable:"",class:"filter-select"},{default:c((()=>[(r(!0),l(u,null,d(m(vt),(t=>(r(),b(o,{key:t.rtCode,label:t.rtName,value:t.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])]),p("div",J,[n(s,{label:m(Q)("checkinType")},{default:c((()=>[n(i,{modelValue:m(kt).search.checkinType,"onUpdate:modelValue":e[3]||(e[3]=t=>m(kt).search.checkinType=t),clearable:"",class:"filter-select"},{default:c((()=>[(r(!0),l(u,null,d(m(wt),(t=>(r(),b(o,{key:t.code,label:t.label,value:t.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"]),n(s,{label:m(Q)("search"),class:"search-input-item"},{default:c((()=>[n(G,{modelValue:m(kt).search.searchContent,"onUpdate:modelValue":e[5]||(e[5]=t=>m(kt).search.searchContent=t),class:"filter-select w-350px!",clearable:"",placeholder:m(Q)("Name/Phone/Room")},{append:c((()=>[n(z,{icon:m(h),onClick:e[4]||(e[4]=t=>St())},null,8,["icon"])])),_:1},8,["modelValue","placeholder"]),n(z,{type:"primary",class:"query-button",onClick:Nt},{default:c((()=>[j(g(m(Q)("query")),1)])),_:1}),p("div",Z,[n(I,{modelValue:m(kt).search.displayWay,"onUpdate:modelValue":e[6]||(e[6]=t=>m(kt).search.displayWay=t),"active-value":"1","inactive-value":"0","active-text":m(Q)("guestMode"),"inactive-text":m(Q)("orderMode"),"inline-prompt":"",class:"display-way-switch",style:{"--el-switch-on-color":"#13ce66","--el-switch-off-color":"#554dd6"}},null,8,["modelValue","active-text","inactive-text"])])])),_:1},8,["label"])])])),_:1},8,["model"])])),_:1}),y((r(),b(A,{class:"list-table","header-cell-style":{background:"#f5f7fa",color:"#606266"},data:m(kt).dataList,stripe:"","highlight-current-row":"",border:"",height:"100%",onSortChange:Pt},{default:c((()=>["0"===m(kt).search.displayWay?(r(),b(q,{key:0,type:"expand",width:"50"},{default:c((({row:t})=>[p("div",tt,[t.togetherList&&t.togetherList.length>0?(r(),b(A,{key:0,data:t.togetherList,class:"expand-table",border:""},{default:c((()=>[n(q,{prop:"name",label:m(Q)("guestName")},null,8,["label"]),n(q,{prop:"phone",label:m(Q)("guestPhone")},null,8,["label"]),n(q,{label:m(Q)("guestSex")},{default:c((t=>[n(B,{type:"1"===t.row.sex?"primary":"danger",size:"small"},{default:c((()=>[j(g("1"===t.row.sex?m(Q)("male"):"0"===t.row.sex?m(Q)("female"):m(Q)("unknown")),1)])),_:2},1032,["type"])])),_:2},1032,["label"]),n(q,{label:m(Q)("mainOrderStatus")},{default:c((e=>[n(B,{type:"1"===e.row.isMain||1===t.togetherList.length?"success":"info",size:"small"},{default:c((()=>[j(g("1"===e.row.isMain||1===t.togetherList.length?m(Q)("mainGuest"):m(Q)("companion")),1)])),_:2},1032,["type"])])),_:2},1032,["label"]),n(q,{label:m(Q)("checkinTime")},{default:c((t=>[j(g(t.row.checkinTime?m(f)(t.row.checkinTime).format("MM/DD HH:mm"):""),1)])),_:2},1032,["label"]),n(q,{label:m(Q)("checkoutTime")},{default:c((t=>[j(g(t.row.checkoutTime?m(f)(t.row.checkoutTime).format("MM/DD HH:mm"):""),1)])),_:2},1032,["label"]),n(q,{label:m(Q)("planCheckoutTime")},{default:c((t=>[j(g(t.row.planCheckoutTime?m(f)(t.row.planCheckoutTime).format("MM/DD HH:mm"):""),1)])),_:2},1032,["label"]),n(q,{label:m(Q)("status"),width:"100"},{default:c((t=>[n(B,{type:Vt(t.row.state),size:"small"},{default:c((()=>{return[j(g((e=t.row.state,Q(`orderStatus.${e}`)||e)),1)];var e})),_:2},1032,["type"])])),_:2},1032,["label"])])),_:2},1032,["data"])):(r(),l("div",et,g(m(Q)("noData")),1))])])),_:1})):k("",!0),n(q,{label:m(Q)("namePhone"),"min-width":"155"},{default:c((t=>[p("div",ot,[p("div",null,g(t.row.name),1),t.row.phone?(r(),l("div",it,g(t.row.phone),1)):k("",!0)])])),_:1},8,["label"]),n(q,{width:"190",label:m(Q)("orderNo")},{default:c((t=>[p("div",st,[p("div",null,[p("span",at,g(m(Q)("orderNumber")),1),j(g(t.row.orderNo),1)]),t.row.outOrderNo?(r(),l("div",rt,[p("span",lt,g(m(Q)("externalOrderNumber")),1),j(g(t.row.outOrderNo),1)])):k("",!0)])])),_:1},8,["label"]),n(q,{label:m(Q)("roomTypePrice"),"min-width":"180"},{default:c((t=>[p("div",nt,[p("span",ct,g(t.row.rtName),1),p("span",mt,g(m(Q)("currencySymbol"))+g(t.row.price),1)])])),_:1},8,["label"]),n(q,{prop:"rNo",width:"80",label:m(Q)("roomNumber")},null,8,["label"]),n(q,{label:m(Q)("checkinTime"),"min-width":"170"},{default:c((t=>[p("div",pt,[p("span",null,[p("span",ut,g(m(Q)("checkinLabel")),1),j(g(m(f)(t.row.checkinTime).format("MM/DD HH:mm")),1)]),p("span",null,[p("span",dt,g(m(Q)("checkoutLabel")),1),j(g(m(f)(t.row.planCheckoutTime).format("MM/DD HH:mm")),1)])])])),_:1},8,["label"]),n(q,{prop:"checkinTypeName",width:"130px",label:m(Q)("checkinType")},null,8,["label"]),n(q,{prop:"guestSrcTypeName",label:m(Q)("guestSource")},null,8,["label"]),n(q,{prop:"channelName",width:"90",label:m(Q)("channels")},null,8,["label"]),n(q,{label:m(Q)("operations"),width:"160",align:"center",fixed:"right"},{default:c((t=>[y((r(),b($,{type:"primary",onClick:e=>{return o=t.row,kt.value.formModeProps.no=o.orderNo,void(Mt.value=!0);var o}},{default:c((()=>[j(g(m(Q)("view")),1)])),_:2},1032,["onClick"])),[[gt,"pms:order:query:get-order-detail"]]),y((r(),b($,{type:"primary",onClick:e=>function(t,e){kt.value.formModeProps.no=e.orderNo;let o=e.togetherCode;if(e.togetherList&&e.togetherList.length>0){const t=e.togetherList.find((t=>"1"===t.isMain));t&&t.togetherCode&&(o=t.togetherCode)}kt.value.formModeProps.orderTogetherCode=o,Rt.value=t,Dt.value=!0}("pay",t.row)},{default:c((()=>[j(g(m(Q)("entry")),1)])),_:2},1032,["onClick"])),[[gt,"pms:account:create"]])])),_:1},8,["label"])])),_:1},8,["data"])),[[yt,m(kt).loading]]),m(ht).total>10?(r(),b(bt,{key:0,"current-page":m(ht).pageNo,total:m(ht).total,"page-size":m(ht).pageSize,"page-sizes":m(ht).sizes,layout:m(ht).layout,"hide-on-single-page":!1,class:"pagination",background:"",onSizeChange:xt,onCurrentChange:St},null,8,["current-page","total","page-size","page-sizes","layout"])):k("",!0),m(Mt)?(r(),b(K,{key:1,modelValue:m(Mt),"onUpdate:modelValue":e[7]||(e[7]=t=>_(Mt)?Mt.value=t:null),"tab-name":m(Ot),no:m(kt).formModeProps.no,"no-type":m(F).ORDER,"tab-type":m(Lt),onReload:Nt},null,8,["modelValue","tab-name","no","no-type","tab-type"])):k("",!0),m(Dt)?(r(),b(Y,{key:2,modelValue:m(Dt),"onUpdate:modelValue":e[8]||(e[8]=t=>_(Dt)?Dt.value=t:null),"order-no":m(kt).formModeProps.no,"order-together-code":m(kt).formModeProps.orderTogetherCode,"order-type":m(F).ORDER,"no-type":m(F).ORDER,"tab-name":m(Rt)},null,8,["modelValue","order-no","order-together-code","order-type","no-type","tab-name"])):k("",!0)])),_:1})],2)}}});function ht(t){const e=t;e.__i18n=e.__i18n||[],e.__i18n.push({locale:"",resource:{en:{channels:{t:0,b:{t:2,i:[{t:3}],s:"Channels"}},search:{t:0,b:{t:2,i:[{t:3}],s:"Search"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source"}},namePhone:{t:0,b:{t:2,i:[{t:3}],s:"Name/Phone"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"Check-in"}},orderNo:{t:0,b:{t:2,i:[{t:3}],s:"Order No"}},externalOrderNo:{t:0,b:{t:2,i:[{t:3}],s:"External Order No"}},orderNumber:{t:0,b:{t:2,i:[{t:3}],s:"Order No:"}},externalOrderNumber:{t:0,b:{t:2,i:[{t:3}],s:"External Order No:"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"Room"}},price:{t:0,b:{t:2,i:[{t:3}],s:"Price"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Time"}},plannedCheckout:{t:0,b:{t:2,i:[{t:3}],s:"Pre Checkout"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"Actions"}},view:{t:0,b:{t:2,i:[{t:3}],s:"View"}},entry:{t:0,b:{t:2,i:[{t:3}],s:"Post"}},all:{t:0,b:{t:2,i:[{t:3}],s:"All"}},query:{t:0,b:{t:2,i:[{t:3}],s:"Filter"}},"Name/Phone/Room":{t:0,b:{t:2,i:[{t:3}],s:"OrderNo、Name、Phone、Room"}},select:{t:0,b:{t:2,i:[{t:3}],s:"select"}},roomTypePrice:{t:0,b:{t:2,i:[{t:3}],s:"Room Type/Price"}},inOutTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Time/Pre Checkout"}},guestLabel:{t:0,b:{t:2,i:[{t:3}],s:"Guest:"}},phoneLabel:{t:0,b:{t:2,i:[{t:3}],s:"Phone:"}},checkinLabel:{t:0,b:{t:2,i:[{t:3}],s:"Check-in:"}},checkoutLabel:{t:0,b:{t:2,i:[{t:3}],s:"Pre Checkout:"}},currencySymbol:{t:0,b:{t:2,i:[{t:3}],s:"$"}},displayWay:{t:0,b:{t:2,i:[{t:3}],s:"Display Mode"}},orderMode:{t:0,b:{t:2,i:[{t:3}],s:"Order Mode"}},guestMode:{t:0,b:{t:2,i:[{t:3}],s:"Guest Mode"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"Guest Information"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"Phone"}},guestSex:{t:0,b:{t:2,i:[{t:3}],s:"Gender"}},mainOrderStatus:{t:0,b:{t:2,i:[{t:3}],s:"Is Main Guest"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-out Time"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Planned Check-out Time"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"No Data"}},male:{t:0,b:{t:2,i:[{t:3}],s:"男"}},female:{t:0,b:{t:2,i:[{t:3}],s:"女"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"保密"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"是"}},no:{t:0,b:{t:2,i:[{t:3}],s:"否"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"主客"}},companion:{t:0,b:{t:2,i:[{t:3}],s:"同来"}},orderStatus:{no_check_in:{t:0,b:{t:2,i:[{t:3}],s:"预订中"}},check_in:{t:0,b:{t:2,i:[{t:3}],s:"已入住"}},check_out:{t:0,b:{t:2,i:[{t:3}],s:"已退房"}},noshow:{t:0,b:{t:2,i:[{t:3}],s:"未到店"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"已取消"}},be_confirm:{t:0,b:{t:2,i:[{t:3}],s:"待确认"}},refuse:{t:0,b:{t:2,i:[{t:3}],s:"已拒绝"}},over:{t:0,b:{t:2,i:[{t:3}],s:"已完成"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"挂账"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"续住"}}}},"zh-cn":{channels:{t:0,b:{t:2,i:[{t:3}],s:"渠道"}},search:{t:0,b:{t:2,i:[{t:3}],s:"精准搜索"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},namePhone:{t:0,b:{t:2,i:[{t:3}],s:"客人/电话"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},orderNo:{t:0,b:{t:2,i:[{t:3}],s:"订单号"}},externalOrderNo:{t:0,b:{t:2,i:[{t:3}],s:"外部订单号"}},orderNumber:{t:0,b:{t:2,i:[{t:3}],s:"订单号："}},externalOrderNumber:{t:0,b:{t:2,i:[{t:3}],s:"外部订单号："}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},price:{t:0,b:{t:2,i:[{t:3}],s:"房价"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"入住时间"}},plannedCheckout:{t:0,b:{t:2,i:[{t:3}],s:"预离"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},view:{t:0,b:{t:2,i:[{t:3}],s:"查看"}},entry:{t:0,b:{t:2,i:[{t:3}],s:"入账"}},all:{t:0,b:{t:2,i:[{t:3}],s:"全部"}},query:{t:0,b:{t:2,i:[{t:3}],s:"查询"}},"Name/Phone/Room":{t:0,b:{t:2,i:[{t:3}],s:"订单号、外部订单号、姓名、手机号、房号"}},select:{t:0,b:{t:2,i:[{t:3}],s:"选择"}},roomTypePrice:{t:0,b:{t:2,i:[{t:3}],s:"房型/房价"}},inOutTime:{t:0,b:{t:2,i:[{t:3}],s:"入住/预离"}},guestLabel:{t:0,b:{t:2,i:[{t:3}],s:"客人："}},phoneLabel:{t:0,b:{t:2,i:[{t:3}],s:"电话："}},checkinLabel:{t:0,b:{t:2,i:[{t:3}],s:"入住："}},checkoutLabel:{t:0,b:{t:2,i:[{t:3}],s:"预离："}},currencySymbol:{t:0,b:{t:2,i:[{t:3}],s:"￥"}},displayWay:{t:0,b:{t:2,i:[{t:3}],s:"显示方式"}},orderMode:{t:0,b:{t:2,i:[{t:3}],s:"订单模式"}},guestMode:{t:0,b:{t:2,i:[{t:3}],s:"客人模式"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"客人信息"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"姓名"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"电话"}},guestSex:{t:0,b:{t:2,i:[{t:3}],s:"性别"}},mainOrderStatus:{t:0,b:{t:2,i:[{t:3}],s:"是否主客"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"退房时间"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"预离时间"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"暂无数据"}},male:{t:0,b:{t:2,i:[{t:3}],s:"男"}},female:{t:0,b:{t:2,i:[{t:3}],s:"女"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"保密"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"是"}},no:{t:0,b:{t:2,i:[{t:3}],s:"否"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"主客"}},companion:{t:0,b:{t:2,i:[{t:3}],s:"同住"}},orderStatus:{no_check_in:{t:0,b:{t:2,i:[{t:3}],s:"预订中"}},check_in:{t:0,b:{t:2,i:[{t:3}],s:"已入住"}},check_out:{t:0,b:{t:2,i:[{t:3}],s:"已退房"}},noshow:{t:0,b:{t:2,i:[{t:3}],s:"未到店"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"已取消"}},be_confirm:{t:0,b:{t:2,i:[{t:3}],s:"待确认"}},refuse:{t:0,b:{t:2,i:[{t:3}],s:"已拒绝"}},over:{t:0,b:{t:2,i:[{t:3}],s:"已完成"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"挂账"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"续住"}}}},km:{channels:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},search:{t:0,b:{t:2,i:[{t:3}],s:"ស្វែងរក"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទភ្ញៀវ"}},namePhone:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវ/ទូរស័ព្ទ"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលស្នាក់"}},orderNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែ"}},externalOrderNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខខាងក្រៅ"}},orderNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែ៖"}},externalOrderNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែខាងក្រៅ៖"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់"}},price:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃ"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលចូលស្នាក់នៅ"}},plannedCheckout:{t:0,b:{t:2,i:[{t:3}],s:"គ្រោងចាកចេញ"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"សកម្មភាព"}},view:{t:0,b:{t:2,i:[{t:3}],s:"មើល"}},entry:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ចូល"}},all:{t:0,b:{t:2,i:[{t:3}],s:"ទាំងអស់"}},query:{t:0,b:{t:2,i:[{t:3}],s:"ស្វែងរក"}},"Name/Phone/Room":{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មងែ, ឈ្មោះ, ទូរស័ព្ទ, បន្ទប់"}},select:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើស"}},roomTypePrice:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់/តម្លៃ"}},inOutTime:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅ/គ្រោងចាកចេញ"}},guestLabel:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវ៖"}},phoneLabel:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទ៖"}},checkinLabel:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅ៖"}},checkoutLabel:{t:0,b:{t:2,i:[{t:3}],s:"គ្រោងចាកចេញ៖"}},currencySymbol:{t:0,b:{t:2,i:[{t:3}],s:"៛"}},displayWay:{t:0,b:{t:2,i:[{t:3}],s:"របៀបបង្ហាញ"}},orderMode:{t:0,b:{t:2,i:[{t:3}],s:"របៀបកម្មងែ"}},guestMode:{t:0,b:{t:2,i:[{t:3}],s:"របៀបភ្ញៀវ"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានភ្ញៀវ"}},guestName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទ"}},guestSex:{t:0,b:{t:2,i:[{t:3}],s:"ភេទ"}},mainOrderStatus:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាពកម្មងែសំខាន់"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលចេញ"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលចេញគ្រោងទុក"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},noData:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានទិន្នន័យ"}},male:{t:0,b:{t:2,i:[{t:3}],s:"ប្រុស"}},female:{t:0,b:{t:2,i:[{t:3}],s:"ស្រី"}},unknown:{t:0,b:{t:2,i:[{t:3}],s:"មិនស្គាល់"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"ប្រាក់"}},no:{t:0,b:{t:2,i:[{t:3}],s:"មិនប្រាក់"}},mainGuest:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវសំខាន់"}},companion:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវអមដំណើរ"}},orderStatus:{no_check_in:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងកក់"}},check_in:{t:0,b:{t:2,i:[{t:3}],s:"បានចូលស្នាក់នៅ"}},check_out:{t:0,b:{t:2,i:[{t:3}],s:"បានចាកចេញ"}},noshow:{t:0,b:{t:2,i:[{t:3}],s:"មិនបានមកដល់"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បានលុបចោល"}},be_confirm:{t:0,b:{t:2,i:[{t:3}],s:"រង់ចាំការបញ្ជាក់"}},refuse:{t:0,b:{t:2,i:[{t:3}],s:"បានបដិសេធ"}},over:{t:0,b:{t:2,i:[{t:3}],s:"បានបញ្ចប់"}},credit:{t:0,b:{t:2,i:[{t:3}],s:"ជំពាក់"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"បន្តស្នាក់នៅ"}}}}}})}ht(bt);const jt=Q(bt,[["__scopeId","data-v-1f4400ec"]]);export{jt as default};
//# sourceMappingURL=noCheckoutList-Ckb5SpW0.js.map
