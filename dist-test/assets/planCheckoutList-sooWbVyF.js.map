{"version": 3, "file": "planCheckoutList-sooWbVyF.js", "sources": ["../../src/views/order/inhand/planCheckoutList.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"channel\": \"Channel\",\r\n    \"search\": \"Precise Search\",\r\n    \"guestSourceType\": \"Guest Source\",\r\n    \"roomType\": \"Room Type\",\r\n    \"checkinType\": \"Check-in Type\",\r\n    \"query\": \"Filter\",\r\n    \"guestPhone\": \"Guest/Phone\",\r\n    \"orderNo\": \"Order No\",\r\n    \"roomTypeLabel\": \"Room Type\",\r\n    \"roomTypePrice\": \"Room Type/Price\",\r\n    \"roomNo\": \"Room No\",\r\n    \"price\": \"Price\",\r\n    \"checkinTime\": \"Check-in Time\",\r\n    \"guestSource\": \"Guest Source\",\r\n    \"channelLabel\": \"Channel\",\r\n    \"operation\": \"Actions\",\r\n    \"view\": \"View\",\r\n    \"entryAccount\": \"Post\",\r\n    \"all\": \"All\",\r\n    \"searchPlaceholder\": \"OrderNo、Name、Phone、Room No\",\r\n    \"guest\": \"Guest:\",\r\n    \"phone\": \"Phone:\",\r\n    \"orderNumber\": \"Order No:\",\r\n    \"externalOrderNumber\": \"External Order No:\",\r\n    \"currency\": \"$\",\r\n    \"displayWay\": \"Display Mode\",\r\n    \"orderMode\": \"Order Mode\",\r\n    \"guestMode\": \"Guest Mode\",\r\n    \"name\": \"Name\",\r\n    \"sex\": \"Gender\",\r\n    \"mainOrderStatus\": \"Is Main Guest\",\r\n    \"plannedCheckoutTime\": \"Planned Checkout\",\r\n    \"checkoutTime\": \"Check-out Time\",\r\n    \"status\": \"Status\",\r\n    \"noData\": \"No Data\",\r\n    \"male\": \"Male\",\r\n    \"female\": \"Female\",\r\n    \"unknown\": \"Unknown\",\r\n    \"mainGuest\": \"Main Guest\",\r\n    \"companion\": \"Companion\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"Booking\",\r\n      \"check_in\": \"Checked In\",\r\n      \"check_out\": \"Checked Out\",\r\n      \"noshow\": \"No Show\",\r\n      \"cancel\": \"Cancelled\",\r\n      \"be_confirm\": \"Pending Confirmation\",\r\n      \"refuse\": \"Refused\",\r\n      \"over\": \"Completed\",\r\n      \"credit\": \"Credit\",\r\n      \"continue\": \"Extended Stay\"\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"channel\": \"渠道\",\r\n    \"search\": \"精准搜索\",\r\n    \"guestSourceType\": \"客源类型\",\r\n    \"roomType\": \"房型\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"query\": \"查询\",\r\n    \"guestPhone\": \"客人/电话\",\r\n    \"orderNo\": \"订单号/外部订单号\",\r\n    \"roomTypeLabel\": \"房型\",\r\n    \"roomTypePrice\": \"房型/房价\",\r\n    \"roomNo\": \"房号\",\r\n    \"price\": \"房价\",\r\n    \"checkinTime\": \"入住时间\",\r\n    \"guestSource\": \"客源类型\",\r\n    \"channelLabel\": \"渠道\",\r\n    \"operation\": \"操作\",\r\n    \"view\": \"查看\",\r\n    \"entryAccount\": \"入账\",\r\n    \"all\": \"全部\",\r\n    \"searchPlaceholder\": \"订单号、外部订单号、姓名、手机号、房号\",\r\n    \"guest\": \"客人：\",\r\n    \"phone\": \"电话：\",\r\n    \"orderNumber\": \"订单号：\",\r\n    \"externalOrderNumber\": \"外部订单号：\",\r\n    \"currency\": \"￥\",\r\n    \"displayWay\": \"展示模式\",\r\n    \"orderMode\": \"订单模式\",\r\n    \"guestMode\": \"客人模式\",\r\n    \"name\": \"姓名\",\r\n    \"sex\": \"性别\",\r\n    \"mainOrderStatus\": \"是否主客\",\r\n    \"plannedCheckoutTime\": \"预离时间\",\r\n    \"checkoutTime\": \"退房时间\",\r\n    \"status\": \"状态\",\r\n    \"noData\": \"暂无数据\",\r\n    \"male\": \"男\",\r\n    \"female\": \"女\",\r\n    \"unknown\": \"保密\",\r\n    \"mainGuest\": \"主客\",\r\n    \"companion\": \"同住\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"预订中\",\r\n      \"check_in\": \"已入住\",\r\n      \"check_out\": \"已退房\",\r\n      \"noshow\": \"未到店\",\r\n      \"cancel\": \"已取消\",\r\n      \"be_confirm\": \"待确认\",\r\n      \"refuse\": \"已拒绝\",\r\n      \"over\": \"已完成\",\r\n      \"credit\": \"挂账\",\r\n      \"continue\": \"续住\"\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"channel\": \"ឆានែល\",\r\n    \"search\": \"ស្វែងរកត្រឹមត្រូវ\",\r\n    \"guestSourceType\": \"ប្រភពភ្ញៀវ\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"checkinType\": \"ប្រភេទចូលសំណាក់\",\r\n    \"query\": \"ត្រង\",\r\n    \"guestPhone\": \"ភ្ញៀវ/ទូរស័ព្ទ\",\r\n    \"orderNo\": \"លេខកម្មងែ\",\r\n    \"roomTypeLabel\": \"ប្រភេទបន្ទប់\",\r\n    \"roomTypePrice\": \"ប្រភេទបន្ទប់/តម្លៃ\",\r\n    \"roomNo\": \"លេខបន្ទប់\",\r\n    \"price\": \"តម្លៃ\",\r\n    \"checkinTime\": \"ពេលចូលសំណាក់\",\r\n    \"guestSource\": \"ប្រភពភ្ញៀវ\",\r\n    \"channelLabel\": \"ឆានែល\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"view\": \"មើល\",\r\n    \"entryAccount\": \"បញ្ចូលគណនី\",\r\n    \"all\": \"ទាំងអស់\",\r\n    \"searchPlaceholder\": \"លេខកម្មងែ, ឈ្មោះ, ទូរស័ព្ទ, លេខបន្ទប់\",\r\n    \"guest\": \"ភ្ញៀវ៖\",\r\n    \"phone\": \"ទូរស័ព្ទ៖\",\r\n    \"orderNumber\": \"លេខកម្មងែ៖\",\r\n    \"externalOrderNumber\": \"លេខកម្មងែខាងក្រៅ៖\",\r\n    \"currency\": \"៛\",\r\n    \"displayWay\": \"របៀបបង្ហាញ\",\r\n    \"orderMode\": \"របៀបបញ្ជាទិញ\",\r\n    \"guestMode\": \"របៀបភ្ញៀវ\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"sex\": \"ភេទ\",\r\n    \"mainOrderStatus\": \"ស្ថានភាពបញ្ជាទិញសំខាន់\",\r\n    \"plannedCheckoutTime\": \"ពេលវេលាចាកចេញគ្រោងទុក\",\r\n    \"checkoutTime\": \"ពេលវេលាចាកចេញ\",\r\n    \"status\": \"ស្ថានភាព\",\r\n    \"noData\": \"គ្មានទិន្នន័យ\",\r\n    \"male\": \"ប្រុស\",\r\n    \"female\": \"ស្រី\",\r\n    \"unknown\": \"មិនស្គាល់\",\r\n    \"mainGuest\": \"ភ្ញៀវសំខាន់\",\r\n    \"companion\": \"ភ្ញៀវអមដំណើរ\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"កំពុងកក់\",\r\n      \"check_in\": \"បានចូលស្នាក់នៅ\",\r\n      \"check_out\": \"បានចាកចេញ\",\r\n      \"noshow\": \"មិនបានមកដល់\",\r\n      \"cancel\": \"បានលុបចោល\",\r\n      \"be_confirm\": \"រង់ចាំការបញ្ជាក់\",\r\n      \"refuse\": \"បានបដិសេធ\",\r\n      \"over\": \"បានបញ្ចប់\",\r\n      \"credit\": \"ជំពាក់\",\r\n      \"continue\": \"បន្តស្នាក់នៅ\"\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { OrderModel } from '@/models/index'\r\nimport { channelApi, dictDataApi, orderApi, rtApi } from '@/api/modules/index'\r\n\r\nimport { BooleanEnum, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE, NoType, OrderState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nimport OrderDialog from '@/views/order/info/order.vue'\r\nimport EntryAccount from '@/views/room/components/entryAccount/index.vue'\r\nimport { Search } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\n\r\ndefineOptions({\r\n  name: 'OrderInHandPlanCheckoutList',\r\n})\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\nconst { t } = useI18n()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  tableAutoHeight: false,\r\n  formMode: 'dialog' as 'router' | 'dialog' | 'drawer',\r\n  formModeProps: {\r\n    visible: false,\r\n    no: '',\r\n    orderTogetherCode: '',\r\n    noType: '',\r\n  },\r\n  search: {\r\n    channelCode: '-1',\r\n    rtCode: '',\r\n    guestSrcType: '',\r\n    checkinType: '',\r\n    searchType: '0',\r\n    searchContent: '',\r\n    // 0: 订单模式 1: 客人模式\r\n    displayWay: '0',\r\n  },\r\n  dataList: [] as OrderModel[],\r\n})\r\n\r\nonMounted(() => {\r\n  getChannels()\r\n  getRts()\r\n  getConstants()\r\n  getDataList()\r\n})\r\n\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst rts = ref<{ rtCode: string; rtName: string }[]>([])\r\nfunction getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isVirtual: BooleanEnum.NO,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst dictTypes = [DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_CHECKIN_TYPE]\r\nconst srcTypeList = ref<{ code: string; label: string }[]>([])\r\nconst checkinTypeList = ref<{ code: string; label: string }[]>([])\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    srcTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n    checkinTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n  })\r\n}\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    keyWords: data.value.search.searchContent,\r\n    channelCode: data.value.search.channelCode === '-1' ? '' : data.value.search.channelCode,\r\n    guestSrcType: data.value.search.guestSrcType,\r\n    rtCode: data.value.search.rtCode,\r\n    checkinType: data.value.search.checkinType,\r\n  }\r\n  orderApi.planCheckOutList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    if (res.data.list) {\r\n      data.value.dataList = res.data.list\r\n      pagination.value.total = res.data.total\r\n    }\r\n  })\r\n}\r\n\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nconst detailVisible = ref(false)\r\nconst routerName = ref('detail')\r\nconst typeName = ref('individual')\r\nfunction onDetail(row: any) {\r\n  data.value.formModeProps.no = row.orderNo\r\n  detailVisible.value = true\r\n}\r\n\r\nconst entryAccountVisible = ref(false)\r\nconst tabName = ref('')\r\n\r\nfunction entryAccount(val: string, row: any) {\r\n  data.value.formModeProps.no = row.orderNo\r\n\r\n  // 从togetherList中找到主客的togetherCode\r\n  let mainGuestTogetherCode = row.togetherCode // 默认使用row的togetherCode作为备选\r\n  if (row.togetherList && row.togetherList.length > 0) {\r\n    const mainGuest = row.togetherList.find((guest: any) => guest.isMain === '1')\r\n    if (mainGuest && mainGuest.togetherCode) {\r\n      mainGuestTogetherCode = mainGuest.togetherCode\r\n    }\r\n  }\r\n\r\n  data.value.formModeProps.orderTogetherCode = mainGuestTogetherCode\r\n  tabName.value = val\r\n  entryAccountVisible.value = true\r\n}\r\n/**\r\n * 获取订单状态的显示文本\r\n * @param state 订单状态代码\r\n * @returns 状态显示文本\r\n */\r\nfunction getOrderStatusText(state: string): string {\r\n  return t(`orderStatus.${state}`) || state\r\n}\r\n\r\n/**\r\n * 获取订单状态的标签类型\r\n * @param state 订单状态代码\r\n * @returns Element Plus 标签类型\r\n */\r\nfunction getOrderStatusType(state: string): 'success' | 'info' | 'warning' | 'danger' | '' {\r\n  switch (state) {\r\n    case OrderState.CHECK_IN:\r\n      return 'success'\r\n    case OrderState.CHECK_OUT:\r\n      return 'info'\r\n    case OrderState.IN_BOOKING:\r\n      return 'warning'\r\n    case OrderState.CANCEL:\r\n    case OrderState.REFUSE:\r\n    case OrderState.NOSHOW:\r\n      return 'danger'\r\n    case OrderState.OVER:\r\n    case OrderState.BE_CONFIRM:\r\n      return ''\r\n    case OrderState.CREDIT:\r\n    case OrderState.CONTINUE:\r\n      return 'warning'\r\n    default:\r\n      return ''\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <search-bar :show-toggle=\"false\">\r\n        <el-form :model=\"data.search\" size=\"default\" label-width=\"80px\" inline-message inline class=\"search-form\">\r\n          <div class=\"filter-row\">\r\n            <el-form-item :label=\"t('channel')\">\r\n              <el-select v-model=\"data.search.channelCode\" clearable class=\"filter-select\">\r\n                <el-option :label=\"t('all')\" value=\"-1\" />\r\n                <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('guestSourceType')\">\r\n              <el-select v-model=\"data.search.guestSrcType\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in srcTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('roomType')\">\r\n              <el-select v-model=\"data.search.rtCode\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"filter-row\">\r\n            <el-form-item :label=\"t('checkinType')\">\r\n              <el-select v-model=\"data.search.checkinType\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in checkinTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('search')\" class=\"search-input-item\">\r\n              <el-input v-model=\"data.search.searchContent\" class=\"filter-select w-350px!\" clearable :placeholder=\"t('searchPlaceholder')\">\r\n                <template #append>\r\n                  <el-button :icon=\"Search\" @click=\"currentChange()\" />\r\n                </template>\r\n              </el-input>\r\n              <el-button type=\"primary\" class=\"query-button\" @click=\"getDataList\">\r\n                {{ t('query') }}\r\n              </el-button>\r\n              <div class=\"switch-container\">\r\n                <el-switch\r\n                  v-model=\"data.search.displayWay\"\r\n                  active-value=\"1\"\r\n                  inactive-value=\"0\"\r\n                  :active-text=\"t('guestMode')\"\r\n                  :inactive-text=\"t('orderMode')\"\r\n                  inline-prompt\r\n                  class=\"display-way-switch\"\r\n                  style=\"--el-switch-on-color: #13ce66; --el-switch-off-color: #554dd6\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </search-bar>\r\n\r\n      <el-table v-loading=\"data.loading\" class=\"list-table\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\" @sort-change=\"sortChange\">\r\n        <!-- 可展开行列 - 仅在订单模式下显示 -->\r\n        <el-table-column v-if=\"data.search.displayWay === '0'\" type=\"expand\" width=\"50\">\r\n          <template #default=\"{ row }\">\r\n            <div class=\"expand-content\">\r\n              <el-table v-if=\"row.togetherList && row.togetherList.length > 0\" :data=\"row.togetherList\" border size=\"small\" class=\"expand-table\">\r\n                <el-table-column prop=\"name\" :label=\"t('name')\" min-width=\"120\" />\r\n                <el-table-column prop=\"phone\" :label=\"t('phone')\" min-width=\"120\" />\r\n                <el-table-column :label=\"t('sex')\" width=\"80\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"scope.row.sex === '1' ? 'primary' : 'danger'\" size=\"small\">\r\n                      {{ scope.row.sex === '1' ? t('male') : scope.row.sex === '0' ? t('female') : t('unknown') }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('mainOrderStatus')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"scope.row.isMain === '1' || row.togetherList.length === 1 ? 'success' : 'info'\" size=\"small\">\r\n                      {{ scope.row.isMain === '1' || row.togetherList.length === 1 ? t('mainGuest') : t('companion') }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('checkinTime')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.checkinTime ? dayjs(scope.row.checkinTime).format('MM/DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('checkoutTime')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.checkoutTime ? dayjs(scope.row.checkoutTime).format('MM/DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('plannedCheckoutTime')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.planCheckoutTime ? dayjs(scope.row.planCheckoutTime).format('MM/DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('status')\" width=\"100\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"getOrderStatusType(scope.row.state)\" size=\"small\">\r\n                      {{ getOrderStatusText(scope.row.state) }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n              <div v-else class=\"no-data\">\r\n                {{ t('noData') }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('guestPhone')\" min-width=\"155\">\r\n          <template #default=\"scope\">\r\n            <div class=\"guest-info\">\r\n              <div>\r\n                {{ scope.row.name }}\r\n              </div>\r\n              <div v-if=\"scope.row.phone\">\r\n                {{ scope.row.phone }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('orderNo')\" min-width=\"190\">\r\n          <template #default=\"scope\">\r\n            <div class=\"order-info\">\r\n              <div>\r\n                <span class=\"label\">{{ t('orderNumber') }}</span\r\n                >{{ scope.row.orderNo }}\r\n              </div>\r\n              <div v-if=\"scope.row.outOrderNo\">\r\n                <span class=\"label\">{{ t('externalOrderNumber') }}</span\r\n                >{{ scope.row.outOrderNo }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('roomTypePrice')\" min-width=\"180\">\r\n          <template #default=\"scope\">\r\n            <div class=\"room-info\">\r\n              <span class=\"room-type\">{{ scope.row.rtName }}</span>\r\n              <span class=\"price\">{{ t('currency') }}{{ scope.row.price }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"rNo\" :label=\"t('roomNo')\" />\r\n        <el-table-column :label=\"t('checkinTime')\">\r\n          <template #default=\"scope\">\r\n            <label>{{ dayjs(scope.row.checkinTime).format('MM/DD HH:mm') }}</label>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"checkinTypeName\" :label=\"t('checkinType')\" />\r\n        <el-table-column prop=\"guestSrcTypeName\" :label=\"t('guestSource')\" />\r\n        <el-table-column prop=\"channelName\" :label=\"t('channelLabel')\" />\r\n        <el-table-column :label=\"t('operation')\" width=\"130\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-link v-auth=\"'pms:order:query:get-order-detail'\" type=\"primary\" @click=\"onDetail(scope.row)\">\r\n              {{ t('view') }}\r\n            </el-link>\r\n            <!-- <el-link v-auth=\"'pms:account:create'\" type=\"primary\" @click=\"entryAccount('pay', scope.row)\">\r\n              {{ t('entryAccount') }}\r\n            </el-link> -->\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n      <OrderDialog v-if=\"detailVisible\" v-model=\"detailVisible\" :no=\"data.formModeProps.no\" :no-type=\"NoType.ORDER\" :tab-name=\"routerName\" :tab-type=\"typeName\" @reload=\"getDataList\" />\r\n      <EntryAccount v-if=\"entryAccountVisible\" v-model=\"entryAccountVisible\" :order-no=\"data.formModeProps.no\" :order-together-code=\"data.formModeProps.orderTogetherCode\" :no-type=\"NoType.ORDER\" :order-type=\"NoType.ORDER\" :tab-name=\"tabName\" />\r\n    </page-main>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-link {\r\n  margin: 0 10px;\r\n}\r\n\r\n.list-table {\r\n  .guest-info,\r\n  .order-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n\r\n    .label {\r\n      color: #909399;\r\n      margin-right: 4px;\r\n    }\r\n  }\r\n\r\n  .room-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    .room-type {\r\n      color: #606266;\r\n    }\r\n\r\n    .price {\r\n      color: #f56c6c;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    .filter-row {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      margin-bottom: 8px;\r\n      align-items: center;\r\n\r\n      .el-form-item {\r\n        margin-right: 12px;\r\n        margin-bottom: 8px;\r\n\r\n        // 增加标签宽度以适应英文文本\r\n        :deep(.el-form-item__label) {\r\n          padding-bottom: 0;\r\n          line-height: 28px;\r\n          min-width: 100px; // 增加最小宽度\r\n          white-space: nowrap; // 防止标签换行\r\n        }\r\n\r\n        .filter-select {\r\n          width: 220px; // 增加选择框宽度\r\n          min-width: 180px; // 设置最小宽度\r\n        }\r\n\r\n        :deep(.el-form-item__content) {\r\n          line-height: 28px;\r\n        }\r\n\r\n        &.search-input-item {\r\n          flex-grow: 1;\r\n          min-width: 300px; // 调整最小宽度以适应200px输入框\r\n\r\n          :deep(.el-form-item__content) {\r\n            display: flex;\r\n            align-items: center;\r\n            flex-wrap: wrap; // 允许换行\r\n            gap: 8px; // 添加间距\r\n          }\r\n\r\n          .filter-select {\r\n            width: 200px; // 设置为200px\r\n            min-width: 200px; // 最小宽度也设置为200px\r\n          }\r\n\r\n          .query-button {\r\n            margin-left: 0; // 移除左边距\r\n            min-width: 80px;\r\n            flex-shrink: 0; // 防止按钮被压缩\r\n          }\r\n\r\n          .switch-container {\r\n            margin-left: auto; // 将开关推到最右边\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 表格工具栏样式\r\n.table-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  margin-bottom: 12px;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n\r\n    .toolbar-label {\r\n      font-size: 14px;\r\n      color: #606266;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .display-way-switch {\r\n      :deep(.el-switch__label) {\r\n        color: #606266;\r\n        font-size: 13px;\r\n      }\r\n\r\n      :deep(.el-switch__label.is-active) {\r\n        color: #409eff;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 可展开行样式\r\n.expand-content {\r\n  padding: 16px;\r\n  background-color: #f8f9fa;\r\n\r\n  .expand-title {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    margin-bottom: 12px;\r\n    padding-bottom: 8px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n\r\n  .expand-table {\r\n    :deep(.el-table__header) {\r\n      background-color: #fafafa;\r\n    }\r\n\r\n    :deep(.el-table__body) {\r\n      background-color: #fff;\r\n    }\r\n\r\n    :deep(.el-table td) {\r\n      padding: 8px 0;\r\n    }\r\n\r\n    :deep(.el-table th) {\r\n      padding: 8px 0;\r\n      background-color: #fafafa !important;\r\n    }\r\n  }\r\n\r\n  .no-data {\r\n    text-align: center;\r\n    color: #909399;\r\n    padding: 20px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.flex-form {\r\n  display: flex;\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "t", "useI18n", "data", "ref", "loading", "tableAutoHeight", "formMode", "formModeProps", "visible", "no", "orderTogetherCode", "noType", "search", "channelCode", "rtCode", "guestSrcType", "checkinType", "searchType", "searchContent", "displayWay", "dataList", "onMounted", "params", "gcode", "hcode", "isEnable", "BooleanEnum", "YES", "channelApi", "getChannelSimpleList", "then", "res", "code", "channels", "value", "getChannels", "isVirtual", "NO", "rtApi", "getRoomTypeSimpleList", "rts", "getRts", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "srcTypeList", "filter", "item", "dictType", "DICT_TYPE_GUEST_SRC_TYPE", "checkinTypeList", "DICT_TYPE_CHECKIN_TYPE", "getDataList", "key<PERSON>ords", "orderApi", "planCheckOutList", "list", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "detailVisible", "routerName", "typeName", "entryAccountVisible", "tabName", "getOrderStatusType", "state", "OrderState", "CHECK_IN", "CHECK_OUT", "IN_BOOKING", "CANCEL", "REFUSE", "NOSHOW", "OVER", "BE_CONFIRM", "CREDIT", "CONTINUE", "row", "orderNo"], "mappings": "67KAsLA,MAAMA,EAAYC,KACZC,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,mBAAcC,GAAiBC,aAAAA,IAAiBC,KACzEC,EAAEA,IAAMC,IAERC,GAAOC,EAAI,CACfC,SAAS,EACTC,iBAAiB,EACjBC,SAAU,SACVC,cAAe,CACbC,SAAS,EACTC,GAAI,GACJC,kBAAmB,GACnBC,OAAQ,IAEVC,OAAQ,CACNC,YAAa,KACbC,OAAQ,GACRC,aAAc,GACdC,YAAa,GACbC,WAAY,IACZC,cAAe,GAEfC,WAAY,KAEdC,SAAU,KAGZC,GAAU,MAQV,WACE,MAAMC,EAAS,CACbC,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjBC,SAAUC,EAAYC,KAExBC,EAAWC,qBAAqBP,GAAQQ,MAAMC,IAC3B,IAAbA,EAAIC,OACNC,GAASC,MAAQH,EAAI7B,KAAA,GAExB,CAjBWiC,GAqBd,WACE,MAAMb,EAAS,CACbC,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjBY,UAAWV,EAAYW,GACvBZ,SAAUC,EAAYC,KAExBW,EAAMC,sBAAsBjB,GAAQQ,MAAMC,IACvB,IAAbA,EAAIC,OACNQ,GAAIN,MAAQH,EAAI7B,KAAA,GAEnB,CA/BMuC,GAsCPC,EAAYC,iBAAiBC,IAAWd,MAAMC,IAChCc,GAAAX,MAAQH,EAAI7B,KAAK4C,QAAQC,GAAcA,EAAKC,WAAaC,IACrDC,GAAAhB,MAAQH,EAAI7B,KAAK4C,QAAQC,GAAcA,EAAKC,WAAaG,GAAsB,IAtCrFC,IAAA,IAGR,MAAAnB,GAAW9B,EAAoD,IAc/D,MAAAqC,GAAMrC,EAA0C,IAehD,MAAAyC,GAAY,CAACK,EAA0BE,GACvCN,GAAc1C,EAAuC,IACrD+C,GAAkB/C,EAAuC,IAQ/D,SAASiD,KACPlD,GAAKgC,MAAM9B,SAAU,EACrB,MAAMkB,EAAS,IACV3B,KACH4B,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjB6B,SAAUnD,GAAKgC,MAAMtB,OAAOM,cAC5BL,YAA+C,OAAlCX,GAAKgC,MAAMtB,OAAOC,YAAuB,GAAKX,GAAKgC,MAAMtB,OAAOC,YAC7EE,aAAcb,GAAKgC,MAAMtB,OAAOG,aAChCD,OAAQZ,GAAKgC,MAAMtB,OAAOE,OAC1BE,YAAad,GAAKgC,MAAMtB,OAAOI,aAEjCsC,EAASC,iBAAiBjC,GAAQQ,MAAMC,IACtC7B,GAAKgC,MAAM9B,SAAU,EACjB2B,EAAI7B,KAAKsD,OACNtD,GAAAgC,MAAMd,SAAWW,EAAI7B,KAAKsD,KACpB9D,GAAAwC,MAAMuB,MAAQ1B,EAAI7B,KAAKuD,MAAA,GAErC,CAGH,SAASC,GAAWC,GAClB/D,GAAa+D,GAAM7B,MAAK,IAAMsB,MAAa,CAGpC,SAAAQ,GAAcC,EAAO,GAC5BhE,GAAgBgE,GAAM/B,MAAK,IAAMsB,MAAa,CAGhD,SAASU,IAAWC,KAAEA,EAAMC,MAAAA,IAC1BlE,GAAaiE,EAAMC,GAAOlC,MAAK,IAAMsB,MAAa,CAG9C,MAAAa,GAAgB9D,GAAI,GACpB+D,GAAa/D,EAAI,UACjBgE,GAAWhE,EAAI,cAMf,MAAAiE,GAAsBjE,GAAI,GAC1BkE,GAAUlE,EAAI,IAgCpB,SAASmE,GAAmBC,GAC1B,OAAQA,GACN,KAAKC,EAAWC,SACP,MAAA,UACT,KAAKD,EAAWE,UACP,MAAA,OACT,KAAKF,EAAWG,WACP,MAAA,UACT,KAAKH,EAAWI,OAChB,KAAKJ,EAAWK,OAChB,KAAKL,EAAWM,OACP,MAAA,SACT,KAAKN,EAAWO,KAChB,KAAKP,EAAWQ,WACP,MAAA,GACT,KAAKR,EAAWS,OAChB,KAAKT,EAAWU,SACP,MAAA,UACT,QACS,MAAA,GACX,qlJA7B0BX,cACnBvE,GAAE,eAAeuE,MAAYA,QADtC,IAA4BA,m0CA7BVY,QACXjF,GAAAgC,MAAM3B,cAAcE,GAAK0E,EAAIC,aAClCnB,GAAc/B,OAAQ,GAFxB,IAAkBiD"}