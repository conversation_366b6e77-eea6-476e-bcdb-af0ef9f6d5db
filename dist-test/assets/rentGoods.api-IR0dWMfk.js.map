{"version": 3, "file": "rentGoods.api-IR0dWMfk.js", "sources": ["../../src/api/modules/pms/goods/rentGoods.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/rent'\r\n/**\r\n * @description: 租借物品\r\n */\r\nexport default {\r\n  /**\r\n   * 获得租借物品列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getRentGoodsList: (data: { gcode: string; hcode: string; orderNo?: string; rentName?: string; rentTimeStart?: string; rentTimeEnd?: string; backTimeStart?: string; backTimeEnd?: string; state?: string }) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 租借物品明细\r\n   * @param id\r\n   * @returns\r\n   */\r\n  getRentGoods: (id: number) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        id,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 删除租借物品\r\n   * @param id\r\n   */\r\n  deleteRentGoods: (id: number) =>\r\n    api.delete(`${BASE_PATH}/delete`, {\r\n      params: {\r\n        id,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 租借物品编辑\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateRentGoods: (data: any) => api.put(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 租借物品归还\r\n   * @param data\r\n   * @returns\r\n   */\r\n  backRentGoods: (data: any) => api.put(`${BASE_PATH}/back`, data, {}),\r\n\r\n  /**\r\n   * 租借物品赔偿\r\n   * @param data\r\n   * @returns\r\n   */\r\n  indemnityRentGoods: (data: any) => api.put(`${BASE_PATH}/indemnity`, data, {}),\r\n\r\n  /**\r\n   * 租借物品创建\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createRentGoods: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n}\r\n"], "names": ["BASE_PATH", "rentApi", "getRentGoodsList", "data", "api", "get", "params", "getRentGoods", "id", "deleteRentGoods", "delete", "updateRentGoods", "put", "backRentGoods", "indemnityRentGoods", "createRentGoods", "post"], "mappings": "wCAEA,MAAMA,EAAY,qBAIHC,EAAA,CAMbC,iBAAmBC,GACjBC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAQZI,aAAeC,GACbJ,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CACNE,QAQNC,gBAAkBD,GAChBJ,EAAIM,OAAO,GAAGV,WAAoB,CAChCM,OAAQ,CACNE,QASNG,gBAAkBR,GAAcC,EAAIQ,IAAI,GAAGZ,WAAoBG,EAAM,IAOrEU,cAAgBV,GAAcC,EAAIQ,IAAI,GAAGZ,SAAkBG,EAAM,IAOjEW,mBAAqBX,GAAcC,EAAIQ,IAAI,GAAGZ,cAAuBG,EAAM,IAO3EY,gBAAkBZ,GAAcC,EAAIY,KAAK,GAAGhB,WAAoBG,EAAM,CAAE"}