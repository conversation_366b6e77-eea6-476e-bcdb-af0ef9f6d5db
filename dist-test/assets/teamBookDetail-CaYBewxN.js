import{_ as e}from"./index-3RMLzyhA.js";import{d as t,aj as o,ai as a,b as r,r as s,B as i,y as n,ae as l,o as d,c as u,g as c,f as m,w as p,Y as b,u as g,e as h,h as f,R as v,aS as y,F as N,ag as k,a6 as C,aR as w,av as R,q as T,m as _,b1 as I,b2 as x,x as S,E as V,l as O,k as U,b0 as M,bF as A,a7 as D,c7 as B,bn as P,bL as j,c2 as E,c3 as Y,ax as G,j as $,bz as q,bA as L,t as H,v as K,bv as F,bO as z,s as W}from"./index-CkEhI1Zk.js";/* empty css                 */import{_ as J}from"./index-ADu0XAHG.js";/* empty css                 *//* empty css                        *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                      *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";import{m as Q}from"./member.api-2tU9HGvl.js";import{h as X}from"./hotelParamConfig.api-CbdvhUfn.js";import{c as Z}from"./customer.api-dB3m63zZ.js";import{d as ee}from"./device.api-BsgckoMw.js";import{b as te}from"./book.api-ERXvEXQF.js";import{o as oe}from"./order.api-B-JCVvq6.js";import{d as ae}from"./dictData.api-DUabpYqy.js";import{s as re}from"./serverTime.api-D89oCqKL.js";import{u as se}from"./user.api-BYl7ypOS.js";import{r as ie}from"./roomCardLog.api-pw0J1hl7.js";import{f as ne,q as le,r as de,S as ue,I as ce,j as me,G as pe,O as be,B as ge,l as he,N as fe}from"./constants-Cg3j_uH4.js";import{C as ve}from"./CardReader-BeR26SIt.js";import{g as ye}from"./myStringUtil-D79vpWmP.js";import{g as Ne}from"./roomCardUtil-DBQw7z7m.js";import ke from"./arrangeRooms-CPfs5GXR.js";import Ce from"./arrangeRts-C83hWsgy.js";import we from"./index-Eu7Cs0xe.js";import{validateTipPhone as Re}from"./utils-S8-xpbSs.js";import{_ as Te}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                          *//* empty css                       *//* empty css                  *//* empty css                          *//* empty css                  *//* empty css                   *//* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-DvSmdj52.js";import"./generalConfig.api-CEBBd8kx.js";import"./GeneralConfigTypeEnum-DERqowgl.js";import"./timeutils-Ib6GkGcq.js";const _e={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center"}},Ie={key:1},xe={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center"}},Se={style:{display:"flex","justify-content":"space-between","align-items":"center","margin-bottom":"10px"}},Ve={style:{"margin-left":"10px","margin-right":"10px"}},Oe={class:"table-wrapper"},Ue={key:0,style:{display:"flex","align-items":"center"}},Me={key:0,style:{display:"flex","align-items":"center"}},Ae={key:1},De={key:0,style:{color:"#f56c6c"}},Be={key:1},Pe={key:1,style:{display:"flex","align-items":"center"}},je={style:{"margin-right":"5px"}},Ee={key:2,class:"flex-around"},Ye={class:"w-[240px]",style:{display:"flex","align-items":"center"}},Ge={key:0,style:{display:"flex","align-items":"center"}},$e={key:0},qe={key:1},Le={key:5,class:"flex-center"},He={class:"flex-between"},Ke={class:"flex-center",style:{color:"var(--el-text-color-secondary)","font-size":"13px"}},Fe={class:"w-[75px]"},ze={class:"w-[75px]"},We=t({__name:"teamBookDetail",props:{bookNo:{default:()=>[]},state:{default:""},isEntryAccount:{default:"0"}},emits:["update:modelValue","success","refresh","reload","seeDetailed"],setup(t,{emit:Te}){const We=t,Je=Te,{t:Qe}=o(),Xe=a(),Ze=r({loading:!1,tableAutoHeight:!1}),et=r(),tt=r({batches:[]}),ot=s({gcode:Xe.gcode,hcode:Xe.hcode}),at=r([]),rt=r([]),st=r([]),it=r([]),nt=r(!1),lt=r(""),dt=r(!1),ut=r([]),ct=r(),mt=r(),pt=r(!1),bt=s({...ot,upgradeMethod:0,bookNo:We.bookNo[0],rtCode:"",upgradeRtCode:"",orderNo:""}),gt=[{prop:"room",label:Qe("tableColumns.roomNoRoomType"),width:"250px",align:"left"},{prop:"price",label:Qe("tableColumns.discountPrice"),width:"120px",align:"left"},{prop:"namePhone",label:Qe("tableColumns.namePhone"),width:"240px",align:"left"},{prop:"CardNumber",label:Qe("tableColumns.idTypeIdNumber"),width:"440px",align:"left"},{prop:"bk",label:Qe("tableColumns.breakfastPortions"),width:"150px",align:"center"},{prop:"cz",label:Qe("tableColumns.operations"),width:"240px",align:"center"}],ht=r({...bt}),ft=r([]),vt=i((()=>tt.value.guestSrcType===pe.MEMBER?Qe("guestSrcValidation.memberRequired"):tt.value.guestSrcType===pe.AGENT?Qe("guestSrcValidation.agentRequired"):tt.value.guestSrcType===pe.PROTOCOL?Qe("guestSrcValidation.protocolRequired"):Qe("guestSrcValidation.selectOrInputRequired"))),yt=i((()=>tt.value.guestSrcType!==pe.WALK_IN)),Nt=r({teamName:[{required:!0,message:Qe("teamNameRequired"),trigger:"blur"}],guestCode:[{required:yt.value,message:vt.value,trigger:"blur"}]});n((()=>{ae.getDictDataBatch([ne,le,de]).then((e=>{at.value=e.data.filter((e=>e.dictType===ne)),rt.value=e.data.filter((e=>e.dictType===le)),st.value=e.data.filter((e=>e.dictType===de)).filter((e=>"0"!==e.code&&"0"!==e.code))})),xt(),se.listSeller(ot).then((e=>{0===e.code&&(It.value=e.data)})),async function(){const e=await re.serverTime(Xe.gcode,"0");lt.value=e.data}(),function(){const e={gcode:Xe.gcode,hcode:Xe.hcode};X.getHotelParamConfigFront(e).then((e=>{0===e.code&&(Tt.value=e.data)}))}(),void 0===window.CallBridge&&void 0===window.__RUNNING_IN_PMS_AGENT__||ve.closeSocket()}));const kt=r();l((()=>{void 0===window.CallBridge&&void 0===window.__RUNNING_IN_PMS_AGENT__||ve.closeSocket(),kt.value&&kt.value.length>0&&kt.value.removeEventListener("scroll",wt)}));const Ct=r(null);function wt(){Ct.value=document.documentElement.scrollTop}const Rt=r([]);const Tt=r({dirtyAlert:"",idOneRoom:"",needId:"",orderNotice:""});function _t(e){Z.simpleList({gcode:Xe.gcode,belongHcode:Xe.hcode,paType:e,isEnable:ge.YES}).then((e=>{Rt.value=e.data}))}const It=r([]);function xt(){dt.value=!0,oe.getTeamBook({bookNo:We.bookNo[0]}).then((e=>{var t;dt.value=!1,0===e.code&&(tt.value=e.data,null==(t=tt.value.batches)||t.forEach((e=>{e.bookRooms.forEach((e=>{e.togethers&&0!==e.togethers.length||(e.togethers=[{togetherCode:"",rCode:"",rNo:"",sex:ue.SECRECY.toString(),nation:"",address:"",name:"",phone:"",idType:ce.IDCERT,idNo:"",isMadeCard:"0",isMain:"1",state:""}]),e.togethers.forEach(((t,o)=>{let a;a=t.togetherCode?`${e.orderNo}_${t.togetherCode}`:`${e.orderNo}_new_${o}`,Gt.value[a]={name:t.name||"",phone:t.phone||"",idNo:t.idNo||"",idType:t.idType||""}})),e.togethers&&e.togethers.length>1&&(ut.value.includes(e.orderNo)||ut.value.push(e.orderNo))}))})))}))}const St=r(!1);function Vt(){St.value=!0}function Ot(){et.value&&et.value.validate((e=>{if(e){const e={bookNo:tt.value.bookNo,channelCode:tt.value.channelCode,teamName:tt.value.teamName,guestSrcType:tt.value.guestSrcType,checkinType:tt.value.checkinType,guestCode:tt.value.guestCode,guestName:tt.value.guestName,teamCode:tt.value.teamCode,contractNo:tt.value.contractNo,contact:tt.value.contact,phone:tt.value.phone,guarantyStyle:tt.value.guarantyStyle,seller:tt.value.seller,remark:tt.value.remark};te.updateBaseTeamBookInfo(e).then((e=>{0===e.code&&(w.success({message:Qe("messages.modifySuccess"),type:"success",center:!0}),St.value=!1)}))}}))}function Ut(){pt.value=!0}const Mt=r();function At(){return re.serverTime(Xe.gcode,"3").then((e=>0===e.code?e.data:""))}async function Dt(e,t){var o,a;if(!e.rNo)return void w.warning(Qe("messages.makeCardBeforeArrangeRoom"));if(!Bt())return;if(!e.lockVersion)return void w({showClose:!0,message:Qe("messages.hotelNoLockConfig"),type:"warning",dangerouslyUseHTMLString:!0});if(await function(e){const t={...ot,...e&&{lockVersion:e}};return ee.getHotelDoor(t).then((e=>{0===e.code&&e.data&&(Mt.value=e.data)}))}(e.lockVersion),!Mt.value)return void w({showClose:!0,message:Qe("messages.hotelNoLockConfig"),type:"warning",dangerouslyUseHTMLString:!0});ve.isConnected&&(ve.closeSocket(),await new Promise((e=>setTimeout(e,300)))),ve.initCardReader((o=>{const a=JSON.parse(o);a&&!0===a.succeed&&a.method===he.WRITELOCKCARD&&(a.cardInfo&&(j({title:Qe("messages.cardInfo"),dangerouslyUseHTMLString:!0,message:Ne(a),position:"bottom-left",type:"success"}),ie.createRoomCardLog({gcode:Xe.gcode,hcode:Xe.hcode,name:e.guestName,rNo:e.rNo,cardNo:a.cardInfo.cardNo,type:t?"0":"1",periodTime:R(1e3*Number(a.cardInfo.expire)).format("YYYY-MM-DD HH:mm:ss")}).then((e=>{0===e.code&&console.log("制卡日志记录成功")})).catch((e=>{console.error("制卡日志记录失败:",e)}))),w({message:`${e.rNo}${Qe("messages.makeCardSuccess")}`,type:"success"}))}));const r=await At();if(!r)return void console.error("Failed to get system time");const s=Math.floor(Number.parseInt(r,10)/1e3),i={method:he.WRITELOCKCARD,lockVer:Mt.value.version,cardInfo:{roomNo:e.rNo,checkin:s,expire:Math.floor(R(e.planCheckoutTime).valueOf()/1e3),allowLockOut:(null==(o=Mt.value)?void 0:o.allowLockOut)===ge.YES,replaceCard:t,checkTime:(null==(a=Mt.value)?void 0:a.checkTime)===ge.YES,lockNo:e.lockNo,mac:e.mac,buildNo:e.buildNo,floorNo:e.floorNo}};Mt.value.conf&&Mt.value.conf.length>0&&Mt.value.conf.forEach((e=>{i.cardInfo[e.parameterCode]=e.parameterContent}));const n=JSON.stringify(i);console.log("jsonString",n);const l=setInterval((()=>{ve.isConnected&&(ve.handleLockCard(n),clearInterval(l))}),200)}function Bt(){if(void 0===window.CallBridge&&void 0===window.__RUNNING_IN_PMS_AGENT__){return w({showClose:!0,message:`读取房卡只能在Hotel-Agent中操作，请${'<a href="https://pms-client-update-1303913307.cos.ap-guangzhou.myqcloud.com/Hotel-Agent-Windows.exe" target="_blank">下载Hotel-Agent客户端</a>'}。`,type:"warning",dangerouslyUseHTMLString:!0}),!1}return!0}function Pt(e,t){if(!Bt())return;ve.isConnected&&ve.closeSocket(),ve.initCardReader((o=>{const a=JSON.parse(o);if(a){const o=a.peopleIDCode,r=a.peopleName,s=a.peopleAddress;let i=ue.SECRECY.toString();i=a.peopleSex?"男"===a.peopleSex?ue.MALE.toString():ue.FEMALE.toString():a.peopleIDCode?ye(a.peopleIDCode).toString():ue.SECRECY.toString();const n=a.peopleNation.indexOf("族")?a.peopleNation:`${a.peopleNation}族`;if(t)for(let t=0;t<tt.value.batches.length;t++)for(let a=0;a<tt.value.batches[t].bookRooms.length;a++)for(let l=0;l<tt.value.batches[t].bookRooms[a].togethers.length;l++)tt.value.batches[t].bookRooms[a].togethers[l].togetherCode===e.row.togetherCode&&(tt.value.batches[t].bookRooms[a].togethers[l].idNo=o,tt.value.batches[t].bookRooms[a].togethers[l].name=r,tt.value.batches[t].bookRooms[a].togethers[l].address=s,tt.value.batches[t].bookRooms[a].togethers[l].sex=i,tt.value.batches[t].bookRooms[a].togethers[l].nation=n);else for(let t=0;t<tt.value.batches.length;t++)for(let a=0;a<tt.value.batches[t].bookRooms.length;a++)tt.value.batches[t].bookRooms[a].orderNo===e.row.orderNo&&(tt.value.batches[t].bookRooms[a].togethers[0].idNo=o,tt.value.batches[t].bookRooms[a].togethers[0].name=r,tt.value.batches[t].bookRooms[a].togethers[0].address=s,tt.value.batches[t].bookRooms[a].togethers[0].sex=i,tt.value.batches[t].bookRooms[a].togethers[0].nation=n)}}));const o=setInterval((()=>{ve.isConnected&&(ve.readIdCard(),clearInterval(o))}),200)}function jt(e){P.confirm(`确定要移除客人"${e.name||"未命名客人"}"吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{!function(e){if(e.togetherCode&&""!==e.togetherCode.trim()){console.log("移除客人，宾客代码:",e.togetherCode);const t={gcode:Xe.gcode,hcode:Xe.hcode,togetherCode:e.togetherCode};console.log("发送移除客人请求，参数:",t),oe.removeTogether(t).then((t=>{0===t.code?(w.success("移除客人成功"),Et(e),Yt(e.togetherCode)):w.error(t.msg||"移除客人失败")})).catch((e=>{var t,o;console.error("移除客人失败:",e),console.error("错误详情:",e.response||e.message||e),w.error(`移除客人失败: ${(null==(o=null==(t=e.response)?void 0:t.data)?void 0:o.msg)||e.message||"未知错误"}`)}))}else console.log("移除未保存的客人"),Et(e),Yt(""),w.success("移除客人成功")}(e)})).catch((()=>{console.log("用户取消移除客人操作")}))}function Et(e){for(let t=0;t<tt.value.batches.length;t++)for(let o=0;o<tt.value.batches[t].bookRooms.length;o++)for(let a=0;a<tt.value.batches[t].bookRooms[o].togethers.length;a++){const r=tt.value.batches[t].bookRooms[o].togethers[a];if(e.togetherCode&&r.togetherCode===e.togetherCode)return tt.value.batches[t].bookRooms[o].togethers.splice(a,1),void console.log("已从前端移除客人，宾客代码:",e.togetherCode);if(!e.togetherCode&&r===e)return tt.value.batches[t].bookRooms[o].togethers.splice(a,1),void console.log("已从前端移除未保存的客人")}}function Yt(e){let t=[];t=e&&""!==e.trim()?Object.keys(Gt.value).filter((t=>t.includes(e))):Object.keys(Gt.value).filter((e=>e.includes("_new_"))),t.forEach((e=>{delete Gt.value[e]})),console.log("已清理原始数据记录:",t)}const Gt=r({});function $t(e,t){switch(e){case ce.IDCERT:return function(e){if(!e||""===e.trim())return{isValid:!0,message:""};const t=e.trim().toUpperCase();if(18===t.length){if(!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/i.test(t))return{isValid:!1,message:"身份证号码格式不正确"};const e=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],o=["1","0","X","9","8","7","6","5","4","3","2"];let a=0;for(let u=0;u<17;u++)a+=Number.parseInt(t[u])*e[u];const r=o[a%11];if(t[17]!==r)return{isValid:!1,message:"身份证号码校验位不正确"};const s=Number.parseInt(t.substring(6,10)),i=Number.parseInt(t.substring(10,12)),n=Number.parseInt(t.substring(12,14)),l=(new Date).getFullYear();if(s<1900||s>l)return{isValid:!1,message:"身份证号码中的年份不正确"};if(i<1||i>12)return{isValid:!1,message:"身份证号码中的月份不正确"};if(n<1||n>31)return{isValid:!1,message:"身份证号码中的日期不正确"};const d=new Date(s,i-1,n);return d.getFullYear()!==s||d.getMonth()!==i-1||d.getDate()!==n?{isValid:!1,message:"身份证号码中的日期不存在"}:{isValid:!0,message:"身份证号码格式正确"}}if(15===t.length){if(!/^[1-9]\d{7}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}$/.test(t))return{isValid:!1,message:"身份证号码格式不正确"};const e=1900+Number.parseInt(t.substring(6,8)),o=Number.parseInt(t.substring(8,10)),a=Number.parseInt(t.substring(10,12));return e<1900||e>1999?{isValid:!1,message:"身份证号码中的年份不正确"}:o<1||o>12?{isValid:!1,message:"身份证号码中的月份不正确"}:a<1||a>31?{isValid:!1,message:"身份证号码中的日期不正确"}:{isValid:!0,message:"身份证号码格式正确"}}return{isValid:!1,message:"身份证号码长度不正确，应为15位或18位"}}(t);case ce.PASSPORT:return function(e){if(!e||""===e.trim())return{isValid:!0,message:""};const t=e.trim().toUpperCase();return/^[A-Z0-9]{8,9}$/.test(t)?{isValid:!0,message:"护照号码格式正确"}:{isValid:!1,message:"护照号码格式不正确，应为8-9位字母和数字组合"}}(t);case ce.OTHER:default:return function(e){if(!e||""===e.trim())return{isValid:!0,message:""};const t=e.trim();return/^[A-Z0-9\-()]{1,20}$/i.test(t)?{isValid:!0,message:"证件号码格式正确"}:{isValid:!1,message:"证件号码格式不正确"}}(t)}}function qt(e){if(!e.idNo||""===e.idNo.trim())return;const t=$t(e.idType||ce.IDCERT,e.idNo);clearTimeout(e._validationTimer),e._validationTimer=setTimeout((()=>{t.isValid}),1e3)}function Lt(e){if(!e.idNo||""===e.idNo.trim())return"";return $t(e.idType||ce.IDCERT,e.idNo).isValid?"id-number-valid":"id-number-invalid"}function Ht(e,t,o){e.idNo&&""!==e.idNo.trim()&&qt(e),Wt(e,t,o)}const Kt=r({});function Ft(e,t){if(!["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(e.key))return;const o=Kt.value[t];if(!o||!o.$el)return;const a=o.$el.querySelector("input")||o.$el,r=a.value||"",s=a.selectionStart||0,i=a.selectionEnd||0;if(!(()=>{switch(e.key){case"ArrowLeft":return 0===s&&0===i;case"ArrowRight":return s===r.length&&i===r.length;case"ArrowUp":case"ArrowDown":return!0;default:return!1}})())return;e.preventDefault();const[,n,l,d,u]=t.split("_"),c=Number.parseInt(n),m=Number.parseInt(l),p=Number.parseInt(d),b=["name","phone","idNo"],g=b.indexOf(u);let h="";switch(e.key){case"ArrowLeft":if(g>0){h=zt(c,m,p,b[g-1])}else h=function(e,t,o){const a=Object.keys(Kt.value).filter((e=>{const t=Kt.value[e];return t&&t.$el&&!t.disabled})).sort(((e,t)=>{const[,o,a,r,s]=e.split("_"),[,i,n,l,d]=t.split("_"),u=Number.parseInt(o),c=Number.parseInt(i),m=Number.parseInt(a),p=Number.parseInt(n),b=Number.parseInt(r),g=Number.parseInt(l);if(u!==c)return u-c;if(m!==p)return m-p;if(b!==g)return b-g;const h=["name","phone","idNo"];return h.indexOf(s)-h.indexOf(d)})),r=zt(e,t,o,"name"),s=a.indexOf(r);if(s>0)return a[s-1];return a[a.length-1]}(c,m,p);break;case"ArrowRight":if(g<b.length-1){h=zt(c,m,p,b[g+1])}else h=function(e,t,o){const a=Object.keys(Kt.value).filter((e=>{const t=Kt.value[e];return t&&t.$el&&!t.disabled})).sort(((e,t)=>{const[,o,a,r,s]=e.split("_"),[,i,n,l,d]=t.split("_"),u=Number.parseInt(o),c=Number.parseInt(i),m=Number.parseInt(a),p=Number.parseInt(n),b=Number.parseInt(r),g=Number.parseInt(l);if(u!==c)return u-c;if(m!==p)return m-p;if(b!==g)return b-g;const h=["name","phone","idNo"];return h.indexOf(s)-h.indexOf(d)})),r=zt(e,t,o,"idNo"),s=a.indexOf(r);if(s<a.length-1)return a[s+1];return a[0]}(c,m,p);break;case"ArrowUp":h=function(e,t,o,a){if(o>0){const r=zt(e,t,o-1,a);if(Kt.value[r])return r}if(t>0){const o=zt(e,t-1,0,a);if(Kt.value[o])return o}if(e>0){if(Object.keys(Kt.value).filter((t=>t.startsWith(`input_${e-1}_`))).length>0){const t=zt(e-1,0,0,a);if(Kt.value[t])return t}}return""}(c,m,p,u);break;case"ArrowDown":h=function(e,t,o,a){const r=zt(e,t,o+1,a);if(Kt.value[r])return r;const s=zt(e,t+1,0,a);if(Kt.value[s])return s;const i=zt(e+1,0,0,a);if(Kt.value[i])return i;return""}(c,m,p,u)}if(h&&Kt.value[h]){const e=Kt.value[h];e&&e.focus&&e.focus()}}function zt(e,t,o,a){return`input_${e}_${t}_${o}_${a}`}function Wt(e,t,o){const a=e=>null!=e&&""!==e&&""!==String(e).trim(),r=a(e.name),s=a(e.phone),i=a(e.idNo);if(!r&&!s&&!i)return;if(i){const t=$t(e.idType||ce.IDCERT,e.idNo||"");if(!t.isValid)return void w.error(t.message)}let n;if(e.togetherCode)n=`${t.orderNo}_${e.togetherCode}`;else{const e=Object.keys(Gt.value).filter((e=>e.startsWith(`${t.orderNo}_new_`)));n=e.length>0?e[0]:`${t.orderNo}_new_0`}const l=Gt.value[n];if(l){const t=(e.name||"")!==(l.name||""),o=(e.phone||"")!==(l.phone||""),a=(e.idNo||"")!==(l.idNo||""),r=(e.idType||"")!==(l.idType||"");if(!(t||o||a||r))return}let d=o;if(!d)for(const c of tt.value.batches){if(c.bookRooms.find((e=>e.orderNo===t.orderNo))){d=c;break}}const u={gcode:Xe.gcode,hcode:Xe.hcode,orderNo:t.orderNo,bookNo:We.bookNo[0],planCheckinTime:t.planCheckinTime,planCheckoutTime:t.planCheckoutTime,rtCode:t.rtCode,rCode:t.rCode||"",rNo:t.rNo||"",name:e.name||"",phone:e.phone||"",idType:e.idType||ce.IDCERT,idNo:e.idNo||"",isMain:e.isMain||"0"};e.togetherCode&&(u.togetherCode=e.togetherCode),oe.bookGuestInfoSave(u).then((o=>{if(0===o.code)if(w.success("修改成功"),o.data&&"string"==typeof o.data&&!e.togetherCode){console.log("接收到新的宾客代码:",o.data),e.togetherCode=o.data;Object.keys(Gt.value).filter((e=>e.startsWith(`${t.orderNo}_new_`))).forEach((e=>{delete Gt.value[e]}));const a=`${t.orderNo}_${o.data}`;Gt.value[a]={name:e.name||"",phone:e.phone||"",idNo:e.idNo||"",idType:e.idType||""},console.log("已更新宾客代码，新key:",a)}else{let o;o=e.togetherCode?`${t.orderNo}_${e.togetherCode}`:n,Gt.value[o]={name:e.name||"",phone:e.phone||"",idNo:e.idNo||"",idType:e.idType||""}}else w.error(o.msg||"保存失败")})).catch((e=>{console.error("保存客人信息失败:",e),w.error("保存失败")}))}const Jt=r({visible:!1,rtCode:"",orderNo:"",orderType:"",initialPriceList:[{bkNum:0,price:0,priceDate:"",priceStrategyCode:"",roomBkNum:0,vipPrice:0,week:0}]});function Qt(e,t){xt()}const Xt=r([]);function Zt(e){Re(e)||(Xt.value=[],Ze.value.loading=!0,setTimeout((async()=>{Ze.value.loading=!1;const t=await async function(e){const t={gcode:Xe.gcode,phone:e.length>11?"":e,idNo:e.length>11?e:"",state:ge.YES},o=await Q.getMemberNoPage(t);if(o&&o.data)return o.data.list}(e);t&&(Xt.value=t)}),200))}function eo(e){const t=Xt.value.filter((t=>t.mcode===e))[0];tt.value.contact=t.name,tt.value.phone=t.phone}const to=r();function oo(e){"agent"===e.target._value&&_t(1),"protocol"===e.target._value&&_t(0),to.value=void 0,Xt.value=[],tt.value.guestName="",tt.value.guestCode=""}const ao=i((()=>{const e=st.value.find((e=>e.code===tt.value.guestSrcType));return e?e.label:""}));async function ro(e){const t=R(e.planCheckinTime).format("YYYY-MM-DD HH:mm"),o=R(e.planCheckoutTime).format("YYYY-MM-DD HH:mm"),a=e.dataList.filter((e=>e.roomNum>0));let r=[];const s=a.map((e=>{e.bookRooms&&e.bookRooms.length>0&&(r=e.bookRooms.map((e=>({rCode:e.rCode,rNo:e.rNo,mac:e.mac,lockVersion:e.lockVersion}))));const t=e.dayPrices.map((e=>({priceDate:R(e.priceDate).format("YYYY-MM-DD"),week:e.week,bkNum:e.bkNum,roomBkNum:e.roomBkNum,price:e.price,vipPrice:e.vipPrice,priceType:e.priceType,priceStrategyCode:e.priceStrategyCode})));return{rtCode:e.rtCode,roomNum:e.roomNum,isMeetingRoom:e.isMeetingRoom||"0",bookRooms:r,dayPrices:t}})),i={gcode:ot.gcode,hcode:ot.hcode,channelCode:tt.value.channelCode,bookNo:tt.value.bookNo,days:e.days,planCheckinTime:t,planCheckoutTime:o,batchNo:`${e.planCheckinTime.slice(0,10)}/${e.planCheckoutTime.slice(0,10)}`,roomTypes:s};await te.addTeamRoom(i).then((e=>{0===e.code&&(w.success(Qe("messages.addRoomSuccess")),xt(),Je("refresh"))}))}const so=r({visible:!1,bookNo:"",batchNo:"",orderNo:"",rtCode:"",rtName:"",rtState:"",rNos:[],planCheckinTime:"",planCheckoutTime:"",roomNum:0});function io(e){nt.value=!0,so.value.rNos=[],so.value.rtCode=e.rtCode,so.value.rtName=e.rtName,so.value.orderNo=e.orderNo,so.value.batchNo=`${R(e.planCheckinTime).format("YYYY-MM-DD")}/${R(e.planCheckoutTime).format("YYYY-MM-DD")}`,so.value.roomNum=1,so.value.planCheckinTime=R(e.planCheckinTime).format("YYYY-MM-DD HH:mm"),so.value.planCheckoutTime=R(e.planCheckoutTime).format("YYYY-MM-DD HH:mm"),e.rNo&&so.value.rNos.push(e.rNo),so.value.visible=!0}function no(e){xt(),Je("refresh")}function lo(e){const t=at.value.find((t=>t.code===e));return t?t.label:""}function uo(){mt.value&&mt.value.formRef.resetFields();for(const e in ht.value)ht.value[e]=bt[e];ct.value.loading=!1,ct.value.visible=!1}const co=r([]);function mo(){const e={...ht.value};delete e.upgradeRtCode,te.getRoomtype(e).then((({data:e})=>{co.value=e,ht.value.upgradeRtCode=""}))}const po=s([{label:Qe("roomUpgrade"),type:"radio",field:"upgradeMethod",options:{data:[{value:0,label:Qe("freeUpgrade")}],radioOptions:{"onUpdate:modelValue":e=>{mo()}}}},{label:Qe("currentRoomType"),field:"rtCode",type:"select",rules:[{required:!0,message:Qe("validation.currentRoomTypeRequired")}],options:{data:ft,valueKey:"rtCode",labelKey:"rtName",selectOptions:{"onUpdate:modelValue":e=>{mo()}}}},{label:Qe("upgradeRoomType"),field:"upgradeRtCode",slot:"upgradeRtCode"}]);function bo(e,t){if(t)ut.value.includes(e.orderNo)||ut.value.push(e.orderNo);else if(e.togethers&&e.togethers.length>1)ut.value.includes(e.orderNo)||ut.value.push(e.orderNo),w.info(Qe("messages.multipleGuestsCannotCollapse"));else{const t=ut.value.indexOf(e.orderNo);-1!==t&&ut.value.splice(t,1)}}return(t,o)=>{const a=T,r=_,s=I,i=x,n=S,l=V,j=O,Q=G,X=$,Z=U,ee=M,ae=q,re=L,se=H,ie=K,ne=F,le=z,de=W,he=J,ve=A,ye=e;return d(),u(N,null,[c("div",null,[m(ee,{shadow:"never"},{header:p((()=>[c("div",_e,[c("span",null,b(g(Qe)("checkinInfo"))+"（"+b(g(Qe)("bookingNo"))+":"+b(tt.value.bookNo)+"）",1),St.value?v("",!0):(d(),h(a,{key:0,type:"primary",onClick:Vt},{default:p((()=>[f(b(g(Qe)("modifyOrder")),1)])),_:1})),St.value?(d(),u("div",Ie,[m(a,{type:"info",onClick:o[0]||(o[0]=e=>St.value=!1)},{default:p((()=>[f(b(g(Qe)("cancel")),1)])),_:1}),m(a,{type:"primary",onClick:Ot},{default:p((()=>[f(b(g(Qe)("saveChanges")),1)])),_:1})])):v("",!0)])])),default:p((()=>[St.value?v("",!0):(d(),h(n,{key:0,"label-width":"180px","label-position":"right","label-suffix":"："},{default:p((()=>[m(i,null,{default:p((()=>[m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("teamName")},{default:p((()=>[f(b(tt.value.teamName),1)])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("checkinType")},{default:p((()=>[f(b(tt.value.checkinTypeName),1)])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("contractNo")},{default:p((()=>[f(b(tt.value.contractNo),1)])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("salesperson")},{default:p((()=>[f(b(tt.value.seller),1)])),_:1},8,["label"])])),_:1})])),_:1}),m(i,null,{default:p((()=>[m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("guestSourceType")},{default:p((()=>[f(b(g(ao)),1)])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("guaranteeMethod")},{default:p((()=>[f(b(tt.value.guarantyStyleName),1)])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("contact")},{default:p((()=>[f(b(tt.value.contact),1)])),_:1},8,["label"])])),_:1})])),_:1}),m(i,null,{default:p((()=>[m(s,{span:24},{default:p((()=>[m(r,{label:g(Qe)("remark")},{default:p((()=>[f(b(tt.value.remark),1)])),_:1},8,["label"])])),_:1})])),_:1})])),_:1})),St.value?(d(),h(n,{key:1,ref_key:"formRef",ref:et,model:tt.value,rules:Nt.value,"label-width":"180px","label-position":"right","label-suffix":"："},{default:p((()=>[m(i,null,{default:p((()=>[m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("teamName"),prop:"teamName"},{default:p((()=>[m(g(y),{modelValue:tt.value.teamName,"onUpdate:modelValue":o[1]||(o[1]=e=>tt.value.teamName=e),maxlength:"30",style:{width:"200px"}},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("checkinType")},{default:p((()=>[m(j,{modelValue:tt.value.checkinType,"onUpdate:modelValue":o[2]||(o[2]=e=>tt.value.checkinType=e)},{default:p((()=>[m(l,{value:g(me).TRAVEL_GROUP},{default:p((()=>[f(b(g(Qe)("travelGroup")),1)])),_:1},8,["value"]),m(l,{value:g(me).MEETING_GROUP},{default:p((()=>[f(b(g(Qe)("meetingGroup")),1)])),_:1},8,["value"])])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("guestSourceType")},{default:p((()=>[m(j,{modelValue:tt.value.guestSrcType,"onUpdate:modelValue":o[3]||(o[3]=e=>tt.value.guestSrcType=e)},{default:p((()=>[(d(!0),u(N,null,k(st.value,(e=>(d(),h(Q,{key:e.code,value:e.code,label:e.label,onChange:oo},null,8,["value","label"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[tt.value.guestSrcType===g(pe).MEMBER?(d(),h(r,{key:0,prop:"guestCode",label:g(Qe)("member")},{default:p((()=>[m(Z,{modelValue:tt.value.guestCode,"onUpdate:modelValue":o[4]||(o[4]=e=>tt.value.guestCode=e),filterable:"",remote:"","reserve-keyword":"",placeholder:g(Qe)("phone"),"remote-method":Zt,loading:Ze.value.loading,style:{width:"200px"},onChange:eo},{default:p((()=>[(d(!0),u(N,null,k(Xt.value,(e=>(d(),h(X,{key:e.mcode,label:e.name,value:e.mcode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder","loading"])])),_:1},8,["label"])):v("",!0),tt.value.guestSrcType===g(pe).AGENT?(d(),h(r,{key:1,label:g(Qe)("agent"),prop:"guestCode"},{default:p((()=>[m(Z,{modelValue:tt.value.guestCode,"onUpdate:modelValue":o[5]||(o[5]=e=>tt.value.guestCode=e),placeholder:g(Qe)("selectAgent"),filterable:"",style:{width:"200px"}},{default:p((()=>[(d(!0),u(N,null,k(Rt.value,(e=>(d(),h(X,{key:e.paCode,label:e.paName,value:e.paCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])):v("",!0),tt.value.guestSrcType===g(pe).PROTOCOL?(d(),h(r,{key:2,label:g(Qe)("protocolUnit"),prop:"guestCode"},{default:p((()=>[m(Z,{modelValue:tt.value.guestCode,"onUpdate:modelValue":o[6]||(o[6]=e=>tt.value.guestCode=e),filterable:"",style:{width:"200px"},placeholder:g(Qe)("selectProtocolUnit")},{default:p((()=>[(d(!0),u(N,null,k(Rt.value,(e=>(d(),h(X,{key:e.paCode,label:e.paName,value:e.paCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])):v("",!0)])),_:1})])),_:1}),m(i,null,{default:p((()=>[m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("contact")},{default:p((()=>[m(g(y),{modelValue:tt.value.contact,"onUpdate:modelValue":o[7]||(o[7]=e=>tt.value.contact=e),maxlength:"30",style:{width:"200px"}},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("contactPhone")},{default:p((()=>[m(g(y),{modelValue:tt.value.phone,"onUpdate:modelValue":o[8]||(o[8]=e=>tt.value.phone=e),maxlength:"30",style:{width:"200px"}},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("salesperson")},{default:p((()=>[m(Z,{modelValue:tt.value.seller,"onUpdate:modelValue":o[9]||(o[9]=e=>tt.value.seller=e),placeholder:g(Qe)("selectSalesperson"),style:{width:"200px"}},{default:p((()=>[(d(!0),u(N,null,k(It.value,(e=>(d(),h(X,{key:e.username,label:e.nickname,value:e.username},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),m(s,{span:6},{default:p((()=>[m(r,{label:g(Qe)("contractNo")},{default:p((()=>[m(g(y),{modelValue:tt.value.contractNo,"onUpdate:modelValue":o[10]||(o[10]=e=>tt.value.contractNo=e),maxlength:"30",style:{width:"200px"}},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(i,null,{default:p((()=>[m(s,{span:24},{default:p((()=>[m(r,{label:g(Qe)("remark")},{default:p((()=>[m(g(y),{modelValue:tt.value.remark,"onUpdate:modelValue":o[11]||(o[11]=e=>tt.value.remark=e),type:"textarea",rows:3,maxlength:"250",style:{width:"723px"}},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["model","rules"])):v("",!0)])),_:1}),o[20]||(o[20]=c("br",null,null,-1)),m(ee,{shadow:"never"},{header:p((()=>[c("div",xe,[c("span",null,b(g(Qe)("roomGuestInfo")),1),m(a,{type:"primary",onClick:Ut},{default:p((()=>[f(b(g(Qe)("addRoom")),1)])),_:1})])])),default:p((()=>[(d(!0),u(N,null,k(tt.value.batches,((e,r)=>(d(),u("div",{key:r},[c("div",Se,[c("div",null,[m(ae,{type:"primary",size:"small"},{default:p((()=>[f(b(g(Qe)("batch"))+b(t.index+1),1)])),_:1}),c("span",Ve,b(g(R)(e.planCheckinTime).format("YYYY-MM-DD"))+"/"+b(g(R)(e.planCheckoutTime).format("YYYY-MM-DD")),1),m(re,{width:"200","confirm-button-text":g(Qe)("confirm"),"cancel-button-text":g(Qe)("cancel"),title:g(Qe)("confirmDeleteBatch"),onConfirm:t=>function(e){1===tt.value.batches.length?w.warning(Qe("messages.onlyOneBatchCannotDelete")):te.batchDeleteBookRoom({gcode:Xe.gcode,hcode:Xe.hcode,bookNo:We.bookNo[0],batchNo:e.batchNo,teamCode:tt.value.teamCode}).then((e=>{0===e.code&&(xt(),Je("refresh"),w.success(Qe("messages.deleteSuccess")))}))}(e)},{reference:p((()=>[m(a,{type:"danger",size:"small"},{default:p((()=>[f(b(g(Qe)("delete")),1)])),_:1})])),_:2},1032,["confirm-button-text","cancel-button-text","title","onConfirm"])]),c("div",null,[m(a,{type:"warning",onClick:t=>function(e){te.autoArrangeRooms({gcode:Xe.gcode,hcode:Xe.hcode,bookNo:We.bookNo[0],batchNo:e.batchNo}).then((e=>{0===e.code&&(w.success(Qe("messages.arrangeRoomSuccess")),xt())}))}(e)},{default:p((()=>[f(b(g(Qe)("autoArrangeRooms")),1)])),_:2},1032,["onClick"]),m(a,{type:"primary",onClick:t=>async function(e){const t=await At();if(!t)return void w.error(Qe("messages.getSystemTimeFailed"));const o=R(Number(t)),a=R(e.planCheckinTime).startOf("day"),r=R(e.planCheckoutTime).endOf("day");if(o.isBefore(a)||o.isAfter(r))return void w.warning(Qe("messages.currentTimeNotInBatchRange"));const s=e.bookRooms.filter((e=>e.rNo&&e.state===be.IN_BOOKING));if(0===s.length)return void w.warning(Qe("messages.noRoomsToCheckin"));let i=!1,n=!1;const l=[],d=[];for(const c of s)c.togethers.filter((e=>!e.name)).length>0?(i=!0,l.push(c.rNo)):c.togethers.filter((e=>e.idType===ce.IDCERT&&!e.idNo)).length>0&&(n=!0,d.push(c.rNo));if(i)return void w.warning(Qe("messages.batchGuestNameMissing",{rooms:l.join("，")}));if(n){if("1"===Tt.value.needId)return void w.warning(Qe("messages.batchIdNumberMissing",{rooms:d.join("，")}));await P.confirm(Qe("messages.batchIdNumberMissingConfirm",{rooms:d.join("，")}),Qe("messages.tip"),{confirmButtonText:Qe("messages.continue"),cancelButtonText:Qe("messages.cancel"),type:"warning"}).catch((()=>{throw new Error(Qe("messages.userCancelOperation"))}))}const u={gcode:Xe.gcode,hcode:Xe.hcode,bookNo:We.bookNo[0],batchNo:e.batchNo,isSendSms:ge.NO,orders:e.bookRooms.map((e=>({orderNo:e.orderNo,bkNum:e.orderPrices[0].bkNum,persons:e.togethers.map((e=>{const t={name:e.name,sex:e.sex||ue.SECRECY.toString(),nation:e.nation||"",address:e.address||"",phone:e.phone||"",idType:e.idType||ce.OTHER,idNo:e.idNo||"",isMadeCard:e.isMadeCard||"0"};return e.togetherCode&&(t.togetherCode=e.togetherCode),t}))})))};oe.bookCheckIn(u).then((e=>{0===e.code&&(w.success(Qe("messages.batchCheckinSuccess")),xt(),Je("refresh"))}))}(e)},{default:p((()=>[f(b(g(Qe)("batchCheckin")),1)])),_:2},1032,["onClick"])])]),c("div",Oe,[m(ie,{data:e.bookRooms,"expand-row-keys":ut.value,"row-key":"orderNo",style:{"margin-bottom":"10px"},onExpandChange:bo},{default:p((()=>[m(se,{width:"48px",type:"expand"},{default:p((t=>[t.row.togethers&&t.row.togethers.length>1?(d(),h(ie,{key:0,data:t.row.togethers.slice(1),"show-header":!1},{default:p((()=>[m(se,{width:"48px"}),m(se,{"min-width":"120px"}),m(se,{"min-width":"250px"}),m(se,{align:"left","min-width":"240px"},{default:p((a=>[t.row.state===g(be).IN_BOOKING?(d(),u("div",Ue,[m(g(y),{ref_for:!0,ref:e=>Kt.value[zt(r,t.$index,a.$index+1,"name")]=e,modelValue:a.row.name,"onUpdate:modelValue":e=>a.row.name=e,style:{width:"100px","margin-right":"8px"},placeholder:g(Qe)("name"),onBlur:o=>Wt(a.row,t.row,e),onKeydown:e=>Ft(e,zt(r,t.$index,a.$index+1,"name"))},null,8,["modelValue","onUpdate:modelValue","placeholder","onBlur","onKeydown"]),m(g(y),{ref_for:!0,ref:e=>Kt.value[zt(r,t.$index,a.$index+1,"phone")]=e,modelValue:a.row.phone,"onUpdate:modelValue":e=>a.row.phone=e,style:{width:"120px"},placeholder:g(Qe)("phone"),onBlur:o=>Wt(a.row,t.row,e),onKeydown:e=>Ft(e,zt(r,t.$index,a.$index+1,"phone"))},null,8,["modelValue","onUpdate:modelValue","placeholder","onBlur","onKeydown"])])):(d(),u(N,{key:1},[f(b(a.row.name)+"/"+b(a.row.phone)+" ",1),o[18]||(o[18]=c("div",{class:"w-[60px] text-right"},null,-1))],64))])),_:2},1024),m(se,{align:"left","min-width":"440px"},{default:p((o=>[t.row.state===g(be).IN_BOOKING?(d(),u("div",Me,[m(Z,{modelValue:o.row.idType,"onUpdate:modelValue":e=>o.row.idType=e,style:{width:"120px","margin-right":"8px"},onChange:a=>Ht(o.row,t.row,e)},{default:p((()=>[(d(!0),u(N,null,k(at.value,(e=>(d(),h(X,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),m(g(y),{ref_for:!0,ref:e=>Kt.value[zt(r,t.$index,o.$index+1,"idNo")]=e,modelValue:o.row.idNo,"onUpdate:modelValue":e=>o.row.idNo=e,class:D(Lt(o.row)),style:{width:"200px","margin-right":"8px"},placeholder:g(Qe)("idNumber"),onBlur:a=>Wt(o.row,t.row,e),onKeydown:e=>Ft(e,zt(r,t.$index,o.$index+1,"idNo")),onInput:e=>qt(o.row)},null,8,["modelValue","onUpdate:modelValue","class","placeholder","onBlur","onKeydown","onInput"]),o.row.idType===g(ce).IDCERT?(d(),h(a,{key:0,type:"primary",icon:g(B),plain:"",onClick:e=>Pt(o,We)},{default:p((()=>[f(b(g(Qe)("readIdCard")),1)])),_:2},1032,["icon","onClick"])):v("",!0)])):(d(),u("span",Ae,b(lo(o.row.idType))+" / "+b(o.row.idNo),1))])),_:2},1024),m(se,{"min-width":"150px"}),m(se,{align:"center","min-width":"240px"},{default:p((e=>[t.row.state===g(be).IN_BOOKING?(d(),u(N,{key:0},[m(a,{type:"primary",link:"",onClick:e=>Dt(t.row,!1)},{default:p((()=>[f(b(g(Qe)("makeSameRoomCard")),1)])),_:2},1032,["onClick"]),t.row.togethers&&t.row.togethers.length>1?(d(),h(a,{key:0,type:"danger",link:"",onClick:t=>jt(e.row)},{default:p((()=>[f(b(g(Qe)("removeGuest")),1)])),_:2},1032,["onClick"])):v("",!0)],64)):v("",!0)])),_:2},1024)])),_:2},1032,["data"])):v("",!0)])),_:2},1024),(d(),u(N,null,k(gt,(t=>m(se,{key:t.prop,label:t.label,prop:t.prop,"min-width":t.width,align:t.align},{default:p((s=>["room"===t.prop?(d(),u(N,{key:0},[s.row.rNo?(d(),u(N,{key:0},[s.row.state===g(be).IN_BOOKING?(d(),h(ne,{key:0,type:"primary",underline:!1,onClick:e=>io(s.row)},{default:p((()=>[f(b(s.row.rNo),1)])),_:2},1032,["onClick"])):(d(),u(N,{key:1},[f(b(s.row.rNo),1)],64))],64)):(d(),u(N,{key:1},[s.row.state===g(be).IN_BOOKING?(d(),h(ne,{key:0,type:"primary",underline:!1,onClick:e=>io(s.row)},{default:p((()=>[f(b(g(Qe)("arrangeRoom")),1)])),_:2},1032,["onClick"])):v("",!0)],64)),m(ae,{type:"warning",style:{"margin-right":"5px","margin-left":"5px"}},{default:p((()=>[f(b(s.row.rtName),1)])),_:2},1024),s.row.state===g(be).IN_BOOKING?(d(),h(le,{key:2,placement:"right",width:200,trigger:"hover"},{reference:p((()=>[m(ne,{type:"primary",underline:!1,onClick:e=>function(e){if(e.rNo)return void w.warning(Qe("messages.currentOrderArrangedCannotUpgrade"));const t=R(e.planCheckoutTime),o=R(lt.value);if(t.isBefore(o))return w.warning(Qe("messages.orderPastCheckoutTime"));{ct.value.show();const t=E([e],"rtCode").filter((e=>!e.rNo))||[];ft.value=Y(t,"orderPrices"),ht.value.rtCode=e.rtCode,ht.value.orderNo=e.orderNo,mo()}}(s.row)},{default:p((()=>[f(b(g(Qe)("upgrade")),1)])),_:2},1032,["onClick"])])),default:p((()=>[c("div",null,[c("p",null,b(g(Qe)("clickToUpgradeRoomType")),1),s.row.rNo?(d(),u("p",De,b(g(Qe)("roomAlreadyArranged")),1)):(d(),u("p",Be,b(g(Qe)("canUpgradeToHigherRoom")),1))])])),_:2},1024)):v("",!0)],64)):v("",!0),"price"===t.prop?(d(),u("div",Pe,[c("span",je,"￥"+b(s.row.orderPrices[0].vipPrice),1),s.row.state===g(be).IN_BOOKING?(d(),h(ne,{key:0,type:"primary",underline:!1,onClick:e=>{return t=s.row,Jt.value.rtCode=t.rtCode,Jt.value.orderNo=t.orderNo,Jt.value.orderType=fe.ORDER,Jt.value.initialPriceList=t.orderPrices,void(Jt.value.visible=!0);var t}},{default:p((()=>[f(b(g(Qe)("changePrice")),1)])),_:2},1032,["onClick"])):v("",!0)])):v("",!0),"namePhone"===t.prop?(d(),u("div",Ee,[c("div",Ye,[s.row.state===g(be).IN_BOOKING?(d(),u(N,{key:0},[m(g(y),{ref_for:!0,ref:e=>Kt.value[zt(r,s.$index,0,"name")]=e,modelValue:s.row.togethers[0].name,"onUpdate:modelValue":e=>s.row.togethers[0].name=e,style:{width:"100px","margin-right":"8px"},placeholder:g(Qe)("name"),onBlur:t=>Wt(s.row.togethers[0],s.row,e),onKeydown:e=>Ft(e,zt(r,s.$index,0,"name"))},null,8,["modelValue","onUpdate:modelValue","placeholder","onBlur","onKeydown"]),m(g(y),{ref_for:!0,ref:e=>Kt.value[zt(r,s.$index,0,"phone")]=e,modelValue:s.row.togethers[0].phone,"onUpdate:modelValue":e=>s.row.togethers[0].phone=e,style:{width:"120px"},placeholder:g(Qe)("phone"),onBlur:t=>Wt(s.row.togethers[0],s.row,e),onKeydown:e=>Ft(e,zt(r,s.$index,0,"phone"))},null,8,["modelValue","onUpdate:modelValue","placeholder","onBlur","onKeydown"])],64)):(d(),u(N,{key:1},[f(b(s.row.togethers[0].name)+" / "+b(s.row.togethers[0].phone),1)],64))]),o[19]||(o[19]=c("div",{class:"w-[60px] text-right"},null,-1))])):v("",!0),"CardNumber"===t.prop?(d(),u(N,{key:3},[s.row.state===g(be).IN_BOOKING?(d(),u("div",Ge,[m(Z,{modelValue:s.row.togethers[0].idType,"onUpdate:modelValue":e=>s.row.togethers[0].idType=e,style:{width:"120px","margin-right":"8px"},onChange:t=>Ht(s.row.togethers[0],s.row,e)},{default:p((()=>[(d(!0),u(N,null,k(at.value,(e=>(d(),h(X,{key:e.code,label:e.label,value:e.code},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),m(g(y),{ref_for:!0,ref:e=>Kt.value[zt(r,s.$index,0,"idNo")]=e,modelValue:s.row.togethers[0].idNo,"onUpdate:modelValue":e=>s.row.togethers[0].idNo=e,class:D(Lt(s.row.togethers[0])),style:{width:"200px","margin-right":"8px"},placeholder:g(Qe)("idNumber"),onBlur:t=>Wt(s.row.togethers[0],s.row,e),onKeydown:e=>Ft(e,zt(r,s.$index,0,"idNo")),onInput:e=>qt(s.row.togethers[0])},null,8,["modelValue","onUpdate:modelValue","class","placeholder","onBlur","onKeydown","onInput"]),s.row.togethers[0].idType===g(ce).IDCERT?(d(),h(a,{key:0,type:"primary",icon:g(B),plain:"",onClick:e=>Pt(s)},{default:p((()=>[f(b(g(Qe)("readIdCard")),1)])),_:2},1032,["icon","onClick"])):v("",!0)])):(d(),u(N,{key:1},[s.row.togethers&&s.row.togethers.length>0?(d(),u("span",$e,[""===s.row.togethers[0].idNo?(d(),u(N,{key:0},[f(b(g(Qe)("noIdInfo")),1)],64)):(d(),u("span",qe,b(lo(s.row.togethers[0].idType))+" / "+b(s.row.togethers[0].idNo),1))])):v("",!0)],64))],64)):v("",!0),"bk"===t.prop?(d(),u(N,{key:4},[s.row.state===g(be).IN_BOOKING?(d(),h(de,{key:0,modelValue:s.row.orderPrices[0].bkNum,"onUpdate:modelValue":e=>s.row.orderPrices[0].bkNum=e,style:{width:"120px"},min:0,max:100,step:1,"step-strictly":"","value-on-clear":0},null,8,["modelValue","onUpdate:modelValue"])):(d(),u(N,{key:1},[f(b(s.row.orderPrices[0].bkNum)+" 份 ",1)],64))],64)):v("",!0),"cz"===t.prop?(d(),u("div",Le,[s.row.state===g(be).IN_BOOKING?(d(),u(N,{key:0},[m(a,{type:"primary",link:"",onClick:e=>Dt(s.row,!0)},{default:p((()=>[f(b(g(Qe)("makeNewCard")),1)])),_:2},1032,["onClick"]),m(a,{type:"primary",link:"",onClick:e=>async function(e){if(!e.row.rNo)return void w.warning(Qe("messages.checkinBeforeArrangeRoom"));const t=await At();if(!t)return void w.error(Qe("messages.getSystemTimeFailed"));const o=R(Number(t)),a=R(e.row.planCheckinTime).startOf("day"),r=R(e.row.planCheckoutTime).endOf("day");if(o.isBefore(a)||o.isAfter(r))return void w.warning(Qe("messages.currentTimeNotInBatchRange"));const s=e.row.togethers.filter((e=>!e.name));if(s.length>0)return void w.warning(Qe("messages.guestNameMissingInRoom",{roomNo:e.row.rNo,count:s.length}));const i=e.row.togethers.filter((e=>e.idType===ce.IDCERT&&!e.idNo));if(i.length>0){if("1"===Tt.value.needId)return void w.warning(Qe("messages.idNumberMissingInRoom",{roomNo:e.row.rNo,count:i.length}));try{await P.confirm(Qe("messages.idNumberMissingConfirm",{roomNo:e.row.rNo,count:i.length}),Qe("messages.tip"),{confirmButtonText:Qe("messages.continue"),cancelButtonText:Qe("messages.cancel"),type:"warning"})}catch(l){return}}const n={gcode:Xe.gcode,hcode:Xe.hcode,bookNo:We.bookNo[0],batchNo:`${R(e.row.planCheckinTime).format("YYYY-MM-DD")}/${R(e.row.planCheckoutTime).format("YYYY-MM-DD")}`,isSendSms:ge.NO,orders:[{orderNo:e.row.orderNo,bkNum:e.row.orderPrices[0].bkNum,persons:e.row.togethers.map((e=>{const t={name:e.name,sex:e.sex||ue.SECRECY.toString(),nation:e.nation||"",address:e.address||"",phone:e.phone||"",idType:e.idType||ce.IDCERT,idNo:e.idNo||"",isMadeCard:e.isMadeCard||"0"};return e.togetherCode&&(t.togetherCode=e.togetherCode),t}))}]};oe.bookCheckIn(n).then((e=>{0===e.code&&(w.success(Qe("messages.checkinSuccess")),xt(),Je("refresh"))}))}(s)},{default:p((()=>[f(b(g(Qe)("checkin")),1)])),_:2},1032,["onClick"]),m(a,{type:"primary",link:"",onClick:e=>function(e){for(let t=0;t<tt.value.batches.length;t++){const o=tt.value.batches[t];for(let t=0;t<o.bookRooms.length;t++){const a=o.bookRooms[t];if(a.orderNo===e.row.orderNo){const e={togetherCode:"",rCode:"",rNo:"",sex:ue.SECRECY.toString(),nation:"",address:"",name:"",phone:"",idType:ce.IDCERT,idNo:"",isMadeCard:"0",isMain:"0",state:""};a.togethers.push(e);const t=a.togethers.length-1,o=`${a.orderNo}_new_${t}`;Gt.value[o]={name:"",phone:"",idNo:"",idType:ce.IDCERT},ut.value.includes(a.orderNo)||ut.value.push(a.orderNo)}}}}(s)},{default:p((()=>[f(b(g(Qe)("addRoommate")),1)])),_:2},1032,["onClick"]),m(re,{width:"200","confirm-button-text":g(Qe)("confirm"),"cancel-button-text":g(Qe)("cancel"),title:g(Qe)("confirmRemoveRoom"),onConfirm:e=>function(e){let t=0;tt.value.batches.forEach((e=>{t+=e.bookRooms.length})),1!==t?te.deleteBookRoom({gcode:Xe.gcode,hcode:Xe.hcode,orderNo:e.orderNo}).then((e=>{0===e.code&&(w.success(Qe("messages.removeRoomSuccess")),xt(),Je("refresh"))})):w.warning(Qe("messages.atLeastOneRoomRequired"))}(s.row)},{reference:p((()=>[m(a,{type:"danger",link:""},{default:p((()=>[f(b(g(Qe)("removeRoom")),1)])),_:1})])),_:2},1032,["confirm-button-text","cancel-button-text","title","onConfirm"])],64)):(d(),h(a,{key:1,type:"primary",text:"",onClick:e=>function(e){const t=e.togethers.find((e=>e.isMain===ge.YES));Je("seeDetailed",{orderNo:e.orderNo,state:e.state,togetherCode:t.togetherCode})}(s.row)},{default:p((()=>[f(b(g(Qe)("viewOrder")),1)])),_:2},1032,["onClick"]))])):v("",!0)])),_:2},1032,["label","prop","min-width","align"]))),64))])),_:2},1032,["data","expand-row-keys"])])])))),128))])),_:1})]),pt.value?(d(),h(Ce,{key:0,modelValue:pt.value,"onUpdate:modelValue":o[12]||(o[12]=e=>pt.value=e),"channel-code":tt.value.channelCode,"checkin-type":tt.value.checkinType,"guest-src-type":tt.value.guestSrcType,"guest-code":tt.value.guestCode,onSuccess:ro},null,8,["modelValue","channel-code","checkin-type","guest-src-type","guest-code"])):v("",!0),Jt.value.visible?(d(),h(we,{key:1,modelValue:Jt.value.visible,"onUpdate:modelValue":o[13]||(o[13]=e=>Jt.value.visible=e),"room-list-price":Jt.value.initialPriceList,"rt-code":Jt.value.rtCode,"order-no":Jt.value.orderNo,"order-type":Jt.value.orderType,onSuccess:Qt},null,8,["modelValue","room-list-price","rt-code","order-no","order-type"])):v("",!0),so.value.visible?(d(),h(ke,C({key:2,modelValue:so.value.visible,"onUpdate:modelValue":o[14]||(o[14]=e=>so.value.visible=e)},so.value,{"is-alone":nt.value,"book-no":tt.value.bookNo,rooms:it.value,onSuccess:ro,onReload:no}),null,16,["modelValue","is-alone","book-no","rooms"])):v("",!0),m(ye,{ref_key:"easyDialogRef",ref:ct,title:g(Qe)("roomUpgrade"),"is-body":"","dialog-width":"500","show-cancel-button":"","show-confirm-button":ft.value.length>0,onSubmit:o[16]||(o[16]=e=>{mt.value.formRef&&mt.value.formRef.validate((async e=>{e&&0===(await te.putUpgrade(ht.value)).code&&(w.success(Qe("messages.roomUpgradeSuccess")),xt(),uo())}))}),onClose:o[17]||(o[17]=e=>uo())},{default:p((()=>[ft.value.length>0?(d(),h(he,{key:0,ref_key:"easyFormRef",ref:mt,"field-list":g(po),model:ht.value,options:{labelSuffix:"："}},{upgradeRtCode:p((()=>[m(r,{label:g(Qe)("upgradeRoomType"),prop:"upgradeRtCode",rules:[{required:!0,message:g(Qe)("validation.upgradeRoomTypeRequired")}]},{default:p((()=>[m(Z,{modelValue:ht.value.upgradeRtCode,"onUpdate:modelValue":o[15]||(o[15]=e=>ht.value.upgradeRtCode=e),placeholder:g(Qe)("selectUpgradeRoomType")},{default:p((()=>[(d(!0),u(N,null,k(co.value,(e=>(d(),h(X,{key:e.rtCode,label:e.rtName,disabled:0===e.canSellNum&&0===e.canOverNum,value:e.rtCode},{default:p((()=>[c("div",He,[c("div",null,b(e.rtName),1),c("div",Ke,[c("span",Fe,[f(b(g(Qe)("availableRooms"))+"：",1),c("span",{class:D(e.canSellNum>0?"text-[var(--el-color-primary)]":"")},b(e.canSellNum),3)]),c("span",ze,[f(b(g(Qe)("overBookingRooms"))+"：",1),c("span",{class:D(e.canOverNum>0?"text-[var(--el-color-primary)]":"")},b(e.canOverNum),3)])])])])),_:2},1032,["label","disabled","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label","rules"])])),_:1},8,["field-list","model"])):(d(),h(ve,{key:1,description:g(Qe)("orderAlreadyArrangedCannotUpgrade")},null,8,["description"]))])),_:1},8,["title","show-confirm-button"])],64)}}});function Je(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{"zh-cn":{checkinInfo:{t:0,b:{t:2,i:[{t:3}],s:"入住信息"}},bookingNo:{t:0,b:{t:2,i:[{t:3}],s:"预订单号"}},modifyOrder:{t:0,b:{t:2,i:[{t:3}],s:"修改订单"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},saveChanges:{t:0,b:{t:2,i:[{t:3}],s:"保存修改"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"团队名称"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},contractNo:{t:0,b:{t:2,i:[{t:3}],s:"合同编号"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"销售员"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"客源类型"}},guaranteeMethod:{t:0,b:{t:2,i:[{t:3}],s:"担保方式"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"联系人"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"联系电话"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},roomGuestInfo:{t:0,b:{t:2,i:[{t:3}],s:"房间/住客信息"}},addRoom:{t:0,b:{t:2,i:[{t:3}],s:"添加房间"}},batch:{t:0,b:{t:2,i:[{t:3}],s:"批次"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"删除"}},confirmDeleteBatch:{t:0,b:{t:2,i:[{t:3}],s:"确定删除该批次吗？"}},autoArrangeRooms:{t:0,b:{t:2,i:[{t:3}],s:"AI自动排房"}},batchCheckin:{t:0,b:{t:2,i:[{t:3}],s:"批量入住"}},arrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"排房"}},upgrade:{t:0,b:{t:2,i:[{t:3}],s:"升级"}},changePrice:{t:0,b:{t:2,i:[{t:3}],s:"改价"}},name:{t:0,b:{t:2,i:[{t:3}],s:"姓名"}},phone:{t:0,b:{t:2,i:[{t:3}],s:"手机号"}},idType:{t:0,b:{t:2,i:[{t:3}],s:"证件类型"}},idNumber:{t:0,b:{t:2,i:[{t:3}],s:"证件号码"}},readIdCard:{t:0,b:{t:2,i:[{t:3}],s:"读身份证"}},makeNewCard:{t:0,b:{t:2,i:[{t:3}],s:"制新卡"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"入住"}},addRoommate:{t:0,b:{t:2,i:[{t:3}],s:"添加同住"}},removeRoom:{t:0,b:{t:2,i:[{t:3}],s:"移除房间"}},confirmRemoveRoom:{t:0,b:{t:2,i:[{t:3}],s:"确定移除该房间吗？"}},viewOrder:{t:0,b:{t:2,i:[{t:3}],s:"查看订单"}},makeSameRoomCard:{t:0,b:{t:2,i:[{t:3}],s:"制同住卡"}},removeGuest:{t:0,b:{t:2,i:[{t:3}],s:"移除客人"}},roomUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"房型升级"}},currentRoomType:{t:0,b:{t:2,i:[{t:3}],s:"当前房型"}},upgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"升级房型"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"免费升级"}},selectUpgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"请选择升级房型"}},availableRooms:{t:0,b:{t:2,i:[{t:3}],s:"可售数"}},overBookingRooms:{t:0,b:{t:2,i:[{t:3}],s:"可超数"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"确定"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"旅行团"}},meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"会议团"}},member:{t:0,b:{t:2,i:[{t:3}],s:"会员"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},selectAgent:{t:0,b:{t:2,i:[{t:3}],s:"请选择中介"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}},selectProtocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"请选择协议单位"}},selectSalesperson:{t:0,b:{t:2,i:[{t:3}],s:"请选择销售员"}},portions:{t:0,b:{t:2,i:[{t:3}],s:"份"}},noIdInfo:{t:0,b:{t:2,i:[{t:3}],s:"无证件信息"}},roomAlreadyArranged:{t:0,b:{t:2,i:[{t:3}],s:"已排房，需先取消排房"}},canUpgradeToHigherRoom:{t:0,b:{t:2,i:[{t:3}],s:"可以升级到更高级的房型"}},clickToUpgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"点击升级房型"}},orderAlreadyArrangedCannotUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"订单已排房，无法升级，请取消排房后再操作"}},messages:{modifySuccess:{t:0,b:{t:2,i:[{t:3}],s:"修改成功"}},deleteSuccess:{t:0,b:{t:2,i:[{t:3}],s:"删除成功"}},onlyOneBatchCannotDelete:{t:0,b:{t:2,i:[{t:3}],s:"当前预订单只有一个批次，不允许删除"}},arrangeRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"排房成功"}},checkinSuccess:{t:0,b:{t:2,i:[{t:3}],s:"入住成功"}},batchCheckinSuccess:{t:0,b:{t:2,i:[{t:3}],s:"批量入住成功"}},addRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"添加房间成功"}},removeRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"移除房间成功"}},roomUpgradeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"房型升级成功！"}},getSystemTimeFailed:{t:0,b:{t:2,i:[{t:3}],s:"获取系统时间失败"}},currentTimeNotInBatchRange:{t:0,b:{t:2,i:[{t:3}],s:"当前时间不在批次时间范围内，不允许入住"}},noRoomsToCheckin:{t:0,b:{t:2,i:[{t:3}],s:"没有可入住的房间，请先排房"}},guestNameMissing:{t:0,b:{t:2,i:[{t:3}],s:"有入住人姓名缺失，请先填写入住人信息"}},idNumberMissing:{t:0,b:{t:2,i:[{t:3}],s:"有入住人身份证号码为空"}},continueQuestion:{t:0,b:{t:2,i:[{t:3}],s:"是否继续？"}},makeCardBeforeArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"制卡前请先排房"}},hotelNoLockConfig:{t:0,b:{t:2,i:[{t:3}],s:"酒店没有配置门锁，请到房锁配置里选择酒店所用门锁型号，如果没有匹配的门锁型号，请联系服务商。"}},makeCardSuccess:{t:0,b:{t:2,i:[{t:3}],s:"房制卡成功"}},checkinBeforeArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"入住前请先排房"}},atLeastOneRoomRequired:{t:0,b:{t:2,i:[{t:3}],s:"移除失败：预订单中至少需要保留一间房"}},multipleGuestsCannotCollapse:{t:0,b:{t:2,i:[{t:3}],s:"该房间有多位住客，无法折叠"}},orderPastCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"订单已过最晚预离时间，请修改订单预离时间"}},currentOrderArrangedCannotUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"当前订单已经有排房，如需升级房型，请先取消排房"}},downloadClientForCardReading:{t:0,b:{t:2,i:[{t:3}],s:"读取房卡只能在Hotel-Agent中操作，请下载Hotel-Agent客户端。"}},cardInfo:{t:0,b:{t:2,i:[{t:3}],s:"房卡信息"}},guestNameMissingInRoom:{t:0,b:{t:2,i:[{t:3,v:"房号 "},{t:4,k:"roomNo"},{t:3,v:" 有 "},{t:4,k:"count"},{t:3,v:" 位入住人姓名缺失，请先填写入住人信息"}]}},idNumberMissingInRoom:{t:0,b:{t:2,i:[{t:3,v:"房号 "},{t:4,k:"roomNo"},{t:3,v:" 有 "},{t:4,k:"count"},{t:3,v:" 位入住人身份证号码为空"}]}},idNumberMissingConfirm:{t:0,b:{t:2,i:[{t:3,v:"房号 "},{t:4,k:"roomNo"},{t:3,v:" 有 "},{t:4,k:"count"},{t:3,v:" 位入住人身份证号码为空，是否继续？"}]}},batchGuestNameMissing:{t:0,b:{t:2,i:[{t:3,v:"以下房间有入住人姓名缺失："},{t:4,k:"rooms"},{t:3,v:"，请先填写入住人信息"}]}},batchIdNumberMissing:{t:0,b:{t:2,i:[{t:3,v:"以下房间有入住人身份证号码为空："},{t:4,k:"rooms"}]}},batchIdNumberMissingConfirm:{t:0,b:{t:2,i:[{t:3,v:"以下房间有入住人身份证号码为空："},{t:4,k:"rooms"},{t:3,v:"，是否继续？"}]}},userCancelOperation:{t:0,b:{t:2,i:[{t:3}],s:"用户取消操作"}},tip:{t:0,b:{t:2,i:[{t:3}],s:"提示"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"继续"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}}},validation:{teamNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"团队名称不能为空"}},currentRoomTypeRequired:{t:0,b:{t:2,i:[{t:3}],s:"当前房型不能为空！"}},upgradeRoomTypeRequired:{t:0,b:{t:2,i:[{t:3}],s:"升级房型不能为空！"}}},placeholders:{enterName:{t:0,b:{t:2,i:[{t:3}],s:"姓名"}},enterPhone:{t:0,b:{t:2,i:[{t:3}],s:"手机号"}},enterIdNumber:{t:0,b:{t:2,i:[{t:3}],s:"证件号码"}},phoneNumber:{t:0,b:{t:2,i:[{t:3}],s:"手机号"}}},tableColumns:{roomNoRoomType:{t:0,b:{t:2,i:[{t:3}],s:"房号/房型"}},discountPrice:{t:0,b:{t:2,i:[{t:3}],s:"优惠价"}},namePhone:{t:0,b:{t:2,i:[{t:3}],s:"姓名/电话"}},idTypeIdNumber:{t:0,b:{t:2,i:[{t:3}],s:"证件类型/证件号"}},breakfastPortions:{t:0,b:{t:2,i:[{t:3}],s:"赠早/份"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"操作"}}},guestSrcValidation:{memberRequired:{t:0,b:{t:2,i:[{t:3}],s:"会员不能为空"}},agentRequired:{t:0,b:{t:2,i:[{t:3}],s:"中介不能为空"}},protocolRequired:{t:0,b:{t:2,i:[{t:3}],s:"协议单位不能为空"}},selectOrInputRequired:{t:0,b:{t:2,i:[{t:3}],s:"请选择或输入客源代码"}}},teamNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"请输入团队名称"}}},en:{checkinInfo:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Information"}},bookingNo:{t:0,b:{t:2,i:[{t:3}],s:"Booking No"}},modifyOrder:{t:0,b:{t:2,i:[{t:3}],s:"Modify Order"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},saveChanges:{t:0,b:{t:2,i:[{t:3}],s:"Save Changes"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"Team Name"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Type"}},contractNo:{t:0,b:{t:2,i:[{t:3}],s:"Contract No"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"Sales"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source Type"}},guaranteeMethod:{t:0,b:{t:2,i:[{t:3}],s:"Guarantee Method"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"Contact"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"Contact Phone"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},roomGuestInfo:{t:0,b:{t:2,i:[{t:3}],s:"Room/Guest Information"}},addRoom:{t:0,b:{t:2,i:[{t:3}],s:"Add Room"}},batch:{t:0,b:{t:2,i:[{t:3}],s:"Batch"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"Delete"}},confirmDeleteBatch:{t:0,b:{t:2,i:[{t:3}],s:"Are you sure to delete this batch?"}},autoArrangeRooms:{t:0,b:{t:2,i:[{t:3}],s:"AI Auto Arrange Rooms"}},batchCheckin:{t:0,b:{t:2,i:[{t:3}],s:"Batch Check-in"}},arrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"Arrange Room"}},upgrade:{t:0,b:{t:2,i:[{t:3}],s:"Upgrade"}},changePrice:{t:0,b:{t:2,i:[{t:3}],s:"Change Price"}},name:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},phone:{t:0,b:{t:2,i:[{t:3}],s:"Phone"}},idType:{t:0,b:{t:2,i:[{t:3}],s:"ID Type"}},idNumber:{t:0,b:{t:2,i:[{t:3}],s:"ID Number"}},readIdCard:{t:0,b:{t:2,i:[{t:3}],s:"Read ID Card"}},makeNewCard:{t:0,b:{t:2,i:[{t:3}],s:"Make New Card"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"Check-in"}},addRoommate:{t:0,b:{t:2,i:[{t:3}],s:"Add Roommate"}},removeRoom:{t:0,b:{t:2,i:[{t:3}],s:"Remove Room"}},confirmRemoveRoom:{t:0,b:{t:2,i:[{t:3}],s:"Are you sure to remove this room?"}},viewOrder:{t:0,b:{t:2,i:[{t:3}],s:"View Order"}},makeSameRoomCard:{t:0,b:{t:2,i:[{t:3}],s:"Make Same Room Card"}},removeGuest:{t:0,b:{t:2,i:[{t:3}],s:"Remove Guest"}},roomUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"Room Upgrade"}},currentRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Current Room Type"}},upgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Upgrade Room Type"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"Free Upgrade"}},selectUpgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Please select upgrade room type"}},availableRooms:{t:0,b:{t:2,i:[{t:3}],s:"Available"}},overBookingRooms:{t:0,b:{t:2,i:[{t:3}],s:"Overbooking"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"Confirm"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"Travel Group"}},meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"Meeting Group"}},member:{t:0,b:{t:2,i:[{t:3}],s:"Member"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"Agent"}},selectAgent:{t:0,b:{t:2,i:[{t:3}],s:"Please select agent"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"Protocol Unit"}},selectProtocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"Please select protocol unit"}},selectSalesperson:{t:0,b:{t:2,i:[{t:3}],s:"Please select salesperson"}},portions:{t:0,b:{t:2,i:[{t:3}],s:"portions"}},noIdInfo:{t:0,b:{t:2,i:[{t:3}],s:"No ID information"}},roomAlreadyArranged:{t:0,b:{t:2,i:[{t:3}],s:"Room already arranged, need to cancel arrangement first"}},canUpgradeToHigherRoom:{t:0,b:{t:2,i:[{t:3}],s:"Can upgrade to higher room type"}},clickToUpgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Click to upgrade room type"}},orderAlreadyArrangedCannotUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"Order already arranged, cannot upgrade, please cancel arrangement first"}},messages:{modifySuccess:{t:0,b:{t:2,i:[{t:3}],s:"Modified successfully"}},deleteSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Deleted successfully"}},onlyOneBatchCannotDelete:{t:0,b:{t:2,i:[{t:3}],s:"Only one batch exists, cannot delete"}},arrangeRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Room arranged successfully"}},checkinSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Check-in successful"}},batchCheckinSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Batch check-in successful"}},addRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Room added successfully"}},removeRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Room removed successfully"}},roomUpgradeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Room upgrade successful!"}},getSystemTimeFailed:{t:0,b:{t:2,i:[{t:3}],s:"Failed to get system time"}},currentTimeNotInBatchRange:{t:0,b:{t:2,i:[{t:3}],s:"Current time is not within batch time range, check-in not allowed"}},noRoomsToCheckin:{t:0,b:{t:2,i:[{t:3}],s:"No rooms available for check-in, please arrange rooms first"}},guestNameMissing:{t:0,b:{t:2,i:[{t:3}],s:"Guest name missing, please fill in guest information first"}},idNumberMissing:{t:0,b:{t:2,i:[{t:3}],s:"Guest ID number is empty"}},continueQuestion:{t:0,b:{t:2,i:[{t:3}],s:"Continue?"}},makeCardBeforeArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"Please arrange room before making card"}},hotelNoLockConfig:{t:0,b:{t:2,i:[{t:3}],s:"Hotel has no lock configuration, please go to 'Lock Configuration' to select the lock model used by the hotel. If there is no matching lock model, please contact the service provider."}},makeCardSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Card made successfully"}},checkinBeforeArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"Please arrange room before check-in"}},atLeastOneRoomRequired:{t:0,b:{t:2,i:[{t:3}],s:"Removal failed: At least one room must be kept in the booking"}},multipleGuestsCannotCollapse:{t:0,b:{t:2,i:[{t:3}],s:"This room has multiple guests and cannot be collapsed"}},orderPastCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Order has passed the latest checkout time, please modify the order checkout time"}},currentOrderArrangedCannotUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"Current order already has room arrangement, please cancel arrangement first if you need to upgrade room type"}},downloadClientForCardReading:{t:0,b:{t:2,i:[{t:3}],s:"Card reading can only be operated in Hotel-Agent, please download Hotel-Agent client."}},cardInfo:{t:0,b:{t:2,i:[{t:3}],s:"Card Information"}},guestNameMissingInRoom:{t:0,b:{t:2,i:[{t:3,v:"Room "},{t:4,k:"roomNo"},{t:3,v:" has "},{t:4,k:"count"},{t:3,v:" guests with missing names, please fill in guest information first"}]}},idNumberMissingInRoom:{t:0,b:{t:2,i:[{t:3,v:"Room "},{t:4,k:"roomNo"},{t:3,v:" has "},{t:4,k:"count"},{t:3,v:" guests with empty ID numbers"}]}},idNumberMissingConfirm:{t:0,b:{t:2,i:[{t:3,v:"Room "},{t:4,k:"roomNo"},{t:3,v:" has "},{t:4,k:"count"},{t:3,v:" guests with empty ID numbers, continue?"}]}},batchGuestNameMissing:{t:0,b:{t:2,i:[{t:3,v:"The following rooms have guests with missing names: "},{t:4,k:"rooms"},{t:3,v:", please fill in guest information first"}]}},batchIdNumberMissing:{t:0,b:{t:2,i:[{t:3,v:"The following rooms have guests with empty ID numbers: "},{t:4,k:"rooms"}]}},batchIdNumberMissingConfirm:{t:0,b:{t:2,i:[{t:3,v:"The following rooms have guests with empty ID numbers: "},{t:4,k:"rooms"},{t:3,v:", continue?"}]}},userCancelOperation:{t:0,b:{t:2,i:[{t:3}],s:"User cancelled operation"}},tip:{t:0,b:{t:2,i:[{t:3}],s:"Tip"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"Continue"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}}},validation:{teamNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"Team name is required"}},currentRoomTypeRequired:{t:0,b:{t:2,i:[{t:3}],s:"Current room type is required!"}},upgradeRoomTypeRequired:{t:0,b:{t:2,i:[{t:3}],s:"Upgrade room type is required!"}}},placeholders:{enterName:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},enterPhone:{t:0,b:{t:2,i:[{t:3}],s:"Phone"}},enterIdNumber:{t:0,b:{t:2,i:[{t:3}],s:"ID Number"}},phoneNumber:{t:0,b:{t:2,i:[{t:3}],s:"Phone Number"}}},tableColumns:{roomNoRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Room No/Room Type"}},discountPrice:{t:0,b:{t:2,i:[{t:3}],s:"Discount Price"}},namePhone:{t:0,b:{t:2,i:[{t:3}],s:"Name/Phone"}},idTypeIdNumber:{t:0,b:{t:2,i:[{t:3}],s:"ID Type/ID Number"}},breakfastPortions:{t:0,b:{t:2,i:[{t:3}],s:"Breakfast/Portions"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"Operations"}}},guestSrcValidation:{memberRequired:{t:0,b:{t:2,i:[{t:3}],s:"Member cannot be empty"}},agentRequired:{t:0,b:{t:2,i:[{t:3}],s:"Agent cannot be empty"}},protocolRequired:{t:0,b:{t:2,i:[{t:3}],s:"Protocol unit cannot be empty"}},selectOrInputRequired:{t:0,b:{t:2,i:[{t:3}],s:"Please select or enter guest source code"}}},teamNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"Please enter team name"}}},km:{checkinInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានចូលស្នាក់នៅ"}},bookingNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មង់"}},modifyOrder:{t:0,b:{t:2,i:[{t:3}],s:"កែប្រែកម្មង់"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},saveChanges:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុកការផ្លាស់ប្តូរ"}},teamName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះក្រុម"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលស្នាក់នៅ"}},contractNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខកិច្ចសន្យា"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកលក់"}},guestSourceType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទប្រភពភ្ញៀវ"}},guaranteeMethod:{t:0,b:{t:2,i:[{t:3}],s:"វិធីធានា"}},contact:{t:0,b:{t:2,i:[{t:3}],s:"ទំនាក់ទំនង"}},contactPhone:{t:0,b:{t:2,i:[{t:3}],s:"លេខទូរស័ព្ទទំនាក់ទំនង"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}},roomGuestInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានបន្ទប់/ភ្ញៀវ"}},addRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែមបន្ទប់"}},batch:{t:0,b:{t:2,i:[{t:3}],s:"ជំនាន់"}},delete:{t:0,b:{t:2,i:[{t:3}],s:"លុប"}},confirmDeleteBatch:{t:0,b:{t:2,i:[{t:3}],s:"តើអ្នកប្រាកដថាចង់លុបជំនាន់នេះមែនទេ?"}},autoArrangeRooms:{t:0,b:{t:2,i:[{t:3}],s:"AI ចាត់ចងបន្ទប់ស្វ័យប្រវត្តិ"}},batchCheckin:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅជាបាច់"}},arrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"ចាត់ចងបន្ទប់"}},upgrade:{t:0,b:{t:2,i:[{t:3}],s:"ដំឡើងកំណែ"}},changePrice:{t:0,b:{t:2,i:[{t:3}],s:"ផ្លាស់ប្តូរតម្លៃ"}},name:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},phone:{t:0,b:{t:2,i:[{t:3}],s:"លេខទូរស័ព្ទ"}},idType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទអត្តសញ្ញាណប័ណ្ណ"}},idNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខអត្តសញ្ញាណប័ណ្ណ"}},readIdCard:{t:0,b:{t:2,i:[{t:3}],s:"អានអត្តសញ្ញាណប័ណ្ណ"}},makeNewCard:{t:0,b:{t:2,i:[{t:3}],s:"ធ្វើកាតថ្មី"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅ"}},addRoommate:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែមដៃគូបន្ទប់"}},removeRoom:{t:0,b:{t:2,i:[{t:3}],s:"ដកបន្ទប់ចេញ"}},confirmRemoveRoom:{t:0,b:{t:2,i:[{t:3}],s:"តើអ្នកប្រាកដថាចង់ដកបន្ទប់នេះចេញមែនទេ?"}},viewOrder:{t:0,b:{t:2,i:[{t:3}],s:"មើលកម្មង់"}},makeSameRoomCard:{t:0,b:{t:2,i:[{t:3}],s:"ធ្វើកាតបន្ទប់ដូចគ្នា"}},removeGuest:{t:0,b:{t:2,i:[{t:3}],s:"ដកភ្ញៀវចេញ"}},roomUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"ដំឡើងកំណែបន្ទប់"}},currentRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់បច្ចុប្បន្ន"}},upgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់ដំឡើងកំណែ"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"ដំឡើងកំណែឥតគិតថ្លៃ"}},selectUpgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទបន្ទប់ដំឡើងកំណែ"}},availableRooms:{t:0,b:{t:2,i:[{t:3}],s:"អាចលក់បាន"}},overBookingRooms:{t:0,b:{t:2,i:[{t:3}],s:"អាចលើសបាន"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ជាក់"}},travelGroup:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមទេសចរណ៍"}},meetingGroup:{t:0,b:{t:2,i:[{t:3}],s:"ក្រុមប្រជុំ"}},member:{t:0,b:{t:2,i:[{t:3}],s:"សមាជិក"}},agent:{t:0,b:{t:2,i:[{t:3}],s:"ភ្នាក់ងារ"}},selectAgent:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសភ្នាក់ងារ"}},protocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"ឯកតាពិធីការ"}},selectProtocolUnit:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឯកតាពិធីការ"}},selectSalesperson:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសអ្នកលក់"}},portions:{t:0,b:{t:2,i:[{t:3}],s:"ចំណែក"}},noIdInfo:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានព័ត៌មានអត្តសញ្ញាណប័ណ្ណ"}},roomAlreadyArranged:{t:0,b:{t:2,i:[{t:3}],s:"បានចាត់ចងបន្ទប់រួចហើយ ត្រូវបោះបង់ការចាត់ចងជាមុនសិន"}},canUpgradeToHigherRoom:{t:0,b:{t:2,i:[{t:3}],s:"អាចដំឡើងកំណែទៅប្រភេទបន្ទប់ខ្ពស់ជាងនេះ"}},clickToUpgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ចុចដើម្បីដំឡើងកំណែប្រភេទបន្ទប់"}},orderAlreadyArrangedCannotUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"កម្មង់បានចាត់ចងរួចហើយ មិនអាចដំឡើងកំណែបាន សូមបោះបង់ការចាត់ចងជាមុនសិន"}},messages:{modifySuccess:{t:0,b:{t:2,i:[{t:3}],s:"កែប្រែបានជោគជ័យ"}},deleteSuccess:{t:0,b:{t:2,i:[{t:3}],s:"លុបបានជោគជ័យ"}},onlyOneBatchCannotDelete:{t:0,b:{t:2,i:[{t:3}],s:"កម្មង់បម្រុងនេះមានតែជំនាន់មួយ មិនអនុញ្ញាតឱ្យលុបទេ"}},arrangeRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចាត់ចងបន្ទប់បានជោគជ័យ"}},checkinSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅបានជោគជ័យ"}},batchCheckinSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅជាបាច់បានជោគជ័យ"}},addRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"បន្ថែមបន្ទប់បានជោគជ័យ"}},removeRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ដកបន្ទប់ចេញបានជោគជ័យ"}},roomUpgradeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ដំឡើងកំណែប្រភេទបន្ទប់បានជោគជ័យ!"}},getSystemTimeFailed:{t:0,b:{t:2,i:[{t:3}],s:"បរាជ័យក្នុងការទទួលបានពេលវេលាប្រព័ន្ធ"}},currentTimeNotInBatchRange:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាបច្ចុប្បន្នមិននៅក្នុងចន្លោះពេលជំនាន់ទេ មិនអនុញ្ញាតឱ្យចូលស្នាក់នៅ"}},noRoomsToCheckin:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានបន្ទប់សម្រាប់ចូលស្នាក់នៅ សូមចាត់ចងបន្ទប់ជាមុនសិន"}},guestNameMissing:{t:0,b:{t:2,i:[{t:3}],s:"មានឈ្មោះអ្នកចូលស្នាក់នៅបាត់ សូមបំពេញព័ត៌មានអ្នកចូលស្នាក់នៅជាមុនសិន"}},idNumberMissing:{t:0,b:{t:2,i:[{t:3}],s:"មានលេខអត្តសញ្ញាណប័ណ្ណអ្នកចូលស្នាក់នៅទទេ"}},continueQuestion:{t:0,b:{t:2,i:[{t:3}],s:"តើបន្តទេ?"}},makeCardBeforeArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"ធ្វើកាតមុនសូមចាត់ចងបន្ទប់ជាមុនសិន"}},hotelNoLockConfig:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារមិនបានកំណត់រចនាសម្ព័ន្ធសោ សូមទៅកាន់ការកំណត់រចនាសម្ព័ន្ធសោបន្ទប់ដើម្បីជ្រើសរើសម៉ូដែលសោដែលសណ្ឋាគារប្រើ ប្រសិនបើគ្មានម៉ូដែលសោដែលត្រូវគ្នា សូមទាក់ទងអ្នកផ្តល់សេវាកម្ម។"}},makeCardSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ធ្វើកាតបន្ទប់បានជោគជ័យ"}},checkinBeforeArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅមុនសូមចាត់ចងបន្ទប់ជាមុនសិន"}},atLeastOneRoomRequired:{t:0,b:{t:2,i:[{t:3}],s:"ដកចេញបរាជ័យ៖ កម្មង់បម្រុងត្រូវតែរក្សាបន្ទប់យ៉ាងហោចណាស់មួយ"}},multipleGuestsCannotCollapse:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់នេះមានភ្ញៀវច្រើននាក់ មិនអាចបត់បានទេ"}},orderPastCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"កម្មង់បានកន្លងផុតពេលចេញចុងក្រោយ សូមកែប្រែពេលចេញកម្មង់"}},currentOrderArrangedCannotUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"កម្មង់បច្ចុប្បន្នមានការចាត់ចងបន្ទប់រួចហើយ ប្រសិនបើត្រូវការដំឡើងកំណែប្រភេទបន្ទប់ សូមបោះបង់ការចាត់ចងជាមុនសិន"}},downloadClientForCardReading:{t:0,b:{t:2,i:[{t:3}],s:"ការអានកាតបន្ទប់អាចធ្វើបានតែក្នុង Hotel-Agent ប៉ុណ្ណោះ សូមទាញយក Hotel-Agent client។"}},cardInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានកាត"}},guestNameMissingInRoom:{t:0,b:{t:2,i:[{t:3,v:"បន្ទប់លេខ "},{t:4,k:"roomNo"},{t:3,v:" មានអ្នកចូលស្នាក់នៅ "},{t:4,k:"count"},{t:3,v:" នាក់ដែលបាត់ឈ្មោះ សូមបំពេញព័ត៌មានអ្នកចូលស្នាក់នៅជាមុនសិន"}]}},idNumberMissingInRoom:{t:0,b:{t:2,i:[{t:3,v:"បន្ទប់លេខ "},{t:4,k:"roomNo"},{t:3,v:" មានអ្នកចូលស្នាក់នៅ "},{t:4,k:"count"},{t:3,v:" នាក់ដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ"}]}},idNumberMissingConfirm:{t:0,b:{t:2,i:[{t:3,v:"បន្ទប់លេខ "},{t:4,k:"roomNo"},{t:3,v:" មានអ្នកចូលស្នាក់នៅ "},{t:4,k:"count"},{t:3,v:" នាក់ដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ តើបន្តទេ?"}]}},batchGuestNameMissing:{t:0,b:{t:2,i:[{t:3,v:"បន្ទប់ខាងក្រោមមានអ្នកចូលស្នាក់នៅដែលបាត់ឈ្មោះ៖ "},{t:4,k:"rooms"},{t:3,v:" សូមបំពេញព័ត៌មានអ្នកចូលស្នាក់នៅជាមុនសិន"}]}},batchIdNumberMissing:{t:0,b:{t:2,i:[{t:3,v:"បន្ទប់ខាងក្រោមមានអ្នកចូលស្នាក់នៅដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ៖ "},{t:4,k:"rooms"}]}},batchIdNumberMissingConfirm:{t:0,b:{t:2,i:[{t:3,v:"បន្ទប់ខាងក្រោមមានអ្នកចូលស្នាក់នៅដែលលេខអត្តសញ្ញាណប័ណ្ណទទេ៖ "},{t:4,k:"rooms"},{t:3,v:" តើបន្តទេ?"}]}},userCancelOperation:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកប្រើបានបោះបង់ប្រតិបត្តិការ"}},tip:{t:0,b:{t:2,i:[{t:3}],s:"ជំនួយ"}},continue:{t:0,b:{t:2,i:[{t:3}],s:"បន្ត"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}}},validation:{teamNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះក្រុមមិនអាចទទេបានទេ"}},currentRoomTypeRequired:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់បច្ចុប្បន្នមិនអាចទទេបានទេ!"}},upgradeRoomTypeRequired:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់ដំឡើងកំណែមិនអាចទទេបានទេ!"}}},placeholders:{enterName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},enterPhone:{t:0,b:{t:2,i:[{t:3}],s:"លេខទូរស័ព្ទ"}},enterIdNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខអត្តសញ្ញាណប័ណ្ណ"}},phoneNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខទូរស័ព្ទ"}}},tableColumns:{roomNoRoomType:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់/ប្រភេទបន្ទប់"}},discountPrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃបញ្ចុះតម្លៃ"}},namePhone:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ/ទូរស័ព្ទ"}},idTypeIdNumber:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទអត្តសញ្ញាណប័ណ្ណ/លេខអត្តសញ្ញាណប័ណ្ណ"}},breakfastPortions:{t:0,b:{t:2,i:[{t:3}],s:"អាហារពេលព្រឹក/ចំណែក"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"ប្រតិបត្តិការ"}}},guestSrcValidation:{memberRequired:{t:0,b:{t:2,i:[{t:3}],s:"សមាជិកមិនអាចទទេបានទេ"}},agentRequired:{t:0,b:{t:2,i:[{t:3}],s:"ភ្នាក់ងារមិនអាចទទេបានទេ"}},protocolRequired:{t:0,b:{t:2,i:[{t:3}],s:"ឯកតាពិធីការមិនអាចទទេបានទេ"}},selectOrInputRequired:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើស ឬបញ្ចូលកូដប្រភពភ្ញៀវ"}}},teamNameRequired:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះក្រុម"}}}}})}Je(We);const Qe=Te(We,[["__scopeId","data-v-703c97f9"]]);export{Qe as default};
//# sourceMappingURL=teamBookDetail-CaYBewxN.js.map
