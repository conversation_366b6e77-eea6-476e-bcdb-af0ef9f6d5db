import{b as t,y as e,z as u,A as n}from"./index-CkEhI1Zk.js";function a(t,e){if(t)return t;let u=null!=e?e:"button";return"string"==typeof u&&"button"===u.toLowerCase()?"button":void 0}function o(o,l){let r=t(a(o.value.type,o.value.as));return e((()=>{r.value=a(o.value.type,o.value.as)})),u((()=>{var t;r.value||n(l)&&n(l)instanceof HTMLButtonElement&&(null==(t=n(l))||!t.hasAttribute("type"))&&(r.value="button")})),r}export{o as s};
//# sourceMappingURL=use-resolve-button-type-CUES3UUR.js.map
