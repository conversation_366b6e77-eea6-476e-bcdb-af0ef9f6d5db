{"version": 3, "file": "merchant.api-BtmIsRm3.js", "sources": ["../../src/api/modules/system/merchant/merchant.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/system/merchant'\r\n\r\nexport default {\r\n  /**\r\n   * 获得门店列表\r\n   * @param gcode\r\n   */\r\n  list: (data: any) => api.get(`${BASE_PATH}/simple-list`, { params: data }),\r\n\r\n  /**\r\n   * 获得门店简单信息\r\n   * @param gcode\r\n   */\r\n  getSimpleList: (gcode: string) =>\r\n    api.get(`${BASE_PATH}/simple-list`, {\r\n      params: {\r\n        gcode,\r\n      },\r\n    }),\r\n  /**\r\n   * 获得相同币种的门店简单信息\r\n   * @param gcode\r\n   */\r\n  getSimpleUnitList: (gcode: string, hcode: string) =>\r\n    api.get(`${BASE_PATH}/simple-list-unit`, {\r\n      params: {\r\n        gcode,\r\n        hcode,\r\n      },\r\n    }),\r\n  /**\r\n   * 获得部门下门店\r\n   * @param gcode\r\n   */\r\n  getDepartmentSimpleList: (data: any) =>\r\n    api.get(`admin-api/system/dept/simple-merchant-list`, { params: data }),\r\n\r\n  /**\r\n   * 获得门店分页\r\n   * @param data\r\n   */\r\n  getPage: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    hname?: string\r\n    pinyin?: string\r\n    deptCode?: string\r\n    manageType?: string\r\n    brandCode?: string\r\n    state?: string\r\n    pc?: string[]\r\n    pageNo?: number\r\n    pageSize?: number\r\n  }) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获得门店(包括扩展信息)\r\n   */\r\n  getAll: (data: any) => api.get('/admin-api/system/merchant/get-all', { params: data }),\r\n\r\n  /**\r\n   * 获得门店\r\n   * @param gcode 集团代码\r\n   * @param hcode 门店代码\r\n   */\r\n  get: (hcode: string) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: {\r\n        hcode,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 创建门店\r\n   * @param data\r\n   */\r\n  create: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n\r\n  /**\r\n   * 更新门店\r\n   * @param data\r\n   */\r\n  update: (data: any) => api.put('/admin-api/system/merchant/update', data),\r\n\r\n  /**\r\n   * 获得用户可访问门店\r\n   * @param data\r\n   */\r\n  visitList: (data: any) => api.get('/admin-api/system/merchant/visit-list', { params: data }),\r\n\r\n  /**\r\n   * 获得用户可访问门店\r\n   * @param data\r\n   */\r\n  getMerchant: (data: any) => api.get('/admin-api/system/merchant/get', { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "merchantApi", "list", "data", "api", "get", "params", "getSimpleList", "gcode", "getSimpleUnitList", "hcode", "getDepartmentSimpleList", "getPage", "getAll", "create", "post", "update", "put", "visitList", "getMerchant"], "mappings": "wCAEA,MAAMA,EAAY,4BAEHC,EAAA,CAKbC,KAAOC,GAAcC,EAAIC,IAAI,GAAGL,gBAAyB,CAAEM,OAAQH,IAMnEI,cAAgBC,GACdJ,EAAIC,IAAI,GAAGL,gBAAyB,CAClCM,OAAQ,CACNE,WAONC,kBAAmB,CAACD,EAAeE,IACjCN,EAAIC,IAAI,GAAGL,qBAA8B,CACvCM,OAAQ,CACNE,QACAE,WAONC,wBAA0BR,GACxBC,EAAIC,IAAI,6CAA8C,CAAEC,OAAQH,IAMlES,QAAUT,GAaRC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IAMZU,OAASV,GAAcC,EAAIC,IAAI,qCAAsC,CAAEC,OAAQH,IAO/EE,IAAMK,GACJN,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQ,CACNI,WAQNI,OAASX,GAAcC,EAAIW,KAAK,GAAGf,WAAoBG,EAAM,IAM7Da,OAASb,GAAcC,EAAIa,IAAI,oCAAqCd,GAMpEe,UAAYf,GAAcC,EAAIC,IAAI,wCAAyC,CAAEC,OAAQH,IAMrFgB,YAAchB,GAAcC,EAAIC,IAAI,iCAAkC,CAAEC,OAAQH"}