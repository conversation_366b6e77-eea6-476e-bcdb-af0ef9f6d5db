{"version": 3, "file": "memberLog.api-B8-GBgik.js", "sources": ["../../src/api/modules/member/member/memberLog.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n// 会员充值\r\nexport default {\r\n\r\n  /**\r\n   * 积分日志列表\r\n   * @returns\r\n   */\r\n  pointLoglist: (data: any) => api.get('/admin-api/member/point-log/page', { params: data }),\r\n\r\n  /**\r\n   * 会员储值日志\r\n   * @returns\r\n   */\r\n  rechargeLoglist: (data: any) => api.get('/admin-api/member/recharge-log/page', { params: data }),\r\n\r\n  /**\r\n   * 会员消费日志\r\n   * @returns\r\n   */\r\n  consumeloglist: (data: any) => api.get('/admin-api/member/consume-log/page', { params: data }),\r\n\r\n  /**\r\n   * 会员升级日志\r\n   * @returns\r\n   */\r\n  levelloglist: (data: any) => api.get('/admin-api/member/level-log/page', { params: data }),\r\n\r\n  /**\r\n   * 会员优惠卷列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getcoupon: (data: any) => api.get('/admin-api/marketing/coupon/get-coupon', { params: data }),\r\n\r\n}\r\n"], "names": ["memberLogApi", "pointLoglist", "data", "api", "get", "params", "rechargeLoglist", "consumeloglist", "levelloglist", "getcoupon"], "mappings": "wCAGA,MAAeA,EAAA,CAMbC,aAAeC,GAAcC,EAAIC,IAAI,mCAAoC,CAAEC,OAAQH,IAMnFI,gBAAkBJ,GAAcC,EAAIC,IAAI,sCAAuC,CAAEC,OAAQH,IAMzFK,eAAiBL,GAAcC,EAAIC,IAAI,qCAAsC,CAAEC,OAAQH,IAMvFM,aAAeN,GAAcC,EAAIC,IAAI,mCAAoC,CAAEC,OAAQH,IAOnFO,UAAYP,GAAcC,EAAIC,IAAI,yCAA0C,CAAEC,OAAQH"}