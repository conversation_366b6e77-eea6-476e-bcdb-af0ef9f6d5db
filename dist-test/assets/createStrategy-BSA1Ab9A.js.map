{"version": 3, "file": "createStrategy-BSA1Ab9A.js", "sources": ["../../src/views/group/group-price/price-strategy/components/DetailForm/createStrategy.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"zh-cn\": {\r\n    \"strategyInfo\": \"策略信息\",\r\n    \"strategyName\": \"房价策略名称\",\r\n    \"rules\": \"规则\",\r\n    \"guestSrcType\": \"客源类型\",\r\n    \"level\": \"级别\",\r\n    \"roomType\": \"房型\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"orderSource\": \"订单来源\",\r\n    \"priceRights\": \"价格权益\",\r\n    \"discountType\": \"优惠类\",\r\n    \"priceMethod\": \"价格方式\",\r\n    \"rightsType\": \"权益类\",\r\n    \"effectiveDate\": \"生效日期\",\r\n    \"week\": \"星期\",\r\n    \"applicationScope\": \"应用范围\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"submit\": \"提交\",\r\n    \"delete\": \"删除\",\r\n    \"add\": \"添加\",\r\n    \"allMemberLevel\": \"全部会员级别\",\r\n    \"noLimit\": \"不限级别\",\r\n    \"allRoomType\": \"全部集团房型\",\r\n    \"allDayRoom\": \"全天房\",\r\n    \"hourlyRoom\": \"钟点房\",\r\n    \"strategyNameRequired\": \"请输入策略名称\",\r\n    \"saveSuccess\": \"新增成功\",\r\n    \"selectLevel\": \"请选择级别\",\r\n    \"selectRoomType\": \"请选择房型\",\r\n    \"selectCheckInType\": \"请选择入住类型\",\r\n    \"noLimitSource\": \"不限来源\",\r\n    \"priceDiscount\": \"房价折扣\",\r\n    \"priceReduction\": \"房价立减\",\r\n    \"represents\": \"0.88折代表88折\",\r\n    \"discount\": \"折\",\r\n    \"enterDiscountAmount\": \"填写立减金额\",\r\n    \"yuan\": \"元\",\r\n    \"to\": \"至\",\r\n    \"endDate\": \"结束日期\",\r\n    \"startDate\": \"开始日期\",\r\n    \"sun\": \"周日\",\r\n    \"wen\": \"周一\",\r\n    \"tue\": \"周二\",\r\n    \"wed\": \"周三\",\r\n    \"thu\": \"周四\",\r\n    \"fri\": \"周五\",\r\n    \"sat\": \"周六\",\r\n    \"channels\": \"应用渠道\",\r\n    \"hotels\": \"应用酒店\",\r\n    \"pleaseSelect\": \"请选择\",\r\n    \"breakfastOption1\": \"1份\",\r\n    \"breakfastOption2\": \"2份\",\r\n    \"breakfastOption3\": \"3份\",\r\n    \"breakfastOption4\": \"4份\",\r\n    \"breakfastFeePlaceholder\": \"多少元一份\",\r\n    \"strategyNamePlaceholder\": \"请输入策略名称\"\r\n  },\r\n  \"en\": {\r\n    \"strategyInfo\": \"Strategy Info\",\r\n    \"strategyName\": \"Strategy Name\",\r\n    \"rules\": \"Rules\",\r\n    \"guestSrcType\": \"Guest Source Type\",\r\n    \"level\": \"Level\",\r\n    \"roomType\": \"Room Type\",\r\n    \"checkinType\": \"Check-in Type\",\r\n    \"orderSource\": \"Order Source\",\r\n    \"priceRights\": \"Price Rights\",\r\n    \"discountType\": \"Discount Type\",\r\n    \"priceMethod\": \"Price Method\",\r\n    \"rightsType\": \"Rights Type\",\r\n    \"effectiveDate\": \"Effective Date\",\r\n    \"week\": \"Week\",\r\n    \"applicationScope\": \"Scope\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"submit\": \"Submit\",\r\n    \"delete\": \"Delete\",\r\n    \"add\": \"Add\",\r\n    \"allMemberLevel\": \"All Member Levels\",\r\n    \"noLimit\": \"No Limit\",\r\n    \"allRoomType\": \"All Group Room Types\",\r\n    \"allDayRoom\": \"Full Day Room\",\r\n    \"hourlyRoom\": \"Hourly Room\",\r\n    \"strategyNameRequired\": \"Strategy name is required\",\r\n    \"saveSuccess\": \"Added successfully\",\r\n    \"selectLevel\": \"Please Select Level\",\r\n    \"selectRoomType\": \"Please Select Room Type\",\r\n    \"selectCheckInType\": \"Please Select Check-in Type\",\r\n    \"noLimitSource\": \"Unlimited source\",\r\n    \"priceDiscount\": \"Price Discount\",\r\n    \"priceReduction\": \"Price Reduction\",\r\n    \"represents\": \"0.88 represents an 88% discount\",\r\n    \"discount\": \"% discount\",\r\n    \"enterDiscountAmount\": \"Enter the discount amount\",\r\n    \"yuan\": \"$\",\r\n    \"to\": \"To\",\r\n    \"endDate\": \"End Date\",\r\n    \"startDate\": \"Start Date\",\r\n    \"sun\": \"Sun\",\r\n    \"wen\": \"Wen\",\r\n    \"tue\": \"Tue\",\r\n    \"wed\": \"Wed\",\r\n    \"thu\": \"Thu\",\r\n    \"fri\": \"Fri\",\r\n    \"sat\": \"Sat\",\r\n    \"channels\": \"Channels\",\r\n    \"hotels\": \"Hotels\",\r\n    \"pleaseSelect\": \"Please Select\",\r\n    \"breakfastOption1\": \"1 portion\",\r\n    \"breakfastOption2\": \"2 portions\",\r\n    \"breakfastOption3\": \"3 portions\",\r\n    \"breakfastOption4\": \"4 portions\",\r\n    \"breakfastFeePlaceholder\": \"Price per portion\",\r\n    \"strategyNamePlaceholder\": \"Please enter strategy name\"\r\n  },\r\n  \"km\": {\r\n    \"strategyInfo\": \"ព័ត៌មានអំពីយុទ្ធសាស្ត្រ\",\r\n    \"strategyName\": \"ឈ្មោះយុទ្ធសាស្ត្រតម្លៃបន្ទប់\",\r\n    \"rules\": \"ច្បាប់\",\r\n    \"guestSrcType\": \"ប្រភេទអតិថិជន\",\r\n    \"level\": \"កម្រិត\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n    \"orderSource\": \"ប្រភពការកម្មង់\",\r\n    \"priceRights\": \"សិទ្ធិតម្លៃ\",\r\n    \"discountType\": \"ប្រភេទការបញ្ចុះតម្លៃ\",\r\n    \"priceMethod\": \"វិធីសាស្ត្រតម្លៃ\",\r\n    \"rightsType\": \"ប្រភេទសិទ្ធិ\",\r\n    \"effectiveDate\": \"កាលបរិច្ឆេទដែលមានប្រសិទ្ធភាព\",\r\n    \"week\": \"សប្តាហ៍\",\r\n    \"applicationScope\": \"វិសាលភាពនៃកម្មវិធី\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"submit\": \"ដាក់ស្នើ\",\r\n    \"delete\": \"លុប\",\r\n    \"add\": \"បន្ថែម\",\r\n    \"allMemberLevel\": \"កម្រិតសមាជិកទាំងអស់\",\r\n    \"noLimit\": \"គ្មានកំណត់\",\r\n    \"allRoomType\": \"ប្រភេទបន្ទប់ក្រុមទាំងអស់\",\r\n    \"allDayRoom\": \"បន្ទប់ពេញមួយថ្ងៃ\",\r\n    \"hourlyRoom\": \"បន្ទប់ម៉ោង\",\r\n    \"strategyNameRequired\": \"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ\",\r\n    \"saveSuccess\": \"បានបន្ថែមដោយជោគជ័យ\",\r\n    \"selectLevel\": \"សូមជ្រើសរើសកម្រិត\",\r\n    \"selectRoomType\": \"សូមជ្រើសរើសប្រភេទបន្ទប់\",\r\n    \"selectCheckInType\": \"សូមជ្រើសរើសប្រភេទចូលស្នាក់នៅ\",\r\n    \"noLimitSource\": \"គ្មានកំណត់ប្រភព\",\r\n    \"priceDiscount\": \"ការបញ្ចុះតម្លៃបន្ទប់\",\r\n    \"priceReduction\": \"ការកាត់បន្ថយតម្លៃបន្ទប់\",\r\n    \"represents\": \"0.88 តំណាងឱ្យការបញ្ចុះតម្លៃ 88%\",\r\n    \"discount\": \"ការបញ្ចុះតម្លៃ\",\r\n    \"enterDiscountAmount\": \"បញ្ចូលចំនួនទឹកប្រាក់បញ្ចុះតម្លៃ\",\r\n    \"yuan\": \"ដុល្លារ\",\r\n    \"to\": \"ដល់\",\r\n    \"endDate\": \"កាលបរិច្ឆេទបញ្ចប់\",\r\n    \"startDate\": \"កាលបរិច្ឆេទចាប់ផ្តើម\",\r\n    \"sun\": \"អាទិត្យ\",\r\n    \"wen\": \"ច័ន្ទ\",\r\n    \"tue\": \"អង្គារ\",\r\n    \"wed\": \"ពុធ\",\r\n    \"thu\": \"ព្រហស្បតិ៍\",\r\n    \"fri\": \"សុក្រ\",\r\n    \"sat\": \"សៅរ៍\",\r\n    \"channels\": \"ឆានែល\",\r\n    \"hotels\": \"សណ្ឋាគារ\",\r\n    \"pleaseSelect\": \"សូមជ្រើសរើស\",\r\n    \"breakfastOption1\": \"១ចំណិត\",\r\n    \"breakfastOption2\": \"២ចំណិត\",\r\n    \"breakfastOption3\": \"៣ចំណិត\",\r\n    \"breakfastOption4\": \"៤ចំណិត\",\r\n    \"breakfastFeePlaceholder\": \"តម្លៃក្នុងមួយចំណិត\",\r\n    \"strategyNamePlaceholder\": \"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { channelApi, dictDataApi, generalConfigApi, memberApi, merchantApi, priceStrategyApi, rtApi } from '@/api/modules/index'\r\nimport {\r\n  AGENT_LEVEL,\r\n  BooleanEnum,\r\n  ChannelEnum,\r\n  CheckinType,\r\n  CONSTANT_TYPE_CODE_SZ,\r\n  DICT_TYPE_CHECKIN_TYPE,\r\n  DICT_TYPE_GUEST_SRC_TYPE,\r\n  DICT_TYPE_PRICE_HANDLE,\r\n  DICT_TYPE_RIGHTS_TYPE,\r\n  DiscountTypeEnum,\r\n  GuestSrcType,\r\n  ORDER_SOURCE,\r\n  PriceHandleEnum,\r\n  PROTOCOL_LEVEL,\r\n  RightTypeEnum,\r\n} from '@/models/index'\r\n\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nimport ymdate from '@/utils/timeutils'\r\nimport dayjs from 'dayjs'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  strategyCode: '',\r\n  strategyName: '',\r\n  condition: {\r\n    guestSrc: {\r\n      guestSrcType: '0',\r\n      guestSrcCodes: [] as string[],\r\n    },\r\n    rt: {\r\n      rtType: '1',\r\n      rtCodes: ['0'] as string[],\r\n    },\r\n    checkinType: {\r\n      type: '0',\r\n      checkinTypes: [] as string[],\r\n    },\r\n    orderSource: ['0'] as string[],\r\n  },\r\n  strategy: {\r\n    discountsType: {\r\n      type: DiscountTypeEnum.DISCOUNT.toString(),\r\n      value: null as unknown as number | undefined,\r\n      priceHandle: PriceHandleEnum.ROUND.toString(),\r\n    },\r\n    rightsTypes: [\r\n      {\r\n        type: RightTypeEnum.DELAY.toString(),\r\n        value: '120',\r\n        fee: '',\r\n      },\r\n      {\r\n        type: RightTypeEnum.MULTI_POINT.toString(),\r\n        value: '1',\r\n        fee: '',\r\n      },\r\n      {\r\n        type: RightTypeEnum.BREAKFAST.toString(),\r\n        value: '1',\r\n        fee: '',\r\n      },\r\n    ],\r\n  },\r\n  scope: {\r\n    weeks: ['1', '2', '3', '4', '5', '6', '7'],\r\n    channels: [ChannelEnum.LOBBY] as string[],\r\n    effectDate: [new Date(), dayjs(new Date()).add(1, 'year')],\r\n    startDate: '',\r\n    endDate: '',\r\n  },\r\n  isEnable: BooleanEnum.YES,\r\n  isG: BooleanEnum.YES,\r\n  hotelCodes: [userStore.hcode] as string[],\r\n})\r\nconst formRules = ref<FormRules>({\r\n  strategyName: [{ required: true, message: t('strategyNameRequired'), trigger: 'blur' }],\r\n})\r\n\r\n// 渠道常量\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\n// 会员类型列表\r\nconst memberTypes = ref<{ mtCode: string; mtName: string }[]>([])\r\n// 酒店列表\r\nconst hotels = ref<{ hcode: string; hname: string }[]>([])\r\n\r\nonMounted(() => {\r\n  getMemberTypes()\r\n  getChannels()\r\n  getRoomTypeList()\r\n  getHotels()\r\n  getConstants()\r\n})\r\n\r\n/** 获取门店列表 */\r\nfunction getHotels() {\r\n  merchantApi.getSimpleList(userStore.gcode).then((res: any) => {\r\n    if (res.code === 0) {\r\n      hotels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取渠道列表 */\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n// 常量里包括多个\r\nconst dictTypes = [DICT_TYPE_GUEST_SRC_TYPE, PROTOCOL_LEVEL, DICT_TYPE_CHECKIN_TYPE, CONSTANT_TYPE_CODE_SZ, ORDER_SOURCE, DICT_TYPE_PRICE_HANDLE, DICT_TYPE_RIGHTS_TYPE, RightTypeEnum.DELAY, RightTypeEnum.MULTI_POINT]\r\nconst dictData = ref({\r\n  // 客源类别\r\n  srcTypeList: [] as DictDataModel[],\r\n  // 入住类型常量 全天房 checkin_type\r\n  rzConstants: [] as DictDataModel[],\r\n  // 入住类型 小时数列表\r\n  szConstants: [] as DictDataModel[],\r\n  // 订单来源常量\r\n  lyConstants: [] as DictDataModel[],\r\n  // 价格处理类型\r\n  qzsConstants: [] as DictDataModel[],\r\n  // 权益类常量\r\n  qylConstants: [] as DictDataModel[],\r\n  // 延时离店\r\n  delayConstants: [] as DictDataModel[],\r\n  // 积分倍数\r\n  multiPointConstants: [] as DictDataModel[],\r\n})\r\n\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    dictData.value.srcTypeList = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n    dictData.value.rzConstants = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE && [CheckinType.ALL_DAY, CheckinType.LONG_STAY, CheckinType.TRAVEL_GROUP, CheckinType.MEETING_GROUP].includes(item.code))\r\n    dictData.value.szConstants = res.data.filter((item: any) => item.dictType === CONSTANT_TYPE_CODE_SZ)\r\n    dictData.value.lyConstants = res.data.filter((item: any) => item.dictType === ORDER_SOURCE)\r\n    dictData.value.qzsConstants = res.data.filter((item: any) => item.dictType === DICT_TYPE_PRICE_HANDLE)\r\n    dictData.value.qylConstants = res.data.filter((item: any) => item.dictType === DICT_TYPE_RIGHTS_TYPE)\r\n    dictData.value.delayConstants = res.data.filter((item: any) => item.dictType === RightTypeEnum.DELAY)\r\n    dictData.value.multiPointConstants = res.data.filter((item: any) => item.dictType === RightTypeEnum.MULTI_POINT)\r\n  })\r\n}\r\n\r\n/** 获取房型 */\r\nconst roomTypeList = ref<{ rtCode: string; rtName: string }[]>([])\r\nfunction getRoomTypeList() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    isVirtual: BooleanEnum.NO,\r\n    isGRt: BooleanEnum.YES,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      roomTypeList.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/**\r\n * 获取会员类型列表\r\n */\r\nfunction getMemberTypes() {\r\n  memberApi.listMemberType(userStore.gcode, '1').then((res: any) => {\r\n    if (res.code === 0) {\r\n      memberTypes.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nfunction disabledDate(time) {\r\n  return time.getTime() < Date.now() - 8.64e7\r\n}\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            form.value.scope.startDate = ymdate(form.value.scope.effectDate[0])\r\n            form.value.scope.endDate = ymdate(form.value.scope.effectDate[1])\r\n            priceStrategyApi.createPriceStrategy(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('saveSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n\r\nfunction handleChange(newValue) {\r\n  if (newValue.includes('0') && newValue.length > 1) {\r\n    // 如果没有选中“不限来源”，则保留其他选项，确保“不限来源”不在数组中\r\n    form.value.condition.orderSource = newValue.filter((value: any) => value !== '0')\r\n  } else if (newValue.includes('0')) {\r\n    // 如果选中了“不限来源”，则只保留“不限来源”，去掉其他所有选项\r\n    form.value.condition.orderSource = ['0']\r\n  }\r\n}\r\n\r\nfunction roomTypeChange(value) {\r\n  if (value.includes('0') && value.length > 1) {\r\n    form.value.condition.rt.rtCodes = value.filter((value: any) => value !== '0')\r\n  } else if (value.includes('0')) {\r\n    form.value.condition.rt.rtCodes = ['0']\r\n  }\r\n}\r\n\r\nfunction protocolChange(value) {\r\n  if (value.includes('0') && value.length > 1) {\r\n    form.value.condition.guestSrc.guestSrcCodes = value.filter((value: any) => value !== '0')\r\n  } else if (value.includes('0')) {\r\n    form.value.condition.guestSrc.guestSrcCodes = ['0']\r\n  }\r\n}\r\n\r\nfunction checkInTypeChange() {\r\n  form.value.condition.checkinType.checkinTypes = []\r\n}\r\n\r\nfunction guestSrcTypeChange(value: string) {\r\n  // member 会员   agent 中介  protocol 协议单位\r\n  form.value.condition.guestSrc.guestSrcCodes = []\r\n  if (value === GuestSrcType.AGENT || value === GuestSrcType.PROTOCOL) {\r\n    getLevel(value)\r\n  }\r\n}\r\n\r\n// 获取客户级别\r\nconst levelList = ref<{ code: string; name: string }[]>([])\r\nfunction getLevel(val: string) {\r\n  const type = val === GuestSrcType.PROTOCOL ? PROTOCOL_LEVEL : AGENT_LEVEL\r\n  generalConfigApi.list({ gcode: userStore.gcode, type }).then((res: any) => {\r\n    levelList.value = res.data\r\n  })\r\n}\r\n\r\n// 删除\r\nfunction onEelete(index: number) {\r\n  form.value.strategy.rightsTypes.splice(index, 1)\r\n}\r\nfunction add() {\r\n  const list = form.value.strategy.rightsTypes.map((e) => e.type)\r\n  const newList = dictData.value.qylConstants.filter((item) => {\r\n    return !list.includes(item.code)\r\n  })\r\n  form.value.strategy.rightsTypes.push({\r\n    type: newList[0].code,\r\n    value: '',\r\n    fee: '',\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"160px\" label-suffix=\"：\">\r\n      <el-divider content-position=\"left\">\r\n        {{ t('strategyInfo') }}\r\n      </el-divider>\r\n      <el-form-item :label=\"t('strategyName')\">\r\n        <el-input v-model=\"form.strategyName\" :placeholder=\"t('strategyNamePlaceholder')\" />\r\n      </el-form-item>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('rules') }}\r\n      </el-divider>\r\n      <el-form-item :label=\"t('guestSrcType')\">\r\n        <el-radio-group v-model=\"form.condition.guestSrc.guestSrcType\" @change=\"guestSrcTypeChange(form.condition.guestSrc.guestSrcType)\">\r\n          <el-radio v-for=\"item in dictData.srcTypeList\" :key=\"item.code\" :value=\"item.code\">\r\n            {{ item.label }}\r\n          </el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"form.condition.guestSrc.guestSrcType !== GuestSrcType.ALL && form.condition.guestSrc.guestSrcType !== GuestSrcType.WALK_IN && form.condition.guestSrc.guestSrcType !== ''\" :label=\"t('level')\">\r\n        <div class=\"equity\">\r\n          <el-select v-model=\"form.condition.guestSrc.guestSrcCodes\" :placeholder=\"t('selectLevel')\" multiple collapse-tags collapse-tags-tooltip clearable style=\"width: 240px\" @change=\"protocolChange\">\r\n            <template v-if=\"form.condition.guestSrc.guestSrcType === GuestSrcType.MEMBER\">\r\n              <el-option :label=\"t('allMemberLevel')\" value=\"0\" />\r\n              <el-option v-for=\"item in memberTypes\" :key=\"item.mtCode\" :label=\"item.mtName\" :value=\"item.mtCode\" />\r\n            </template>\r\n            <template v-if=\"form.condition.guestSrc.guestSrcType === GuestSrcType.AGENT || form.condition.guestSrc.guestSrcType === GuestSrcType.PROTOCOL\">\r\n              <el-option :label=\"t('noLimit')\" value=\"0\" />\r\n              <el-option v-for=\"item in levelList\" :key=\"item.code\" :label=\"item.name\" :value=\"item.code\" />\r\n            </template>\r\n          </el-select>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('roomType')\">\r\n        <div class=\"equity\">\r\n          <el-select v-model=\"form.condition.rt.rtCodes\" :placeholder=\"t('selectRoomType')\" multiple collapse-tags collapse-tags-tooltip clearable style=\"width: 240px\" @change=\"roomTypeChange\">\r\n            <el-option :label=\"t('allRoomType')\" value=\"0\" />\r\n            <el-option v-for=\"item in roomTypeList\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n          </el-select>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('checkinType')\">\r\n        <div class=\"equity\">\r\n          <el-select v-model=\"form.condition.checkinType.type\" style=\"width: 150px\" @change=\"checkInTypeChange\">\r\n            <el-option :label=\"t('allDayRoom')\" value=\"0\" />\r\n            <el-option :label=\"t('hourlyRoom')\" value=\"1\" />\r\n          </el-select>\r\n          <el-select v-model=\"form.condition.checkinType.checkinTypes\" :placeholder=\"t('selectCheckInType')\" multiple collapse-tags collapse-tags-tooltip clearable style=\"width: 240px\">\r\n            <template v-if=\"form.condition.checkinType.type === '1'\">\r\n              <el-option v-for=\"item in dictData.szConstants\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n            </template>\r\n            <template v-else>\r\n              <el-option v-for=\"item in dictData.rzConstants\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n            </template>\r\n          </el-select>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('orderSource')\">\r\n        <el-select v-model=\"form.condition.orderSource\" multiple collapse-tags collapse-tags-tooltip style=\"width: 240px\" clearable @change=\"handleChange\">\r\n          <el-option :label=\"t('noLimitSource')\" value=\"0\" />\r\n          <el-option v-for=\"item in dictData.lyConstants\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('priceRights') }}\r\n      </el-divider>\r\n      <el-form-item :label=\"t('discountType')\">\r\n        <div class=\"equity\">\r\n          <el-select v-model=\"form.strategy.discountsType.type\" style=\"width: 150px\">\r\n            <el-option :label=\"t('priceDiscount')\" :value=\"DiscountTypeEnum.DISCOUNT\" />\r\n            <el-option :label=\"t('priceReduction')\" :value=\"DiscountTypeEnum.REDUCE\" />\r\n          </el-select>\r\n          <template v-if=\"form.strategy.discountsType.type === DiscountTypeEnum.DISCOUNT\">\r\n            <el-input v-model=\"form.strategy.discountsType.value\" :placeholder=\"t('represents')\" controls-position=\"right\" style=\"width: 200px; margin-right: 8px\" />{{ t('discount') }}\r\n          </template>\r\n          <template v-else> <el-input v-model=\"form.strategy.discountsType.value\" :placeholder=\"t('enterDiscountAmount')\" controls-position=\"right\" style=\"width: 200px; margin-right: 8px\" />{{ t('yuan') }} </template>\r\n        </div>\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('priceMethod')\">\r\n        <el-select v-model=\"form.strategy.discountsType.priceHandle\" collapse-tags collapse-tags-tooltip style=\"width: 240px\">\r\n          <el-option v-for=\"item in dictData.qzsConstants\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rightsType')\">\r\n        <div v-for=\"(item, index) in form.strategy.rightsTypes\" :key=\"index\" class=\"equity\">\r\n          <el-select v-model=\"item.type\" style=\"width: 150px\">\r\n            <el-option\r\n              v-for=\"constant in dictData.qylConstants\"\r\n              :key=\"constant.code\"\r\n              :label=\"constant.label\"\r\n              :value=\"constant.code\"\r\n              :disabled=\"constant.code === RightTypeEnum.MULTI_POINT && form.condition.guestSrc.guestSrcType !== GuestSrcType.MEMBER\"\r\n            />\r\n          </el-select>\r\n          <div v-if=\"item.type === RightTypeEnum.DELAY\">\r\n            <el-select v-model=\"item.value\" style=\"width: 120px\"> <el-option v-for=\"o in dictData.delayConstants\" :key=\"o.code\" :label=\"o.label\" :value=\"o.code\" />\" </el-select>\r\n          </div>\r\n          <div v-if=\"item.type === RightTypeEnum.MULTI_POINT\">\r\n            <el-select v-model=\"item.value\" style=\"width: 120px\">\r\n              <el-option v-for=\"o in dictData.multiPointConstants\" :key=\"o.code\" :label=\"o.label\" :value=\"o.code\" />\r\n            </el-select>\r\n          </div>\r\n          <div v-if=\"item.type === RightTypeEnum.BREAKFAST\">\r\n            <el-select v-model=\"item.value\" style=\"width: 120px\">\r\n              <el-option :label=\"t('breakfastOption1')\" value=\"1\" />\r\n              <el-option :label=\"t('breakfastOption2')\" value=\"2\" />\r\n              <el-option :label=\"t('breakfastOption3')\" value=\"3\" />\r\n              <el-option :label=\"t('breakfastOption4')\" value=\"4\" />\r\n            </el-select>\r\n            <el-input v-model=\"item.fee\" :placeholder=\"t('breakfastFeePlaceholder')\" controls-position=\"right\" style=\"width: 120px; margin-right: 8px\" />\r\n          </div>\r\n          <el-link v-if=\"form.strategy.rightsTypes.length > 1\" type=\"primary\" :underline=\"false\" @click=\"onEelete(index)\">\r\n            {{ t('delete') }}\r\n          </el-link>\r\n          &nbsp; &nbsp;\r\n          <el-link v-if=\"form.strategy.rightsTypes.length < dictData.qylConstants.length && form.strategy.rightsTypes.length === index + 1\" type=\"primary\" :underline=\"false\" @click=\"add\">\r\n            {{ t('add') }}\r\n          </el-link>\r\n        </div>\r\n      </el-form-item>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('applicationScope') }}\r\n      </el-divider>\r\n      <el-form-item :label=\"t('effectiveDate')\" prop=\"effectDate\">\r\n        <el-date-picker v-model=\"form.scope.effectDate\" :disabled-date=\"disabledDate\" :end-placeholder=\"t('endDate')\" :range-separator=\"t('to')\" :start-placeholder=\"t('startDate')\" type=\"daterange\" />\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('week')\">\r\n        <el-checkbox-group v-model=\"form.scope.weeks\">\r\n          <el-checkbox key=\"7\" value=\"7\">\r\n            {{ t('sun') }}\r\n          </el-checkbox>\r\n          <el-checkbox key=\"1\" value=\"1\">\r\n            {{ t('wen') }}\r\n          </el-checkbox>\r\n          <el-checkbox key=\"2\" value=\"2\">\r\n            {{ t('tue') }}\r\n          </el-checkbox>\r\n          <el-checkbox key=\"3\" value=\"3\">\r\n            {{ t('wed') }}\r\n          </el-checkbox>\r\n          <el-checkbox key=\"4\" value=\"4\">\r\n            {{ t('thu') }}\r\n          </el-checkbox>\r\n          <el-checkbox key=\"5\" value=\"5\">\r\n            {{ t('fri') }}\r\n          </el-checkbox>\r\n          <el-checkbox key=\"6\" value=\"6\">\r\n            {{ t('sat') }}\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('channels')\">\r\n        <el-select v-model=\"form.scope.channels\" multiple collapse-tags collapse-tags-tooltip :placeholder=\"t('pleaseSelect')\" style=\"width: 240px\" clearable>\r\n          <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('hotels')\">\r\n        <el-select v-model=\"form.hotelCodes\" multiple collapse-tags collapse-tags-tooltip :placeholder=\"t('pleaseSelect')\" style=\"width: 240px\" clearable>\r\n          <el-option v-for=\"hotel in hotels\" :key=\"hotel.hcode\" :label=\"hotel.hname\" :value=\"hotel.hcode\" />\r\n        </el-select>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.input-with-select .el-input-group__prepend {\r\n  background-color: var(--el-fill-color-blank);\r\n}\r\n\r\n.equity {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "loading", "ref", "formRef", "form", "gcode", "strategyCode", "strategyName", "condition", "guestSrc", "guestSrcType", "guestSrcCodes", "rt", "rtType", "rtCodes", "checkinType", "type", "checkinTypes", "orderSource", "strategy", "discountsType", "DiscountTypeEnum", "DISCOUNT", "toString", "value", "priceHandle", "PriceHandleEnum", "ROUND", "rightsTypes", "RightTypeEnum", "DELAY", "fee", "MULTI_POINT", "BREAKFAST", "scope", "weeks", "channels", "ChannelEnum", "LOBBY", "effectDate", "Date", "dayjs", "add", "startDate", "endDate", "isEnable", "BooleanEnum", "YES", "isG", "hotelCodes", "hcode", "formRules", "required", "message", "trigger", "memberTypes", "hotels", "onMounted", "memberApi", "listMemberType", "then", "res", "code", "data", "params", "channelApi", "getChannelSimpleList", "getChannels", "isVirtual", "NO", "isGRt", "rtApi", "getRoomTypeSimpleList", "roomTypeList", "getRoomTypeList", "merchantApi", "getSimpleList", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "dictData", "srcTypeList", "filter", "item", "dictType", "DICT_TYPE_GUEST_SRC_TYPE", "rzConstants", "DICT_TYPE_CHECKIN_TYPE", "CheckinType", "ALL_DAY", "LONG_STAY", "TRAVEL_GROUP", "MEETING_GROUP", "includes", "szConstants", "CONSTANT_TYPE_CODE_SZ", "lyConstants", "ORDER_SOURCE", "qzsConstants", "DICT_TYPE_PRICE_HANDLE", "qylConstants", "DICT_TYPE_RIGHTS_TYPE", "delayConstants", "multiPointConstants", "PROTOCOL_LEVEL", "disabledDate", "time", "getTime", "now", "handleChange", "newValue", "length", "roomTypeChange", "protocolChange", "checkInTypeChange", "guest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GuestSrcType", "AGENT", "PROTOCOL", "val", "AGENT_LEVEL", "generalConfigApi", "list", "levelList", "getLevel", "__expose", "submit", "Promise", "resolve", "validate", "valid", "ymdate", "priceStrategyApi", "createPriceStrategy", "ElMessage", "success", "center", "error", "msg", "map", "e", "newList", "push", "index", "splice"], "mappings": "mhDA8MA,MAAMA,GAAYC,KACZC,EAAEA,IAAMC,IAERC,GAAUC,GAAI,GACdC,GAAUD,IACVE,GAAOF,EAAI,CACfG,MAAOR,GAAUQ,MACjBC,aAAc,GACdC,aAAc,GACdC,UAAW,CACTC,SAAU,CACRC,aAAc,IACdC,cAAe,IAEjBC,GAAI,CACFC,OAAQ,IACRC,QAAS,CAAC,MAEZC,YAAa,CACXC,KAAM,IACNC,aAAc,IAEhBC,YAAa,CAAC,MAEhBC,SAAU,CACRC,cAAe,CACbJ,KAAMK,EAAiBC,SAASC,WAChCC,MAAO,KACPC,YAAaC,EAAgBC,MAAMJ,YAErCK,YAAa,CACX,CACEZ,KAAMa,EAAcC,MAAMP,WAC1BC,MAAO,MACPO,IAAK,IAEP,CACEf,KAAMa,EAAcG,YAAYT,WAChCC,MAAO,IACPO,IAAK,IAEP,CACEf,KAAMa,EAAcI,UAAUV,WAC9BC,MAAO,IACPO,IAAK,MAIXG,MAAO,CACLC,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACtCC,SAAU,CAACC,EAAYC,OACvBC,WAAY,CAAK,IAAAC,KAAQC,EAAU,IAAAD,MAAQE,IAAI,EAAG,SAClDC,UAAW,GACXC,QAAS,IAEXC,SAAUC,EAAYC,IACtBC,IAAKF,EAAYC,IACjBE,WAAY,CAACpD,GAAUqD,SAEnBC,GAAYjD,EAAe,CAC/BK,aAAc,CAAC,CAAE6C,UAAU,EAAMC,QAAStD,GAAE,wBAAyBuD,QAAS,WAI1ElB,GAAWlC,EAAoD,IAE/DqD,GAAcrD,EAA0C,IAExDsD,GAAStD,EAAwC,IAEvDuD,GAAU,KAoFRC,EAAUC,eAAe9D,GAAUQ,MAAO,KAAKuD,MAAMC,IAClC,IAAbA,EAAIC,OACNP,GAAY/B,MAAQqC,EAAIE,KAAA,IApE9B,WACE,MAAMC,EAAS,CACb3D,MAAOR,GAAUQ,MACjBwC,SAAUC,EAAYC,KAExBkB,EAAWC,qBAAqBF,GAAQJ,MAAMC,IAC3B,IAAbA,EAAIC,OACN1B,GAASZ,MAAQqC,EAAIE,KAAA,GAExB,CAzBWI,GAgEd,WACE,MAAMH,EAAS,CACb3D,MAAOR,GAAUQ,MACjB+D,UAAWtB,EAAYuB,GACvBC,MAAOxB,EAAYC,IACnBF,SAAUC,EAAYC,KAExBwB,EAAMC,sBAAsBR,GAAQJ,MAAMC,IACvB,IAAbA,EAAIC,OACNW,GAAajD,MAAQqC,EAAIE,KAAA,GAE5B,CA1EeW,GAOhBC,EAAYC,cAAc/E,GAAUQ,OAAOuD,MAAMC,IAC9B,IAAbA,EAAIC,OACNN,GAAOhC,MAAQqC,EAAIE,KAAA,IAwCvBc,EAAYC,iBAAiBC,IAAWnB,MAAMC,IACnCmB,GAAAxD,MAAMyD,YAAcpB,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAaC,IACrEL,GAAAxD,MAAM8D,YAAczB,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAaG,GAA0B,CAACC,EAAYC,QAASD,EAAYE,UAAWF,EAAYG,aAAcH,EAAYI,eAAeC,SAASV,EAAKrB,QAC/MkB,GAAAxD,MAAMsE,YAAcjC,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAaW,IACrEf,GAAAxD,MAAMwE,YAAcnC,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAaa,IACrEjB,GAAAxD,MAAM0E,aAAerC,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAae,IACtEnB,GAAAxD,MAAM4E,aAAevC,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAaiB,IACtErB,GAAAxD,MAAM8E,eAAiBzC,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAavD,EAAcC,QACtFkD,GAAAxD,MAAM+E,oBAAsB1C,EAAIE,KAAKmB,QAAQC,GAAcA,EAAKC,WAAavD,EAAcG,aAAW,GAvDpG,IA0Bf,MAAM+C,GAAY,CAACM,EAA0BmB,EAAgBjB,EAAwBQ,EAAuBE,EAAcE,EAAwBE,EAAuBxE,EAAcC,MAAOD,EAAcG,aACtMgD,GAAW9E,EAAI,CAEnB+E,YAAa,GAEbK,YAAa,GAEbQ,YAAa,GAEbE,YAAa,GAEbE,aAAc,GAEdE,aAAc,GAEdE,eAAgB,GAEhBC,oBAAqB,KAiBjB,MAAA9B,GAAevE,EAA0C,IA0B/D,SAASuG,GAAaC,GACpB,OAAOA,EAAKC,UAAYnE,KAAKoE,MAAQ,KAAA,CA8BvC,SAASC,GAAaC,GAChBA,EAASjB,SAAS,MAAQiB,EAASC,OAAS,EAEzC3G,GAAAoB,MAAMhB,UAAUU,YAAc4F,EAAS5B,QAAQ1D,GAAyB,MAAVA,IAC1DsF,EAASjB,SAAS,OAE3BzF,GAAKoB,MAAMhB,UAAUU,YAAc,CAAC,KACtC,CAGF,SAAS8F,GAAexF,GAClBA,EAAMqE,SAAS,MAAQrE,EAAMuF,OAAS,EACnC3G,GAAAoB,MAAMhB,UAAUI,GAAGE,QAAUU,EAAM0D,QAAQ1D,GAAyB,MAAVA,IACtDA,EAAMqE,SAAS,OACxBzF,GAAKoB,MAAMhB,UAAUI,GAAGE,QAAU,CAAC,KACrC,CAGF,SAASmG,GAAezF,GAClBA,EAAMqE,SAAS,MAAQrE,EAAMuF,OAAS,EACnC3G,GAAAoB,MAAMhB,UAAUC,SAASE,cAAgBa,EAAM0D,QAAQ1D,GAAyB,MAAVA,IAClEA,EAAMqE,SAAS,OACxBzF,GAAKoB,MAAMhB,UAAUC,SAASE,cAAgB,CAAC,KACjD,CAGF,SAASuG,KACP9G,GAAKoB,MAAMhB,UAAUO,YAAYE,aAAe,EAAC,CAGnD,SAASkG,GAAmB3F,GAE1BpB,GAAKoB,MAAMhB,UAAUC,SAASE,cAAgB,GAC1Ca,IAAU4F,EAAaC,OAAS7F,IAAU4F,EAAaE,UAO7D,SAAkBC,GAChB,MAAMvG,EAAOuG,IAAQH,EAAaE,SAAWd,EAAiBgB,GAC7CC,EAAAC,KAAK,CAAErH,MAAOR,GAAUQ,MAAOW,SAAQ4C,MAAMC,IAC5D8D,GAAUnG,MAAQqC,EAAIE,IAAA,GACvB,CAVC6D,CAASpG,EACX,CA/DWqG,GAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxB7H,GAAQqB,OACNrB,GAAQqB,MAAMyG,UAAUC,IAClBA,IACG9H,GAAAoB,MAAMU,MAAMS,UAAYwF,GAAO/H,GAAKoB,MAAMU,MAAMK,WAAW,IAC3DnC,GAAAoB,MAAMU,MAAMU,QAAUuF,GAAO/H,GAAKoB,MAAMU,MAAMK,WAAW,IAC9D6F,EAAiBC,oBAAoBjI,GAAKoB,OAAOoC,MAAMC,IACpC,IAAbA,EAAIC,MACNwE,EAAUC,QAAQ,CAChBlF,QAAStD,GAAE,eACXyI,QAAQ,IAEFR,KAERM,EAAUG,MAAM,CACdpF,QAASQ,EAAI6E,IACbF,QAAQ,GACT,IAEJ,GAEJ,MA4CH,MAAAb,GAAYzH,EAAsC,IAYxD,SAASwC,KACD,MAAAgF,EAAOtH,GAAKoB,MAAML,SAASS,YAAY+G,KAAKC,GAAMA,EAAE5H,OACpD6H,EAAU7D,GAASxD,MAAM4E,aAAalB,QAAQC,IAC1CuC,EAAK7B,SAASV,EAAKrB,QAExB1D,GAAAoB,MAAML,SAASS,YAAYkH,KAAK,CACnC9H,KAAM6H,EAAQ,GAAG/E,KACjBtC,MAAO,GACPO,IAAK,IACN,i0PAZH,SAAkBgH,GAChB3I,GAAKoB,MAAML,SAASS,YAAYoH,OAAOD,EAAO,EAAC"}