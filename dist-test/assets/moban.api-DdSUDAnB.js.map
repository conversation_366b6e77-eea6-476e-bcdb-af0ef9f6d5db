{"version": 3, "file": "moban.api-DdSUDAnB.js", "sources": ["../../src/api/modules/moban.api.ts"], "sourcesContent": ["import api from '../index'\r\n\r\n// 业务模板=内部模板\r\nexport default {\r\n\r\n  // 获得短信默认模板分页\r\n  pagelist: (data: any) => api.get('/admin-api/template/inner/page-list', { params: data }),\r\n\r\n  // 添加模板\r\n  add: (data: any) => api.post('/admin-api/marketing/sms-default-template/create', data),\r\n\r\n  // 模板详情\r\n  detail: (data: any) => api.get('/admin-api/marketing/sms-default-template/get', { params: data }),\r\n\r\n  // 修改模板\r\n  update: (data: any) => api.put('/admin-api/marketing/sms-default-template/update', data),\r\n\r\n  // 模板列表\r\n  list: (data: any) => api.get('/template/inner/list', { params: data }),\r\n\r\n  // 删除模板\r\n  delete: (id: any) => api.get(`/template/inner/delete/${id}`),\r\n\r\n  // 修改模板状态\r\n  enabled: (data: any) => api.put('/template/inner/enabled', data),\r\n\r\n  // //发送短信\r\n  batchSend: (data: any) => api.post('/batchSend', data),\r\n\r\n  // 发送短信\r\n  // batchSend:(data: any) => api.post(\"/send\",  data),\r\n}\r\n"], "names": ["moban", "pagelist", "data", "api", "get", "params", "add", "post", "detail", "update", "put", "list", "delete", "id", "enabled", "batchSend"], "mappings": "wCAGA,MAAeA,EAAA,CAGbC,SAAWC,GAAcC,EAAIC,IAAI,sCAAuC,CAAEC,OAAQH,IAGlFI,IAAMJ,GAAcC,EAAII,KAAK,mDAAoDL,GAGjFM,OAASN,GAAcC,EAAIC,IAAI,gDAAiD,CAAEC,OAAQH,IAG1FO,OAASP,GAAcC,EAAIO,IAAI,mDAAoDR,GAGnFS,KAAOT,GAAcC,EAAIC,IAAI,uBAAwB,CAAEC,OAAQH,IAG/DU,OAASC,GAAYV,EAAIC,IAAI,0BAA0BS,KAGvDC,QAAUZ,GAAcC,EAAIO,IAAI,0BAA2BR,GAG3Da,UAAYb,GAAcC,EAAII,KAAK,aAAcL"}