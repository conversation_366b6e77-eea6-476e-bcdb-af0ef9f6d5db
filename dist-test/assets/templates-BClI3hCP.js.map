{"version": 3, "file": "templates-BClI3hCP.js", "sources": ["../../src/views/marketing/sms/send/components/templates.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { SmsTemplateModel } from '@/models/index'\r\nimport { smsTemplateApi } from '@/api/modules/index'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\ndefineOptions({\r\n  name: 'MarketingCouponCouponsetList4',\r\n})\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n  },\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [\r\n    value: boolean,\r\n  ]\r\n  'success': []\r\n}>()\r\nconst userStore = useUserStore()\r\nconst router = useRouter()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\nconst tabbar = useTabbar()\r\nconst settingsStore = useSettingsStore()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: true,\r\n\r\n  // 搜索\r\n  search: {\r\n    /** 酒店代码 */\r\n    hcode: userStore.hcode,\r\n  },\r\n  // 列表数据\r\n  dataList: [] as SmsTemplateModel[],\r\n  /** 被选中的模板代码 */\r\n  templateCode: '',\r\n})\r\n\r\nconst merchants = ref<{ hcode: string, hname: string }[]>([])\r\nconst mts = ref<{ mtCode: string, mtName: string }[]>([])\r\n\r\nonMounted(() => {\r\n  getDataList()\r\n})\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    ...(data.value.search.hcode && { hcode: data.value.search.hcode }),\r\n  }\r\n  smsTemplateApi.list(params).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data.list\r\n    pagination.value.total = res.data.total\r\n  })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string, order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction onSubmit() {\r\n\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <el-dialog\r\n      v-model=\"myVisible\" title=\"选择模板\" width=\"1000px\" :close-on-click-modal=\"false\" append-to-body\r\n      destroy-on-close\r\n    >\r\n      <page-main>\r\n        <el-table\r\n          v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" stripe highlight-current-row border\r\n          height=\"100%\" @sort-change=\"sortChange\"\r\n        >\r\n          <el-table-column align=\"center\" fixed width=\"200\">\r\n            <template #default=\"{ row }\">\r\n              <el-radio v-model=\"data.templateCode\" :label=\"row.templateCode\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"templateName\" label=\"模板名称\" width=\"200\" />\r\n          <el-table-column prop=\"content\" label=\"模板内容\" />\r\n        </el-table>\r\n        <el-pagination\r\n          v-if=\"pagination.total > 10\"\r\n          :current-page=\"pagination.pageNo\" :total=\"pagination.total\" :page-size=\"pagination.pageSize\"\r\n          :page-sizes=\"pagination.sizes\" :layout=\"pagination.layout\" :hide-on-single-page=\"false\" class=\"pagination\"\r\n          background @size-change=\"sizeChange\" @current-change=\"currentChange\"\r\n        />\r\n      </page-main>\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          确定\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n}\r\n</style>\r\n@/api/modules/couponset.api\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "useRouter", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "useTabbar", "useSettingsStore", "myVisible", "computed", "get", "modelValue", "set", "val", "data", "ref", "loading", "tableAutoHeight", "search", "hcode", "dataList", "templateCode", "getDataList", "value", "params", "gcode", "smsTemplateApi", "list", "then", "res", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "onCancel", "onSubmit", "onMounted"], "mappings": "omCASA,MAAMA,EAAQC,EAQRC,EAAQC,EAMRC,EAAYC,IACOC,IACzB,MAAMC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IACtDC,IACcC,IAEvC,MAAMC,EAAYC,EAAS,CACzBC,IAAM,IACGjB,EAAMkB,WAEf,GAAAC,CAAIC,GACFlB,EAAM,oBAAqBkB,EAAG,IAG5BC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAGjBC,OAAQ,CAENC,MAAOtB,EAAUsB,OAGnBC,SAAU,GAEVC,aAAc,KAUhB,SAASC,IACPR,EAAKS,MAAMP,SAAU,EACrB,MAAMQ,EAAS,IACVvB,IACHwB,MAAO5B,EAAU4B,SACbX,EAAKS,MAAML,OAAOC,OAAS,CAAEA,MAAOL,EAAKS,MAAML,OAAOC,QAE5DO,EAAeC,KAAKH,GAAQI,MAAMC,IAChCf,EAAKS,MAAMP,SAAU,EAChBF,EAAAS,MAAMH,SAAWS,EAAIf,KAAKa,KACpB3B,EAAAuB,MAAMO,MAAQD,EAAIf,KAAKgB,KAAA,GACnC,CAIH,SAASC,EAAWC,GAClB9B,EAAa8B,GAAMJ,MAAK,IAAMN,KAAa,CAIpC,SAAAW,EAAcC,EAAO,GAC5B/B,EAAgB+B,GAAMN,MAAK,IAAMN,KAAa,CAIhD,SAASa,GAAWC,KAAEA,EAAMC,MAAAA,IAC1BjC,EAAagC,EAAMC,GAAOT,MAAK,IAAMN,KAAa,CAGpD,SAASgB,IACP9B,EAAUe,OAAQ,CAAA,CAGpB,SAASgB,IAAW,QAxCFxB,EAAwC,IAC9CA,EAA0C,IAEtDyB,GAAU,KACIlB,GAAA"}