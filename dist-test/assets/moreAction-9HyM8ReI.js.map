{"version": 3, "file": "moreAction-9HyM8ReI.js", "sources": ["../../src/layouts/components/Topbar/Tabbar/moreAction.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { Tabbar } from '#/global'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useTabbarStore from '@/store/modules/tabbar'\r\nimport eventBus from '@/utils/eventBus'\r\nimport { OverlayScrollbarsComponent } from 'overlayscrollbars-vue'\r\nimport Sortable from 'sortablejs'\r\n\r\n\r\ndefineOptions({\r\n  name: 'TabbarMoreAction',\r\n})\r\n\r\nconst router = useRouter()\r\n\r\nconst settingsStore = useSettingsStore()\r\nconst tabbarStore = useTabbarStore()\r\n\r\nconst tabbar = useTabbar()\r\n\r\nconst { t } = useI18n()\r\nconst { generateI18nTitle } = useMenu()\r\n\r\nconst activedTabId = computed(() => tabbar.getId())\r\n\r\nconst dropdownTabContainerRef = useTemplateRef('dropdownTabContainerRef')\r\n\r\nconst isDragging = ref(false)\r\nwatch(() => dropdownTabContainerRef.value, (val) => {\r\n  if (val) {\r\n    // eslint-disable-next-line no-new\r\n    new Sortable(val.$el, {\r\n      animation: 200,\r\n      ghostClass: 'ghost',\r\n      draggable: '.tab',\r\n      onStart: () => {\r\n        isDragging.value = true\r\n      },\r\n      onEnd: () => {\r\n        isDragging.value = false\r\n      },\r\n      onMove: (e) => {\r\n        return e.dragged.classList.contains('pinned') ? e.related.classList.contains('pinned') : !e.related.classList.contains('pinned')\r\n      },\r\n      onUpdate: (e) => {\r\n        if (e.newIndex !== undefined && e.oldIndex !== undefined) {\r\n          tabbarStore.sort(e.newIndex, e.oldIndex)\r\n        }\r\n      },\r\n    })\r\n  }\r\n})\r\n\r\nfunction actionCommand(command: 'search-tabs' | 'other-side' | 'left-side' | 'right-side') {\r\n  switch (command) {\r\n    case 'search-tabs':\r\n      eventBus.emit('global-search-toggle', 'tab')\r\n      break\r\n    case 'other-side':\r\n      tabbar.closeOtherSide()\r\n      break\r\n    case 'left-side':\r\n      tabbar.closeLeftSide()\r\n      break\r\n    case 'right-side':\r\n      tabbar.closeRightSide()\r\n      break\r\n  }\r\n}\r\n\r\nfunction iconName(isActive: boolean, icon: Tabbar.recordRaw['icon'], activeIcon: Tabbar.recordRaw['activeIcon']) {\r\n  let name\r\n  if ((!isActive && icon) || (isActive && !activeIcon)) {\r\n    name = icon\r\n  }\r\n  else if (isActive && activeIcon) {\r\n    name = activeIcon\r\n  }\r\n  return name\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <HDropdown placement=\"bottom-end\" popper-class=\"tabbar-dropdown\">\r\n      <div class=\"h-6 w-6 flex-center cursor-pointer rounded-1 bg-[var(--g-container-bg)] text-lg shadow transition-background-color transition-shadow\">\r\n        <SvgIcon name=\"i-ri:arrow-down-s-fill\" />\r\n      </div>\r\n      <template #dropdown>\r\n        <div class=\"quick-button\">\r\n          <button v-if=\"settingsStore.settings.toolbar.navSearch\" class=\"button\" @click=\"actionCommand('search-tabs')\">\r\n            <HTooltip :text=\"t('app.tabbar.searchTabs')\">\r\n              <SvgIcon name=\"i-ri:search-line\" />\r\n            </HTooltip>\r\n          </button>\r\n          <button class=\"button\" :disabled=\"!tabbar.checkCloseOtherSide()\" @click=\"actionCommand('other-side')\">\r\n            <HTooltip :text=\"t('app.tabbar.closeOtherSide')\">\r\n              <SvgIcon name=\"i-ri:close-fill\" />\r\n            </HTooltip>\r\n          </button>\r\n          <button class=\"button\" :disabled=\"!tabbar.checkCloseLeftSide()\" @click=\"actionCommand('left-side')\">\r\n            <HTooltip :text=\"t('app.tabbar.closeLeftSide')\">\r\n              <SvgIcon name=\"i-ph:arrow-line-left\" />\r\n            </HTooltip>\r\n          </button>\r\n          <button class=\"button\" :disabled=\"!tabbar.checkCloseRightSide()\" @click=\"actionCommand('right-side')\">\r\n            <HTooltip :text=\"t('app.tabbar.closeRightSide')\">\r\n              <SvgIcon name=\"i-ph:arrow-line-right\" />\r\n            </HTooltip>\r\n          </button>\r\n        </div>\r\n        <OverlayScrollbarsComponent :options=\"{ scrollbars: { autoHide: 'leave', autoHideDelay: 300 } }\" defer class=\"max-h-[300px]\">\r\n          <TransitionGroup ref=\"dropdownTabContainerRef\" :name=\"!isDragging ? 'dropdown-tab' : undefined\" tag=\"div\" class=\"tabs\" :class=\"{ dragging: isDragging }\">\r\n            <div\r\n              v-for=\"element in tabbarStore.list\" :key=\"element.tabId\" class=\"tab\" :class=\"{\r\n                actived: element.tabId === activedTabId,\r\n                pinned: element.isPermanent || element.isPin,\r\n              }\"\r\n            >\r\n              <div :key=\"element.tabId\" class=\"title\" :title=\"element.customTitleList.find(item => item.fullPath === element.fullPath)?.title || generateI18nTitle(element.title)\" @click=\"router.push(element.fullPath)\">\r\n                <SvgIcon v-if=\"settingsStore.settings.tabbar.enableIcon && iconName(element.tabId === activedTabId, element.icon, element.activeIcon)\" :name=\"iconName(element.tabId === activedTabId, element.icon, element.activeIcon)!\" />\r\n                {{ element.customTitleList.find(item => item.fullPath === element.fullPath)?.title || generateI18nTitle(element.title) }}\r\n              </div>\r\n              <div v-if=\"!element.isPermanent && element.isPin\" class=\"action-icon\" @click.stop=\"tabbarStore.unPin(element.tabId)\" @dblclick.stop>\r\n                <SvgIcon name=\"i-ri:pushpin-2-fill\" />\r\n              </div>\r\n              <div v-else-if=\"!element.isPermanent && tabbarStore.list.length > 1\" class=\"action-icon\" @click.stop=\"tabbar.closeById(element.tabId)\" @dblclick.stop>\r\n                <SvgIcon name=\"i-ri:close-fill\" />\r\n              </div>\r\n            </div>\r\n          </TransitionGroup>\r\n        </OverlayScrollbarsComponent>\r\n      </template>\r\n    </HDropdown>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.tabbar-dropdown {\r\n  .quick-button {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 10px;\r\n\r\n    .button {\r\n      --uno: transition-colors;\r\n\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 32px;\r\n      height: 32px;\r\n      cursor: pointer;\r\n      background-color: var(--g-bg);\r\n      border: none;\r\n      border-radius: 5px;\r\n      outline: none;\r\n\r\n      &:hover:not(:disabled) {\r\n        --uno: text-ui-primary;\r\n      }\r\n\r\n      &:disabled {\r\n        cursor: not-allowed;\r\n      }\r\n\r\n      i {\r\n        font-size: 16px !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .tabs {\r\n    width: 200px;\r\n    padding: 0 0 10px;\r\n\r\n    &:not(.dragging) {\r\n      .tab:hover {\r\n        &:not(.actived) {\r\n          background-color: var(--g-bg);\r\n        }\r\n\r\n        .action-icon {\r\n          opacity: 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    .tab {\r\n      position: relative;\r\n      width: calc(100% - 30px);\r\n      height: 30px;\r\n      padding: 0 5px;\r\n      margin: 0 15px;\r\n      background-color: var(--g-container-bg);\r\n      border-radius: 5px;\r\n      transition: background-color 0.3s;\r\n\r\n      &.actived {\r\n        background-color: var(--g-bg);\r\n      }\r\n\r\n      &.ghost {\r\n        opacity: 0;\r\n      }\r\n\r\n      &:hover {\r\n        .title {\r\n          --uno: opacity-100;\r\n\r\n          margin-inline-end: 20px;\r\n          mask-image: linear-gradient(to right, #000 calc(100% - 44px), transparent);\r\n\r\n          [dir=\"rtl\"] & {\r\n            mask-image: linear-gradient(to left, #000 calc(100% - 44px), transparent);\r\n          }\r\n        }\r\n      }\r\n\r\n      * {\r\n        user-select: none;\r\n      }\r\n\r\n      .title {\r\n        --uno: opacity-70 transition;\r\n\r\n        position: relative;\r\n        display: flex;\r\n        gap: 5px;\r\n        align-items: center;\r\n        height: 100%;\r\n        overflow: hidden;\r\n        font-size: 14px;\r\n        white-space: nowrap;\r\n        cursor: pointer;\r\n        mask-image: linear-gradient(to right, #000 calc(100% - 24px), transparent);\r\n\r\n        [dir=\"rtl\"] & {\r\n          mask-image: linear-gradient(to left, #000 calc(100% - 24px), transparent);\r\n        }\r\n\r\n        i {\r\n          flex-shrink: 0;\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .action-icon {\r\n        --uno: transition;\r\n\r\n        position: absolute;\r\n        inset-inline-end: 0.5em;\r\n        top: 50%;\r\n        z-index: 10;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 1.5em;\r\n        height: 1.5em;\r\n        font-size: 12px;\r\n        color: var(--g-tabbar-tab-color);\r\n        border-radius: 50%;\r\n        opacity: 0;\r\n        transform: translateY(-50%);\r\n\r\n        &:hover {\r\n          --uno: ring-1 ring-stone-3 dark-ring-stone-7;\r\n\r\n          background-color: var(--g-bg);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.dropdown-tab-enter-from,\r\n.dropdown-tab-leave-to {\r\n  opacity: 0;\r\n  transform: translateX(-100%);\r\n}\r\n\r\n.dropdown-tab-enter-active {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.dropdown-tab-leave-active {\r\n  position: absolute !important;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.dropdown-tab-move {\r\n  transition: transform 0.3s;\r\n}\r\n</style>\r\n"], "names": ["router", "useRouter", "settingsStore", "useSettingsStore", "tabbarStore", "useTabbarStore", "tabbar", "useTabbar", "t", "useI18n", "generateI18nTitle", "useMenu", "activedTabId", "computed", "getId", "dropdownTabContainerRef", "useTemplateRef", "isDragging", "ref", "actionCommand", "command", "eventBus", "emit", "closeOtherSide", "closeLeftSide", "closeRightSide", "iconName", "isActive", "icon", "activeIcon", "name", "watch", "value", "val", "Sortable", "$el", "animation", "ghostClass", "draggable", "onStart", "onEnd", "onMove", "e", "dragged", "classList", "contains", "related", "onUpdate", "newIndex", "oldIndex", "sort"], "mappings": "i0BAaA,MAAMA,EAASC,IAETC,EAAgBC,IAChBC,EAAcC,IAEdC,EAASC,KAETC,EAAEA,GAAMC,KACRC,kBAAEA,GAAsBC,IAExBC,EAAeC,GAAS,IAAMP,EAAOQ,UAErCC,EAA0BC,EAAe,2BAEzCC,EAAaC,GAAI,GA0BvB,SAASC,EAAcC,GACrB,OAAQA,GACN,IAAK,cACMC,EAAAC,KAAK,uBAAwB,OACtC,MACF,IAAK,aACHhB,EAAOiB,iBACP,MACF,IAAK,YACHjB,EAAOkB,gBACP,MACF,IAAK,aACHlB,EAAOmB,iBAEX,CAGO,SAAAC,EAASC,EAAmBC,EAAgCC,GAC/D,IAAAC,EAOG,OANDH,GAAYC,GAAUD,IAAaE,EAChCC,EAAAF,EAEAD,GAAYE,IACZC,EAAAD,GAEFC,CAAA,QAlDTC,GAAM,IAAMhB,EAAwBiB,QAAQC,IACtCA,GAEE,IAAAC,EAASD,EAAIE,IAAK,CACpBC,UAAW,IACXC,WAAY,QACZC,UAAW,OACXC,QAAS,KACPtB,EAAWe,OAAQ,CAAA,EAErBQ,MAAO,KACLvB,EAAWe,OAAQ,CAAA,EAErBS,OAASC,GACAA,EAAEC,QAAQC,UAAUC,SAAS,UAAYH,EAAEI,QAAQF,UAAUC,SAAS,WAAaH,EAAEI,QAAQF,UAAUC,SAAS,UAEzHE,SAAWL,SACU,IAAfA,EAAEM,eAAyC,IAAfN,EAAEO,UAChC7C,EAAY8C,KAAKR,EAAEM,SAAUN,EAAEO,SAAQ,GAG5C"}