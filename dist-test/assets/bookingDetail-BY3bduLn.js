import{d as e,aj as t,ai as o,b as a,r,B as l,y as i,av as n,D as s,o as u,c as d,e as m,w as c,f as p,g as b,u as k,h as f,Y as h,R as v,F as y,aq as g,b4 as C,ag as R,a6 as N,i as T,aR as _,bn as Y,c2 as w,c3 as M,q as S,bG as D,b1 as x,b2 as P,b0 as O,m as U,bK as V,aS as j,t as H,v as A,s as J,x as I,k as E,ay as L,b3 as q,aT as B,a7 as $,bz as z,j as W,bF as F}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                  *//* empty css                   */import{_ as G}from"./index-3RMLzyhA.js";/* empty css                 */import{_ as K}from"./index-ADu0XAHG.js";/* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                       *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                *//* empty css               *//* empty css               */import{b as Q}from"./book.api-ERXvEXQF.js";import{o as X}from"./order.api-B-JCVvq6.js";import{d as Z}from"./dictData.api-DUabpYqy.js";import{s as ee}from"./serverTime.api-D89oCqKL.js";import{f as te,q as oe,r as ae,j as re,s as le,k as ie}from"./constants-Cg3j_uH4.js";import{y as ne,a as se}from"./timeutils-Ib6GkGcq.js";import ue from"./cancelPopUP-BbPXaQdi.js";import de from"./arrangeRooms-CPfs5GXR.js";import me from"./index-CkWKDwTG.js";import ce from"./index-Eu7Cs0xe.js";import pe from"./checkModal-tyH9Ceqi.js";/* empty css                       */import{_ as be}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                          *//* empty css                 *//* empty css                  *//* empty css                          *//* empty css                       *//* empty css                        */import"./generalConfig.api-CEBBd8kx.js";import"./index-DAulSAJI.js";import"./index-D8c6PuWt.js";/* empty css                      *//* empty css                        *//* empty css                         *//* empty css                */import"./index-CDbn0nBx.js";import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";/* empty css                    *//* empty css                   *//* empty css                        */import"./member.api-2tU9HGvl.js";import"./DictTypeEnum-DKIIlHnN.js";import"./CardReader-BeR26SIt.js";import"./myStringUtil-D79vpWmP.js";import"./sensitive-la8iBfdn.js";const ke={key:0},fe={key:1},he={key:1},ve={key:1},ye={key:1},ge={class:"card-header"},Ce={key:1},Re={key:1},Ne={key:1},Te={key:1},_e={key:0},Ye={class:"mt-2 flex"},we={key:1,class:"flex items-center"},Me={key:0,style:{width:"100%"}},Se={class:"mt-2"},De={key:1,class:"flex items-center"},xe={key:0,style:{width:"100%"}},Pe={class:"mt-2"},Oe={key:1,class:"flex items-center"},Ue={class:"flex-between mb-[7px]"},Ve={key:0},je={key:0},He={key:0},Ae={key:1},Je={key:0,class:"vipPrice"},Ie={key:1,class:"vipPrice"},Ee={class:"flex-between"},Le={class:"flex-center",style:{color:"var(--el-text-color-secondary)","font-size":"13px"}},qe={class:"w-[75px]"},Be={class:"w-[75px]"},$e={style:{padding:"15px","font-size":"16px"}},ze={style:{padding:"15px","font-size":"16px"}},We={class:"dialog-footer"},Fe=e({__name:"bookingDetail",props:{bookNo:{default:()=>[]},state:{default:""},isEntryAccount:{default:"0"}},emits:["update:modelValue","success","refresh","seeDetailed"],setup(e,{emit:be}){const Fe=e,Ge=be,{t:Ke}=t(),Qe=o(),Xe=a({orderNo:"",outOrderNo:"",guestName:"",teamName:"",pinyin:"",phone:"",sex:"",idType:"",idNo:"",channelCode:"",channelName:"",orderSource:"",address:"",nation:"",orderSourceName:"",checkinType:"0",checkinTypeName:"",planCheckinTime:"",planCheckoutTime:"",rtCode:"",rtName:"",rCode:"",rNo:"",guestSrcType:"",guestSrcTypeName:"",bookNo:"",orderType:"",bindCode:"",isMain:"",accState:"",isInvoice:"",bkTicketNum:0,activityCode:"",priceStrategyCode:"",state:"",checkoutState:"",isMadeCard:"",checkinShiftNo:"",checkinOperator:"",checkoutShiftNo:"",checkoutTime:"",checkoutOperator:"",payShiftNo:"",payTime:"",payOperator:"",seller:"",totalFee:0,balance:0,bookRooms:[],prices:[],arrState:"",deposit:"",remark:"",days:"",tags:{awaken:{isEnable:"",time:""},secrecy:"",notDisturbing:""},summary:{price:0,consume:0,payment:0,preAuth:0,discounts:0,balance:0},bookType:"",hourCode:"",contact:"",checkinPerson:"",checkinPhone:"",retainTime:"",guarantyStyleName:"",outOrderRemark:"",guestCode:""}),Ze=r({gcode:Qe.gcode,hcode:Qe.hcode}),et=a(""),tt=a(""),ot=a(""),at=a([]),rt=a([]),lt=a([]),it=a(!1),nt=a([]),st=a(!1),ut=a(!0),dt=a(0),mt=l((()=>dt.value>0)),ct=a({dataList:[]}),pt=a(!1),bt=a(!1),kt=a(!1),ft=a([]),ht=a(),vt=a(),yt=r({...Ze,upgradeMethod:0,bookNo:Fe.bookNo[0],rtCode:"",upgradeRtCode:""}),gt=a({...yt}),Ct=a([]),Rt=a([]),Nt=r([{label:Ke("roomUpgrade"),type:"radio",field:"upgradeMethod",options:{data:[{value:0,label:Ke("freeUpgrade")}],radioOptions:{"onUpdate:modelValue":e=>{oo()}}}},{label:Ke("currentRoomType"),field:"rtCode",slot:"currentRoomType"},{label:Ke("upgradeRoomType"),field:"upgradeRtCode",slot:"upgradeRtCode"},{slot:"show",field:""}]);async function Tt(){Z.getDictDataBatch([te,oe,ae]).then((e=>{at.value=e.data.filter((e=>e.dictType===te)),rt.value=e.data.filter((e=>e.dictType===oe)),lt.value=e.data.filter((e=>e.dictType===ae))}))}async function _t(){const e=await ee.serverTime(Qe.gcode,"0");et.value=e.data}async function Yt(){kt.value=!0;const e=await X.generalDetail({...Ze,bookNo:Fe.bookNo[0]});kt.value=!1,Xe.value=e.data}i((async()=>{var e,t;await Promise.all([Tt(),Yt(),_t()]),function(){var e;if(Xe.value.order&&(null==(e=Xe.value.order)?void 0:e.prices.length)>0){const e=Xe.value.order.prices.find((e=>n(e.priceStartTime).format("YYYY-MM-DD")===n(et.value).format("YYYY-MM-DD")));tt.value=e?e.vipPrice.toString():""}}(),ot.value=n(null==(e=Xe.value)?void 0:e.planCheckinTime).format("MM/DD HH:mm").concat(" - ").concat(n(null==(t=Xe.value)?void 0:t.planCheckoutTime).format("MM/DD HH:mm")),$t()})),s((()=>({_orderNo:Fe.bookNo})),(({_orderNo:e})=>{Yt()}));const wt=a({visible:!1,bookNo:"",batchNo:"",orderNo:"",rtCode:"",rtName:"",rtState:"",rNos:[],planCheckinTime:"",planCheckoutTime:"",roomNum:0});function Mt(){st.value=!0,$t()}async function St(){st.value=!1,Yt()}function Dt(){const e=ct.value.dataList.filter((e=>e.roomNum>0)),t=Xe.value.retainTime&&n(Xe.value.retainTime).isValid()?n(Xe.value.retainTime).format("YYYY-MM-DD HH:mm"):"",o={...Ze,bookNo:Xe.value.bookNo,channelCode:Xe.value.channelCode,checkinType:Xe.value.checkinType,orderSource:Xe.value.orderSource,guestSrcType:Xe.value.guestSrcType,guestCode:Xe.value.guestCode,bookType:Xe.value.bookType,hourCode:Xe.value.hourCode,planCheckinTime:n(Xe.value.planCheckinTime).format("YYYY-MM-DD HH:mm"),planCheckoutTime:n(Xe.value.planCheckoutTime).format("YYYY-MM-DD HH:mm"),retainTime:t,contact:Xe.value.contact,phone:Xe.value.phone,checkinPerson:Xe.value.checkinPerson,checkinPhone:Xe.value.checkinPhone,remark:Xe.value.remark,isSendSms:"0",batches:[{batchNo:`${ne(Xe.value.planCheckinTime)}/${ne(Xe.value.planCheckoutTime)}`,days:n(n(Xe.value.planCheckoutTime).format("YYYY-MM-DD")).diff(n(n(n(Xe.value.planCheckinTime).format("YYYY-MM-DD"))),"day"),planCheckinTime:`${ne(Xe.value.planCheckinTime)} ${se(Xe.value.planCheckinTime).substring(11,16)}`,planCheckoutTime:`${ne(Xe.value.planCheckoutTime)} ${se(Xe.value.planCheckoutTime).substring(11,16)}`,bookRoomTypes:e}]};Q.updateGeneralBook(o).then((e=>{0===e.code?(_.success(Ke("orderUpdateSuccess")),St(),$t()):_.error(e.msg)}))}function xt(){it.value=!0}a({expandRowKeys:[]});const Pt=a({}),Ot=a();function Ut(){Pt.value={},async function(){if(Ge("refresh"),await Yt(),Ge("success"),Pt.value.orderNo){const e=Xe.value.bookRooms.find((e=>Pt.value.orderNo===e.orderNo));Ot.value.refresh([{rNo:e.rNo,rtName:e.rtName}])}}()}function Vt(){$t(),Yt()}function jt(e){return e.getTime()<Date.now()-864e5}function Ht(e){const t=new Date(Xe.value.planCheckinTime).getTime();return e.getTime()<=t}function At(e){const t=new Date(Xe.value.planCheckinTime).getTime(),o=new Date(Xe.value.planCheckoutTime).getTime();return t>e.getTime()+864e5||e.getTime()>o}function Jt(){const e=[],t=new Date;if(n(Xe.value.planCheckinTime).format("YYYY-MM-DD")===n(t).format("YYYY-MM-DD"))for(let o=0;o<24;o++)o>=t.getHours()||e.push(o);return e}function It(e,t,o){const a=[],r=new Date,l=n(Xe.value.planCheckinTime).format("YYYY-MM-DD"),i=n(r).format("YYYY-MM-DD"),s=n(r).format("H");if(l===i&&e<=s)for(let n=0;n<60;n++)r.getMinutes()<=n||a.push(n);return a}function Et(){var e;if(Xe.value.checkinType!==re.HOUR_ROOM){const e=n(Xe.value.planCheckoutTime).format("HH:mm");n(Xe.value.planCheckinTime).format("YYYY-MM-DD")>=n(Xe.value.planCheckoutTime).format("YYYY-MM-DD")&&(Xe.value.planCheckoutTime=`${n(Xe.value.planCheckinTime).add(1,"day").format("YYYY-MM-DD")} ${e}`)}else{const t=null==(e=le.find((e=>e.key===Xe.value.hourCode)))?void 0:e.value;Xe.value.planCheckoutTime=`${n(Xe.value.planCheckinTime).add(t,"hour").format("YYYY-MM-DD HH:mm")}`}mt.value=!1,$t()}function Lt(e){e?dt.value++:setTimeout((()=>{dt.value=Math.max(0,dt.value-1)}),100)}const qt=a({visible:!1,rtCode:"",initialPriceList:[{bkNum:0,price:0,priceDate:"",priceStrategyCode:"",roomBkNum:0,vipPrice:0,week:0}]});function Bt(e,t){ct.value.dataList.forEach((o=>{o.rtCode===t&&(o.dayPrices=e,o.vipPrice=e[0].vipPrice)}))}function $t(){const e={...Ze,bookNo:Xe.value.bookNo,channelCode:Xe.value.channelCode,checkinType:Xe.value.checkinType,guestSrcType:Xe.value.guestSrcType,guestCode:Xe.value.guestCode,delayMinute:0,orderSource:Xe.value.orderSource,hourCode:Xe.value.checkinType===re.HOUR_ROOM?Xe.value.hourCode:"",planCheckinTime:n(Xe.value.planCheckinTime).format("YYYY-MM-DD HH:mm"),planCheckoutTime:n(Xe.value.planCheckoutTime).format("YYYY-MM-DD HH:mm")};bt.value=!0,Q.roomtypeList(e).then((e=>{bt.value=!1,0===e.code&&(ct.value.dataList=e.data.map((e=>(e.vipPrice=e.dayPrices[0].vipPrice,e.bookRooms=e.bookRooms??[],e))),ct.value.dataList.map((e=>{e.roomNum=0;for(let t=0;t<Xe.value.bookRooms.length;t++){const o=Xe.value.bookRooms[t];e.rtCode===o.rtCode&&(o.rCode&&e.bookRooms.push({preOccupied:"0",state:o.rtState,rNo:o.rNo,rCode:o.rCode}),e.roomNum+=1,e.bkNum=o.roomPkPrices[0].bkNum)}return e.oldRoomNum=JSON.parse(JSON.stringify(e.roomNum)),e})),zt())}))}function zt(){const e=[];ct.value.dataList.forEach((t=>{t.bookRooms.length>0&&e.push(t.rtCode)})),ft.value=e}function Wt(e){ct.value.dataList.forEach((t=>{t.rtCode===e.rtCode&&(t.bookRooms=e.bookRooms)})),zt()}function Ft(){Y.confirm(Ke("confirmAutoArrange"),{confirmButtonText:Ke("confirm"),cancelButtonText:Ke("cancel"),type:"warning"}).then((()=>{Q.autoArrangeRooms({...Ze,bookNo:Xe.value.bookNo,batchNo:Xe.value.bookRooms[0].batchNo}).then((e=>{0===e.code&&(_.success(Ke("arrangeSuccess")),Yt())}))}))}const Gt=a(!1),Kt=a(!1),Qt=a(!1);function Xt(){Q.updateRemarkOutOrderNo({...Ze,bookNo:Xe.value.bookNo,remark:Xe.value.remark}).then((e=>{0===e.code&&(_.success(Ke("orderRemarkUpdateSuccess")),Kt.value=!1)}))}function Zt(){Q.updateRemarkOutOrderNo({...Ze,bookNo:Xe.value.bookNo,outOrderNo:Xe.value.outOrderNo}).then((e=>{0===e.code&&(_.success(Ke("externalOrderNoUpdateSuccess")),Gt.value=!1)}))}function eo(){Q.updateRemarkOutOrderNo({...Ze,bookNo:Xe.value.bookNo,outOrderRemark:Xe.value.outOrderRemark}).then((e=>{0===e.code&&(_.success(Ke("externalOrderRemarkUpdateSuccess")),Qt.value=!1)}))}function to(){var e;const t=n(Xe.value.planCheckoutTime),o=n(et.value);if(t.isBefore(o))return _.warning(Ke("orderPastCheckoutTimeWarning"));{ht.value.show();const t=w(((null==(e=Xe.value)?void 0:e.bookRooms)||[]).filter((e=>!e.rNo)),"rtCode")||[];Ct.value=M(t,"roomPkPrices"),gt.value={...yt},Ct.value.length>0&&(gt.value.rtCode=Ct.value[0].rtCode,oo())}}function oo(){const e={...gt.value};delete e.upgradeRtCode,Q.getRoomtype(e).then((({data:e})=>{Rt.value=e,gt.value.upgradeRtCode=""}))}function ao(){vt.value&&vt.value.formRef.resetFields();for(const e in gt.value)gt.value[e]=yt[e];ht.value.loading=!1,ht.value.visible=!1}const ro=l((()=>Xe.value.bookRooms&&Xe.value.bookRooms.some((e=>e.orderType===ie.JOIN)))),lo=a(!1);function io(){lo.value=!0}function no(){lo.value=!1,Yt(),Ge("refresh")}const so=a(!1);function uo(){const e=Xe.value.bookRooms.find((e=>e.orderType===ie.JOIN));if(!e)return void _.error("未找到联房订单");const t={gcode:Qe.gcode,hcode:Qe.hcode,orderNo:e.orderNo};X.quitMergeRoom(t).then((e=>{0===e.code?(_.success(Ke("unlinkRoomSuccess")),Yt(),Ge("refresh"),so.value=!1):_.error(e.msg)}))}return(e,t)=>{const o=S,a=D,r=x,l=P,i=O,s=U,Y=V,w=j,M=H,X=A,Z=z,ee=J,te=I,oe=W,ae=E,be=K,Qe=F,Ze=G,et=L,tt=q("auth"),ot=B;return u(),d("div",null,[["no_check_in"].includes(Fe.state)?(u(),m(i,{key:0,class:"my-[10px]",shadow:"never"},{default:c((()=>[p(l,null,{default:c((()=>[p(r,{span:24,class:"dropdown-container"},{default:c((()=>[b("div",null,[k(st)?(u(),d("div",fe,[p(a,{content:k(mt)?"请先确认日期时间选择":"",disabled:!k(mt),placement:"top"},{default:c((()=>[p(o,{type:"primary",plain:"",disabled:k(mt),onClick:Dt},{default:c((()=>[f(h(k(Ke)("confirmModify")),1)])),_:1},8,["disabled"])])),_:1},8,["content","disabled"]),p(o,{type:"primary",plain:"",onClick:St},{default:c((()=>[f(h(k(Ke)("cancelModify")),1)])),_:1})])):(u(),d("div",ke,[p(o,{type:"primary",plain:"",onClick:Mt},{default:c((()=>[f(h(k(Ke)("orderModify")),1)])),_:1}),"cancel"!==k(Xe).state?(u(),m(o,{key:0,type:"primary",plain:"",onClick:xt},{default:c((()=>[f(h(k(Ke)("cancelOrder")),1)])),_:1})):v("",!0),p(o,{type:"primary",plain:""},{default:c((()=>[f(h(k(Ke)("sendSMS")),1)])),_:1}),"no_check_in"===Fe.state?(u(),m(o,{key:1,type:"primary",plain:"",onClick:io},{default:c((()=>[f(h(k(Ke)("joinRoom")),1)])),_:1})):v("",!0),"no_check_in"===Fe.state&&k(ro)?(u(),m(o,{key:2,type:"primary",plain:"",onClick:t[0]||(t[0]=e=>so.value=!0)},{default:c((()=>[f(h(k(Ke)("quitJoinRoom")),1)])),_:1})):v("",!0)]))])])),_:1})])),_:1})])),_:1})):v("",!0),p(te,{size:"default","label-width":"140px","inline-message":"",inline:"","label-suffix":"："},{default:c((()=>[p(l,{gutter:24},{default:c((()=>[p(r,{span:10,style:{"padding-right":"0"}},{default:c((()=>[p(i,{shadow:"never"},{header:c((()=>[b("span",null,h(k(Ke)("orderInfo")),1)])),default:c((()=>[p(l,null,{default:c((()=>[p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("bookingNumber")},{default:c((()=>[f(h(k(Xe).bookNo),1)])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("channel")},{default:c((()=>[f(h(k(Xe).channelName),1)])),_:1},8,["label"])])),_:1})])),_:1}),p(l,null,{default:c((()=>[p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("checkinType")},{default:c((()=>[f(h(k(Xe).checkinTypeName),1)])),_:1},8,["label"])])),_:1}),k(Xe).checkinType===k(re).HOUR_ROOM?(u(),m(r,{key:0,span:12},{default:c((()=>[p(s,{label:k(Ke)("hourDuration")},{default:c((()=>{var e;return[f(h(null==(e=k(le).find((e=>e.key===k(Xe).hourCode)))?void 0:e.name),1)]})),_:1},8,["label"])])),_:1})):v("",!0)])),_:1}),p(l,null,{default:c((()=>[p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("guestSource")},{default:c((()=>[f(h(k(Xe).guestSrcTypeName),1)])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("name")},{default:c((()=>[b("span",null,h(k(Xe).guestName),1)])),_:1},8,["label"])])),_:1})])),_:1}),p(l,null,{default:c((()=>[p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("arrivalTime")},{default:c((()=>[k(st)?(u(),m(Y,{key:0,modelValue:k(Xe).planCheckinTime,"onUpdate:modelValue":t[1]||(t[1]=e=>k(Xe).planCheckinTime=e),type:"datetime",clearable:!1,"disabled-date":jt,"disabled-hours":Jt,"disabled-minutes":It,format:"YYYY-MM-DD HH:mm","date-format":"MMM DD, YYYY","time-format":"HH:mm",style:{width:"160px"},onChange:Et,onVisibleChange:Lt},null,8,["modelValue"])):(u(),d("span",he,h(k(n)(k(Xe).planCheckinTime).format("YYYY-MM-DD HH:mm")),1))])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("departureTime")},{default:c((()=>[k(st)?(u(),m(Y,{key:0,modelValue:k(Xe).planCheckoutTime,"onUpdate:modelValue":t[2]||(t[2]=e=>k(Xe).planCheckoutTime=e),type:"datetime",clearable:!1,disabled:k(Xe).checkinType===k(re).HOUR_ROOM,"disabled-date":Ht,format:"YYYY-MM-DD HH:mm","date-format":"MMM DD, YYYY","time-format":"HH:mm",style:{width:"160px"},onChange:$t,onVisibleChange:Lt},null,8,["modelValue","disabled"])):(u(),d("span",ve,h(k(n)(k(Xe).planCheckoutTime).format("YYYY-MM-DD HH:mm")),1))])),_:1},8,["label"])])),_:1})])),_:1}),p(l,null,{default:c((()=>[k(Xe).checkinType!==k(re).HOUR_ROOM?(u(),m(r,{key:0,span:12},{default:c((()=>[p(s,{label:k(Ke)("bookingDays")},{default:c((()=>[b("span",null,h(k(n)(k(n)(k(Xe).planCheckoutTime).format("YYYY-MM-DD")).diff(k(n)(k(n)(k(Xe).planCheckinTime).format("YYYY-MM-DD")),"day")),1)])),_:1},8,["label"])])),_:1})):v("",!0),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("retain")},{default:c((()=>[k(st)?(u(),m(Y,{key:0,modelValue:k(Xe).retainTime,"onUpdate:modelValue":t[3]||(t[3]=e=>k(Xe).retainTime=e),type:"datetime",format:"YYYY-MM-DD HH:mm","disabled-date":At,"date-format":"MMM DD, YYYY","time-format":"HH:mm",style:{width:"160px"}},null,8,["modelValue"])):(u(),d("span",ye,h(k(Xe).retainTime?k(n)(k(Xe).retainTime).format("YYYY-MM-DD HH:mm"):""),1))])),_:1},8,["label"])])),_:1})])),_:1}),p(l,null,{default:c((()=>[p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("orderSource")},{default:c((()=>[f(h(k(Xe).orderSourceName),1)])),_:1},8,["label"])])),_:1})])),_:1})])),_:1}),p(i,{shadow:"never",class:"mt-[12px]"},{header:c((()=>[b("div",ge,[b("span",null,h(k(Ke)("guestInfo")),1)])])),default:c((()=>[p(l,null,{default:c((()=>[p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("booker")},{default:c((()=>[k(st)?(u(),m(w,{key:0,modelValue:k(Xe).contact,"onUpdate:modelValue":t[4]||(t[4]=e=>k(Xe).contact=e),style:{width:"160px"}},null,8,["modelValue"])):(u(),d("span",Ce,h(k(Xe).contact),1))])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("bookerPhone")},{default:c((()=>[k(st)?(u(),m(w,{key:0,modelValue:k(Xe).phone,"onUpdate:modelValue":t[5]||(t[5]=e=>k(Xe).phone=e),style:{width:"160px"}},null,8,["modelValue"])):(u(),d("span",Re,h(k(Xe).phone),1))])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("checkinPerson")},{default:c((()=>[k(st)?(u(),m(w,{key:0,modelValue:k(Xe).checkinPerson,"onUpdate:modelValue":t[6]||(t[6]=e=>k(Xe).checkinPerson=e),style:{width:"160px"}},null,8,["modelValue"])):(u(),d("span",Ne,h(k(Xe).checkinPerson),1))])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("checkinPhone")},{default:c((()=>[k(st)?(u(),m(w,{key:0,modelValue:k(Xe).checkinPhone,"onUpdate:modelValue":t[7]||(t[7]=e=>k(Xe).checkinPhone=e),style:{width:"160px"}},null,8,["modelValue"])):(u(),d("span",Te,h(k(Xe).checkinPhone),1))])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("OTAExternal")},{default:c((()=>[k(Gt)?(u(),d("div",_e,[p(w,{modelValue:k(Xe).outOrderNo,"onUpdate:modelValue":t[8]||(t[8]=e=>k(Xe).outOrderNo=e),style:{width:"160px"}},null,8,["modelValue"]),b("div",Ye,[p(o,{type:"primary",link:"",onClick:Zt},{default:c((()=>[f(h(k(Ke)("save")),1)])),_:1}),p(o,{link:"",onClick:t[9]||(t[9]=e=>Gt.value=!1)},{default:c((()=>[f(h(k(Ke)("cancel")),1)])),_:1})])])):(u(),d("div",we,[b("span",null,h(k(Xe).outOrderNo),1),k(st)?v("",!0):(u(),m(o,{key:0,type:"primary",link:"",onClick:t[10]||(t[10]=e=>Gt.value=!0)},{default:c((()=>[f(h(k(Ke)("edit")),1)])),_:1}))]))])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("guarantyMethod")},{default:c((()=>[f(h(k(Xe).guarantyStyleName),1)])),_:1},8,["label"])])),_:1}),p(r,{span:12},{default:c((()=>[p(s,{label:k(Ke)("salesperson")},{default:c((()=>[f(h(k(Xe).seller),1)])),_:1},8,["label"])])),_:1}),p(r,{span:24},{default:c((()=>[p(s,{label:k(Ke)("orderRemark"),style:{width:"100%"}},{default:c((()=>[k(Kt)?(u(),d("div",Me,[p(w,{modelValue:k(Xe).remark,"onUpdate:modelValue":t[11]||(t[11]=e=>k(Xe).remark=e),type:"textarea",rows:3,placeholder:k(Ke)("inputRemark")},null,8,["modelValue","placeholder"]),b("div",Se,[p(o,{type:"primary",link:"",onClick:Xt},{default:c((()=>[f(h(k(Ke)("save")),1)])),_:1}),p(o,{link:"",onClick:t[12]||(t[12]=e=>Kt.value=!1)},{default:c((()=>[f(h(k(Ke)("cancel")),1)])),_:1})])])):(u(),d("div",De,[b("span",null,h(k(Xe).remark),1),k(st)?v("",!0):(u(),m(o,{key:0,type:"primary",link:"",onClick:t[13]||(t[13]=e=>Kt.value=!0)},{default:c((()=>[f(h(k(Ke)("edit")),1)])),_:1}))]))])),_:1},8,["label"])])),_:1}),p(r,{span:24},{default:c((()=>[p(s,{label:k(Ke)("externalOrderRemark"),style:{width:"100%"}},{default:c((()=>[k(Qt)?(u(),d("div",xe,[p(w,{modelValue:k(Xe).outOrderRemark,"onUpdate:modelValue":t[14]||(t[14]=e=>k(Xe).outOrderRemark=e),type:"textarea",rows:3,placeholder:k(Ke)("externalOrderRemark")},null,8,["modelValue","placeholder"]),b("div",Pe,[p(o,{type:"primary",link:"",onClick:eo},{default:c((()=>[f(h(k(Ke)("save")),1)])),_:1}),p(o,{link:"",onClick:t[15]||(t[15]=e=>Qt.value=!1)},{default:c((()=>[f(h(k(Ke)("cancel")),1)])),_:1})])])):(u(),d("div",Oe,[b("span",null,h(k(Xe).outOrderRemark),1),k(st)?v("",!0):(u(),m(o,{key:0,type:"primary",link:"",onClick:t[16]||(t[16]=e=>Qt.value=!0)},{default:c((()=>[f(h(k(Ke)("edit")),1)])),_:1}))]))])),_:1},8,["label"])])),_:1})])),_:1})])),_:1})])),_:1}),p(r,{span:14},{default:c((()=>[p(i,{shadow:"never"},{header:c((()=>[f(h(k(Ke)("checkin")),1)])),default:c((()=>[k(st)?g((u(),m(X,{key:1,data:k(ct).dataList,height:"100%","row-key":"rtCode","expand-row-keys":k(ft),border:""},{default:c((()=>[p(M,{type:"expand"},{default:c((e=>[e.row.bookRooms&&e.row.bookRooms.length>0?(u(),d("div",He,[(u(!0),d(y,null,R(e.row.bookRooms,((t,o)=>(u(),m(Z,{key:t.rtCode,style:{"margin-right":"5px"},closable:"",onClose:t=>function(e,t){const o=ct.value.dataList.findIndex((t=>t.rtCode===e.rtCode)),a=ct.value.dataList[o].bookRooms.indexOf(t);ct.value.dataList[o].bookRooms.splice(a,1),zt()}(e.row,o)},{default:c((()=>[f(h(t.rNo),1)])),_:2},1032,["onClose"])))),128))])):(u(),d("span",Ae,h(k(Ke)("noArrangeRoom")),1))])),_:1}),p(M,{prop:"rtName",label:k(Ke)("roomType")},null,8,["label"]),p(M,{label:k(Ke)("discountSalePrice"),align:"left","min-width":"180px"},{default:c((e=>[k(ut)?(u(),d("div",Je,[p(ee,{modelValue:e.row.vipPrice,"onUpdate:modelValue":t=>e.row.vipPrice=t,min:0,precision:2,controls:!1,style:{width:"100px","margin-right":"5px"},onChange:t=>{var o;(o=e.row).dayPrices=o.dayPrices.map((e=>(e.vipPrice=o.vipPrice,e)))}},null,8,["modelValue","onUpdate:modelValue","onChange"]),f(" / "+h(e.row.price)+" ",1),p(o,{type:"primary",text:"",onClick:t=>{return o=e.row,qt.value.rtCode=o.rtCode,qt.value.initialPriceList=o.dayPrices,void(qt.value.visible=!0);var o}},{default:c((()=>[f(h(k(Ke)("changePrice")),1)])),_:2},1032,["onClick"])])):(u(),d("div",Ie,[p(ee,{modelValue:e.row.vipPrice,"onUpdate:modelValue":t=>e.row.vipPrice=t,min:0,precision:2,controls:!1,style:{width:"100px","margin-right":"5px"},disabled:""},null,8,["modelValue","onUpdate:modelValue"]),f(" / "+h(e.row.price),1)]))])),_:1},8,["label"]),p(M,{label:k(Ke)("availableRooms"),prop:"canSellNum",align:"right",width:"90px"},null,8,["label"]),p(M,{label:k(Ke)("availableOverbook"),prop:"canOverNum",align:"right",width:"90px"},null,8,["label"]),p(M,{label:k(Ke)("bookingRooms"),align:"center",width:"145px"},{default:c((e=>[p(ee,{modelValue:e.row.roomNum,"onUpdate:modelValue":t=>e.row.roomNum=t,min:0,max:e.row.canSellNum+e.row.canOverNum+e.row.oldRoomNum,precision:0,"value-on-clear":0,style:{width:"120px"},onChange:t=>{var o;(o=e.row).bookRooms&&o.bookRooms.length>o.roomNum&&(o.roomNum=o.bookRooms.length,_.warning(Ke("reduceRoomWarning",{roomCount:o.bookRooms.length})))}},null,8,["modelValue","onUpdate:modelValue","max","onChange"])])),_:1},8,["label"]),p(M,{label:k(Ke)("breakfastCopies"),align:"center",width:"150px"},{default:c((e=>[p(ee,{modelValue:e.row.bkNum,"onUpdate:modelValue":t=>e.row.bkNum=t,min:0,precision:0,"value-on-clear":0,controls:!1,style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue"])])),_:1},8,["label"]),p(M,{label:k(Ke)("operation"),width:"120px",align:"center"},{default:c((e=>[p(o,{type:"primary",size:"small",onClick:t=>{return o=e.row,wt.value.rNos=[],""===Xe.value.planCheckinTime||""===Xe.value.planCheckoutTime||!1===n(Xe.value.planCheckoutTime).isAfter(n(Xe.value.planCheckinTime))?_.warning(Ke("setCheckinCheckoutTime")):o.roomNum<=0?_.warning(Ke("selectRoomNumber")):(pt.value=!1,wt.value.rtCode=o.rtCode,wt.value.rtName=o.rtName,wt.value.roomNum=o.roomNum,wt.value.planCheckinTime=`${ne(Xe.value.planCheckinTime)} ${se(Xe.value.planCheckinTime).substring(11,16)}`,wt.value.planCheckoutTime=`${ne(Xe.value.planCheckoutTime)} ${se(Xe.value.planCheckoutTime).substring(11,16)}`,o.bookRooms&&o.bookRooms.forEach((e=>{wt.value.rNos.push(e.rNo)})),nt.value=o.bookRooms,void(wt.value.visible=!0));var o}},{default:c((()=>[f(h(k(Ke)("arrangeRoom")),1)])),_:2},1032,["onClick"])])),_:1},8,["label"])])),_:1},8,["data","expand-row-keys"])),[[ot,k(bt)]]):(u(),d(y,{key:0},[b("div",Ue,[b("div",null,h(k(Ke)("rooms"))+"："+h(k(Xe).bookRooms.length)+h(k(Ke)("roomUnit")),1),["no_check_in"].includes(Fe.state)?g((u(),d("div",Ve,[p(o,{onClick:to},{default:c((()=>[f(h(k(Ke)("roomUpgrade")),1)])),_:1}),p(o,{onClick:Ft},{default:c((()=>[f(h(k(Ke)("autoArrangeRoom")),1)])),_:1})])),[[tt,"pms:book-room:update:arrange"]]):v("",!0)]),g((u(),m(X,{ref:"singleTableRef",data:k(Xe).bookRooms,"highlight-current-row":"",style:{width:"100%"}},{default:c((()=>[p(M,{label:k(Ke)("roomNumber")},{default:c((e=>[e.row.rNo&&"check_in"===e.row.state?(u(),d("span",je,h(e.row.rNo),1)):v("",!0),"no_check_in"===e.row.state?(u(),m(o,{key:1,disabled:k(C)("pms:book-room:update:arrange"),link:"",type:"primary",onClick:t=>{return o=e.row,pt.value=!0,wt.value.rNos=[],wt.value.rtCode=o.rtCode,wt.value.rtName=o.rtName,wt.value.orderNo=o.orderNo,wt.value.batchNo=o.batchNo,wt.value.roomNum=1,wt.value.planCheckinTime=n(o.planCheckinTime).format("YYYY-MM-DD HH:mm"),wt.value.planCheckoutTime=n(o.planCheckoutTime).format("YYYY-MM-DD HH:mm"),o.rNo&&wt.value.rNos.push(o.rNo),void(wt.value.visible=!0);var o}},{default:c((()=>[f(h(e.row.rNo?e.row.rNo:k(Ke)("arrangeRoom")),1)])),_:2},1032,["disabled","onClick"])):v("",!0)])),_:1},8,["label"]),p(M,{prop:"rtName",label:k(Ke)("roomType")},null,8,["label"]),p(M,{label:k(Ke)("roomPrice")},{default:c((e=>[f(h(e.row.roomPkPrices[0].vipPrice),1)])),_:1},8,["label"]),p(M,{label:k(Ke)("breakfastCopies"),align:"center"},{default:c((e=>[f(h(e.row.roomPkPrices[0].bkNum),1)])),_:1},8,["label"]),p(M,{label:k(Ke)("operation"),align:"center"},{default:c((e=>[b("div",null,["no_check_in"===e.row.state?g((u(),m(o,{key:0,link:"",type:"primary",onClick:t=>{return o=e.row,Pt.value=o,void Ot.value.open({rNos:o.rNo?[o.rNo]:[],rtCode:o.rtCode,rtName:o.rtName,orderNo:o.orderNo,batchNo:o.batchNo,roomNum:1,planCheckinTime:n(o.planCheckinTime).format("YYYY-MM-DD HH:mm"),planCheckoutTime:n(o.planCheckoutTime).format("YYYY-MM-DD HH:mm"),bkNum:o.roomPkPrices[0].bkNum,bookNo:Xe.value.bookNo,data:[{rNo:o.rNo,rtName:o.rtName}],rooms:o.bookRooms});var o}},{default:c((()=>[f(h(k(Ke)("checkin")),1)])),_:2},1032,["onClick"])),[[tt,"pms:order:create:check-in"]]):v("",!0),"check_in"===e.row.state?g((u(),m(o,{key:1,link:"",type:"primary",onClick:t=>{return o=e.row,void Ge("seeDetailed",{orderNo:o.orderNo,state:o.state,togetherCode:o.togetherCode});var o}},{default:c((()=>[f(h(k(Ke)("viewDetails")),1)])),_:2},1032,["onClick"])),[[tt,"pms:order:query:get-order-detail"]]):v("",!0)])])),_:1},8,["label"])])),_:1},8,["data"])),[[ot,k(kt)]])],64))])),_:1})])),_:1})])),_:1})])),_:1}),k(wt).visible?(u(),m(de,N({key:1,modelValue:k(wt).visible,"onUpdate:modelValue":t[17]||(t[17]=e=>k(wt).visible=e)},k(wt),{"is-alone":k(pt),"book-no":k(Xe).bookNo,rooms:k(nt),onSuccess:Wt,onReload:Vt}),null,16,["modelValue","is-alone","book-no","rooms"])):v("",!0),k(qt).visible?(u(),m(ce,{key:2,modelValue:k(qt).visible,"onUpdate:modelValue":t[18]||(t[18]=e=>k(qt).visible=e),"room-list-price":k(qt).initialPriceList,"rt-code":k(qt).rtCode,onSuccess:Bt},null,8,["modelValue","room-list-price","rt-code"])):v("",!0),p(pe,{ref_key:"checkModalRef",ref:Ot,onSuccess:Ut,onReverts:Yt},null,512),k(it)?(u(),m(ue,{key:3,modelValue:k(it),"onUpdate:modelValue":t[19]||(t[19]=e=>T(it)?it.value=e:null),"book-no":k(Xe).bookNo,onSuccess:t[20]||(t[20]=e=>Ge("refresh"))},null,8,["modelValue","book-no"])):v("",!0),p(Ze,{ref_key:"easyDialogRef",ref:ht,title:k(Ke)("roomUpgrade"),"is-body":"","dialog-width":"500","show-cancel-button":"","show-confirm-button":k(Ct).length>0,onSubmit:t[23]||(t[23]=e=>{vt.value.formRef&&vt.value.formRef.validate((async e=>{if(e){const{code:e}=await Q.putUpgrade(gt.value);0===e&&(_.success(Ke("roomUpgradeSuccess")),Vt(),ao())}}))}),onClose:t[24]||(t[24]=e=>ao())},{default:c((()=>[k(Ct).length>0?(u(),m(be,{key:0,ref_key:"easyFormRef",ref:vt,"field-list":k(Nt),model:k(gt),options:{labelSuffix:"："}},{show:c((()=>t[28]||(t[28]=[b("div",{class:"bg-[var(--g-sub-sidebar-menu-hover-bg)] text-[var(--el-color-primary)]",style:{"text-align":"center","font-size":"13px"}},"该预订单下，当前房型中所有【未排房】的房间都会被升级",-1)]))),upgradeRtCode:c((()=>[p(s,{label:"升级房型",prop:"upgradeRtCode",rules:[{required:!0,message:"升级房型不能为空！"}]},{default:c((()=>[p(ae,{modelValue:k(gt).upgradeRtCode,"onUpdate:modelValue":t[21]||(t[21]=e=>k(gt).upgradeRtCode=e),placeholder:"请选择升级房型"},{default:c((()=>[(u(!0),d(y,null,R(k(Rt),(e=>(u(),m(oe,{key:e.rtCode,label:e.rtName,disabled:0===e.canSellNum&&0===e.canOverNum,value:e.rtCode},{default:c((()=>[b("div",Ee,[b("div",null,h(e.rtName),1),b("div",Le,[b("span",qe,[f(h(k(Ke)("availableRooms"))+"：",1),b("span",{class:$(e.canSellNum>0?"text-[var(--el-color-primary)]":"")},h(e.canSellNum),3)]),b("span",Be,[f(h(k(Ke)("availableOverbook"))+"：",1),b("span",{class:$(e.canOverNum>0?"text-[var(--el-color-primary)]":"")},h(e.canOverNum),3)])])])])),_:2},1032,["label","disabled","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),currentRoomType:c((()=>[p(s,{label:"当前房型"},{default:c((()=>[p(ae,{modelValue:k(gt).rtCode,"onUpdate:modelValue":t[22]||(t[22]=e=>k(gt).rtCode=e),placeholder:"请选择当前房型"},{default:c((()=>[(u(!0),d(y,null,R(k(Ct),(e=>(u(),m(oe,{key:e.rtCode,label:e.rtName,value:e.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["field-list","model"])):(u(),m(Qe,{key:1,description:"订单已全部排房，无法升级，请取消排房后再操作"}))])),_:1},8,["title","show-confirm-button"]),k(lo)?(u(),m(me,{key:4,modelValue:k(lo),"onUpdate:modelValue":t[25]||(t[25]=e=>T(lo)?lo.value=e:null),"order-no":"","r-no":"","book-rooms":k(Xe).bookRooms,onSuccess:no},null,8,["modelValue","book-rooms"])):v("",!0),p(et,{modelValue:k(so),"onUpdate:modelValue":t[27]||(t[27]=e=>T(so)?so.value=e:null),title:k(Ke)("outJoinRoom"),width:"500"},{footer:c((()=>[b("div",We,[p(o,{onClick:t[26]||(t[26]=e=>so.value=!1)},{default:c((()=>[f(h(k(Ke)("cancel")),1)])),_:1}),p(o,{type:"primary",onClick:uo},{default:c((()=>[f(h(k(Ke)("confirm")),1)])),_:1})])])),default:c((()=>{var e;return[b("div",$e,h(k(Ke)("selectedRoom"))+" "+h((null==(e=k(Xe).bookRooms.find((e=>e.orderType===k(ie).JOIN)))?void 0:e.rNo)||k(Xe).bookNo),1),b("div",ze,h(k(Ke)("confirmOutJoinRoom")),1)]})),_:1},8,["modelValue","title"])])}}});function Ge(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{orderModify:{t:0,b:{t:2,i:[{t:3}],s:"Modify Order"}},cancelOrder:{t:0,b:{t:2,i:[{t:3}],s:"Cancel Order"}},sendSMS:{t:0,b:{t:2,i:[{t:3}],s:"Send SMS"}},confirmModify:{t:0,b:{t:2,i:[{t:3}],s:"Confirm Modification"}},cancelModify:{t:0,b:{t:2,i:[{t:3}],s:"Cancel Modification"}},orderInfo:{t:0,b:{t:2,i:[{t:3}],s:"Order Information"}},bookingNumber:{t:0,b:{t:2,i:[{t:3}],s:"Pre-order No"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"Channel"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"CheckIn Type"}},hourDuration:{t:0,b:{t:2,i:[{t:3}],s:"Hourly Duration"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source"}},name:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},arrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"Arrival"}},departureTime:{t:0,b:{t:2,i:[{t:3}],s:"Departure"}},bookingDays:{t:0,b:{t:2,i:[{t:3}],s:"Days"}},retain:{t:0,b:{t:2,i:[{t:3}],s:"Retain"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"Order Source"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"Guest Information"}},booker:{t:0,b:{t:2,i:[{t:3}],s:"Booker"}},bookerPhone:{t:0,b:{t:2,i:[{t:3}],s:"Booker Phone"}},checkinPerson:{t:0,b:{t:2,i:[{t:3}],s:"Person"}},checkinPhone:{t:0,b:{t:2,i:[{t:3}],s:"Phone"}},OTAExternal:{t:0,b:{t:2,i:[{t:3}],s:"External"}},guarantyMethod:{t:0,b:{t:2,i:[{t:3}],s:"Guaranty"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"Salesperson"}},orderRemark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},inputRemark:{t:0,b:{t:2,i:[{t:3}],s:"Please enter remarks"}},externalOrderRemark:{t:0,b:{t:2,i:[{t:3}],s:"Ext. Remark"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"Check-in"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"Rooms"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:"roomUnit"}},autoArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"Auto Arrange Room"}},roomUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"Room Upgrade"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"Room No"}},arrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"Arr. Room"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},roomPrice:{t:0,b:{t:2,i:[{t:3}],s:"Room Price"}},breakfastCopies:{t:0,b:{t:2,i:[{t:3}],s:"Breakfast Copies"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"Actions"}},viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"View Details"}},noArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"No rooms arranged"}},discountPrice:{t:0,b:{t:2,i:[{t:3}],s:"Discount Price"}},salePrice:{t:0,b:{t:2,i:[{t:3}],s:"Sale Price"}},discountSalePrice:{t:0,b:{t:2,i:[{t:3}],s:"Disc. Price/Sale Price"}},availableRooms:{t:0,b:{t:2,i:[{t:3}],s:"Avail. Rooms"}},availableOverbook:{t:0,b:{t:2,i:[{t:3}],s:"OBR"}},bookingRooms:{t:0,b:{t:2,i:[{t:3}],s:"Booking Rooms"}},changePrice:{t:0,b:{t:2,i:[{t:3}],s:"Change Price"}},orderUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Order updated successfully"}},reduceRoomWarning:{t:0,b:{t:2,i:[{t:3,v:"Already arranged "},{t:4,k:"roomCount"},{t:3,v:" rooms. To reduce the number of bookings, please remove arranged rooms first."}]}},setCheckinCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Please set the arrival and departure times first, and ensure that the departure time is later than the arrival time."}},selectRoomNumber:{t:0,b:{t:2,i:[{t:3}],s:"Please select the number of rooms for the current room type first."}},confirmAutoArrange:{t:0,b:{t:2,i:[{t:3}],s:"Are you sure you want to auto-arrange rooms?"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"Confirm"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},arrangeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Rooms arranged successfully"}},joinRoom:{t:0,b:{t:2,i:[{t:3}],s:"Join Room"}},quitJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"Quit Join Room"}},outJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"Quit Join Room"}},selectedRoom:{t:0,b:{t:2,i:[{t:3}],s:"Selected Room:"}},confirmOutJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"Are you sure you want to quit the joint room?"}},unlinkRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Successfully quit joint room"}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"Edit"}},orderRemarkUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Order remark updated successfully"}},externalOrderNoUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"External order number updated successfully"}},externalOrderRemarkUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"External order remark updated successfully"}},orderPastCheckoutTimeWarning:{t:0,b:{t:2,i:[{t:3}],s:"Order has passed the latest checkout time, please modify the order arrival and departure times"}},roomUpgradeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Room upgrade successful!"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"Free Upgrade"}},currentRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Current Room Type"}},upgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Upgrade Room Type"}}},"zh-cn":{orderModify:{t:0,b:{t:2,i:[{t:3}],s:"订单修改"}},cancelOrder:{t:0,b:{t:2,i:[{t:3}],s:"取消订单"}},sendSMS:{t:0,b:{t:2,i:[{t:3}],s:"发送短信"}},confirmModify:{t:0,b:{t:2,i:[{t:3}],s:"确认修改"}},cancelModify:{t:0,b:{t:2,i:[{t:3}],s:"取消修改"}},orderInfo:{t:0,b:{t:2,i:[{t:3}],s:"订单信息"}},bookingNumber:{t:0,b:{t:2,i:[{t:3}],s:"预订单号"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"渠道"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},hourDuration:{t:0,b:{t:2,i:[{t:3}],s:"钟点时长"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"客源"}},name:{t:0,b:{t:2,i:[{t:3}],s:"姓名"}},arrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"预抵时间"}},departureTime:{t:0,b:{t:2,i:[{t:3}],s:"预离时间"}},bookingDays:{t:0,b:{t:2,i:[{t:3}],s:"预订天数"}},retain:{t:0,b:{t:2,i:[{t:3}],s:"保留"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"订单来源"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"客人信息"}},booker:{t:0,b:{t:2,i:[{t:3}],s:"预订人"}},bookerPhone:{t:0,b:{t:2,i:[{t:3}],s:"预订人电话"}},checkinPerson:{t:0,b:{t:2,i:[{t:3}],s:"入住人"}},checkinPhone:{t:0,b:{t:2,i:[{t:3}],s:"入住人电话"}},OTAExternal:{t:0,b:{t:2,i:[{t:3}],s:"外部订单号"}},guarantyMethod:{t:0,b:{t:2,i:[{t:3}],s:"担保方式"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"销售员"}},orderRemark:{t:0,b:{t:2,i:[{t:3}],s:"订单备注"}},inputRemark:{t:0,b:{t:2,i:[{t:3}],s:"请输入备注"}},externalOrderRemark:{t:0,b:{t:2,i:[{t:3}],s:"外部订单备注"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"办理入住"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"房间"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:"间"}},autoArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"AI自动排房"}},roomUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"房型升级"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},arrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"排房"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},roomPrice:{t:0,b:{t:2,i:[{t:3}],s:"房价"}},breakfastCopies:{t:0,b:{t:2,i:[{t:3}],s:"赠早/份"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"查看详单"}},noArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"未排房"}},discountPrice:{t:0,b:{t:2,i:[{t:3}],s:"优惠价"}},salePrice:{t:0,b:{t:2,i:[{t:3}],s:"售价"}},discountSalePrice:{t:0,b:{t:2,i:[{t:3}],s:"优惠价/销售价"}},availableRooms:{t:0,b:{t:2,i:[{t:3}],s:"可售数"}},availableOverbook:{t:0,b:{t:2,i:[{t:3}],s:"可超数"}},bookingRooms:{t:0,b:{t:2,i:[{t:3}],s:"预订间数"}},changePrice:{t:0,b:{t:2,i:[{t:3}],s:"改价"}},orderUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"修改预订单成功"}},reduceRoomWarning:{t:0,b:{t:2,i:[{t:3,v:"已排房"},{t:4,k:"roomCount"},{t:3,v:"间,若要减少预订间数，请先删减排房"}]}},setCheckinCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"请先设置预抵和预离时间，并使预离时间晚于预抵时间."}},selectRoomNumber:{t:0,b:{t:2,i:[{t:3}],s:"请先选择当前房型预订间数"}},confirmAutoArrange:{t:0,b:{t:2,i:[{t:3}],s:"确定AI自动排房吗?"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"确认"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},arrangeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"排房成功"}},joinRoom:{t:0,b:{t:2,i:[{t:3}],s:"加入联房"}},quitJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"退出联房"}},outJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"退出联房"}},selectedRoom:{t:0,b:{t:2,i:[{t:3}],s:"选中房间："}},confirmOutJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"是否确定退出联房？"}},unlinkRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"退出联房成功"}},save:{t:0,b:{t:2,i:[{t:3}],s:"保存"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"修改"}},orderRemarkUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"订单备注修改成功"}},externalOrderNoUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"外部订单号修改成功"}},externalOrderRemarkUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"外部订单备注修改成功"}},orderPastCheckoutTimeWarning:{t:0,b:{t:2,i:[{t:3}],s:"订单已过最晚预离时间，请修改订单预抵预离时间"}},roomUpgradeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"房型升级成功！"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"免费升级"}},currentRoomType:{t:0,b:{t:2,i:[{t:3}],s:"当前房型"}},upgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"升级房型"}}},km:{orderModify:{t:0,b:{t:2,i:[{t:3}],s:"កែប្រែការកម្មង់"}},cancelOrder:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់ការកម្មង់"}},sendSMS:{t:0,b:{t:2,i:[{t:3}],s:"ផ្ញើសារ SMS"}},confirmModify:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ជាក់ការកែប្រែ"}},cancelModify:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់ការកែប្រែ"}},orderInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានការកម្មង់"}},bookingNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខការកម្មង់"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},checkinType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទចូលស្នាក់"}},hourDuration:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលជាម៉ោង"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពភ្ញៀវ"}},name:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},arrivalTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាមកដល់"}},departureTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលចាកចេញ"}},bookingDays:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនថ្ងៃ"}},retain:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពការកម្មង់"}},guestInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានភ្ញៀវ"}},booker:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកកម្មង់"}},bookerPhone:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទអ្នកកម្មង់"}},checkinPerson:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកចូលស្នាក់"}},checkinPhone:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទចូលស្នាក់"}},OTAExternal:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មង់ក្រៅ"}},guarantyMethod:{t:0,b:{t:2,i:[{t:3}],s:"វិធីធានា"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកលក់"}},orderRemark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំការកម្មង់"}},inputRemark:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលចំណាំ"}},externalOrderRemark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំការកម្មង់ខាងក្រៅ"}},checkin:{t:0,b:{t:2,i:[{t:3}],s:"ចូលស្នាក់នៅ"}},rooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់"}},roomUnit:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់"}},autoArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"រៀបចំបន្ទប់ដោយស្វ័យប្រវត្តិ"}},roomUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"ធ្វើឱ្យបន្ទប់ប្រសើរឡើង"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់"}},arrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"រៀបចំបន្ទប់"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},roomPrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃបន្ទប់"}},breakfastCopies:{t:0,b:{t:2,i:[{t:3}],s:"អាហារព្រឹក/ចំនួន"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"សកម្មភាព"}},viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"មើលព័ត៌មានលម្អិត"}},noArrangeRoom:{t:0,b:{t:2,i:[{t:3}],s:"មិនមានការរៀបចំបន្ទប់"}},discountPrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃបញ្ចុះតម្លៃ"}},salePrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃលក់"}},discountSalePrice:{t:0,b:{t:2,i:[{t:3}],s:"តម្លៃបញ្ចុះតម្លៃ/តម្លៃលក់"}},availableRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់អាចលក់"}},availableOverbook:{t:0,b:{t:2,i:[{t:3}],s:"អាចលក់លើស"}},bookingRooms:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់កម្មង់"}},changePrice:{t:0,b:{t:2,i:[{t:3}],s:"ផ្លាស់ប្តូរតម្លៃ"}},orderUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"កែប្រែការកម្មង់ដោយជោគជ័យ"}},reduceRoomWarning:{t:0,b:{t:2,i:[{t:3,v:"បានរៀបចំបន្ទប់ "},{t:4,k:"roomCount"},{t:3,v:" បន្ទប់ ប្រសិនបើចង់កាត់បន្ថយចំនួនបន្ទប់ដែលបានកម្មង់ សូមលុបការរៀបចំបន្ទប់ជាមុនសិន។"}]}},setCheckinCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"សូមកំណត់ពេលវេលាមកដល់ និងពេលវេលាចាកចេញជាមុនសិន ហើយធ្វើឱ្យពេលវេលាចាកចេញក្រោយពេលវេលាមកដល់។"}},selectRoomNumber:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសចំនួនបន្ទប់សម្រាប់ប្រភេទបន្ទប់បច្ចុប្បន្នជាមុនសិន។"}},confirmAutoArrange:{t:0,b:{t:2,i:[{t:3}],s:"តើអ្នកប្រាកដថាចង់រៀបចំបន្ទប់ដោយស្វ័យប្រវត្តិមែនទេ?"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ជាក់"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},arrangeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"រៀបចំបន្ទប់ដោយជោគជ័យ"}},joinRoom:{t:0,b:{t:2,i:[{t:3}],s:"ចូលរួមបន្ទប់"}},quitJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"ចាកចេញពីបន្ទប់រួម"}},outJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"ចាកចេញពីបន្ទប់រួម"}},selectedRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានជ្រើសរើស៖"}},confirmOutJoinRoom:{t:0,b:{t:2,i:[{t:3}],s:"តើអ្នកប្រាកដថាចង់ចាកចេញពីបន្ទប់រួមទេ?"}},unlinkRoomSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចាកចេញពីបន្ទប់រួមដោយជោគជ័យ"}},save:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"កែប្រែ"}},orderRemarkUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំការកម្មង់ត្រូវបានកែប្រែដោយជោគជ័យ"}},externalOrderNoUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"លេខកម្មង់ខាងក្រៅត្រូវបានកែប្រែដោយជោគជ័យ"}},externalOrderRemarkUpdateSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំការកម្មង់ខាងក្រៅត្រូវបានកែប្រែដោយជោគជ័យ"}},orderPastCheckoutTimeWarning:{t:0,b:{t:2,i:[{t:3}],s:"ការកម្មង់បានកន្លងផុតពេលវេលាចាកចេញចុងក្រោយ សូមកែប្រែពេលវេលាមកដល់ និងចាកចេញនៃការកម្មង់"}},roomUpgradeSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ការធ្វើឱ្យបន្ទប់ប្រសើរឡើងបានជោគជ័យ!"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"ធ្វើឱ្យប្រសើរឡើងដោយឥតគិតថ្លៃ"}},currentRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់បច្ចុប្បន្ន"}},upgradeRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់ដែលធ្វើឱ្យប្រសើរឡើង"}}}}})}Ge(Fe);const Ke=be(Fe,[["__scopeId","data-v-1c112a3e"]]);export{Ke as default};
//# sourceMappingURL=bookingDetail-BY3bduLn.js.map
