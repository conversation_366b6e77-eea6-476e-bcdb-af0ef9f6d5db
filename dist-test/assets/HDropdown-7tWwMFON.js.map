{"version": 3, "file": "HDropdown-7tWwMFON.js", "sources": ["../../src/layouts/ui-kit/HDropdown.vue"], "sourcesContent": ["<template>\r\n  <VDropdown :show-triggers=\"['hover']\" :hide-triggers=\"['hover']\" :auto-hide=\"false\" :popper-triggers=\"['hover']\" :delay=\"200\" v-bind=\"$attrs\">\r\n    <slot />\r\n    <template #popper>\r\n      <slot name=\"dropdown\" />\r\n    </template>\r\n  </VDropdown>\r\n</template>\r\n"], "names": ["_openBlock", "_createBlock", "_component_VDropdown", "_mergeProps", "_ctx", "$attrs", "_withCtx"], "mappings": "kMACc,OAAAA,IAAwBC,EAAAC,EAAAC,EAAA,CAAG,gBAAe,CAAS,SAAG,gBAAgB,CAAA,SAAG,aAAA,EAA6B,kBAAU,CAAA,eAAgB,KAE/HC,EAAMC,gBACSC,GAAA,IAAA,mCAFlBA,GAAA,IAAA"}