{"version": 3, "file": "roomCardLogList-DIxcpNbT.js", "sources": ["../../src/views/order/info/components/orderdetail/roomCardLogList.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"roomCardLog\": {\r\n        \"table\": {\r\n          \"operationTime\": \"Operation Time\",\r\n          \"cardHolder\": \"Card Holder\",\r\n          \"roomNumber\": \"Room No.\",\r\n          \"validPeriod\": \"Valid Period\",\r\n          \"operation\": \"Operation\",\r\n          \"operator\": \"Operator\"\r\n        },\r\n        \"types\": {\r\n          \"newCard\": \"New Card\",\r\n          \"companionCard\": \"Companion Card\",\r\n          \"clearCard\": \"Clear Card\"\r\n        },\r\n        \"messages\": {\r\n          \"fetchError\": \"Failed to fetch room card records:\"\r\n        }\r\n      }\r\n    },\r\n    \"zh-cn\": {\r\n      \"roomCardLog\": {\r\n        \"table\": {\r\n          \"operationTime\": \"操作时间\",\r\n          \"cardHolder\": \"持卡人\",\r\n          \"roomNumber\": \"房号\",\r\n          \"validPeriod\": \"有效期\",\r\n          \"operation\": \"操作\",\r\n          \"operator\": \"操作员\"\r\n        },\r\n        \"types\": {\r\n          \"newCard\": \"制新卡\",\r\n          \"companionCard\": \"制同住卡\",\r\n          \"clearCard\": \"清卡\"\r\n        },\r\n        \"messages\": {\r\n          \"fetchError\": \"获取房卡记录失败:\"\r\n        }\r\n      }\r\n    },\r\n    \"km\": {\r\n      \"roomCardLog\": {\r\n        \"table\": {\r\n          \"operationTime\": \"ពេលវេលាប្រតិបត្តិការ\",\r\n          \"cardHolder\": \"អ្នកកាន់កាត\",\r\n          \"roomNumber\": \"លេខបន្ទប់\",\r\n          \"validPeriod\": \"រយៈពេលមាន\",\r\n          \"operation\": \"ប្រតិបត្តិការ\",\r\n          \"operator\": \"អ្នកប្រតិបត្តិ\"\r\n        },\r\n        \"types\": {\r\n          \"newCard\": \"កាតថ្មី\",\r\n          \"companionCard\": \"កាតដៃគូ\",\r\n          \"clearCard\": \"សម្អាតកាត\"\r\n        },\r\n        \"messages\": {\r\n          \"fetchError\": \"បរាជ័យក្នុងការទាញយកកំណត់ត្រាកាតបន្ទប់:\"\r\n        }\r\n      }\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport roomCardLogApi from '@/api/modules/pms/room/roomCardLog.api'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\nimport { computed, onMounted, ref } from 'vue'\r\n\r\nconst props = defineProps<Props>()\r\nconst { t } = useI18n()\r\ninterface Props {\r\n  orderNo: string\r\n}\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 响应式数据\r\nconst loading = ref(false)\r\nconst tableData = ref([])\r\nconst total = ref(0)\r\n\r\n// 查询参数\r\nconst queryParams = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  orderNo: props.orderNo,\r\n  pageNo: 1,\r\n  pageSize: 10,\r\n})\r\n\r\n// 格式化制卡时间\r\nfunction formatCreateTime(row: any) {\r\n  return row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'\r\n}\r\n\r\n// 格式化有效期\r\nfunction formatPeriodTime(row: any) {\r\n  return row.periodTime ? dayjs(row.periodTime).format('YYYY-MM-DD HH:mm:ss') : '-'\r\n}\r\n\r\n// 格式化操作类型\r\nfunction formatType(row: any) {\r\n  switch (row.type) {\r\n    case '0':\r\n      return t('roomCardLog.types.newCard')\r\n    case '1':\r\n      return t('roomCardLog.types.companionCard')\r\n    case '2':\r\n      return t('roomCardLog.types.clearCard')\r\n    default:\r\n      return '-'\r\n  }\r\n}\r\n\r\n// 获取房卡记录列表\r\nasync function getList() {\r\n  loading.value = true\r\n  try {\r\n    const res = await roomCardLogApi.getRoomCardLogPage(queryParams.value)\r\n    if (res.code === 0) {\r\n      tableData.value = res.data.list || []\r\n      total.value = res.data.total || 0\r\n    }\r\n  }\r\n  catch (error) {\r\n    console.error(t('roomCardLog.messages.fetchError'), error)\r\n  }\r\n  finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 组件挂载时获取数据\r\nonMounted(() => {\r\n  getList()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"room-card-log-list\">\r\n    <el-table v-loading=\"loading\" :data=\"tableData\" stripe border height=\"400\" style=\"width: 100%\">\r\n      <el-table-column prop=\"createTime\" :label=\"t('roomCardLog.table.operationTime')\" width=\"180\" :formatter=\"formatCreateTime\" />\r\n      <el-table-column prop=\"name\" :label=\"t('roomCardLog.table.cardHolder')\" width=\"120\" />\r\n      <el-table-column prop=\"rNo\" :label=\"t('roomCardLog.table.roomNumber')\" width=\"100\" />\r\n      <el-table-column prop=\"periodTime\" :label=\"t('roomCardLog.table.validPeriod')\" width=\"180\" :formatter=\"formatPeriodTime\" />\r\n      <el-table-column prop=\"type\" :label=\"t('roomCardLog.table.operation')\" width=\"100\" :formatter=\"formatType\" />\r\n      <el-table-column prop=\"creator\" :label=\"t('roomCardLog.table.operator')\" width=\"120\" />\r\n    </el-table>\r\n    <Pagination v-model:current-page=\"queryParams.pageNo\" v-model:page-size=\"queryParams.pageSize\" :total=\"total\" @pagination=\"getList\" />\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.room-card-log-list {\r\n  padding: 20px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "tableData", "total", "queryParams", "gcode", "hcode", "orderNo", "pageNo", "pageSize", "formatCreateTime", "row", "createTime", "dayjs", "format", "formatPeriodTime", "periodTime", "formatType", "type", "async", "getList", "value", "res", "roomCardLogApi", "getRoomCardLogPage", "code", "data", "list", "error", "console", "onMounted"], "mappings": "wtBAuEA,MAAMA,EAAQC,GACRC,EAAEA,GAAMC,IAKRC,EAAYC,IAGZC,EAAUC,GAAI,GACdC,EAAYD,EAAI,IAChBE,EAAQF,EAAI,GAGZG,EAAcH,EAAI,CACtBI,MAAOP,EAAUO,MACjBC,MAAOR,EAAUQ,MACjBC,QAASb,EAAMa,QACfC,OAAQ,EACRC,SAAU,KAIZ,SAASC,EAAiBC,GACjB,OAAAA,EAAIC,WAAaC,EAAMF,EAAIC,YAAYE,OAAO,uBAAyB,GAAA,CAIhF,SAASC,EAAiBJ,GACjB,OAAAA,EAAIK,WAAaH,EAAMF,EAAIK,YAAYF,OAAO,uBAAyB,GAAA,CAIhF,SAASG,EAAWN,GAClB,OAAQA,EAAIO,MACV,IAAK,IACH,OAAOtB,EAAE,6BACX,IAAK,IACH,OAAOA,EAAE,mCACX,IAAK,IACH,OAAOA,EAAE,+BACX,QACS,MAAA,IACX,CAIFuB,eAAeC,IACbpB,EAAQqB,OAAQ,EACZ,IACF,MAAMC,QAAYC,EAAeC,mBAAmBpB,EAAYiB,OAC/C,IAAbC,EAAIG,OACNvB,EAAUmB,MAAQC,EAAII,KAAKC,MAAQ,GAC7BxB,EAAAkB,MAAQC,EAAII,KAAKvB,OAAS,SAG7ByB,GACLC,QAAQD,MAAMhC,EAAE,mCAAoCgC,EAAK,CAE3D,QACE5B,EAAQqB,OAAQ,CAAA,CAClB,QAIFS,GAAU,KACAV,GAAA"}