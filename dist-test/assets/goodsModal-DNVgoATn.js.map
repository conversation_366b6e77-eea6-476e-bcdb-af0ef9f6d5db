{"version": 3, "file": "goodsModal-DNVgoATn.js", "sources": ["../../src/views/order/info/components/orderdetail/goodsModal.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"cancel\": \"Cancel\",\r\n    \"confirm\": \"confirm\",\r\n    \"close\": \"Close\",\r\n    \"remark\": \"remark\",\r\n    \"productName\": \"Product Name\",\r\n    \"returnableQuantity\":\"Returnable Quantity\",\r\n    \"unitPrice\":\"Unit Price\",\r\n    \"returnQuantity\":\"Return Quantity\",\r\n    \"refundAmount\":\"Refund Amount\",\r\n    \"youMustItem\":\"You must choose an item!\",\r\n    \"remarks\": \"Remarks cannot be empty!\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"cancel\": \"取消\",\r\n    \"confirm\": \"确认\",\r\n    \"close\": \"关闭\",\r\n    \"remark\": \"备注\",\r\n    \"productName\":\"商品名称\",\r\n    \"returnableQuantity\":\"可退数量\",\r\n    \"unitPrice\":\"单价\",\r\n    \"returnQuantity\":\"退货数量\",\r\n    \"refundAmount\":\"退款金额\",\r\n    \"youMustItem\":\"必须选择一个商品！\",\r\n    \"remarks\":\"备注不能为空！\"\r\n  },\r\n  \"km\": {\r\n    \"cancel\": \"បោះបង់\",\r\n    \"confirm\": \"បញ្ជាក់\",\r\n    \"close\": \"បិទ\",\r\n    \"remark\": \"សម្គាល់\",\r\n    \"productName\":\"ឈ្មោះផលិតផល\",\r\n    \"returnableQuantity\":\"ចំនួនអាចត្រឡប់\",\r\n    \"unitPrice\":\"តម្លៃឯកតា\",\r\n    \"returnQuantity\":\"ចំនួនត្រឡប់\",\r\n    \"refundAmount\":\"ចំនួនទឹកប្រាក់សង\",\r\n    \"youMustItem\":\"អ្នកត្រូវជ្រើសរើសធាតុមួយ!\",\r\n    \"remarks\":\"សម្គាល់មិនអាចទទេបានទេ!\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { ConsumeGoodRespVO, refundGoodType } from './account'\r\nimport { orderApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { removePropertiesFromArray } from '@/utils'\r\nimport Decimal from 'decimal.js'\r\nimport { ElInputNumber } from 'element-plus'\r\n\r\ndefineOptions({\r\n  name: 'GoodsModal', // 弹窗退货\r\n})\r\n/** 传入的props变量 */\r\nconst props = defineProps({\r\n  /** 账务号 */\r\n  accNo: {\r\n    type: String,\r\n    default: '',\r\n  },\r\n})\r\n/**\r\n * @param 发射给父组件的方法\r\n * @param 用于子组件给父组件传值或调用父组件方法\r\n */\r\nconst emits = defineEmits(['submit', 'close'])\r\nconst { t } = useI18n()\r\n/** 获取缓存数据 */\r\nconst userStore = useUserStore()\r\n/** 开发票tabs */\r\nconst activeName = ref('1')\r\n/** 获取form */\r\nconst easyFormRef = ref()\r\n/** 查询条件 */\r\nconst queryParams = reactive<refundGoodType>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  accNo: props.accNo,\r\n})\r\n/** 后台获取到的数据对象 */\r\nconst tableData = ref<ConsumeGoodRespVO[]>([])\r\n/** 发票记录表格配置 */\r\nconst tableColumns = ref<Table.Column<ConsumeGoodRespVO>[]>([\r\n  { label: t('productName'), prop: 'goodsName' },\r\n  { label: t('returnableQuantity'), prop: 'remainCount' },\r\n  { label: t('unitPrice'), prop: 'price', render: ({ row }) => h('span', `￥${row.price}`) },\r\n  {\r\n    label: t('returnQuantity'),\r\n    render: ({ row }) =>\r\n      h(ElInputNumber, {\r\n        modelValue: row.num,\r\n        style: 'width:110px;',\r\n        min: 0,\r\n        max: row.remainCount,\r\n        'onUpdate:modelValue': (newValue) => {\r\n          row.num = newValue\r\n        },\r\n      }),\r\n  },\r\n  {\r\n    label: t('refundAmount'),\r\n    prop: 'price',\r\n    render: ({ row }) => h('span', `￥${countPrice(row)}`),\r\n  },\r\n])\r\n/** 表格加载状态 */\r\nconst loading = ref(false)\r\n/** form提交禁用按钮 */\r\nconst btLoading = ref(false)\r\n/** 弹窗内容（初始化） */\r\nconst _modelForm = ref<refundGoodType>({\r\n  ...queryParams,\r\n  remark: '',\r\n})\r\n/** 弹窗内容 */\r\nconst modelForm = ref<refundGoodType>({ ..._modelForm.value })\r\n/** form表单 */\r\nconst ruleFieldList = reactive<Form.FieldItem[]>([\r\n  {\r\n    label: t('remark'),\r\n    type: 'textarea',\r\n    rows: 4,\r\n    field: 'remark',\r\n    rules: [{ required: true, message: t('remarks') }],\r\n  },\r\n])\r\n/** 计算退款金额 */\r\nfunction countPrice(row: any) {\r\n  const sumPrice = Decimal(row?.num).mul(Decimal(row?.price))\r\n  return sumPrice\r\n}\r\n/** 开发票提交form */\r\nfunction formSubmit() {\r\n  const numList = removePropertiesFromArray([...tableData.value.filter((item) => item.num! > 0)], ['goodsName', 'price', 'remainCount'])\r\n  if (numList.length == 0) {\r\n    return ElMessage.error(t('youMustItem'))\r\n  }\r\n  easyFormRef.value.formRef.validate(async (valid: boolean) => {\r\n    if (valid) {\r\n      const params: refundGoodType = {\r\n        ...modelForm.value,\r\n        goodsList: [...numList],\r\n      }\r\n      console.log('params', params)\r\n      await orderApi.postRefundGood(params)\r\n      emits('submit')\r\n      emits('close')\r\n    }\r\n  })\r\n}\r\n/** 获取发票记录 */\r\nasync function getList() {\r\n  loading.value = true\r\n  const { data } = await orderApi.getRefundGood(queryParams)\r\n  loading.value = false\r\n  tableData.value = data.map((item: ConsumeGoodRespVO) => {\r\n    item.num = 0\r\n    return item\r\n  })\r\n}\r\nonMounted(() => {\r\n  getList()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"invoiced\">\r\n    <EasyTable v-loading=\"loading\" :options=\"{ maxHeight: 298 }\" :columns=\"tableColumns\" :table-data=\"tableData\" />\r\n    <easyForm\r\n      ref=\"easyFormRef\"\r\n      class=\"invoiced-form\"\r\n      :field-list=\"ruleFieldList\"\r\n      :model=\"modelForm\"\r\n      :options=\"{\r\n        labelSuffix: '：',\r\n      }\"\r\n    />\r\n    <div class=\"invoiced-button\">\r\n      <ElButton @click=\"emits('close')\">\r\n        {{ t('cancel') }}\r\n      </ElButton>\r\n      <ElButton :loading=\"btLoading\" type=\"primary\" @click=\"formSubmit\">\r\n        {{ t('confirm') }}\r\n      </ElButton>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.invoiced {\r\n  .invoiced-button {\r\n    text-align: right;\r\n    padding-top: var(--el-dialog-padding-primary);\r\n  }\r\n}\r\n.invoiced-form {\r\n  @apply mt-15px;\r\n  :deep(.el-form-item:nth-of-type(n + 4)) {\r\n    width: 50% !important;\r\n    display: inline-flex;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "ref", "easyFormRef", "queryParams", "reactive", "gcode", "hcode", "accNo", "tableData", "tableColumns", "label", "prop", "render", "row", "h", "price", "ElInputNumber", "modelValue", "num", "style", "min", "max", "remainCount", "newValue", "sumPrice", "Decimal", "mul", "countPrice", "loading", "btLoading", "_modelForm", "remark", "modelForm", "value", "ruleFieldList", "type", "rows", "field", "rules", "required", "message", "formSubmit", "numList", "removePropertiesFromArray", "filter", "item", "length", "ElMessage", "error", "formRef", "validate", "async", "valid", "params", "goodsList", "console", "log", "orderApi", "postRefundGood", "onMounted", "data", "getRefundGood", "map", "getList"], "mappings": "qiDAwDA,MAAMA,EAAQC,EAWRC,EAAQC,GACRC,EAAEA,GAAMC,IAERC,EAAYC,IAECC,EAAI,KAEvB,MAAMC,EAAcD,IAEdE,EAAcC,EAAyB,CAC3CC,MAAON,EAAUM,MACjBC,MAAOP,EAAUO,MACjBC,MAAOd,EAAMc,QAGTC,EAAYP,EAAyB,IAErCQ,EAAeR,EAAuC,CAC1D,CAAES,MAAOb,EAAE,eAAgBc,KAAM,aACjC,CAAED,MAAOb,EAAE,sBAAuBc,KAAM,eACxC,CAAED,MAAOb,EAAE,aAAcc,KAAM,QAASC,OAAQ,EAAGC,SAAUC,EAAE,OAAQ,IAAID,EAAIE,UAC/E,CACEL,MAAOb,EAAE,kBACTe,OAAQ,EAAGC,SACTC,EAAEE,EAAe,CACfC,WAAYJ,EAAIK,IAChBC,MAAO,eACPC,IAAK,EACLC,IAAKR,EAAIS,YACT,sBAAwBC,IACtBV,EAAIK,IAAMK,CAAA,KAIlB,CACEb,MAAOb,EAAE,gBACTc,KAAM,QACNC,OAAQ,EAAGC,SAAUC,EAAE,OAAQ,IAyBnC,SAAoBD,GACZ,MAAAW,EAAWC,EAAa,MAALZ,OAAK,EAAAA,EAAAK,KAAKQ,IAAID,EAAa,MAALZ,OAAK,EAAAA,EAAAE,QAC7C,OAAAS,CAAA,CA3B8BG,CAAWd,SAI5Ce,EAAU3B,GAAI,GAEd4B,EAAY5B,GAAI,GAEhB6B,EAAa7B,EAAoB,IAClCE,EACH4B,OAAQ,KAGJC,EAAY/B,EAAoB,IAAK6B,EAAWG,QAEhDC,EAAgB9B,EAA2B,CAC/C,CACEM,MAAOb,EAAE,UACTsC,KAAM,WACNC,KAAM,EACNC,MAAO,SACPC,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAAS3C,EAAE,gBASzC,SAAS4C,IACP,MAAMC,EAAUC,EAA0B,IAAInC,EAAUyB,MAAMW,QAAQC,GAASA,EAAK3B,IAAO,KAAK,CAAC,YAAa,QAAS,gBACnH,GAAkB,GAAlBwB,EAAQI,OACV,OAAOC,EAAUC,MAAMnD,EAAE,gBAE3BK,EAAY+B,MAAMgB,QAAQC,UAASC,MAAOC,IACxC,GAAIA,EAAO,CACT,MAAMC,EAAyB,IAC1BrB,EAAUC,MACbqB,UAAW,IAAIZ,IAETa,QAAAC,IAAI,SAAUH,SAChBI,EAASC,eAAeL,GAC9B1D,EAAM,UACNA,EAAM,QAAO,IAEhB,QAYHgE,GAAU,MATVR,iBACEvB,EAAQK,OAAQ,EAChB,MAAM2B,KAAEA,SAAeH,EAASI,cAAc1D,GAC9CyB,EAAQK,OAAQ,EAChBzB,EAAUyB,MAAQ2B,EAAKE,KAAKjB,IAC1BA,EAAK3B,IAAM,EACJ2B,IACR,CAGOkB,EAAA"}