<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脱敏功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 40px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-card-header {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
        }
        
        .info-label {
            width: 120px;
            color: #606266;
            padding-right: 12px;
            text-align: right;
            flex-shrink: 0;
        }
        
        .info-value {
            flex: 1;
            color: #303133;
            display: flex;
            align-items: center;
        }
        
        .toggle-btn {
            margin-left: 8px;
            padding: 4px 8px;
            background: #f0f2f5;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            color: #606266;
            transition: all 0.3s;
        }
        
        .toggle-btn:hover {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
        }
        
        .demo-item {
            background: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .original {
            color: #67c23a;
        }
        
        .masked {
            color: #e6a23c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔒 客人信息脱敏功能测试</h1>
        
        <div class="demo-section">
            <div class="demo-title">脱敏效果演示</div>
            
            <div class="demo-item">
                <strong>手机号脱敏：</strong><br>
                <span class="original">原始：13812345678</span><br>
                <span class="masked">脱敏：138****5678</span>
            </div>
            
            <div class="demo-item">
                <strong>身份证号脱敏：</strong><br>
                <span class="original">原始：110101199001011234</span><br>
                <span class="masked">脱敏：110101********1234</span>
            </div>
        </div>
        
        <div class="info-card">
            <div class="info-card-header">客人信息</div>
            
            <div class="info-item">
                <div class="info-label">客人姓名：</div>
                <div class="info-value">
                    <span>张三</span>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-label">联系方式：</div>
                <div class="info-value">
                    <span id="phone-display">138****5678</span>
                    <button class="toggle-btn" onclick="togglePhone()">👁️ 查看完整</button>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-label">性别：</div>
                <div class="info-value">
                    <span>男</span>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-label">证件号码：</div>
                <div class="info-value">
                    <span id="idno-display">110101********1234</span>
                    <button class="toggle-btn" onclick="toggleIdNo()">👁️ 查看完整</button>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-label">证件类型：</div>
                <div class="info-value">
                    <span>身份证</span>
                </div>
            </div>
        </div>
        
        <div style="background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 6px; padding: 15px; color: #409eff;">
            <strong>✨ 功能特点：</strong><br>
            • 🔒 默认显示脱敏信息，保护客人隐私<br>
            • 👁️ 点击眼睛图标可切换显示完整信息<br>
            • 🎯 手机号中间4位用*号替代<br>
            • 🆔 身份证号中间8位用*号替代<br>
            • 🔄 支持反复切换显示状态
        </div>
    </div>

    <script>
        let isPhoneVisible = false;
        let isIdNoVisible = false;
        
        const originalPhone = '13812345678';
        const maskedPhone = '138****5678';
        const originalIdNo = '110101199001011234';
        const maskedIdNo = '110101********1234';
        
        function togglePhone() {
            const phoneDisplay = document.getElementById('phone-display');
            const toggleBtn = phoneDisplay.nextElementSibling;
            
            if (isPhoneVisible) {
                phoneDisplay.textContent = maskedPhone;
                toggleBtn.textContent = '👁️ 查看完整';
                isPhoneVisible = false;
            } else {
                phoneDisplay.textContent = originalPhone;
                toggleBtn.textContent = '🙈 隐藏';
                isPhoneVisible = true;
            }
        }
        
        function toggleIdNo() {
            const idNoDisplay = document.getElementById('idno-display');
            const toggleBtn = idNoDisplay.nextElementSibling;
            
            if (isIdNoVisible) {
                idNoDisplay.textContent = maskedIdNo;
                toggleBtn.textContent = '👁️ 查看完整';
                isIdNoVisible = false;
            } else {
                idNoDisplay.textContent = originalIdNo;
                toggleBtn.textContent = '🙈 隐藏';
                isIdNoVisible = true;
            }
        }
    </script>
</body>
</html>
