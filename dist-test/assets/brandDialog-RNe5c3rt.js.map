{"version": 3, "file": "brandDialog-RNe5c3rt.js", "sources": ["../../src/views/group/base-info/brand/brandDialog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"dialog\": {\r\n      \"title\": {\r\n        \"add\": \"Add Brand\",\r\n        \"edit\": \"Edit Brand\"\r\n      }\r\n    },\r\n    \"validation\": {\r\n      \"brandName\": \"Please enter the brand name\"\r\n    },\r\n    \"placeholder\": {\r\n      \"brandName\": \"Enter brand name\",\r\n      \"intro\": \"Enter brand Intro\"\r\n    },\r\n    \"button\": {\r\n      \"cancel\": \"Cancel\",\r\n      \"confirm\": \"Save\"\r\n    },\r\n    \"success\": {\r\n      \"create\": \"Brand added successfully\",\r\n      \"update\": \"Brand edited successfully\"\r\n    },\r\n    \"form\": {\r\n      \"label\": {\r\n        \"brandName\": \"Brand Name\",\r\n        \"brandLogo\": \"Brand Logo\",\r\n        \"brandIntro\": \"Brand Intro\",\r\n        \"selectFile\": \"Select File\"\r\n      }\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"dialog\": {\r\n      \"title\": {\r\n        \"add\": \"新增品牌\",\r\n        \"edit\": \"编辑品牌\"\r\n      }\r\n    },\r\n    \"validation\": {\r\n      \"brandName\": \"请输入品牌名称\"\r\n    },\r\n    \"placeholder\": {\r\n      \"brandName\": \"请输入品牌名称\",\r\n      \"intro\": \"请输入品牌介绍\"\r\n    },\r\n    \"button\": {\r\n      \"cancel\": \"取消\",\r\n      \"confirm\": \"确定\"\r\n    },\r\n    \"success\": {\r\n      \"create\": \"品牌新增成功\",\r\n      \"update\": \"品牌编辑成功\"\r\n    },\r\n    \"form\": {\r\n      \"label\": {\r\n        \"brandName\": \"品牌名称\",\r\n        \"brandLogo\": \"品牌LOGO\",\r\n        \"brandIntro\": \"品牌介绍\",\r\n        \"selectFile\": \"选择文件\"\r\n      }\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"dialog\": {\r\n      \"title\": {\r\n        \"add\": \"បន្ថែមម៉ាក\",\r\n        \"edit\": \"កែសម្រួលម៉ាក\"\r\n      }\r\n    },\r\n    \"validation\": {\r\n      \"brandName\": \"សូមបញ្ចូលឈ្មោះម៉ាក\"\r\n    },\r\n    \"placeholder\": {\r\n      \"brandName\": \"បញ្ចូលឈ្មោះម៉ាក\",\r\n      \"intro\": \"បញ្ចូលការណែនាំម៉ាក\"\r\n    },\r\n    \"button\": {\r\n      \"cancel\": \"បោះបង់\",\r\n      \"confirm\": \"រក្សាទុក\"\r\n    },\r\n    \"success\": {\r\n      \"create\": \"បានបន្ថែមម៉ាកដោយជោគជ័យ\",\r\n      \"update\": \"បានកែសម្រួលម៉ាកដោយជោគជ័យ\"\r\n    },\r\n    \"form\": {\r\n      \"label\": {\r\n        \"brandName\": \"ឈ្មោះម៉ាក\",\r\n        \"brandLogo\": \"រូបសញ្ញាម៉ាក\",\r\n        \"brandIntro\": \"ការណែនាំម៉ាក\",\r\n        \"selectFile\": \"ជ្រើសរើសឯកសារ\"\r\n      }\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { BrandModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { brandApi, fileApi } from '@/api/modules/index'\r\nimport { BooleanEnum } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nimport axios from 'axios'\r\n\r\nexport interface Props {\r\n  modelValue: boolean\r\n  gcode: string\r\n  brandCode: string\r\n}\r\n\r\ndefineOptions({\r\n  name: 'BrandDialog',\r\n})\r\nconst props = withDefaults(defineProps<Props>(), {\r\n  modelValue: false,\r\n  gcode: '',\r\n  brandCode: '',\r\n})\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  addBrand: [\r\n    node: {\r\n      id: string | number\r\n      brandCode: string\r\n      brandName: string\r\n    }\r\n  ]\r\n  editBrand: [\r\n    node: {\r\n      id: string | number\r\n      brandCode: string\r\n      brandName: string\r\n    }\r\n  ]\r\n}>()\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst formRules = ref<FormRules>({\r\n  brandName: [{ required: true, message: t('validation.brandName') }],\r\n})\r\n\r\nconst myVisible = ref(props.modelValue)\r\nconst title = computed(() => (props.brandCode === '' ? t('dialog.title.add') : t('dialog.title.edit')))\r\nconst formRef = ref<FormInstance>()\r\nconst form = reactive<BrandModel>({\r\n  gcode: userStore.gcode,\r\n  brandCode: '',\r\n  brandName: '',\r\n  intro: '',\r\n  isEnable: BooleanEnum.YES,\r\n  logo: '',\r\n})\r\n\r\nconst fileInput = ref<HTMLInputElement | null>(null)\r\n\r\nonMounted(() => {\r\n  if (props.brandCode !== '') {\r\n    brandApi.getGroupBrand(props.brandCode).then((res: any) => {\r\n      if (res.code === 0) {\r\n        Object.assign(form, res.data)\r\n      }\r\n    })\r\n  }\r\n})\r\n\r\n// 打开文件选择\r\nfunction selectFile() {\r\n  fileInput.value?.click()\r\n}\r\n\r\n// 处理文件选择并上传\r\nfunction handleFileChange(event: Event) {\r\n  const file = (event.target as HTMLInputElement).files?.[0]\r\n  if (file) {\r\n    uploadFile(file)\r\n  }\r\n}\r\n\r\n// 获取预签名 URL 并上传文件\r\nasync function uploadFile(file: File) {\r\n  try {\r\n    const uploadUrl = await getPresignedUrl(file)\r\n    await axios.put(uploadUrl, file, {\r\n      headers: {\r\n        'Content-Type': file.type,\r\n      },\r\n    })\r\n    form.logo = uploadUrl.split('?')[0] // 更新为上传成功后的图片 URL\r\n    ElMessage.success(t('success.update'))\r\n  } catch (error) {\r\n    console.error('上传失败:', error)\r\n    ElMessage.error('上传失败')\r\n  }\r\n}\r\n\r\n// 获取预签名 URL\r\nasync function getPresignedUrl(file: File) {\r\n  const fileExtension = file.name.split('.').pop()\r\n  const timestamp = Date.now()\r\n  const customPath = `brand/logo/${userStore.hname}_${form.brandName}_${timestamp}.${fileExtension}`\r\n\r\n  try {\r\n    const response = await fileApi.getFilePresignedUrl(customPath)\r\n    return response.data.uploadUrl\r\n  } catch (error) {\r\n    console.error('获取预签名 URL 失败:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\nfunction onSubmit() {\r\n  const action = form.brandCode === '' ? 'create' : 'update'\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        const apiCall = action === 'create' ? brandApi.createGroupBrand(form) : brandApi.updateGroupBrand(form)\r\n        apiCall.then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success({\r\n              message: t(`success.${action}`),\r\n              center: true,\r\n            })\r\n            emits(action === 'create' ? 'addBrand' : 'editBrand', {\r\n              id: res.data.id,\r\n              brandCode: res.data.brandCode,\r\n              brandName: res.data.brandName,\r\n            })\r\n            onCancel()\r\n          } else {\r\n            ElMessage.error({\r\n              message: res.msg,\r\n              center: true,\r\n            })\r\n          }\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"title\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close @closed=\"emits('update:modelValue', false)\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"140px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('form.label.brandName')\" prop=\"brandName\">\r\n        <el-input v-model=\"form.brandName\" :placeholder=\"t('placeholder.brandName')\" clearable />\r\n      </el-form-item>\r\n      <!--      <el-form-item :label=\"t('form.label.brandLogo')\">\r\n              <image-upload\r\n                v-model:url=\"form.logo\"\r\n                action=\"https://aaa.aa/app/mock/project/1f50f1da-5189-4282-d3c7-c133a514c5a8/upload/image\"\r\n                name=\"image\" :width=\"200\" :height=\"200\"\r\n              />\r\n            </el-form-item> -->\r\n      <!-- 新增文件选择和图片预览部分 -->\r\n      <el-form-item :label=\"t('form.label.brandLogo')\">\r\n        <div class=\"image-upload-container\">\r\n          <el-image v-if=\"form.logo\" :src=\"form.logo\" style=\"width: 200px; height: 200px\" />\r\n          <el-button type=\"primary\" style=\"margin-left: 20px\" @click=\"selectFile\">\r\n            {{ t('form.label.selectFile') }}\r\n          </el-button>\r\n          <input ref=\"fileInput\" type=\"file\" style=\"display: none\" @change=\"handleFileChange\" />\r\n        </div>\r\n      </el-form-item>\r\n\r\n      <el-form-item :label=\"t('form.label.brandIntro')\">\r\n        <el-input v-model=\"form.intro\" type=\"textarea\" :rows=\"6\" :placeholder=\"t('placeholder.intro')\" clearable />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('button.cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('button.confirm') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n"], "names": ["props", "__props", "emits", "__emit", "userStore", "useUserStore", "t", "useI18n", "formRules", "ref", "brandName", "required", "message", "myVisible", "modelValue", "title", "computed", "brandCode", "formRef", "form", "reactive", "gcode", "intro", "isEnable", "BooleanEnum", "YES", "logo", "fileInput", "selectFile", "_a", "value", "click", "handleFileChange", "event", "file", "target", "files", "async", "uploadUrl", "fileExtension", "name", "split", "pop", "timestamp", "Date", "now", "customPath", "hname", "fileApi", "getFilePresignedUrl", "data", "error", "console", "getPresignedUrl", "axios", "put", "headers", "type", "ElMessage", "success", "uploadFile", "onSubmit", "action", "validate", "valid", "brandApi", "createGroupBrand", "updateGroupBrand", "then", "res", "code", "center", "id", "onCancel", "msg", "onMounted", "getGroupBrand", "Object", "assign"], "mappings": "uyBAoHA,MAAMA,EAAQC,EAMRC,EAAQC,EAkBRC,EAAYC,KACZC,EAAEA,GAAMC,IAERC,EAAYC,EAAe,CAC/BC,UAAW,CAAC,CAAEC,UAAU,EAAMC,QAASN,EAAE,4BAGrCO,EAAYJ,EAAIT,EAAMc,YACtBC,EAAQC,GAAS,IAA2B,KAApBhB,EAAMiB,UAAmBX,EAAE,oBAAsBA,EAAE,uBAC3EY,EAAUT,IACVU,EAAOC,EAAqB,CAChCC,MAAOjB,EAAUiB,MACjBJ,UAAW,GACXP,UAAW,GACXY,MAAO,GACPC,SAAUC,EAAYC,IACtBC,KAAM,KAGFC,EAAYlB,EAA6B,MAa/C,SAASmB,UACP,OAAAC,EAAAF,EAAUG,QAAOD,EAAAE,OAAM,CAIzB,SAASC,EAAiBC,SACxB,MAAMC,EAAQ,OAAAL,EAAAI,EAAME,OAA4BC,YAAQ,EAAAP,EAAA,GACpDK,GAMNG,eAA0BH,GACpB,IACI,MAAAI,QAeVD,eAA+BH,GAC7B,MAAMK,EAAgBL,EAAKM,KAAKC,MAAM,KAAKC,MACrCC,EAAYC,KAAKC,MACjBC,EAAa,cAAc1C,EAAU2C,SAAS5B,EAAKT,aAAaiC,KAAaJ,IAE/E,IAEF,aADuBS,EAAQC,oBAAoBH,IACnCI,KAAKZ,gBACda,GAED,MADEC,QAAAD,MAAM,gBAAiBA,GACzBA,CAAA,CACR,CA1B0BE,CAAgBnB,SAClCoB,EAAMC,IAAIjB,EAAWJ,EAAM,CAC/BsB,QAAS,CACP,eAAgBtB,EAAKuB,QAGzBtC,EAAKO,KAAOY,EAAUG,MAAM,KAAK,GACvBiB,EAAAC,QAAQrD,EAAE,yBACb6C,GACCC,QAAAD,MAAM,QAASA,GACvBO,EAAUP,MAAM,OAAM,CACxB,CAlBES,CAAW1B,EACb,CAmCF,SAAS2B,IACP,MAAMC,EAA4B,KAAnB3C,EAAKF,UAAmB,SAAW,SAClDC,EAAQY,OACNZ,EAAQY,MAAMiC,UAAUC,IACtB,GAAIA,EAAO,EACkB,WAAXF,EAAsBG,EAASC,iBAAiB/C,GAAQ8C,EAASE,iBAAiBhD,IAC1FiD,MAAMC,IACK,IAAbA,EAAIC,MACNZ,EAAUC,QAAQ,CAChB/C,QAASN,EAAE,WAAWwD,KACtBS,QAAQ,IAEJrE,EAAW,WAAX4D,EAAsB,WAAa,YAAa,CACpDU,GAAIH,EAAInB,KAAKsB,GACbvD,UAAWoD,EAAInB,KAAKjC,UACpBP,UAAW2D,EAAInB,KAAKxC,YAEb+D,KAETf,EAAUP,MAAM,CACdvC,QAASyD,EAAIK,IACbH,QAAQ,GACT,GAEJ,IAEJ,CAGL,SAASE,IACP5D,EAAUiB,OAAQ,CAAA,QArFpB6C,GAAU,KACgB,KAApB3E,EAAMiB,WACRgD,EAASW,cAAc5E,EAAMiB,WAAWmD,MAAMC,IAC3B,IAAbA,EAAIC,MACCO,OAAAC,OAAO3D,EAAMkD,EAAInB,KAAI,GAE/B"}