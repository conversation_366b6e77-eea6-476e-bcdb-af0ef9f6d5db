import{d as e,b as a,aO as l,D as o,y as t,o as s,c as u,g as i,f as n,w as r,h as c,e as p,u as d,bC as m,R as v,aM as g,b6 as f,aq as h,F as b,ag as y,Y as C,s as I,m as V,aS as _,b5 as F,bD as w,q as j,bE as k,bF as x,x as E,bt as N,ay as U,aT as R}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                *//* empty css                 *//* empty css                  *//* empty css                 *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                        */import{m as S}from"./material.api-B6h8YE6r.js";import{_ as T}from"./_plugin-vue_export-helper-BCo6x5W8.js";const A={class:"dynamic-config-form"},W={key:0,class:"share-config"},O={key:1,class:"share-video-config"},z={key:0,class:"cover-preview"},$=["src"],D={class:"cover-actions"},H={key:2,class:"shop-config"},q={key:3,class:"share-video-config"},M={key:4,class:"share-video-config"},L={class:"cover-search"},P={class:"cover-content"},G={key:0,class:"cover-grid"},K=["onClick"],Y=["src"],B=["title"],J={key:1,class:"no-data"},Q={key:0,class:"cover-pagination"},X=T(e({__name:"DynamicConfigForm",props:{configType:{},modelValue:{}},emits:["update:modelValue"],setup(e,{emit:T}){const X=e,Z=T,ee=a({video:1,photoCount:1,title:"",isFreeContent:!1,coverImage:"",account:"",password:""}),ae=a(!1),le=a(!1),oe=a([]),te=a(0),se=a(""),ue=a({pageNo:1,pageSize:20,gcode:l.local.get("gcode"),hcode:l.local.get("hcode"),platform:"",materialName:"",materialType:""}),ie=a(),ne={account:[{required:!0,message:"请输入WIFI账号",trigger:"blur"}],password:[{required:!0,message:"请输入WIFI密码",trigger:"blur"}]};function re(){le.value=!0,ue.value.materialName=se.value,S.getMaterialPage(ue.value).then((e=>{e&&0===e.code?(oe.value=e.data.list||[],te.value=e.data.total||0):(console.error("获取素材列表失败:",e),oe.value=[],te.value=0)})).catch((e=>{console.error("获取素材列表异常:",e),oe.value=[],te.value=0})).finally((()=>{le.value=!1}))}function ce(){ue.value.pageNo=1,re()}function pe(){se.value="",ue.value.pageNo=1,re()}function de(e){ue.value.pageNo=e,re()}function me(){ae.value=!0,se.value="",ue.value.pageNo=1,re()}const ve=a("");function ge(){if(console.log("props",X,"formData",ee),"SHARE_NORMAL"===X.configType){const e=[];e.push(`photo:${ee.value.photoCount||1}`),ee.value.title&&ee.value.title.trim()&&e.push(`title:${ee.value.title.trim()}`),e.push("isFreeContent:"+(ee.value.isFreeContent?"1":"0")),ve.value=e.join(","),Z("update:modelValue",ve.value)}else if("SHARE_VIDEO"===X.configType){const e=[];e.push(`video:${ee.value.video||1}`),ee.value.coverImage&&ee.value.coverImage.trim()&&e.push(`cover:${ee.value.coverImage.trim()}`),e.push("isFreeContent:"+(ee.value.isFreeContent?"1":"0")),ve.value=e.join(","),Z("update:modelValue",ve.value)}else if("WIFI"===X.configType){const e=[];e.push(`account:${ee.value.account?ee.value.account.trim():""}`),e.push(`password:${ee.value.password?ee.value.password.trim():""}`),ve.value=e.join(","),Z("update:modelValue",ve.value)}else if("USER_REVIEWS"===X.configType){const e=[];e.push(`photo:${ee.value.photoCount||1}`),e.push("isFreeContent:"+(ee.value.isFreeContent?"1":"0")),ve.value=e.join(","),Z("update:modelValue",ve.value)}}return o((()=>X.modelValue),(e=>{e&&e!==ve.value&&function(e){if(e)try{const a=e.split(","),l={};a.forEach((e=>{const a=e.indexOf(":");if(-1===a)return;const o=e.substring(0,a),t=e.substring(a+1);if(o&&void 0!==t){const e=o.trim(),a=t.trim();switch(e){case"video":l.video=Number.parseInt(a)||1;break;case"cover":l.coverImage=a;break;case"photo":l.photoCount=Number.parseInt(a)||1;break;case"title":l.title=a;break;case"isFreeContent":l.isFreeContent="1"===a||"true"===a;break;case"account":l.account=a;break;case"password":l.password=a}}})),Object.keys(l).forEach((e=>{void 0!==l[e]&&(ee.value[e]=l[e])}))}catch(a){console.warn("解析配置代码失败:",a)}}(e)}),{immediate:!0}),o((()=>X.configType),(()=>{ee.value={video:1,photoCount:1,title:"",isFreeContent:!1,coverImage:"",account:"",password:""},ge()}),{immediate:!0}),t((()=>{ge()})),(e,a)=>{const l=I,o=V,t=_,S=F,T=w,X=j,Z=k,re=x,ve=E,fe=N,he=U,be=R;return s(),u(b,null,[i("div",A,["SHARE_NORMAL"===e.configType?(s(),u("div",W,[n(o,{label:"图片数量",prop:"photoCount"},{default:r((()=>[n(l,{modelValue:ee.value.photoCount,"onUpdate:modelValue":a[0]||(a[0]=e=>ee.value.photoCount=e),min:1,max:20,placeholder:"请输入图片数量",onChange:ge},null,8,["modelValue"])])),_:1}),n(o,{label:"标题名称",prop:"title"},{default:r((()=>[n(t,{modelValue:ee.value.title,"onUpdate:modelValue":a[1]||(a[1]=e=>ee.value.title=e),placeholder:"请输入标题名称",maxlength:"20","show-word-limit":"",onInput:ge},null,8,["modelValue"])])),_:1}),n(o,{label:"AI生成内容",prop:"isFreeContent"},{default:r((()=>[n(S,{modelValue:ee.value.isFreeContent,"onUpdate:modelValue":a[2]||(a[2]=e=>ee.value.isFreeContent=e),onChange:ge},null,8,["modelValue"])])),_:1}),n(T,{description:"开启：AI自动生成内容  关闭：随机获取素材库文字",type:"info",closable:!1,"show-icon":"",class:"compact-alert"})])):"SHARE_VIDEO"===e.configType?(s(),u("div",O,[n(T,{description:"当前视频数量固定",type:"info",closable:!1,"show-icon":"",class:"compact-alert"}),n(o,{label:"视频数量",prop:"video"},{default:r((()=>[n(l,{modelValue:ee.value.video,"onUpdate:modelValue":a[3]||(a[3]=e=>ee.value.video=e),min:1,max:1,disabled:""},null,8,["modelValue"])])),_:1}),n(o,{label:"AI生成内容",prop:"isFreeContent"},{default:r((()=>[n(S,{modelValue:ee.value.isFreeContent,"onUpdate:modelValue":a[4]||(a[4]=e=>ee.value.isFreeContent=e),onChange:ge},null,8,["modelValue"])])),_:1}),n(T,{description:"开启：AI自动生成内容 ； 关闭：随机获取素材库文字",type:"info",closable:!1,"show-icon":"",class:"compact-alert"}),n(o,{label:"封面设置"},{default:r((()=>[ee.value.coverImage?(s(),u("div",z,[i("img",{src:ee.value.coverImage,alt:"封面图片",class:"cover-image",style:{width:"100px",height:"100px","object-fit":"cover","border-radius":"4px"}},null,8,$),i("div",D,[n(X,{size:"small",onClick:me},{default:r((()=>a[13]||(a[13]=[c(" 更换封面 ")]))),_:1}),n(X,{size:"small",type:"danger",onClick:a[5]||(a[5]=e=>ee.value.coverImage="")},{default:r((()=>a[14]||(a[14]=[c(" 移除封面 ")]))),_:1})])])):(s(),p(X,{key:1,type:"primary",onClick:me},{default:r((()=>[n(Z,null,{default:r((()=>[n(d(m))])),_:1}),a[15]||(a[15]=c(" 选择封面 "))])),_:1}))])),_:1})])):"SHOP_URL"===e.configType?(s(),u("div",H,[n(re,{description:"店铺主页配置待开发"})])):"WIFI"===e.configType?(s(),u("div",q,[n(ve,{ref_key:"formRef",ref:ie,model:ee.value,rules:ne,"label-width":"120px"},{default:r((()=>[n(o,{label:"WIFI账号",prop:"account"},{default:r((()=>[n(t,{modelValue:ee.value.account,"onUpdate:modelValue":a[6]||(a[6]=e=>ee.value.account=e),placeholder:"请输入WIFI账号",maxlength:"30",onInput:ge},null,8,["modelValue"])])),_:1}),n(o,{label:"WIFI密码",prop:"password"},{default:r((()=>[n(t,{modelValue:ee.value.password,"onUpdate:modelValue":a[7]||(a[7]=e=>ee.value.password=e),type:"password",placeholder:"请输入WIFI密码",maxlength:"30",onInput:ge},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),n(T,{description:"用户可获取WIFI账号和密码信息",type:"info",closable:!1,"show-icon":"",class:"compact-alert"})])):"USER_REVIEWS"===e.configType?(s(),u("div",M,[n(o,{label:"图片数量",prop:"photoCount"},{default:r((()=>[n(l,{modelValue:ee.value.photoCount,"onUpdate:modelValue":a[8]||(a[8]=e=>ee.value.photoCount=e),min:1,max:20,placeholder:"请输入图片数量",onChange:ge},null,8,["modelValue"])])),_:1}),n(o,{label:"AI生成内容",prop:"isFreeContent"},{default:r((()=>[n(S,{modelValue:ee.value.isFreeContent,"onUpdate:modelValue":a[9]||(a[9]=e=>ee.value.isFreeContent=e),onChange:ge},null,8,["modelValue"])])),_:1}),n(T,{description:"开启：AI自动生成内容 ； 关闭：随机获取素材库文字",type:"info",closable:!1,"show-icon":"",class:"compact-alert"})])):v("",!0)]),n(he,{modelValue:ae.value,"onUpdate:modelValue":a[12]||(a[12]=e=>ae.value=e),title:"选择视频封面",width:"900px","close-on-click-modal":!1,"destroy-on-close":""},{default:r((()=>[i("div",L,[n(t,{modelValue:se.value,"onUpdate:modelValue":a[10]||(a[10]=e=>se.value=e),placeholder:"请输入素材名称搜索",clearable:"",style:{width:"300px","margin-right":"12px"},onKeyup:g(ce,["enter"]),onClear:pe},{prefix:r((()=>[n(Z,null,{default:r((()=>[n(d(f))])),_:1})])),_:1},8,["modelValue"]),n(X,{type:"primary",loading:le.value,onClick:ce},{default:r((()=>[n(Z,null,{default:r((()=>[n(d(f))])),_:1}),a[16]||(a[16]=c(" 搜索 "))])),_:1},8,["loading"]),n(X,{disabled:le.value,onClick:pe},{default:r((()=>a[17]||(a[17]=[c(" 重置 ")]))),_:1},8,["disabled"])]),h((s(),u("div",P,[oe.value.length>0?(s(),u("div",G,[(s(!0),u(b,null,y(oe.value,(e=>(s(),u("div",{key:e.id,class:"cover-item",onClick:a=>function(e){ee.value.coverImage=e.url,ae.value=!1,ge()}(e)},[i("img",{src:e.url,alt:"封面图片",style:{width:"120px",height:"80px","object-fit":"cover","border-radius":"4px",cursor:"pointer"}},null,8,Y),i("div",{class:"cover-name",title:e.materialName},C(e.materialName),9,B)],8,K)))),128))])):le.value?v("",!0):(s(),u("div",J,[n(re,{description:"暂无图片素材"})]))])),[[be,le.value]]),te.value>ue.value.pageSize?(s(),u("div",Q,[n(fe,{"current-page":ue.value.pageNo,"onUpdate:currentPage":a[11]||(a[11]=e=>ue.value.pageNo=e),"page-size":ue.value.pageSize,total:te.value,layout:"prev, pager, next, jumper",onCurrentChange:de},null,8,["current-page","page-size","total"])])):v("",!0)])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-2bc4085d"]]);export{X as default};
//# sourceMappingURL=DynamicConfigForm-DqS7---Y.js.map
