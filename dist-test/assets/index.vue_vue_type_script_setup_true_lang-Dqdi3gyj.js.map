{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-Dqdi3gyj.js", "sources": ["../../src/components/Chip/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\ndefineOptions({\r\n  name: 'Chip',\r\n})\r\n\r\ndefineProps<{\r\n  closable?: boolean\r\n}>()\r\n\r\nconst emits = defineEmits<{\r\n  close: []\r\n}>()\r\n</script>\r\n\r\n<template>\r\n  <div class=\"chip inline-block rounded-999 bg-stone-1 px-3 vertical-mid transition-background-color dark-bg-stone-8\">\r\n    <div class=\"content h-8 flex items-center gap-2 text-xs\">\r\n      <slot />\r\n      <span v-if=\"closable\" class=\"closable h-6 w-6 flex-center cursor-pointer rounded-1/2 bg-stone-2 text-sm text-initial transition-background-color -mr-1.5 dark-bg-stone-9 hover-op-70\" @click=\"emits('close')\">\r\n        <SvgIcon name=\"i-ep:close-bold\" />\r\n      </span>\r\n    </div>\r\n  </div>\r\n</template>\r\n"], "names": ["emits", "__emit"], "mappings": "6WASA,MAAMA,EAAQC"}