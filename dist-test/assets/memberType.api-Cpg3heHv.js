import{a as e}from"./index-CkEhI1Zk.js";const t="admin-api/member/type",p={getTypeList:p=>e.get(`${t}/list`,{params:p}),getTypeSimpleList:p=>e.get(`${t}/simple-list`,{params:p}),listMemberType:t=>e.get("/admin-api/member/type/list",{params:{gcode:t}}),getType:p=>e.get(`${t}/get`,{params:p}),updateTypeStatus:p=>e.put(`${t}/update-status`,p,{}),createType:p=>e.post(`${t}/create`,p,{}),updateType:p=>e.put(`${t}/update`,p,{})};export{p as m};
//# sourceMappingURL=memberType.api-Cpg3heHv.js.map
