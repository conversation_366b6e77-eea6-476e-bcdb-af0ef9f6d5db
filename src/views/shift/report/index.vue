<i18n>
{
  "en": {
    "Business Date": "Business Date",
    "Select Date": "Select Date",
    "Shift": "Shift",
    "Please Select": "Please Select",
    "Operator": "Operator",
    "Query": "Filter",
    "Today": "Today",
    "Yesterday": "Yesterday",
    "A Week Ago": "A Week Ago",
    "Change Shift": "Change Shift",
    "Current Shift": "Current Shift",
    "Available Shifts": "Available Shifts",
    "Confirm Change": "Confirm Change",
    "Cancel": "Cancel",
    "Already Changed": "Already Changed",
    "No Data Available": "No data available for this shift"
  },
  "zh-cn": {
    "Business Date": "营业日",
    "Select Date": "选择日期",
    "Shift": "班次",
    "Please Select": "请选择",
    "Operator": "操作员",
    "Query": "查询",
    "Today": "今天",
    "Yesterday": "昨天",
    "A Week Ago": "一周前",
    "Change Shift": "交班",
    "Current Shift": "当前班次",
    "Available Shifts": "可选班次",
    "Confirm Change": "确认交班",
    "Cancel": "取消",
    "Already Changed": "已交班",
    "No Data Available": "该班次暂无数据"
  },
  "km": {
    "Business Date": "ថ្ងៃធ្វើអាជីវកម្ម",
    "Select Date": "ជ្រើសរើសកាលបរិច្ឆេទ",
    "Shift": "វេន",
    "Please Select": "សូមជ្រើសរើស",
    "Operator": "ប្រតិបត្តិករ",
    "Query": "ស្វែងរក",
    "Today": "ថ្ងៃនេះ",
    "Yesterday": "ម្សិលមិញ",
    "A Week Ago": "មួយសប្តាហ៍មុន",
    "Change Shift": "ប្តូរវេន",
    "Current Shift": "វេនបច្ចុប្បន្ន",
    "Available Shifts": "វេនដែលអាចជ្រើសរើស",
    "Confirm Change": "បញ្ជាក់ការប្តូរ",
    "Cancel": "បោះបង់",
    "Already Changed": "បានប្តូររួច",
    "No Data Available": "វេននេះមិនមានទិន្នន័យ"
  }
}
</i18n>

<script setup lang="ts">
import { shiftTimeApi, userApi, hotelParamConfigApi } from '@/api/modules/index'
import useSettingsStore from '@/store/modules/settings' // 导入 useI18n
import useUserStore from '@/store/modules/user'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import storage from '@/utils/storage'

const Stimulsoft = window.Stimulsoft

const { t } = useI18n() // 解构 t 函数
const userStore = useUserStore()
const data = ref({
  searchFold: false,
  tableAutoHeight: true,
  labelState: '1',
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    bizDate: Date.now(),
    recorder: '',
    shiftNo: [] as string[],
    singleShiftNo: '', // 单选班次，用于 paid_in 模式
  },
  handoverStatus: '', // 添加交班状态
})
const loading = ref(false)
const shiftMode = ref('cash_flow') // 默认为现金流模式
const availableShiftNos = ref<string[]>([]) // 存储可用的班次编号
const handoverReportLoading = ref(false) // 获取已固化班次的加载状态

// 交班弹窗相关状态
const changeShiftDialogVisible = ref(false)
const changeShiftLoading = ref(false)
const changeShiftData = ref({
  currentShiftCode: '',
  currentShiftName: '',
  shiftTimeList: []
})
const selectedShiftCode = ref('')

onMounted(async () => {
  await getHotelParamConfigShiftmode()
  await getShiftTimeList()
  await getOperatorList()
  // 如果是 paid_in 模式，获取可用班次
  if (shiftMode.value === 'paid_in') {
    await getAvailableShifts()
  }
  onSearch()
})

// 监听营业日期变化，在 paid_in 模式下重新获取可用班次
watch(() => data.value.search.bizDate, async (newDate) => {
  if (shiftMode.value === 'paid_in') {
    await getAvailableShifts()
    // getAvailableShifts 函数内部已经包含了自动选择逻辑，这里不需要额外处理
  }
})

function onSearch() {
  loading.value = true
  const searchParams = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    bizDate: dayjs(data.value.search.bizDate).format('YYYY-MM-DD'),
    shiftNo: shiftMode.value === 'paid_in'
      ? data.value.search.singleShiftNo
      : data.value.search.shiftNo.toString(),
  }

  // 只有在非 paid_in 模式下才添加操作员参数
  if (shiftMode.value !== 'paid_in') {
    searchParams.operator = data.value.search.recorder
  }

  // 根据交班模式调用不同的接口
  const apiCall = shiftMode.value === 'paid_in'
    ? shiftTimeApi.handoverReportCashRealization(searchParams)
    : shiftTimeApi.handoverReport(searchParams)

  apiCall
    .then((res) => {
      // 保存交班状态
      if (res.data && res.data.handoverStatus) {
        data.value.handoverStatus = res.data.handoverStatus
      }
      setJson(res)
    })
    .catch()
}

async function setJson(json: any) {
  const licensePath = new URL('/src/assets/license.key', import.meta.url).href
  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)
  const report = new Stimulsoft.Report.StiReport()

  // 根据交班模式加载不同的报表文件
  const reportFile = shiftMode.value === 'paid_in'
    ? 'handoverReportCashRealization.mrt'
    : 'handoverReport.mrt'
  report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/${reportFile}`)

  const settingsStore = useSettingsStore() // 使用 Vuex 存储语言设置
  const currentLanguage = settingsStore.lang // 当前语言 'zh-cn' 或 'en'
  const localizationFile = `${currentLanguage}.xml`
  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)

  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')
  dataSet.readJson(JSON.stringify(json))
  report.dictionary.databases.clear()
  report.regData('JSON', 'JSON', dataSet)

  // 创建Stimulsoft.ViewerOptions
  const options = new Stimulsoft.Viewer.StiViewerOptions()
  // 隐藏帮助按钮（问号图标）
  options.toolbar.showAboutButton = false // 隐藏帮助按钮
  const viewer = new Stimulsoft.Viewer.StiViewer(options, 'StiViewer', false)
  viewer.report = report
  viewer.renderHtml('report')
  loading.value = false
}

// 获取交班模式配置
async function getHotelParamConfigShiftmode() {
  try {
    const res = await hotelParamConfigApi.getHotelParamConfigShiftmode({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
    })
    if (res.code === 0 && res.data && res.data.value) {
      shiftMode.value = res.data.value.shiftMode || 'cash_flow'
    }
  } catch (error) {
    console.error('获取交班模式配置失败:', error)
    // 如果获取失败，保持默认值 cash_flow
  }
}

// 获取可用班次（已固化的班次）
async function getAvailableShifts() {
  if (shiftMode.value !== 'paid_in') {
    return
  }

  handoverReportLoading.value = true
  try {
    const res = await shiftTimeApi.getHandoverReportList({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      bizDate: dayjs(data.value.search.bizDate).format('YYYY-MM-DD'),
    })

    if (res.code === 0 && res.data) {
      // 提取返回数据中的 shiftNo 数组
      availableShiftNos.value = res.data.map((item: any) => item.shiftNo).filter(Boolean)
    } else {
      availableShiftNos.value = []
    }

    // 切换营业日后，自动选择可用班次
    autoSelectAvailableShift()
  } catch (error) {
    console.error('获取可用班次失败:', error)
    availableShiftNos.value = []
    // 即使出错也要尝试自动选择
    autoSelectAvailableShift()
  } finally {
    handoverReportLoading.value = false
  }
}

// 自动选择可用班次
function autoSelectAvailableShift() {
  if (shiftMode.value !== 'paid_in') {
    return
  }

  // 获取所有可选的班次
  const availableShifts = shifts.value.filter(shift => isShiftAvailable(shift.shiftCode))

  if (availableShifts.length > 0) {
    // 如果有可选班次，选择第一个
    data.value.search.singleShiftNo = availableShifts[0].shiftCode
  } else {
    // 如果没有可选班次，清空选择
    data.value.search.singleShiftNo = ''
  }
}

// 计算是否有可选班次（用于控制查询按钮状态）
const hasAvailableShifts = computed(() => {
  if (shiftMode.value !== 'paid_in') {
    return true
  }
  return shifts.value.some(shift => isShiftAvailable(shift.shiftCode))
})

// 检查班次是否可用
function isShiftAvailable(shiftCode: string): boolean {
  if (shiftMode.value !== 'paid_in') {
    return true
  }

  // 如果是当前营业日的当前班次，也能选择
  const currentBizDate = dayjs().format('YYYY-MM-DD')
  const selectedBizDate = dayjs(data.value.search.bizDate).format('YYYY-MM-DD')
  if (selectedBizDate === currentBizDate && shiftCode === userStore.shiftCode) {
    return true
  }

  return availableShiftNos.value.includes(shiftCode)
}

const shifts = ref<{ shiftCode: string; shiftName: string }[]>([])
async function getShiftTimeList() {
  await shiftTimeApi
    .getShiftTimeList({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      state: '1',
    })
    .then((res) => {
      shifts.value = res.data

      // 设置默认班次
      const defaultShift = shifts.value.find((shift) => shift.shiftCode === userStore.shiftCode)
      if (defaultShift) {
        data.value.search.shiftNo = [defaultShift.shiftCode] // 多选模式默认班次
        data.value.search.singleShiftNo = defaultShift.shiftCode // 单选模式默认班次
      }
    })
}

const operators = ref<{ username: string; nickname: string }[]>([])
async function getOperatorList() {
  await userApi.getSimpleUserList({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {
    operators.value = res.data
  })
}

const shortcuts = [
  {
    text: t('Today'), // 使用多语言支持
    value: new Date(),
  },
  {
    text: t('Yesterday'), // 使用多语言支持
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return date
    },
  },
  {
    text: t('A Week Ago'), // 使用多语言支持
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      return date
    },
  },
]

// 打开交班弹窗
async function openChangeShiftDialog() {
  changeShiftDialogVisible.value = true
  changeShiftLoading.value = true

  try {
    const res = await shiftTimeApi.getChangeShiftTime({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
    })

    if (res.code === 0 && res.data) {
      changeShiftData.value = res.data
      // 默认选中当前班次
      selectedShiftCode.value = res.data.currentShiftCode
    }
  } catch (error) {
    console.error('获取交班信息失败:', error)
    ElMessage.error('获取交班信息失败')
  } finally {
    changeShiftLoading.value = false
  }
}

// 确认交班
async function confirmChangeShift() {
  // 前端已经通过按钮禁用做了校验，这里保留双重校验以确保安全
  if (!selectedShiftCode.value) {
    ElMessage.warning('请选择要交班的班次')
    return
  }

  if (selectedShiftCode.value === changeShiftData.value.currentShiftCode) {
    ElMessage.warning('请选择不同的班次进行交班')
    return
  }

  changeShiftLoading.value = true

  try {
    const res = await shiftTimeApi.handover({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      shiftCode: selectedShiftCode.value
    })

    if (res.code === 0) {
      ElMessage.success('交班成功')
      changeShiftDialogVisible.value = false

      // 更新 localStorage 中的班次和营业日期信息
      if (res.data) {
        // 更新班次信息
        if (res.data.shiftName) {
          storage.local.set('shiftName', res.data.shiftName)
        }
        if (res.data.shiftCode) {
          storage.local.set('shiftCode', res.data.shiftCode)
          // 同时更新 userStore 中的 shiftCode
          userStore.shiftCode = res.data.shiftCode
        }
        // 更新营业日期
        if (res.data.bizDate) {
          storage.local.set('bizDate', res.data.bizDate)
        }
      }

      // 交班成功后重新查询已固化的班次
      if (shiftMode.value === 'paid_in') {
        await getAvailableShifts()
      }

      // 交班成功后刷新页面数据
      onSearch()
    } else {
      ElMessage.error(res.msg || '交班失败')
    }
  } catch (error) {
    console.error('交班失败:', error)
    ElMessage.error('交班失败，请稍后重试')
  } finally {
    changeShiftLoading.value = false
  }
}

// 取消交班
function cancelChangeShift() {
  changeShiftDialogVisible.value = false
  selectedShiftCode.value = ''
}
</script>

<template>
  <div v-loading="loading" :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="150px" inline-message inline class="search-form">
          <div class="form-content">
            <el-form-item :label="t('Business Date')">
              <el-date-picker v-model="data.search.bizDate" type="date" :placeholder="t('Select Date')" :shortcuts="shortcuts" :clearable="false" />
            </el-form-item>
            <el-form-item :label="t('Shift')">
              <!-- paid_in 模式：单选 -->
              <el-select
                v-if="shiftMode === 'paid_in'"
                v-model="data.search.singleShiftNo"
                :placeholder="t('Please Select')"
                :loading="handoverReportLoading"
                style="width: 300px"
              >
                <el-option
                  v-for="item in shifts"
                  :key="item.shiftCode"
                  :label="item.shiftName"
                  :value="item.shiftCode"
                  :disabled="!isShiftAvailable(item.shiftCode)"
                  :title="!isShiftAvailable(item.shiftCode) ? t('No Data Available') : ''"
                />
              </el-select>
              <!-- cash_flow 模式：多选 -->
              <el-select
                v-else
                v-model="data.search.shiftNo"
                multiple
                :placeholder="t('Please Select')"
                style="width: 300px"
              >
                <el-option v-for="item in shifts" :key="item.shiftCode" :label="item.shiftName" :value="item.shiftCode" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="shiftMode !== 'paid_in'" :label="t('Operator')">
              <el-select v-model="data.search.recorder" :placeholder="t('Please Select')" style="width: 150px" clearable>
                <el-option v-for="item in operators" :key="item.username" :label="item.nickname" :value="item.username" />
              </el-select>
            </el-form-item>
            <el-form-item class="button-item">
              <el-button
                type="primary"
                @click="onSearch"
                :disabled="shiftMode === 'paid_in' && !hasAvailableShifts"
              >
                {{ t('Query') }}
              </el-button>
              <!-- 只有在 paid_in 模式下才显示交班按钮 -->
              <el-button
                v-if="shiftMode === 'paid_in'"
                type="primary"
                plain
                @click="openChangeShiftDialog"
                :disabled="data.handoverStatus === '1'"
              >
                {{ data.handoverStatus === '1' ? t('Already Changed') : t('Change Shift') }}
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </search-bar>
      <div id="report" />
    </page-main>

    <!-- 交班弹窗 -->
    <el-dialog
      v-model="changeShiftDialogVisible"
      :title="t('Change Shift')"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-loading="changeShiftLoading">
        <!-- 当前班次 -->
        <div class="current-shift-section">
          <h4>{{ t('Current Shift') }}</h4>
          <div class="shift-info">
            <span class="shift-name">{{ changeShiftData.currentShiftName }}</span>
            <span class="shift-code">({{ changeShiftData.currentShiftCode }})</span>
          </div>
        </div>

        <!-- 可选班次 -->
        <div class="available-shifts-section">
          <h4>{{ t('Available Shifts') }}</h4>
          <el-radio-group v-model="selectedShiftCode" class="shift-radio-group">
            <el-radio
              v-for="shift in changeShiftData.shiftTimeList"
              :key="shift.shiftCode"
              :value="shift.shiftCode"
              class="shift-radio-item"
            >
              <div class="shift-option">
                <span class="shift-name">{{ shift.shiftName }}</span>
                <span class="shift-time">{{ shift.startTime }} - {{ shift.endTime }}</span>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelChangeShift">{{ t('Cancel') }}</el-button>
          <el-button
            type="primary"
            @click="confirmChangeShift"
            :loading="changeShiftLoading"
            :disabled="!selectedShiftCode || selectedShiftCode === changeShiftData.currentShiftCode"
          >
            {{ t('Confirm Change') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  width: 100%;

  .form-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px; // 减小间距
    padding: 0px 0 8px;

    .el-form-item {
      margin: 0;
    }

    .button-item {
      margin-left: 8px; // 减小按钮间距
    }
  }
}

/* 交班弹窗样式 */
.current-shift-section {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .shift-info {
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;

    .shift-name {
      font-weight: 500;
      color: #303133;
      margin-right: 8px;
    }

    .shift-code {
      color: #909399;
      font-size: 14px;
    }
  }
}

.available-shifts-section {
  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .shift-radio-group {
    width: 100%;

    .shift-radio-item {
      width: 100%;
      margin: 0 0 12px 0;
      padding: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      &.is-checked {
        border-color: #409eff;
        background-color: #ecf5ff;
      }

      .shift-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .shift-name {
          font-weight: 500;
          color: #303133;
        }

        .shift-time {
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button.is-disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

/* 禁用班次选项的样式 */
:deep(.el-select-dropdown__item.is-disabled) {
  color: #c0c4cc !important;
  cursor: not-allowed !important;

  &:hover {
    background-color: #f5f7fa !important;
  }
}
</style>
