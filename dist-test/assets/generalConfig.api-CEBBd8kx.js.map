{"version": 3, "file": "generalConfig.api-CEBBd8kx.js", "sources": ["../../src/api/modules/pms/config/generalConfig.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/general-config'\r\n/** 通用配置 */\r\nexport default {\r\n  /**\r\n   * 通用查询接口\r\n   * @param data\r\n   * @returns\r\n   */\r\n  list: (data: { gcode: string; hcode?: string; type?: string; types?: string; isG?: string; parentCode?: string; isEnable?: string }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获得通用配置列表(客户等级、订单原因、服务标识)\r\n   * @param gcode 集团代码\r\n   * @param type 参数类型\r\n   * @returns\r\n   */\r\n  getGeneralConfigList: (gcode: string, type: string) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: {\r\n        gcode,\r\n        type,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 获得付款科目列表\r\n   * @param gcode 集团代码\r\n   */\r\n  getPayAccountList: (gcode: string) =>\r\n    api.get(`${BASE_PATH}/list-pay-account`, {\r\n      params: {\r\n        gcode,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 获得消费科目列表\r\n   * @param gcode 集团代码\r\n   */\r\n  getConsumeAccountList: (gcode: string) =>\r\n    api.get(`${BASE_PATH}/list-consume-account`, {\r\n      params: { gcode },\r\n    }),\r\n\r\n  /**\r\n   * 更新通用配置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGeneralConfig: (data: any) => api.put(`${BASE_PATH}/update`, data, {}),\r\n\r\n  /**\r\n   * 更新通用配置-订单原因状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGeneralConfigReasonStatus: (data: { id: number; isEnable: string }) => api.put(`${BASE_PATH}/update-reason-status`, data, {}),\r\n\r\n  /**\r\n   * 更新通用配置-付款科目状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGeneralConfigPayAccountStatus: (data: { id: number; isEnable: string }) => api.put(`${BASE_PATH}/update-pay-account-status`, data, {}),\r\n\r\n  /**\r\n   * 更新通用配置-消费科目状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGeneralConfigConsumeAccountStatus: (data: { id: number; isEnable: string }) => api.put(`${BASE_PATH}/update-consume-account-status`, data, {}),\r\n\r\n  /**\r\n   * 获得通用配置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getGeneralConfig: (data: { gcode: string; hcode: string; code: string; type: string }) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 修改预抵时间配置\r\n   * @returns\r\n   */\r\n  updateDefaultPlanCheckinTime: (data: any) => api.put(`${BASE_PATH}/update-default-plan-checkin-time`, data, {}),\r\n\r\n  /**\r\n   * 更新通用配置-订单原因\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGeneralConfigReason: (data: any) => api.put(`${BASE_PATH}/update-reason`, data, {}),\r\n  /**\r\n   * 创建通用配置-订单原因\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createGeneralConfigReason: (data: any) => api.post(`${BASE_PATH}/create-reason`, data, {}),\r\n\r\n  /**\r\n   * 创建通用配置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createGeneralConfig: (data: any) => api.post(`${BASE_PATH}/create`, data, {}),\r\n\r\n  /**\r\n   * 创建通用配置-房间特征\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createGeneralConfigRoomFeature: (data: any) => api.post(`${BASE_PATH}/create-room-feature`, data, {}),\r\n\r\n  /**\r\n   * 更新通用配置-房间特征\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGeneralConfigRoomFeature: (data: any) => api.put(`${BASE_PATH}/update-room-feature`, data, {}),\r\n\r\n  /**\r\n   * 更新通用配置-房间特征状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateGeneralConfigRoomFeatureStatus: (data: { id: number; isEnable: string }) => api.put(`${BASE_PATH}/update-room-feature-status`, data, {}),\r\n\r\n  /**\r\n   * 获取营业日期\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getBizData: (data: any) => api.get(`${BASE_PATH}/get-biz-date`, { params: data }),\r\n\r\n  /**\r\n   * 最晚退房时间\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getCheckOutTime: (data: any) => api.get(`${BASE_PATH}/get-check-out-time`, { params: data }),\r\n\r\n  /**\r\n   * 获得超过多少分钟算1小时配置\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getHowMinuteConfig: (data: any) => api.get(`${BASE_PATH}/get-how-minute`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "generalConfigApi", "list", "data", "api", "get", "params", "getGeneralConfigList", "gcode", "type", "getPayAccountList", "getConsumeAccountList", "updateGeneralConfig", "put", "updateGeneralConfigReasonStatus", "updateGeneralConfigPayAccountStatus", "updateGeneralConfigConsumeAccountStatus", "getGeneralConfig", "updateDefaultPlanCheckinTime", "updateGeneralConfigReason", "createGeneralConfigReason", "post", "createGeneralConfig", "createGeneralConfigRoomFeature", "updateGeneralConfigRoomFeature", "updateGeneralConfigRoomFeatureStatus", "getBizData", "getCheckOutTime", "getHowMinuteConfig"], "mappings": "wCAEA,MAAMA,EAAY,+BAEHC,EAAA,CAMbC,KAAOC,GACLC,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQH,IASZI,qBAAsB,CAACC,EAAeC,IACpCL,EAAIC,IAAI,GAAGL,SAAkB,CAC3BM,OAAQ,CACNE,QACAC,UAQNC,kBAAoBF,GAClBJ,EAAIC,IAAI,GAAGL,qBAA8B,CACvCM,OAAQ,CACNE,WAQNG,sBAAwBH,GACtBJ,EAAIC,IAAI,GAAGL,yBAAkC,CAC3CM,OAAQ,CAAEE,WAQdI,oBAAsBT,GAAcC,EAAIS,IAAI,GAAGb,WAAoBG,EAAM,IAOzEW,gCAAkCX,GAA2CC,EAAIS,IAAI,GAAGb,yBAAkCG,EAAM,IAOhIY,oCAAsCZ,GAA2CC,EAAIS,IAAI,GAAGb,8BAAuCG,EAAM,IAOzIa,wCAA0Cb,GAA2CC,EAAIS,IAAI,GAAGb,kCAA2CG,EAAM,IAOjJc,iBAAmBd,GACjBC,EAAIC,IAAI,GAAGL,QAAiB,CAC1BM,OAAQH,IAOZe,6BAA+Bf,GAAcC,EAAIS,IAAI,GAAGb,qCAA8CG,EAAM,IAO5GgB,0BAA4BhB,GAAcC,EAAIS,IAAI,GAAGb,kBAA2BG,EAAM,IAMtFiB,0BAA4BjB,GAAcC,EAAIiB,KAAK,GAAGrB,kBAA2BG,EAAM,IAOvFmB,oBAAsBnB,GAAcC,EAAIiB,KAAK,GAAGrB,WAAoBG,EAAM,IAO1EoB,+BAAiCpB,GAAcC,EAAIiB,KAAK,GAAGrB,wBAAiCG,EAAM,IAOlGqB,+BAAiCrB,GAAcC,EAAIS,IAAI,GAAGb,wBAAiCG,EAAM,IAOjGsB,qCAAuCtB,GAA2CC,EAAIS,IAAI,GAAGb,+BAAwCG,EAAM,IAO3IuB,WAAavB,GAAcC,EAAIC,IAAI,GAAGL,iBAA0B,CAAEM,OAAQH,IAO1EwB,gBAAkBxB,GAAcC,EAAIC,IAAI,GAAGL,uBAAgC,CAAEM,OAAQH,IAOrFyB,mBAAqBzB,GAAcC,EAAIC,IAAI,GAAGL,mBAA4B,CAAEM,OAAQH"}