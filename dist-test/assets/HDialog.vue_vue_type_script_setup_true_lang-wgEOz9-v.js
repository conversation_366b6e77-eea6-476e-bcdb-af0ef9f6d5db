import{d as e,a1 as a,a2 as t,a3 as l,b as s,B as o,o as r,e as n,w as i,f as p,u as d,a4 as u,a5 as c,a6 as f,g as y,a7 as m,a8 as x,a9 as v,h as g,Y as b,aa as w,ab as _,c as h,R as k,ac as B,_ as j}from"./index-CkEhI1Zk.js";const C={class:"fixed inset-0 overflow-y-auto"},F={class:"min-h-full flex items-end justify-center p-4 text-center lg-items-center"},T={flex:"~ items-center justify-between","px-4":"","py-3":"","border-b":"~ solid stone/15","text-6":""},V={key:0,flex:"~ items-center justify-end","px-4":"","py-3":"","border-t":"~ solid stone/15"},$=e({__name:"HDialog",props:a({appear:{type:Boolean,default:!1},title:{},preventClose:{type:Boolean,default:!1},overlay:{type:Boolean,default:!1}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:a(["close"],["update:modelValue"]),setup(e,{emit:a}){const $=a,z=t(e,"modelValue"),D=l(),H=s({enter:"ease-in-out duration-500",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-500",leaveFrom:"opacity-100",leaveTo:"opacity-0"}),M=o((()=>({enter:"ease-out duration-300",enterFrom:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95",enterTo:"opacity-100 translate-y-0 lg-scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-y-0 lg-scale-100",leaveTo:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95"})));function R(){z.value=!1,$("close")}return(e,a)=>{const t=j;return r(),n(d(B),{as:"template",appear:e.appear,show:z.value},{default:i((()=>[p(d(u),{class:"fixed inset-0 z-2000 flex",onClose:a[0]||(a[0]=a=>!e.preventClose&&R())},{default:i((()=>[p(d(c),f({as:"template",appear:e.appear},d(H)),{default:i((()=>[y("div",{class:m(["fixed inset-0 bg-stone-2/75 transition-opacity dark-bg-stone-8/75",{"backdrop-blur-sm":e.overlay}])},null,2)])),_:1},16,["appear"]),y("div",C,[y("div",F,[p(d(c),f({as:"template",appear:e.appear},d(M)),{default:i((()=>[p(d(x),{class:"relative w-full flex flex-col overflow-hidden rounded-xl bg-white text-left shadow-xl lg-my-8 lg-max-w-lg dark-bg-stone-8"},{default:i((()=>[y("div",T,[p(d(v),{"m-0":"","text-lg":"","text-dark":"","dark-text-white":""},{default:i((()=>[g(b(e.title),1)])),_:1}),p(t,{name:"i-carbon:close","cursor-pointer":"",onClick:R})]),p(d(w),{"m-0":"","overflow-y-auto":"","p-4":"","text-start":""},{default:i((()=>[_(e.$slots,"default")])),_:3}),d(D).footer?(r(),h("div",V,[_(e.$slots,"footer")])):k("",!0)])),_:3})])),_:3},16,["appear"])])])])),_:3})])),_:3},8,["appear","show"])}}});export{$ as _};
//# sourceMappingURL=HDialog.vue_vue_type_script_setup_true_lang-wgEOz9-v.js.map
