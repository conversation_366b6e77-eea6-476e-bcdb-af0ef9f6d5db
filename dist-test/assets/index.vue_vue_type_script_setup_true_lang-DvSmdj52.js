import{d as e,a1 as o,a2 as t,o as a,c as l,ab as s,g as n,f as r,R as u,a7 as i,_ as d}from"./index-CkEhI1Zk.js";const g={key:0,class:"absolute bottom-0 left-0 w-full translate-y-1/2 text-center"},c=e({name:"SearchBar",__name:"index",props:o({showToggle:{type:Boolean,default:!0},background:{type:Boolean,default:!1}},{fold:{type:Boolean,default:!0},foldModifiers:{}}),emits:o(["toggle"],["update:fold"]),setup(e,{emit:o}){const c=o,f=t(e,"fold");function p(){f.value=!f.value,c("toggle",f.value)}return(e,o)=>{const t=d;return a(),l("div",{class:i(["relative",{"py-4":e.showToggle,"px-4 bg-[var(--g-bg)] transition":e.background}])},[s(e.$slots,"default",{fold:f.value,toggle:p}),e.showToggle?(a(),l("div",g,[n("button",{class:"h-5 inline-flex cursor-pointer select-none items-center border-size-0 rounded bg-[var(--g-bg)] px-2 text-xs font-medium outline-none",onClick:p},[r(t,{name:f.value?"i-ep:caret-bottom":"i-ep:caret-top"},null,8,["name"])])])):u("",!0)],2)}}});export{c as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-DvSmdj52.js.map
