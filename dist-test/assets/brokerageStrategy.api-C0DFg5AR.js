import{a as e}from"./index-CkEhI1Zk.js";const t="admin-api/pms/brokerage-strategy",a={createBrokerageStrategy:a=>e.post(`${t}/create`,a),getBrokerageStrategyList:a=>e.get(`${t}/list`,{params:a}),getBrokerageStrategyPage:a=>e.get(`${t}/page`,{params:a}),getBrokerageStrategy:a=>e.get(`${t}/get`,{params:a}),updateBrokerageStrategyStatus:a=>e.put(`${t}/update-status`,a),updateBrokerageStrategy:a=>e.put(`${t}/update`,a)};export{a as b};
//# sourceMappingURL=brokerageStrategy.api-C0DFg5AR.js.map
