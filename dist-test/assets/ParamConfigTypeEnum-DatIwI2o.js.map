{"version": 3, "file": "ParamConfigTypeEnum-DatIwI2o.js", "sources": ["../../src/models/dict/ParamConfigTypeEnum.ts"], "sourcesContent": ["export enum ParamConfigTypeEnum {\r\n  CASH_FLOW = 'cash_flow',\r\n  PAID_IN = 'paid_in',\r\n  RECEIVABLE = 'receivable',\r\n  SHIFT_MODE = 'shift_mode',\r\n  BREAKFAST_TICKET = 'breakfast_ticket',\r\n  SUBJECT = 'subject',\r\n  ROOMCOLOR = 'roomcolor',\r\n  FINANCE = 'finance',\r\n  MEMBERCONFIG = 'memberconfig',\r\n  MEMBERRULE = 'memberrule',\r\n  NIGHT_NUM = 'nightnum',\r\n  ORDER = 'order',\r\n  FRONT = 'front',\r\n  CASH_MODIFY = 'cash_modify',\r\n  DEPOSIT = 'deposit',\r\n  USER_ROOM_PANEL = 'room_panel',\r\n}\r\n"], "names": ["ParamConfigTypeEnum"], "mappings": "AAAY,IAAAA,GAAAA,IACVA,EAAY,UAAA,YACZA,EAAU,QAAA,UACVA,EAAa,WAAA,aACbA,EAAa,WAAA,aACbA,EAAmB,iBAAA,mBACnBA,EAAU,QAAA,UACVA,EAAY,UAAA,YACZA,EAAU,QAAA,UACVA,EAAe,aAAA,eACfA,EAAa,WAAA,aACbA,EAAY,UAAA,WACZA,EAAQ,MAAA,QACRA,EAAQ,MAAA,QACRA,EAAc,YAAA,cACdA,EAAU,QAAA,UACVA,EAAkB,gBAAA,aAhBRA,IAAAA,GAAA,CAAA"}