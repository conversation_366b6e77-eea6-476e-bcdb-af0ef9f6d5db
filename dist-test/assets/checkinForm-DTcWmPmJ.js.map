{"version": 3, "file": "checkinForm-DTcWmPmJ.js", "sources": ["../../src/views/print/checkinForm.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"printCheckinRecord\": \"Print Check-in Record\",\r\n    \"cancel\": \"Cancel\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"printCheckinRecord\": \"打印入住单\",\r\n    \"cancel\": \"取消\"\r\n  },\r\n  \"km\": {\r\n    \"printCheckinRecord\": \"បោះពុម្ពកំណត់ត្រាចូលសំណាក\",\r\n    \"cancel\": \"បោះបង់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { printApi, printFormApi } from '@/api/modules/index'\r\nimport { BillType, layout_width_map, PrintFormat } from '@/models/dict/constants'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\nimport PosCheckInForm78 from './posCheckInForm.vue' // 引入POS格式的自定义组件\r\n\r\ndefineOptions({\r\n  name: 'PrintCheckInForm',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    orderNo: string | number\r\n    subCode?: string\r\n    fee?: number\r\n    autoShowPrint?: boolean\r\n    hideDialog?: boolean\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    orderNo: '',\r\n    subCode: '',\r\n    fee: 0,\r\n    autoShowPrint: false,\r\n    hideDialog: false,\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  refresh: []\r\n  'print-started': []\r\n}>()\r\n\r\nconst Stimulsoft = window.Stimulsoft\r\n\r\nconst { t } = useI18n() // 解构 t 函数\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  tableAutoHeight: true,\r\n  showWidth: '400px',\r\n})\r\nconst loading = ref(false)\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nonMounted(async () => {\r\n  await Promise.all([getPrintLayout(), getPrintInfo()])\r\n})\r\n\r\nconst layout = ref(PrintFormat.POS.toString())\r\nasync function getPrintLayout() {\r\n  printApi\r\n    .getPrintLayout({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      billCode: BillType.CHECK_IN_FORM,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        layout.value = res.data.layout\r\n        data.value.showWidth = layout_width_map.get(layout.value)\r\n      }\r\n    })\r\n}\r\n\r\n// 是否使用POS格式\r\nconst isPosFormat = computed(() => layout.value === PrintFormat.POS.toString())\r\n\r\n// POS格式打印数据\r\nconst posFormData = ref(null)\r\n\r\nasync function getPrintInfo() {\r\n  loading.value = true\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    orderNo: props.orderNo,\r\n  }\r\n\r\n  // 如果传入了subCode参数，则添加到请求参数中\r\n  if (props.subCode) {\r\n    params.subCode = props.subCode\r\n  }\r\n\r\n  // 如果传入了fee参数，则添加到请求参数中\r\n  if (props.fee && props.fee > 0) {\r\n    params.fee = props.fee\r\n  }\r\n\r\n  printFormApi.printCheckInForm(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      if (isPosFormat.value) {\r\n        // 如果是POS格式，直接保存数据供自定义组件使用\r\n        posFormData.value = res.data\r\n        loading.value = false\r\n\r\n        // 如果设置了自动打印，等待DOM更新后触发打印\r\n        if (props.autoShowPrint) {\r\n          nextTick(() => {\r\n            setTimeout(() => {\r\n              printReport()\r\n            }, 800)\r\n          })\r\n        }\r\n      } else {\r\n        // 其他格式使用Stimulsoft报表\r\n        setJson(res.data)\r\n      }\r\n    }\r\n  })\r\n}\r\nconst timestamp = Date.now()\r\nlet reportInstance: any\r\nasync function setJson(json: any) {\r\n  // 只有在非POS格式时才执行报表渲染\r\n  if (isPosFormat.value) {\r\n    return\r\n  }\r\n\r\n  const licensePath = new URL('/src/assets/license.key', import.meta.url).href\r\n  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)\r\n  const report = new Stimulsoft.Report.StiReport()\r\n\r\n  if (layout.value === PrintFormat.A4.toString()) {\r\n     //report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinFormA4.mrt`)\r\n     report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinFormA4.mrt?t=${timestamp}`)\r\n  } else if (layout.value === PrintFormat.A412.toString()) {\r\n     //report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinFormA4-1-2.mrt`)\r\n     report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinFormA4-1-2.mrt?t=${timestamp}`)\r\n  } else if (layout.value === PrintFormat.A413.toString()) {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinFormA4-1-3.mrt`)\r\n    // report.loadFile(\r\n    //   `https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinFormA4-1-3.mrt?t=${timestamp}`,\r\n    // )\r\n  } else {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinForm78.mrt`)\r\n    // report.loadFile(\r\n    //   `https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinForm78.mrt?t=${timestamp}`\r\n    // )\r\n  }\r\n\r\n  const settingsStore = useSettingsStore() // 使用 Vuex 存储语言设置\r\n  const currentLanguage = settingsStore.lang // 当前语言 'zh-cn' 或 'en'\r\n  const localizationFile = `${currentLanguage}.xml`\r\n  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)\r\n\r\n  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')\r\n  dataSet.readJson(JSON.stringify(json))\r\n  report.dictionary.databases.clear()\r\n  report.regData('JSON', 'JSON', dataSet)\r\n  // 配置报表查看器选项\r\n  const viewerOptions = new Stimulsoft.Viewer.StiViewerOptions()\r\n  viewerOptions.toolbar.visible = false // 隐藏工具栏\r\n  const viewer = new Stimulsoft.Viewer.StiViewer(viewerOptions, 'StiViewer', false)\r\n  viewer.report = report\r\n  viewer.renderHtml('report')\r\n  reportInstance = report // 保存报表实例\r\n  loading.value = false\r\n\r\n  // 如果设置了自动打印，等待报表渲染完成后触发打印\r\n  if (props.autoShowPrint) {\r\n    setTimeout(() => {\r\n      printReport()\r\n    }, 1000)\r\n  }\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n\r\nfunction printReport() {\r\n  // 通知父组件打印已开始\r\n  emits('print-started')\r\n\r\n  if (isPosFormat.value) {\r\n    // 创建一个隐藏的iframe用于打印\r\n    const printFrame = document.createElement('iframe')\r\n    printFrame.style.position = 'fixed'\r\n    printFrame.style.right = '0'\r\n    printFrame.style.bottom = '0'\r\n    printFrame.style.width = '0'\r\n    printFrame.style.height = '0'\r\n    printFrame.style.border = '0'\r\n\r\n    document.body.appendChild(printFrame)\r\n    const printContent = document.querySelector('#pos-checkin-form')?.innerHTML || ''\r\n    // 检查contentWindow是否存在\r\n    const contentWindow = printFrame.contentWindow\r\n    if (!contentWindow) {\r\n      console.error('打印窗口创建失败')\r\n      return\r\n    }\r\n\r\n    const frameDoc = contentWindow.document\r\n    frameDoc.write(`\r\n        <html>\r\n          <head>\r\n          <meta charset=\"UTF-8\">\r\n            <title>入住登记单</title>\r\n            <style>\r\n              .pos-print-container {\r\n                width: 76mm;\r\n                margin: 0 auto;\r\n                padding: 2mm;\r\n                font-size: 14px;\r\n                display: flex;\r\n                flex-direction: column;\r\n                align-items: center;\r\n                color: black;\r\n                padding: 5mm;\r\n              }\r\n\r\n              .hotel-title {\r\n                text-align: center;\r\n                font-size: 30px;\r\n                font-weight: bold;\r\n                margin-bottom: 5px;\r\n              }\r\n\r\n              .order-info {\r\n                width: 95%;\r\n                text-align: left;\r\n                margin-bottom: 10px;\r\n              }\r\n\r\n              .order-no,\r\n              .print-time {\r\n                margin-top: 5px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n              }\r\n\r\n              .form-title {\r\n                text-align: center;\r\n                font-size: 14px;\r\n                margin-bottom: 10px;\r\n              }\r\n\r\n              .info-table {\r\n                width: 95%;\r\n                border-collapse: collapse;\r\n                margin-bottom: 10px;\r\n                margin-left: auto;\r\n                margin-right: auto;\r\n              }\r\n\r\n              .info-table,\r\n              .info-table th,\r\n              .info-table td {\r\n                border: 1px solid #000;\r\n              }\r\n\r\n              .info-table th {\r\n                text-align: left;\r\n                font-weight: bold;\r\n                font-size: 12px;\r\n                width: 25%;\r\n              }\r\n\r\n              .info-table td {\r\n                padding: 3px 5px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n              }\r\n\r\n              .section-title {\r\n                text-align: left;\r\n                font-weight: bold;\r\n                margin: 10px 0 5px 0;\r\n                width: 95%;\r\n              }\r\n\r\n              .together-guests {\r\n                margin-top: 10px;\r\n                width: 100%;\r\n                display: flex;\r\n                flex-direction: column;\r\n                align-items: center;\r\n              }\r\n\r\n              .together-table {\r\n                width: 95%;\r\n              }\r\n\r\n              .footer {\r\n                width: 95%;\r\n                margin-top: 10px;\r\n                font-size: 12px;\r\n              }\r\n\r\n              .footer-line {\r\n                margin-bottom: 10px;\r\n              }\r\n              @media print {\r\n                @page { size: 80mm auto; margin: 0; }\r\n              }\r\n            </style>\r\n          </head>\r\n          <body>\r\n            ${printContent}\r\n          </body>\r\n        </html>\r\n      `)\r\n    frameDoc.close()\r\n\r\n    // 等待内容加载完成后打印\r\n    printFrame.onload = () => {\r\n      try {\r\n        // 使用浏览器原生打印预览\r\n        printFrame.contentWindow.print()\r\n\r\n        // 打印完成后移除iframe并关闭组件\r\n        setTimeout(() => {\r\n          document.body.removeChild(printFrame)\r\n          // 如果是隐藏模式，打印完成后自动关闭组件\r\n          if (props.hideDialog) {\r\n            myVisible.value = false\r\n          }\r\n        }, 1000)\r\n      } catch (e) {\r\n        console.error('打印失败:', e)\r\n      }\r\n    }\r\n  } else {\r\n    // 其他格式使用Stimulsoft报表打印\r\n    reportInstance?.print()\r\n    // 如果是隐藏模式，打印完成后自动关闭组件\r\n    if (props.hideDialog) {\r\n      setTimeout(() => {\r\n        myVisible.value = false\r\n      }, 2000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <!-- 当hideDialog为true时，使用隐藏的div容器 -->\r\n  <div v-if=\"hideDialog\" style=\"position: fixed; top: -9999px; left: -9999px; visibility: hidden\">\r\n    <!-- 报表区域 -->\r\n    <div v-if=\"!isPosFormat\" id=\"report\" style=\"width: 800px; height: 600px\" />\r\n\r\n    <!-- POS格式使用自定义Vue组件 -->\r\n    <div v-else id=\"pos-checkin-form\" style=\"width: 800px; height: 600px\">\r\n      <PosCheckInForm78 v-if=\"posFormData\" id=\"pos-bill-form\" :form-data=\"posFormData\" />\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 正常的弹窗模式 -->\r\n  <el-dialog v-else v-model=\"myVisible\" :close-on-click-modal=\"false\" :width=\"data.showWidth\" append-to-body destroy-on-close :show-close=\"true\" style=\"min-height: 600px\">\r\n    <div style=\"display: flex; flex-direction: column; height: 100%\">\r\n      <!-- 按钮区域 - 当autoShowPrint为true时隐藏 -->\r\n      <div v-if=\"!autoShowPrint\" style=\"display: flex; gap: 10px; justify-content: center; margin-bottom: 10px\">\r\n        <el-button type=\"primary\" @click=\"printReport\">\r\n          {{ t('printCheckinRecord') }}\r\n        </el-button>\r\n        <el-button @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n      </div>\r\n      <!-- 报表区域 -->\r\n      <div v-if=\"!isPosFormat\" id=\"report\" style=\"flex: 1; overflow-y: auto\" />\r\n\r\n      <!-- POS格式使用自定义Vue组件 -->\r\n      <div v-else id=\"pos-checkin-form\" style=\"flex: 1; overflow-y: auto\">\r\n        <PosCheckInForm78 v-if=\"posFormData\" id=\"pos-bill-form\" :form-data=\"posFormData\" />\r\n        <div v-else class=\"loading-placeholder\">\r\n          <el-empty v-if=\"!loading\" description=\"暂无数据\" />\r\n          <el-skeleton v-else :rows=\"10\" animated />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "Stimulsoft", "window", "t", "useI18n", "userStore", "useUserStore", "data", "ref", "tableAutoHeight", "showWidth", "loading", "myVisible", "computed", "get", "modelValue", "set", "val", "onMounted", "async", "Promise", "all", "getPrintLayout", "getPrintInfo", "layout", "PrintFormat", "POS", "toString", "printApi", "gcode", "hcode", "billCode", "BillType", "CHECK_IN_FORM", "then", "res", "code", "value", "layout_width_map", "isPosFormat", "posFormData", "params", "orderNo", "subCode", "fee", "printFormApi", "printCheckInForm", "autoShowPrint", "nextTick", "setTimeout", "printReport", "json", "licensePath", "URL", "url", "href", "Base", "StiLicense", "loadFromFile", "report", "Report", "StiReport", "A4", "loadFile", "timestamp", "A412", "A413", "settingsStore", "useSettingsStore", "localizationFile", "lang", "Localization", "StiLocalization", "setLocalizationFile", "dataSet", "System", "Data", "DataSet", "read<PERSON>son", "JSON", "stringify", "dictionary", "databases", "clear", "regData", "viewerOptions", "Viewer", "StiViewerOptions", "toolbar", "visible", "viewer", "StiViewer", "renderHtml", "reportInstance", "<PERSON><PERSON><PERSON>", "Date", "now", "onCancel", "printFrame", "document", "createElement", "style", "position", "right", "bottom", "width", "height", "border", "body", "append<PERSON><PERSON><PERSON>", "printContent", "_a", "querySelector", "innerHTML", "contentWindow", "console", "error", "frameDoc", "write", "close", "onload", "print", "<PERSON><PERSON><PERSON><PERSON>", "hideDialog", "e"], "mappings": "u7CA4BA,MAAMA,EAAQC,EAmBRC,EAAQC,EAMRC,EAAaC,OAAOD,YAEpBE,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAOC,EAAI,CACfC,iBAAiB,EACjBC,UAAW,UAEPC,EAAUH,GAAI,GACdI,EAAYC,EAAS,CACzBC,IAAM,IACGjB,EAAMkB,WAEf,GAAAC,CAAIC,GACFlB,EAAM,oBAAqBkB,EAAG,IAIlCC,GAAUC,gBACFC,QAAQC,IAAI,CAACC,IAAkBC,KAAe,IAGtD,MAAMC,EAAShB,EAAIiB,EAAYC,IAAIC,YACnCR,eAAeG,IACbM,EACGN,eAAe,CACdO,MAAOxB,EAAUwB,MACjBC,MAAOzB,EAAUyB,MACjBC,SAAUC,EAASC,gBAEpBC,MAAMC,IACY,IAAbA,EAAIC,OACCZ,EAAAa,MAAQF,EAAI5B,KAAKiB,OACxBjB,EAAK8B,MAAM3B,UAAY4B,EAAiBxB,IAAIU,EAAOa,OAAK,GAE3D,CAIC,MAAAE,EAAc1B,GAAS,IAAMW,EAAOa,QAAUZ,EAAYC,IAAIC,aAG9Da,EAAchC,EAAI,MAExBW,eAAeI,IACbZ,EAAQ0B,OAAQ,EAChB,MAAMI,EAAS,CACbZ,MAAOxB,EAAUwB,MACjBC,MAAOzB,EAAUyB,MACjBY,QAAS7C,EAAM6C,SAIb7C,EAAM8C,UACRF,EAAOE,QAAU9C,EAAM8C,SAIrB9C,EAAM+C,KAAO/C,EAAM+C,IAAM,IAC3BH,EAAOG,IAAM/C,EAAM+C,KAGrBC,EAAaC,iBAAiBL,GAAQP,MAAMC,IACzB,IAAbA,EAAIC,OACFG,EAAYF,OAEdG,EAAYH,MAAQF,EAAI5B,KACxBI,EAAQ0B,OAAQ,EAGZxC,EAAMkD,eACRC,GAAS,KACPC,YAAW,KACGC,IAAA,GACX,IAAG,KAYlB/B,eAAuBgC,GAErB,GAAIZ,EAAYF,MACd,OAGF,MAAMe,EAAc,IAAoDC,IAAA,4xBAAAC,KAAAC,WAClEtD,EAAWuD,KAAKC,WAAWC,aAAaN,GAC9C,MAAMO,EAAS,IAAI1D,EAAW2D,OAAOC,UAEjCrC,EAAOa,QAAUZ,EAAYqC,GAAGnC,WAE1BgC,EAAAI,SAAS,sGAAsGC,KAC9GxC,EAAOa,QAAUZ,EAAYwC,KAAKtC,WAEnCgC,EAAAI,SAAS,0GAA0GC,KAClHxC,EAAOa,QAAUZ,EAAYyC,KAAKvC,WACpCgC,EAAAI,SAAS,6CAKTJ,EAAAI,SAAS,yCAMlB,MAAMI,EAAgBC,IAEhBC,EAAmB,GADDF,EAAcG,WAEtCrE,EAAWuD,KAAKe,aAAaC,gBAAgBC,oBAAoBJ,GAEjE,MAAMK,EAAU,IAAIzE,EAAW0E,OAAOC,KAAKC,QAAQ,QACnDH,EAAQI,SAASC,KAAKC,UAAU7B,IACzBQ,EAAAsB,WAAWC,UAAUC,QACrBxB,EAAAyB,QAAQ,OAAQ,OAAQV,GAE/B,MAAMW,EAAgB,IAAIpF,EAAWqF,OAAOC,iBAC5CF,EAAcG,QAAQC,SAAU,EAChC,MAAMC,EAAS,IAAIzF,EAAWqF,OAAOK,UAAUN,EAAe,aAAa,GAC3EK,EAAO/B,OAASA,EAChB+B,EAAOE,WAAW,UACDC,EAAAlC,EACjBhD,EAAQ0B,OAAQ,EAGZxC,EAAMkD,eACRE,YAAW,KACGC,IAAA,GACX,IACL,CA1DM4C,CAAQ3D,EAAI5B,MACd,GAEH,CAEG,MAAAyD,EAAY+B,KAAKC,MACnB,IAAAH,EAuDJ,SAASI,IACPrF,EAAUyB,OAAQ,CAAA,CAGpB,SAASa,WAIP,GAFAnD,EAAM,iBAEFwC,EAAYF,MAAO,CAEf,MAAA6D,EAAaC,SAASC,cAAc,UAC1CF,EAAWG,MAAMC,SAAW,QAC5BJ,EAAWG,MAAME,MAAQ,IACzBL,EAAWG,MAAMG,OAAS,IAC1BN,EAAWG,MAAMI,MAAQ,IACzBP,EAAWG,MAAMK,OAAS,IAC1BR,EAAWG,MAAMM,OAAS,IAEjBR,SAAAS,KAAKC,YAAYX,GAC1B,MAAMY,GAAe,OAAAC,EAASZ,SAAAa,cAAc,+BAAsBC,YAAa,GAEzEC,EAAgBhB,EAAWgB,cACjC,IAAKA,EAEH,YADAC,QAAQC,MAAM,YAIhB,MAAMC,EAAWH,EAAcf,SAC/BkB,EAASC,MAAM,kuFAyGLR,iDAIVO,EAASE,QAGTrB,EAAWsB,OAAS,KACd,IAEFtB,EAAWgB,cAAcO,QAGzBxE,YAAW,KACAkD,SAAAS,KAAKc,YAAYxB,GAEtBrG,EAAM8H,aACR/G,EAAUyB,OAAQ,EAAA,GAEnB,WACIuF,GACCT,QAAAC,MAAM,QAASQ,EAAC,EAE5B,MAGgB,MAAA/B,GAAAA,EAAA4B,QAEZ5H,EAAM8H,YACR1E,YAAW,KACTrC,EAAUyB,OAAQ,CAAA,GACjB,IAEP"}