# 优惠券布局调整 - 文字右对齐

## 功能说明
调整优惠券项的布局，确保不可用原因和"满多少可用"的文字都右对齐显示。

## 修改文件
- `src/views/room/components/leavePrice/components/CouponDialog.vue`

## 已完成的修改

### 1. ✅ 不可用原因文字右对齐
为 `.unavailable-text` 样式添加了 `text-align: right`：

```scss
.unavailable-text {
  font-size: 12px;
  color: #f56c6c;
  font-weight: bold;
  text-align: right; // 新添加
}
```

### 2. ✅ "满多少可用"文字右对齐
为 `.min-amount` 样式添加了 `text-align: right`：

```scss
.min-amount {
  color: #e6a23c;
  text-align: right; // 新添加
}
```

## 布局结构

### 当前优惠券项布局
```
┌─────────────────────────────────────────────────────────┐
│ 优惠券名称                                    ¥20/8折/免房 │
├─────────────────────────────────────────────────────────┤
│ 券代码：1234567890                           代金券        │
├─────────────────────────────────────────────────────────┤
│ 券号：0987654321                    该优惠券不适用该房型   │ ← 右对齐
├─────────────────────────────────────────────────────────┤
│ 优惠券描述信息...                                        │
├─────────────────────────────────────────────────────────┤
│ 有效期：2025-01-01 ~ 2025-12-31           满 ¥100 可用   │ ← 右对齐
└─────────────────────────────────────────────────────────┘
```

### 关键布局特点
1. **券号行**：左侧显示券号，右侧显示不可用原因（如果有）
2. **有效期行**：左侧显示有效期，右侧显示使用门槛（如果有）
3. **右对齐**：所有右侧文字都采用右对齐显示

## 实现效果

### 不可用原因显示
- 位置：券号行右侧
- 样式：红色文字，加粗，右对齐
- 内容：根据不可用原因动态显示（如"该优惠券不适用该房型"）

### 使用门槛显示
- 位置：有效期行右侧
- 样式：橙色文字，右对齐
- 内容：显示最低使用金额（如"满 ¥100 可用"）

### 布局一致性
- 使用 `justify-content: space-between` 确保左右分布
- 使用 `text-align: right` 确保右侧文字右对齐
- 保持与现有设计风格一致

## 技术实现细节

### CSS Flexbox 布局
```scss
.coupon-info-row, .coupon-info {
  display: flex;
  justify-content: space-between; // 左右分布
  align-items: center;
}
```

### 文字对齐
```scss
.unavailable-text, .min-amount {
  text-align: right; // 右对齐
}
```

### 颜色区分
- 不可用原因：`#f56c6c`（红色）
- 使用门槛：`#e6a23c`（橙色）

## 状态：✅ 调整完成
优惠券项的布局已调整完成，不可用原因和使用门槛文字都已右对齐显示。
