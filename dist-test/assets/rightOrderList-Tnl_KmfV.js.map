{"version": 3, "file": "rightOrderList-Tnl_KmfV.js", "sources": ["../../src/views/room/realtime/components/rightOrderList.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"viewDetails\":\"Details\",\r\n    \"more\":\"More\",\r\n    \"allStatus\": \"All\",\r\n    \"checkinDate\": \"Check-in\",\r\n    \"todayCheckin\": \"Today\",\r\n    \"nextSevenDays\": \"Next 7D\",\r\n    \"searchPlaceholder\": \"Name/Phone/Order\",\r\n    \"noData\": \"No data\",\r\n    \"nights\": \"N\",\r\n    \"rooms\": \"R\",\r\n    \"operationTips\": \"Tips: Click to select order, Double-click to open detail dialog\",\r\n    \"clickToSelect\": \"Click to select\",\r\n    \"doubleClickToViewDialog\": \"Double-click to open detail dialog\",\r\n    \"channels\": {\r\n      \"tiktok\": \"TikTok\",\r\n      \"fliggy\": \"Fliggy\",\r\n      \"meituan\": \"<PERSON>tu<PERSON>\",\r\n      \"lobby\": \"Lobby\",\r\n      \"elong\": \"Elong\",\r\n      \"qunar\": \"Qunar\",\r\n      \"ctrip\": \"Ctrip\",\r\n      \"miniApp\": \"Mini App\",\r\n      \"jd\": \"JD\"\r\n    },\r\n    \"options\":{\r\n      \"all\": \"All\",\r\n      \"lobby\": \"<PERSON>bby\",\r\n      \"meituan\": \"Meituan\",\r\n      \"ctrip\": \"Ctrip\",\r\n      \"fliggy\": \"Fliggy\",\r\n      \"tiktok\": \"TikTok\",\r\n      \"xiaohongshu\": \"XHS\",\r\n      \"jd\": \"JD\"\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"viewDetails\":\"详情\",\r\n    \"more\":\"更多\",\r\n    \"allStatus\": \"全部\",\r\n    \"checkinDate\": \"全部日期\",\r\n    \"todayCheckin\": \"今日\",\r\n    \"nextSevenDays\": \"未来7天\",\r\n    \"searchPlaceholder\": \"姓名/手机/订单号\",\r\n    \"noData\": \"暂无数据\",\r\n    \"nights\": \"晚\",\r\n    \"rooms\": \"间\",\r\n    \"operationTips\": \"提示：单击选中订单，双击打开详情\",\r\n    \"clickToSelect\": \"单击选中\",\r\n    \"doubleClickToViewDialog\": \"双击打开详情\",\r\n    \"channels\": {\r\n      \"tiktok\": \"抖音\",\r\n      \"fliggy\": \"飞猪\",\r\n      \"meituan\": \"美团\",\r\n      \"lobby\": \"门店\",\r\n      \"elong\": \"艺龙\",\r\n      \"qunar\": \"去哪儿\",\r\n      \"ctrip\": \"携程\",\r\n      \"miniApp\": \"小程序\",\r\n      \"jd\": \"京东\"\r\n    },\r\n    \"options\":{\r\n      \"all\": \"全部\",\r\n      \"lobby\": \"门店\",\r\n      \"meituan\": \"美团\",\r\n      \"ctrip\": \"携程\",\r\n      \"fliggy\": \"飞猪\",\r\n      \"tiktok\": \"抖音\",\r\n      \"xiaohongshu\": \"小红书\",\r\n      \"jd\": \"京东\"\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"viewDetails\":\"ព័ត៌មានលម្អិត\",\r\n    \"more\":\"ច្រើនទៀត\",\r\n    \"allStatus\": \"ទាំងអស់\",\r\n    \"checkinDate\": \"កាលបរិច្ឆេទចូល\",\r\n    \"todayCheckin\": \"ថ្ងៃនេះ\",\r\n    \"nextSevenDays\": \"៧ថ្ងៃបន្ទាប់\",\r\n    \"searchPlaceholder\": \"ឈ្មោះ/ទូរស័ព្ទ/លេខកម្មង់\",\r\n    \"noData\": \"គ្មានទិន្នន័យ\",\r\n    \"nights\": \"យប់\",\r\n    \"rooms\": \"បន្ទប់\",\r\n    \"operationTips\": \"ជំនួយ: ចុចដើម្បីជ្រើសរើសកម្មង់, ចុចពីរដងដើម្បីបើកផ្ទាំងព័ត៌មានលម្អិត\",\r\n    \"clickToSelect\": \"ចុចដើម្បីជ្រើសរើស\",\r\n    \"doubleClickToViewDialog\": \"ចុចពីរដងដើម្បីបើកផ្ទាំងព័ត៌មានលម្អិត\",\r\n    \"channels\": {\r\n      \"tiktok\": \"TikTok\",\r\n      \"fliggy\": \"Fliggy\",\r\n      \"meituan\": \"Meituan\",\r\n      \"lobby\": \"ហាង\",\r\n      \"elong\": \"Elong\",\r\n      \"qunar\": \"Qunar\",\r\n      \"ctrip\": \"Ctrip\",\r\n      \"miniApp\": \"កម្មវិធីតូច\",\r\n      \"jd\": \"JD\"\r\n    },\r\n    \"options\":{\r\n      \"all\": \"ទាំងអស់\",\r\n      \"lobby\": \"ហាង\",\r\n      \"meituan\": \"Meituan\",\r\n      \"ctrip\": \"Ctrip\",\r\n      \"fliggy\": \"Fliggy\",\r\n      \"tiktok\": \"TikTok\",\r\n      \"xiaohongshu\": \"XHS\",\r\n      \"jd\": \"JD\"\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { BookModel } from '@/models'\r\nimport type roomOrderList from './roomOrderList'\r\nimport { bookApi, dictDataApi } from '@/api/modules/index'\r\nimport { DictTypeEnum, NoType, OrderState, OrderType } from '@/models'\r\nimport { HotelMessageTypeEnum } from '@/models/dict/constants'\r\nimport useUserStore from '@/store/modules/user'\r\nimport OrderDialog from '@/views/order/info/order.vue'\r\nimport { Mouse, Search } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\nimport eventBus from '../event-bus'\r\n\r\ndefineOptions({\r\n  name: 'RoomOrderList', // ota订单列表\r\n})\r\n\r\n/**\r\n * @param 发射给父组件的方法\r\n * @param 用于子组件给父组件传值或调用父组件方法\r\n */\r\nconst { t } = useI18n()\r\n/** 获取缓存数据 */\r\nconst userStore = useUserStore()\r\n/** ota名称 */\r\nconst otaName = ref('')\r\nconst otaOptions = computed<roomOrderList.otaOptions[]>(() => [\r\n  {\r\n    value: '-1',\r\n    label: t('options.all'),\r\n  },\r\n  {\r\n    value: 'lobby',\r\n    label: t('options.lobby'),\r\n  },\r\n  {\r\n    value: 'ctrip',\r\n    label: t('options.ctrip'),\r\n  },\r\n  {\r\n    value: 'meituan',\r\n    label: t('options.meituan'),\r\n  },\r\n  {\r\n    value: 'fliggy',\r\n    label: t('options.fliggy'),\r\n  },\r\n  {\r\n    value: 'tiktok',\r\n    label: t('options.tiktok'),\r\n  },\r\n  {\r\n    value: 'jd',\r\n    label: t('options.jd'),\r\n  },\r\n  {\r\n    value: 'xiaohongshu',\r\n    label: t('options.xiaohongshu'),\r\n  },\r\n])\r\nconst routerName = ref('detail')\r\nconst typeName = ref('individual')\r\n\r\n// 添加选中状态管理\r\nconst selectedOrderId = ref<string | null>(null)\r\nlet clickTimer: NodeJS.Timeout | null = null\r\n\r\n// 添加提示显示状态\r\nconst showOperationTips = ref(true)\r\n\r\n/**\r\n * 处理订单卡片点击事件\r\n * @param item 订单项\r\n * @param event 点击事件\r\n */\r\nfunction handleOrderClick(item: BookModel, event: Event) {\r\n  // 检查是否有文本被选中\r\n  const selection = window.getSelection()\r\n  if (selection && selection.toString().length > 0) {\r\n    // 如果有文本被选中，不阻止默认行为，允许复制\r\n    return\r\n  }\r\n\r\n  // 检查点击的目标是否是可选择的文本元素\r\n  const target = event.target as HTMLElement\r\n  if (target.tagName === 'SPAN' || target.classList.contains('selectable-text')) {\r\n    // 如果点击的是文本元素，不阻止默认行为\r\n    return\r\n  }\r\n\r\n  event.preventDefault()\r\n\r\n  // 清除之前的定时器\r\n  if (clickTimer) {\r\n    clearTimeout(clickTimer)\r\n    clickTimer = null\r\n  }\r\n\r\n  // 设置延迟执行单击事件，以区分单击和双击\r\n  clickTimer = setTimeout(() => {\r\n    // 单击事件：选中/取消选中订单\r\n    if (selectedOrderId.value === item.bookNo) {\r\n      selectedOrderId.value = null // 取消选中\r\n    } else {\r\n      selectedOrderId.value = item.bookNo // 选中当前订单\r\n    }\r\n  }, 200) // 200ms延迟\r\n}\r\n\r\n/**\r\n * 处理订单卡片双击事件\r\n * @param item 订单项\r\n * @param event 双击事件\r\n */\r\nfunction handleOrderDoubleClick(item: BookModel, event: Event) {\r\n  event.preventDefault()\r\n\r\n  // 清除单击定时器\r\n  if (clickTimer) {\r\n    clearTimeout(clickTimer)\r\n    clickTimer = null\r\n  }\r\n\r\n  // 双击事件：进入订单详情\r\n  detailsClick(item)\r\n}\r\n\r\n/**\r\n * 检查订单是否被选中\r\n * @param item 订单项\r\n */\r\nfunction isOrderSelected(item: BookModel): boolean {\r\n  return selectedOrderId.value === item.bookNo\r\n}\r\n\r\n// 关闭提示\r\nfunction closeTips() {\r\n  showOperationTips.value = false\r\n  localStorage.setItem('order-operation-tips-seen', 'true')\r\n}\r\n\r\nonMounted(() => {\r\n  otaName.value = otaOptions.value[0].value\r\n  getConstants()\r\n  getDataList()\r\n\r\n  // 检查本地存储，如果用户已经看过提示就不再显示\r\n  const hasSeenTips = localStorage.getItem('order-operation-tips-seen')\r\n  if (hasSeenTips) {\r\n    showOperationTips.value = false\r\n  }\r\n\r\n  // 监听 SSE 消息\r\n  eventBus.on('sse_message_ota_order', (data: any) => {\r\n    handleOtaOrderMessage(data)\r\n  })\r\n  eventBus.on('sse_message_ota_order_cancel', (data: any) => {\r\n    handleOtaOrderCancelMessage(data)\r\n  })\r\n})\r\n\r\nonUnmounted(() => {\r\n  eventBus.off('sse_message_ota_order')\r\n  eventBus.off('sse_message_ota_order_cancel')\r\n})\r\n\r\nconst { pagination, getParams, onSizeChange, onCurrentChange } = usePagination()\r\n\r\nconst dictTypes = [DictTypeEnum.BOOK_STATUS]\r\nconst orderStatus = ref<{ code: string; label: string }[]>([])\r\nfunction getConstants() {\r\n  const list = [OrderState.OVER, OrderState.CANCEL, OrderState.NOSHOW, OrderState.IN_BOOKING]\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    orderStatus.value = res.data.filter((item: any) => item.dictType === DictTypeEnum.BOOK_STATUS && list.includes(item.code))\r\n  })\r\n}\r\nconst data = ref({\r\n  loading: false,\r\n  tableAutoHeight: false,\r\n  formModeProps: {\r\n    visible: false,\r\n    bookNo: '',\r\n    noType: NoType.BOOK,\r\n  },\r\n  search: {\r\n    state: OrderState.IN_BOOKING.toString(),\r\n    channelCode: '-1',\r\n    searchContent: '',\r\n    // 时间类型\r\n    dateType: '-1',\r\n  },\r\n  dataList: [] as BookModel[],\r\n  newOrderIds: new Set<string>(),\r\n})\r\nconst queryParams = reactive<hgCode>({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n})\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    ...queryParams,\r\n    channelCode: data.value.search.channelCode === '-1' ? '' : data.value.search.channelCode,\r\n    keyWords: data.value.search.searchContent,\r\n    timeType: '2',\r\n    state: data.value.search.state === '-1' ? '' : data.value.search.state,\r\n  }\r\n  // 根据dateType设置日期过滤参数\r\n  if (data.value.search.dateType === '0') {\r\n    // 今天\r\n    const today = dayjs().format('YYYY-MM-DD')\r\n    params.startTime = `${today} 00:00:00`\r\n    params.endTime = `${today} 23:59:59`\r\n  } else if (data.value.search.dateType === '1') {\r\n    // 今天到未来7天\r\n    const today = dayjs().format('YYYY-MM-DD')\r\n    const future7 = dayjs().add(7, 'day').format('YYYY-MM-DD')\r\n    params.startTime = `${today} 00:00:00`\r\n    params.endTime = `${future7} 23:59:59`\r\n  }\r\n  bookApi.bookPageList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data.list\r\n    pagination.value.total = res.data.total\r\n  })\r\n}\r\n\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n/** 酒店图标 */\r\nconst channelImageList = computed(() => [\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/tiktok.png',\r\n    channelCode: 'tiktok',\r\n    channelName: t('channels.tiktok'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/fliggy.png',\r\n    channelCode: 'fliggy',\r\n    channelName: t('channels.fliggy'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/meituan.png',\r\n    channelCode: 'meituan',\r\n    channelName: t('channels.meituan'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/store.png',\r\n    channelCode: 'lobby',\r\n    channelName: t('channels.lobby'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/elong.png',\r\n    channelCode: 'elong',\r\n    channelName: t('channels.elong'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/quar.png',\r\n    channelCode: 'qunar',\r\n    channelName: t('channels.qunar'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/ctrip.png',\r\n    channelCode: 'ctrip',\r\n    channelName: t('channels.ctrip'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/logo.png',\r\n    channelCode: 'mini_app',\r\n    channelName: t('channels.miniApp'),\r\n  },\r\n  {\r\n    image: 'https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/jd.png',\r\n    channelCode: 'jd',\r\n    channelName: t('channels.jd'),\r\n  },\r\n])\r\nfunction getChannelImage(code) {\r\n  const channel = channelImageList.value.find((item: any) => item.channelCode === code)\r\n  return channel?.image\r\n}\r\n\r\n/**\r\n * 查看详情\r\n * @param rows 当前行内容\r\n */\r\nasync function detailsClick(row: any) {\r\n  data.value.formModeProps.bookNo = row.bookNo\r\n  data.value.formModeProps.visible = true\r\n  // 如果是团队预订单，那么跳转到团队预订单详情页\r\n  if (row.bookType === OrderType.GROUP) {\r\n    data.value.formModeProps.noType = NoType.TEAM\r\n  } else {\r\n    data.value.formModeProps.noType = NoType.BOOK\r\n  }\r\n}\r\n\r\n// 监听 SSE 消息\r\nfunction handleOtaOrderMessage(data: any) {\r\n  switch (data.messageType) {\r\n    case HotelMessageTypeEnum.OTA_ORDER: // OTA订单\r\n      addOtaOrder(data)\r\n      break\r\n    default:\r\n      console.warn('未知消息类型:', data.messageType)\r\n  }\r\n}\r\n\r\nfunction handleOtaOrderCancelMessage(data: any) {\r\n  switch (data.messageType) {\r\n    case HotelMessageTypeEnum.OTA_ORDER_CANCEL: // OTA订单取消\r\n      cancelOtaOrder(data)\r\n      break\r\n    default:\r\n      console.warn('未知消息类型:', data.messageType)\r\n  }\r\n}\r\ninterface Message {\r\n  clientId: string\r\n  messageType?: string\r\n  data: MesData\r\n}\r\ninterface MesData {\r\n  type: string\r\n  message: any\r\n}\r\nfunction addOtaOrder(clientDataStr: any) {\r\n  let clientData: Message\r\n  try {\r\n    // 检查 clientDataStr 是否是字符串\r\n    if (typeof clientDataStr === 'string') {\r\n      clientData = JSON.parse(clientDataStr) // 如果是字符串，解析为对象\r\n    } else {\r\n      clientData = clientDataStr // 如果已经是对象，直接使用\r\n    }\r\n  } catch (error) {\r\n    console.error('Error parsing clientData:', error)\r\n    return\r\n  }\r\n\r\n  // 将新订单添加到dataList的最前面\r\n  if (clientData && clientData.clientId) {\r\n    let d = clientData.data\r\n    if (typeof d === 'string') {\r\n      d = JSON.parse(d)\r\n    }\r\n\r\n    // 为新订单设置默认状态为no_check_in\r\n    const newOrder = {\r\n      ...d,\r\n      state: d.state || 'no_check_in',\r\n      contact: d.guestName,\r\n      roomTypeClass: [{ rtCode: d.rtCode, rtName: d.rtName, num: d.roomNum }],\r\n      createTime: new Date(), // 添加创建时间\r\n    }\r\n\r\n    // 添加到新订单标识集合\r\n    data.value.newOrderIds.add(newOrder.bookNo)\r\n\r\n    // 设置定时器，5分钟后移除新订单标识\r\n    setTimeout(() => {\r\n      data.value.newOrderIds.delete(newOrder.bookNo)\r\n    }, 5 * 60 * 1000) // 5分钟\r\n\r\n    data.value.dataList.unshift(newOrder)\r\n  } else {\r\n    console.warn('clientData 或 clientId 无效:', clientData)\r\n  }\r\n}\r\n\r\n// 判断是否为新订单\r\nfunction isNewOrder(item: BookModel): boolean {\r\n  return data.value.newOrderIds.has(item.bookNo)\r\n}\r\n\r\nfunction cancelOtaOrder(clientDataStr: any) {\r\n  let clientData: Message\r\n  try {\r\n    // 检查 clientDataStr 是否是字符串\r\n    if (typeof clientDataStr === 'string') {\r\n      clientData = JSON.parse(clientDataStr) // 如果是字符串，解析为对象\r\n    } else {\r\n      clientData = clientDataStr // 如果已经是对象，直接使用\r\n    }\r\n  } catch (error) {\r\n    console.error('Error parsing clientData:', error)\r\n    return\r\n  }\r\n\r\n  // 循环dataList，找到相同订单号的订单并从列表中移除\r\n  if (clientData && clientData.clientId) {\r\n    // 修复：正确解析订单数据\r\n    let orderData = clientData.data\r\n    if (typeof orderData === 'string') {\r\n      try {\r\n        orderData = JSON.parse(orderData)\r\n      } catch (error) {\r\n        console.error('Error parsing orderData:', error)\r\n        return\r\n      }\r\n    }\r\n\r\n    // 修复：从解析后的数据中获取订单号\r\n    const targetBookNo = orderData.bookNo || orderData.message?.bookNo\r\n\r\n    if (!targetBookNo) {\r\n      console.warn('无法获取订单号，orderData:', orderData)\r\n      return\r\n    }\r\n\r\n    const orderIndex = data.value.dataList.findIndex((order) => order.bookNo === targetBookNo)\r\n    if (orderIndex !== -1) {\r\n      // 从列表中移除订单\r\n      data.value.dataList.splice(orderIndex, 1)\r\n      // 同时从新订单标识中移除（如果存在）\r\n      data.value.newOrderIds.delete(targetBookNo)\r\n    }\r\n  } else {\r\n    console.warn('clientData 或 clientId 无效:', clientData)\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div class=\"order-list-container\">\r\n    <!-- 操作提示 -->\r\n    <div v-if=\"showOperationTips\" class=\"operation-tips\">\r\n      <el-alert :title=\"t('operationTips')\" type=\"info\" :closable=\"true\" show-icon @close=\"closeTips\" />\r\n    </div>\r\n\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-area\">\r\n      <el-form :model=\"data.search\" class=\"search-form\">\r\n        <div class=\"channel-selector\">\r\n          <el-radio-group v-model=\"data.search.channelCode\" size=\"small\" @change=\"getDataList()\">\r\n            <el-radio v-for=\"(item, index) in otaOptions\" :key=\"index\" :value=\"item.value\">\r\n              {{ item.label }}\r\n            </el-radio>\r\n          </el-radio-group>\r\n        </div>\r\n        <div class=\"filter-group\">\r\n          <el-select v-model=\"data.search.state\" size=\"small\" @change=\"getDataList()\">\r\n            <el-option :label=\"t('allStatus')\" value=\"-1\">\r\n              {{ t('allStatus') }}\r\n            </el-option>\r\n            <el-option v-for=\"(item, index) in orderStatus\" :key=\"index\" :label=\"item.label\" :value=\"item.code\" />\r\n          </el-select>\r\n          <el-select v-model=\"data.search.dateType\" size=\"small\" @change=\"getDataList()\">\r\n            <el-option :label=\"t('checkinDate')\" value=\"-1\" />\r\n            <el-option :label=\"t('todayCheckin')\" value=\"0\" />\r\n            <el-option :label=\"t('nextSevenDays')\" value=\"1\" />\r\n          </el-select>\r\n          <el-input v-model=\"data.search.searchContent\" :placeholder=\"t('searchPlaceholder')\" size=\"small\" clearable @clear=\"getDataList()\">\r\n            <template #append>\r\n              <el-button :icon=\"Search\" size=\"small\" @click=\"getDataList()\" />\r\n            </template>\r\n          </el-input>\r\n        </div>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 订单列表 -->\r\n    <div class=\"order-list-wrapper\">\r\n      <div class=\"order-list\">\r\n        <el-skeleton v-if=\"data.loading\" :rows=\"10\" animated />\r\n        <el-empty v-else-if=\"data.dataList.length === 0\" :description=\"t('noData')\" :image-size=\"100\" />\r\n        <div v-for=\"(item, index) in data.dataList\" v-else :key=\"index\" class=\"order-card\" :class=\"{ 'order-card--selected': isOrderSelected(item) }\" @click=\"handleOrderClick(item, $event)\" @dblclick=\"handleOrderDoubleClick(item, $event)\">\r\n          <!-- 操作提示图标 -->\r\n          <div v-if=\"!isOrderSelected(item)\" class=\"operation-hint\">\r\n            <el-icon class=\"click-icon\">\r\n              <Mouse />\r\n            </el-icon>\r\n          </div>\r\n\r\n          <!-- 新订单标识 -->\r\n          <div v-if=\"isNewOrder(item)\" class=\"new-order-badge\">\r\n            <el-tag type=\"danger\" effect=\"dark\" size=\"small\"> 新订单 </el-tag>\r\n          </div>\r\n\r\n          <div class=\"order-header\">\r\n            <div class=\"order-status\">\r\n              <el-tag :type=\"item.state === 'no_check_in' ? 'primary' : 'info'\" effect=\"light\" size=\"small\">\r\n                {{ orderStatus.find((status) => status.code === item.state)?.label || item.state }}\r\n              </el-tag>\r\n            </div>\r\n            <div class=\"order-number selectable-text\">\r\n              {{ item.outOrderNo ? item.outOrderNo : item.bookNo }}\r\n            </div>\r\n            <div v-if=\"getChannelImage(item.channelCode)\" class=\"order-channel\">\r\n              <el-image v-if=\"getChannelImage(item.channelCode)\" class=\"channel-icon\" :src=\"getChannelImage(item.channelCode)\" fit=\"cover\" />\r\n            </div>\r\n            <div class=\"check-in-time selectable-text\">\r\n              {{ dayjs(item.planCheckinTime).format('MM/DD HH:mm') }}\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"order-content\">\r\n            <div class=\"room-info\">\r\n              <div class=\"room-count-list\">\r\n                <div v-for=\"(i, inx) in item.roomTypeClass || []\" :key=\"inx\" class=\"room-count-item selectable-text\">\r\n                  <span class=\"room-num\">{{ i.num }}</span\r\n                  >{{ t('rooms') }}\r\n                </div>\r\n              </div>\r\n              <div class=\"room-type-list\">\r\n                <div v-for=\"(roomType, roomIndex) in item.roomTypeClass\" :key=\"roomIndex\" class=\"room-type-item selectable-text\">\r\n                  <el-tooltip v-if=\"roomType.rtName && roomType.rtName.length > 23\" effect=\"dark\" placement=\"top-start\" :content=\"roomType.rtName\">\r\n                    <span class=\"selectable-text\">{{ roomType.rtName }}</span>\r\n                  </el-tooltip>\r\n                  <template v-else>\r\n                    {{ roomType.rtName }}\r\n                  </template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"guest-info\">\r\n              <div class=\"guest-name selectable-text\">\r\n                {{ item.contact }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"order-footer\">\r\n            <div class=\"stay-duration selectable-text\">\r\n              <template v-if=\"dayjs(item.planCheckinTime).format('YYYY-MM-DD') !== dayjs(item.planCheckoutTime).format('YYYY-MM-DD')\"> {{ dayjs(item.planCheckoutTime).diff(dayjs(item.planCheckinTime), 'day') + 1 }} {{ t('nights') }} </template>\r\n            </div>\r\n            <div class=\"stay-dates selectable-text\">{{ dayjs(item.planCheckinTime).format('MM/DD') }} - {{ dayjs(item.planCheckoutTime).format('MM/DD') }}</div>\r\n            <div class=\"view-details\">\r\n              <el-button type=\"primary\" size=\"small\" link @click.stop=\"detailsClick(item)\">\r\n                {{ t('viewDetails') }}\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"pagination-container\">\r\n      <!-- 分页 -->\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10 && !data.loading\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        layout=\"prev, pager, next\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        size=\"small\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 订单详情弹窗 -->\r\n    <OrderDialog v-if=\"data.formModeProps.visible\" v-model=\"data.formModeProps.visible\" :no=\"data.formModeProps.bookNo\" :no-type=\"data.formModeProps.noType\" :tab-name=\"routerName\" :tab-type=\"typeName\" @success=\"getDataList\" />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.order-list-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  background-color: #f5f7fa;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.operation-tips {\r\n  margin-bottom: 12px;\r\n  z-index: 10;\r\n}\r\n\r\n.search-area {\r\n  padding: 10px 16px;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.channel-selector {\r\n  margin-bottom: 4px;\r\n\r\n  :deep(.el-radio-group) {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  :deep(.el-radio) {\r\n    margin-right: 1px;\r\n    margin-bottom: 4px;\r\n    border-radius: 6px;\r\n    padding: 4px 2px;\r\n    border: 1px solid #e4e7ed;\r\n    white-space: nowrap;\r\n\r\n    &.is-checked {\r\n      background-color: #f0f5ff;\r\n      border-color: #409eff;\r\n    }\r\n  }\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n\r\n  .el-select {\r\n    width: auto;\r\n    min-width: 120px;\r\n  }\r\n\r\n  .el-input {\r\n    width: auto;\r\n    min-width: 200px;\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n.order-list-wrapper {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0 4px;\r\n}\r\n\r\n.order-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 0 4px;\r\n}\r\n\r\n.selectable-text {\r\n  user-select: text !important;\r\n  -webkit-user-select: text !important;\r\n  -moz-user-select: text !important;\r\n  -ms-user-select: text !important;\r\n  cursor: text;\r\n\r\n  &:hover {\r\n    background-color: rgba(64, 158, 255, 0.1);\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.order-card {\r\n  background-color: #fff;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  border: 2px solid transparent;\r\n  position: relative;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\r\n\r\n    .operation-hint {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  &--selected {\r\n    border-color: #554dd6;\r\n    background-color: #f0f5ff;\r\n    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.2);\r\n    }\r\n  }\r\n\r\n  // 新订单样式\r\n  &--new {\r\n    border-color: #f56c6c;\r\n    background: linear-gradient(135deg, #fff 0%, #fef0f0 100%);\r\n    animation: newOrderPulse 2s ease-in-out infinite;\r\n\r\n    &:hover {\r\n      box-shadow: 0 4px 16px rgba(245, 108, 108, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n// 新订单标识\r\n.new-order-badge {\r\n  position: absolute;\r\n  top: 0px;\r\n  right: 0px;\r\n  z-index: 10;\r\n}\r\n\r\n// 新订单脉动动画\r\n@keyframes newOrderPulse {\r\n  0%,\r\n  100% {\r\n    box-shadow: 0 2px 12px rgba(245, 108, 108, 0.2);\r\n  }\r\n  50% {\r\n    box-shadow: 0 4px 20px rgba(245, 108, 108, 0.4);\r\n  }\r\n}\r\n\r\n.operation-hint {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  z-index: 2;\r\n\r\n  .click-icon {\r\n    color: #909399;\r\n    font-size: 16px;\r\n    animation: pulse 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    opacity: 0.6;\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\n.order-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 6px;\r\n  padding-bottom: 6px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n\r\n  .order-status {\r\n    margin-right: 6px;\r\n  }\r\n\r\n  .order-number {\r\n    flex: 1;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .order-channel {\r\n    margin: 0 6px;\r\n  }\r\n\r\n  .channel-icon {\r\n    width: 18px;\r\n    height: 18px;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .check-in-time {\r\n    color: #606266;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n.order-content {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n\r\n  .room-info {\r\n    flex: 1;\r\n    display: flex;\r\n  }\r\n\r\n  .room-count-list {\r\n    width: 60px;\r\n\r\n    .room-count-item {\r\n      margin-bottom: 8px;\r\n      color: #606266;\r\n      font-size: 13px;\r\n\r\n      .room-num {\r\n        font-weight: 600;\r\n        color: #303133;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .room-type-list {\r\n    flex: 1;\r\n\r\n    .room-type-item {\r\n      margin-bottom: 8px;\r\n      color: #303133;\r\n      font-size: 13px;\r\n      @include text-overflow(1);\r\n    }\r\n  }\r\n\r\n  .guest-info {\r\n    width: 80px;\r\n    text-align: right;\r\n\r\n    .guest-name {\r\n      color: #303133;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.order-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  padding-top: 12px;\r\n  border-top: 1px solid #f0f2f5;\r\n\r\n  .stay-duration {\r\n    width: 60px;\r\n    color: #606266;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .stay-dates {\r\n    flex: 1;\r\n    color: #303133;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .view-details {\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding-bottom: 60px;\r\n  background-color: #fff;\r\n  border-top: 1px solid #f0f2f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  z-index: 1;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  padding: 8px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  z-index: 1;\r\n}\r\n\r\n:deep(.el-empty) {\r\n  margin: 60px 0;\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "otaName", "ref", "otaOptions", "computed", "value", "label", "routerName", "typeName", "selectedOrderId", "clickTimer", "showOperationTips", "isOrderSelected", "item", "bookNo", "closeTips", "localStorage", "setItem", "onMounted", "list", "OrderState", "OVER", "CANCEL", "NOSHOW", "IN_BOOKING", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "then", "res", "orderStatus", "data", "filter", "dictType", "DictTypeEnum", "BOOK_STATUS", "includes", "code", "getConstants", "getDataList", "getItem", "eventBus", "on", "messageType", "HotelMessageTypeEnum", "OTA_ORDER", "clientDataStr", "clientData", "JSON", "parse", "error", "console", "clientId", "d", "newOrder", "state", "contact", "<PERSON><PERSON><PERSON>", "roomTypeClass", "rtCode", "rtName", "num", "roomNum", "createTime", "Date", "newOrderIds", "add", "setTimeout", "delete", "dataList", "unshift", "warn", "addOtaOrder", "handleOtaOrderMessage", "OTA_ORDER_CANCEL", "orderData", "targetBookNo", "_a", "message", "orderIndex", "findIndex", "order", "splice", "cancelOtaOrder", "handleOtaOrderCancelMessage", "onUnmounted", "off", "pagination", "getParams", "onSizeChange", "onCurrentChange", "usePagination", "loading", "tableAutoHeight", "formModeProps", "visible", "noType", "NoType", "BOOK", "search", "toString", "channelCode", "searchContent", "dateType", "Set", "queryParams", "reactive", "gcode", "hcode", "params", "key<PERSON>ords", "timeType", "today", "dayjs", "format", "startTime", "endTime", "future7", "bookApi", "bookPageList", "total", "currentChange", "page", "channelImageList", "image", "channelName", "getChannelImage", "channel", "find", "async", "detailsClick", "row", "bookType", "OrderType", "GROUP", "TEAM", "isNewOrder", "has", "event", "selection", "window", "getSelection", "length", "target", "tagName", "classList", "contains", "preventDefault", "clearTimeout"], "mappings": "y+LAqIM,MAAAA,EAAEA,GAAMC,IAERC,GAAYC,IAEZC,GAAUC,EAAI,IACdC,GAAaC,GAAqC,IAAM,CAC5D,CACEC,MAAO,KACPC,MAAOT,EAAE,gBAEX,CACEQ,MAAO,QACPC,MAAOT,EAAE,kBAEX,CACEQ,MAAO,QACPC,MAAOT,EAAE,kBAEX,CACEQ,MAAO,UACPC,MAAOT,EAAE,oBAEX,CACEQ,MAAO,SACPC,MAAOT,EAAE,mBAEX,CACEQ,MAAO,SACPC,MAAOT,EAAE,mBAEX,CACEQ,MAAO,KACPC,MAAOT,EAAE,eAEX,CACEQ,MAAO,cACPC,MAAOT,EAAE,2BAGPU,GAAaL,EAAI,UACjBM,GAAWN,EAAI,cAGfO,GAAkBP,EAAmB,MAC3C,IAAIQ,GAAoC,KAGlC,MAAAC,GAAoBT,GAAI,GA+D9B,SAASU,GAAgBC,GAChB,OAAAJ,GAAgBJ,QAAUQ,EAAKC,MAAA,CAIxC,SAASC,KACPJ,GAAkBN,OAAQ,EACbW,aAAAC,QAAQ,4BAA6B,OAAM,CAG1DC,GAAU,KACRjB,GAAQI,MAAQF,GAAWE,MAAM,GAAGA,MA4BtC,WACQ,MAAAc,EAAO,CAACC,EAAWC,KAAMD,EAAWE,OAAQF,EAAWG,OAAQH,EAAWI,YAChFC,EAAYC,iBAAiBC,IAAWC,MAAMC,IAC5CC,GAAYzB,MAAQwB,EAAIE,KAAKC,QAAQnB,GAAcA,EAAKoB,WAAaC,EAAaC,aAAehB,EAAKiB,SAASvB,EAAKwB,OAAK,GAC1H,CA/BYC,GACDC,KAGQvB,aAAawB,QAAQ,+BAEvC7B,GAAkBN,OAAQ,GAInBoC,EAAAC,GAAG,yBAA0BX,KAmJxC,SAA+BA,GAC7B,GAAQA,EAAKY,cACNC,EAAqBC,WA0B9B,SAAqBC,GACf,IAAAC,EACA,IAGaA,EADc,iBAAlBD,EACIE,KAAKC,MAAMH,GAEXA,QAERI,GAEP,YADQC,QAAAD,MAAM,4BAA6BA,EAC3C,CAIE,GAAAH,GAAcA,EAAWK,SAAU,CACrC,IAAIC,EAAIN,EAAWhB,KACF,iBAANsB,IACLA,EAAAL,KAAKC,MAAMI,IAIjB,MAAMC,EAAW,IACZD,EACHE,MAAOF,EAAEE,OAAS,cAClBC,QAASH,EAAEI,UACXC,cAAe,CAAC,CAAEC,OAAQN,EAAEM,OAAQC,OAAQP,EAAEO,OAAQC,IAAKR,EAAES,UAC7DC,eAAgBC,MAIlBjC,GAAK1B,MAAM4D,YAAYC,IAAIZ,EAASxC,QAGpCqD,YAAW,KACTpC,GAAK1B,MAAM4D,YAAYG,OAAOd,EAASxC,OAAM,GAC5C,KAEEiB,GAAA1B,MAAMgE,SAASC,QAAQhB,EAAQ,MAE5BH,QAAAoB,KAAK,4BAA6BxB,EAC5C,CAlEIyB,CAAYzC,QAGJoB,QAAAoB,KAAK,UAAWxC,EAAKY,YACjC,CAzJE8B,CAAsB1C,EAAI,IAEnBU,EAAAC,GAAG,gCAAiCX,KA0J/C,SAAqCA,GACnC,GAAQA,EAAKY,cACNC,EAAqB8B,kBAiE9B,SAAwB5B,SAClB,IAAAC,EACA,IAGaA,EADc,iBAAlBD,EACIE,KAAKC,MAAMH,GAEXA,QAERI,GAEP,YADQC,QAAAD,MAAM,4BAA6BA,EAC3C,CAIE,GAAAH,GAAcA,EAAWK,SAAU,CAErC,IAAIuB,EAAY5B,EAAWhB,KACvB,GAAqB,iBAAd4C,EACL,IACUA,EAAA3B,KAAKC,MAAM0B,SAChBzB,GAEP,YADQC,QAAAD,MAAM,2BAA4BA,EAC1C,CAKJ,MAAM0B,EAAeD,EAAU7D,SAAU,OAAA+D,EAAAF,EAAUG,cAAS,EAAAD,EAAA/D,QAE5D,IAAK8D,EAEH,YADQzB,QAAAoB,KAAK,qBAAsBI,GAI/B,MAAAI,EAAahD,GAAK1B,MAAMgE,SAASW,WAAWC,GAAUA,EAAMnE,SAAW8D,KACtD,IAAnBG,IAEFhD,GAAK1B,MAAMgE,SAASa,OAAOH,EAAY,GAElChD,GAAA1B,MAAM4D,YAAYG,OAAOQ,GAChC,MAEQzB,QAAAoB,KAAK,4BAA6BxB,EAC5C,CA5GIoC,CAAepD,QAGPoB,QAAAoB,KAAK,UAAWxC,EAAKY,YACjC,CAhKEyC,CAA4BrD,EAAI,GACjC,IAGHsD,GAAY,KACV5C,EAAS6C,IAAI,yBACb7C,EAAS6C,IAAI,+BAA8B,IAG7C,MAAMC,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,GAAcC,gBAAAA,IAAoBC,IAE3DhE,GAAY,CAACO,EAAaC,aAC1BL,GAAc5B,EAAuC,IAO3D,MAAM6B,GAAO7B,EAAI,CACf0F,SAAS,EACTC,iBAAiB,EACjBC,cAAe,CACbC,SAAS,EACTjF,OAAQ,GACRkF,OAAQC,EAAOC,MAEjBC,OAAQ,CACN5C,MAAOnC,EAAWI,WAAW4E,WAC7BC,YAAa,KACbC,cAAe,GAEfC,SAAU,MAEZlC,SAAU,GACVJ,gBAAiBuC,MAEbC,GAAcC,EAAiB,CACnCC,MAAO5G,GAAU4G,MACjBC,MAAO7G,GAAU6G,QAEnB,SAASrE,KACPR,GAAK1B,MAAMuF,SAAU,EACrB,MAAMiB,EAAS,IACVrB,QACAiB,GACHJ,YAA+C,OAAlCtE,GAAK1B,MAAM8F,OAAOE,YAAuB,GAAKtE,GAAK1B,MAAM8F,OAAOE,YAC7ES,SAAU/E,GAAK1B,MAAM8F,OAAOG,cAC5BS,SAAU,IACVxD,MAAmC,OAA5BxB,GAAK1B,MAAM8F,OAAO5C,MAAiB,GAAKxB,GAAK1B,MAAM8F,OAAO5C,OAGnE,GAAmC,MAA/BxB,GAAK1B,MAAM8F,OAAOI,SAAkB,CAEtC,MAAMS,EAAQC,IAAQC,OAAO,cACtBL,EAAAM,UAAY,GAAGH,aACfH,EAAAO,QAAU,GAAGJ,YACX,MAAA,GAA+B,MAA/BjF,GAAK1B,MAAM8F,OAAOI,SAAkB,CAE7C,MAAMS,EAAQC,IAAQC,OAAO,cACvBG,EAAUJ,IAAQ/C,IAAI,EAAG,OAAOgD,OAAO,cACtCL,EAAAM,UAAY,GAAGH,aACfH,EAAAO,QAAU,GAAGC,YAAO,CAE7BC,EAAQC,aAAaV,GAAQjF,MAAMC,IACjCE,GAAK1B,MAAMuF,SAAU,EAChB7D,GAAA1B,MAAMgE,SAAWxC,EAAIE,KAAKZ,KACpBoE,GAAAlF,MAAMmH,MAAQ3F,EAAIE,KAAKyF,KAAA,GACnC,CAGM,SAAAC,GAAcC,EAAO,GAC5BhC,GAAgBgC,GAAM9F,MAAK,IAAMW,MAAa,CAG1C,MAAAoF,GAAmBvH,GAAS,IAAM,CACtC,CACEwH,MAAO,mFACPvB,YAAa,SACbwB,YAAahI,EAAE,oBAEjB,CACE+H,MAAO,mFACPvB,YAAa,SACbwB,YAAahI,EAAE,oBAEjB,CACE+H,MAAO,oFACPvB,YAAa,UACbwB,YAAahI,EAAE,qBAEjB,CACE+H,MAAO,kFACPvB,YAAa,QACbwB,YAAahI,EAAE,mBAEjB,CACE+H,MAAO,kFACPvB,YAAa,QACbwB,YAAahI,EAAE,mBAEjB,CACE+H,MAAO,iFACPvB,YAAa,QACbwB,YAAahI,EAAE,mBAEjB,CACE+H,MAAO,kFACPvB,YAAa,QACbwB,YAAahI,EAAE,mBAEjB,CACE+H,MAAO,iFACPvB,YAAa,WACbwB,YAAahI,EAAE,qBAEjB,CACE+H,MAAO,+EACPvB,YAAa,KACbwB,YAAahI,EAAE,mBAGnB,SAASiI,GAAgBzF,GACjB,MAAA0F,EAAUJ,GAAiBtH,MAAM2H,MAAMnH,GAAcA,EAAKwF,cAAgBhE,IAChF,OAAgB,MAAT0F,OAAS,EAAAA,EAAAH,KAAA,CAOlBK,eAAeC,GAAaC,GACrBpG,GAAA1B,MAAMyF,cAAchF,OAASqH,EAAIrH,OACjCiB,GAAA1B,MAAMyF,cAAcC,SAAU,EAE/BoC,EAAIC,WAAaC,EAAUC,MACxBvG,GAAA1B,MAAMyF,cAAcE,OAASC,EAAOsC,KAEpCxG,GAAA1B,MAAMyF,cAAcE,OAASC,EAAOC,IAC3C,CA6EF,SAASsC,GAAW3H,GAClB,OAAOkB,GAAK1B,MAAM4D,YAAYwE,IAAI5H,EAAKC,OAAM,2jEA3StC,SAAiBD,EAAiB6H,GAEnC,MAAAC,EAAYC,OAAOC,eACzB,GAAIF,GAAaA,EAAUvC,WAAW0C,OAAS,EAE7C,OAIF,MAAMC,EAASL,EAAMK,OACE,SAAnBA,EAAOC,SAAsBD,EAAOE,UAAUC,SAAS,qBAK3DR,EAAMS,iBAGFzI,KACF0I,aAAa1I,IACAA,GAAA,MAIfA,GAAayD,YAAW,KAElB1D,GAAgBJ,QAAUQ,EAAKC,OACjCL,GAAgBJ,MAAQ,KAExBI,GAAgBJ,MAAQQ,EAAKC,MAAA,GAE9B,KAAG,qBAQC,SAAuBD,EAAiB6H,GAC/CA,EAAMS,iBAGFzI,KACF0I,aAAa1I,IACAA,GAAA,MAIfwH,GAAarH,EAAI"}