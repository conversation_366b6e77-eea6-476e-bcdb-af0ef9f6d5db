{"version": 3, "file": "brokerageStrategy.api-C0DFg5AR.js", "sources": ["../../src/api/modules/pms/brokerage/brokerageStrategy.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/brokerage-strategy'\r\n/**\r\n * 佣金策略接口\r\n */\r\nexport default {\r\n  /**\r\n   * 创建佣金策略\r\n   * @param data\r\n   * @returns\r\n   */\r\n  createBrokerageStrategy: (data: any) =>\r\n    api.post(`${BASE_PATH}/create`, data),\r\n\r\n  /**\r\n   * 获取佣金策略列表\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getBrokerageStrategyList: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    isEnable?: string\r\n    companyType?: string\r\n    strategyName?: string\r\n  }) =>\r\n    api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获取佣金集团策略列表分页\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getBrokerageStrategyPage: (data: {\r\n    gcode: string\r\n    hcode?: string\r\n    isEnable?: string\r\n    companyType?: string\r\n    strategyName?: string\r\n    pageNo: number\r\n    pageSize: number\r\n  }) =>\r\n    api.get(`${BASE_PATH}/page`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 获得佣金策略\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getBrokerageStrategy: (data: { strategyCode: string }) =>\r\n    api.get(`${BASE_PATH}/get`, {\r\n      params: data,\r\n    }),\r\n\r\n  /**\r\n   * 修改佣金策略状态\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateBrokerageStrategyStatus: (data: { id: number, isEnable: string }) =>\r\n    api.put(`${BASE_PATH}/update-status`, data),\r\n\r\n  /**\r\n   * 修改佣金策略\r\n   * @param data\r\n   * @returns\r\n   */\r\n  updateBrokerageStrategy: (data: any) =>\r\n    api.put(`${BASE_PATH}/update`, data),\r\n}\r\n"], "names": ["BASE_PATH", "brokerageStrategyApi", "createBrokerageStrategy", "data", "api", "post", "getBrokerageStrategyList", "get", "params", "getBrokerageStrategyPage", "getBrokerageStrategy", "updateBrokerageStrategyStatus", "put", "updateBrokerageStrategy"], "mappings": "wCAEA,MAAMA,EAAY,mCAIHC,EAAA,CAMbC,wBAA0BC,GACxBC,EAAIC,KAAK,GAAGL,WAAoBG,GAOlCG,yBAA2BH,GAOzBC,EAAIG,IAAI,GAAGP,SAAkB,CAC3BQ,OAAQL,IAQZM,yBAA2BN,GASzBC,EAAIG,IAAI,GAAGP,SAAkB,CAC3BQ,OAAQL,IAQZO,qBAAuBP,GACrBC,EAAIG,IAAI,GAAGP,QAAiB,CAC1BQ,OAAQL,IAQZQ,8BAAgCR,GAC9BC,EAAIQ,IAAI,GAAGZ,kBAA2BG,GAOxCU,wBAA0BV,GACxBC,EAAIQ,IAAI,GAAGZ,WAAoBG"}