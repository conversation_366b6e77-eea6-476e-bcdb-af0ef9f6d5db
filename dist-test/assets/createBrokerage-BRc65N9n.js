import{d as e,aj as t,ai as a,b as l,y as s,aR as o,aq as r,u as i,o as p,c as u,f as d,w as n,h as b,Y as m,F as c,ag as g,e as y,i as f,g as h,R as _,aD as k,E as R,l as V,m as T,b1 as v,b2 as S,aS as j,j as N,k as x,s as U,x as C,aT as E}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css                 *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css               *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 *//* empty css                   */import{b as L}from"./brokerageStrategy.api-C0DFg5AR.js";import{c as H}from"./channel.api-CM6FWEgD.js";import{r as w}from"./rt.api-5a8-At7-.js";import{d as P}from"./dictData.api-DUabpYqy.js";import{m as z}from"./merchant.api-BtmIsRm3.js";import{B,b as G}from"./constants-Cg3j_uH4.js";const Y={style:{display:"flex"}},D={key:1,style:{"margin-left":"5px"}},I={key:3,style:{"margin-left":"5px"}},O=e({__name:"createBrokerage",props:{strategyCode:{},handle:{},isEdit:{type:Boolean}},setup(e,{expose:O}){const{t:q}=t(),A=a(),F=l(!1),$=l(),X=l({gcode:A.gcode,isG:B.NO,hotels:[],strategyCode:"",strategyName:"",companyType:"0",isGrt:B.YES,brokerageLevelCode:"",rts:[],brokerageType:"0",brokerageValue:0,channels:[],isEnable:B.YES,remark:""}),J=l("1"),K=l({strategyName:[{required:!0,message:q("pleaseEnterStrategyName"),trigger:"blur"}]});s((()=>{!function(){const e={gcode:A.gcode,isG:B.YES};H.getChannelSimpleList(e).then((e=>{0===e.code&&(W.value=e.data)}))}(),function(){const e={gcode:A.gcode,isVirtual:B.NO,isGRt:B.YES,isEnable:B.YES};w.getRoomTypeSimpleList(e).then((e=>{0===e.code&&(Z.value=e.data)}))}(),z.getSimpleList(A.gcode).then((e=>{ee.value=e.data})),P.getDictDataBatch(M).then((e=>{Q.value=e.data.filter((e=>e.dictType===G))}))}));const M=[G],Q=l([]);const W=l([]);const Z=l([]);const ee=l([]);function te(){X.value.brokerageValue=0}return O({submit:()=>new Promise((e=>{$.value&&$.value.validate((t=>{t&&L.createBrokerageStrategy(X.value).then((t=>{0===t.code?(o.success({message:q("successfulUpdate"),center:!0}),e()):o.error({message:t.msg,center:!0})}))}))}))}),(e,t)=>{const a=k,l=R,s=V,o=T,L=v,H=S,w=j,P=N,z=x,B=U,G=C,O=E;return r((p(),u("div",null,[d(G,{ref_key:"formRef",ref:$,model:i(X),rules:i(K),"label-width":"140px","label-suffix":"："},{default:n((()=>[d(a,{"content-position":"left"},{default:n((()=>[b(m(i(q)("strategyInfo")),1)])),_:1}),d(H,{gutter:24},{default:n((()=>[d(L,{md:24},{default:n((()=>[d(o,{label:i(q)("companyType")},{default:n((()=>[d(s,{modelValue:i(X).companyType,"onUpdate:modelValue":t[0]||(t[0]=e=>i(X).companyType=e)},{default:n((()=>[d(l,{value:"0",size:"large"},{default:n((()=>[b(m(i(q)("broker")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),d(H,{gutter:24},{default:n((()=>[d(L,{md:12},{default:n((()=>[d(o,{label:i(q)("strategyName"),prop:"strategyName"},{default:n((()=>[d(w,{modelValue:i(X).strategyName,"onUpdate:modelValue":t[1]||(t[1]=e=>i(X).strategyName=e),placeholder:i(q)("pleaseEnterStrategyName"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),d(L,{md:12},{default:n((()=>[d(o,{label:i(q)("commissionLevel")},{default:n((()=>[d(z,{modelValue:i(X).brokerageLevelCode,"onUpdate:modelValue":t[2]||(t[2]=e=>i(X).brokerageLevelCode=e),"collapse-tags":"","collapse-tags-tooltip":"",placeholder:i(q)("pleaseSelectRoomType"),style:{width:"300px"}},{default:n((()=>[(p(!0),u(c,null,g(i(Q),(e=>(p(),y(P,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),d(a,{"content-position":"left"},{default:n((()=>[b(m(i(q)("strategyRule")),1)])),_:1}),d(H,{gutter:20},{default:n((()=>[d(L,{md:24},{default:n((()=>[d(o,{label:i(q)("rebateType")},{default:n((()=>[d(s,{modelValue:i(J),"onUpdate:modelValue":t[3]||(t[3]=e=>f(J)?J.value=e:null)},{default:n((()=>[d(l,{value:"1",size:"large"},{default:n((()=>[b(m(i(q)("perNight")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),d(o,{label:""},{default:n((()=>[h("div",Y,[d(z,{modelValue:i(X).brokerageType,"onUpdate:modelValue":t[4]||(t[4]=e=>i(X).brokerageType=e),style:{width:"200px"},onChange:te},{default:n((()=>[d(P,{label:i(q)("fixedRebate"),value:"0"},null,8,["label"]),d(P,{label:i(q)("percentageRebate"),value:"1"},null,8,["label"])])),_:1},8,["modelValue"]),"0"===i(X).brokerageType?(p(),y(B,{key:0,modelValue:i(X).brokerageValue,"onUpdate:modelValue":t[5]||(t[5]=e=>i(X).brokerageValue=e),max:1e4,min:0,"controls-position":"right"},null,8,["modelValue"])):_("",!0),"0"===i(X).brokerageType?(p(),u("span",D,m(i(q)("yuan")),1)):_("",!0),"1"===i(X).brokerageType?(p(),y(B,{key:2,modelValue:i(X).brokerageValue,"onUpdate:modelValue":t[6]||(t[6]=e=>i(X).brokerageValue=e),max:1,min:0,step:.01,precision:2,"controls-position":"right"},null,8,["modelValue"])):_("",!0),"1"===i(X).brokerageType?(p(),u("span",I,"%")):_("",!0)])])),_:1})])),_:1})])),_:1}),d(a,{"content-position":"left"},{default:n((()=>[b(m(i(q)("strategyScope")),1)])),_:1}),d(H,{gutter:24},{default:n((()=>[d(L,{md:24},{default:n((()=>[d(o,{label:i(q)("applyChannel")},{default:n((()=>[d(z,{modelValue:i(X).channels,"onUpdate:modelValue":t[7]||(t[7]=e=>i(X).channels=e),multiple:"","collapse-tags-tooltip":"",clearable:"",placeholder:i(q)("pleaseSelectChannel")},{default:n((()=>[(p(!0),u(c,null,g(i(W),(e=>(p(),y(P,{key:e.channelCode,label:e.channelName,value:e.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),d(H,{gutter:24},{default:n((()=>[d(L,{md:24},{default:n((()=>[d(o,{label:i(q)("applyRoomType")},{default:n((()=>[d(s,{modelValue:i(X).isGrt,"onUpdate:modelValue":t[8]||(t[8]=e=>i(X).isGrt=e)},{default:n((()=>[d(l,{value:"0",size:"large",disabled:""},{default:n((()=>[b(m(i(q)("hotelRoomType")),1)])),_:1}),d(l,{value:"1",size:"large"},{default:n((()=>[b(m(i(q)("groupRoomType")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),d(o,{label:""},{default:n((()=>[d(z,{modelValue:i(X).rts,"onUpdate:modelValue":t[9]||(t[9]=e=>i(X).rts=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",placeholder:i(q)("pleaseSelectRoomType")},{default:n((()=>[(p(!0),u(c,null,g(i(Z),(e=>(p(),y(P,{key:e.rtCode,label:e.rtName,value:e.rtCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1})])),_:1})])),_:1}),d(H,{gutter:24},{default:n((()=>[d(L,{md:24},{default:n((()=>[d(o,{label:i(q)("applyHotel")},{default:n((()=>[d(z,{modelValue:i(X).hotels,"onUpdate:modelValue":t[10]||(t[10]=e=>i(X).hotels=e),multiple:"","collapse-tags":"",clearable:"","collapse-tags-tooltip":"",placeholder:i(q)("pleaseSelectHotel")},{default:n((()=>[(p(!0),u(c,null,g(i(ee),(e=>(p(),y(P,{key:e.hcode,label:e.hname,value:e.hcode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),d(H,{gutter:24},{default:n((()=>[d(L,{md:24},{default:n((()=>[d(o,{label:i(q)("status")},{default:n((()=>[d(s,{modelValue:i(X).isEnable,"onUpdate:modelValue":t[11]||(t[11]=e=>i(X).isEnable=e)},{default:n((()=>[d(l,{value:"1",size:"large"},{default:n((()=>[b(m(i(q)("enabled")),1)])),_:1}),d(l,{value:"0",size:"large"},{default:n((()=>[b(m(i(q)("disabled")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),d(H,{gutter:24},{default:n((()=>[d(L,{md:24},{default:n((()=>[d(o,{label:i(q)("remark")},{default:n((()=>[d(w,{modelValue:i(X).remark,"onUpdate:modelValue":t[12]||(t[12]=e=>i(X).remark=e),type:"textarea",rows:3,placeholder:i(q)("pleaseEnterRemark"),maxlength:"250"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["model","rules"])])),[[O,i(F)]])}}});function q(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Info"}},companyType:{t:0,b:{t:2,i:[{t:3}],s:"Company Type"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"Agreement Unit"}},broker:{t:0,b:{t:2,i:[{t:3}],s:"Broker"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},commissionLevel:{t:0,b:{t:2,i:[{t:3}],s:"Level"}},rebateType:{t:0,b:{t:2,i:[{t:3}],s:"Rebate Type"}},perNight:{t:0,b:{t:2,i:[{t:3}],s:"Per Night"}},fixedRebate:{t:0,b:{t:2,i:[{t:3}],s:"Fixed Rebate"}},percentageRebate:{t:0,b:{t:2,i:[{t:3}],s:"Percentage Rebate"}},strategyScope:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Scope"}},applyChannel:{t:0,b:{t:2,i:[{t:3}],s:"Channels"}},applyRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Room Type"}},applyHotel:{t:0,b:{t:2,i:[{t:3}],s:"Hotels"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},enabled:{t:0,b:{t:2,i:[{t:3}],s:"Enabled"}},disabled:{t:0,b:{t:2,i:[{t:3}],s:"Disabled"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},pleaseEnterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter strategy name"}},pleaseSelectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Please select room type"}},pleaseSelectChannel:{t:0,b:{t:2,i:[{t:3}],s:"Please select channel"}},pleaseSelectHotel:{t:0,b:{t:2,i:[{t:3}],s:"Please select hotel"}},pleaseEnterRemark:{t:0,b:{t:2,i:[{t:3}],s:"Please enter remark"}},successfulUpdate:{t:0,b:{t:2,i:[{t:3}],s:"Update successful"}},errorOccurred:{t:0,b:{t:2,i:[{t:3}],s:"An error occurred"}},strategyRule:{t:0,b:{t:2,i:[{t:3}],s:"Strategy Rules"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"$"}},hotelRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Hotel Room Type"}},groupRoomType:{t:0,b:{t:2,i:[{t:3}],s:"Group Room Type"}}},"zh-cn":{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"策略信息"}},companyType:{t:0,b:{t:2,i:[{t:3}],s:"公司类型"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}},broker:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"策略名称"}},commissionLevel:{t:0,b:{t:2,i:[{t:3}],s:"佣金级别"}},rebateType:{t:0,b:{t:2,i:[{t:3}],s:"返佣类型"}},perNight:{t:0,b:{t:2,i:[{t:3}],s:"按间夜"}},fixedRebate:{t:0,b:{t:2,i:[{t:3}],s:"定额反佣"}},percentageRebate:{t:0,b:{t:2,i:[{t:3}],s:"百分比返佣"}},strategyScope:{t:0,b:{t:2,i:[{t:3}],s:"策略应用范围"}},applyChannel:{t:0,b:{t:2,i:[{t:3}],s:"应用渠道"}},applyRoomType:{t:0,b:{t:2,i:[{t:3}],s:"应用房型"}},applyHotel:{t:0,b:{t:2,i:[{t:3}],s:"应用酒店"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},enabled:{t:0,b:{t:2,i:[{t:3}],s:"有效"}},disabled:{t:0,b:{t:2,i:[{t:3}],s:"无效"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},pleaseEnterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"请输入策略名称"}},pleaseSelectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"请选择房型"}},pleaseSelectChannel:{t:0,b:{t:2,i:[{t:3}],s:"请选择渠道"}},pleaseSelectHotel:{t:0,b:{t:2,i:[{t:3}],s:"请选择适用门店"}},pleaseEnterRemark:{t:0,b:{t:2,i:[{t:3}],s:"请输入备注"}},successfulUpdate:{t:0,b:{t:2,i:[{t:3}],s:"修改成功"}},errorOccurred:{t:0,b:{t:2,i:[{t:3}],s:"发生错误"}},strategyRule:{t:0,b:{t:2,i:[{t:3}],s:"策略规则"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"元"}},hotelRoomType:{t:0,b:{t:2,i:[{t:3}],s:"酒店房型"}},groupRoomType:{t:0,b:{t:2,i:[{t:3}],s:"集团房型"}}},km:{strategyInfo:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានអំពីយុទ្ធសាស្ត្រ"}},companyType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទក្រុមហ៊ុន"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"អង្គភាពសន្យា"}},broker:{t:0,b:{t:2,i:[{t:3}],s:"ភ្នាក់ងារ"}},strategyName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះយុទ្ធសាស្ត្រ"}},commissionLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតកំរៃជើងសា"}},rebateType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទកំរៃជើងសា"}},perNight:{t:0,b:{t:2,i:[{t:3}],s:"ក្នុងមួយយប់"}},fixedRebate:{t:0,b:{t:2,i:[{t:3}],s:"កំរៃជើងសាថេរ"}},percentageRebate:{t:0,b:{t:2,i:[{t:3}],s:"កំរៃជើងសាគិតជាភាគរយ"}},strategyScope:{t:0,b:{t:2,i:[{t:3}],s:"វិសាលភាពយុទ្ធសាស្ត្រ"}},applyChannel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែលអនុវត្ត"}},applyRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់អនុវត្ត"}},applyHotel:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារអនុវត្ត"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},enabled:{t:0,b:{t:2,i:[{t:3}],s:"សកម្ម"}},disabled:{t:0,b:{t:2,i:[{t:3}],s:"អសកម្ម"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}},pleaseEnterStrategyName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ"}},pleaseSelectRoomType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទបន្ទប់"}},pleaseSelectChannel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសឆានែល"}},pleaseSelectHotel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសសណ្ឋាគារ"}},pleaseEnterRemark:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលចំណាំ"}},successfulUpdate:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួលជោគជ័យ"}},errorOccurred:{t:0,b:{t:2,i:[{t:3}],s:"មានកំហុសកើតឡើង"}},strategyRule:{t:0,b:{t:2,i:[{t:3}],s:"ច្បាប់យុទ្ធសាស្ត្រ"}},yuan:{t:0,b:{t:2,i:[{t:3}],s:"រៀល"}},hotelRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់សណ្ឋាគារ"}},groupRoomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់ក្រុម"}}}}})}q(O);export{O as default};
//# sourceMappingURL=createBrokerage-BRc65N9n.js.map
