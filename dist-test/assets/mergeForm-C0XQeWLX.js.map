{"version": 3, "file": "mergeForm-C0XQeWLX.js", "sources": ["../../src/views/print/mergeForm.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"printTeamCheckInForm\": \"Print Team/Shared Room Check-in Form\",\r\n    \"cancel\": \"Cancel\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"printTeamCheckInForm\": \"打印团队/联房入住单\",\r\n    \"cancel\": \"取消\"\r\n  },\r\n  \"km\": {\r\n    \"printTeamCheckInForm\": \"បោះពុម្ពទម្រង់ចូលសំណាកក្រុម/បន្ទប់រួម\",\r\n    \"cancel\": \"បោះបង់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { printApi, printFormApi } from '@/api/modules/index'\r\nimport { BillType, layout_width_map, PrintFormat } from '@/models/dict/constants.ts'\r\nimport useSettingsStore from '@/store/modules/settings'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\ndefineOptions({\r\n  name: 'PrintCheckInForm',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue: boolean\r\n    bindCode: string | number\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    bindCode: '',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  refresh: []\r\n}>()\r\n\r\nconst Stimulsoft = window.Stimulsoft\r\n\r\nconst { t } = useI18n() // 解构 t 函数\r\nconst userStore = useUserStore()\r\nconst data = ref({\r\n  tableAutoHeight: true,\r\n  showWidth: '400px',\r\n})\r\nconst loading = ref(false)\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nonMounted(async () => {\r\n  await Promise.all([getPrintLayout(), getPrintInfo()])\r\n})\r\nconst layout = ref(PrintFormat.POS.toString())\r\nasync function getPrintLayout() {\r\n  printApi\r\n    .getPrintLayout({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      billCode: BillType.BILL,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        layout.value = res.data.layout\r\n        data.value.showWidth = layout_width_map.get(layout.value)\r\n      }\r\n    })\r\n}\r\nasync function getPrintInfo() {\r\n  loading.value = true\r\n  printFormApi\r\n    .printMergeForm({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      bindCode: props.bindCode,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        setJson(res.data)\r\n      }\r\n    })\r\n}\r\nconst timestamp = Date.now()\r\nlet reportInstance: any\r\nasync function setJson(json: any) {\r\n  const licensePath = new URL('/src/assets/license.key', import.meta.url).href\r\n  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)\r\n  const report = new Stimulsoft.Report.StiReport()\r\n\r\n  if (layout.value === PrintFormat.A4.toString()) {\r\n    // report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printTogetherBillA4.mrt`)\r\n    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printTogetherBillA4.mrt?t=${timestamp}`)\r\n  } else if (layout.value === PrintFormat.A412.toString()) {\r\n    // report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printTogetherBillA4-1-2.mrt`)\r\n    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printTogetherBillA4-1-2.mrt?t=${timestamp}`)\r\n  } else if (layout.value === PrintFormat.A413.toString()) {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printTogetherBillA4-1-3.mrt?t=${timestamp}`)\r\n    // report.loadFile(\r\n    //   `https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printTogetherBillA4-1-3.mrt?t=${timestamp}`,\r\n    // )\r\n  } else {\r\n    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printMergeForm78.mrt?t=${timestamp}`)\r\n    // report.loadFile(\r\n    //   `https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printMergeForm78.mrt?t=${timestamp}`\r\n    // )\r\n  }\r\n  const settingsStore = useSettingsStore() // 使用 Vuex 存储语言设置\r\n  const currentLanguage = settingsStore.lang // 当前语言 'zh-cn' 或 'en'\r\n  const localizationFile = `${currentLanguage}.xml`\r\n  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)\r\n\r\n  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')\r\n  dataSet.readJson(JSON.stringify(json))\r\n  report.dictionary.databases.clear()\r\n  report.regData('JSON', 'JSON', dataSet)\r\n  // 配置报表查看器选项\r\n  const viewerOptions = new Stimulsoft.Viewer.StiViewerOptions()\r\n  viewerOptions.toolbar.visible = false // 隐藏工具栏\r\n  const viewer = new Stimulsoft.Viewer.StiViewer(viewerOptions, 'StiViewer', false)\r\n  viewer.report = report\r\n  viewer.renderHtml('report')\r\n  reportInstance = report // 保存报表实例\r\n  loading.value = false\r\n}\r\n\r\nfunction printReport() {\r\n  reportInstance.print() // 打印报表\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :width=\"data.showWidth\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\" style=\"min-height: 600px\">\r\n    <div style=\"display: flex; flex-direction: column; height: 100%\">\r\n      <!-- 按钮区域 -->\r\n      <div style=\"display: flex; gap: 10px; justify-content: center; margin-bottom: 10px\">\r\n        <el-button type=\"primary\" @click=\"printReport\">\r\n          {{ t('printTeamCheckInForm') }}\r\n        </el-button>\r\n        <el-button @click=\"onCancel\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n      </div>\r\n      <!-- 报表区域 -->\r\n      <div id=\"report\" style=\"flex: 1; overflow-y: auto\" />\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "Stimulsoft", "window", "t", "useI18n", "userStore", "useUserStore", "data", "ref", "tableAutoHeight", "showWidth", "loading", "myVisible", "computed", "get", "modelValue", "set", "val", "onMounted", "async", "Promise", "all", "getPrintLayout", "getPrintInfo", "layout", "PrintFormat", "POS", "toString", "printApi", "gcode", "hcode", "billCode", "BillType", "BILL", "then", "res", "code", "value", "layout_width_map", "printFormApi", "printMergeForm", "bindCode", "json", "licensePath", "URL", "url", "href", "Base", "StiLicense", "loadFromFile", "report", "Report", "StiReport", "A4", "loadFile", "timestamp", "A412", "A413", "settingsStore", "useSettingsStore", "localizationFile", "lang", "Localization", "StiLocalization", "setLocalizationFile", "dataSet", "System", "Data", "DataSet", "read<PERSON>son", "JSON", "stringify", "dictionary", "databases", "clear", "regData", "viewerOptions", "Viewer", "StiViewerOptions", "toolbar", "visible", "viewer", "StiViewer", "renderHtml", "reportInstance", "<PERSON><PERSON><PERSON>", "Date", "now", "printReport", "print", "onCancel"], "mappings": "iyBA2BA,MAAMA,EAAQC,EAWRC,EAAQC,EAKRC,EAAaC,OAAOD,YAEpBE,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAOC,EAAI,CACfC,iBAAiB,EACjBC,UAAW,UAEPC,EAAUH,GAAI,GACdI,EAAYC,EAAS,CACzBC,IAAM,IACGjB,EAAMkB,WAEf,GAAAC,CAAIC,GACFlB,EAAM,oBAAqBkB,EAAG,IAIlCC,GAAUC,gBACFC,QAAQC,IAAI,CAACC,IAAkBC,KAAe,IAEtD,MAAMC,EAAShB,EAAIiB,EAAYC,IAAIC,YACnCR,eAAeG,IACbM,EACGN,eAAe,CACdO,MAAOxB,EAAUwB,MACjBC,MAAOzB,EAAUyB,MACjBC,SAAUC,EAASC,OAEpBC,MAAMC,IACY,IAAbA,EAAIC,OACCZ,EAAAa,MAAQF,EAAI5B,KAAKiB,OACxBjB,EAAK8B,MAAM3B,UAAY4B,EAAiBxB,IAAIU,EAAOa,OAAK,GAE3D,CAELlB,eAAeI,IACbZ,EAAQ0B,OAAQ,EAChBE,EACGC,eAAe,CACdX,MAAOxB,EAAUwB,MACjBC,MAAOzB,EAAUyB,MACjBW,SAAU5C,EAAM4C,WAEjBP,MAAMC,IACY,IAAbA,EAAIC,MAOdjB,eAAuBuB,GACrB,MAAMC,EAAc,IAAoDC,IAAA,4xBAAAC,KAAAC,WAClE7C,EAAW8C,KAAKC,WAAWC,aAAaN,GAC9C,MAAMO,EAAS,IAAIjD,EAAWkD,OAAOC,UAEjC5B,EAAOa,QAAUZ,EAAY4B,GAAG1B,WAE3BuB,EAAAI,SAAS,uGAAuGC,KAC9G/B,EAAOa,QAAUZ,EAAY+B,KAAK7B,WAEpCuB,EAAAI,SAAS,2GAA2GC,KAClH/B,EAAOa,QAAUZ,EAAYgC,KAAK9B,WAC3CuB,EAAOI,SAAS,gDAAkFC,KAKlGL,EAAOI,SAAS,yCAA2EC,KAK7F,MAAMG,EAAgBC,IAEhBC,EAAmB,GADDF,EAAcG,WAEtC5D,EAAW8C,KAAKe,aAAaC,gBAAgBC,oBAAoBJ,GAEjE,MAAMK,EAAU,IAAIhE,EAAWiE,OAAOC,KAAKC,QAAQ,QACnDH,EAAQI,SAASC,KAAKC,UAAU7B,IACzBQ,EAAAsB,WAAWC,UAAUC,QACrBxB,EAAAyB,QAAQ,OAAQ,OAAQV,GAE/B,MAAMW,EAAgB,IAAI3E,EAAW4E,OAAOC,iBAC5CF,EAAcG,QAAQC,SAAU,EAChC,MAAMC,EAAS,IAAIhF,EAAW4E,OAAOK,UAAUN,EAAe,aAAa,GAC3EK,EAAO/B,OAASA,EAChB+B,EAAOE,WAAW,UACDC,EAAAlC,EACjBvC,EAAQ0B,OAAQ,CAAA,CA5CVgD,CAAQlD,EAAI5B,KAAI,GAEnB,CAEC,MAAAgD,EAAY+B,KAAKC,MACnB,IAAAH,EA0CJ,SAASI,IACPJ,EAAeK,OAAM,CAGvB,SAASC,IACP9E,EAAUyB,OAAQ,CAAA"}