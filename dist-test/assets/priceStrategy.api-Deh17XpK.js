import{a as t}from"./index-CkEhI1Zk.js";const e="admin-api/pms/price-strategy",a={getPriceStrategyBoardList:a=>t.get(`${e}/list-board`,{params:a}),getPriceStrategyList:a=>t.get(`${e}/list`,{params:a}),createPriceStrategy:e=>t.post("/admin-api/pms/price-strategy/create",e),getPriceStrategy:a=>t.get(`${e}/get`,{params:{strategyCode:a}}),updatePriceStrategy:a=>t.put(`${e}/update`,a),updatePriceStrategyStatus:a=>t.put(`${e}/update-status`,a)};export{a as p};
//# sourceMappingURL=priceStrategy.api-Deh17XpK.js.map
