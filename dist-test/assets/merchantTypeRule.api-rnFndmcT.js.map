{"version": 3, "file": "merchantTypeRule.api-rnFndmcT.js", "sources": ["../../src/api/modules/pms/config/merchantTypeRule.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/merchant-type-rule'\r\n/** 门店类型规则 */\r\nexport default {\r\n\r\n  /**\r\n   * 更新门店类型规则\r\n   * @param data\r\n   */\r\n  updateMerchantTypeRule: (data: any) => {\r\n    return api.put(`${BASE_PATH}/update`, data)\r\n  },\r\n\r\n  /**\r\n   * 获得门店类型规则\r\n   * @param data\r\n   */\r\n  getMerchantTypeRule: (data: { gcode: string, merchantType: string }) => {\r\n    return api.get(`${BASE_PATH}/get`, {\r\n      params: data,\r\n    })\r\n  },\r\n\r\n  /**\r\n   * 获得门店类型规则列表\r\n   * @param data\r\n   */\r\n  getMerchantTypeRuleList: (data: { gcode: string, merchantType?: string }) => {\r\n    return api.get(`${BASE_PATH}/list`, {\r\n      params: data,\r\n    })\r\n  },\r\n}\r\n"], "names": ["BASE_PATH", "merchantTypeRuleApi", "updateMerchantTypeRule", "data", "api", "put", "getMerchantTypeRule", "get", "params", "getMerchantTypeRuleList"], "mappings": "wCAEA,MAAMA,EAAY,mCAEHC,EAAA,CAMbC,uBAAyBC,GAChBC,EAAIC,IAAI,GAAGL,WAAoBG,GAOxCG,oBAAsBH,GACbC,EAAIG,IAAI,GAAGP,QAAiB,CACjCQ,OAAQL,IAQZM,wBAA0BN,GACjBC,EAAIG,IAAI,GAAGP,SAAkB,CAClCQ,OAAQL"}