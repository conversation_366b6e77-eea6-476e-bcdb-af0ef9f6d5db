{"version": 3, "file": "checkinList-C-MK4twV.js", "sources": ["../../src/views/order/search/checkinList.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"search\": {\r\n      \"quickFilter\": \"Quick Filter\",\r\n      \"all\": \"All\",\r\n      \"todayCheckout\": \"Today's Check-out\",\r\n      \"todayCheckin\": \"Today's Check-in\",\r\n      \"preciseSearch\": \"Precise Search\",\r\n      \"searchPlaceholder\": \"OrderNo、Name、Phone、Room\",\r\n      \"channel\": \"Channel\",\r\n      \"select\": \"select\",\r\n      \"guestSourceType\": \"Guest Source\",\r\n      \"roomType\": \"Room Type\",\r\n      \"checkinType\": \"Check-in Type\",\r\n      \"searchButton\": \"Search\",\r\n      \"displayWay\": \"Display Mode\",\r\n      \"orderMode\": \"Order Mode\",\r\n      \"guestMode\": \"Guest Mode\"\r\n    },\r\n    \"date\": {\r\n      \"time\":\"Time\",\r\n      \"checkInTime\": \"Check-in Time\",\r\n      \"planCheckoutTime\": \"Plan Check-out Time\",\r\n      \"rangeSeparator\": \"to\",\r\n      \"startPlaceholder\": \"Start Time\",\r\n      \"endPlaceholder\": \"End Time\"\r\n    },\r\n    \"table\": {\r\n      \"orderNumber\": \"Order No\",\r\n      \"guestPhone\": \"Guest/Phone\",\r\n      \"roomType\": \"Room Type/Price\",\r\n      \"roomNumber\": \"Room No\",\r\n      \"roomPrice\": \"Room Price\",\r\n      \"checkinTime\": \"Check-in Time\",\r\n      \"checkoutTime\": \"Check-out Time\",\r\n      \"guestSourceType\": \"Guest Source Type\",\r\n      \"checkinType\": \"Check-in Type\",\r\n      \"channel\": \"Channel\",\r\n      \"operation\": \"Actions\",\r\n      \"view\": \"View\",\r\n      \"entryAccount\": \"Post\",\r\n      \"hourToFull\": \"Hourly To Full\",\r\n      \"guest\": \"Guest\",\r\n      \"phone\": \"Phone\",\r\n      \"gender\": \"Gender\",\r\n      \"isMainGuest\": \"Main Guest\",\r\n      \"actualCheckinTime\": \"Check-in Time\",\r\n      \"actualCheckoutTime\": \"Check-out Time\",\r\n      \"plannedCheckoutTime\": \"Planned Check-out\",\r\n      \"status\": \"Status\"\r\n    },\r\n    \"button\": {\r\n      \"query\": \"Filter\"\r\n    },\r\n    \"orderNoLabel\": \"Order No:\",\r\n    \"externalOrderNoLabel\": \"External Order No:\",\r\n    \"guestLabel\": \"Guest:\",\r\n    \"phoneLabel\": \"Phone:\",\r\n    \"checkinLabel\": \"Check-in:\",\r\n    \"checkoutLabel\": \"Check-out:\",\r\n    \"currencySymbol\": \"$\",\r\n    \"mainGuest\": \"Main Guest\",\r\n    \"companion\": \"Companion\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"Booking\",\r\n      \"check_in\": \"Checked In\",\r\n      \"check_out\": \"Checked Out\",\r\n      \"noshow\": \"No Show\",\r\n      \"cancel\": \"Cancelled\",\r\n      \"be_confirm\": \"Pending Confirmation\",\r\n      \"refuse\": \"Refused\",\r\n      \"over\": \"Completed\",\r\n      \"credit\": \"Credit\",\r\n      \"continue\": \"Extended Stay\"\r\n    },\r\n    \"male\": \"Male\",\r\n    \"female\": \"Female\",\r\n    \"unknown\": \"Unknown\",\r\n  },\r\n  \"zh-cn\": {\r\n    \"search\": {\r\n      \"quickFilter\": \"快速筛选\",\r\n      \"all\": \"全部\",\r\n      \"todayCheckout\": \"今日预离\",\r\n      \"todayCheckin\": \"今日入住\",\r\n      \"preciseSearch\": \"精确搜索\",\r\n      \"searchPlaceholder\": \"订单号、外部订单号、姓名、手机号、房号\",\r\n      \"channel\": \"渠道\",\r\n      \"select\": \"请选择\",\r\n      \"guestSourceType\": \"客源类型\",\r\n      \"roomType\": \"房型\",\r\n      \"checkinType\": \"入住类型\",\r\n      \"searchButton\": \"查询\",\r\n      \"displayWay\": \"显示方式\",\r\n      \"orderMode\": \"订单模式\",\r\n      \"guestMode\": \"客人模式\"\r\n    },\r\n    \"date\": {\r\n      \"checkInTime\": \"入住时间\",\r\n      \"planCheckoutTime\": \"预离时间\",\r\n      \"time\":\"时间\",\r\n      \"rangeSeparator\": \"到\",\r\n      \"startPlaceholder\": \"开始时间\",\r\n      \"endPlaceholder\": \"结束时间\"\r\n    },\r\n    \"table\": {\r\n      \"orderNumber\": \"订单号/外部订单号\",\r\n      \"guestPhone\": \"客人/电话\",\r\n      \"roomType\": \"房型/房价\",\r\n      \"roomNumber\": \"房号\",\r\n      \"roomPrice\": \"房价\",\r\n      \"checkinTime\": \"入住时间\",\r\n      \"checkoutTime\": \"预离时间\",\r\n      \"guestSourceType\": \"客源类型\",\r\n      \"checkinType\": \"入住类型\",\r\n      \"channel\": \"渠道\",\r\n      \"operation\": \"操作\",\r\n      \"view\": \"查看\",\r\n      \"entryAccount\": \"入账\",\r\n      \"hourToFull\": \"钟点房转全天\",\r\n      \"guest\": \"客人\",\r\n      \"phone\": \"电话\",\r\n      \"gender\": \"性别\",\r\n      \"isMainGuest\": \"是否主客\",\r\n      \"actualCheckinTime\": \"入住时间\",\r\n      \"actualCheckoutTime\": \"退房时间\",\r\n      \"plannedCheckoutTime\": \"预离时间\",\r\n      \"status\": \"状态\"\r\n    },\r\n    \"button\": {\r\n      \"query\": \"查询\"\r\n    },\r\n    \"orderNoLabel\": \"订单号：\",\r\n    \"externalOrderNoLabel\": \"外部订单号：\",\r\n    \"guestLabel\": \"客人：\",\r\n    \"phoneLabel\": \"电话：\",\r\n    \"checkinLabel\": \"入住：\",\r\n    \"checkoutLabel\": \"预离：\",\r\n    \"currencySymbol\": \"￥\",\r\n    \"mainGuest\": \"主客\",\r\n    \"companion\": \"同住\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"预订中\",\r\n      \"check_in\": \"已入住\",\r\n      \"check_out\": \"已退房\",\r\n      \"noshow\": \"未到店\",\r\n      \"cancel\": \"已取消\",\r\n      \"be_confirm\": \"待确认\",\r\n      \"refuse\": \"已拒绝\",\r\n      \"over\": \"已完成\",\r\n      \"credit\": \"挂账\",\r\n      \"continue\": \"续住\"\r\n    },\r\n    \"male\": \"男\",\r\n    \"female\": \"女\",\r\n    \"unknown\": \"保密\",\r\n  },\r\n  \"km\": {\r\n    \"search\": {\r\n      \"quickFilter\": \"តម្រងរហ័ស\",\r\n      \"all\": \"ទាំងអស់\",\r\n      \"todayCheckout\": \"ចាកចេញថ្ងៃនេះ\",\r\n      \"todayCheckin\": \"ចូលស្នាក់នៅថ្ងៃនេះ\",\r\n      \"preciseSearch\": \"ស្វែងរកយ៉ាងជាក់លាក់\",\r\n      \"searchPlaceholder\": \"លេខបញ្ជាទិញ, ឈ្មោះ, ទូរស័ព្ទ, បន្ទប់\",\r\n      \"channel\": \"ឆានែល\",\r\n      \"select\": \"ជ្រើសរើស\",\r\n      \"guestSourceType\": \"ប្រភពភ្ញៀវ\",\r\n      \"roomType\": \"ប្រភេទបន្ទប់\",\r\n      \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n      \"searchButton\": \"ស្វែងរក\",\r\n      \"displayWay\": \"របៀបបង្ហាញ\",\r\n      \"orderMode\": \"របៀបបញ្ជាទិញ\",\r\n      \"guestMode\": \"របៀបភ្ញៀវ\"\r\n    },\r\n    \"date\": {\r\n      \"time\":\"ពេលវេលា\",\r\n      \"checkInTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n      \"planCheckoutTime\": \"ពេលវេលាចាកចេញគម្រោង\",\r\n      \"rangeSeparator\": \"ទៅ\",\r\n      \"startPlaceholder\": \"ពេលវេលាចាប់ផ្តើម\",\r\n      \"endPlaceholder\": \"ពេលវេលាបញ្ចប់\"\r\n    },\r\n    \"table\": {\r\n      \"orderNumber\": \"លេខបញ្ជាទិញ\",\r\n      \"guestPhone\": \"ភ្ញៀវ/ទូរស័ព្ទ\",\r\n      \"roomType\": \"ប្រភេទបន្ទប់/តម្លៃ\",\r\n      \"roomNumber\": \"លេខបន្ទប់\",\r\n      \"roomPrice\": \"តម្លៃបន្ទប់\",\r\n      \"checkinTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n      \"checkoutTime\": \"ពេលវេលាចាកចេញ\",\r\n      \"guestSourceType\": \"ប្រភពភ្ញៀវ\",\r\n      \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n      \"channel\": \"ឆានែល\",\r\n      \"operation\": \"សកម្មភាព\",\r\n      \"view\": \"មើល\",\r\n      \"entryAccount\": \"បញ្ចូលគណនី\",\r\n      \"hourToFull\": \"បន្ទប់ម៉ោងទៅពេញម៉ោង\",\r\n      \"guest\": \"ភ្ញៀវ\",\r\n      \"phone\": \"ទូរស័ព្ទ\",\r\n      \"gender\": \"ភេទ\",\r\n      \"isMainGuest\": \"ភ្ញៀវសំខាន់\",\r\n      \"actualCheckinTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n      \"actualCheckoutTime\": \"ពេលវេលាចាកចេញ\",\r\n      \"plannedCheckoutTime\": \"ពេលវេលាចាកចេញគម្រោង\",\r\n      \"status\": \"ស្ថានភាព\"\r\n    },\r\n    \"button\": {\r\n      \"query\": \"តម្រង\"\r\n    },\r\n    \"orderNoLabel\": \"លេខបញ្ជាទិញ៖\",\r\n    \"externalOrderNoLabel\": \"លេខបញ្ជាទិញខាងក្រៅ៖\",\r\n    \"guestLabel\": \"ភ្ញៀវ៖\",\r\n    \"phoneLabel\": \"ទូរស័ព្ទ៖\",\r\n    \"checkinLabel\": \"ចូលស្នាក់នៅ៖\",\r\n    \"checkoutLabel\": \"ចាកចេញ៖\",\r\n    \"currencySymbol\": \"៛\",\r\n    \"mainGuest\": \"ភ្ញៀវសំខាន់\",\r\n    \"companion\": \"ភ្ញៀវអមដំណើរ\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"កំពុងកក់\",\r\n      \"check_in\": \"បានចូលស្នាក់នៅ\",\r\n      \"check_out\": \"បានចាកចេញ\",\r\n      \"noshow\": \"មិនបានមកដល់\",\r\n      \"cancel\": \"បានលុបចោល\",\r\n      \"be_confirm\": \"រង់ចាំការបញ្ជាក់\",\r\n      \"refuse\": \"បានបដិសេធ\",\r\n      \"over\": \"បានបញ្ចប់\",\r\n      \"credit\": \"ជំពាក់\",\r\n      \"continue\": \"បន្តស្នាក់នៅ\"\r\n    },\r\n    \"male\": \"ប្រុស\",\r\n    \"female\": \"ស្រី\",\r\n    \"unknown\": \"ផ្សេងទៀត\",\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { BookModel } from '@/models/index'\r\nimport { channelApi, dictDataApi, orderApi, rtApi } from '@/api/modules/index'\r\nimport userApi from '@/api/modules/system/user/user.api'\r\nimport { BooleanEnum, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE, NoType, OrderState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport OrderDialog from '@/views/order/info/order.vue'\r\nimport EntryAccount from '@/views/room/components/entryAccount/index.vue'\r\nimport { Search } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n  /**\r\n   * 详情展示模式\r\n   * router 路由跳转\r\n   * dialog 对话框\r\n   * drawer 抽屉\r\n   */\r\n  formMode: 'dialog' as 'router' | 'dialog' | 'drawer',\r\n  // 详情\r\n  formModeProps: {\r\n    visible: false,\r\n    // 订单号\r\n    orderNo: '',\r\n    noType: NoType.ORDER,\r\n  },\r\n  // 搜索\r\n  search: {\r\n    // 快速筛选\r\n    state: '-1',\r\n    // 渠道\r\n    channelCode: '-1',\r\n    // 房型\r\n    rtCode: '',\r\n    // 客源类型\r\n    guestSrcType: '',\r\n    // 入住类型\r\n    checkinType: '',\r\n    // 销售员\r\n    seller: '',\r\n    /** 查询类型 0姓名 1手机号码 2房号 3预订单号 4外部订单号 */\r\n    searchType: '0',\r\n    searchContent: '',\r\n    // 时间类型\r\n    dataType: '1',\r\n    dataTime: [] as [],\r\n    // 显示方式, 0: 订单模式,1：客人模式\r\n    displayWay: '0',\r\n  },\r\n\r\n  // 列表数据\r\n  dataList: [] as BookModel[],\r\n})\r\n\r\nonMounted(() => {\r\n  getChannels()\r\n  getRts()\r\n  getConstants()\r\n  getDataList()\r\n  getSeller()\r\n})\r\n\r\n/** 销售员列表 */\r\nconst sellers = ref<{ nickname: string; username: string }[]>([])\r\nfunction getSeller() {\r\n  userApi.listSeller({ gcode: userStore.gcode, hcode: userStore.hcode }).then((res: any) => {\r\n    sellers.value = res.data\r\n  })\r\n}\r\n\r\n/** 获取渠道列表 */\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 获取房型列表 */\r\nconst rts = ref<{ rtCode: string; rtName: string }[]>([])\r\nfunction getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isVirtual: BooleanEnum.NO,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n// 通用字典\r\nconst dictTypes = [DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_CHECKIN_TYPE]\r\n/** 客源类型列表 */\r\nconst srcTypeList = ref<{ code: string; label: string }[]>([])\r\n/** 入住类型列表 */\r\nconst checkinTypeList = ref<{ code: string; label: string }[]>([])\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    srcTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n    checkinTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n  })\r\n}\r\n\r\nfunction getDataList() {\r\n  let start = ''\r\n  let end = ''\r\n  if (data.value.search.dataTime && data.value.search.dataTime.length > 0) {\r\n    start = dayjs(data.value.search.dataTime[0]).format('YYYY-MM-DD HH:mm:ss')\r\n    end = dayjs(data.value.search.dataTime[1]).format('YYYY-MM-DD HH:mm:ss')\r\n  }\r\n\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    quickFilter: data.value.search.state === '-1' ? '' : data.value.search.state,\r\n    keyWords: data.value.search.searchContent,\r\n    channelCode: data.value.search.channelCode === '-1' ? '' : data.value.search.channelCode,\r\n    timeType: data.value.search.dataType,\r\n    startTime: start,\r\n    endTime: end,\r\n    guestSrcType: data.value.search.guestSrcType,\r\n    rtCode: data.value.search.rtCode,\r\n    checkinType: data.value.search.checkinType,\r\n    displayWay: data.value.search.displayWay,\r\n  }\r\n  data.value.loading = true\r\n  orderApi.orderPagerList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    if (res.code === 0) {\r\n      data.value.dataList = res.data.list\r\n      pagination.value.total = res.data.total\r\n    }\r\n  })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nconst routerName = ref('detail')\r\nconst typeName = ref('individual')\r\n\r\nfunction onEdit(row: any) {\r\n  data.value.formModeProps.orderNo = row.orderNo\r\n  data.value.formModeProps.visible = true\r\n}\r\n/** 展示入账页面 */\r\nconst entryAccountVisible = ref(false)\r\nconst orderVisible = ref({\r\n  accountOrderNo: '',\r\n  togetherCode: '',\r\n  type: 'order',\r\n})\r\nconst tabName = ref('')\r\nfunction entryAccount(val: string, row: any) {\r\n  orderVisible.value.accountOrderNo = row.orderNo\r\n\r\n  // 从togetherList中找到主客的togetherCode\r\n  let mainGuestTogetherCode = row.togetherCode // 默认使用row的togetherCode作为备选\r\n  if (row.togetherList && row.togetherList.length > 0) {\r\n    const mainGuest = row.togetherList.find((guest: any) => guest.isMain === '1')\r\n    if (mainGuest && mainGuest.togetherCode) {\r\n      mainGuestTogetherCode = mainGuest.togetherCode\r\n    }\r\n  }\r\n\r\n  orderVisible.value.togetherCode = mainGuestTogetherCode\r\n  tabName.value = val\r\n  entryAccountVisible.value = true\r\n}\r\n\r\n/**\r\n * 获取订单状态的显示文本\r\n * @param state 订单状态代码\r\n * @returns 状态显示文本\r\n */\r\nfunction getOrderStatusText(state: string): string {\r\n  return t(`orderStatus.${state}`) || state\r\n}\r\n\r\n/**\r\n * 获取订单状态的标签类型\r\n * @param state 订单状态代码\r\n * @returns Element Plus 标签类型\r\n */\r\nfunction getOrderStatusType(state: string): 'success' | 'info' | 'warning' | 'danger' | '' {\r\n  switch (state) {\r\n    case OrderState.CHECK_IN:\r\n      return 'success'\r\n    case OrderState.CHECK_OUT:\r\n      return 'info'\r\n    case OrderState.IN_BOOKING:\r\n      return 'warning'\r\n    case OrderState.CANCEL:\r\n    case OrderState.REFUSE:\r\n    case OrderState.NOSHOW:\r\n      return 'danger'\r\n    case OrderState.OVER:\r\n    case OrderState.BE_CONFIRM:\r\n      return ''\r\n    case OrderState.CREDIT:\r\n    case OrderState.CONTINUE:\r\n      return 'warning'\r\n    default:\r\n      return ''\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <search-bar :show-toggle=\"false\">\r\n        <el-form :model=\"data.search\" size=\"default\" label-width=\"80px\" inline-message inline class=\"search-form\">\r\n          <!-- 快速筛选区域 -->\r\n          <div class=\"filter-row quick-filter-row\">\r\n            <el-form-item :label=\"t('search.quickFilter')\">\r\n              <el-radio-group v-model=\"data.search.state\" @change=\"currentChange()\">\r\n                <el-radio-button label=\"-1\" value=\"-1\">\r\n                  {{ t('search.all') }}\r\n                </el-radio-button>\r\n                <el-radio-button label=\"0\" value=\"0\">\r\n                  {{ t('search.todayCheckout') }}\r\n                </el-radio-button>\r\n                <el-radio-button label=\"1\" value=\"1\">\r\n                  {{ t('search.todayCheckin') }}\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('search.preciseSearch')\">\r\n              <el-input v-model=\"data.search.searchContent\" class=\"filter-select w-350px!\" clearable :placeholder=\"t('search.searchPlaceholder')\" @clear=\"currentChange()\" @keydown.enter=\"currentChange()\">\r\n                <template #append>\r\n                  <el-button :icon=\"Search\" @click=\"currentChange()\" />\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <!-- 筛选条件行 -->\r\n          <div class=\"filter-row filter-items-row\">\r\n            <el-form-item :label=\"t('search.channel')\">\r\n              <el-select v-model=\"data.search.channelCode\" clearable class=\"filter-select\">\r\n                <el-option :label=\"t('search.all')\" value=\"-1\" />\r\n                <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('search.guestSourceType')\">\r\n              <el-select v-model=\"data.search.guestSrcType\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in srcTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('search.roomType')\">\r\n              <el-select v-model=\"data.search.rtCode\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"filter-row filter-items-row\">\r\n            <el-form-item :label=\"t('search.checkinType')\">\r\n              <el-select v-model=\"data.search.checkinType\" clearable class=\"filter-select\" :placeholder=\"t('search.select')\">\r\n                <el-option v-for=\"item in checkinTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('date.time')\" class=\"date-picker-item\">\r\n              <el-select v-model=\"data.search.dataType\" style=\"width: 100px\" :placeholder=\"t('search.select')\">\r\n                <el-option :label=\"t('date.checkInTime')\" value=\"1\" />\r\n                <el-option :label=\"t('date.planCheckoutTime')\" value=\"0\" />\r\n              </el-select>\r\n              <el-date-picker\r\n                v-model=\"data.search.dataTime\"\r\n                :disabled=\"!data.search.dataType\"\r\n                type=\"daterange\"\r\n                class=\"date-picker\"\r\n                :range-separator=\"t('date.rangeSeparator')\"\r\n                :start-placeholder=\"t('date.startPlaceholder')\"\r\n                :end-placeholder=\"t('date.endPlaceholder')\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item class=\"search-btn\">\r\n              <el-button type=\"primary\" @click=\"getDataList\">\r\n                {{ t('button.query') }}\r\n              </el-button>\r\n            </el-form-item>\r\n            <el-form-item class=\"switch-form-item\">\r\n              <div class=\"switch-container\">\r\n                <el-switch\r\n                  v-model=\"data.search.displayWay\"\r\n                  active-value=\"1\"\r\n                  inactive-value=\"0\"\r\n                  :active-text=\"t('search.guestMode')\"\r\n                  :inactive-text=\"t('search.orderMode')\"\r\n                  inline-prompt\r\n                  style=\"--el-switch-on-color: #13ce66; --el-switch-off-color: #554dd6\"\r\n                  @change=\"currentChange()\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </search-bar>\r\n\r\n      <el-table v-loading=\"data.loading\" class=\"list-table\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\" @sort-change=\"sortChange\">\r\n        <!-- 展开行列 - 仅在订单模式下显示 -->\r\n        <el-table-column v-if=\"data.search.displayWay === '0'\" type=\"expand\" width=\"50\">\r\n          <template #default=\"{ row }\">\r\n            <div class=\"expand-content\">\r\n              <el-table :data=\"row.togetherList\" border size=\"small\" class=\"expand-table\">\r\n                <el-table-column :label=\"t('table.guest')\" prop=\"name\" min-width=\"100\" />\r\n                <el-table-column :label=\"t('table.phone')\" prop=\"phone\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.phone || '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('table.gender')\" prop=\"sex\" width=\"80\" align=\"center\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"scope.row.sex === '1' ? 'primary' : 'danger'\" size=\"small\">\r\n                      {{ scope.row.sex === '1' ? t('male') : scope.row.sex === '0' ? t('female') : t('unknown') }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('table.isMainGuest')\" prop=\"isMain\" width=\"100\" align=\"center\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"scope.row.isMain === '1' || row.togetherList.length === 1 ? 'success' : 'info'\" size=\"small\">\r\n                      {{ scope.row.isMain === '1' || row.togetherList.length === 1 ? t('mainGuest') : t('companion') }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('table.actualCheckinTime')\" prop=\"checkinTime\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ dayjs(scope.row.checkinTime).format('MM/DD HH:mm') }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('table.actualCheckoutTime')\" prop=\"checkoutTime\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.checkoutTime ? dayjs(scope.row.checkoutTime).format('MM/DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('table.plannedCheckoutTime')\" prop=\"planCheckoutTime\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.planCheckoutTime ? dayjs(scope.row.planCheckoutTime).format('MM/DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('table.status')\" prop=\"state\" width=\"100\" align=\"center\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"getOrderStatusType(scope.row.state)\" size=\"small\">\r\n                      {{ getOrderStatusText(scope.row.state) }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column :label=\"t('table.orderNumber')\" min-width=\"200px\">\r\n          <template #default=\"scope\">\r\n            <div class=\"order-info\">\r\n              <div class=\"info-row\">\r\n                <span class=\"label\">{{ t('orderNoLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.orderNo }}</span>\r\n              </div>\r\n              <div v-if=\"scope.row.outOrderNo\" class=\"info-row\">\r\n                <span class=\"label\">{{ t('externalOrderNoLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.outOrderNo }}</span>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('table.guestPhone')\" min-width=\"150px\">\r\n          <template #default=\"scope\">\r\n            <div class=\"guest-info\">\r\n              <div class=\"info-row\">\r\n                <span class=\"label\">{{ t('guestLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.name }}</span>\r\n              </div>\r\n              <div v-if=\"scope.row.phone\" class=\"info-row\">\r\n                <span class=\"label\">{{ t('phoneLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.phone }}</span>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('table.roomType')\" min-width=\"180\">\r\n          <template #default=\"scope\">\r\n            <div class=\"room-info\">\r\n              <span class=\"room-type\">{{ scope.row.rtName }}</span>\r\n              <span class=\"price\">{{ t('currencySymbol') }}{{ scope.row.price ?? '-' }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"rNo\" :label=\"t('table.roomNumber')\" sortable=\"custom\" />\r\n        <el-table-column prop=\"checkinTime\" :label=\"t('table.checkinTime')\" min-width=\"120px\" sortable=\"custom\">\r\n          <template #default=\"scope\">\r\n            {{ dayjs(scope.row.checkinTime).format('MM/DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planCheckoutTime\" :label=\"t('table.checkoutTime')\" min-width=\"120px\" sortable=\"custom\">\r\n          <template #default=\"scope\">\r\n            {{ dayjs(scope.row.planCheckoutTime).format('MM/DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"guestSrcTypeName\" :label=\"t('table.guestSourceType')\" />\r\n        <el-table-column :label=\"t('table.checkinType')\">\r\n          <template #default=\"scope\">\r\n            {{ scope.row.checkinTypeName }}\r\n            <span v-if=\"scope.row.hourToFull === BooleanEnum.YES\"><br />({{ t('table.hourToFull') }})</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"channelName\" :label=\"t('table.channel')\" />\r\n        <el-table-column :label=\"t('table.operation')\" width=\"130\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-link v-auth=\"'pms:order:query:get-order-detail'\" type=\"primary\" @click=\"onEdit(scope.row)\">\r\n              {{ t('table.view') }}\r\n            </el-link>\r\n            <el-link v-auth=\"'pms:account:create'\" type=\"primary\" @click=\"entryAccount('pay', scope.row)\">\r\n              {{ t('table.entryAccount') }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n      <!-- 弹出订单详情窗口 -->\r\n      <OrderDialog v-if=\"data.formModeProps.visible\" v-model=\"data.formModeProps.visible\" :no=\"data.formModeProps.orderNo\" :no-type=\"data.formModeProps.noType\" :tab-name=\"routerName\" :tab-type=\"typeName\" @reload=\"getDataList\" />\r\n      <!--    入账弹窗 -->\r\n      <EntryAccount v-if=\"entryAccountVisible\" v-model=\"entryAccountVisible\" :tab-name=\"tabName\" :order-no=\"orderVisible.accountOrderNo\" :order-type=\"orderVisible.type\" :no-type=\"orderVisible.type\" :order-together-code=\"orderVisible.togetherCode\" />\r\n    </page-main>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 显示方式开关样式 */\r\n.display-way-item {\r\n  margin-left: 30px;\r\n\r\n  :deep(.el-form-item__label) {\r\n    color: #606266;\r\n    font-weight: 500;\r\n  }\r\n\r\n  :deep(.el-switch__label) {\r\n    font-size: 12px;\r\n    color: #606266;\r\n  }\r\n\r\n  :deep(.el-switch__label.is-active) {\r\n    color: #409eff;\r\n  }\r\n}\r\n\r\n/* 表格工具栏样式 */\r\n.table-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  border-bottom: 1px solid #ebeef5;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .display-way-control {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .control-label {\r\n        margin-right: 8px;\r\n        font-size: 14px;\r\n        color: #606266;\r\n        font-weight: 500;\r\n      }\r\n\r\n      :deep(.el-switch__label) {\r\n        font-size: 12px;\r\n        color: #606266;\r\n      }\r\n\r\n      :deep(.el-switch__label.is-active) {\r\n        color: #409eff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .toolbar-right {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n}\r\n/* 展开行样式 */\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f8f9fa;\r\n\r\n  .expand-title {\r\n    margin: 0 0 15px 0;\r\n    color: #303133;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .expand-table {\r\n    :deep(.el-table__header) {\r\n      background-color: #fafafa;\r\n    }\r\n\r\n    :deep(.el-table td) {\r\n      padding: 8px 0;\r\n    }\r\n\r\n    :deep(.el-table th) {\r\n      padding: 10px 0;\r\n      background-color: #fafafa !important;\r\n    }\r\n  }\r\n}\r\n\r\n.el-link {\r\n  margin: 0 10px;\r\n}\r\n\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    width: 100%;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      margin-bottom: 8px;\r\n\r\n      &.quick-filter-row {\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      &.filter-items-row {\r\n        justify-content: flex-start;\r\n        gap: 0;\r\n\r\n        .el-form-item {\r\n          width: auto;\r\n          margin-right: 12px;\r\n          margin-bottom: 8px;\r\n\r\n          &:nth-child(3n) {\r\n            margin-right: 0;\r\n          }\r\n        }\r\n      }\r\n\r\n      .el-form-item {\r\n        margin-bottom: 0;\r\n\r\n        .filter-select {\r\n          width: 200px; /* 减小宽度 */\r\n        }\r\n\r\n        /* 减小表单项内部元素的垂直间距 */\r\n        :deep(.el-form-item__label) {\r\n          white-space: nowrap;\r\n          min-width: 80px;\r\n          width: auto !important;\r\n        }\r\n\r\n        :deep(.el-form-item__content) {\r\n          line-height: 28px;\r\n        }\r\n      }\r\n\r\n      .date-type-item {\r\n        width: auto !important;\r\n\r\n        .date-type-select {\r\n          width: 150px;\r\n        }\r\n      }\r\n\r\n      &.filter-items-row {\r\n        justify-content: flex-start;\r\n        gap: 0;\r\n\r\n        .el-form-item {\r\n          width: auto;\r\n          margin-right: 12px;\r\n          margin-bottom: 8px;\r\n\r\n          &:nth-child(3n) {\r\n            margin-right: 0;\r\n          }\r\n\r\n          &.search-input-item,\r\n          &.search-btn {\r\n            :deep(.el-form-item__content) {\r\n              display: flex;\r\n              align-items: center;\r\n              gap: 8px;\r\n            }\r\n          }\r\n\r\n          &.switch-form-item {\r\n            flex: 1;\r\n            display: flex;\r\n            justify-content: flex-end;\r\n\r\n            :deep(.el-form-item__content) {\r\n              display: flex;\r\n              justify-content: flex-end;\r\n              width: 100%;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n\r\n  .pagination {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n.flex-form {\r\n  display: flex;\r\n}\r\n\r\n.list-table {\r\n  .order-info,\r\n  .guest-info {\r\n    .info-row {\r\n      display: flex;\r\n      align-items: center;\r\n      line-height: 20px;\r\n      margin-bottom: 4px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      .label {\r\n        color: #909399;\r\n        flex-shrink: 0;\r\n        white-space: nowrap; // 防止换行\r\n      }\r\n\r\n      .value {\r\n        flex: 1;\r\n      }\r\n    }\r\n  }\r\n\r\n  .room-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    .room-type {\r\n      color: #606266;\r\n    }\r\n\r\n    .price {\r\n      color: #f56c6c;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "data", "ref", "loading", "tableAutoHeight", "formMode", "formModeProps", "visible", "orderNo", "noType", "NoType", "ORDER", "search", "state", "channelCode", "rtCode", "guestSrcType", "checkinType", "seller", "searchType", "searchContent", "dataType", "dataTime", "displayWay", "dataList", "onMounted", "params", "gcode", "hcode", "isEnable", "BooleanEnum", "YES", "channelApi", "getChannelSimpleList", "then", "res", "code", "channels", "value", "getChannels", "isVirtual", "NO", "rtApi", "getRoomTypeSimpleList", "rts", "getRts", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "srcTypeList", "filter", "item", "dictType", "DICT_TYPE_GUEST_SRC_TYPE", "checkinTypeList", "DICT_TYPE_CHECKIN_TYPE", "getDataList", "userApi", "listSeller", "sellers", "start", "end", "length", "dayjs", "format", "quickFilter", "key<PERSON>ords", "timeType", "startTime", "endTime", "orderApi", "orderPagerList", "list", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "routerName", "typeName", "entryAccountVisible", "orderVisible", "accountOrderNo", "togetherCode", "type", "tabName", "getOrderStatusType", "OrderState", "CHECK_IN", "CHECK_OUT", "IN_BOOKING", "CANCEL", "REFUSE", "NOSHOW", "OVER", "BE_CONFIRM", "CREDIT", "CONTINUE", "row", "val", "mainGuestTogetherCode", "togetherList", "mainGuest", "find", "guest", "is<PERSON><PERSON>"], "mappings": "gsLA0PM,MAAAA,EAAEA,IAAMC,IAERC,GAAYC,KACZC,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,mBAAcC,GAAiBC,aAAAA,IAAiBC,KAEzEC,GAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAOjBC,SAAU,SAEVC,cAAe,CACbC,SAAS,EAETC,QAAS,GACTC,OAAQC,EAAOC,OAGjBC,OAAQ,CAENC,MAAO,KAEPC,YAAa,KAEbC,OAAQ,GAERC,aAAc,GAEdC,YAAa,GAEbC,OAAQ,GAERC,WAAY,IACZC,cAAe,GAEfC,SAAU,IACVC,SAAU,GAEVC,WAAY,KAIdC,SAAU,KAGZC,GAAU,MAkBV,WACE,MAAMC,EAAS,CACbC,MAAOlC,GAAUkC,MACjBC,MAAOnC,GAAUmC,MACjBC,SAAUC,EAAYC,KAExBC,EAAWC,qBAAqBP,GAAQQ,MAAMC,IAC3B,IAAbA,EAAIC,OACNC,GAASC,MAAQH,EAAIlC,KAAA,GAExB,CA3BWsC,GAgCd,WACE,MAAMb,EAAS,CACbC,MAAOlC,GAAUkC,MACjBC,MAAOnC,GAAUmC,MACjBY,UAAWV,EAAYW,GACvBZ,SAAUC,EAAYC,KAExBW,EAAMC,sBAAsBjB,GAAQQ,MAAMC,IACvB,IAAbA,EAAIC,OACNQ,GAAIN,MAAQH,EAAIlC,KAAA,GAEnB,CA1CM4C,GAoDPC,EAAYC,iBAAiBC,IAAWd,MAAMC,IAChCc,GAAAX,MAAQH,EAAIlC,KAAKiD,QAAQC,GAAcA,EAAKC,WAAaC,IACrDC,GAAAhB,MAAQH,EAAIlC,KAAKiD,QAAQC,GAAcA,EAAKC,WAAaG,GAAsB,IApDrFC,KAOZC,EAAQC,WAAW,CAAE/B,MAAOlC,GAAUkC,MAAOC,MAAOnC,GAAUmC,QAASM,MAAMC,IAC3EwB,GAAQrB,MAAQH,EAAIlC,IAAA,GAPZ,IAIN,MAAA0D,GAAUzD,EAA8C,IAQxD,MAAAmC,GAAWnC,EAAoD,IAe/D,MAAA0C,GAAM1C,EAA0C,IAgBhD,MAAA8C,GAAY,CAACK,EAA0BE,GAEvCN,GAAc/C,EAAuC,IAErDoD,GAAkBpD,EAAuC,IAQ/D,SAASsD,KACP,IAAII,EAAQ,GACRC,EAAM,GACN5D,GAAKqC,MAAM1B,OAAOU,UAAYrB,GAAKqC,MAAM1B,OAAOU,SAASwC,OAAS,IAC5DF,EAAAG,EAAM9D,GAAKqC,MAAM1B,OAAOU,SAAS,IAAI0C,OAAO,uBAC9CH,EAAAE,EAAM9D,GAAKqC,MAAM1B,OAAOU,SAAS,IAAI0C,OAAO,wBAGpD,MAAMtC,EAAS,IACV9B,KACH+B,MAAOlC,GAAUkC,MACjBC,MAAOnC,GAAUmC,MACjBqC,YAAyC,OAA5BhE,GAAKqC,MAAM1B,OAAOC,MAAiB,GAAKZ,GAAKqC,MAAM1B,OAAOC,MACvEqD,SAAUjE,GAAKqC,MAAM1B,OAAOQ,cAC5BN,YAA+C,OAAlCb,GAAKqC,MAAM1B,OAAOE,YAAuB,GAAKb,GAAKqC,MAAM1B,OAAOE,YAC7EqD,SAAUlE,GAAKqC,MAAM1B,OAAOS,SAC5B+C,UAAWR,EACXS,QAASR,EACT7C,aAAcf,GAAKqC,MAAM1B,OAAOI,aAChCD,OAAQd,GAAKqC,MAAM1B,OAAOG,OAC1BE,YAAahB,GAAKqC,MAAM1B,OAAOK,YAC/BM,WAAYtB,GAAKqC,MAAM1B,OAAOW,YAEhCtB,GAAKqC,MAAMnC,SAAU,EACrBmE,EAASC,eAAe7C,GAAQQ,MAAMC,IACpClC,GAAKqC,MAAMnC,SAAU,EACJ,IAAbgC,EAAIC,OACDnC,GAAAqC,MAAMd,SAAWW,EAAIlC,KAAKuE,KACpB7E,GAAA2C,MAAMmC,MAAQtC,EAAIlC,KAAKwE,MAAA,GAErC,CAIH,SAASC,GAAWC,GAClB9E,GAAa8E,GAAMzC,MAAK,IAAMsB,MAAa,CAIpC,SAAAoB,GAAcC,EAAO,GAC5B/E,GAAgB+E,GAAM3C,MAAK,IAAMsB,MAAa,CAIhD,SAASsB,IAAWC,KAAEA,EAAMC,MAAAA,IAC1BjF,GAAagF,EAAMC,GAAO9C,MAAK,IAAMsB,MAAa,CAG9C,MAAAyB,GAAa/E,EAAI,UACjBgF,GAAWhF,EAAI,cAOf,MAAAiF,GAAsBjF,GAAI,GAC1BkF,GAAelF,EAAI,CACvBmF,eAAgB,GAChBC,aAAc,GACdC,KAAM,UAEFC,GAAUtF,EAAI,IAgCpB,SAASuF,GAAmB5E,GAC1B,OAAQA,GACN,KAAK6E,EAAWC,SACP,MAAA,UACT,KAAKD,EAAWE,UACP,MAAA,OACT,KAAKF,EAAWG,WACP,MAAA,UACT,KAAKH,EAAWI,OAChB,KAAKJ,EAAWK,OAChB,KAAKL,EAAWM,OACP,MAAA,SACT,KAAKN,EAAWO,KAChB,KAAKP,EAAWQ,WACP,MAAA,GACT,KAAKR,EAAWS,OAChB,KAAKT,EAAWU,SACP,MAAA,UACT,QACS,MAAA,GACX,w4MA7B0BvF,cACnBtB,GAAE,eAAesB,MAAYA,QADtC,IAA4BA,y4DAlCZwF,QACTpG,GAAAqC,MAAMhC,cAAcE,QAAU6F,EAAI7F,aAClCP,GAAAqC,MAAMhC,cAAcC,SAAU,GAFrC,IAAgB8F,8JAYP,SAAaC,EAAaD,GACpBjB,GAAA9C,MAAM+C,eAAiBgB,EAAI7F,QAGxC,IAAI+F,EAAwBF,EAAIf,aAChC,GAAIe,EAAIG,cAAgBH,EAAIG,aAAa1C,OAAS,EAAG,CAC7C,MAAA2C,EAAYJ,EAAIG,aAAaE,MAAMC,GAAgC,MAAjBA,EAAMC,SAC1DH,GAAaA,EAAUnB,eACzBiB,EAAwBE,EAAUnB,aACpC,CAGFF,GAAa9C,MAAMgD,aAAeiB,EAClCf,GAAQlD,MAAQgE,EAChBnB,GAAoB7C,OAAQ,CAAA"}