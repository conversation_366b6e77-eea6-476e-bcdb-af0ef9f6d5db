{"version": 3, "file": "createBrokerage-BRc65N9n.js", "sources": ["../../src/views/group/channels/brokerage/components/DetailForm/createBrokerage.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"strategyInfo\": \"Strategy Info\",\r\n      \"companyType\": \"Company Type\",\r\n      \"agreementUnit\": \"Agreement Unit\",\r\n      \"broker\": \"Broker\",\r\n      \"strategyName\": \"Name\",\r\n      \"commissionLevel\": \"Level\",\r\n      \"rebateType\": \"Rebate Type\",\r\n      \"perNight\": \"Per Night\",\r\n      \"fixedRebate\": \"Fixed Rebate\",\r\n      \"percentageRebate\": \"Percentage Rebate\",\r\n      \"strategyScope\": \"Strategy Scope\",\r\n      \"applyChannel\": \"Channels\",\r\n      \"applyRoomType\": \"Room Type\",\r\n      \"applyHotel\": \"Hotels\",\r\n      \"status\": \"Status\",\r\n      \"enabled\": \"Enabled\",\r\n      \"disabled\": \"Disabled\",\r\n      \"remark\": \"Remark\",\r\n      \"pleaseEnterStrategyName\": \"Please enter strategy name\",\r\n      \"pleaseSelectRoomType\": \"Please select room type\",\r\n      \"pleaseSelectChannel\": \"Please select channel\",\r\n      \"pleaseSelectHotel\": \"Please select hotel\",\r\n      \"pleaseEnterRemark\": \"Please enter remark\",\r\n      \"successfulUpdate\": \"Update successful\",\r\n      \"errorOccurred\": \"An error occurred\",\r\n      \"strategyRule\": \"Strategy Rules\",\r\n      \"yuan\": \"$\",\r\n      \"hotelRoomType\": \"Hotel Room Type\",\r\n      \"groupRoomType\": \"Group Room Type\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"strategyInfo\": \"策略信息\",\r\n      \"companyType\": \"公司类型\",\r\n      \"agreementUnit\": \"协议单位\",\r\n      \"broker\": \"中介\",\r\n      \"strategyName\": \"策略名称\",\r\n      \"commissionLevel\": \"佣金级别\",\r\n      \"rebateType\": \"返佣类型\",\r\n      \"perNight\": \"按间夜\",\r\n      \"fixedRebate\": \"定额反佣\",\r\n      \"percentageRebate\": \"百分比返佣\",\r\n      \"strategyScope\": \"策略应用范围\",\r\n      \"applyChannel\": \"应用渠道\",\r\n      \"applyRoomType\": \"应用房型\",\r\n      \"applyHotel\": \"应用酒店\",\r\n      \"status\": \"状态\",\r\n      \"enabled\": \"有效\",\r\n      \"disabled\": \"无效\",\r\n      \"remark\": \"备注\",\r\n      \"pleaseEnterStrategyName\": \"请输入策略名称\",\r\n      \"pleaseSelectRoomType\": \"请选择房型\",\r\n      \"pleaseSelectChannel\": \"请选择渠道\",\r\n      \"pleaseSelectHotel\": \"请选择适用门店\",\r\n      \"pleaseEnterRemark\": \"请输入备注\",\r\n      \"successfulUpdate\": \"修改成功\",\r\n      \"errorOccurred\": \"发生错误\",\r\n      \"strategyRule\": \"策略规则\",\r\n      \"yuan\": \"元\",\r\n      \"hotelRoomType\": \"酒店房型\",\r\n      \"groupRoomType\": \"集团房型\"\r\n    },\r\n    \"km\": {\r\n      \"strategyInfo\": \"ព័ត៌មានអំពីយុទ្ធសាស្ត្រ\",\r\n      \"companyType\": \"ប្រភេទក្រុមហ៊ុន\",\r\n      \"agreementUnit\": \"អង្គភាពសន្យា\",\r\n      \"broker\": \"ភ្នាក់ងារ\",\r\n      \"strategyName\": \"ឈ្មោះយុទ្ធសាស្ត្រ\",\r\n      \"commissionLevel\": \"កម្រិតកំរៃជើងសា\",\r\n      \"rebateType\": \"ប្រភេទកំរៃជើងសា\",\r\n      \"perNight\": \"ក្នុងមួយយប់\",\r\n      \"fixedRebate\": \"កំរៃជើងសាថេរ\",\r\n      \"percentageRebate\": \"កំរៃជើងសាគិតជាភាគរយ\",\r\n      \"strategyScope\": \"វិសាលភាពយុទ្ធសាស្ត្រ\",\r\n      \"applyChannel\": \"ឆានែលអនុវត្ត\",\r\n      \"applyRoomType\": \"ប្រភេទបន្ទប់អនុវត្ត\",\r\n      \"applyHotel\": \"សណ្ឋាគារអនុវត្ត\",\r\n      \"status\": \"ស្ថានភាព\",\r\n      \"enabled\": \"សកម្ម\",\r\n      \"disabled\": \"អសកម្ម\",\r\n      \"remark\": \"ចំណាំ\",\r\n      \"pleaseEnterStrategyName\": \"សូមបញ្ចូលឈ្មោះយុទ្ធសាស្ត្រ\",\r\n      \"pleaseSelectRoomType\": \"សូមជ្រើសរើសប្រភេទបន្ទប់\",\r\n      \"pleaseSelectChannel\": \"សូមជ្រើសរើសឆានែល\",\r\n      \"pleaseSelectHotel\": \"សូមជ្រើសរើសសណ្ឋាគារ\",\r\n      \"pleaseEnterRemark\": \"សូមបញ្ចូលចំណាំ\",\r\n      \"successfulUpdate\": \"កែសម្រួលជោគជ័យ\",\r\n      \"errorOccurred\": \"មានកំហុសកើតឡើង\",\r\n      \"strategyRule\": \"ច្បាប់យុទ្ធសាស្ត្រ\",\r\n      \"yuan\": \"រៀល\",\r\n      \"hotelRoomType\": \"ប្រភេទបន្ទប់សណ្ឋាគារ\",\r\n      \"groupRoomType\": \"ប្រភេទបន្ទប់ក្រុម\"\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { ChannelModel, RoomTypeModel } from '@/models/index'\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { brokerageStrategyApi, channelApi, dictDataApi, merchantApi, rtApi } from '@/api/modules/index'\r\nimport { BooleanEnum, BROKERAGE_LEVEL } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  isG: BooleanEnum.NO,\r\n  hotels: [],\r\n  strategyCode: '',\r\n  strategyName: '',\r\n  companyType: '0',\r\n  isGrt: BooleanEnum.YES,\r\n  brokerageLevelCode: '',\r\n  rts: [],\r\n  brokerageType: '0',\r\n  brokerageValue: 0,\r\n  channels: [],\r\n  isEnable: BooleanEnum.YES,\r\n  remark: '',\r\n})\r\nconst mission = ref('1')\r\nconst formRules = ref<FormRules>({\r\n  strategyName: [{ required: true, message: t('pleaseEnterStrategyName'), trigger: 'blur' }],\r\n})\r\nonMounted(() => {\r\n  getChannels()\r\n  getRts()\r\n  getMerchants()\r\n  getConstants()\r\n})\r\n\r\nconst dictTypes = [BROKERAGE_LEVEL]\r\nconst commissionLevel = ref<{ value: string; label: string }[]>([])\r\n\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    commissionLevel.value = res.data.filter((item: any) => item.dictType === BROKERAGE_LEVEL)\r\n  })\r\n}\r\n\r\nconst channels = ref<ChannelModel[]>([])\r\n\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    isG: BooleanEnum.YES,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst rts = ref<RoomTypeModel[]>([])\r\n\r\nfunction getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    isVirtual: BooleanEnum.NO,\r\n    isGRt: BooleanEnum.YES,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst merchants = ref<{ hcode: string; hname: string }[]>([])\r\n\r\nfunction getMerchants() {\r\n  merchantApi.getSimpleList(userStore.gcode).then((res: any) => {\r\n    merchants.value = res.data\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            brokerageStrategyApi.createBrokerageStrategy(form.value).then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success({\r\n                  message: t('successfulUpdate'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              } else {\r\n                ElMessage.error({\r\n                  message: res.msg,\r\n                  center: true,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n\r\nfunction changeType() {\r\n  form.value.brokerageValue = 0\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"140px\" label-suffix=\"：\">\r\n      <el-divider content-position=\"left\">\r\n        {{ t('strategyInfo') }}\r\n      </el-divider>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('companyType')\">\r\n            <el-radio-group v-model=\"form.companyType\">\r\n              <el-radio value=\"0\" size=\"large\">\r\n                {{ t('broker') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('strategyName')\" prop=\"strategyName\">\r\n            <el-input v-model=\"form.strategyName\" :placeholder=\"t('pleaseEnterStrategyName')\" maxlength=\"30\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :md=\"12\">\r\n          <el-form-item :label=\"t('commissionLevel')\">\r\n            <el-select v-model=\"form.brokerageLevelCode\" collapse-tags collapse-tags-tooltip :placeholder=\"t('pleaseSelectRoomType')\" style=\"width: 300px\">\r\n              <el-option v-for=\"item in commissionLevel\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('strategyRule') }}\r\n      </el-divider>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('rebateType')\">\r\n            <el-radio-group v-model=\"mission\">\r\n              <el-radio value=\"1\" size=\"large\">\r\n                {{ t('perNight') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"\">\r\n            <div style=\"display: flex\">\r\n              <el-select v-model=\"form.brokerageType\" style=\"width: 200px\" @change=\"changeType\">\r\n                <el-option :label=\"t('fixedRebate')\" value=\"0\" />\r\n                <el-option :label=\"t('percentageRebate')\" value=\"1\" />\r\n              </el-select>\r\n              <el-input-number v-if=\"form.brokerageType === '0'\" v-model=\"form.brokerageValue\" :max=\"10000\" :min=\"0\" controls-position=\"right\" /><span v-if=\"form.brokerageType === '0'\" style=\"margin-left: 5px\">{{ t('yuan') }}</span>\r\n              <el-input-number v-if=\"form.brokerageType === '1'\" v-model=\"form.brokerageValue\" :max=\"1\" :min=\"0\" :step=\"0.01\" :precision=\"2\" controls-position=\"right\" /><span v-if=\"form.brokerageType === '1'\" style=\"margin-left: 5px\">%</span>\r\n            </div>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-divider content-position=\"left\">\r\n        {{ t('strategyScope') }}\r\n      </el-divider>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('applyChannel')\">\r\n            <el-select v-model=\"form.channels\" multiple collapse-tags-tooltip clearable :placeholder=\"t('pleaseSelectChannel')\">\r\n              <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('applyRoomType')\">\r\n            <el-radio-group v-model=\"form.isGrt\">\r\n              <el-radio value=\"0\" size=\"large\" disabled>\r\n                {{ t('hotelRoomType') }}\r\n              </el-radio>\r\n              <el-radio value=\"1\" size=\"large\">\r\n                {{ t('groupRoomType') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"\">\r\n            <el-select v-model=\"form.rts\" multiple collapse-tags collapse-tags-tooltip clearable :placeholder=\"t('pleaseSelectRoomType')\">\r\n              <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('applyHotel')\">\r\n            <el-select v-model=\"form.hotels\" multiple collapse-tags clearable collapse-tags-tooltip :placeholder=\"t('pleaseSelectHotel')\">\r\n              <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.hname\" :value=\"item.hcode\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('status')\">\r\n            <el-radio-group v-model=\"form.isEnable\">\r\n              <el-radio value=\"1\" size=\"large\">\r\n                {{ t('enabled') }}\r\n              </el-radio>\r\n              <el-radio value=\"0\" size=\"large\">\r\n                {{ t('disabled') }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"24\">\r\n        <el-col :md=\"24\">\r\n          <el-form-item :label=\"t('remark')\">\r\n            <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('pleaseEnterRemark')\" maxlength=\"250\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "gcode", "isG", "BooleanEnum", "NO", "hotels", "strategyCode", "strategyName", "companyType", "isGrt", "YES", "brokerageLevelCode", "rts", "brokerageType", "brokerageValue", "channels", "isEnable", "remark", "mission", "formRules", "required", "message", "trigger", "onMounted", "params", "channelApi", "getChannelSimpleList", "then", "res", "code", "value", "data", "getChannels", "isVirtual", "isGRt", "rtApi", "getRoomTypeSimpleList", "getRts", "merchantApi", "getSimpleList", "merchants", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "commissionLevel", "filter", "item", "dictType", "BROKERAGE_LEVEL", "changeType", "__expose", "submit", "Promise", "resolve", "validate", "valid", "brokerageStrategyApi", "createBrokerageStrategy", "ElMessage", "success", "center", "error", "msg"], "mappings": "msCA4GM,MAAAA,EAAEA,GAAMC,IAERC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,MAAON,EAAUM,MACjBC,IAAKC,EAAYC,GACjBC,OAAQ,GACRC,aAAc,GACdC,aAAc,GACdC,YAAa,IACbC,MAAON,EAAYO,IACnBC,mBAAoB,GACpBC,IAAK,GACLC,cAAe,IACfC,eAAgB,EAChBC,SAAU,GACVC,SAAUb,EAAYO,IACtBO,OAAQ,KAEJC,EAAUpB,EAAI,KACdqB,EAAYrB,EAAe,CAC/BS,aAAc,CAAC,CAAEa,UAAU,EAAMC,QAAS5B,EAAE,2BAA4B6B,QAAS,WAEnFC,GAAU,MAkBV,WACE,MAAMC,EAAS,CACbvB,MAAON,EAAUM,MACjBC,IAAKC,EAAYO,KAEnBe,EAAWC,qBAAqBF,GAAQG,MAAMC,IAC3B,IAAbA,EAAIC,OACNd,EAASe,MAAQF,EAAIG,KAAA,GAExB,CA1BWC,GA+Bd,WACE,MAAMR,EAAS,CACbvB,MAAON,EAAUM,MACjBgC,UAAW9B,EAAYC,GACvB8B,MAAO/B,EAAYO,IACnBM,SAAUb,EAAYO,KAExByB,EAAMC,sBAAsBZ,GAAQG,MAAMC,IACvB,IAAbA,EAAIC,OACNjB,EAAIkB,MAAQF,EAAIG,KAAA,GAEnB,CAzCMM,GA+CPC,EAAYC,cAAc5C,EAAUM,OAAO0B,MAAMC,IAC/CY,GAAUV,MAAQF,EAAIG,IAAA,IAvCxBU,EAAYC,iBAAiBC,GAAWhB,MAAMC,IAC5BgB,EAAAd,MAAQF,EAAIG,KAAKc,QAAQC,GAAcA,EAAKC,WAAaC,GAAe,GAR7E,IAGT,MAAAL,EAAY,CAACK,GACbJ,EAAkB9C,EAAwC,IAQ1D,MAAAiB,EAAWjB,EAAoB,IAc/B,MAAAc,EAAMd,EAAqB,IAgB3B,MAAA0C,GAAY1C,EAAwC,IAkC1D,SAASmD,KACPjD,EAAK8B,MAAMhB,eAAiB,CAAA,QA3BjBoC,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBtD,EAAQ+B,OACN/B,EAAQ+B,MAAMwB,UAAUC,IAClBA,GACFC,EAAqBC,wBAAwBzD,EAAK8B,OAAOH,MAAMC,IAC5C,IAAbA,EAAIC,MACN6B,EAAUC,QAAQ,CAChBtC,QAAS5B,EAAE,oBACXmE,QAAQ,IAEFP,KAERK,EAAUG,MAAM,CACdxC,QAASO,EAAIkC,IACbF,QAAQ,GACT,GAEJ,GAEJ"}