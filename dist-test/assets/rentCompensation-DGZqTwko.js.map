{"version": 3, "file": "rentCompensation-DGZqTwko.js", "sources": ["../../src/views/room/goods/rent/components/DetailForm/rentCompensation.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"rentInformation\": \"Rent Information\",\r\n    \"compensationInformation\": \"Compensation Information\",\r\n    \"rentVoucherNumber\": \"Rent Voucher No\",\r\n    \"rentItem\": \"Rent Item\",\r\n    \"roomNumber\": \"Room Number\",\r\n    \"guestName\": \"Guest Name\",\r\n    \"rent\": \"Rent\",\r\n    \"yuan\": \"Yuan\",\r\n    \"rentRemark\": \"Rent Remark\",\r\n    \"none\": \"None\",\r\n    \"compensationPersonName\": \"Compensation Person Name\",\r\n    \"compensationAmount\": \"Compensation Amount\",\r\n    \"accountingMethod\": \"Accounting Method\",\r\n    \"compensationRemark\": \"Compensation Remark\",\r\n    \"roomAccount\": \"Room Account\",\r\n    \"pleaseEnterCompensationPersonName\": \"Please enter compensation person name\",\r\n    \"pleaseEnterCompensationRemark\": \"Please enter compensation remark, such as item damage condition, compensation reason, etc.\",\r\n    \"compensationSuccess\": \"Compensation successful\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"rentInformation\": \"租借信息\",\r\n    \"compensationInformation\": \"赔偿信息\",\r\n    \"rentVoucherNumber\": \"租借凭证号\",\r\n    \"rentItem\": \"租借物品\",\r\n    \"roomNumber\": \"房号\",\r\n    \"guestName\": \"客人姓名\",\r\n    \"rent\": \"租金\",\r\n    \"yuan\": \"元\",\r\n    \"rentRemark\": \"租借备注\",\r\n    \"none\": \"无\",\r\n    \"compensationPersonName\": \"赔偿人姓名\",\r\n    \"compensationAmount\": \"赔偿金额\",\r\n    \"accountingMethod\": \"入账方式\",\r\n    \"compensationRemark\": \"赔偿备注\",\r\n    \"roomAccount\": \"挂房账\",\r\n    \"pleaseEnterCompensationPersonName\": \"请输入赔偿人姓名\",\r\n    \"pleaseEnterCompensationRemark\": \"请输入赔偿备注，如物品损坏情况、赔偿原因等\",\r\n    \"compensationSuccess\": \"赔偿成功\"\r\n  },\r\n  \"km\": {\r\n    \"rentInformation\": \"ព័ត៌មានអំពីការជួល\",\r\n    \"compensationInformation\": \"ព័ត៌មានអំពីការបង់ប្រាក់សំណង\",\r\n    \"rentVoucherNumber\": \"លេខប័ណ្ណជួល\",\r\n    \"rentItem\": \"វត្ថុជួល\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"guestName\": \"ឈ្មោះភ្ញៀវ\",\r\n    \"rent\": \"តម្លៃជួល\",\r\n    \"yuan\": \"យួន\",\r\n    \"rentRemark\": \"ចំណាំអំពីការជួល\",\r\n    \"none\": \"គ្មាន\",\r\n    \"compensationPersonName\": \"ឈ្មោះអ្នកបង់សំណង\",\r\n    \"compensationAmount\": \"ចំនួនទឹកប្រាក់សំណង\",\r\n    \"accountingMethod\": \"វិធីសារពើភ័ណ្ឌ\",\r\n    \"compensationRemark\": \"ចំណាំអំពីការបង់សំណង\",\r\n    \"roomAccount\": \"គណនីបន្ទប់\",\r\n    \"pleaseEnterCompensationPersonName\": \"សូមបញ្ចូលឈ្មោះអ្នកបង់សំណង\",\r\n    \"pleaseEnterCompensationRemark\": \"សូមបញ្ចូលចំណាំអំពីការបង់សំណង ដូចជាស្ថានភាពវត្ថុខូច ឬមូលហេតុសំណងជាដើម\",\r\n    \"compensationSuccess\": \"បានបង់សំណងដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { rentGoodsApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { ElMessage } from 'element-plus'\r\nimport { onMounted, ref } from 'vue'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  id: 0,\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\n\r\n// 租借信息（只读）\r\nconst rentInfo = ref({\r\n  /** 租借凭证号 */\r\n  rentNo: '',\r\n  /** 租借物品名称 */\r\n  rentName: '',\r\n  /** 房号 */\r\n  rNo: '',\r\n  /** 客人姓名 */\r\n  name: '',\r\n  /** 租金 */\r\n  rentPrice: 0,\r\n  /** 租借备注 */\r\n  remark: '',\r\n  /** 赔偿金额 */\r\n  indemnityPrice: 0,\r\n})\r\n\r\n// 赔偿信息（可编辑）\r\nconst compensationForm = ref({\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n  id: props.id,\r\n  /** 赔偿人姓名 */\r\n  compensationName: '',\r\n  /** 入账方式 - 写死为挂房账 */\r\n  accountingMethod: t('roomAccount'),\r\n  /** 赔偿金额 */\r\n  compensationPrice: 0,\r\n  /** 赔偿备注 */\r\n  compensationRemark: '',\r\n  /** 状态更新为已赔偿 */\r\n  state: '2',\r\n})\r\n\r\nconst formRules = ref<FormRules>({\r\n  compensationName: [{ required: true, message: t('pleaseEnterCompensationPersonName'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {\r\n  if (props.id !== 0) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\nfunction getInfo() {\r\n  loading.value = true\r\n  rentGoodsApi.getRentGoods(props.id).then((res: any) => {\r\n    loading.value = false\r\n    // 将获取的数据填充到租借信息中（只读）\r\n    Object.assign(rentInfo.value, res.data)\r\n    // 将赔偿金额设置为获取到的indemnityPrice值\r\n    compensationForm.value.compensationPrice = res.data.indemnityPrice || 0\r\n    // 默认将客人姓名设置为赔偿人姓名\r\n    compensationForm.value.compensationName = res.data.name || ''\r\n  })\r\n}\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            // 调用赔偿API\r\n            rentGoodsApi\r\n              .indemnityRentGoods({\r\n                ...compensationForm.value,\r\n                id: props.id,\r\n              })\r\n              .then(() => {\r\n                ElMessage.success({\r\n                  message: t('compensationSuccess'),\r\n                  center: true,\r\n                })\r\n                resolve()\r\n              })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <!-- 租借信息（只读） -->\r\n    <div class=\"section-title\">\r\n      {{ t('rentInformation') }}\r\n    </div>\r\n    <el-form label-width=\"150px\" label-suffix=\"：\" class=\"readonly-form\">\r\n      <el-form-item :label=\"t('rentVoucherNumber')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rentNo }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rentItem')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rentName }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('roomNumber')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rNo }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('guestName')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.name }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rent')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.rentPrice }} {{ t('yuan') }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('rentRemark')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.remark || t('none') }}</span>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 分割线 -->\r\n    <el-divider />\r\n\r\n    <!-- 赔偿信息（可编辑） -->\r\n    <div class=\"section-title\">\r\n      {{ t('compensationInformation') }}\r\n    </div>\r\n    <el-form ref=\"formRef\" :model=\"compensationForm\" :rules=\"formRules\" label-width=\"160px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('compensationPersonName')\" prop=\"compensationName\">\r\n        <el-input v-model=\"compensationForm.compensationName\" :placeholder=\"t('pleaseEnterCompensationPersonName')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('compensationAmount')\">\r\n        <span class=\"readonly-text\">{{ rentInfo.indemnityPrice }} {{ t('yuan') }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('accountingMethod')\">\r\n        <span class=\"readonly-text\">{{ compensationForm.accountingMethod }}</span>\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('compensationRemark')\">\r\n        <el-input v-model=\"compensationForm.compensationRemark\" type=\"textarea\" :rows=\"3\" maxlength=\"200\" :placeholder=\"t('pleaseEnterCompensationRemark')\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  margin-bottom: 16px;\r\n  padding-left: 8px;\r\n  border-left: 4px solid #f56c6c;\r\n}\r\n\r\n.readonly-form {\r\n  .readonly-text {\r\n    color: #606266;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.el-divider {\r\n  margin: 24px 0;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "rentInfo", "rentNo", "rentName", "rNo", "name", "rentPrice", "remark", "indemnityPrice", "compensationForm", "gcode", "hcode", "id", "compensationName", "accountingMethod", "compensationPrice", "compensationRemark", "state", "formRules", "required", "message", "trigger", "onMounted", "value", "rentGoodsApi", "getRentGoods", "then", "res", "Object", "assign", "data", "__expose", "submit", "Promise", "resolve", "validate", "valid", "indemnityRentGoods", "ElMessage", "success", "center"], "mappings": "gzBA0EA,MAAMA,EAAQC,GAGRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IAGVE,EAAWF,EAAI,CAEnBG,OAAQ,GAERC,SAAU,GAEVC,IAAK,GAELC,KAAM,GAENC,UAAW,EAEXC,OAAQ,GAERC,eAAgB,IAIZC,EAAmBV,EAAI,CAC3BW,MAAOd,EAAUc,MAEjBC,MAAOf,EAAUe,MACjBC,GAAIpB,EAAMoB,GAEVC,iBAAkB,GAElBC,iBAAkBpB,EAAE,eAEpBqB,kBAAmB,EAEnBC,mBAAoB,GAEpBC,MAAO,MAGHC,EAAYnB,EAAe,CAC/Bc,iBAAkB,CAAC,CAAEM,UAAU,EAAMC,QAAS1B,EAAE,qCAAsC2B,QAAS,kBAGjGC,GAAU,KACS,IAAb9B,EAAMoB,KAMVd,EAAQyB,OAAQ,EAChBC,EAAaC,aAAajC,EAAMoB,IAAIc,MAAMC,IACxC7B,EAAQyB,OAAQ,EAEhBK,OAAOC,OAAO5B,EAASsB,MAAOI,EAAIG,MAElCrB,EAAiBc,MAAMR,kBAAoBY,EAAIG,KAAKtB,gBAAkB,EAEtEC,EAAiBc,MAAMV,iBAAmBc,EAAIG,KAAKzB,MAAQ,EAAA,IAbnD,IAiBC0B,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBlC,EAAQuB,OACNvB,EAAQuB,MAAMY,UAAUC,IAClBA,GAEFZ,EACGa,mBAAmB,IACf5B,EAAiBc,MACpBX,GAAIpB,EAAMoB,KAEXc,MAAK,KACJY,EAAUC,QAAQ,CAChBnB,QAAS1B,EAAE,uBACX8C,QAAQ,IAEFN,GAAA,GACT,GAEN"}