{"version": 3, "file": "use-resolve-button-type-CUES3UUR.js", "sources": ["../../node_modules/@headlessui/vue/dist/hooks/use-resolve-button-type.js"], "sourcesContent": ["import{onMounted as i,ref as f,watchEffect as l}from\"vue\";import{dom as o}from'../utils/dom.js';function r(t,e){if(t)return t;let n=e!=null?e:\"button\";if(typeof n==\"string\"&&n.toLowerCase()===\"button\")return\"button\"}function s(t,e){let n=f(r(t.value.type,t.value.as));return i(()=>{n.value=r(t.value.type,t.value.as)}),l(()=>{var u;n.value||o(e)&&o(e)instanceof HTMLButtonElement&&!((u=o(e))!=null&&u.hasAttribute(\"type\"))&&(n.value=\"button\")}),n}export{s as useResolveButtonType};\n"], "names": ["r", "t", "e", "n", "toLowerCase", "s", "f", "value", "type", "as", "i", "l", "u", "o", "HTMLButtonElement", "hasAttribute"], "mappings": "6DAAgG,SAASA,EAAEC,EAAEC,GAAG,GAAGD,EAAS,OAAAA,EAAM,IAAAE,EAAK,MAAHD,EAAQA,EAAE,SAAS,MAAa,iBAAHC,GAA+B,WAAlBA,EAAEC,cAA+B,cAAxD,CAAgE,CAAC,SAASC,EAAEJ,EAAEC,GAAO,IAAAC,EAAEG,EAAEN,EAAEC,EAAEM,MAAMC,KAAKP,EAAEM,MAAME,KAAK,OAAOC,GAAE,KAAKP,EAAEI,MAAMP,EAAEC,EAAEM,MAAMC,KAAKP,EAAEM,MAAME,GAAE,IAAIE,GAAE,KAAS,IAAAC,EAAET,EAAEI,OAAOM,EAAEX,IAAIW,EAAEX,aAAaY,oBAA+B,OAATF,EAAEC,EAAEX,MAAWU,EAAEG,aAAa,WAAWZ,EAAEI,MAAM,SAAA,IAAYJ,CAAC", "x_google_ignoreList": [0]}