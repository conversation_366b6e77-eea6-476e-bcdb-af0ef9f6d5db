{"version": 3, "file": "roomAwaken-Dgg5_VGw.js", "sources": ["../../src/views/room/realtime/components/roomAwaken.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"wakeUpService\": \"Wake-up Service\",\r\n      \"wakeUpTime\": \"Wake-up Time\",\r\n      \"pleaseEnterWakeUpTime\": \"Please enter wake-up time\",\r\n      \"cancel\": \"Cancel\",\r\n      \"submit\": \"Submit\",\r\n      \"wakeUpServiceSetSuccess\": \"Wake-up service for room '{rNo}' set successfully\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"wakeUpService\": \"叫醒服务\",\r\n      \"wakeUpTime\": \"叫醒时间\",\r\n      \"pleaseEnterWakeUpTime\": \"请输入叫醒时间\",\r\n      \"cancel\": \"取消\",\r\n      \"submit\": \"提交\",\r\n      \"wakeUpServiceSetSuccess\": \"房间「{rNo}」叫醒服务设置成功\"\r\n    },\r\n    \"km\": {\r\n      \"wakeUpService\": \"សេវាកម្មដាស់\",\r\n      \"wakeUpTime\": \"ពេលវេលាដាស់\",\r\n      \"pleaseEnterWakeUpTime\": \"សូមបញ្ចូលពេលវេលាដាស់\",\r\n      \"cancel\": \"បោះបង់\",\r\n      \"submit\": \"ដាក់ស្នើ\",\r\n      \"wakeUpServiceSetSuccess\": \"សេវាកម្មដាស់សម្រាប់បន្ទប់ '{rNo}' បានកំណត់ដោយជោគជ័យ\"\r\n    }\r\n  }\r\n  </i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { orderApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    rCode: string\r\n    rNo: string\r\n    orderNo?: number | string\r\n  }>(),\r\n  {\r\n    rCode: '',\r\n    rNo: '',\r\n    orderNo: '',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: [data: any]\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst formRef = ref<FormInstance>()\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  rCode: props.rCode,\r\n  orderNo: props.orderNo,\r\n  time: dayjs().add(3, 'hour').format('YYYY-MM-DD HH:mm'),\r\n})\r\nconst formRules = ref<FormRules>({\r\n  time: [{ required: true, message: t('pleaseEnterWakeUpTime'), trigger: 'blur' }],\r\n})\r\n\r\nonMounted(() => {})\r\n\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        orderApi.edit(form.value).then((res: any) => {\r\n          if (res.code !== 0) {\r\n            ElMessage.error(res.msg)\r\n            return\r\n          }\r\n          ElMessage.success({\r\n            message: t('wakeUpServiceSetSuccess', { rNo: props.rNo }),\r\n            type: 'success',\r\n            center: true,\r\n          })\r\n          emits('success', {\r\n            rCode: props.rCode,\r\n            rNo: props.rNo,\r\n            orderNo: props.orderNo,\r\n          })\r\n          onCancel()\r\n        })\r\n      }\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-model=\"myVisible\" :title=\"`{t('wakeUpService')}：${props.rNo}`\" width=\"400px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"100px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('wakeUpTime')\" prop=\"time\">\r\n        <el-date-picker v-model=\"form.time\" type=\"datetime\" :placeholder=\"t('pleaseEnterWakeUpTime')\" style=\"width: 200px\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('submit') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "form", "gcode", "hcode", "rCode", "orderNo", "time", "dayjs", "add", "format", "formRules", "required", "message", "trigger", "onSubmit", "value", "validate", "valid", "orderApi", "edit", "then", "res", "code", "ElMessage", "success", "rNo", "type", "center", "onCancel", "error", "msg", "onMounted"], "mappings": "6vBAmCA,MAAMA,EAAQC,EAaRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,EAAUC,IACVC,EAAYC,EAAS,CACzBC,IAAM,IACGZ,EAAMa,WAEf,GAAAC,CAAIC,GACFb,EAAM,oBAAqBa,EAAG,IAG5BC,EAAOP,EAAI,CACfQ,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBC,MAAOnB,EAAMmB,MACbC,QAASpB,EAAMoB,QACfC,KAAMC,IAAQC,IAAI,EAAG,QAAQC,OAAO,sBAEhCC,EAAYhB,EAAe,CAC/BY,KAAM,CAAC,CAAEK,UAAU,EAAMC,QAASvB,EAAE,yBAA0BwB,QAAS,WAKzE,SAASC,IACPrB,EAAQsB,OACNtB,EAAQsB,MAAMC,UAAUC,IAClBA,GACFC,EAASC,KAAKlB,EAAKc,OAAOK,MAAMC,IACb,IAAbA,EAAIC,MAIRC,EAAUC,QAAQ,CAChBZ,QAASvB,EAAE,0BAA2B,CAAEoC,IAAKxC,EAAMwC,MACnDC,KAAM,UACNC,QAAQ,IAEVxC,EAAM,UAAW,CACfiB,MAAOnB,EAAMmB,MACbqB,IAAKxC,EAAMwC,IACXpB,QAASpB,EAAMoB,UAERuB,KAbGL,EAAAM,MAAMR,EAAIS,IAab,GACV,GAEJ,CAGL,SAASF,IACPjC,EAAUoB,OAAQ,CAAA,QA5BpBgB,GAAU"}