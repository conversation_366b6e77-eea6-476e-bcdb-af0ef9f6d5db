import{d as e,ai as t,aj as a,b as s,y as l,o,e as r,w as i,f as n,u,g as c,Y as d,aq as m,c as p,F as g,h as b,aR as v,bQ as f,bR as _,aZ as k,a$ as E,cy as L,aS as j,t as w,q as y,v as V,aT as h}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css                  *//* empty css               *//* empty css                 *//* empty css                  *//* empty css                *//* empty css                     */import{g as C}from"./generalConfig.api-CEBBd8kx.js";import{B as x}from"./constants-Cg3j_uH4.js";import{G}from"./GeneralConfigTypeEnum-DERqowgl.js";import{_ as R}from"./_plugin-vue_export-helper-BCo6x5W8.js";const M={key:1},N={key:1},O=e({__name:"customerlevel",setup(e){const R=t(),{t:O}=a(),S=s(!1),T=s({id:0,gcode:R.gcode,code:"",name:"",value:"",type:G.AGENT_LEVEL,remark:"",isG:x.YES}),A=s([]),B=s(G.AGENT_LEVEL.toString());function P(){S.value=!0,C.list({gcode:R.gcode,isG:x.YES,type:B.value}).then((e=>{S.value=!1,A.value=e.data}))}function U(e){B.value=e,P()}l((()=>{P()}));const Y=new Map([[G.AGENT_LEVEL,{index:"1",menuName:O("agentLevel")}],[G.PROTOCOL_LEVEL,{index:"2",menuName:O("protocolLevel")}],[G.BROKERAGE_LEVEL,{index:"3",menuName:O("brokerageLevel")}]]);return(e,t)=>{const a=f,s=_,l=k,x=E,R=L,P=j,q=w,K=y,z=V,F=h;return o(),r(x,null,{default:i((()=>[n(R,{style:{width:"200px"}},{default:i((()=>[n(x,null,{default:i((()=>[n(l,{class:"nopadding"},{default:i((()=>[n(s,{"default-active":"1",class:"el-menu-vertical-demo"},{default:i((()=>[n(a,{index:"1",onClick:t[0]||(t[0]=e=>U(u(G).AGENT_LEVEL.toString()))},{default:i((()=>[c("span",null,d(u(O)("agentLevel")),1)])),_:1}),n(a,{index:"2",onClick:t[1]||(t[1]=e=>U(u(G).PROTOCOL_LEVEL.toString()))},{default:i((()=>[c("span",null,d(u(O)("protocolLevel")),1)])),_:1}),n(a,{index:"3",onClick:t[2]||(t[2]=e=>U(u(G).BROKERAGE_LEVEL.toString()))},{default:i((()=>[c("span",null,d(u(O)("brokerageLevel")),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(l,null,{default:i((()=>[c("div",null,[m((o(),r(z,{"header-cell-style":{background:"#f5f7fa",color:"#606266"},class:"list-table",data:u(A),height:"100%"},{default:i((()=>{var e;return[n(q,{label:null==(e=u(Y).get(u(B)))?void 0:e.menuName},{default:i((e=>[e.row.isEdit?(o(),r(P,{key:0,modelValue:e.row.name,"onUpdate:modelValue":t=>e.row.name=t,maxlength:"10"},null,8,["modelValue","onUpdate:modelValue"])):(o(),p("span",M,d(e.row.name),1))])),_:1},8,["label"]),n(q,{label:u(O)("remark"),align:"left"},{default:i((e=>[e.row.isEdit?(o(),r(P,{key:0,modelValue:e.row.remark,"onUpdate:modelValue":t=>e.row.remark=t,maxlength:"50"},null,8,["modelValue","onUpdate:modelValue"])):(o(),p("span",N,d(e.row.remark),1))])),_:1},8,["label"]),n(q,{label:u(O)("operation"),align:"left",fixed:"right"},{default:i((e=>[e.row.isEdit?(o(),p(g,{key:0},[n(K,{type:"primary",plain:"",onClick:t=>{return a=e.row,T.value=a,new Promise((e=>{C.updateGeneralConfig(T.value).then((t=>{0===t.code?(v.success({message:O("successMessage"),center:!0}),a.isEdit=!1):v.error({message:O("errorMessage",{message:t.msg}),center:!0}),e()}))}));var a}},{default:i((()=>[b(d(u(O)("save")),1)])),_:2},1032,["onClick"]),n(K,{plain:"",onClick:t=>e.row.isEdit=!1},{default:i((()=>[b(d(u(O)("cancel")),1)])),_:2},1032,["onClick"])],64)):(o(),r(K,{key:1,type:"primary",plain:"",onClick:t=>e.row.isEdit=!0},{default:i((()=>[b(d(u(O)("edit")),1)])),_:2},1032,["onClick"]))])),_:1},8,["label"])]})),_:1},8,["data"])),[[F,u(S)]])])])),_:1})])),_:1})}}});function S(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{agentLevel:{t:0,b:{t:2,i:[{t:3}],s:"Agent Level"}},protocolLevel:{t:0,b:{t:2,i:[{t:3}],s:"Protocol Level"}},brokerageLevel:{t:0,b:{t:2,i:[{t:3}],s:"Brokerage Level"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},save:{t:0,b:{t:2,i:[{t:3}],s:"Save"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"Edit"}},successMessage:{t:0,b:{t:2,i:[{t:3}],s:"Edit successful"}},errorMessage:{t:0,b:{t:2,i:[{t:3,v:"Error: "},{t:4,k:"message"}]}},operation:{t:0,b:{t:2,i:[{t:3}],s:"operation"}}},"zh-cn":{agentLevel:{t:0,b:{t:2,i:[{t:3}],s:"中介等级"}},protocolLevel:{t:0,b:{t:2,i:[{t:3}],s:"协议单位等级"}},brokerageLevel:{t:0,b:{t:2,i:[{t:3}],s:"佣金等级"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},save:{t:0,b:{t:2,i:[{t:3}],s:"保存"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"编辑"}},successMessage:{t:0,b:{t:2,i:[{t:3}],s:"编辑成功"}},errorMessage:{t:0,b:{t:2,i:[{t:3,v:"错误: "},{t:4,k:"message"}]}},operation:{t:0,b:{t:2,i:[{t:3}],s:"操作"}}},km:{agentLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតភ្នាក់ងារ"}},protocolLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតអង្គភាពព្រមព្រៀង"}},brokerageLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតគណនី"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់សម្គាល់"}},save:{t:0,b:{t:2,i:[{t:3}],s:"រក្សាទុក"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},edit:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួល"}},successMessage:{t:0,b:{t:2,i:[{t:3}],s:"កែសម្រួលបានជោគជ័យ"}},errorMessage:{t:0,b:{t:2,i:[{t:3,v:"កំហុស: "},{t:4,k:"message"}]}},operation:{t:0,b:{t:2,i:[{t:3}],s:"ប្រតិបត្តិការ"}}}}})}S(O);const T=R(O,[["__scopeId","data-v-e9511c15"]]);export{T as default};
//# sourceMappingURL=customerlevel-DVnyH-xD.js.map
