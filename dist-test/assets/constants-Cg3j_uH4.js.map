{"version": 3, "file": "constants-Cg3j_uH4.js", "sources": ["../../src/models/dict/constants.ts"], "sourcesContent": ["/** 酒店类型 */\r\nexport const DICT_TYPE_HOTEL_TYPE = 'hotel_type'\r\n/** 酒店状态 */\r\nexport const DICT_TYPE_HOTEL_STATUS = 'hotel_status'\r\n/** 房态 */\r\nexport const DICT_TYPE_ROOM_STATUS = 'room_status'\r\n/** 清扫类型 */\r\nexport const DICT_TYPE_ROOM_CLEAN_TYPE = 'room_clean_type'\r\n/** 打扫状态 */\r\nexport const DICT_TYPE_TASK_STATE = 'task_state'\r\n/** 床型 */\r\nexport const DICT_TYPE_BED_TYPE = 'bed_type'\r\n/** 酒店级别 */\r\nexport const DICT_TYPE_HOTEL_LEVEL = 'hotel_level'\r\n/** 房间特性 */\r\nexport const DICT_TYPE_ROOM_FEATURE = 'room_feature'\r\n/** 清扫任务 */\r\nexport const DICT_TYPE_ROOM_CLEAN_TASK = 'room_clean_task'\r\n/** 清扫流程 */\r\nexport const DICT_TYPE_ROOM_HOUSEKEEPING = 'housekeeping'\r\n/**\r\n * 钟点小时数\r\n */\r\nexport const CONSTANT_TYPE_CODE_SZ = 'hour_room'\r\n\r\n/** 客源类型 */\r\nexport const DICT_TYPE_GUEST_SRC_TYPE = 'guest_src_type'\r\n\r\n/**\r\n * 入住类型\r\n */\r\nexport const DICT_TYPE_CHECKIN_TYPE = 'checkin_type'\r\n// 证件类型\r\nexport const DICT_TYPE_ID_TYPE = 'id_type'\r\n// 订单日志类型\r\nexport const ORDER_LOG_TYPE = 'order_log'\r\n/**\r\n * 服务项目`\r\n */\r\nexport const DICT_TYPE_HOTEL_SERVICE = 'service'\r\n/** 权益类 */\r\nexport const DICT_TYPE_RIGHTS_TYPE = 'rights_type'\r\n/** 优惠类 */\r\nexport const DICT_TYPE_DISCOUNT_TYPE = 'discount_type'\r\n/** 渠道 */\r\nexport const DICT_TYPE_CHANNEL = 'channel'\r\n/** 价格尾数处理 */\r\nexport const DICT_TYPE_PRICE_HANDLE = 'price_handle'\r\n/** 担保类型 */\r\nexport const DICT_TYPE_GUARANTEE_TYPE = 'guarantee_type'\r\n/** 银行类型代码 */\r\nexport const DICT_TYPE_BANK_TYPE = 'bank_type'\r\n/** 办卡方式 */\r\nexport const DICT_TYPE_CARD_REGISTER_TYPE = 'card_register_type'\r\n\r\n/** 系统参数-订单配置 换房原因 */\r\nexport const CHANGE_REASON = 'change_reason'\r\n/** 系统参数-订单配置 订单取消原因 */\r\nexport const CANCEL_REASON = 'cancel_reason'\r\n/** 系统参数-订单配置 冲调账原因 */\r\nexport const OFFSET_REASON = 'offset_reason'\r\n/** 系统参数-订单配置 维修原因 */\r\nexport const REPAIR_REASON = 'repair_reason'\r\n/** 系统参数-订单配置 锁房原因 */\r\nexport const LOCK_REASON = 'lock_reason'\r\n\r\n/** 集团参数设置中 房态盘颜色 */\r\nexport const PARAM_TYPE_ROOMCOLOR = 'roomcolor'\r\n/** 集团参数设置中 业务逻辑-间夜数设置 */\r\nexport const PARAM_TYPE_NIGHT_NUM = 'nightnum'\r\n/** 集团参数设置中 业务逻辑-钟点房占房配置 */\r\nexport const PARAM_TYPE_HOUR_ROOM = 'hourroom'\r\n/** 集团参数设置中 会员设置部分 */\r\nexport const PARAM_TYPE_MEMBERCONFIG = 'memberconfig'\r\n/** 集团参数设置中 业务逻辑-订单配置 */\r\nexport const PARAM_TYPE_ORDER = 'order'\r\n/** 集团参数设置中 业务逻辑-财务 */\r\nexport const PARAM_TYPE_FINANCE = 'finance'\r\n/** 集团参数设置中 业务逻辑-会员规则配置 */\r\nexport const PARAM_TYPE_MEMBERRULE = 'memberrule'\r\n/** 集团收银配置 付款科目代码 */\r\nexport const DICT_TYPE_PAY_ACCOUNT = 'pay_account'\r\n/** 集团收银配置 消费科目代码 */\r\nexport const DICT_TYPE_CONSUME_ACCOUNT = 'consume_account'\r\n/** 门店参数设置 前台 代码 */\r\nexport const PARAM_TYPE_FRONT = 'front'\r\n/** 门店参数设置 班次设置 代码 */\r\nexport const DICT_TYPE_SHIFT = 'shift'\r\n/** 门店参数设置 班次设置 交班模式-现金流模式 */\r\nexport const PARAM_TYPE_CASH_FLOW = 'cash_flow'\r\n/** 门店参数设置 班次设置 交班模式-应收模式 */\r\nexport const PARAM_TYPE_PAID_IN = 'paid_in'\r\n/** 门店参数设置 班次设置 交班模式-实收模式 */\r\nexport const PARAM_TYPE_RECEIVABLE = 'receivable'\r\n/** 职位 */\r\nexport const DICT_TYPE_POST = 'post'\r\n/** 打印内容 */\r\nexport const CONSTANT_DYNR = 'DYNR'\r\n/** 消费科目类型 */\r\nexport const CONSUME_ACCOUNT_TYPE = 'consume_account_type'\r\n/** 消费科目类型 所有可借款的消费科目 */\r\nexport const ALL_DEBIT_ACCOUNT = 'all_debit_account'\r\n/** 付款科目类型 可以入账的信用科目 */\r\nexport const RECORD_CREDIT_ACCOUNT = 'record_credit_account'\r\n/** 付款科目类型 所有可以入账的信用科目 */\r\nexport const ALL_CREDIT_ACCOUNT = 'all_credit_account'\r\n/** 付款科目类型 可以入现付账的信用科目 */\r\nexport const RECORD_CASH_CREDIT_ACCOUNT = 'record_cash_credit_account'\r\n/** 付款科目类型 现付账退款的信用科目 */\r\nexport const REFUND_CASH_CREDIT_ACCOUNT = 'refund_cash_credit_account'\r\n/** 付款科目类型 入押金的Credit科目 */\r\nexport const DEPOSIT_CREDIT_ACCOUNT = 'deposit_credit_account'\r\n/** 付款科目类型 可以担保的Credit科目 */\r\nexport const SECURITY_CREDIT_ACCOUNT = 'security_credit_account'\r\n/** 付款科目类型 阿里信用住订单结账可以支付的科目配置 */\r\nexport const ALI_CREDIT_ACCOUNT = 'ali_credit_account'\r\n/** 付款科目类型 可以付款的Credit科目 */\r\nexport const PAY_CREDIT_ACCOUNT = 'pay_credit_account'\r\n/** 付款科目类型 会员相关的Credit科目 */\r\nexport const MEMBER_CREDIT_ACCOUNT = 'member_credit_account'\r\n/** 付款科目类型 会员升级相关的Credit科目 */\r\nexport const UP_CREDIT_ACCOUNT = 'up_credit_account'\r\n/** 付款科目类型 可以核销Ar的Credit科目 */\r\nexport const CANCEL_AR_CREDIT_ACCOUNT = 'cancel_ar_credit_account'\r\n/** 付款科目类型 核销Ar可以退款的Credit科目 */\r\nexport const CANCEL_AR_REFUND_CREDIT_ACCOUNT = 'cancel_ar_refund_credit_account'\r\n/** 付款科目类型 可以调整的Debit科目 */\r\nexport const ADJUST_DEBIT_ACCOUNT = 'adjust_debit_account'\r\n/** 付款科目类型 可以入现付账的Debit科目 */\r\nexport const CASH_DEBIT_ACCOUNT = 'cash_debit_account'\r\n/** 付款科目类型 可以手工入账的Debit科目 */\r\nexport const HAND_DEBIT_ACCOUNT = 'hand_debit_account'\r\n/** 付款科目 */\r\nexport const PAY_ACCOUNT = 'pay_account'\r\n/** 消费科目 */\r\nexport const CONSUME_ACCOUNT = 'consume_account'\r\n/** 中介等级类型 */\r\nexport const AGENT_LEVEL = 'agent_level'\r\n/** 单位等级类型 */\r\nexport const PROTOCOL_LEVEL = 'protocol_level'\r\n/** 佣金等级类型 */\r\nexport const BROKERAGE_LEVEL = 'brokerage_level'\r\n/** 银行卡预授权代码 */\r\nexport const BANK_PRE_AUTH = 'bank_pre_auth'\r\n/** 人民币现金收款代码 */\r\nexport const RMB_RECEIPT = 'rmb_receipt'\r\n/** 会员升级方式代码 */\r\nexport const DICT_TYPE_UP_MODE = 'up_mode'\r\n/** 订单来源 */\r\nexport const ORDER_SOURCE = 'order_source'\r\n/* 公共默认短信 */\r\nexport const SYSTEM_SMS_TEMPLATE_TYPE = 'system_sms_template_type'\r\n\r\nexport enum BooleanEnum {\r\n  /** 是 */\r\n  YES = '1',\r\n  /** 否 */\r\n  NO = '0',\r\n}\r\n\r\n/**\r\n * 结账退房时选择加收方式\r\n */\r\nexport enum ConsumeAccountEnum {\r\n  /** 房费 */\r\n  ROOM_FEE = 'room_fee',\r\n  /** 手工加收 */\r\n  HAND_INPUT_ROOM_FEE = 'hand_input_room_fee',\r\n  /** 加收半天 */\r\n  ADD_HALF_DAY = 'add_half_day',\r\n  /** 加收全天 */\r\n  ADD_ALL_DAY = 'add_all_day',\r\n}\r\n\r\n/**\r\n * 计费规则类型\r\n */\r\nexport enum ChargeRuleTypeEnum {\r\n  /** 全天房 */\r\n  ALL_DAY = 'all_day',\r\n  /** 白天房 */\r\n  DAYTIME = 'daytime',\r\n  /** 钟点房 */\r\n  HOUR = 'hour',\r\n  /** 午夜房 */\r\n  MIDNIGHT = 'midnight',\r\n}\r\n\r\n// 性别\r\nexport enum SexEnum {\r\n  /** 男 */\r\n  MALE = '1',\r\n  /** 女 */\r\n  FEMALE = '0',\r\n  /** 未知 */\r\n  SECRECY = '2',\r\n}\r\n\r\n/**\r\n * 数字枚举类\r\n */\r\nexport enum NumberEnum {\r\n  MINUSONE = '-1',\r\n  ZERO = '0',\r\n  ONE = '1',\r\n  TWO = '2',\r\n  THREE = '3',\r\n  FOUR = '4',\r\n  FIVE = '5',\r\n  SIX = '6',\r\n  SEVEN = '7',\r\n  EIGHT = '8',\r\n  NINE = '9',\r\n  TEN = '10',\r\n}\r\n\r\n/** 付款方式代码 */\r\nexport enum PayModelCode {\r\n  CREDIT_S_ACCOUNT = 'credit_s_account',\r\n  RMB_RECEIPT = 'rmb_receipt',\r\n  STORE_CARD = 'store_card',\r\n  BANK_CARD = 'bank_card',\r\n  ALIPAY = 'alipay',\r\n  WX = 'wx',\r\n  SCAN_GUN = 'scan_gun',\r\n  BANK = 'bank',\r\n  SCAN = 'scan',\r\n  CREDIT = 'credit',\r\n  CARD = 'card',\r\n  STORE_CARD_REFUND = 'store_card_refund',\r\n}\r\n\r\n/** 赠券活动类型代码 */\r\nexport enum CouponActivityType {\r\n  /** 注册会员赠券 */\r\n  REGISTER = 'register',\r\n  /** 会员生日赠券 */\r\n  BIRTHDAY = 'birthday',\r\n  /** 会员退房赠券 */\r\n  CHECKOUT = 'checkout',\r\n  /** 会员升级赠券 */\r\n  UPGRADE = 'upgrade',\r\n  /** 自动升级赠券 */\r\n  AUTO_UPGRADE = 'auto_upgrade',\r\n}\r\n\r\n/** 订单状态代码 */\r\nexport enum OrderState {\r\n  /** 预订中 */\r\n  IN_BOOKING = 'no_check_in',\r\n  /** 在住 */\r\n  CHECK_IN = 'check_in',\r\n  /** 已退房 */\r\n  CHECK_OUT = 'check_out',\r\n  /** noShow */\r\n  NOSHOW = 'noshow',\r\n  /** 取消 */\r\n  CANCEL = 'cancel',\r\n  /** 待确认 */\r\n  BE_CONFIRM = 'be_confirm ',\r\n  /** 拒绝 */\r\n  REFUSE = 'refuse',\r\n  /** 完成 */\r\n  OVER = 'over',\r\n  /** 挂账 */\r\n  CREDIT = 'credit',\r\n  /** 续住 */\r\n  CONTINUE = 'continue',\r\n}\r\n\r\n/** 客源类型代码 */\r\nexport enum GuestSrcType {\r\n  /**\r\n   * 不限\r\n   */\r\n  ALL = '0',\r\n  /** 散客 */\r\n  WALK_IN = 'walk_in',\r\n  /** 会员 */\r\n  MEMBER = 'member',\r\n  /** 中介 */\r\n  AGENT = 'agent',\r\n  /** 单位 */\r\n  PROTOCOL = 'protocol',\r\n}\r\n\r\n/**\r\n * 酒店状态代码\r\n */\r\nexport enum HotelState {\r\n  PREP = 'prep',\r\n  IN_CONSTRUCTION = 'in_construction',\r\n  TRIAL_OPEN = 'trial_open',\r\n  OPEN = 'open',\r\n  TEMP_CLOSE = 'temp_close',\r\n  STOP = 'stop',\r\n}\r\n\r\n/**\r\n * 打印格式\r\n */\r\nexport enum PrintFormat {\r\n  POS = 'POS',\r\n  A4 = 'A4',\r\n  A412 = 'A412',\r\n  A413 = 'A413',\r\n}\r\n\r\n/**\r\n * 账单类型\r\n */\r\nexport enum BillType {\r\n  BILL = 'bill',\r\n  MERGE_FORM = 'merge_form',\r\n  CHECK_IN_FORM = 'check_in_form',\r\n  ROOM_BILL = 'room_bill',\r\n  DEPOSIT_RECEIPT = 'deposit_receipt',\r\n  EXTENSION_FORM = 'extension_form',\r\n  ROOM_CHANGE_FORM = 'room_change_form',\r\n  MEMBER_BILL = 'member_bill',\r\n  MEMBER_REPAID_ACCOUNT = 'member_repaid_account',\r\n}\r\n\r\n/**\r\n * 入住类型代码\r\n */\r\nexport enum CheckinType {\r\n  ALL_DAY = 'all_day', // 全天房\r\n  LONG_STAY = 'long_stay', // 长包\r\n  HOUR_ROOM = 'hour_room', // 钟点房\r\n  SELF_USE = 'self_use', // 自用\r\n  FREE = 'free', // 免费\r\n  TRAVEL_GROUP = 'travel_group', // 旅行团\r\n  MEETING_GROUP = 'meeting_group', // 会议室\r\n}\r\n\r\n/** 订单类型代码 */\r\nexport enum OrderType {\r\n  /** (个人)普通订单 */\r\n  GENERAL = 'general',\r\n  /** 团队订单 */\r\n  GROUP = 'group',\r\n  /** 联房订单 */\r\n  JOIN = 'join',\r\n}\r\n\r\n/**\r\n * 账务状态代码\r\n */\r\nexport enum AccountState {\r\n  // 未结\r\n  UNCLOSED = 'open',\r\n  // 已结\r\n  CLOSED = 'closed',\r\n  // 已冲红\r\n  REDEEMED = 'red',\r\n  // 已转账\r\n  TRANSFEROUT = 'transfer_out',\r\n  // 预授权取消\r\n  CANCEL = 'cancel',\r\n}\r\n\r\n/** 房间状态代码 */\r\nexport enum RoomState {\r\n  /** 空净 */\r\n  VC = 'VC',\r\n  /** 空脏 */\r\n  VD = 'VD',\r\n  /** 住净 */\r\n  OC = 'OC',\r\n  /** 住脏 */\r\n  OD = 'OD',\r\n  /**    维修 */\r\n  OO = 'OO',\r\n  /** 锁房 */\r\n  LR = 'LR',\r\n}\r\n\r\n/** 证件类型代码 */\r\nexport enum IdType {\r\n  IDCERT = 'id_cert',\r\n  HONGKONG = 'hongkong',\r\n  HOUSEHOLDREGISTER = 'household_register',\r\n  OFFICERCERT = 'officer_cert',\r\n  POLICECERT = 'police_cert',\r\n  SOLDIERCERT = 'soldier_cert',\r\n  PASSPORT = 'passport',\r\n  DRIVINGLICENCE = 'driving_licence',\r\n  SOCIAL_SECURITY_CARD = 'social_security_card',\r\n  OTHER = 'other',\r\n}\r\n\r\n/** 账务类型代码 */\r\nexport enum AccountType {\r\n  /** 普通 */\r\n  GENERAL = 'general',\r\n  /** 团队主账 */\r\n  GROUP = 'group',\r\n  /** 预订单账务 */\r\n  BOOK = 'book',\r\n  /** 现付账 */\r\n  CASH = 'cash',\r\n}\r\n\r\n/** 挂账类型代码 */\r\nexport enum CreditType {\r\n  /** 信用账户 */\r\n  CASH_PAY = '0',\r\n  /** 预付帐户 */\r\n  GENERAL = '1',\r\n}\r\n\r\n/** 订单范围类型 */\r\nexport enum NoType {\r\n  /** 客人（客单） */\r\n  ORDER = 'order',\r\n  /** 普通预订单 */\r\n  BOOK = 'book',\r\n  /** 团队预订单 */\r\n  TEAM = 'team',\r\n  /** 非团队接待 */\r\n  ORDERLIST = 'orderList',\r\n  /** 团队主单 */\r\n  TEAMMAIN = 'teamMain',\r\n  /** 团队接待 */\r\n  TEAMRECEPTION = 'teamReception',\r\n  /** 房间 */\r\n  ROOM = 'room',\r\n}\r\n\r\n/** 房价策略，权益类型 */\r\nexport enum RightTypeEnum {\r\n  /**\r\n   * 延迟退房\r\n   */\r\n  DELAY = 'delay',\r\n  /**\r\n   * 积分倍数\r\n   */\r\n  MULTI_POINT = 'multi_point',\r\n  /**\r\n   * 早餐券\r\n   */\r\n  BREAKFAST = 'breakfast',\r\n}\r\n\r\n/**\r\n * 优惠类型枚举\r\n */\r\nexport enum DiscountTypeEnum {\r\n  /**\r\n   * 房价折扣\r\n   */\r\n  DISCOUNT = 'discount',\r\n  /**\r\n   * 房价立减\r\n   */\r\n  REDUCE = 'reduce',\r\n  /**\r\n   * 固定房价\r\n   */\r\n  FIX = 'fixed',\r\n}\r\n\r\n/**\r\n * 优惠券类型枚举\r\n */\r\nexport enum CouponEnum {\r\n  // 代金券\r\n  VOUCHER = 'voucher',\r\n  // 折扣券\r\n  DISCOUNT = 'discount',\r\n  // 免房券\r\n  FREE = 'free',\r\n  // 早餐券\r\n  BREAKFAST = 'breakfast',\r\n}\r\n\r\n/**\r\n * 价格处理方式\r\n */\r\nexport enum PriceHandleEnum {\r\n  // 取整数\r\n  ROUND = 'round',\r\n  // 保留一位小数\r\n  ONE_DECIMAL = 'one_decimal',\r\n  // 保留两位小数\r\n  TWO_DECIMAL = 'two_decimal',\r\n}\r\n\r\n/** 会员升级方式 */\r\nexport enum UpModeEnum {\r\n  // 积分\r\n  POINT = 'point',\r\n  // 现金\r\n  CASH = 'cash',\r\n  // 挂房账\r\n  ROOM_FEE = 'room_fee',\r\n}\r\n/** 酒店类型枚举类 */\r\nexport enum HotelTypeEnum {\r\n  // 加盟\r\n  JOIN = 'join',\r\n  // 直营\r\n  DIRECT = 'direct',\r\n  // 托管\r\n  DEPOSIT = 'deposit',\r\n}\r\n\r\n/** 渠道 */\r\nexport enum ChannelEnum {\r\n  MIMI_APP = 'mini_app',\r\n  CTRIP = 'ctrip',\r\n  ELONG = 'elong',\r\n  LOBBY = 'lobby',\r\n  MEITUAN = 'meituan',\r\n  FLIGGY = 'fliggy',\r\n  TIKTOK = 'tiktok',\r\n  HIII = 'hiii',\r\n  LY = 'ly',\r\n  QUNAR = 'qunar',\r\n  TUJIA = 'tujia',\r\n  XIAOZHU = 'xiaozhu',\r\n  MUNIAO = 'muniao',\r\n  XIAOHONGSHU = 'xiaohongshu',\r\n  APP = 'app',\r\n  TUNIU = 'tuniu',\r\n  MAFENGWO = 'mafengwo',\r\n  JD = 'jd',\r\n}\r\n\r\n/**\r\n * 消息类型\r\n */\r\nexport enum HotelMessageTypeEnum {\r\n  CONNECT_SUCCESS = 'connect_success',\r\n  ROOM_ORDER = 'room_order',\r\n  ORDER = 'order',\r\n  NOTIFICATION = 'notification',\r\n  // 刷新房态盘\r\n  REFRESH = 'refresh',\r\n  // 叫醒\r\n  WAKEUP = 'wakeup',\r\n  // 小时房到点提醒\r\n  HOURORDERALERT = 'hourOrderAlert',\r\n  // 夜审完成提醒\r\n  NIGHTAUDITOVER = 'nightAuditOver',\r\n  // 夜审开始提醒\r\n  NIGHTAUDITBEGIN = 'nightAuditBegin',\r\n  // OTA订单\r\n  OTA_ORDER = 'ota_order',\r\n  // OTA订单取消提醒\r\n  OTA_ORDER_CANCEL = 'ota_order_cancel',\r\n  // OTA订单同步失败提醒\r\n  OTA_ORDER_FAIL = 'ota_order_fail',\r\n  // 心跳\r\n  HEARTBEAT = 'heartbeat',\r\n}\r\n\r\n/**\r\n * 时区常量\r\n */\r\nexport enum TimeZoneEnum {\r\n  AsiaShanghai = 'Asia/Shanghai',\r\n}\r\n\r\n/**\r\n * 客户端通信的方法名称\r\n */\r\nexport enum ClientMethodEnum {\r\n  // 读身份证\r\n  READIDCARD = 'readIdCard',\r\n  // 读房卡\r\n  READLOCKCARD = 'readLockCard',\r\n  // 写房卡\r\n  WRITELOCKCARD = 'writeLockCard',\r\n  // 清卡\r\n  CLEARLOCKCARD = 'clearLockCard',\r\n  // 注销\r\n  CANCELLOCKCARD = 'cancelLockCard',\r\n}\r\n\r\n/** 有效 无效 数组 */\r\nexport const STATES = [\r\n  { key: '-1', value: 'all' },\r\n  { key: '1', value: 'valid' },\r\n  { key: '0', value: 'invalid' },\r\n]\r\n/** 公司类型 */\r\nexport const COMPANY_TYPES = [\r\n  { key: '-1', value: 'all' },\r\n  { key: '1', value: 'protocol' },\r\n  { key: '0', value: 'agent' },\r\n]\r\n/** 是否有窗 */\r\nexport const HAS_WINDOWS = [\r\n  { key: '1', value: 'hasWindowWith' },\r\n  { key: '0', value: 'hasWindowWithout' },\r\n  { key: '2', value: 'hasWindowPartial' },\r\n]\r\n\r\n/** 积分方式 */\r\nexport const POINT_MODE = [\r\n  { key: '0', value: '按房费' },\r\n  { key: '1', value: '按间夜数' },\r\n  { key: '2', value: '按房费+消费' },\r\n]\r\n/** 会员类型的级别,依次从低到高 */\r\nexport const MEMBER_TYPE_LEVEL = [\r\n  { key: 1, value: '一级' },\r\n  { key: 2, value: '二级' },\r\n  { key: 3, value: '三级' },\r\n  { key: 4, value: '四级' },\r\n  { key: 5, value: '五级' },\r\n  { key: 6, value: '六级' },\r\n  { key: 7, value: '七级' },\r\n  { key: 8, value: '八级' },\r\n  { key: 9, value: '九级' },\r\n  { key: 10, value: '十级' },\r\n]\r\n\r\n/** 小时数量 */\r\nexport const HOUR_TYPE_NAME = [\r\n  { key: '0', name: '非钟点房', value: 0 },\r\n  { key: '0hour', name: '非钟点房', value: 0 },\r\n  { key: '1hour', name: '一小时', value: 1 },\r\n  { key: '2hour', name: '二小时', value: 2 },\r\n  { key: '3hour', name: '三小时', value: 3 },\r\n  { key: '4hour', name: '四小时', value: 4 },\r\n  { key: '5hour', name: '五小时', value: 5 },\r\n  { key: '6hour', name: '六小时', value: 6 },\r\n  { key: '7hour', name: '七小时', value: 7 },\r\n  { key: '8hour', name: '八小时', value: 8 },\r\n  { key: '9hour', name: '九小时', value: 9 },\r\n  { key: '10hour', name: '十小时', value: 10 },\r\n  { key: '11our', name: '十一小时', value: 11 },\r\n  { key: '12hour', name: '十二小时', value: 12 },\r\n]\r\n\r\n/** 会员升级渠道 */\r\nexport const UPDATE_CHANNEL_MAP = new Map<string, string>([\r\n  ['mini_app', '小程序'],\r\n  ['lobby', '门店'],\r\n])\r\n\r\n/**\r\n * 打印布局弹出窗口的宽度map\r\n */\r\nexport const layout_width_map = new Map<string, string>([\r\n  ['POS', '400px'],\r\n  ['A4', '900px'],\r\n  ['A412', '900px'],\r\n  ['A413', '950px'],\r\n])\r\n\r\n/**\r\n * 职位列表\r\n */\r\nexport const POSITIONS = new Map<string, string>([\r\n  ['ceo', '总经理'],\r\n  ['store_manager', '店长'],\r\n  ['manager', '经理'],\r\n  ['receptionist', '前台接待员'],\r\n  ['sell', '销售'],\r\n  ['waiter', '服务员'],\r\n  ['security', '保安'],\r\n  ['cleaner', '清洁工'],\r\n  ['accountant', '财务'],\r\n  ['other', '其他'],\r\n])\r\n/**\r\n * 酒店类型\r\n */\r\nexport const HOTELTYPE = new Map<string, string>([\r\n  ['join', '加盟'],\r\n  ['deposit', '托管'],\r\n  ['direct', '直营'],\r\n])\r\n\r\n/** 用户默认个性配置 */\r\nexport const DefaultUserConfig = {\r\n  /** 房间排列方式 不分行 按楼层分行 按房型分行 */\r\n  sortMode: '0',\r\n  /** 房态格大小 */\r\n  cellSize: 'medium',\r\n  /** 房态格字体大小 */\r\n  cellFontSize: {\r\n    /** 房号 */\r\n    rNo: '16px',\r\n    /** 房型 */\r\n    rtName: '16px',\r\n    /** 住客 */\r\n    guestName: '16px',\r\n  },\r\n  /** 客源类型 */\r\n  guestSrcType: {\r\n    /** 散客 */\r\n    walkin: '1',\r\n    /** 会员 */\r\n    member: '1',\r\n    /** 单位 */\r\n    protocol: '1',\r\n    /** 中介 */\r\n    agent: '1',\r\n  },\r\n  /** 入住类型 */\r\n  checkinType: {\r\n    /** 全天 */\r\n    allDay: '1',\r\n    /** 钟点房 */\r\n    hour: '1',\r\n    /** 长包 */\r\n    longStay: '1',\r\n    /** 自用 */\r\n    selfUse: '1',\r\n    /** 免费 */\r\n    free: '1',\r\n  },\r\n  /** 服务类型 */\r\n  serverType: {\r\n    /** 叫醒服务 */\r\n    awaken: '1',\r\n    /** 免打扰 */\r\n    noDisturbing: '1',\r\n    /** 保密 */\r\n    secrecy: '1',\r\n    /** 生日 */\r\n    birthday: '1',\r\n  },\r\n  /** 财务类型 */\r\n  financeType: {\r\n    /** 是否显示欠费 */\r\n    arrearage: '1',\r\n    /** 是否显示押金 */\r\n    deposit: '1',\r\n  },\r\n  /** 是否展示 N天预抵预离 */\r\n  planCheckinOut: '1',\r\n  /** 房态格是否显示客人姓名 */\r\n  cellGuestName: '1',\r\n  /** 房态背景展示图形 今日预抵 */\r\n  todayPlanCheckin: 'default',\r\n  /** 房态字体颜色 */\r\n  fontColor: '#FFFFFF',\r\n  /** 房态颜色 */\r\n  colors: {\r\n    /** 空净颜色 */\r\n    VC: '#6CC178',\r\n    /** 空脏颜色 */\r\n    VD: '#DCDF20',\r\n    /** 住净颜色 */\r\n    OC: '#009FE8',\r\n    /** 住脏颜色 */\r\n    OD: '#0B5068',\r\n    /** 置维修颜色 */\r\n    OO: '#DB7E75',\r\n  },\r\n}\r\n"], "names": ["DICT_TYPE_HOTEL_TYPE", "DICT_TYPE_HOTEL_STATUS", "DICT_TYPE_ROOM_STATUS", "DICT_TYPE_ROOM_CLEAN_TYPE", "DICT_TYPE_TASK_STATE", "DICT_TYPE_BED_TYPE", "DICT_TYPE_HOTEL_LEVEL", "DICT_TYPE_ROOM_CLEAN_TASK", "DICT_TYPE_ROOM_HOUSEKEEPING", "CONSTANT_TYPE_CODE_SZ", "DICT_TYPE_GUEST_SRC_TYPE", "DICT_TYPE_CHECKIN_TYPE", "DICT_TYPE_ID_TYPE", "ORDER_LOG_TYPE", "DICT_TYPE_HOTEL_SERVICE", "DICT_TYPE_RIGHTS_TYPE", "DICT_TYPE_PRICE_HANDLE", "DICT_TYPE_GUARANTEE_TYPE", "DICT_TYPE_BANK_TYPE", "DICT_TYPE_CARD_REGISTER_TYPE", "CHANGE_REASON", "CANCEL_REASON", "OFFSET_REASON", "REPAIR_REASON", "LOCK_REASON", "PARAM_TYPE_NIGHT_NUM", "PARAM_TYPE_HOUR_ROOM", "PARAM_TYPE_ORDER", "PARAM_TYPE_FINANCE", "DICT_TYPE_POST", "CONSUME_ACCOUNT", "AGENT_LEVEL", "PROTOCOL_LEVEL", "BROKERAGE_LEVEL", "RMB_RECEIPT", "ORDER_SOURCE", "SYSTEM_SMS_TEMPLATE_TYPE", "BooleanEnum", "ConsumeAccountEnum", "SexEnum", "NumberEnum", "PayModelCode", "CouponActivityType", "OrderState", "GuestSrcType", "PrintFormat", "BillType", "CheckinType", "OrderType", "AccountState", "RoomState", "IdType", "AccountType", "NoType", "RightTypeEnum", "DiscountTypeEnum", "PriceHandleEnum", "UpModeEnum", "HotelTypeEnum", "ChannelEnum", "HotelMessageTypeEnum", "TimeZoneEnum", "ClientMethodEnum", "STATES", "key", "value", "COMPANY_TYPES", "HAS_WINDOWS", "MEMBER_TYPE_LEVEL", "HOUR_TYPE_NAME", "name", "UPDATE_CHANNEL_MAP", "Map", "layout_width_map", "DefaultUserConfig", "sortMode", "cellSize", "cellFontSize", "rNo", "rtName", "<PERSON><PERSON><PERSON>", "guestSrcType", "walkin", "member", "protocol", "agent", "checkinType", "allDay", "hour", "longStay", "selfUse", "free", "serverType", "awaken", "noDisturbing", "secrecy", "birthday", "financeType", "arrearage", "deposit", "planCheckinOut", "cellGuestName", "todayPlanCheckin", "fontColor", "colors", "VC", "VD", "OC", "OD", "OO"], "mappings": "AACO,MAAMA,EAAuB,aAEvBC,EAAyB,eAEzBC,EAAwB,cAExBC,EAA4B,kBAE5BC,EAAuB,aAEvBC,EAAqB,WAErBC,EAAwB,cAIxBC,EAA4B,kBAE5BC,EAA8B,eAI9BC,EAAwB,YAGxBC,EAA2B,iBAK3BC,EAAyB,eAEzBC,EAAoB,UAEpBC,EAAiB,YAIjBC,EAA0B,UAE1BC,EAAwB,cAMxBC,EAAyB,eAEzBC,EAA2B,iBAE3BC,EAAsB,YAEtBC,EAA+B,qBAG/BC,EAAgB,gBAEhBC,EAAgB,gBAEhBC,EAAgB,gBAEhBC,EAAgB,gBAEhBC,EAAc,cAKdC,EAAuB,WAEvBC,EAAuB,WAIvBC,EAAmB,QAEnBC,EAAqB,UAkBrBC,EAAiB,OAwCjBC,EAAkB,kBAElBC,EAAc,cAEdC,EAAiB,iBAEjBC,EAAkB,kBAIlBC,EAAc,cAIdC,EAAe,eAEfC,EAA2B,2BAE5B,IAAAC,GAAAA,IAEVA,EAAM,IAAA,IAENA,EAAK,GAAA,IAJKA,IAAAA,GAAA,CAAA,GAUAC,GAAAA,IAEVA,EAAW,SAAA,WAEXA,EAAsB,oBAAA,sBAEtBA,EAAe,aAAA,eAEfA,EAAc,YAAA,cARJA,IAAAA,GAAA,CAAA,GA0BAC,GAAAA,IAEVA,EAAO,KAAA,IAEPA,EAAS,OAAA,IAETA,EAAU,QAAA,IANAA,IAAAA,GAAA,CAAA,GAYAC,GAAAA,IACVA,EAAW,SAAA,KACXA,EAAO,KAAA,IACPA,EAAM,IAAA,IACNA,EAAM,IAAA,IACNA,EAAQ,MAAA,IACRA,EAAO,KAAA,IACPA,EAAO,KAAA,IACPA,EAAM,IAAA,IACNA,EAAQ,MAAA,IACRA,EAAQ,MAAA,IACRA,EAAO,KAAA,IACPA,EAAM,IAAA,KAZIA,IAAAA,GAAA,CAAA,GAgBAC,GAAAA,IACVA,EAAmB,iBAAA,mBACnBA,EAAc,YAAA,cACdA,EAAa,WAAA,aACbA,EAAY,UAAA,YACZA,EAAS,OAAA,SACTA,EAAK,GAAA,KACLA,EAAW,SAAA,WACXA,EAAO,KAAA,OACPA,EAAO,KAAA,OACPA,EAAS,OAAA,SACTA,EAAO,KAAA,OACPA,EAAoB,kBAAA,oBAZVA,IAAAA,GAAA,CAAA,GAgBAC,GAAAA,IAEVA,EAAW,SAAA,WAEXA,EAAW,SAAA,WAEXA,EAAW,SAAA,WAEXA,EAAU,QAAA,UAEVA,EAAe,aAAA,eAVLA,IAAAA,GAAA,CAAA,GAcAC,GAAAA,IAEVA,EAAa,WAAA,cAEbA,EAAW,SAAA,WAEXA,EAAY,UAAA,YAEZA,EAAS,OAAA,SAETA,EAAS,OAAA,SAETA,EAAa,WAAA,cAEbA,EAAS,OAAA,SAETA,EAAO,KAAA,OAEPA,EAAS,OAAA,SAETA,EAAW,SAAA,WApBDA,IAAAA,GAAA,CAAA,GAwBAC,GAAAA,IAIVA,EAAM,IAAA,IAENA,EAAU,QAAA,UAEVA,EAAS,OAAA,SAETA,EAAQ,MAAA,QAERA,EAAW,SAAA,WAZDA,IAAAA,GAAA,CAAA,GA8BAC,GAAAA,IACVA,EAAM,IAAA,MACNA,EAAK,GAAA,KACLA,EAAO,KAAA,OACPA,EAAO,KAAA,OAJGA,IAAAA,GAAA,CAAA,GAUAC,GAAAA,IACVA,EAAO,KAAA,OACPA,EAAa,WAAA,aACbA,EAAgB,cAAA,gBAChBA,EAAY,UAAA,YACZA,EAAkB,gBAAA,kBAClBA,EAAiB,eAAA,iBACjBA,EAAmB,iBAAA,mBACnBA,EAAc,YAAA,cACdA,EAAwB,sBAAA,wBATdA,IAAAA,GAAA,CAAA,GAeAC,GAAAA,IACVA,EAAU,QAAA,UACVA,EAAY,UAAA,YACZA,EAAY,UAAA,YACZA,EAAW,SAAA,WACXA,EAAO,KAAA,OACPA,EAAe,aAAA,eACfA,EAAgB,cAAA,gBAPNA,IAAAA,GAAA,CAAA,GAWAC,GAAAA,IAEVA,EAAU,QAAA,UAEVA,EAAQ,MAAA,QAERA,EAAO,KAAA,OANGA,IAAAA,GAAA,CAAA,GAYAC,GAAAA,IAEVA,EAAW,SAAA,OAEXA,EAAS,OAAA,SAETA,EAAW,SAAA,MAEXA,EAAc,YAAA,eAEdA,EAAS,OAAA,SAVCA,IAAAA,GAAA,CAAA,GAcAC,GAAAA,IAEVA,EAAK,GAAA,KAELA,EAAK,GAAA,KAELA,EAAK,GAAA,KAELA,EAAK,GAAA,KAELA,EAAK,GAAA,KAELA,EAAK,GAAA,KAZKA,IAAAA,GAAA,CAAA,GAgBAC,GAAAA,IACVA,EAAS,OAAA,UACTA,EAAW,SAAA,WACXA,EAAoB,kBAAA,qBACpBA,EAAc,YAAA,eACdA,EAAa,WAAA,cACbA,EAAc,YAAA,eACdA,EAAW,SAAA,WACXA,EAAiB,eAAA,kBACjBA,EAAuB,qBAAA,uBACvBA,EAAQ,MAAA,QAVEA,IAAAA,GAAA,CAAA,GAcAC,GAAAA,IAEVA,EAAU,QAAA,UAEVA,EAAQ,MAAA,QAERA,EAAO,KAAA,OAEPA,EAAO,KAAA,OARGA,IAAAA,GAAA,CAAA,GAoBAC,GAAAA,IAEVA,EAAQ,MAAA,QAERA,EAAO,KAAA,OAEPA,EAAO,KAAA,OAEPA,EAAY,UAAA,YAEZA,EAAW,SAAA,WAEXA,EAAgB,cAAA,gBAEhBA,EAAO,KAAA,OAdGA,IAAAA,GAAA,CAAA,GAkBAC,IAAAA,IAIVA,EAAQ,MAAA,QAIRA,EAAc,YAAA,cAIdA,EAAY,UAAA,YAZFA,IAAAA,IAAA,CAAA,GAkBAC,IAAAA,IAIVA,EAAW,SAAA,WAIXA,EAAS,OAAA,SAITA,EAAM,IAAA,QAZIA,IAAAA,IAAA,CAAA,GAgCAC,IAAAA,IAEVA,EAAQ,MAAA,QAERA,EAAc,YAAA,cAEdA,EAAc,YAAA,cANJA,IAAAA,IAAA,CAAA,GAUAC,IAAAA,IAEVA,EAAQ,MAAA,QAERA,EAAO,KAAA,OAEPA,EAAW,SAAA,WANDA,IAAAA,IAAA,CAAA,GASAC,IAAAA,IAEVA,EAAO,KAAA,OAEPA,EAAS,OAAA,SAETA,EAAU,QAAA,UANAA,IAAAA,IAAA,CAAA,GAUAC,IAAAA,IACVA,EAAW,SAAA,WACXA,EAAQ,MAAA,QACRA,EAAQ,MAAA,QACRA,EAAQ,MAAA,QACRA,EAAU,QAAA,UACVA,EAAS,OAAA,SACTA,EAAS,OAAA,SACTA,EAAO,KAAA,OACPA,EAAK,GAAA,KACLA,EAAQ,MAAA,QACRA,EAAQ,MAAA,QACRA,EAAU,QAAA,UACVA,EAAS,OAAA,SACTA,EAAc,YAAA,cACdA,EAAM,IAAA,MACNA,EAAQ,MAAA,QACRA,EAAW,SAAA,WACXA,EAAK,GAAA,KAlBKA,IAAAA,IAAA,CAAA,GAwBAC,IAAAA,IACVA,EAAkB,gBAAA,kBAClBA,EAAa,WAAA,aACbA,EAAQ,MAAA,QACRA,EAAe,aAAA,eAEfA,EAAU,QAAA,UAEVA,EAAS,OAAA,SAETA,EAAiB,eAAA,iBAEjBA,EAAiB,eAAA,iBAEjBA,EAAkB,gBAAA,kBAElBA,EAAY,UAAA,YAEZA,EAAmB,iBAAA,mBAEnBA,EAAiB,eAAA,iBAEjBA,EAAY,UAAA,YAtBFA,IAAAA,IAAA,CAAA,GA4BAC,IAAAA,IACVA,EAAe,aAAA,gBADLA,IAAAA,IAAA,CAAA,GAOAC,IAAAA,IAEVA,EAAa,WAAA,aAEbA,EAAe,aAAA,eAEfA,EAAgB,cAAA,gBAEhBA,EAAgB,cAAA,gBAEhBA,EAAiB,eAAA,iBAVPA,IAAAA,IAAA,CAAA,GAcL,MAAMC,GAAS,CACpB,CAAEC,IAAK,KAAMC,MAAO,OACpB,CAAED,IAAK,IAAKC,MAAO,SACnB,CAAED,IAAK,IAAKC,MAAO,YAGRC,GAAgB,CAC3B,CAAEF,IAAK,KAAMC,MAAO,OACpB,CAAED,IAAK,IAAKC,MAAO,YACnB,CAAED,IAAK,IAAKC,MAAO,UAGRE,GAAc,CACzB,CAAEH,IAAK,IAAKC,MAAO,iBACnB,CAAED,IAAK,IAAKC,MAAO,oBACnB,CAAED,IAAK,IAAKC,MAAO,qBAURG,GAAoB,CAC/B,CAAEJ,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,EAAGC,MAAO,MACjB,CAAED,IAAK,GAAIC,MAAO,OAIPI,GAAiB,CAC5B,CAAEL,IAAK,IAAKM,KAAM,OAAQL,MAAO,GACjC,CAAED,IAAK,QAASM,KAAM,OAAQL,MAAO,GACrC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,QAASM,KAAM,MAAOL,MAAO,GACpC,CAAED,IAAK,SAAUM,KAAM,MAAOL,MAAO,IACrC,CAAED,IAAK,QAASM,KAAM,OAAQL,MAAO,IACrC,CAAED,IAAK,SAAUM,KAAM,OAAQL,MAAO,KAI3BM,OAAyBC,IAAoB,CACxD,CAAC,WAAY,OACb,CAAC,QAAS,QAMCC,OAAuBD,IAAoB,CACtD,CAAC,MAAO,SACR,CAAC,KAAM,SACP,CAAC,OAAQ,SACT,CAAC,OAAQ,WA4BEE,GAAoB,CAE/BC,SAAU,IAEVC,SAAU,SAEVC,aAAc,CAEZC,IAAK,OAELC,OAAQ,OAERC,UAAW,QAGbC,aAAc,CAEZC,OAAQ,IAERC,OAAQ,IAERC,SAAU,IAEVC,MAAO,KAGTC,YAAa,CAEXC,OAAQ,IAERC,KAAM,IAENC,SAAU,IAEVC,QAAS,IAETC,KAAM,KAGRC,WAAY,CAEVC,OAAQ,IAERC,aAAc,IAEdC,QAAS,IAETC,SAAU,KAGZC,YAAa,CAEXC,UAAW,IAEXC,QAAS,KAGXC,eAAgB,IAEhBC,cAAe,IAEfC,iBAAkB,UAElBC,UAAW,UAEXC,OAAQ,CAENC,GAAI,UAEJC,GAAI,UAEJC,GAAI,UAEJC,GAAI,UAEJC,GAAI"}