import{d as e,aj as t,ai as i,b as o,r as n,b9 as s,bz as a,y as l,o as c,c as r,f as b,w as d,u,aq as v,e as p,q as m,h as f,Y as g,g as I,i as T,R as y,aR as j,b7 as h,bA as A,b8 as k,aT as _}from"./index-CkEhI1Zk.js";/* empty css                   */import{_ as x}from"./index-3RMLzyhA.js";/* empty css                    */import{_ as C}from"./index-DAulSAJI.js";/* empty css                      *//* empty css                  *//* empty css                   *//* empty css                  */import{_ as S}from"./index-ADu0XAHG.js";import{o as w}from"./order.api-B-JCVvq6.js";import{_ as F}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                */import"./index-D8c6PuWt.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                        *//* empty css                         *//* empty css                */import"./index-CDbn0nBx.js";/* empty css                          *//* empty css                 */import"./index.vue_vue_type_script_setup_true_lang-BUdGw_WH.js";/* empty css                       *//* empty css                        *//* empty css                *//* empty css                        *//* empty css                       *//* empty css                  *//* empty css                          */import"./el-form-item-l0sNRNKZ.js";const N={class:"invoiced"},V={class:"invoiced-money"},R={class:"invoiced-button"},O=e({name:"Invoiced",__name:"invoicedModal",props:{orderNo:{type:String,default:""},togetherCode:{type:String,default:""}},emits:["submit","close"],setup(e,{emit:F}){const O=e,P=F,{t:G}=t(),E=i(),q=o("1"),D=o(),B=o(),M=o(),U=n({gcode:E.gcode,hcode:E.hcode,orderNo:O.orderNo}),z=o([]),W=o([{label:G("guest"),prop:"name",width:120},{label:G("invoiceType"),width:100,render:({row:e})=>{return s("span",(t=e.invoiceTitleType,Q.filter((e=>t===e.value))[0].label));var t}},{label:G("invoiceForm"),width:100,render:({row:e})=>{return s("span",(t=e.invoiceType,X.filter((e=>t===e.value))[0].label));var t}},{label:G("invoiceNumber"),prop:"invoiceNo",width:180},{label:G("invoiceAmount"),prop:"invoiceMoney",width:100},{label:G("invoiceTaxAmount"),prop:"tax",width:100},{label:G("invoiceTime"),prop:"makeTime",width:180},{label:G("state"),render:({row:e})=>s(a,{type:1===e.state?"success":"danger"},{default:()=>function(e){let t="";switch(e){case"0":t=G("cancel1");break;case"1":t=G("normal")}return t}(e.state)})},{label:G("remark"),prop:"remark",width:180},{label:G("operations"),width:140,slot:"operations",fixed:"right"}]),L=o(!1),$=o(!1),Y=o(),H=o({togetherCode:O.togetherCode,invoiceTitleType:"0",invoiceType:"0",invoiceNo:"",invoiceMoney:0,invoiceCode:"",tax:null,invoiceTitle:"",taxpayerId:"",regAddress:"",regPhone:"",bank:"",bankNo:"",remark:"",invoicePerson:E.username,orderNo:O.orderNo,gcode:E.gcode,hcode:E.hcode}),J=o({...H.value,id:""}),K=n([]),Q=n([{label:G("invoiceTypeObj.VATSpecial"),value:"0"},{label:G("invoiceTypeObj.VATGeneral"),value:"1"},{label:G("invoiceTypeObj.individualVATGeneral"),value:"2"}]),X=n([{label:G("invoiceFormObj.paperInvoice"),value:"0"},{label:G("invoiceFormObj.electronicInvoice"),value:"1"},{label:G("invoiceFormObj.DElectronicInvoice"),value:"2"}]);const Z=n([{label:G("guest"),field:"togetherCode",type:"select",options:{data:K}},{label:G("invoiceType"),field:"invoiceTitleType",type:"radio",rules:[{required:!0,message:G("pleaseSelectInvoiceType"),trigger:["change","blue"]}],options:{data:Q}},{label:G("invoiceForm"),field:"invoiceType",type:"radio",rules:[{required:!0,message:G("pleaseSelectInvoiceForm"),trigger:["change","blue"]}],options:{data:X}},{label:G("invoiceNumber"),field:"invoiceNo",rules:[{required:!0,message:G("pleaseInputInvoiceNumber"),trigger:["change","blue"]}]},{label:G("invoiceAmount"),field:"invoiceMoney",type:"number",precision:2,rules:[{required:!0,trigger:["change","blue"],validator:function(e,t,i){t<="0"?i(new Error(G("pleaseInputInvoiceAmount"))):i()}}]},{label:G("invoiceCode"),field:"invoiceCode"},{label:G("invoiceTaxAmount"),field:"tax"},{label:G("invoiceTitle"),field:"invoiceTitle"},{label:G("unitTaxCode"),field:"taxpayerId"},{label:G("registeredAddress"),field:"regAddress"},{label:G("registeredPhone"),field:"regPhone"},{label:G("openingBank"),field:"bank"},{label:G("bankAccount"),field:"bankNo"},{label:G("remark"),field:"remark"}]);function ee(){let e="";if("1"===q.value&&!D.value.formRef)return;if("2"===q.value&&!B.value.formRef)return;"1"===q.value&&(e="invoiceCreate"),"2"===q.value&&(e="getInvoiceUpdate"),"1"===q.value&&J.value.id&&delete J.value.id,"2"===q.value&&(M.value.loading=!0);const t=Object.keys(J.value).reduce(((e,t)=>{const i=J.value[t];return null!=i&&""!==i&&!1!==i&&(e[t]=i),e}),{});D.value.formRef.validate((async i=>{i&&($.value=!0,await w[e](t),$.value=!1,j.success(G("invoiceSuccess")),"2"===q.value?(oe(),M.value.close()):(P("submit"),P("close")))}))}async function te(){K.length=0;const{data:e}=await w.getGuestList(U);e.forEach((e=>{K.push({label:e.name,value:e.togetherCode})}))}function ie(e,t){ne(),"1"===e.props.name&&te(),"2"===e.props.name&&oe()}async function oe(){L.value=!0;const{data:e}=await w.getInvoiceList(U);L.value=!1,z.value=e.list,Y.value=e.totalInvoiceMoney}function ne(){D.value&&D.value.formRef.resetFields(),B.value&&B.value.formRef.resetFields();for(const e in H.value)J.value[e]=H.value[e]}async function se(e,t){if("modify"===e){for(const e in J.value)J.value[e]=t[e];J.value.id=t.id,M.value.show()}"delete"===e&&(await w.getInvoiceUpdateState({id:t.id}),oe())}function ae(e,t){U.pageNo=e,U.pageSize=t,oe()}return l((()=>{te()})),(e,t)=>{const i=S,o=h,n=A,s=C,a=k,l=x,j=_;return c(),r("div",N,[b(a,{modelValue:u(q),"onUpdate:modelValue":t[0]||(t[0]=e=>T(q)?q.value=e:null),onTabClick:ie},{default:d((()=>[b(o,{label:u(G)("issueAnInvoice"),name:"1"},{default:d((()=>[b(i,{ref_key:"easyFormRefAdd",ref:D,class:"invoiced-form","field-list":u(Z),model:u(J),options:{labelSuffix:"：",labelWidth:160}},null,8,["field-list","model"])])),_:1},8,["label"]),b(o,{label:u(G)("invoiceRecords"),name:"2"},{default:d((()=>[v((c(),p(s,{columns:u(W),"table-data":u(z),onPaginationChange:ae},{operations:d((({row:e})=>[b(u(m),{type:"primary",link:"",onClick:t=>se("modify",e)},{default:d((()=>[f(g(u(G)("modify")),1)])),_:2},1032,["onClick"]),b(n,{title:u(G)("confirm2"),onConfirm:t=>se("delete",e)},{reference:d((()=>[b(u(m),{link:"",type:"danger"},{default:d((()=>[f(g(u(G)("cancel1")),1)])),_:1})])),_:2},1032,["title","onConfirm"])])),_:1},8,["columns","table-data"])),[[j,u(L)]]),I("div",V,g(`${u(G)("amountInvoiced")}：￥${u(Y)}`),1)])),_:1},8,["label"])])),_:1},8,["modelValue"]),I("div",R,[b(u(m),{onClick:t[1]||(t[1]=e=>P("close"))},{default:d((()=>[f(g("1"===u(q)?u(G)("cancel"):u(G)("close")),1)])),_:1}),"1"===u(q)?(c(),p(u(m),{key:0,loading:u($),type:"primary",onClick:ee},{default:d((()=>[f(g(u(G)("confirm")),1)])),_:1},8,["loading"])):y("",!0)]),b(l,{ref_key:"easyDialogRef",ref:M,title:u(G)("modify"),"is-body":"","show-cancel-button":"","show-confirm-button":"","dialog-width":"800",onSubmit:t[2]||(t[2]=e=>ee()),onClose:t[3]||(t[3]=e=>ne())},{default:d((()=>[b(i,{ref_key:"easyFormRefUpdate",ref:B,class:"invoiced-form","field-list":u(Z),model:u(J),options:{labelSuffix:"：",labelWidth:160}},null,8,["field-list","model"])])),_:1},8,["title"])])}}});function P(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"confirm"}},close:{t:0,b:{t:2,i:[{t:3}],s:"Close"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"remark"}},invoiceType:{t:0,b:{t:2,i:[{t:3}],s:"Invoice Type"}},pleaseSelectInvoiceType:{t:0,b:{t:2,i:[{t:3}],s:"Please Select Invoice Type"}},invoiceTypeObj:{VATSpecial:{t:0,b:{t:2,i:[{t:3}],s:"VAT Special Invoice"}},VATGeneral:{t:0,b:{t:2,i:[{t:3}],s:"VAT General Invoice"}},individualVATGeneral:{t:0,b:{t:2,i:[{t:3}],s:"Individual VAT General Invoice"}}},invoiceForm:{t:0,b:{t:2,i:[{t:3}],s:"Invoice Form"}},pleaseSelectInvoiceForm:{t:0,b:{t:2,i:[{t:3}],s:"Please Select Invoice Form"}},invoiceFormObj:{paperInvoice:{t:0,b:{t:2,i:[{t:3}],s:"Paper Invoice"}},electronicInvoice:{t:0,b:{t:2,i:[{t:3}],s:"Electronic Invoice"}},DElectronicInvoice:{t:0,b:{t:2,i:[{t:3}],s:"Digital Electronic Invoice"}}},invoiceNumber:{t:0,b:{t:2,i:[{t:3}],s:"Invoice number"}},pleaseInputInvoiceNumber:{t:0,b:{t:2,i:[{t:3}],s:"Please input Invoice number"}},invoiceTitle:{t:0,b:{t:2,i:[{t:3}],s:"Invoice title"}},invoiceCode:{t:0,b:{t:2,i:[{t:3}],s:"Invoice code"}},unitTaxCode:{t:0,b:{t:2,i:[{t:3}],s:"unit tax code"}},registeredAddress:{t:0,b:{t:2,i:[{t:3}],s:"Registered address"}},registeredPhone:{t:0,b:{t:2,i:[{t:3}],s:"Registered phone"}},openingBank:{t:0,b:{t:2,i:[{t:3}],s:"Opening Bank"}},bankAccount:{t:0,b:{t:2,i:[{t:3}],s:"bank account"}},invoiceAmount:{t:0,b:{t:2,i:[{t:3}],s:"Invoice amount"}},pleaseInputInvoiceAmount:{t:0,b:{t:2,i:[{t:3}],s:"Please input Invoice amount"}},invoiceTaxAmount:{t:0,b:{t:2,i:[{t:3}],s:"Invoice tax amount"}},invoiceTime:{t:0,b:{t:2,i:[{t:3}],s:"Invoice Time"}},state:{t:0,b:{t:2,i:[{t:3}],s:"State"}},modify:{t:0,b:{t:2,i:[{t:3}],s:"modify"}},normal:{t:0,b:{t:2,i:[{t:3}],s:"Normal"}},cancel1:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},guest:{t:0,b:{t:2,i:[{t:3}],s:"guest"}},invoiceRecords:{t:0,b:{t:2,i:[{t:3}],s:"Invoice Records"}},issueAnInvoice:{t:0,b:{t:2,i:[{t:3}],s:"issue an invoice"}},invoiceSuccess:{t:0,b:{t:2,i:[{t:3}],s:"invoice success"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"Operations"}},confirm2:{t:0,b:{t:2,i:[{t:3}],s:"Are you sure to invalidate this invoice record?"}},amountInvoiced:{t:0,b:{t:2,i:[{t:3}],s:"Amount Invoiced"}}},"zh-cn":{cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"确认"}},close:{t:0,b:{t:2,i:[{t:3}],s:"关闭"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},invoiceType:{t:0,b:{t:2,i:[{t:3}],s:"发票类别"}},pleaseSelectInvoiceType:{t:0,b:{t:2,i:[{t:3}],s:"请选择发票类别"}},invoiceTypeObj:{VATSpecial:{t:0,b:{t:2,i:[{t:3}],s:"公司专票"}},VATGeneral:{t:0,b:{t:2,i:[{t:3}],s:"公司普票"}},individualVATGeneral:{t:0,b:{t:2,i:[{t:3}],s:"个人普票"}}},invoiceForm:{t:0,b:{t:2,i:[{t:3}],s:"发票形式"}},pleaseSelectInvoiceForm:{t:0,b:{t:2,i:[{t:3}],s:"请选择发票类别"}},invoiceFormObj:{paperInvoice:{t:0,b:{t:2,i:[{t:3}],s:"纸质发票"}},electronicInvoice:{t:0,b:{t:2,i:[{t:3}],s:"电子发票"}},DElectronicInvoice:{t:0,b:{t:2,i:[{t:3}],s:"数电发票"}}},invoiceNumber:{t:0,b:{t:2,i:[{t:3}],s:"发票号码"}},pleaseInputInvoiceNumber:{t:0,b:{t:2,i:[{t:3}],s:"请输入发票号码"}},invoiceTitle:{t:0,b:{t:2,i:[{t:3}],s:"发票抬头"}},invoiceCode:{t:0,b:{t:2,i:[{t:3}],s:"发票代码"}},unitTaxCode:{t:0,b:{t:2,i:[{t:3}],s:"纳税人识别号"}},registeredAddress:{t:0,b:{t:2,i:[{t:3}],s:"注册地址"}},registeredPhone:{t:0,b:{t:2,i:[{t:3}],s:"注册电话"}},openingBank:{t:0,b:{t:2,i:[{t:3}],s:"开户银行"}},bankAccount:{t:0,b:{t:2,i:[{t:3}],s:"银行账号"}},invoiceAmount:{t:0,b:{t:2,i:[{t:3}],s:"发票金额"}},pleaseInputInvoiceAmount:{t:0,b:{t:2,i:[{t:3}],s:"请输入发票金额"}},invoiceTaxAmount:{t:0,b:{t:2,i:[{t:3}],s:"发票税额"}},invoiceTime:{t:0,b:{t:2,i:[{t:3}],s:"开票时间"}},state:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},modify:{t:0,b:{t:2,i:[{t:3}],s:"修改"}},normal:{t:0,b:{t:2,i:[{t:3}],s:"正常"}},cancel1:{t:0,b:{t:2,i:[{t:3}],s:"作废"}},guest:{t:0,b:{t:2,i:[{t:3}],s:"住客"}},invoiceRecords:{t:0,b:{t:2,i:[{t:3}],s:"开票记录"}},issueAnInvoice:{t:0,b:{t:2,i:[{t:3}],s:"开票"}},invoiceSuccess:{t:0,b:{t:2,i:[{t:3}],s:"发票成功"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},confirm2:{t:0,b:{t:2,i:[{t:3}],s:"确定要作废此开票记录吗？"}},amountInvoiced:{t:0,b:{t:2,i:[{t:3}],s:"已开票金额"}}},km:{cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},confirm:{t:0,b:{t:2,i:[{t:3}],s:"បញ្ជាក់"}},close:{t:0,b:{t:2,i:[{t:3}],s:"បិទ"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"សម្គាល់"}},invoiceType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទវិក័យប័ត្រ"}},pleaseSelectInvoiceType:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសប្រភេទវិក័យប័ត្រ"}},invoiceTypeObj:{VATSpecial:{t:0,b:{t:2,i:[{t:3}],s:"វិក័យប័ត្រពិសេស VAT"}},VATGeneral:{t:0,b:{t:2,i:[{t:3}],s:"វិក័យប័ត្រទូទៅ VAT"}},individualVATGeneral:{t:0,b:{t:2,i:[{t:3}],s:"វិក័យប័ត្រទូទៅ VAT បុគ្គល"}}},invoiceForm:{t:0,b:{t:2,i:[{t:3}],s:"ទម្រង់វិក័យប័ត្រ"}},pleaseSelectInvoiceForm:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសទម្រង់វិក័យប័ត្រ"}},invoiceFormObj:{paperInvoice:{t:0,b:{t:2,i:[{t:3}],s:"វិក័យប័ត្រក្រដាស"}},electronicInvoice:{t:0,b:{t:2,i:[{t:3}],s:"វិក័យប័ត្រអេឡិចត្រូនិច"}},DElectronicInvoice:{t:0,b:{t:2,i:[{t:3}],s:"វិក័យប័ត្រអេឡិចត្រូនិចឌីជីថល"}}},invoiceNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខវិក័យប័ត្រ"}},pleaseInputInvoiceNumber:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលលេខវិក័យប័ត្រ"}},invoiceTitle:{t:0,b:{t:2,i:[{t:3}],s:"ចំណងជើងវិក័យប័ត្រ"}},invoiceCode:{t:0,b:{t:2,i:[{t:3}],s:"កូដវិក័យប័ត្រ"}},unitTaxCode:{t:0,b:{t:2,i:[{t:3}],s:"លេខសម្គាល់អ្នកបង់ពន្ធ"}},registeredAddress:{t:0,b:{t:2,i:[{t:3}],s:"អាសយដ្ឋានចុះបញ្ជី"}},registeredPhone:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ធចុះបញ្ជី"}},openingBank:{t:0,b:{t:2,i:[{t:3}],s:"ធនាគារបើកគណនី"}},bankAccount:{t:0,b:{t:2,i:[{t:3}],s:"លេខគណនីធនាគារ"}},invoiceAmount:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនទឹកប្រាក់វិក័យប័ត្រ"}},pleaseInputInvoiceAmount:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលចំនួនទឹកប្រាក់វិក័យប័ត្រ"}},invoiceTaxAmount:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនពន្ធវិក័យប័ត្រ"}},invoiceTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចេញវិក័យប័ត្រ"}},state:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},modify:{t:0,b:{t:2,i:[{t:3}],s:"កែប្រែ"}},normal:{t:0,b:{t:2,i:[{t:3}],s:"ធម្មតា"}},cancel1:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},guest:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវ"}},invoiceRecords:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់ត្រាចេញវិក័យប័ត្រ"}},issueAnInvoice:{t:0,b:{t:2,i:[{t:3}],s:"ចេញវិក័យប័ត្រ"}},invoiceSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ចេញវិក័យប័ត្របានជោគជ័យ"}},operations:{t:0,b:{t:2,i:[{t:3}],s:"ប្រតិបត្តិការ"}},confirm2:{t:0,b:{t:2,i:[{t:3}],s:"តើអ្នកប្រាកដថាចង់បោះបង់កំណត់ត្រាចេញវិក័យប័ត្រនេះទេ?"}},amountInvoiced:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនទឹកប្រាក់ដែលបានចេញវិក័យប័ត្រ"}}}}})}P(O);const G=F(O,[["__scopeId","data-v-4d100962"]]);export{G as default};
//# sourceMappingURL=invoicedModal-XEk1BZXP.js.map
