{"version": 3, "file": "overBook.api-BtwES0nu.js", "sources": ["../../src/api/modules/system/group/overBook.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n/**\r\n * 超预定配置接口\r\n */\r\nexport default {\r\n  // 获取超预定列表\r\n  page: (data: any) => api.get('/admin-api/pms/over-book/page', { params: data }),\r\n\r\n  // 超预定新增\r\n  create: (data: any) => api.post('/admin-api/pms/over-book/create', data),\r\n\r\n  // 编辑超预定\r\n  edit: (data: any) => api.put('/admin-api/pms/over-book/update', data),\r\n\r\n  // 超预定详情\r\n  detail: (data: any) => api.get('/admin-api/pms/over-book/get', { params: data }),\r\n  /**\r\n   * 修改超预订配置状态\r\n   * @param data\r\n   */\r\n  updateOverBookStatus: (data: any) => api.put('/admin-api/pms/over-book/update-status', data),\r\n\r\n  /**\r\n   * 超预定配置数量\r\n   * @param data\r\n   */\r\n  getOverBookCount: (data: any) => api.get('/admin-api/pms/over-book/count', { params: data }),\r\n}\r\n"], "names": ["overBookApi", "page", "data", "api", "get", "params", "create", "post", "edit", "put", "detail", "updateOverBookStatus", "getOverBookCount"], "mappings": "mCAKA,MAAeA,EAAA,CAEbC,KAAOC,GAAcC,EAAIC,IAAI,gCAAiC,CAAEC,OAAQH,IAGxEI,OAASJ,GAAcC,EAAII,KAAK,kCAAmCL,GAGnEM,KAAON,GAAcC,EAAIM,IAAI,kCAAmCP,GAGhEQ,OAASR,GAAcC,EAAIC,IAAI,+BAAgC,CAAEC,OAAQH,IAKzES,qBAAuBT,GAAcC,EAAIM,IAAI,yCAA0CP,GAMvFU,iBAAmBV,GAAcC,EAAIC,IAAI,iCAAkC,CAAEC,OAAQH"}