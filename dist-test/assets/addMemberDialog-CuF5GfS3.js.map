{"version": 3, "file": "addMemberDialog-CuF5GfS3.js", "sources": ["../../src/views/customer/member/add/addMemberDialog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"addMember\": \"Add Member\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"addMember\": \"添加会员\"\r\n  },\r\n  \"km\": {\r\n    \"addMember\": \"បន្ថែមសមាជិក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport AddMember from '@/views/customer/member/add/index.vue'\r\n\r\nconst emits = defineEmits<{\r\n  success: []\r\n}>()\r\n\r\nconst visible = ref<boolean>(false)\r\nconst { t } = useI18n()\r\nfunction open() {\r\n  visible.value = true\r\n}\r\n\r\nfunction close() {\r\n  visible.value = false\r\n  emits('success')\r\n}\r\n\r\ndefineExpose({\r\n  open,\r\n  close,\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"visible\" :title=\"t('addMember')\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <AddMember @success=\"close\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n/* 添加会员弹窗 */\r\n:deep(.content) {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["emits", "__emit", "visible", "ref", "t", "useI18n", "close", "value", "__expose", "open"], "mappings": "izCAiBA,MAAMA,EAAQC,EAIRC,EAAUC,GAAa,IACvBC,EAAEA,GAAMC,IAKd,SAASC,IACPJ,EAAQK,OAAQ,EAChBP,EAAM,UAAS,QAGJQ,EAAA,CACXC,KAVF,WACEP,EAAQK,OAAQ,CAAA,EAUhBD"}