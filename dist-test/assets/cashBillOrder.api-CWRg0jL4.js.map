{"version": 3, "file": "cashBillOrder.api-CWRg0jL4.js", "sources": ["../../src/api/modules/pms/cashbillorder/cashBillOrder.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/cash-bill-order'\r\n/**\r\n * 现付账订单\r\n */\r\nexport default {\r\n\r\n  /**\r\n   * 创建现付账订单\r\n   * @param data\r\n   */\r\n  createCashBillOrder: (data: any) => api.post(`${BASE_PATH}/create`, data),\r\n\r\n  /**\r\n   * 冲调现付账订单\r\n   * @param data\r\n   */\r\n  revocationCashBillOrder: (data: any) => api.post('/admin-api/pms/cash-bill-order/revocation', data),\r\n\r\n  /**\r\n   * 获得现付账订单分页\r\n   * @param data\r\n   */\r\n  getCashBillOrderPage: (data: any) => api.get(`${BASE_PATH}/page`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "cashBillOrderApi", "createCashBillOrder", "data", "api", "post", "revocationCashBillOrder", "getCashBillOrderPage", "get", "params"], "mappings": "mCAEA,MAAMA,EAAY,gCAIHC,EAAA,CAMbC,oBAAsBC,GAAcC,EAAIC,KAAK,GAAGL,WAAoBG,GAMpEG,wBAA0BH,GAAcC,EAAIC,KAAK,4CAA6CF,GAM9FI,qBAAuBJ,GAAcC,EAAII,IAAI,GAAGR,SAAkB,CAAES,OAAQN"}