{"version": 3, "file": "equipmentForm-CZwEvFEt.js", "sources": ["../../src/views/merchant/equipment/info/components/equipmentForm.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"addDevice\": \"Add Device\",\r\n    \"editDevice\": \"Edit Device\",\r\n    \"deviceType\": \"Type\",\r\n    \"selectDevice\": \"Please select a device\",\r\n    \"deviceBrand\": \"Brand\",\r\n    \"enterDeviceBrand\": \"Please enter device brand\",\r\n    \"model\": \"Model\",\r\n    \"enterModel\": \"Please enter model\",\r\n    \"cardDispenserType\": \"Card Type\",\r\n    \"enterCardDispenserType\": \"Please enter card dispenser type\",\r\n    \"version\": \"Version\",\r\n    \"enterVersion\": \"Please enter version\",\r\n    \"port\": \"Port\",\r\n    \"enterPort\": \"Please enter port\",\r\n    \"customParams\": \"Parameters\",\r\n    \"paramName\": \"Name\",\r\n    \"enterParamName\": \"Enter Param Name\",\r\n    \"enterParamCode\": \"Enter Param Code\",\r\n    \"activeStatus\": \"Status\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"remark\": \"Remark\",\r\n    \"enterRemark\": \"Please enter remark\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"addSuccess\": \"Added successfully\",\r\n    \"editSuccess\": \"Edited successfully\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"addDevice\": \"新增设备\",\r\n    \"editDevice\": \"修改设备\",\r\n    \"deviceType\": \"设备类型\",\r\n    \"selectDevice\": \"请选择设备\",\r\n    \"deviceBrand\": \"设备品牌\",\r\n    \"enterDeviceBrand\": \"请输入设备品牌\",\r\n    \"model\": \"型号\",\r\n    \"enterModel\": \"请输入型号\",\r\n    \"cardDispenserType\": \"发卡器类型\",\r\n    \"enterCardDispenserType\": \"请输入发卡器类型\",\r\n    \"version\": \"版本\",\r\n    \"enterVersion\": \"请输入版本\",\r\n    \"port\": \"端口\",\r\n    \"enterPort\": \"请输入端口\",\r\n    \"customParams\": \"自定义参数\",\r\n    \"paramName\": \"参数名称\",\r\n    \"paramCode\": \"参数代码\",\r\n    \"enterParamName\": \"请输入参数名称\",\r\n    \"enterParamCode\": \"请输入参数代码\",\r\n    \"activeStatus\": \"有效标识\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"remark\": \"备注\",\r\n    \"enterRemark\": \"请输入备注\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"addSuccess\": \"新增成功\",\r\n    \"editSuccess\": \"修改成功\"\r\n  },\r\n  \"km\": {\r\n    \"addDevice\": \"បន្ថែមឧបករណ៍\",\r\n    \"editDevice\": \"កែសម្រួលឧបករណ៍\",\r\n    \"deviceType\": \"ប្រភេទឧបករណ៍\",\r\n    \"selectDevice\": \"សូមជ្រើសរើសឧបករណ៍\",\r\n    \"deviceBrand\": \"ម៉ាក\",\r\n    \"enterDeviceBrand\": \"សូមបញ្ចូលម៉ាកឧបករណ៍\",\r\n    \"model\": \"ម៉ូដែល\",\r\n    \"enterModel\": \"សូមបញ្ចូលម៉ូដែល\",\r\n    \"cardDispenserType\": \"ប្រភេទអ្នកចែកកាត\",\r\n    \"enterCardDispenserType\": \"សូមបញ្ចូលប្រភេទអ្នកចែកកាត\",\r\n    \"version\": \"កំណែ\",\r\n    \"enterVersion\": \"សូមបញ្ចូលកំណែ\",\r\n    \"port\": \"ច្រក\",\r\n    \"enterPort\": \"សូមបញ្ចូលច្រក\",\r\n    \"customParams\": \"ប៉ារ៉ាម៉ែត្រផ្ទាល់ខ្លួន\",\r\n    \"paramName\": \"ឈ្មោះប៉ារ៉ាម៉ែត្រ\",\r\n    \"enterParamName\": \"បញ្ចូលឈ្មោះប៉ារ៉ាម៉ែត្រ\",\r\n    \"enterParamCode\": \"បញ្ចូលកូដប៉ារ៉ាម៉ែត្រ\",\r\n    \"activeStatus\": \"ស្ថានភាព\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"remark\": \"ចំណាំ\",\r\n    \"enterRemark\": \"សូមបញ្ចូលចំណាំ\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"addSuccess\": \"បានបន្ថែមដោយជោគជ័យ\",\r\n    \"editSuccess\": \"បានកែប្រែដោយជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport { deviceApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { transferProperty } from '@/utils/index'\r\nimport { Minus, Plus } from '@element-plus/icons-vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n    isEdit?: boolean\r\n    infoData: any\r\n  }>(),\r\n  {\r\n    modelValue: false,\r\n    isEdit: false,\r\n    infoData: {},\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  success: []\r\n}>()\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  gcode: userStore.gcode,\r\n  hcode: userStore.hcode,\r\n  deviceType: undefined,\r\n  port: '',\r\n  brandName: '',\r\n  deviceVerName: '',\r\n  cardDispenserType: '',\r\n  version: '',\r\n  state: '1',\r\n  remark: '',\r\n  conf: [\r\n    {\r\n      parameterName: '',\r\n      parameterCode: '',\r\n    },\r\n  ],\r\n})\r\nconst formRules = ref<FormRules>({\r\n  deviceType: [{ required: true, message: t('selectDevice'), trigger: 'change' }],\r\n  brandName: [{ required: true, message: t('enterDeviceBrand'), trigger: 'blur' }],\r\n})\r\n\r\nconst equipmentList = ref<{ code: string; label: string }[]>([])\r\n/**\r\n * 获取设备类型下拉\r\n */\r\nasync function getOptionList() {\r\n  await deviceApi.getDeviceType().then((res: any) => {\r\n    equipmentList.value = res.data\r\n  })\r\n}\r\n\r\nconst title = ref(t('addDevice'))\r\nconst loading = ref(false)\r\nfunction onSubmit() {\r\n  formRef.value &&\r\n    formRef.value.validate((valid) => {\r\n      if (valid) {\r\n        if (props.isEdit) {\r\n          deviceApi\r\n            .deviceUpdate({\r\n              ...form.value,\r\n              id: props.infoData.id,\r\n              deviceCode: props.infoData.deviceCode,\r\n            })\r\n            .then((res: any) => {\r\n              if (res.code === 0) {\r\n                ElMessage.success(t('editSuccess'))\r\n                onCancel()\r\n                emits('success')\r\n              }\r\n            })\r\n        } else {\r\n          deviceApi.deviceCreate(form.value).then((res: any) => {\r\n            if (res.code === 0) {\r\n              ElMessage.success(t('addSuccess'))\r\n              onCancel()\r\n              emits('success')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    })\r\n}\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n  form.value = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    deviceType: undefined,\r\n    brandName: '',\r\n    deviceVerName: '',\r\n    cardDispenserType: '',\r\n    version: '',\r\n    port: '',\r\n    state: '1',\r\n    remark: '',\r\n    conf: [\r\n      {\r\n        parameterName: '',\r\n        parameterCode: '',\r\n      },\r\n    ],\r\n  }\r\n}\r\n\r\nfunction addRow() {\r\n  const newRow = {\r\n    parameterName: '',\r\n    parameterCode: '',\r\n  }\r\n  form.value.conf.push(newRow)\r\n}\r\nfunction deleteRow(i: number) {\r\n  form.value.conf.splice(i, 1)\r\n}\r\n\r\nonMounted(() => {\r\n  if (props.isEdit) {\r\n    title.value = t('editDevice')\r\n    transferProperty(form.value, props.infoData)\r\n  } else {\r\n    title.value = t('addDevice')\r\n  }\r\n  getOptionList()\r\n})\r\n</script>\r\n\r\n<template>\r\n  <el-dialog v-if=\"myVisible\" v-model=\"myVisible\" width=\"700px\" :title=\"title\" :close-on-click-modal=\"false\" append-to-body :modal=\"true\" destroy-on-close>\r\n    <div v-loading=\"loading\">\r\n      <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"100px\" label-suffix=\"：\">\r\n        <el-form-item :label=\"t('deviceType')\" prop=\"deviceType\">\r\n          <el-select v-model=\"form.deviceType\" :placeholder=\"t('selectDevice')\" class=\"w-80\">\r\n            <el-option v-for=\"item in equipmentList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('deviceBrand')\" prop=\"brandName\">\r\n          <el-input v-model=\"form.brandName\" :placeholder=\"t('enterDeviceBrand')\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('model')\" prop=\"deviceVerName\">\r\n          <el-input v-model=\"form.deviceVerName\" :placeholder=\"t('enterModel')\" />\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.deviceType === 'door_lock'\" :label=\"t('cardDispenserType')\" prop=\"cardDispenserType\">\r\n          <el-input v-model=\"form.cardDispenserType\" :placeholder=\"t('enterCardDispenserType')\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('version')\" prop=\"version\">\r\n          <el-input v-model=\"form.version\" :placeholder=\"t('enterVersion')\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('port')\" prop=\"port\">\r\n          <el-input v-model=\"form.port\" :placeholder=\"t('enterPort')\" />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('customParams')\">\r\n          <div v-for=\"(iem, index) in form.conf\" :key=\"index\" class=\"mb-2\" style=\"display: flex; align-items: center\">\r\n            <el-row>\r\n              <el-col :md=\"10\">\r\n                <el-form-item :label=\"t('paramName')\" label-width=\"90px\">\r\n                  <el-input v-model=\"iem.parameterName\" :placeholder=\"t('enterParamName')\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :md=\"10\">\r\n                <el-form-item :label=\"t('paramCode')\" label-width=\"90px\">\r\n                  <el-input v-model=\"iem.parameterCode\" :placeholder=\"t('enterParamCode')\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :md=\"4\">\r\n                &nbsp;&nbsp;&nbsp;\r\n                <el-button v-if=\"index === form.conf.length - 1\" :icon=\"Plus\" circle type=\"primary\" @click=\"addRow\" />\r\n                <el-button v-if=\"form.conf.length > 1\" :icon=\"Minus\" circle type=\"danger\" @click=\"deleteRow(index)\" />\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('activeStatus')\" prop=\"state\">\r\n          <el-switch v-model=\"form.state\" :active-text=\"t('yes')\" active-value=\"1\" :inactive-text=\"t('no')\" inactive-value=\"0\" inline-prompt />\r\n        </el-form-item>\r\n        <el-form-item :label=\"t('remark')\">\r\n          <el-input v-model=\"form.remark\" maxlength=\"250\" :rows=\"4\" type=\"textarea\" :placeholder=\"t('enterRemark')\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <template #footer>\r\n      <el-button size=\"large\" @click=\"onCancel\">\r\n        {{ t('cancel') }}\r\n      </el-button>\r\n      <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n        {{ t('save') }}\r\n      </el-button>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<style scoped lang=\"scss\">\r\n.box {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "userStore", "useUserStore", "myVisible", "computed", "get", "modelValue", "set", "val", "formRef", "ref", "form", "gcode", "hcode", "deviceType", "port", "brandName", "deviceVerName", "cardDispenserType", "version", "state", "remark", "conf", "parameterName", "parameterCode", "formRules", "required", "message", "trigger", "equipmentList", "title", "loading", "onSubmit", "value", "validate", "valid", "isEdit", "deviceApi", "deviceUpdate", "id", "infoData", "deviceCode", "then", "res", "code", "ElMessage", "success", "onCancel", "deviceCreate", "addRow", "push", "onMounted", "transferProperty", "async", "getDeviceType", "data", "getOptionList", "i", "splice"], "mappings": "2/BAoGA,MAAMA,EAAQC,EAYRC,EAAQC,GAIRC,EAAEA,GAAMC,IACRC,EAAYC,IAEZC,EAAYC,EAAS,CACzBC,IAAM,IACGV,EAAMW,WAEf,GAAAC,CAAIC,GACFX,EAAM,oBAAqBW,EAAG,IAI5BC,EAAUC,IACVC,EAAOD,EAAI,CACfE,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBC,gBAAY,EACZC,KAAM,GACNC,UAAW,GACXC,cAAe,GACfC,kBAAmB,GACnBC,QAAS,GACTC,MAAO,IACPC,OAAQ,GACRC,KAAM,CACJ,CACEC,cAAe,GACfC,cAAe,OAIfC,EAAYf,EAAe,CAC/BI,WAAY,CAAC,CAAEY,UAAU,EAAMC,QAAS5B,EAAE,gBAAiB6B,QAAS,WACpEZ,UAAW,CAAC,CAAEU,UAAU,EAAMC,QAAS5B,EAAE,oBAAqB6B,QAAS,WAGnEC,EAAgBnB,EAAuC,IAU7D,MAAMoB,EAAQpB,EAAIX,EAAE,cACdgC,EAAUrB,GAAI,GACpB,SAASsB,IACPvB,EAAQwB,OACNxB,EAAQwB,MAAMC,UAAUC,IAClBA,IACExC,EAAMyC,OACRC,EACGC,aAAa,IACT3B,EAAKsB,MACRM,GAAI5C,EAAM6C,SAASD,GACnBE,WAAY9C,EAAM6C,SAASC,aAE5BC,MAAMC,IACY,IAAbA,EAAIC,OACIC,EAAAC,QAAQ/C,EAAE,gBACXgD,IACTlD,EAAM,WAAS,IAIrBwC,EAAUW,aAAarC,EAAKsB,OAAOS,MAAMC,IACtB,IAAbA,EAAIC,OACIC,EAAAC,QAAQ/C,EAAE,eACXgD,IACTlD,EAAM,WAAS,IAGrB,GAEH,CAEL,SAASkD,IACP5C,EAAU8B,OAAQ,EAClBtB,EAAKsB,MAAQ,CACXrB,MAAOX,EAAUW,MACjBC,MAAOZ,EAAUY,MACjBC,gBAAY,EACZE,UAAW,GACXC,cAAe,GACfC,kBAAmB,GACnBC,QAAS,GACTJ,KAAM,GACNK,MAAO,IACPC,OAAQ,GACRC,KAAM,CACJ,CACEC,cAAe,GACfC,cAAe,KAGrB,CAGF,SAASyB,IAKFtC,EAAAsB,MAAMX,KAAK4B,KAJD,CACb3B,cAAe,GACfC,cAAe,IAEU,QAM7B2B,GAAU,KACJxD,EAAMyC,QACFN,EAAAG,MAAQlC,EAAE,cACCqD,EAAAzC,EAAKsB,MAAOtC,EAAM6C,WAE7BV,EAAAG,MAAQlC,EAAE,aA5EpBsD,uBACQhB,EAAUiB,gBAAgBZ,MAAMC,IACpCd,EAAcI,MAAQU,EAAIY,IAAA,GAC3B,CA2EaC,EAAA,mwGAXGC,SACjB9C,EAAKsB,MAAMX,KAAKoC,OAAOD,EAAG,GAD5B,IAAmBA"}