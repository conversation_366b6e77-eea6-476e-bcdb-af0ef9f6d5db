{"version": 3, "file": "orderDetail-B1c5B-Jp.js", "sources": ["../../src/views/order/info/components/orderdetail/orderDetail.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"checkInOutTime\": \"Check-in/Check-out Time\",\r\n    \"price\": \"Price\",\r\n    \"consume\": \"Consume\",\r\n    \"payment\": \"Payment\",\r\n    \"preAuth\": \"Pre-auth\",\r\n    \"coupon\": \"Coupon\",\r\n    \"makeCard\": \"Make Card\",\r\n    \"makeSameRoomCard\": \"Make Same Room Card\",\r\n    \"cancelCard\": \"Cancel Card\",\r\n    \"cardRecord\": \"Card Record\",\r\n    \"entryAccount\": \"Post\",\r\n    \"checkOut\": \"Check-out with <PERSON>\",\r\n    \"extendStay\": \"Extend/Advance\",\r\n    \"addTogether\": \"Add Guest\",\r\n    \"rent\": \"Rent\",\r\n    \"transferToTeam\": \"Transfer to Team\",\r\n    \"joinRoom\": \"Join Room\",\r\n    \"quitJoinRoom\": \"Quit Join Room\",\r\n    \"recheckIn\": \"Recheck-in\",\r\n    \"roomInfo\": \"Room Info\",\r\n    \"roomNumber\": \"Room\",\r\n    \"changeRoom\": \"Change Room\",\r\n    \"roomType\": \"Room Type\",\r\n    \"roomPrice\": \"Room Price\",\r\n    \"changePrice\": \"Change Price\",\r\n    \"breakfastCount\": \"Breakfast Count\",\r\n    \"guestInfo\": \"Guest Info\",\r\n    \"modify\": \"Edit\",\r\n    \"guestName\": \"Name\",\r\n    \"contactInfo\": \"Phone\",\r\n    \"gender\": \"Gender\",\r\n    \"birthday\": \"Birthday\",\r\n    \"idNumber\": \"ID\",\r\n    \"idType\": \"ID Type\",\r\n    \"nation\": \"Nation\",\r\n    \"address\": \"Address\",\r\n    \"orderInfo\": \"Order Info\",\r\n    \"orderNumber\": \"Order No\",\r\n    \"externalOrderNumber\": \"External Order No\",\r\n    \"checkinType\": \"Check-in Type\",\r\n    \"orderChannel\": \"Channel\",\r\n    \"roomRatePolicy\": \"Price Strategy\",\r\n    \"checkinTime\": \"Check-in Time\",\r\n    \"stayDuration\": \"Days\",\r\n    \"plannedCheckoutTime\": \"Checkout Time\",\r\n    \"salesperson\": \"Salesperson\",\r\n    \"guestSourceType\": \"Source Type\",\r\n    \"searchMember\": \"Search Member\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"phone\": \"Phone\",\r\n    \"balance\": \"Balance\",\r\n    \"membershipLevel\": \"Membership Level\",\r\n    \"otherInfo\": \"Other Info\",\r\n    \"remarks\": \"Remark\",\r\n    \"outJoinRoom\": \"Exit Join Room\",\r\n    \"selectedRoom\": \"Selected Room\",\r\n    \"confirmOutJoinRoom\": \"Are you sure you want to exit the joined room?\",\r\n    \"confirm\": \"Confirm\",\r\n    \"unlinkRoomSuccess\": \"Successfully unlinked the room\",\r\n    \"renewStayBeforeSwitch\": \"Please renew your stay before changing the room\",\r\n    \"renewStayBeforeChangePrice\": \"Please renew your stay before changing the price\",\r\n    \"enterPhoneToGetMemberInfo\": \"Please enter phone number to get member information\",\r\n    \"selectAgent\": \"Please select an agent\",\r\n    \"selectCompany\": \"Please select a company\",\r\n    \"sourceTypeUpdateSuccess\": \"Successfully updated the source type\",\r\n    \"submitError\": \"An error occurred while submitting\",\r\n    \"confirmRecheckIn\": \"Are you sure you want to re-check-in?\",\r\n    \"checkInSuccess\": \"Check-in successful\",\r\n    \"yuan\": \"$\",\r\n    \"female\": \"Female\",\r\n    \"male\": \"Male\",\r\n    \"secret\": \"Secret\",\r\n    \"printCheckInForm\": \"Print Check-In Form\",\r\n    \"fullPhone\": \"View full phone\",\r\n    \"fullIdNo\": \"View full IdNO\",\r\n    \"read_card_in_client\": \"Reading room cards can only be performed in the client environment. Please {downloadLink}.\",\r\n    \"download_client\": \"download the PMS client\",\r\n    \"lock_card_success\": \"{roomNo} room lock card written successfully\",\r\n    \"cancel_card_success\": \"Cancel card successfully\",\r\n\t\t\"addBreakfas\":\"Add Breakfas\",\r\n\t\t\"date\":\"Date\",\r\n\t\t\"roomsWith\":\"Rooms with breakfast included\",\r\n\t\t\"complimentary\":\"complimentary\",\r\n\t\t\"purchase\":\"Purchase\",\r\n\t\t\"pleaseInput\":\"Please input\",\r\n\t\t\"unifiedGiveaway\":\"Unified giveaway every day\",\r\n\t\t\"purchasedCopies\":\"Purchased copies\",\r\n\t\t\"addPurchasedCopies\":\"Additional purchased copies\",\r\n\t\t\"unitPrice\":\"unit price\",\r\n\t\t\"servings\":\"servings\",\r\n    \"selectSalesperson\": \"Select\",\r\n    \"updateSuccess\": \"Successfully\",\r\n    \"addWakeupTime\":\"Add Wakeup Time\",\r\n    \"wakeupTime\":\"Wakeup Time\",\r\n    \"wakeupTimePlaceholder\":\"Please enter the wakeup time\",\r\n    \"editWakeupTime\":\"Eidt Wakeup Time\",\r\n    \"confirmDeleteWakeup\": \"Are you sure to delete the wake-up call?\",\r\n\t\t\"name\": \"Name\",\r\n    \"checkOutTime\": \"Check-out Time\",\r\n    \"outOrderRemark\": \"External order remark\",\r\n    \"hotel_no_lock_config\": \"The hotel has not configured a door lock. Please go to 'Lock Configuration' to select the door lock model used by the hotel. If there is no matching lock model, please contact the service provider.\",\r\n    \"wakeUpReminder\": \"Wake-up reminder\",\r\n  },\r\n  \"zh-cn\": {\r\n    \"checkInOutTime\": \"入离时间\",\r\n    \"price\": \"房价\",\r\n    \"consume\": \"消费\",\r\n    \"payment\": \"付款\",\r\n    \"preAuth\": \"预授权\",\r\n    \"coupon\": \"优惠卷\",\r\n    \"makeCard\": \"制新卡\",\r\n    \"makeSameRoomCard\": \"制同住卡\",\r\n    \"cancelCard\": \"注销卡\",\r\n    \"cardRecord\": \"门卡记录\",\r\n    \"entryAccount\": \"入账\",\r\n    \"checkOut\": \"结账退房\",\r\n    \"extendStay\": \"续住/提前\",\r\n    \"addTogether\": \"添加同住\",\r\n    \"rent\": \"租借\",\r\n    \"transferToTeam\": \"转入团队\",\r\n    \"joinRoom\": \"加入联房\",\r\n    \"quitJoinRoom\": \"退出联房\",\r\n    \"recheckIn\": \"重新入住\",\r\n    \"roomInfo\": \"房间信息\",\r\n    \"roomNumber\": \"房号\",\r\n    \"changeRoom\": \"换房\",\r\n    \"roomType\": \"房型\",\r\n    \"roomPrice\": \"房价\",\r\n    \"changePrice\": \"改价\",\r\n    \"breakfastCount\": \"当天可用早餐数\",\r\n    \"guestInfo\": \"客人信息\",\r\n    \"modify\": \"修改\",\r\n    \"guestName\": \"客人姓名\",\r\n    \"contactInfo\": \"联系方式\",\r\n    \"gender\": \"性别\",\r\n    \"birthday\": \"出生日期\",\r\n    \"idNumber\": \"证件号码\",\r\n    \"idType\": \"证件类型\",\r\n    \"nation\": \"民族\",\r\n    \"address\": \"地址\",\r\n    \"orderInfo\": \"订单信息\",\r\n    \"orderNumber\": \"订单号\",\r\n    \"externalOrderNumber\": \"外部订单号\",\r\n    \"checkinType\": \"入住类型\",\r\n    \"orderChannel\": \"订单渠道\",\r\n    \"roomRatePolicy\": \"房价策略\",\r\n    \"checkinTime\": \"入住时间\",\r\n    \"stayDuration\": \"入住天数\",\r\n    \"plannedCheckoutTime\": \"预离时间\",\r\n    \"salesperson\": \"销售员\",\r\n    \"guestSourceType\": \"客源类型\",\r\n    \"searchMember\": \"搜索会员\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"phone\": \"电话\",\r\n    \"balance\": \"余额\",\r\n    \"membershipLevel\": \"会员级别\",\r\n    \"otherInfo\": \"其他信息\",\r\n    \"remarks\": \"备注\",\r\n    \"outJoinRoom\": \"退出联房\",\r\n    \"selectedRoom\": \"选中房间\",\r\n    \"confirmOutJoinRoom\": \"是否确定退出联房?\",\r\n    \"confirm\": \"确定\",\r\n    \"unlinkRoomSuccess\": \"退出联房成功\",\r\n    \"renewStayBeforeSwitch\": \"请先续住再换房\",\r\n    \"renewStayBeforeChangePrice\": \"请续住后再改价\",\r\n    \"enterPhoneToGetMemberInfo\": \"请输入手机号获取会员信息\",\r\n    \"selectAgent\": \"请选择中介\",\r\n    \"selectCompany\": \"请选择单位\",\r\n    \"sourceTypeUpdateSuccess\": \"修改客源类型成功\",\r\n    \"submitError\": \"提交时发生错误\",\r\n    \"confirmRecheckIn\": \"确定重新入住吗?\",\r\n    \"checkInSuccess\": \"入住成功\",\r\n    \"yuan\": \"￥\",\r\n    \"female\": \"女\",\r\n    \"male\": \"男\",\r\n    \"secret\": \"保密\",\r\n    \"printCheckInForm\": \"打印入住单\",\r\n    \"fullPhone\": \"查看完整手机号\",\r\n    \"fullIdNo\": \"查看完整证件号\",\r\n    \"read_card_in_client\": \"读取房卡只能在Hotel-Agent中操作，请{downloadLink}。\",\r\n    \"download_client\": \"下载Hotel-Agent客户端\",\r\n    \"lock_card_success\": \"{roomNo}房制卡成功\",\r\n    \"cancel_card_success\": \"清卡成功\",\r\n\t\t\"addBreakfas\":\"添加早餐\",\r\n\t\t\"date\":\"日期\",\r\n\t\t\"roomsWith\":\"房包早\",\r\n\t\t\"complimentary\":\"赠送\",\r\n\t\t\"purchase\":\"购买\",\r\n\t\t\"pleaseInput\":\"请输入\",\r\n\t\t\"unifiedGiveaway\":\"每天统一赠送\",\r\n\t\t\"purchasedCopies\":\"已购份数\",\r\n\t\t\"addPurchasedCopies\":\"加购份数\",\r\n\t\t\"unitPrice\":\"单价\",\r\n\t\t\"servings\":\"份\",\r\n    \"selectSalesperson\": \"选择销售员\",\r\n    \"updateSuccess\": \"修改成功\",\r\n    \"addWakeupTime\":\"添加叫早\",\r\n    \"wakeupTime\":\"叫早时间\",\r\n    \"wakeupTimePlaceholder\":\"请选择叫早时间\",\r\n    \"editWakeupTime\": \"修改叫早\",\r\n    \"confirmDeleteWakeup\": \"确定要删除叫早提醒吗？\",\r\n\t\t\"name\": \"姓名\",\r\n    \"checkOutTime\": \"退房时间\",\r\n    \"outOrderRemark\": \"外部订单备注\",\r\n    \"hotel_no_lock_config\": \"酒店没有配置门锁，请到'房锁配置'里选择酒店所用门锁型号，如果没有匹配的门锁型号，请联系服务商。\",\r\n    \"wakeUpReminder\": \"叫早提醒\",\r\n  },\r\n  \"km\": {\r\n    \"checkInOutTime\": \"ពេលវេលាចូល/ចេញ\",\r\n    \"price\": \"តម្លៃបន្ទប់\",\r\n    \"consume\": \"ការប្រើប្រាស់\",\r\n    \"payment\": \"ការទូទាត់\",\r\n    \"preAuth\": \"ការអនុញ្ញាតជាមុន\",\r\n    \"coupon\": \"គូប៉ុង\",\r\n    \"makeCard\": \"បង្កើតកាត\",\r\n    \"makeSameRoomCard\": \"បង្កើតកាតបន្ទប់ដូចគ្នា\",\r\n    \"cancelCard\": \"លុបកាត\",\r\n    \"cardRecord\": \"កំណត់ត្រាកាត\",\r\n    \"entryAccount\": \"បញ្ចូលគណនី\",\r\n    \"checkOut\": \"ចេញពីបន្ទប់ជាមួយការសង\",\r\n    \"extendStay\": \"បន្ត/ពន្យាពេល\",\r\n    \"addTogether\": \"បន្ថែមភ្ញៀវរួម\",\r\n    \"rent\": \"ជួល\",\r\n    \"transferToTeam\": \"ផ្ទេរទៅក្រុម\",\r\n    \"joinRoom\": \"ចូលរួមបន្ទប់\",\r\n    \"quitJoinRoom\": \"ចាកចេញពីបន្ទប់រួម\",\r\n    \"recheckIn\": \"ចូលស្នាក់នៅឡើងវិញ\",\r\n    \"roomInfo\": \"ព័ត៌មានបន្ទប់\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"changeRoom\": \"ផ្លាស់បន្ទប់\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"roomPrice\": \"តម្លៃបន្ទប់\",\r\n    \"changePrice\": \"ផ្លាស់ប្តូរតម្លៃ\",\r\n    \"breakfastCount\": \"ចំនួនអាហារពេលព្រឹក\",\r\n    \"guestInfo\": \"ព័ត៌មានភ្ញៀវ\",\r\n    \"modify\": \"កែសម្រួល\",\r\n    \"guestName\": \"ឈ្មោះភ្ញៀវ\",\r\n    \"contactInfo\": \"ព័ត៌មានទំនាក់ទំនង\",\r\n    \"gender\": \"ភេទ\",\r\n    \"birthday\": \"ថ្ងៃខែឆ្នាំកំណើត\",\r\n    \"idNumber\": \"លេខអត្តសញ្ញាណប័ណ្ណ\",\r\n    \"idType\": \"ប្រភេទអត្តសញ្ញាណប័ណ្ណ\",\r\n    \"nation\": \"សញ្ជាតិ\",\r\n    \"address\": \"អាសយដ្ឋាន\",\r\n    \"orderInfo\": \"ព័ត៌មានការកម្មង់\",\r\n    \"orderNumber\": \"លេខការកម្មង់\",\r\n    \"externalOrderNumber\": \"លេខការកម្មង់ខាងក្រៅ\",\r\n    \"checkinType\": \"ប្រភេទការចូលស្នាក់នៅ\",\r\n    \"orderChannel\": \"ឆានែលការកម្មង់\",\r\n    \"roomRatePolicy\": \"យុទ្ធសាស្ត្រតម្លៃបន្ទប់\",\r\n    \"checkinTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n    \"stayDuration\": \"ចំនួនថ្ងៃស្នាក់នៅ\",\r\n    \"plannedCheckoutTime\": \"ពេលវេលាចេញដែលបានគ្រោងទុក\",\r\n    \"salesperson\": \"អ្នកលក់\",\r\n    \"guestSourceType\": \"ប្រភេទប្រភពភ្ញៀវ\",\r\n    \"searchMember\": \"ស្វែងរកសមាជិក\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"phone\": \"ទូរស័ព្ទ\",\r\n    \"balance\": \"សមតុល្យ\",\r\n    \"membershipLevel\": \"កម្រិតសមាជិកភាព\",\r\n    \"otherInfo\": \"ព័ត៌មានផ្សេងទៀត\",\r\n    \"remarks\": \"ចំណាំ\",\r\n    \"outJoinRoom\": \"ចាកចេញពីបន្ទប់រួម\",\r\n    \"selectedRoom\": \"បន្ទប់ដែលបានជ្រើសរើស\",\r\n    \"confirmOutJoinRoom\": \"តើអ្នកប្រាកដថាចង់ចាកចេញពីបន្ទប់រួមទេ?\",\r\n    \"confirm\": \"បញ្ជាក់\",\r\n    \"unlinkRoomSuccess\": \"ចាកចេញពីបន្ទប់រួមបានជោគជ័យ\",\r\n    \"renewStayBeforeSwitch\": \"សូមបន្តស្នាក់នៅមុនពេលផ្លាស់បន្ទប់\",\r\n    \"renewStayBeforeChangePrice\": \"សូមបន្តស្នាក់នៅមុនពេលផ្លាស់ប្តូរតម្លៃ\",\r\n    \"enterPhoneToGetMemberInfo\": \"សូមបញ្ចូលលេខទូរស័ព្ទដើម្បីទទួលព័ត៌មានសមាជិក\",\r\n    \"selectAgent\": \"សូមជ្រើសរើសភ្នាក់ងារ\",\r\n    \"selectCompany\": \"សូមជ្រើសរើសក្រុមហ៊ុន\",\r\n    \"sourceTypeUpdateSuccess\": \"កែប្រែប្រភេទប្រភពភ្ញៀវបានជោគជ័យ\",\r\n    \"submitError\": \"មានកំហុសក្នុងការដាក់ស្នើ\",\r\n    \"confirmRecheckIn\": \"តើអ្នកប្រាកដថាចង់ចូលស្នាក់នៅឡើងវិញទេ?\",\r\n    \"checkInSuccess\": \"ចូលស្នាក់នៅបានជោគជ័យ\",\r\n    \"yuan\": \"៛\",\r\n    \"female\": \"ស្រី\",\r\n    \"male\": \"ប្រុស\",\r\n    \"secret\": \"អាថ៌កំបាំង\",\r\n    \"printCheckInForm\": \"បោះពុម្ពទម្រង់ចូលស្នាក់នៅ\",\r\n    \"fullPhone\": \"មើលលេខទូរស័ព្ទពេញ\",\r\n    \"fullIdNo\": \"មើលលេខអត្តសញ្ញាណប័ណ្ណពេញ\",\r\n    \"read_card_in_client\": \"ការអានកាតបន្ទប់អាចធ្វើបានតែនៅក្នុងបរិស្ថានអតិថិជនប៉ុណ្ណោះ។ សូម {downloadLink}។\",\r\n    \"download_client\": \"ទាញយកអតិថិជន PMS\",\r\n    \"lock_card_success\": \"កាតបន្ទប់ {roomNo} ត្រូវបានបង្កើតដោយជោគជ័យ\",\r\n    \"cancel_card_success\": \"លុបកាតបានជោគជ័យ\",\r\n    \"addBreakfas\": \"បន្ថែមអាហារពេលព្រឹក\",\r\n    \"date\": \"កាលបរិច្ឆេទ\",\r\n    \"roomsWith\": \"បន្ទប់ដែលមានអាហារពេលព្រឹក\",\r\n    \"complimentary\": \"អំណោយ\",\r\n    \"purchase\": \"ទិញ\",\r\n    \"pleaseInput\": \"សូមបញ្ចូល\",\r\n    \"unifiedGiveaway\": \"អំណោយជារៀងរាល់ថ្ងៃ\",\r\n    \"purchasedCopies\": \"ចំនួនដែលបានទិញ\",\r\n    \"addPurchasedCopies\": \"ចំនួនបន្ថែមដែលបានទិញ\",\r\n    \"unitPrice\": \"តម្លៃឯកតា\",\r\n    \"servings\": \"ចំនួន\",\r\n    \"selectSalesperson\": \"ជ្រើសរើសអ្នកលក់\",\r\n    \"updateSuccess\": \"កែប្រែបានជោគជ័យ\",\r\n    \"addWakeupTime\": \"បន្ថែមពេលវេលាដាស់\",\r\n    \"wakeupTime\": \"ពេលវេលាដាស់\",\r\n    \"wakeupTimePlaceholder\": \"សូមជ្រើសរើសពេលវេលាដាស់\",\r\n    \"editWakeupTime\": \"កែសម្រួលពេលវេលាដាស់\",\r\n    \"confirmDeleteWakeup\": \"តើអ្នកប្រាកដថាចង់លុបការដាស់ដែរឬទេ?\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"checkOutTime\": \"ពេលវេលាចេញ\",\r\n    \"outOrderRemark\": \"ចំណាំការកម្មង់ខាងក្រៅ\",\r\n    \"hotel_no_lock_config\": \"សណ្ឋាគារមិនទាន់មានការកំណត់រចនាសម្ព័ន្ធសោទេ។ សូមចូលទៅកាន់ 'ការកំណត់រចនាសម្ព័ន្ធសោ' ដើម្បីជ្រើសរើសម៉ូដែលសោដែលសណ្ឋាគារប្រើប្រាស់។ ប្រសិនបើគ្មានម៉ូដែលសោដែលត្រូវគ្នាទេ សូមទាក់ទងអ្នកផ្តល់សេវាកម្ម។\",\r\n    \"wakeUpReminder\": \"បញ្ហាការដាស់\",\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { HotelDeviceSetRespVO } from '@/api/modules/pms/device/device.api'\r\nimport type { DictDataModel } from '@/models'\r\nimport type orderDetail from './orderDetail.d.ts'\r\nimport type { HourRoom, OrderCheckinTypeRespVO, updateCheckinTypes } from './orderDetail.d.ts'\r\nimport { accountApi, customerApi, deviceApi, dictDataApi, memberApi, orderApi, serverTimeApi, userApi } from '@/api/modules/index'\r\nimport rentApi from '@/api/modules/pms/goods/rentGoods.api'\r\nimport roomCardLogApi from '@/api/modules/pms/room/roomCardLog.api'\r\nimport { BooleanEnum, CheckinType, ClientMethodEnum, CONSTANT_TYPE_CODE_SZ, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUARANTEE_TYPE, DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_ID_TYPE, GuestSrcType, IdType, ORDER_SOURCE, OrderState, OrderType } from '@/models'\r\nimport { SexEnum } from '@/models/dict/constants.ts'\r\nimport useUserStore from '@/store/modules/user'\r\nimport { CardReader } from '@/store/websocket/CardReader'\r\nimport { deepCopy } from '@/utils'\r\nimport { generateCardInfoHtml } from '@/utils/roomCardUtil'\r\nimport { saveAndMask } from '@/utils/sensitive'\r\nimport PrintCheckInForm from '@/views/print/checkinForm.vue'\r\nimport CohabitAtion from '@/views/room/components/cohabitAtion/index.vue'\r\nimport EntryAccount from '@/views/room/components/entryAccount/index.vue'\r\nimport LeavePrice from '@/views/room/components/leavePrice/index.vue'\r\nimport LianFang from '@/views/room/components/lianFang/index.vue'\r\nimport RevokeCheckout from '@/views/room/components/revokeCheckout/index.vue'\r\nimport RoomEditPrice from '@/views/room/components/roomEditPrice/index.vue'\r\nimport TransferTeam from '@/views/room/components/transferTeam/index.vue'\r\nimport RentList from '@/views/room/goods/rent/list.vue'\r\nimport RoomContinue from '@/views/room/realtime/components/roomContinue.vue'\r\nimport RoomExchange from '@/views/room/realtime/components/roomExchange.vue'\r\nimport { getBirthdayFromIdCard } from '@/views/room/realtime/components/utils'\r\nimport { Delete, Hide, View } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { ElInputNumber, ElNotification, type TabsPaneContext } from 'element-plus'\r\nimport GrantModal from './grantModal.vue'\r\nimport RoomCardLogList from './roomCardLogList.vue'\r\n// orderNo: 订单号\r\n// noType: order\r\n// togetherCode: 客人代码\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    orderNo: string[]\r\n    togetherCode: number | string\r\n    noType: string\r\n    isEntryAccount: string\r\n  }>(),\r\n  {\r\n    orderNo: () => [],\r\n    togetherCode: '',\r\n    noType: '',\r\n    isEntryAccount: '0',\r\n  }\r\n)\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  editStatus: [value: boolean]\r\n  success: []\r\n  refresh: []\r\n}>()\r\nconst { t } = useI18n()\r\nfunction orderRefresh() {\r\n  emits('refresh')\r\n  emits('success')\r\n}\r\n\r\n/** 获取缓存数据 */\r\nconst userStore = useUserStore()\r\n\r\n/** 通用字典 */\r\nconst dictTypes = [DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_CHECKIN_TYPE, ORDER_SOURCE, CONSTANT_TYPE_CODE_SZ, DICT_TYPE_GUARANTEE_TYPE]\r\n/** 获根据会员代码获取会员详情 */\r\nconst memberCardDetail = ref<orderDetail.MemberDetail>({\r\n  name: '',\r\n  mtName: '',\r\n  phone: '',\r\n  storeCardBalance: 0,\r\n})\r\nconst updateGuestSrcType = ref(true)\r\nconst updateRemark = ref(true)\r\n/** 修改销售员 */\r\nconst isUpdateSeller = ref(true)\r\n/** 销售员列表 */\r\nconst sellers = ref<{ username: string; nickname: string }[]>([])\r\n/** 公用参数 */\r\nconst queryParams = reactive({\r\n  /** 集团代码 */\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n})\r\nconst updateOutOrderNo = ref(true)\r\nconst entryAccountVisible = ref(false)\r\n/** 弹出打印入住单界面 */\r\nconst printCheckInFormVisible = ref(false)\r\n/** 展示结账退房弹窗 */\r\nconst entryLeavePriceVisible = ref(false)\r\n/** 展示部分结账弹窗 */\r\nconst entryPartialCheckoutVisible = ref(false)\r\n/** 标题名称 */\r\nconst titleName = ref('')\r\n/** 展示部冲红,转账弹窗 */\r\nconst entryBillCheckVisible = ref(false)\r\n/** 展示撤销结账弹窗 */\r\nconst entryRevokeCheckoutVisible = ref(false)\r\n/** 展示续住/提前弹窗 */\r\nconst entryRoomContinueVisible = ref(false)\r\n/** 换房弹窗 */\r\nconst entryexchangeVisible = ref(false)\r\n/** 展示同住弹窗 */\r\nconst cohabitAtionTitle = ref('')\r\nconst entrycohabitAtionVisible = ref(false)\r\n/** 标签名称 */\r\nconst tabName = ref('')\r\nconst hotelDoorConfig = ref<HotelDeviceSetRespVO>()\r\n/** 会员 */\r\nconst members = ref<orderDetail.membersType[]>([])\r\n/** 会员form */\r\nconst memberForm = ref<orderDetail.memberFormType>({\r\n  guestSrcType: '',\r\n  // 手机号\r\n  phone: '',\r\n  mcode: '',\r\n  agentCode: '',\r\n  protocolCode: '',\r\n  loading: false,\r\n})\r\n/** 展示入账页面form */\r\nconst orderVisible = ref<orderDetail.orderVisibleType>({\r\n  accountOrderNo: '',\r\n  togetherCode: '',\r\n  tabId: '0',\r\n  type: 'order',\r\n})\r\n/** 是否修改入住类型 */\r\nconst isCheckin = ref(false)\r\n/** 备注 */\r\nconst remark = ref()\r\n/** 弹出预授权弹窗 */\r\nconst preAuthVisible = ref(false)\r\n/** 中介列表 */\r\nconst agents = ref<{ paCode: string; paName: string }[]>([])\r\n/** 协议单位列表 */\r\nconst protocols = ref<{ paCode: string; paName: string }[]>([])\r\n/** 客源类型列表 */\r\nconst srcTypeList = ref<orderDetail.typeList[]>([])\r\n/** 入住类型列表 */\r\nconst checkinTypeList = ref<orderDetail.typeList[]>([])\r\n/** 订单来源列表 */\r\nconst orderSources = ref<orderDetail.typeList[]>([])\r\n/** 小时列表 */\r\nconst hourRts = ref<orderDetail.typeList[]>([])\r\n/** 担保列表 */\r\nconst dbs = ref<orderDetail.typeList[]>([])\r\n\r\nconst dt = ref({\r\n  order: {\r\n    hourCode: '',\r\n    protocolCode: '',\r\n    mcode: '',\r\n    agentCode: '',\r\n    guestCode: '',\r\n    /** 可用餐数 */\r\n    buyBkNum: '',\r\n    /** 订单号 */\r\n    orderNo: '',\r\n    /** 外部订单号 */\r\n    outOrderNo: '',\r\n    /** 客人姓名 */\r\n    guestName: '',\r\n    /** 如果是团队订单，这里写团队名称 */\r\n    teamName: '',\r\n    /** 拼音 */\r\n    pinyin: '',\r\n    /** 电话 */\r\n    phone: '',\r\n    /** 渠道代码 */\r\n    channelCode: '',\r\n    /** 渠道名称 */\r\n    channelName: '',\r\n    /** 订单来源 */\r\n    orderSource: '',\r\n    /** 地址 */\r\n    address: '',\r\n    orderSourceName: '',\r\n    /** 入住类型 */\r\n    checkinType: '',\r\n    /** 入住类型名称 */\r\n    checkinTypeName: '',\r\n    /** 入住时间 */\r\n    checkinTime: '',\r\n    /** 预抵时间 */\r\n    planCheckinTime: '',\r\n    /** 预离时间 */\r\n    planCheckoutTime: '',\r\n    /** 房型代码 */\r\n    rtCode: '',\r\n    rtName: '',\r\n    /** 房间代码 */\r\n    rCode: '',\r\n    /** 房号 */\r\n    rNo: '',\r\n    /** 锁号 */\r\n    lockNo: '',\r\n    // mac地址\r\n    mac: '',\r\n    // 锁版本\r\n    lockVersion: '',\r\n    // 楼栋号\r\n    buildNo: '',\r\n    // 楼层号\r\n    floorNo: '',\r\n    /** 客源类型 */\r\n    guestSrcType: '',\r\n    guestSrcTypeName: '',\r\n    /** 预订单号 */\r\n    bookNo: '',\r\n    /** 订单类型 */\r\n    orderType: '',\r\n    /** 绑定代码 */\r\n    bindCode: '',\r\n    /** 是否允许改价 */\r\n    canChangePrice: '',\r\n    /** 是否主订单 是否为主订单（0否 1是） */\r\n    isMain: '',\r\n    /** 账务状态 账务状态（1已结  2未结） */\r\n    accState: '',\r\n    /** 是否已开发票 */\r\n    isInvoice: '',\r\n    /** 早餐券数量 */\r\n    bkTicketNum: 0,\r\n    /** 活动代码 */\r\n    activityCode: '',\r\n    /** 房价策略代码 */\r\n    priceStrategyCode: '',\r\n    /** 订单状态 */\r\n    state: '',\r\n    /** 离店状态 */\r\n    checkoutState: '',\r\n    /** 是否已制卡 */\r\n    isMadeCard: '',\r\n    /** 开房班次 */\r\n    checkinShiftNo: '',\r\n    /** 开房操作者 */\r\n    checkinOperator: '',\r\n    /** 退房班次 */\r\n    checkoutShiftNo: '',\r\n    /** 退房时间 */\r\n    checkoutTime: '',\r\n    /** 退房操作者 */\r\n    checkoutOperator: '',\r\n    /** 结账班次 */\r\n    payShiftNo: '',\r\n    /** 结账时间 */\r\n    payTime: '',\r\n    /** 结账操作者 */\r\n    payOperator: '',\r\n    /** 销售者 */\r\n    seller: '',\r\n    sellerName: '',\r\n    // 入住天数\r\n    days: 0,\r\n    /** 总消费 */\r\n    totalFee: 0,\r\n    /** 金额(余额) */\r\n    balance: 0,\r\n    /** 房价 */\r\n    vipPrice: 0,\r\n    prices: [] as orderDetail.pricesType[],\r\n    /** 欠费状态 */\r\n    arrState: '',\r\n    /** 是否交押金 */\r\n    deposit: '',\r\n    /** 订单备注 */\r\n    remark: '',\r\n    /** 外部订单备注 */\r\n    outOrderRemark: '',\r\n  },\r\n  summary: {\r\n    /** 消费 */\r\n    consumeAmount: null as unknown as number | undefined,\r\n    /** 付款 */\r\n    payAmount: null as unknown as number | undefined,\r\n    /** 预授权 */\r\n    preAuthAmount: null as unknown as number | undefined,\r\n    /** 优惠券 */\r\n    couponAmount: null as unknown as number | undefined,\r\n    /** 余额 */\r\n    balanceAmount: null as unknown as number | undefined,\r\n  },\r\n  orderPrice: {\r\n    /** 基础价 */\r\n    price: 0,\r\n    /** 当天 */\r\n    priceDate: '',\r\n    /** 优惠价 */\r\n    vipPrice: 0,\r\n    /** 星期几 */\r\n    week: 0,\r\n  },\r\n  orderPrices: [] as orderDetail.OrderPrice[],\r\n  orderTogether: {\r\n    /** 客人代码 */\r\n    togetherCode: '',\r\n    /** 客人姓名 */\r\n    name: '',\r\n    /** 证件类型 */\r\n    idType: IdType.IDCERT,\r\n    idTypeName: '',\r\n    nation: '',\r\n    /** 证件号码 */\r\n    idNo: '',\r\n    /** 地址 */\r\n    address: '',\r\n    /** 手机号码 */\r\n    phone: '',\r\n    /** 性别 1,男 0,女 2,保密 */\r\n    sex: SexEnum.MALE.toString(),\r\n    rCode: '',\r\n    rNo: '',\r\n    no: '',\r\n    isMain: BooleanEnum.YES,\r\n    isTeam: BooleanEnum.NO,\r\n    state: OrderState.CHECK_IN,\r\n    // 叫醒时间\r\n    awakenTime: '',\r\n    // 是否保密\r\n    secrecy: '0',\r\n    // 免打扰\r\n    noDisturbing: '0',\r\n    nationName: '',\r\n    /** 入住时间 */\r\n    checkinTime: '',\r\n    checkoutTime: '',\r\n    /** 预离时间 */\r\n    planCheckoutTime: '',\r\n  },\r\n})\r\n\r\nconst serverTime = ref('')\r\n\r\n/** 证件类型列表 */\r\nconst idTypes = ref<DictDataModel[]>([])\r\n/** 入住类型列表 */\r\nconst checkinTypes = ref<OrderCheckinTypeRespVO[]>([])\r\n/** 钟点房列表 */\r\nconst hourRoom = ref<HourRoom[]>([])\r\n/** 客源类型列表 */\r\nconst guestSrcTypes = ref<DictDataModel[]>([])\r\n/** 添加早餐弹窗 */\r\nconst easyDialogRef = ref()\r\n\r\n// 脱敏状态管理\r\nconst sensitiveStatus = ref({\r\n  isShowFullPhone: false,\r\n  isShowFullIdNo: false,\r\n  isShowFullAddress: false,\r\n})\r\n\r\n// 存储原始值\r\nconst originalValues = ref({\r\n  phone: '',\r\n  idNo: '',\r\n  address: '',\r\n})\r\nconst activeName = ref<string | number | undefined>(1)\r\nconst pleaseInput = ref()\r\n/** 表格配置(鼠标弹窗) */\r\nconst options = ref<any>({\r\n  maxHeight: 250,\r\n  paginationConfig: {\r\n    pageLimit: 100,\r\n  },\r\n})\r\nconst loading = ref(false)\r\n/** 后台获取到的数据对象 */\r\nconst tableData = ref<orderDetail.tableData[]>([])\r\nconst tableData1 = ref<orderDetail.tableData[]>([])\r\nconst tableData2 = ref<orderDetail.tableData[]>([])\r\n\r\n/** 表格配置(鼠标弹窗) */\r\nconst tableColumns = reactive<Table.Column<orderDetail.tableData>[]>([\r\n  {\r\n    prop: 'priceDate',\r\n    label: t('date'),\r\n  },\r\n  {\r\n    prop: 'roomBkNum',\r\n    label: t('roomsWith'),\r\n  },\r\n  {\r\n    prop: 'bkNum',\r\n    label: t('complimentary'),\r\n  },\r\n  {\r\n    prop: 'buyedBkNum',\r\n    label: t('purchase'),\r\n  },\r\n])\r\n/** 表格配置(赠送) */\r\nconst tableColumns1 = reactive<Table.Column<orderDetail.tableData>[]>([\r\n  {\r\n    prop: 'priceDate',\r\n    width: 150,\r\n    label: t('date'),\r\n  },\r\n  {\r\n    prop: 'roomBkNum',\r\n    width: 150,\r\n    label: t('roomsWith'),\r\n  },\r\n  {\r\n    prop: 'bkNum',\r\n    label: t('complimentary'),\r\n    render: ({ row }) =>\r\n      h(ElInputNumber, {\r\n        modelValue: row.bkNum,\r\n        style: 'width:120px;',\r\n        min: 0,\r\n        'onUpdate:modelValue': (newValue) => {\r\n          row.bkNum = newValue\r\n        },\r\n      }),\r\n  },\r\n])\r\n/** 表格配置(购买) */\r\nconst tableColumns2 = reactive<Table.Column<orderDetail.tableData>[]>([\r\n  {\r\n    prop: 'priceDate',\r\n    width: 150,\r\n    label: t('date'),\r\n  },\r\n  {\r\n    prop: 'roomBkNum',\r\n    width: 150,\r\n    label: t('roomsWith'),\r\n  },\r\n  {\r\n    prop: 'buyedBkNum',\r\n    label: t('purchase'),\r\n    render: ({ row }) =>\r\n      h('div', [\r\n        h('div', { style: 'text-align:left;' }, `${t('purchasedCopies')} ${row.buyedBkNum}`),\r\n        h('div', { style: 'text-align:left;' }, [\r\n          h('span', { class: 'mr-[10px]' }, t('addPurchasedCopies')),\r\n          h(ElInputNumber, {\r\n            modelValue: row.buyBkNum,\r\n            style: 'width:140px',\r\n            min: 0,\r\n            'onUpdate:modelValue': (newValue) => {\r\n              row.buyBkNum = newValue\r\n            },\r\n          }),\r\n        ]),\r\n        h('div', { style: 'text-align:left;' }, `${t('unitPrice')} ${row.bkPrice}`),\r\n      ]),\r\n  },\r\n])\r\n/** 统一购买 */\r\nfunction addActiveName() {\r\n  if (pleaseInput.value) {\r\n    tableData1.value.map((item) => {\r\n      item.bkNum = Number(pleaseInput.value)\r\n      return item\r\n    })\r\n  }\r\n}\r\n/** 弹窗新增早餐tabs切换 */\r\nfunction handleClick(tab: TabsPaneContext) {\r\n  activeName.value = tab.paneName\r\n  tableData1.value = deepCopy(tableData.value)\r\n  tableData2.value = deepCopy(tableData.value)\r\n  tableData2.value.map((item) => {\r\n    item.buyBkNum = 0\r\n    return item\r\n  })\r\n}\r\n/** 新增早餐 */\r\nfunction buyBkNumClick() {\r\n  easyDialogRef.value.show()\r\n  pleaseInput.value = ''\r\n  activeName.value = 1\r\n  tableData1.value = deepCopy(tableData.value)\r\n  tableData2.value = deepCopy(tableData.value)\r\n  tableData2.value.map((item) => {\r\n    item.buyBkNum = 0\r\n    return item\r\n  })\r\n}\r\n/**\r\n * 添加早餐确认\r\n * @ 1 赠送早餐  2 购买早餐\r\n */\r\nasync function formSubmit() {\r\n  const orderNo = props.orderNo[0]\r\n  easyDialogRef.value.loading = true\r\n  // 1 赠送早餐\r\n  if (activeName.value === 1) {\r\n    const dayBks = tableData1.value.map((item) => {\r\n      return {\r\n        priceDate: item.priceDate, // 购买日期\r\n        bkNum: item.bkNum, // 每日购买数量\r\n      }\r\n    })\r\n    await orderApi.putOrderGiveBk({\r\n      orderNo,\r\n      dayBks,\r\n    })\r\n    formCancel()\r\n  }\r\n  // 2 购买早餐\r\n  if (activeName.value === 2) {\r\n    await orderApi.putOrderBuyBk({\r\n      orderNo,\r\n      buyBkNum: tableData2.value[0].buyBkNum,\r\n      togetherCode: props.togetherCode,\r\n      price: tableData2.value[0].bkPrice,\r\n    })\r\n    formCancel()\r\n  }\r\n}\r\n/** 添加早餐取消 */\r\nfunction formCancel() {\r\n  easyDialogRef.value.visible = false\r\n  easyDialogRef.value.loading = false\r\n  getOrderDetail()\r\n}\r\nconst spanArr = ref<number[]>([])\r\n/**\r\n * 表格单元格合并\r\n * @description 实现合并行或列\r\n * @param row:Object 需要合并的列name 如:'name' 'id'\r\n * @param column:Object 当前行的行数，由合并函数传入\r\n * @param rowIndex:Number 当前列的数据，由合并函数传入\r\n * @param columnIndex:Number 当前列的数据，由合并函数传入\r\n * @return 函数可以返回一个包含两个元素的数组，第一个元素代表rowspan，\r\n * 第二个元素代表colspan。 也可以返回一个键名为rowspan和colspan的对象\r\n */\r\nfunction colSpanMethod({ rowIndex, columnIndex }: orderDetail.SpanMethodProps) {\r\n  // 最后一列合并(没打开弹窗，添加早餐)\r\n  if (columnIndex === 3 && !easyDialogRef.value.visible) {\r\n    const _row = spanArr.value[rowIndex]\r\n    const _col = _row > 0 ? 1 : 0\r\n    return [_row, _col]\r\n  }\r\n  // 最后一列合并(打开弹窗，添加早餐，购买早餐)\r\n  if (columnIndex === 2 && easyDialogRef.value.visible && activeName.value === '2') {\r\n    const _row = spanArr.value[rowIndex]\r\n    const _col = _row > 0 ? 1 : 0\r\n    return [_row, _col]\r\n  }\r\n}\r\n/** 获得增早/售早数据(增早/售早界面需要的数据) */\r\nfunction getOrderGetBk() {\r\n  orderApi\r\n    .getOrderGetBk({\r\n      ...queryParams,\r\n      orderNo: props.orderNo[0],\r\n    })\r\n    .then((res) => {\r\n      tableData.value = res.data.dayBks || []\r\n      let pos = 0 // 默认0\r\n      // 解析需要合并的单元格\r\n      for (let i = 0; i < tableData.value.length; i++) {\r\n        if (i === 0) {\r\n          spanArr.value.push(1) // 第一行均有数据，记录当前合并行数为1.\r\n          pos = 0 // spanArr的当前索引是0，需要存进一条数据\r\n        } else {\r\n          // 判断当前元素与上一个元素是否相同（需要合并的名称）\r\n          if (tableData.value[i].buyedBkNum === tableData.value[i - 1].buyedBkNum) {\r\n            spanArr.value[pos] += 1\r\n            spanArr.value.push(0)\r\n          }\r\n        }\r\n      }\r\n    })\r\n}\r\n\r\nonMounted(async () => {\r\n  await Promise.all([initConstants(), getOrderDetail(), getServerTime()])\r\n  // getHotelDoorConfig()\r\n  // getFrontConfig()\r\n  // 租借信息\r\n  rentList()\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n})\r\n\r\nonBeforeUnmount(() => {\r\n  if (typeof (window as any).CallBridge !== 'undefined' || typeof (window as any).__RUNNING_IN_PMS_AGENT__ !== 'undefined') {\r\n    CardReader.closeSocket()\r\n  }\r\n})\r\n// 租借物品数量\r\nconst rentGoodsCount = ref(0)\r\nfunction rentList() {\r\n  rentApi\r\n    .getRentGoodsList({\r\n      ...queryParams,\r\n      orderNo: props.orderNo[0],\r\n      state: '0',\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0 && res.data) {\r\n        rentGoodsCount.value = res.data.total\r\n      }\r\n    })\r\n}\r\n\r\n/** 入离时间处理 */\r\nfunction checkInOutTime() {\r\n  // 当订单状态为check_in时，使用实际入住时间；否则使用计划入住时间\r\n  const checkinTime =\r\n    dt.value.orderTogether.state === OrderState.CHECK_IN || dt.value.orderTogether.state === OrderState.CHECK_OUT || dt.value.orderTogether.state === OrderState.CREDIT ? dt.value.orderTogether.checkinTime : dt.value.order.planCheckinTime\r\n  const checkoutTime = dt.value.orderTogether.state === OrderState.CHECK_OUT || dt.value.orderTogether.state === OrderState.CREDIT ? dt.value.orderTogether.checkoutTime : dt.value.order.planCheckoutTime\r\n  return dayjs(checkinTime).format('MM/DD HH:mm').concat(' - ').concat(dayjs(checkoutTime).format('MM/DD HH:mm'))\r\n}\r\n\r\n// 入住天数计算\r\nfunction checkDays() {\r\n  if (dt.value.order.checkinType == CheckinType.HOUR_ROOM) {\r\n    return 0\r\n  }\r\n  const checkinTime =\r\n    dt.value.orderTogether.state === OrderState.CHECK_IN || dt.value.orderTogether.state === OrderState.CHECK_OUT || dt.value.orderTogether.state === OrderState.CREDIT ? dt.value.orderTogether.checkinTime : dt.value.order.planCheckinTime\r\n  const checkoutTime = dt.value.orderTogether.state === OrderState.CHECK_OUT || dt.value.orderTogether.state === OrderState.CREDIT ? dt.value.orderTogether.checkoutTime : dt.value.order.planCheckoutTime\r\n  // 使用日期差计算，而不是时间差\r\n  const checkinDate = dayjs(checkinTime).startOf('day')\r\n  const checkoutDate = dayjs(checkoutTime).startOf('day')\r\n  const daysDiff = checkoutDate.diff(checkinDate, 'day')\r\n\r\n  // 当入住日期等于退房日期时，返回1\r\n  return daysDiff === 0 ? 1 : daysDiff\r\n}\r\n\r\n/**\r\n * 获取酒店的门锁配置\r\n */\r\nfunction getHotelDoorConfig(lockVersion?: string): Promise<void> {\r\n  const params = {\r\n    ...queryParams,\r\n    ...(lockVersion && { lockVersion }),\r\n  }\r\n  return deviceApi.getHotelDoor(params).then((res: any) => {\r\n    if (res.code === 0 && res.data) {\r\n      hotelDoorConfig.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/**\r\n * 将常量初始化\r\n */\r\nasync function initConstants() {\r\n  dictDataApi.getDictDataBatch([DICT_TYPE_ID_TYPE, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE]).then((res: any) => {\r\n    idTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_ID_TYPE)\r\n    guestSrcTypes.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n  })\r\n}\r\n/** 获取服务器时间 */\r\nasync function getServerTime() {\r\n  const res = await serverTimeApi.serverTime(userStore.gcode, '0')\r\n  serverTime.value = res.data\r\n}\r\n/** 是否开启了主单入账 */\r\n// const isMainOrderBilling = ref(false)\r\n// async function getFrontConfig() {\r\n//   const params = {\r\n//     gcode: userStore.gcode,\r\n//     hcode: userStore.hcode,\r\n//   }\r\n//   const res = await hotelParamConfigApi.getHotelParamConfigFront(params)\r\n//   if (res.code === 0) {\r\n//     isMainOrderBilling.value = res.data.value.masterOnlyBilling === '1'\r\n//   }\r\n// }\r\n\r\nwatch(\r\n  () => ({ _orderNo: props.orderNo }),\r\n  ({ _orderNo }) => {\r\n    getOrderDetail()\r\n  }\r\n)\r\n/** 获取在住订单详情 */\r\nasync function getOrderDetail() {\r\n  const res = await orderApi.getOrderDtail({\r\n    ...queryParams,\r\n    orderNo: props.orderNo[0],\r\n    togetherCode: props.togetherCode,\r\n  })\r\n  dt.value = res.data\r\n  dt.value.order.hourCode = res.data.order.hourCode === 0 ? '' : res.data.order.hourCode\r\n  memberForm.value.mcode = dt.value.order.mcode\r\n  memberForm.value.guestSrcType = dt.value.order.guestSrcType\r\n  memberForm.value.agentCode = dt.value.order.agentCode\r\n  memberForm.value.protocolCode = dt.value.order.protocolCode\r\n  remark.value = res.data.order.remark\r\n\r\n  // 初始化脱敏数据\r\n  if (dt.value.orderTogether?.phone) {\r\n    originalValues.value.phone = dt.value.orderTogether.phone\r\n    dt.value.orderTogether.phone = saveAndMask(dt.value.orderTogether.phone, 'phone')\r\n    sensitiveStatus.value.isShowFullPhone = false\r\n  }\r\n\r\n  if (dt.value.orderTogether?.address) {\r\n    originalValues.value.address = dt.value.orderTogether.address\r\n    dt.value.orderTogether.address = saveAndMask(dt.value.orderTogether.address, 'address')\r\n    sensitiveStatus.value.isShowFullAddress = false\r\n  }\r\n\r\n  if (dt.value.orderTogether?.idNo) {\r\n    originalValues.value.idNo = dt.value.orderTogether.idNo\r\n    dt.value.orderTogether.idNo = saveAndMask(dt.value.orderTogether.idNo, 'idNo')\r\n    sensitiveStatus.value.isShowFullIdNo = false\r\n  }\r\n\r\n  if (dt.value.order.mcode) {\r\n    changMember(dt.value.order.mcode)\r\n  }\r\n  getOrderGetBk()\r\n  getSummary()\r\n}\r\n/** 修改销售员 */\r\nfunction isSellerClick() {\r\n  isUpdateSeller.value = false\r\n  getSellers()\r\n}\r\n/** 销售员保存 */\r\nfunction sellersSubmit() {\r\n  const params = {\r\n    ...queryParams,\r\n    orderNo: dt.value.order.orderNo,\r\n    seller: dt.value.order.seller,\r\n  }\r\n  memberApi.updateSeller(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      // 将选中的销售员姓名赋值给当前订单sellerName\r\n      const selectedSeller = sellers.value.find((item) => item.username === dt.value.order.seller)\r\n      if (selectedSeller) {\r\n        dt.value.order.sellerName = selectedSeller.nickname\r\n      }\r\n      isUpdateSeller.value = true\r\n      ElMessage.success(t('updateSuccess'))\r\n    }\r\n  })\r\n}\r\n/** 获取销售员列表 */\r\nasync function getSellers() {\r\n  const { data } = await userApi.listSeller(queryParams)\r\n  sellers.value = data\r\n}\r\n/** 获取统计在住宾客的账务 */\r\nfunction getSummary() {\r\n  accountApi.statAccountByTogetherCode({ no: props.togetherCode }).then((res: any) => {\r\n    if (res.code === 0) {\r\n      dt.value.summary = res.data\r\n    }\r\n  })\r\n}\r\n\r\n/** 展示入账页面 */\r\nfunction entryAccount(val: any) {\r\n  if (dt.value.order.orderNo) {\r\n    orderVisible.value.accountOrderNo = dt.value.order.orderNo\r\n    orderVisible.value.togetherCode = dt.value.orderTogether.togetherCode\r\n  }\r\n  tabName.value = val\r\n  entryAccountVisible.value = true\r\n}\r\n\r\nfunction entryRoomContinue() {\r\n  if (dt.value.order.orderNo) {\r\n    orderVisible.value.accountOrderNo = dt.value.order.orderNo\r\n    orderVisible.value.togetherCode = dt.value.orderTogether.togetherCode\r\n  }\r\n  entryRoomContinueVisible.value = true\r\n}\r\n/** 展示同住弹窗 */\r\nfunction entryCohabitAtion() {\r\n  if (dt.value.order.orderNo) {\r\n    orderVisible.value.accountOrderNo = dt.value.order.orderNo\r\n    orderVisible.value.togetherCode = dt.value.orderTogether.togetherCode\r\n  }\r\n  cohabitAtionTitle.value = 'addGuest'\r\n  entrycohabitAtionVisible.value = true\r\n}\r\n// 添加租借弹窗控制变量\r\nconst rentListVisible = ref(false)\r\n\r\n/** 展示转入团队弹窗 */\r\nconst entryTransferTeamVisible = ref(false)\r\n\r\n/** 弹出租借窗口 */\r\nfunction rent() {\r\n  rentListVisible.value = true\r\n}\r\n\r\n/** 租借成功后的处理 */\r\nfunction handleRentSuccess() {\r\n  // 重新获取租借物品数量\r\n  rentList()\r\n}\r\n\r\nfunction entryLeavePrice() {\r\n  // 检查是否有未归还的租借物品\r\n  if (rentGoodsCount.value > 0) {\r\n    ElMessageBox.confirm(`当前订单还有 ${rentGoodsCount.value} 件租借物品未归还，确定要继续结账退房吗？`, '租借物品提醒', {\r\n      confirmButtonText: '继续结账',\r\n      cancelButtonText: '取消',\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n      message: `<div style=\"color: #E6A23C;\"><i class=\"el-icon-warning\"></i> 当前订单还有 <strong>${rentGoodsCount.value}</strong> 件租借物品未归还</div><div style=\"margin-top: 10px; color: #606266;\">建议先处理租借物品归还后再进行结账退房操作</div>`,\r\n    })\r\n      .then(() => {\r\n        // 用户确认继续结账\r\n        entryLeavePriceVisible.value = true\r\n      })\r\n      .catch(() => {\r\n        // 用户取消操作\r\n        ElMessage.info('已取消结账退房操作')\r\n      })\r\n  } else {\r\n    // 没有未归还的租借物品，直接打开结账弹窗\r\n    entryLeavePriceVisible.value = true\r\n  }\r\n}\r\n\r\nfunction entryTransferTeam() {\r\n  entryTransferTeamVisible.value = true\r\n}\r\n\r\n/** 展示加入联房弹窗 */\r\nconst entryJoinRoomVisible = ref(false)\r\n\r\nfunction entryJoinRoom() {\r\n  if (dt.value.order.orderNo) {\r\n    orderVisible.value.accountOrderNo = dt.value.order.orderNo\r\n  }\r\n  entryJoinRoomVisible.value = true\r\n}\r\n\r\n/** 展示退出联房弹窗 */\r\nconst outJoinRoomVisible = ref(false)\r\n/** 提交退出联房 */\r\nfunction outSubmit() {\r\n  const params = {\r\n    ...queryParams,\r\n    orderNo: dt.value.order.orderNo,\r\n  }\r\n  orderApi.quitMergeRoom(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      ElMessage.success(t('unlinkRoomSuccess'))\r\n      orderRefresh()\r\n      outJoinRoomVisible.value = false\r\n    } else {\r\n      ElMessage.error(res.msg)\r\n    }\r\n  })\r\n}\r\n\r\nconst roomPriceProps = ref({\r\n  visible: false,\r\n  rtCode: '',\r\n  orderNo: '',\r\n  orderType: 'order',\r\n  initialPriceList: [\r\n    {\r\n      bkNum: 0,\r\n      price: 0,\r\n      priceDate: '',\r\n      priceStrategyCode: '',\r\n      roomBkNum: 0,\r\n      vipPrice: 0,\r\n      week: 0,\r\n    },\r\n  ] as orderDetail.ParameterMaps[],\r\n})\r\n\r\nconst checkVisible = ref({\r\n  orderNo: '',\r\n  rNo: '',\r\n  rCode: '',\r\n  rtName: '',\r\n  vipPrice: 0,\r\n  planCheckinTime: '',\r\n  planCheckoutTime: '',\r\n  checkinType: '',\r\n  hourCode: '',\r\n  guestSrcTypeName: '',\r\n  guestSrcType: '',\r\n  channelCode: '',\r\n  orderSource: '',\r\n  guestCode: '',\r\n})\r\n\r\nfunction checkType(value: string) {\r\n  if (value === 'changeHouses') {\r\n    if (dt.value.order.canChangePrice === '1') {\r\n      // 可以直接换房\r\n      openChangeRoomDialog()\r\n    } else {\r\n      // 显示顶部通知，带有可点击的\"续住\"和\"强制换房\"链接\r\n      ElMessage({\r\n        message: h('div', { style: 'display: flex; align-items: center; font-size: 14px;' }, [\r\n          h('span', null, `${t('renewStayBeforeSwitch')}，去`),\r\n          h(\r\n            'span',\r\n            {\r\n              style: 'cursor: pointer; font-size: 14px; color: #6366f1;',\r\n              onClick: () => entryRoomContinue(),\r\n            },\r\n            '续住'\r\n          ),\r\n          h('span', null, '或'),\r\n          h(\r\n            'span',\r\n            {\r\n              style: 'margin-left: 5px; cursor: pointer; font-size: 14px; color: #6366f1;',\r\n              onClick: () => openChangeRoomDialog(),\r\n            },\r\n            '继续换房'\r\n          ),\r\n        ]),\r\n        type: 'warning',\r\n        showClose: true,\r\n        duration: 5000,\r\n      })\r\n    }\r\n  } else if (value === 'changePrice') {\r\n    if (dt.value.order.canChangePrice === '1') {\r\n      const params = {\r\n        ...queryParams,\r\n        orderNo: dt.value.order.orderNo,\r\n      }\r\n      orderApi.getChangePrice(params).then((res: any) => {\r\n        if (res.code === 0) {\r\n          roomPriceProps.value.rtCode = dt.value.order.rtCode\r\n          roomPriceProps.value.orderNo = res.data.orderNo\r\n          roomPriceProps.value.initialPriceList = res.data.dayPrices\r\n          roomPriceProps.value.visible = true\r\n        } else {\r\n          ElMessage.error(res.msg)\r\n        }\r\n      })\r\n    } else {\r\n      ElMessage.error(t('renewStayBeforeChangePrice'))\r\n    }\r\n  }\r\n}\r\n\r\n// 打开换房对话框的函数\r\nfunction openChangeRoomDialog() {\r\n  entryexchangeVisible.value = true\r\n  checkVisible.value.orderNo = dt.value.order.orderNo\r\n  checkVisible.value.rNo = dt.value.order.rNo\r\n  checkVisible.value.rCode = dt.value.order.rCode\r\n  checkVisible.value.rtName = dt.value.order.rtName\r\n  checkVisible.value.vipPrice = dt.value.orderPrice.vipPrice\r\n  checkVisible.value.guestSrcTypeName = dt.value.order.guestSrcTypeName\r\n  checkVisible.value.guestSrcType = dt.value.order.guestSrcType\r\n  checkVisible.value.guestCode = dt.value.order.guestCode\r\n  checkVisible.value.channelCode = dt.value.order.channelCode\r\n  checkVisible.value.orderSource = dt.value.order.orderSource\r\n  checkVisible.value.checkinType = dt.value.order.checkinType\r\n  checkVisible.value.hourCode = dt.value.order.checkinType === CheckinType.HOUR_ROOM ? dt.value.order.hourCode : ''\r\n  checkVisible.value.planCheckinTime = dayjs(dt.value.order.planCheckinTime).format('YYYY-MM-DD HH:mm')\r\n  checkVisible.value.planCheckoutTime = dayjs(dt.value.order.planCheckoutTime).format('YYYY-MM-DD HH:mm')\r\n}\r\n\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    checkinTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n    checkinTypeList.value = checkinTypeList.value.filter((item: any) => item.code !== CheckinType.TRAVEL_GROUP && item.code !== CheckinType.MEETING_GROUP)\r\n    srcTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n    srcTypeList.value = srcTypeList.value.filter((item: any) => item.code !== '0' && item.code !== '0')\r\n    orderSources.value = res.data.filter((item: any) => item.dictType === ORDER_SOURCE)\r\n    hourRts.value = res.data.filter((item: any) => item.dictType === CONSTANT_TYPE_CODE_SZ)\r\n    dbs.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUARANTEE_TYPE)\r\n  })\r\n}\r\n/** 获取中介列表 */\r\nfunction getAgents() {\r\n  customerApi\r\n    .simpleList({\r\n      gcode: userStore.gcode,\r\n      belongHcode: userStore.hcode,\r\n      paType: '1',\r\n      isEnable: BooleanEnum.YES,\r\n    })\r\n    .then((res: any) => {\r\n      agents.value = res.data\r\n    })\r\n}\r\n/** 获得协议单位 */\r\nfunction getProtocols() {\r\n  customerApi\r\n    .simpleList({\r\n      gcode: userStore.gcode,\r\n      belongHcode: userStore.hcode,\r\n      paType: '0',\r\n      isEnable: BooleanEnum.YES,\r\n    })\r\n    .then((res: any) => {\r\n      protocols.value = res.data\r\n    })\r\n}\r\n/** 客源类型 */\r\nfunction sourceEdit() {\r\n  updateGuestSrcType.value = false\r\n  getConstants()\r\n  getAgents()\r\n  getProtocols()\r\n}\r\n\r\n/** 备注修改 */\r\nfunction remarkSubmit() {\r\n  const params = {\r\n    ...queryParams,\r\n    orderNo: dt.value.order.orderNo,\r\n    remark: dt.value.order.remark,\r\n  }\r\n  memberApi.updateRemark(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      updateRemark.value = true\r\n      ElMessage.success(t('updateSuccess'))\r\n    }\r\n  })\r\n}\r\n/** 外部订单号修改 */\r\nfunction outOrderNoSubmit() {\r\n  const params = {\r\n    ...queryParams,\r\n    orderNo: dt.value.order.orderNo,\r\n    outOrderNo: dt.value.order.outOrderNo,\r\n  }\r\n  memberApi.updateOutOrderNo(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      updateOutOrderNo.value = true\r\n      ElMessage.success(t('updateSuccess'))\r\n    }\r\n  })\r\n}\r\n/** 修改客人信息 */\r\nfunction onGuestEdit() {\r\n  cohabitAtionTitle.value = 'guestInfo'\r\n  entrycohabitAtionVisible.value = true\r\n}\r\n\r\n/** 远程查询会员信息 */\r\nfunction remoteQueryMember(query: string) {\r\n  if (query) {\r\n    memberForm.value.loading = true\r\n    setTimeout(() => {\r\n      memberForm.value.loading = false\r\n      const params = {\r\n        ...queryParams,\r\n        state: BooleanEnum.YES,\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        phone: query,\r\n      }\r\n      memberApi.pagelist(params).then((res: any) => {\r\n        members.value = res.data.list\r\n      })\r\n    }, 200)\r\n  } else {\r\n    members.value = []\r\n  }\r\n}\r\n\r\nfunction changMember(value: string) {\r\n  dt.value.order.mcode = value\r\n  const params = {\r\n    ...queryParams,\r\n    mcode: dt.value.order.mcode,\r\n  }\r\n  memberApi.detail(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      memberCardDetail.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\n// 切换手机号脱敏显示\r\nfunction togglePhoneSensitive() {\r\n  if (sensitiveStatus.value.isShowFullPhone) {\r\n    // 当前显示完整手机号，切换为脱敏\r\n    dt.value.orderTogether.phone = saveAndMask(originalValues.value.phone, 'phone')\r\n    sensitiveStatus.value.isShowFullPhone = false\r\n  } else {\r\n    // 当前显示脱敏，切换为完整\r\n    dt.value.orderTogether.phone = originalValues.value.phone\r\n    sensitiveStatus.value.isShowFullPhone = true\r\n  }\r\n}\r\n\r\n// 切换身份证号脱敏显示\r\nfunction toggleIdNoSensitive() {\r\n  if (sensitiveStatus.value.isShowFullIdNo) {\r\n    // 当前显示完整身份证号，切换为脱敏\r\n    dt.value.orderTogether.idNo = saveAndMask(originalValues.value.idNo, 'idNo')\r\n    sensitiveStatus.value.isShowFullIdNo = false\r\n  } else {\r\n    // 当前显示脱敏，切换为完整\r\n    dt.value.orderTogether.idNo = originalValues.value.idNo\r\n    sensitiveStatus.value.isShowFullIdNo = true\r\n  }\r\n}\r\n\r\n// 切换地址脱敏显示\r\nfunction toggleAddressSensitive() {\r\n  if (sensitiveStatus.value.isShowFullAddress) {\r\n    // 当前显示完整地址，切换为脱敏\r\n    dt.value.orderTogether.address = saveAndMask(originalValues.value.address, 'address')\r\n    sensitiveStatus.value.isShowFullAddress = false\r\n  } else {\r\n    // 当前显示脱敏，切换为完整\r\n    dt.value.orderTogether.address = originalValues.value.address\r\n    sensitiveStatus.value.isShowFullAddress = true\r\n  }\r\n}\r\n\r\nfunction submitMember() {\r\n  const order = dt.value.order\r\n  const params: any = {\r\n    ...queryParams,\r\n    guestSrcType: order.guestSrcType,\r\n    orderNo: order.orderNo,\r\n  }\r\n  const guestCodeKey = {\r\n    member: 'mcode',\r\n    agent: 'agentCode',\r\n    protocol: 'protocolCode',\r\n  }\r\n  if (order.guestSrcType === GuestSrcType.WALK_IN) {\r\n    params.guestCode = ''\r\n  } else {\r\n    const guestCode = order[guestCodeKey[order.guestSrcType]]\r\n    if (!guestCode) {\r\n      const errorMessage = {\r\n        member: t('enterPhoneToGetMemberInfo'),\r\n        agent: t('selectAgent'),\r\n        protocol: t('selectCompany'),\r\n      }\r\n      ElMessage.error(errorMessage[order.guestSrcType])\r\n      return\r\n    }\r\n\r\n    params.guestCode = guestCode\r\n  }\r\n\r\n  orderApi\r\n    .guestSrcType(params)\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('sourceTypeUpdateSuccess'))\r\n        updateGuestSrcType.value = true\r\n        orderRefresh()\r\n      } else {\r\n        ElMessage.error(res.msg)\r\n      }\r\n    })\r\n    .catch(() => {\r\n      ElMessage.error(t('submitError'))\r\n    })\r\n}\r\n\r\n/** 初始化 */\r\nfunction onMemberCancel() {\r\n  memberForm.value.phone = ''\r\n  dt.value.order.mcode = memberForm.value.mcode\r\n  dt.value.order.guestSrcType = memberForm.value.guestSrcType\r\n  dt.value.order.agentCode = memberForm.value.agentCode\r\n  dt.value.order.protocolCode = memberForm.value.protocolCode\r\n  updateGuestSrcType.value = true\r\n  updateRemark.value = true\r\n}\r\n\r\n/** 重新入住 */\r\nfunction onRecheckIn() {\r\n  ElMessageBox.confirm('确定重新入住吗?', {\r\n    confirmButtonText: t('confirm'),\r\n    cancelButtonText: t('cancel'),\r\n    type: 'warning',\r\n  })\r\n    .then(() => {\r\n      orderApi\r\n        .recheckIn({\r\n          ...queryParams,\r\n          togetherCode: dt.value.orderTogether.togetherCode,\r\n        })\r\n        .then((res: any) => {\r\n          if (res.code === 0) {\r\n            ElMessage.success(t('checkInSuccess'))\r\n            orderRefresh()\r\n          }\r\n        })\r\n    })\r\n    .catch(() => {})\r\n}\r\n\r\n/** 客户端判断 */\r\nfunction isInClient(): boolean {\r\n  if (typeof (window as any).CallBridge === 'undefined' && typeof (window as any).__RUNNING_IN_PMS_AGENT__ === 'undefined') {\r\n    // 提示没有下载客户端不能读取房卡，并提示下载客户端的路径\r\n    const downloadLink = `<a href=\"${import.meta.env.VITE_APP_CLIENT_DOWNLOAD_URL}\" target=\"_blank\">${t('download_client')}</a>`\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('read_card_in_client', { downloadLink }),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/** 获取服务器的系统时间戳 */\r\nfunction getSysTime(): Promise<string> {\r\n  return serverTimeApi.serverTime(userStore.gcode, '3').then((res: any) => {\r\n    if (res.code === 0) {\r\n      return res.data // 返回字符串类型的时间戳\r\n    }\r\n    return ''\r\n  })\r\n}\r\n\r\n/** 制房卡 */\r\nasync function writeLockCard(newCard: boolean) {\r\n  if (!isInClient()) {\r\n    return\r\n  }\r\n\r\n  if (!dt.value.order.lockVersion) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('hotel_no_lock_config'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n\r\n  await getHotelDoorConfig(dt.value.order.lockVersion)\r\n\r\n  // 如果没有门锁的配置，则提示\r\n  if (!hotelDoorConfig.value) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('hotel_no_lock_config'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n  // 每次点击都要先关闭websocket\r\n  if (CardReader.isConnected) {\r\n    CardReader.closeSocket()\r\n    // 等待WebSocket完全关闭\r\n    await new Promise((resolve) => setTimeout(resolve, 300))\r\n  }\r\n  CardReader.initCardReader((message: string) => {\r\n    const data = JSON.parse(message)\r\n    if (data && data.succeed === true && data.method === ClientMethodEnum.WRITELOCKCARD) {\r\n      if (data.cardInfo) {\r\n        ElNotification({\r\n          title: '房卡信息',\r\n          dangerouslyUseHTMLString: true,\r\n          message: generateCardInfoHtml(data),\r\n          position: 'bottom-left',\r\n          type: 'success',\r\n        })\r\n      }\r\n      ElMessage({\r\n        message: t('lock_card_success', { roomNo: dt.value.order.rNo }),\r\n        type: 'success',\r\n      })\r\n      // 异步记录制卡日志，不影响主业务\r\n      roomCardLogApi\r\n        .createRoomCardLog({\r\n          gcode: userStore.gcode,\r\n          hcode: userStore.hcode,\r\n          orderNo: dt.value.order.orderNo,\r\n          name: dt.value.orderTogether?.name,\r\n          rNo: dt.value.order.rNo,\r\n          type: newCard ? '0' : '1',\r\n          periodTime: dayjs(Number(data.cardInfo.expire) * 1000).format('YYYY-MM-DD HH:mm:ss'),\r\n        })\r\n        .then((res: any) => {\r\n          if (res.code === 0) {\r\n            console.log('制卡日志记录成功')\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          // 日志记录失败不影响主业务，只记录错误\r\n          console.error('制卡日志记录失败:', error)\r\n        })\r\n    }\r\n    if (data && data.succeed === false && data.method === ClientMethodEnum.WRITELOCKCARD) {\r\n      if (data.msg) {\r\n        ElMessage({\r\n          message: data.msg,\r\n          type: 'error',\r\n        })\r\n      }\r\n    }\r\n  })\r\n  // 获取系统时间戳\r\n  const sysTimeString = await getSysTime() // 调用异步方法\r\n  if (!sysTimeString) {\r\n    console.error('Failed to get system time')\r\n    return\r\n  }\r\n  // 单位秒\r\n  const timeStampInSeconds = Math.floor(Number.parseInt(sysTimeString, 10) / 1000)\r\n  // 构建 json 对象\r\n  const json = {\r\n    method: ClientMethodEnum.WRITELOCKCARD, // 写门锁卡\r\n    lockVer: hotelDoorConfig.value.version, // 门锁类型\r\n    cardInfo: {\r\n      roomNo: dt.value.order.rNo, // 房间号，字符串\r\n      checkin: timeStampInSeconds, // 入住时间戳，单位秒，整型\r\n      expire: Math.floor(dayjs(dt.value.order.planCheckoutTime).valueOf() / 1000), // 到期时间戳，单位秒，整型\r\n      allowLockOut: hotelDoorConfig.value?.allowLockOut === BooleanEnum.YES, // 是否允许开反锁，布尔值\r\n      replaceCard: newCard, // 新卡替换旧卡，布尔值\r\n      checkTime: hotelDoorConfig.value?.checkTime === BooleanEnum.YES, // 检查入住时间，布尔值\r\n      lockNo: dt.value.order.lockNo, // 门锁号，字符串\r\n      mac: dt.value.order.mac, // 门锁mac地址\r\n      buildNo: dt.value.order.buildNo,\r\n      floorNo: dt.value.order.floorNo,\r\n    },\r\n  }\r\n\r\n  // 检查 confParameter 是否有值\r\n  if (hotelDoorConfig.value.conf && hotelDoorConfig.value.conf.length > 0) {\r\n    // 将 confParameter 转换为 cardInfo 对象\r\n    hotelDoorConfig.value.conf.forEach((param) => {\r\n      json.cardInfo[param.parameterCode] = param.parameterContent\r\n    })\r\n  }\r\n\r\n  // 将 json 对象转换为字符串\r\n  const jsonString = JSON.stringify(json)\r\n  console.log('jsonString', jsonString)\r\n  const timer = setInterval(() => {\r\n    if (CardReader.isConnected) {\r\n      CardReader.handleLockCard(jsonString)\r\n      clearInterval(timer)\r\n    }\r\n  }, 200)\r\n}\r\n\r\n/** 注销卡 */\r\nasync function cancelCard() {\r\n  if (!isInClient()) {\r\n    return\r\n  }\r\n\r\n  if (!dt.value.order.lockVersion) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('hotel_no_lock_config'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n\r\n  await getHotelDoorConfig(dt.value.order.lockVersion)\r\n\r\n  // 如果没有门锁的配置，则提示\r\n  if (!hotelDoorConfig.value) {\r\n    ElMessage({\r\n      showClose: true,\r\n      message: t('hotel_no_lock_config'),\r\n      type: 'warning',\r\n      dangerouslyUseHTMLString: true,\r\n    })\r\n    return\r\n  }\r\n  // 每次点击都要先关闭websocket\r\n  if (CardReader.isConnected) {\r\n    CardReader.closeSocket()\r\n    // 等待WebSocket完全关闭\r\n    await new Promise((resolve) => setTimeout(resolve, 300))\r\n  }\r\n  CardReader.initCardReader((message: string) => {\r\n    const data = JSON.parse(message)\r\n    if (data && data.succeed === true && data.method === ClientMethodEnum.CANCELLOCKCARD) {\r\n      ElMessage({\r\n        message: t('cancel_card_success'),\r\n        type: 'success',\r\n      })\r\n      // 异步记录制卡日志，不影响主业务\r\n      roomCardLogApi\r\n        .createRoomCardLog({\r\n          gcode: userStore.gcode,\r\n          hcode: userStore.hcode,\r\n          orderNo: dt.value.order.orderNo,\r\n          name: dt.value.orderTogether?.name,\r\n          rNo: dt.value.order.rNo,\r\n          type: '2',\r\n        })\r\n        .then((res: any) => {\r\n          if (res.code === 0) {\r\n            console.log('清卡日志记录成功')\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          // 日志记录失败不影响主业务，只记录错误\r\n          console.error('清卡日志记录失败:', error)\r\n        })\r\n    }\r\n  })\r\n  const json = {\r\n    method: ClientMethodEnum.CANCELLOCKCARD, // 清卡\r\n    lockVer: hotelDoorConfig.value.version, // 门锁类型\r\n  } as { method: string; lockVer: string; cardInfo?: { [key: string]: string } }\r\n  // 检查 confParameter 是否有值\r\n  if (hotelDoorConfig.value.conf && hotelDoorConfig.value.conf.length > 0) {\r\n    // 将 confParameter 转换为 cardInfo 对象\r\n    const cardInfo = {\r\n      lockNo: dt.value.order.lockNo, // 门锁号，字符串\r\n      mac: dt.value.order.mac, // 门锁mac地址\r\n    }\r\n    hotelDoorConfig.value.conf.forEach((param) => {\r\n      cardInfo[param.parameterCode] = param.parameterContent\r\n    })\r\n\r\n    // 将 cardInfo 添加到 json 中\r\n    json.cardInfo = cardInfo\r\n  }\r\n  // 将 json 对象转换为字符串\r\n  const jsonString = JSON.stringify(json)\r\n  console.log('jsonString', jsonString)\r\n  const timer = setInterval(() => {\r\n    if (CardReader.isConnected) {\r\n      CardReader.handleLockCard(jsonString)\r\n      clearInterval(timer)\r\n    }\r\n  }, 200)\r\n}\r\n\r\n// 展示房卡日志\r\nconst roomCardLogVisible = ref(false)\r\nfunction handleRoomCardLog() {\r\n  roomCardLogVisible.value = true\r\n}\r\n\r\nconst dialogVisible = ref(false)\r\nconst selectedTime = ref('')\r\nconst isEditMode = ref(false)\r\nfunction handleEditTime() {\r\n  selectedTime.value = dt.value.orderTogether.awakenTime // 设置当前时间\r\n  isEditMode.value = true // 进入编辑模式\r\n  dialogVisible.value = true // 显示弹窗\r\n}\r\nfunction handleAddTime() {\r\n  selectedTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss') // 默认当前时间\r\n  isEditMode.value = false // 进入添加模式\r\n  dialogVisible.value = true // 显示弹窗\r\n}\r\nfunction handleConfirm(awakenTime: string) {\r\n  const params: any = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    orderNo: dt.value.order.orderNo,\r\n    togetherCode: dt.value.orderTogether.togetherCode,\r\n  }\r\n\r\n  // 只有当 awakenTime 不为空时才添加到参数中\r\n  if (awakenTime) {\r\n    params.awakenTime = awakenTime\r\n  }\r\n  orderApi.updateOrderAwaken(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      dialogVisible.value = false // 关闭弹窗\r\n      if (awakenTime) {\r\n        dt.value.orderTogether.awakenTime = selectedTime.value // 更新叫早时间\r\n        ElMessage.success(isEditMode.value ? '时间修改成功' : '时间添加成功')\r\n      } else {\r\n        ElMessage.success('叫早提醒删除成功')\r\n        dt.value.orderTogether.awakenTime = ''\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\nfunction deleteAwaken() {\r\n  handleConfirm('')\r\n}\r\n\r\n// 禁用今天之前的日期和预离时间之后的日期\r\nfunction disabledDate(time: Date) {\r\n  // 禁用今天之前的日期\r\n  const isBeforeToday = time.getTime() < Date.now() - 8.64e7\r\n  // 禁用预离时间之后的日期\r\n  const isAfterCheckout = time.getTime() > dayjs(dt.value.orderTogether.planCheckoutTime).valueOf()\r\n  return isBeforeToday || isAfterCheckout\r\n}\r\n\r\n/** 如果选择了今天，禁用当前时间之前的小时 */\r\nfunction disabledHours() {\r\n  const hours = []\r\n  const selectedDate = dayjs(selectedTime.value)\r\n  const now = dayjs()\r\n  const checkoutTime = dayjs(dt.value.orderTogether.planCheckoutTime)\r\n\r\n  // 如果选择了今天，禁用当前时间之前的小时\r\n  if (selectedDate.isSame(now, 'day')) {\r\n    const currentHour = now.hour()\r\n    for (let i = 0; i < currentHour; i++) {\r\n      hours.push(i)\r\n    }\r\n  }\r\n\r\n  // 如果选择了预离当天，禁用预离时间之后的小时\r\n  if (selectedDate.isSame(checkoutTime, 'day')) {\r\n    const checkoutHour = checkoutTime.hour()\r\n    for (let i = checkoutHour + 1; i <= 23; i++) {\r\n      hours.push(i)\r\n    }\r\n  }\r\n\r\n  return hours\r\n}\r\n\r\n/** 添加一个函数验证叫早时间 */\r\nfunction validateWakeupTime() {\r\n  const wakeupTime = dayjs(selectedTime.value)\r\n  const checkoutTime = dayjs(dt.value.orderTogether.planCheckoutTime)\r\n  if (wakeupTime.isAfter(checkoutTime)) {\r\n    ElMessage.warning('叫早时间不能晚于预离时间')\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\nfunction addAwaken() {\r\n  if (validateWakeupTime()) {\r\n    handleConfirm(selectedTime.value)\r\n  }\r\n}\r\n/** 修改入住类型 */\r\nasync function isCheckinClick() {\r\n  const { data } = await orderApi.getCheckinType({ ...queryParams, orderNo: dt.value.order.orderNo })\r\n  let hourList = []\r\n  checkinTypes.value = data.map((item) => {\r\n    if (item.hourList.length > 0) {\r\n      hourList = item.hourList\r\n    }\r\n    return item\r\n  })\r\n  hourRoom.value = hourList\r\n  isCheckin.value = true\r\n}\r\n/** 保存入住类型 */\r\nfunction isCheckinSubmit() {\r\n  const params: updateCheckinTypes = {\r\n    ...queryParams,\r\n    orderNo: dt.value.order.orderNo,\r\n    checkinType: dt.value.order.checkinType,\r\n    hourCode: '',\r\n  }\r\n  // 如果是钟点房  则传钟点房代码\r\n  if (dt.value.order.checkinType == CheckinType.HOUR_ROOM) {\r\n    params.hourCode = dt.value.order.hourCode\r\n  }\r\n  orderApi.updateCheckinType(params).then((res) => {\r\n    getOrderDetail()\r\n    isCheckin.value = false\r\n    ElMessage.success(t('updateSuccess'))\r\n  })\r\n}\r\n\r\n// 计算出生日期的计算属性\r\nconst calculatedBirthday = computed(() => {\r\n  // 根据身份证号计算\r\n  const idNo = sensitiveStatus.value.isShowFullIdNo ? originalValues.value.idNo : dt.value.orderTogether?.idNo\r\n\r\n  if (idNo) {\r\n    // 如果当前显示的是脱敏的身份证号，使用原始值进行计算\r\n    const fullIdNo = sensitiveStatus.value.isShowFullIdNo ? idNo : originalValues.value.idNo\r\n    return getBirthdayFromIdCard(fullIdNo) || '-'\r\n  }\r\n\r\n  return '-'\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div class=\"order-dialog\">\r\n    <!-- 抵离时间 账务信息 -->\r\n    <div class=\"prices\">\r\n      <div class=\"price-info-container\">\r\n        <div class=\"price-item\">\r\n          <div class=\"price-label\">\r\n            {{ t('checkInOutTime') }}\r\n          </div>\r\n          <div class=\"price-value\">\r\n            {{ checkInOutTime() }}\r\n          </div>\r\n        </div>\r\n        <div class=\"price-item\">\r\n          <div class=\"price-label\">\r\n            {{ t('price') }}\r\n          </div>\r\n          <div class=\"price-value\">{{ t('yuan') }}{{ dt.orderPrice.vipPrice }}</div>\r\n        </div>\r\n        <div class=\"price-item\">\r\n          <div class=\"price-label\">\r\n            {{ t('consume') }}\r\n          </div>\r\n          <div class=\"price-value consume\">{{ t('yuan') }}{{ dt.summary?.consumeAmount }}</div>\r\n        </div>\r\n        <div class=\"price-item\">\r\n          <div class=\"price-label\">\r\n            {{ t('payment') }}\r\n          </div>\r\n          <div class=\"price-value payment\">{{ t('yuan') }}{{ dt.summary?.payAmount }}</div>\r\n        </div>\r\n        <div class=\"price-item\">\r\n          <div class=\"price-label\">\r\n            {{ t('preAuth') }}\r\n          </div>\r\n          <div class=\"price-value preauth\" @click=\"preAuthVisible = true\">{{ t('yuan') }}{{ dt.summary?.preAuthAmount }}</div>\r\n        </div>\r\n        <div class=\"price-item\">\r\n          <div class=\"price-label\">\r\n            {{ t('coupon') }}\r\n          </div>\r\n          <div class=\"price-value coupon\">{{ t('yuan') }}{{ dt.summary?.couponAmount }}</div>\r\n        </div>\r\n        <div class=\"price-item balance-item\">\r\n          <div class=\"price-label\">\r\n            {{ t('balance') }}\r\n          </div>\r\n          <div class=\"price-value\">{{ t('yuan') }}{{ dt.summary?.balanceAmount }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 按钮操作区域 -->\r\n    <div class=\"action-card\">\r\n      <div class=\"action-container\">\r\n        <div class=\"action-group\">\r\n          <el-dropdown v-if=\"dt.order.state === OrderState.CHECK_IN && dt.orderTogether.state === OrderState.CHECK_IN\" type=\"primary\" split-button plain placement=\"bottom-end\" class=\"spaced-dropdown\" @click=\"writeLockCard(true)\">\r\n            {{ t('makeCard') }}\r\n            <template #dropdown>\r\n              <el-dropdown-menu>\r\n                <el-dropdown-item @click=\"writeLockCard(false)\">\r\n                  {{ t('makeSameRoomCard') }}\r\n                </el-dropdown-item>\r\n                <el-dropdown-item @click=\"cancelCard()\">\r\n                  {{ t('cancelCard') }}\r\n                </el-dropdown-item>\r\n                <el-dropdown-item @click=\"handleRoomCardLog()\">\r\n                  {{ t('cardRecord') }}\r\n                </el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </template>\r\n          </el-dropdown>\r\n\r\n          <el-dropdown v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"['pms:account:create', 'pms:account:create:consume']\" split-button type=\"primary\" plain placement=\"bottom-end\" class=\"spaced-dropdown\" @click=\"entryAccount('pay')\">\r\n            {{ t('entryAccount') }}\r\n            <template #dropdown>\r\n              <el-dropdown-menu>\r\n                <Auth value=\"pms:account:create\">\r\n                  <el-dropdown-item @click=\"entryAccount('pay')\">\r\n                    {{ t('payment') }}\r\n                  </el-dropdown-item>\r\n                </Auth>\r\n                <Auth value=\"pms:account:create:consume\">\r\n                  <el-dropdown-item @click=\"entryAccount('consume')\">\r\n                    {{ t('consume') }}\r\n                  </el-dropdown-item>\r\n                </Auth>\r\n                <Auth value=\"pms:account:create\">\r\n                  <el-dropdown-item @click=\"entryAccount('preAuth')\">\r\n                    {{ t('preAuth') }}\r\n                  </el-dropdown-item>\r\n                </Auth>\r\n              </el-dropdown-menu>\r\n            </template>\r\n          </el-dropdown>\r\n\r\n          <el-button v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"'pms:account:create:pay-check-out'\" type=\"primary\" plain @click=\"entryLeavePrice()\">\r\n            {{ t('checkOut') }}\r\n          </el-button>\r\n\r\n          <el-button v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"['pms:order:update:continue-in', 'pms:order:update:advance-out']\" type=\"primary\" plain @click=\"entryRoomContinue\">\r\n            {{ t('extendStay') }}\r\n          </el-button>\r\n\r\n          <el-button v-if=\"dt.order.state === OrderState.CHECK_IN\" v-auth=\"'pms:order:create:add-together'\" type=\"primary\" plain @click=\"entryCohabitAtion\">\r\n            {{ t('addTogether') }}\r\n          </el-button>\r\n\r\n          <!--            <el-button\r\n              v-if=\"\r\n                dt.orderTogether.state === OrderState.CHECK_IN\r\n                  && dt.order.orderType === OrderType.GENERAL\r\n              \"\r\n              v-auth=\"'pms:order:update:change-in-team'\"\r\n              type=\"primary\"\r\n              plain\r\n              @click=\"entryTransferTeam\"\r\n            >\r\n              {{ t('transferToTeam') }}\r\n            </el-button> -->\r\n\r\n          <el-button v-if=\"dt.order.state === OrderState.CHECK_IN\" v-auth=\"'pms:order:update:merge-room'\" type=\"primary\" plain @click=\"entryJoinRoom\">\r\n            {{ t('joinRoom') }}\r\n          </el-button>\r\n\r\n          <el-button v-if=\"dt.order.state === OrderState.CHECK_IN && dt.order.orderType === OrderType.JOIN\" v-auth=\"'pms:order:update:quit-merge-room'\" type=\"primary\" plain @click=\"outJoinRoomVisible = true\">\r\n            {{ t('quitJoinRoom') }}\r\n          </el-button>\r\n\r\n          <el-button v-if=\"[OrderState.CREDIT, OrderState.CHECK_OUT].includes(dt.orderTogether.state)\" v-auth=\"'pms:order:update:recheck-in'\" type=\"primary\" plain @click=\"onRecheckIn\">\r\n            {{ t('recheckIn') }}\r\n          </el-button>\r\n          <template v-if=\"dt.order.state === OrderState.CHECK_IN\">\r\n            <el-badge v-if=\"rentGoodsCount > 0\" :value=\"rentGoodsCount\" class=\"item\">\r\n              <el-button type=\"primary\" plain style=\"margin-left: 10px\" @click=\"rent\">\r\n                {{ t('rent') }}\r\n              </el-button>\r\n            </el-badge>\r\n            <el-button v-else type=\"primary\" plain style=\"margin-left: 10px\" @click=\"rent\">\r\n              {{ t('rent') }}\r\n            </el-button>\r\n          </template>\r\n        </div>\r\n        <el-button type=\"primary\" plain @click=\"printCheckInFormVisible = true\">\r\n          {{ t('printCheckInForm') }}\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 信息卡片区域 -->\r\n    <div class=\"info-cards\">\r\n      <div class=\"info-card-row\">\r\n        <!-- 房间信息卡片 -->\r\n        <div class=\"info-card\">\r\n          <div class=\"info-card-header\">\r\n            <span>{{ t('roomInfo') }}</span>\r\n          </div>\r\n          <div class=\"info-card-body\">\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('roomNumber') }}：</div>\r\n              <div class=\"info-value\">\r\n                {{ dt.order.rNo }}\r\n                <el-button v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"'pms:order:update:change-room'\" link type=\"primary\" @click=\"checkType('changeHouses')\">\r\n                  {{ t('changeRoom') }}\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('roomType') }}：</div>\r\n              <div class=\"info-value\">\r\n                {{ dt.order.rtName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('roomPrice') }}：</div>\r\n              <div class=\"info-value price\">\r\n                <el-popover placement=\"top\" :width=\"300\" trigger=\"hover\" popper-class=\"price-detail-popover\">\r\n                  <template #reference>\r\n                    <span style=\"cursor: pointer; text-decoration: underline\">{{ t('yuan') }}{{ dt.order.vipPrice }}</span>\r\n                  </template>\r\n                  <template #default>\r\n                    <el-table :data=\"dt.orderPrices\" size=\"small\" style=\"width: 100%\">\r\n                      <el-table-column prop=\"priceDate\" label=\"日期\" width=\"140\">\r\n                        <template #default=\"{ row }\"> {{ row.priceDate }} ({{ ['', '一', '二', '三', '四', '五', '六', '日'][row.week] }}) </template>\r\n                      </el-table-column>\r\n                      <el-table-column prop=\"vipPrice\" label=\"房价\" width=\"80\">\r\n                        <template #default=\"{ row }\"> {{ t('yuan') }}{{ row.vipPrice }} </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                  </template>\r\n                </el-popover>\r\n                <el-button v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"'pms:order:update:change-price'\" link type=\"primary\" @click=\"checkType('changePrice')\">\r\n                  {{ t('changePrice') }}\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('breakfastCount') }}：</div>\r\n              <div class=\"info-value\">\r\n                <el-popover placement=\"top\" :width=\"500\">\r\n                  <EasyTable v-loading=\"loading\" :columns=\"tableColumns\" :span-method=\"colSpanMethod\" :table-data=\"tableData\" :options=\"options\" />\r\n                  <template #reference>\r\n                    <span class=\"breakfast-count\" style=\"text-decoration: underline\">{{ dt.order.buyBkNum }}</span>\r\n                  </template>\r\n                </el-popover>\r\n                <el-button v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"'pms:order:update:change-price'\" link type=\"primary\" @click=\"buyBkNumClick()\">\r\n                  {{ t('addBreakfas') }}\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 客人信息卡片 -->\r\n        <div class=\"info-card\">\r\n          <div class=\"info-card-header\">\r\n            <span>{{ t('guestInfo') }}</span>\r\n            <el-button v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"'pms:order:update:update-guest'\" link type=\"primary\" @click=\"onGuestEdit\">\r\n              {{ t('modify') }}\r\n            </el-button>\r\n          </div>\r\n          <div class=\"info-card-body\">\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('guestName') }}：</div>\r\n              <div class=\"info-value\">\r\n                {{ dt.orderTogether?.name }}\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('contactInfo') }}：</div>\r\n              <div class=\"info-value\">\r\n                <div class=\"flex items-center\">\r\n                  <span>{{ dt.orderTogether?.phone || '-' }}</span>\r\n                  <el-icon v-if=\"dt.orderTogether?.phone\" class=\"ml-2 cursor-pointer\" @click=\"togglePhoneSensitive\">\r\n                    <View v-if=\"!sensitiveStatus.isShowFullPhone\" />\r\n                    <Hide v-else />\r\n                  </el-icon>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('gender') }}：</div>\r\n              <div class=\"info-value\">\r\n                {{ dt.orderTogether?.sex === '0' ? t('female') : dt.orderTogether?.sex === '1' ? t('male') : t('secret') }}\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('birthday') }}：</div>\r\n              <div class=\"info-value\">\r\n                {{ calculatedBirthday }}\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('idNumber') }}：</div>\r\n              <div class=\"info-value\">\r\n                <div class=\"flex items-center\">\r\n                  <span>{{ dt.orderTogether?.idNo || '-' }}</span>\r\n                  <el-icon v-if=\"dt.orderTogether?.idNo\" class=\"ml-2 cursor-pointer\" @click=\"toggleIdNoSensitive\">\r\n                    <View v-if=\"!sensitiveStatus.isShowFullIdNo\" />\r\n                    <Hide v-else />\r\n                  </el-icon>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('idType') }}：</div>\r\n              <div class=\"info-value\">\r\n                <template v-for=\"item in idTypes\" :key=\"item.code\">\r\n                  <span v-if=\"item.code === dt.orderTogether?.idType\">\r\n                    {{ item.label }}\r\n                  </span>\r\n                </template>\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('nation') }}：</div>\r\n              <div class=\"info-value\">\r\n                {{ dt.orderTogether?.nationName || '-' }}\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('address') }}：</div>\r\n              <div class=\"info-value\">\r\n                <div class=\"flex items-center\">\r\n                  <span>{{ dt.orderTogether?.address || '-' }}</span>\r\n                  <el-icon v-if=\"dt.orderTogether?.address\" class=\"ml-2 cursor-pointer\" @click=\"toggleAddressSensitive\">\r\n                    <View v-if=\"!sensitiveStatus.isShowFullAddress\" />\r\n                    <Hide v-else />\r\n                  </el-icon>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"info-card-row\">\r\n        <!-- 订单信息卡片 -->\r\n        <div class=\"info-card\">\r\n          <div class=\"info-card-header\">\r\n            <span>{{ t('orderInfo') }}</span>\r\n          </div>\r\n          <div class=\"info-card-body\">\r\n            <div class=\"info-columns\">\r\n              <div class=\"info-column\">\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('orderNumber') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    {{ dt.order.orderNo }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('externalOrderNumber') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    <template v-if=\"updateOutOrderNo && dt.orderTogether.state === OrderState.CHECK_IN\">\r\n                      {{ dt.order.outOrderNo }}\r\n                      <el-button link type=\"primary\" @click=\"updateOutOrderNo = false\">\r\n                        {{ t('modify') }}\r\n                      </el-button>\r\n                    </template>\r\n                    <template v-else-if=\"dt.orderTogether.state === OrderState.CHECK_IN\">\r\n                      <el-input v-model=\"dt.order.outOrderNo\" style=\"width: 200px\" />\r\n                      <el-button link type=\"primary\" @click=\"outOrderNoSubmit\">\r\n                        {{ t('save') }}\r\n                      </el-button>\r\n                      <el-button link @click=\"updateOutOrderNo = true\">\r\n                        {{ t('cancel') }}\r\n                      </el-button>\r\n                    </template>\r\n                    <template v-else>\r\n                      {{ dt.order.outOrderNo }}\r\n                    </template>\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('checkinType') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    <template v-if=\"!isCheckin && dt.order.state === OrderState.CHECK_IN\">\r\n                      {{ dt.order.checkinTypeName }}\r\n                      <el-button link type=\"primary\" @click=\"isCheckinClick\">\r\n                        {{ t('modify') }}\r\n                      </el-button>\r\n                    </template>\r\n                    <template v-else-if=\"dt.order.state === OrderState.CHECK_IN\">\r\n                      <el-select v-model=\"dt.order.checkinType\" style=\"width: 100px\">\r\n                        <el-option v-for=\"(item, inx) in checkinTypes\" :key=\"inx\" :label=\"item.checkinTypeName\" :value=\"item.checkinType\" />\r\n                      </el-select>\r\n                      <el-select v-if=\"dt.order.checkinType == CheckinType.HOUR_ROOM\" v-model=\"dt.order.hourCode\" style=\"width: 100px\">\r\n                        <el-option v-for=\"item in hourRoom\" :key=\"item.hourCode\" :label=\"item.hourName\" :value=\"item.hourCode\" />\r\n                      </el-select>\r\n                      <el-button link type=\"primary\" @click=\"isCheckinSubmit\">\r\n                        {{ t('save') }}\r\n                      </el-button>\r\n                      <el-button link @click=\"isCheckin = false\">\r\n                        {{ t('cancel') }}\r\n                      </el-button>\r\n                    </template>\r\n                    <template v-else>\r\n                      {{ dt.order.checkinTypeName }}\r\n                    </template>\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('orderChannel') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    {{ dt.order.channelName }}\r\n                  </div>\r\n                </div>\r\n                <!--\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">\r\n                    {{ t('roomRatePolicy') }}\r\n                  </div>\r\n                  <div class=\"info-value\">\r\n                    {{ dt.order.ratePolicyName }}\r\n                  </div>\r\n                </div>\r\n                -->\r\n              </div>\r\n              <div class=\"info-column\">\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('checkinTime') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    {{ dayjs(dt.orderTogether.checkinTime).format('YYYY-MM-DD HH:mm') }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('stayDuration') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    {{ checkDays() }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ dt.orderTogether.state === OrderState.CHECK_OUT ? t('checkOutTime') : t('plannedCheckoutTime') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    {{\r\n                      dt.orderTogether.state === OrderState.CHECK_OUT || dt.orderTogether.state === OrderState.CREDIT\r\n                        ? dayjs(dt.orderTogether.checkoutTime).format('YYYY-MM-DD HH:mm')\r\n                        : dayjs(dt.orderTogether.planCheckoutTime).format('YYYY-MM-DD HH:mm')\r\n                    }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('salesperson') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    <template v-if=\"isUpdateSeller && dt.order.state === OrderState.CHECK_IN\">\r\n                      {{ dt.order.sellerName }}\r\n                      <el-button link type=\"primary\" @click=\"isSellerClick\">\r\n                        {{ t('modify') }}\r\n                      </el-button>\r\n                    </template>\r\n                    <template v-else-if=\"dt.order.state === OrderState.CHECK_IN\">\r\n                      <el-select v-model=\"dt.order.seller\" style=\"width: 120px\">\r\n                        <el-option v-for=\"item in sellers\" :key=\"item.username\" :label=\"item.nickname\" :value=\"item.username\" />\r\n                      </el-select>\r\n                      <el-button link type=\"primary\" @click=\"sellersSubmit\">\r\n                        {{ t('save') }}\r\n                      </el-button>\r\n                      <el-button link @click=\"isUpdateSeller = true\">\r\n                        {{ t('cancel') }}\r\n                      </el-button>\r\n                    </template>\r\n                    <template v-else>\r\n                      {{ dt.order.seller }}\r\n                    </template>\r\n                  </div>\r\n                </div>\r\n                <div class=\"info-item\">\r\n                  <div class=\"info-label\">{{ t('guestSourceType') }}：</div>\r\n                  <div class=\"info-value\">\r\n                    <div v-if=\"updateGuestSrcType\">\r\n                      <span>{{ dt.order.guestSrcTypeName }}</span>\r\n                      <el-button v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" v-auth=\"'pms:order:update:update-guest-src-type'\" link type=\"primary\" @click=\"sourceEdit\">\r\n                        {{ t('modify') }}\r\n                      </el-button>\r\n                    </div>\r\n                    <div v-else class=\"edit-field\">\r\n                      <div class=\"member-select-row\">\r\n                        <div class=\"select-with-buttons\">\r\n                          <el-select v-model=\"dt.order.guestSrcType\" placeholder=\"请选择\" style=\"width: 120px\">\r\n                            <el-option v-for=\"item in srcTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n                          </el-select>\r\n                          <el-button link type=\"primary\" @click=\"submitMember\">\r\n                            {{ t('save') }}\r\n                          </el-button>\r\n                          <el-button link @click=\"onMemberCancel\">\r\n                            {{ t('cancel') }}\r\n                          </el-button>\r\n                        </div>\r\n                        <!-- 根据客源类型显示不同的选择器 -->\r\n                        <span v-if=\"dt.order.guestSrcType === GuestSrcType.MEMBER\">\r\n                          <el-select\r\n                            v-model=\"memberForm.phone\"\r\n                            filterable\r\n                            remote\r\n                            reserve-keyword\r\n                            placeholder=\"请输入完整手机号\"\r\n                            :remote-method=\"remoteQueryMember\"\r\n                            :loading=\"memberForm.loading\"\r\n                            style=\"width: 150px; margin: 0 0px\"\r\n                            @change=\"changMember\"\r\n                          >\r\n                            <el-option v-for=\"item in members\" :key=\"item.mcode\" :value=\"item.mcode\" :label=\"item.name\"> {{ item.name }} - {{ item.phone }} </el-option>\r\n                          </el-select>\r\n                        </span>\r\n                        <span v-if=\"dt.order.guestSrcType === GuestSrcType.AGENT\">\r\n                          <el-select v-model=\"dt.order.agentCode\" style=\"width: 150px\" :placeholder=\"t('selectAgent')\">\r\n                            <el-option v-for=\"item in agents\" :key=\"item.paCode\" :label=\"item.paName\" :value=\"item.paCode\" />\r\n                          </el-select>\r\n                        </span>\r\n                        <span v-if=\"dt.order.guestSrcType === GuestSrcType.PROTOCOL\">\r\n                          <el-select v-model=\"dt.order.protocolCode\" style=\"width: 150px\" :placeholder=\"t('selectCompany')\">\r\n                            <el-option v-for=\"item in protocols\" :key=\"item.paCode\" :label=\"item.paName\" :value=\"item.paCode\" />\r\n                          </el-select>\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"dt.order.guestSrcType === GuestSrcType.MEMBER && dt.order.mcode\" class=\"info-item\">\r\n                  <div class=\"info-label\" />\r\n                  <div class=\"info-value\">\r\n                    <div class=\"member-detail\">\r\n                      <div class=\"member-row\">\r\n                        <span class=\"member-item\">{{ t('name') }}：{{ memberCardDetail.name }}</span>\r\n                        <span class=\"member-item\">{{ t('phone') }}：{{ memberCardDetail.phone }}</span>\r\n                      </div>\r\n                      <div class=\"member-row\">\r\n                        <span class=\"member-item\">{{ t('balance') }}：{{ memberCardDetail.storeCardBalance ? memberCardDetail.storeCardBalance : 0 }}</span>\r\n                        <span class=\"member-item\">{{ t('membershipLevel') }}：{{ memberCardDetail.mtName }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 其他信息卡片 -->\r\n        <div class=\"info-card\">\r\n          <div class=\"info-card-header\">\r\n            <span>{{ t('otherInfo') }}</span>\r\n          </div>\r\n          <div class=\"info-card-body\">\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('wakeUpReminder') }}：</div>\r\n              <div class=\"info-value\">\r\n                <span v-if=\"dt.orderTogether.awakenTime && dayjs(dt.orderTogether.awakenTime).isValid()\">\r\n                  <el-link type=\"primary\" @click=\"handleEditTime\">\r\n                    {{ dayjs(dt.orderTogether.awakenTime).format('MM-DD HH:mm') }}\r\n                    (到点系统会弹出通知提醒)\r\n                  </el-link>\r\n                  <el-popconfirm :title=\"t('confirmDeleteWakeup')\" @confirm=\"deleteAwaken\">\r\n                    <template #reference>\r\n                      <el-button type=\"danger\" link class=\"delete-icon\">\r\n                        <el-icon><Delete /></el-icon>\r\n                      </el-button>\r\n                    </template>\r\n                  </el-popconfirm>\r\n                </span>\r\n                <span v-else>\r\n                  <el-link v-if=\"dt.orderTogether.state === OrderState.CHECK_IN\" type=\"primary\" @click=\"handleAddTime\">{{ t('addWakeupTime') }}</el-link>\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">{{ t('remarks') }}：</div>\r\n              <div class=\"info-value\">\r\n                <template v-if=\"updateRemark && dt.order.state === OrderState.CHECK_IN\">\r\n                  {{ dt.order.remark || '-' }}\r\n                  <el-button link type=\"primary\" @click=\"updateRemark = false\">\r\n                    {{ t('modify') }}\r\n                  </el-button>\r\n                </template>\r\n                <template v-else-if=\"dt.order.state === OrderState.CHECK_IN\">\r\n                  <el-input v-model=\"dt.order.remark\" type=\"textarea\" :rows=\"6\" maxlength=\"1000\" />\r\n                  <div class=\"button-group\">\r\n                    <el-button link type=\"primary\" @click=\"remarkSubmit\">\r\n                      {{ t('save') }}\r\n                    </el-button>\r\n                    <el-button link @click=\"updateRemark = true\">\r\n                      {{ t('cancel') }}\r\n                    </el-button>\r\n                  </div>\r\n                </template>\r\n                <template v-else>\r\n                  {{ dt.order.remark }}\r\n                </template>\r\n              </div>\r\n            </div>\r\n            <div class=\"info-item\">\r\n              <div class=\"info-label\">\r\n                {{ t('outOrderRemark') }}\r\n              </div>\r\n              <div class=\"info-value\">\r\n                {{ dt.order.outOrderRemark }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!--    入账弹窗 -->\r\n    <EntryAccount\r\n      v-if=\"entryAccountVisible\"\r\n      v-model=\"entryAccountVisible\"\r\n      :tab-name=\"tabName\"\r\n      :order-no=\"orderVisible.accountOrderNo\"\r\n      :order-type=\"orderVisible.type\"\r\n      :no-type=\"props.noType\"\r\n      :order-together-code=\"orderVisible.togetherCode\"\r\n      @success=\"orderRefresh\"\r\n    />\r\n    <!--    结账退房 -->\r\n    <LeavePrice v-if=\"entryLeavePriceVisible\" v-model=\"entryLeavePriceVisible\" :no-type=\"props.noType\" :order-no=\"dt.order.orderNo\" :order-together-code=\"props.togetherCode\" @success=\"orderRefresh\" />\r\n\r\n    <!--    撤销结账 -->\r\n    <RevokeCheckout v-if=\"entryRevokeCheckoutVisible\" v-model=\"entryRevokeCheckoutVisible\" :order-no=\"orderVisible.accountOrderNo\" :order-type=\"orderVisible.type\" :order-together-code=\"orderVisible.togetherCode\" />\r\n    <!--    弹出续住/提前窗口 -->\r\n    <RoomContinue v-if=\"entryRoomContinueVisible\" v-model=\"entryRoomContinueVisible\" :order-no=\"orderVisible.accountOrderNo\" :order-type=\"orderVisible.type\" :order-together-code=\"orderVisible.togetherCode\" @success=\"orderRefresh\" />\r\n    <!--    弹出同住窗口 客人 -->\r\n    <CohabitAtion\r\n      v-if=\"entrycohabitAtionVisible\"\r\n      v-model=\"entrycohabitAtionVisible\"\r\n      :cohabit-ation-title=\"cohabitAtionTitle\"\r\n      :order-together=\"{\r\n        ...dt.orderTogether,\r\n        phone: originalValues.phone || dt.orderTogether.phone,\r\n        idNo: originalValues.idNo || dt.orderTogether.idNo,\r\n        address: originalValues.address || dt.orderTogether.address,\r\n      }\"\r\n      :order-no=\"orderVisible.accountOrderNo\"\r\n      :order-type=\"orderVisible.type\"\r\n      :r-no=\"dt.order.rNo\"\r\n      :r-code=\"dt.order.rCode\"\r\n      :order-together-code=\"orderVisible.togetherCode\"\r\n      @success=\"orderRefresh\"\r\n    />\r\n    <!--    转入团队 -->\r\n    <TransferTeam v-if=\"entryTransferTeamVisible\" v-model=\"entryTransferTeamVisible\" />\r\n    <!--    加入联房 -->\r\n    <LianFang v-if=\"entryJoinRoomVisible\" v-model=\"entryJoinRoomVisible\" :order-no=\"orderVisible.accountOrderNo\" :r-no=\"dt.order.rNo\" @success=\"orderRefresh\" />\r\n    <!--    换房窗口 -->\r\n    <RoomExchange v-if=\"entryexchangeVisible\" v-model=\"entryexchangeVisible\" v-bind=\"checkVisible\" @success=\"orderRefresh\" />\r\n\r\n    <!--    改价窗口 -->\r\n    <RoomEditPrice\r\n      v-if=\"roomPriceProps.visible\"\r\n      v-model=\"roomPriceProps.visible\"\r\n      :room-list-price=\"roomPriceProps.initialPriceList\"\r\n      :rt-code=\"roomPriceProps.rtCode\"\r\n      :order-type=\"roomPriceProps.orderType\"\r\n      :order-no=\"roomPriceProps.orderNo\"\r\n      @success=\"orderRefresh\"\r\n    />\r\n    <!-- 预授权窗口 -->\r\n    <GrantModal v-if=\"preAuthVisible\" v-model=\"preAuthVisible\" :order-together-code=\"props.togetherCode\" @refresh=\"orderRefresh\" />\r\n    <!-- 打印入住单窗口 -->\r\n    <PrintCheckInForm v-if=\"printCheckInFormVisible\" v-model=\"printCheckInFormVisible\" :order-no=\"dt.order.orderNo\" />\r\n    <!-- 添加租借列表弹窗 -->\r\n    <el-dialog v-model=\"rentListVisible\" title=\"租借物品\" width=\"70%\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <RentList :order-no=\"dt.order.orderNo\" @success=\"handleRentSuccess\" />\r\n    </el-dialog>\r\n\r\n    <!-- 房卡记录弹窗 -->\r\n    <el-dialog v-model=\"roomCardLogVisible\" title=\"房卡记录\" width=\"880\" :close-on-click-modal=\"false\" append-to-body destroy-on-close>\r\n      <RoomCardLogList :order-no=\"dt.order.orderNo\" />\r\n    </el-dialog>\r\n\r\n    <!-- 退出联房 -->\r\n    <el-dialog v-model=\"outJoinRoomVisible\" :title=\"t('outJoinRoom')\" width=\"500\">\r\n      <div style=\"padding: 15px; font-size: 16px\">{{ t('selectedRoom') }} {{ dt.order.rNo }}</div>\r\n      <div style=\"padding: 15px; font-size: 16px\">\r\n        {{ t('confirmOutJoinRoom') }}\r\n      </div>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"outJoinRoomVisible = false\">\r\n            {{ t('cancel') }}\r\n          </el-button>\r\n          <el-button type=\"primary\" @click=\"outSubmit\">\r\n            {{ t('confirm') }}\r\n          </el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 添加叫早弹窗 -->\r\n    <el-dialog v-model=\"dialogVisible\" :title=\"isEditMode ? t('editWakeupTime') : t('addWakeupTime')\" width=\"400px\">\r\n      <div class=\"wake-up-form\">\r\n        <el-form label-width=\"100px\">\r\n          <el-form-item :label=\"t('wakeupTime')\">\r\n            <el-date-picker\r\n              v-model=\"selectedTime\"\r\n              type=\"datetime\"\r\n              format=\"YYYY-MM-DD HH:mm\"\r\n              value-format=\"YYYY-MM-DD HH:mm:ss\"\r\n              :clearable=\"false\"\r\n              :disabled-date=\"disabledDate\"\r\n              :disabled-hours=\"disabledHours\"\r\n              :picker-options=\"{\r\n                start: '00:00',\r\n                step: '00:15',\r\n                end: '23:45',\r\n              }\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">{{ t('cancel') }}</el-button>\r\n          <el-button type=\"primary\" @click=\"addAwaken\">\r\n            {{ t('confirm') }}\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <EasyDialog ref=\"easyDialogRef\" :title=\"t('addBreakfas')\" is-body show-cancel-button show-confirm-button dialog-width=\"600\" :options=\"{ class: 'order-dialog' }\" @submit=\"formSubmit\" @close=\"formCancel\">\r\n      <div v-if=\"activeName === 1\" class=\"dialog-input flex-center\">\r\n        <span class=\"mr-[10px]\">{{ t('unifiedGiveaway') }}</span>\r\n        <div>\r\n          <el-input v-model=\"pleaseInput\" type=\"number\" :placeholder=\"t('pleaseInput')\" @input=\"addActiveName()\">\r\n            <template #append>\r\n              {{ t('servings') }}\r\n            </template>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <el-tabs v-model=\"activeName\" class=\"dialog-tabs\" @tab-click=\"handleClick\">\r\n        <el-tab-pane :label=\"t('complimentary')\" :name=\"1\">\r\n          <EasyTable v-loading=\"loading\" :columns=\"tableColumns1\" :span-method=\"colSpanMethod\" :table-data=\"tableData1\" :options=\"options\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane :label=\"t('purchase')\" :name=\"2\">\r\n          <EasyTable v-loading=\"loading\" :columns=\"tableColumns2\" :span-method=\"colSpanMethod\" :table-data=\"tableData2\" :options=\"options\" />\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </EasyDialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.order-dialog {\r\n  padding: 16px;\r\n}\r\n\r\n.delete-icon {\r\n  margin-left: 8px;\r\n  padding: 2px;\r\n}\r\n\r\n.wake-up-form {\r\n  padding: 20px;\r\n\r\n  :deep(.el-date-picker) {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.member-detail {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n\r\n  span {\r\n    white-space: nowrap;\r\n    min-width: 150px;\r\n    margin-right: 10px;\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n.select-with-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.prices,\r\n.action-card {\r\n  padding: 16px;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 16px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.price-info-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n}\r\n\r\n.price-item {\r\n  flex: 1;\r\n  min-width: 140px;\r\n  padding: 8px 12px;\r\n  text-align: center;\r\n  position: relative;\r\n\r\n  &:not(:last-child)::after {\r\n    content: '';\r\n    position: absolute;\r\n    right: 0;\r\n    top: 20%;\r\n    height: 60%;\r\n    width: 1px;\r\n    background-color: #ebeef5;\r\n  }\r\n}\r\n\r\n.price-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.price-value {\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n\r\n  &.consume {\r\n    color: #f56c6c;\r\n  }\r\n\r\n  &.payment {\r\n    color: #67c23a;\r\n  }\r\n\r\n  &.preauth {\r\n    color: #409eff;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      text-decoration: underline;\r\n    }\r\n  }\r\n\r\n  &.coupon {\r\n    color: #e6a23c;\r\n  }\r\n}\r\n\r\n.balance-item {\r\n  background-color: #f56c6c;\r\n  border-radius: 6px;\r\n  margin-left: 8px;\r\n\r\n  .price-label,\r\n  .price-value {\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.action-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.action-group {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.spaced-dropdown {\r\n  margin-right: 0 !important;\r\n}\r\n\r\n/* 信息卡片样式 */\r\n.info-cards {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.info-card-row {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.info-card {\r\n  flex: 1;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  border: 1px solid #ebeef5;\r\n  /* 移除阴影效果 */\r\n  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); */\r\n  overflow: hidden;\r\n}\r\n\r\n.info-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background-color: #f8f9fa;\r\n  font-weight: 500;\r\n}\r\n\r\n.info-card-body {\r\n  padding: 16px;\r\n}\r\n\r\n.info-columns {\r\n  display: flex;\r\n  gap: 16px;\r\n}\r\n\r\n.info-column {\r\n  flex: 1;\r\n}\r\n\r\n/* 在小屏幕上改为单列显示 */\r\n@media screen and (max-width: 992px) {\r\n  .info-columns {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-label {\r\n  width: 140px;\r\n  color: #606266;\r\n  padding-right: 12px;\r\n  text-align: right;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  color: #303133;\r\n\r\n  &.price {\r\n    color: #f56c6c;\r\n    font-weight: 500;\r\n  }\r\n\r\n  // 脱敏切换按钮样式\r\n  .el-icon {\r\n    color: #909399;\r\n    transition: color 0.3s;\r\n\r\n    &:hover {\r\n      color: #409eff;\r\n    }\r\n  }\r\n}\r\n\r\n.breakfast-count {\r\n  color: #f56c6c;\r\n  cursor: pointer;\r\n  font-weight: 500;\r\n}\r\n\r\n.edit-field {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.edit-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 8px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.member-detail {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n\r\n  span {\r\n    white-space: nowrap;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media screen and (max-width: 992px) {\r\n  .info-card-row {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .info-card {\r\n    margin-bottom: 16px;\r\n  }\r\n}\r\n\r\n.member-detail {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.member-row {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.member-item {\r\n  min-width: 180px;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "t", "useI18n", "orderRefresh", "userStore", "useUserStore", "dictTypes", "DICT_TYPE_GUEST_SRC_TYPE", "DICT_TYPE_CHECKIN_TYPE", "ORDER_SOURCE", "CONSTANT_TYPE_CODE_SZ", "DICT_TYPE_GUARANTEE_TYPE", "memberCardDetail", "ref", "name", "mtName", "phone", "storeCardBalance", "updateGuestSrcType", "updateRemark", "isUpdateSeller", "sellers", "queryParams", "reactive", "gcode", "hcode", "updateOutOrderNo", "entryAccountVisible", "printCheckInFormVisible", "entryLeavePriceVisible", "entryRevokeCheckoutVisible", "entryRoomContinueVisible", "entryexchangeVisible", "cohabitAtionTitle", "entrycohabitAtionVisible", "tabName", "hotelDoorConfig", "members", "memberForm", "guestSrcType", "mcode", "agentCode", "protocolCode", "loading", "orderVisible", "accountOrderNo", "togetherCode", "tabId", "type", "is<PERSON><PERSON><PERSON>n", "remark", "preAuthVisible", "agents", "protocols", "srcTypeList", "checkinTypeList", "orderSources", "hourRts", "dbs", "dt", "order", "hourCode", "guest<PERSON><PERSON>", "buyBkNum", "orderNo", "outOrderNo", "<PERSON><PERSON><PERSON>", "teamName", "pinyin", "channelCode", "channelName", "orderSource", "address", "orderSourceName", "checkinType", "checkinTypeName", "checkinTime", "planCheckinTime", "planCheckoutTime", "rtCode", "rtName", "rCode", "rNo", "lockNo", "mac", "lockVersion", "buildNo", "floorNo", "guestSrcTypeName", "bookNo", "orderType", "bindCode", "canChangePrice", "is<PERSON><PERSON>", "accState", "isInvoice", "bkTicketNum", "activityCode", "priceStrategyCode", "state", "checkoutState", "isMadeCard", "checkinShiftNo", "checkinOperator", "checkoutShiftNo", "checkoutTime", "checkoutOperator", "payShiftNo", "payTime", "payOperator", "seller", "sellerName", "days", "totalFee", "balance", "vipPrice", "prices", "arrState", "deposit", "outOrderRemark", "summary", "consumeAmount", "payAmount", "preAuthAmount", "couponAmount", "balanceAmount", "orderPrice", "price", "priceDate", "week", "orderPrices", "orderTogether", "idType", "IdType", "IDCERT", "idTypeName", "nation", "idNo", "sex", "SexEnum", "MALE", "toString", "no", "BooleanEnum", "YES", "isTeam", "NO", "OrderState", "CHECK_IN", "awakenTime", "secrecy", "noDisturbing", "nationName", "serverTime", "idTypes", "checkinTypes", "hourRoom", "guestSrcTypes", "easyDialogRef", "sensitiveStatus", "isShowFullPhone", "isShowFullIdNo", "isShowFullAddress", "originalValues", "activeName", "pleaseInput", "options", "maxHeight", "paginationConfig", "pageLimit", "tableData", "tableData1", "tableData2", "tableColumns", "prop", "label", "tableColumns1", "width", "render", "row", "h", "ElInputNumber", "modelValue", "bkNum", "style", "min", "newValue", "tableColumns2", "buyedBkNum", "class", "bkPrice", "handleClick", "tab", "value", "paneName", "deepCopy", "map", "item", "async", "formSubmit", "dayBks", "orderApi", "putOrderGiveBk", "formCancel", "putOrderBuyBk", "visible", "getOrderDetail", "spanArr", "colSpanMethod", "rowIndex", "columnIndex", "_row", "onMounted", "Promise", "all", "initConstants", "getServerTime", "rentList", "window", "CallBridge", "__RUNNING_IN_PMS_AGENT__", "<PERSON><PERSON><PERSON><PERSON>", "closeSocket", "onBeforeUnmount", "rentGoodsCount", "rentApi", "getRentGoodsList", "then", "res", "code", "data", "total", "checkInOutTime", "CHECK_OUT", "CREDIT", "dayjs", "format", "concat", "checkDays", "CheckinType", "HOUR_ROOM", "checkinDate", "startOf", "daysDiff", "diff", "getHotelDoorConfig", "params", "deviceApi", "getHotelDoor", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "DICT_TYPE_ID_TYPE", "filter", "dictType", "serverTimeApi", "getOrderDtail", "_a", "saveAndMask", "_b", "_c", "changMember", "getOrderGetBk", "pos", "i", "length", "push", "accountApi", "statAccountByTogetherCode", "isSellerClick", "userApi", "listSeller", "getSellers", "sellersSubmit", "memberApi", "updateSeller", "selectedSeller", "find", "username", "nickname", "ElMessage", "success", "entryAccount", "val", "entryRoomContinue", "entryCohabitAtion", "watch", "_orderNo", "rentListVisible", "entryTransferTeamVisible", "rent", "handleRentSuccess", "entryJoinRoomVisible", "entryJoin<PERSON>oom", "outJoinRoomVisible", "outSubmit", "quitMergeRoom", "error", "msg", "roomPriceProps", "initialPriceList", "roomBkNum", "checkVisible", "checkType", "openChangeRoomDialog", "message", "onClick", "showClose", "duration", "getChangePrice", "dayPrices", "sourceEdit", "TRAVEL_GROUP", "MEETING_GROUP", "customerApi", "simpleList", "belongHcode", "paType", "isEnable", "remarkSubmit", "outOrderNoSubmit", "onGuestEdit", "remoteQueryMember", "query", "setTimeout", "pageNo", "pageSize", "pagelist", "list", "detail", "togglePhoneSensitive", "toggleIdNoSensitive", "toggleAddressSensitive", "submitMember", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "member", "agent", "protocol", "GuestSrcType", "WALK_IN", "errorMessage", "catch", "onMemberCancel", "onRecheckIn", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "recheckIn", "isInClient", "downloadLink", "dangerouslyUseHTMLString", "writeLockCard", "newCard", "isConnected", "resolve", "initCardReader", "JSON", "parse", "succeed", "method", "ClientMethodEnum", "WRITELOCKCARD", "cardInfo", "ElNotification", "title", "generateCardInfoHtml", "position", "roomNo", "roomCardLogApi", "createRoomCardLog", "periodTime", "Number", "expire", "console", "log", "sysTimeString", "timeStampInSeconds", "Math", "floor", "parseInt", "json", "lock<PERSON>er", "version", "checkin", "valueOf", "allowLockOut", "replaceCard", "checkTime", "conf", "for<PERSON>ach", "param", "parameterCode", "parameterContent", "jsonString", "stringify", "timer", "setInterval", "handleLockCard", "clearInterval", "roomCardLogVisible", "dialogVisible", "selectedTime", "isEditMode", "handleEditTime", "handleAddTime", "handleConfirm", "updateOrderAwaken", "deleteAwaken", "disabledDate", "time", "isBeforeToday", "getTime", "Date", "now", "isAfterCheckout", "disabledHours", "hours", "selectedDate", "isSame", "currentHour", "hour", "addAwaken", "wakeupTime", "isAfter", "warning", "validateWakeupTime", "isCheckinClick", "getCheckinType", "hourList", "isCheckinSubmit", "updateCheckinType", "calculatedBirthday", "computed", "fullIdNo", "getBirthdayFromIdCard", "CANCELLOCKCARD", "info", "show"], "mappings": "ogRAoWA,MAAMA,GAAQC,EAcRC,GAAQC,IAMRC,EAAEA,IAAMC,IACd,SAASC,KACPJ,GAAM,WACNA,GAAM,UAAS,CAIjB,MAAMK,GAAYC,IAGZC,GAAY,CAACC,GAA0BC,GAAwBC,GAAcC,GAAuBC,IAEpGC,GAAmBC,EAA8B,CACrDC,KAAM,GACNC,OAAQ,GACRC,MAAO,GACPC,iBAAkB,IAEdC,GAAqBL,GAAI,GACzBM,GAAeN,GAAI,GAEnBO,GAAiBP,GAAI,GAErBQ,GAAUR,EAA8C,IAExDS,GAAcC,EAAS,CAE3BC,MAAOpB,GAAUoB,MAEjBC,MAAOrB,GAAUqB,QAEbC,GAAmBb,GAAI,GACvBc,GAAsBd,GAAI,GAE1Be,GAA0Bf,GAAI,GAE9BgB,GAAyBhB,GAAI,GAECA,GAAI,GAEtBA,EAAI,IAEQA,GAAI,GAE5B,MAAAiB,GAA6BjB,GAAI,GAEjCkB,GAA2BlB,GAAI,GAE/BmB,GAAuBnB,GAAI,GAE3BoB,GAAoBpB,EAAI,IACxBqB,GAA2BrB,GAAI,GAE/BsB,GAAUtB,EAAI,IACduB,GAAkBvB,IAElBwB,GAAUxB,EAA+B,IAEzCyB,GAAazB,EAAgC,CACjD0B,aAAc,GAEdvB,MAAO,GACPwB,MAAO,GACPC,UAAW,GACXC,aAAc,GACdC,SAAS,IAGLC,GAAe/B,EAAkC,CACrDgC,eAAgB,GAChBC,aAAc,GACdC,MAAO,IACPC,KAAM,UAGFC,GAAYpC,GAAI,GAEhBqC,GAASrC,IAETsC,GAAiBtC,GAAI,GAErBuC,GAASvC,EAA0C,IAEnDwC,GAAYxC,EAA0C,IAEtDyC,GAAczC,EAA4B,IAE1C0C,GAAkB1C,EAA4B,IAE9C2C,GAAe3C,EAA4B,IAE3C4C,GAAU5C,EAA4B,IAEtC6C,GAAM7C,EAA4B,IAElC8C,GAAK9C,EAAI,CACb+C,MAAO,CACLC,SAAU,GACVnB,aAAc,GACdF,MAAO,GACPC,UAAW,GACXqB,UAAW,GAEXC,SAAU,GAEVC,QAAS,GAETC,WAAY,GAEZC,UAAW,GAEXC,SAAU,GAEVC,OAAQ,GAERpD,MAAO,GAEPqD,YAAa,GAEbC,YAAa,GAEbC,YAAa,GAEbC,QAAS,GACTC,gBAAiB,GAEjBC,YAAa,GAEbC,gBAAiB,GAEjBC,YAAa,GAEbC,gBAAiB,GAEjBC,iBAAkB,GAElBC,OAAQ,GACRC,OAAQ,GAERC,MAAO,GAEPC,IAAK,GAELC,OAAQ,GAERC,IAAK,GAELC,YAAa,GAEbC,QAAS,GAETC,QAAS,GAEThD,aAAc,GACdiD,iBAAkB,GAElBC,OAAQ,GAERC,UAAW,GAEXC,SAAU,GAEVC,eAAgB,GAEhBC,OAAQ,GAERC,SAAU,GAEVC,UAAW,GAEXC,YAAa,EAEbC,aAAc,GAEdC,kBAAmB,GAEnBC,MAAO,GAEPC,cAAe,GAEfC,WAAY,GAEZC,eAAgB,GAEhBC,gBAAiB,GAEjBC,gBAAiB,GAEjBC,aAAc,GAEdC,iBAAkB,GAElBC,WAAY,GAEZC,QAAS,GAETC,YAAa,GAEbC,OAAQ,GACRC,WAAY,GAEZC,KAAM,EAENC,SAAU,EAEVC,QAAS,EAETC,SAAU,EACVC,OAAQ,GAERC,SAAU,GAEVC,QAAS,GAETpE,OAAQ,GAERqE,eAAgB,IAElBC,QAAS,CAEPC,cAAe,KAEfC,UAAW,KAEXC,cAAe,KAEfC,aAAc,KAEdC,cAAe,MAEjBC,WAAY,CAEVC,MAAO,EAEPC,UAAW,GAEXb,SAAU,EAEVc,KAAM,GAERC,YAAa,GACbC,cAAe,CAEbrF,aAAc,GAEdhC,KAAM,GAENsH,OAAQC,GAAOC,OACfC,WAAY,GACZC,OAAQ,GAERC,KAAM,GAENjE,QAAS,GAETxD,MAAO,GAEP0H,IAAKC,GAAQC,KAAKC,WAClB5D,MAAO,GACPC,IAAK,GACL4D,GAAI,GACJjD,OAAQkD,GAAYC,IACpBC,OAAQF,GAAYG,GACpB/C,MAAOgD,GAAWC,SAElBC,WAAY,GAEZC,QAAS,IAETC,aAAc,IACdC,WAAY,GAEZ5E,YAAa,GACb6B,aAAc,GAEd3B,iBAAkB,MAIhB2E,GAAa5I,EAAI,IAGjB6I,GAAU7I,EAAqB,IAE/B8I,GAAe9I,EAA8B,IAE7C+I,GAAW/I,EAAgB,IAE3BgJ,GAAgBhJ,EAAqB,IAErCiJ,GAAgBjJ,IAGhBkJ,GAAkBlJ,EAAI,CAC1BmJ,iBAAiB,EACjBC,gBAAgB,EAChBC,mBAAmB,IAIfC,GAAiBtJ,EAAI,CACzBG,MAAO,GACPyH,KAAM,GACNjE,QAAS,KAEL4F,GAAavJ,EAAiC,GAC9CwJ,GAAcxJ,IAEdyJ,GAAUzJ,EAAS,CACvB0J,UAAW,IACXC,iBAAkB,CAChBC,UAAW,OAGT9H,GAAU9B,GAAI,GAEd6J,GAAY7J,EAA6B,IACzC8J,GAAa9J,EAA6B,IAC1C+J,GAAa/J,EAA6B,IAG1CgK,GAAetJ,EAAgD,CACnE,CACEuJ,KAAM,YACNC,MAAO9K,GAAE,SAEX,CACE6K,KAAM,YACNC,MAAO9K,GAAE,cAEX,CACE6K,KAAM,QACNC,MAAO9K,GAAE,kBAEX,CACE6K,KAAM,aACNC,MAAO9K,GAAE,eAIP+K,GAAgBzJ,EAAgD,CACpE,CACEuJ,KAAM,YACNG,MAAO,IACPF,MAAO9K,GAAE,SAEX,CACE6K,KAAM,YACNG,MAAO,IACPF,MAAO9K,GAAE,cAEX,CACE6K,KAAM,QACNC,MAAO9K,GAAE,iBACTiL,OAAQ,EAAGC,SACTC,EAAEC,EAAe,CACfC,WAAYH,EAAII,MAChBC,MAAO,eACPC,IAAK,EACL,sBAAwBC,IACtBP,EAAII,MAAQG,CAAA,OAMhBC,GAAgBpK,EAAgD,CACpE,CACEuJ,KAAM,YACNG,MAAO,IACPF,MAAO9K,GAAE,SAEX,CACE6K,KAAM,YACNG,MAAO,IACPF,MAAO9K,GAAE,cAEX,CACE6K,KAAM,aACNC,MAAO9K,GAAE,YACTiL,OAAQ,EAAGC,SACTC,EAAE,MAAO,CACPA,EAAE,MAAO,CAAEI,MAAO,oBAAsB,GAAGvL,GAAE,sBAAsBkL,EAAIS,cACvER,EAAE,MAAO,CAAEI,MAAO,oBAAsB,CACtCJ,EAAE,OAAQ,CAAES,MAAO,aAAe5L,GAAE,uBACpCmL,EAAEC,EAAe,CACfC,WAAYH,EAAIpH,SAChByH,MAAO,cACPC,IAAK,EACL,sBAAwBC,IACtBP,EAAIpH,SAAW2H,CAAA,MAIrBN,EAAE,MAAO,CAAEI,MAAO,oBAAsB,GAAGvL,GAAE,gBAAgBkL,EAAIW,gBAczE,SAASC,GAAYC,GACnB5B,GAAW6B,MAAQD,EAAIE,SACZvB,GAAAsB,MAAQE,EAASzB,GAAUuB,OAC3BrB,GAAAqB,MAAQE,EAASzB,GAAUuB,OAC3BrB,GAAAqB,MAAMG,KAAKC,IACpBA,EAAKtI,SAAW,EACTsI,IACR,CAkBHC,eAAeC,KACP,MAAAvI,EAAUnE,GAAMmE,QAAQ,GAG1B,GAFJ8F,GAAcmC,MAAMtJ,SAAU,EAEL,IAArByH,GAAW6B,MAAa,CAC1B,MAAMO,EAAS7B,GAAWsB,MAAMG,KAAKC,IAC5B,CACLrE,UAAWqE,EAAKrE,UAChBuD,MAAOc,EAAKd,gBAGVkB,GAASC,eAAe,CAC5B1I,UACAwI,WAESG,IAAA,CAGY,IAArBvC,GAAW6B,cACPQ,GAASG,cAAc,CAC3B5I,UACAD,SAAU6G,GAAWqB,MAAM,GAAGlI,SAC9BjB,aAAcjD,GAAMiD,aACpBiF,MAAO6C,GAAWqB,MAAM,GAAGH,UAElBa,KACb,CAGF,SAASA,KACP7C,GAAcmC,MAAMY,SAAU,EAC9B/C,GAAcmC,MAAMtJ,SAAU,EACfmK,IAAA,CAEX,MAAAC,GAAUlM,EAAc,IAW9B,SAASmM,IAAcC,SAAEA,EAAUC,YAAAA,IAEjC,GAAoB,IAAhBA,IAAsBpD,GAAcmC,MAAMY,QAAS,CAC/C,MAAAM,EAAOJ,GAAQd,MAAMgB,GAEpB,MAAA,CAACE,EADKA,EAAO,EAAI,EAAI,EACV,CAGpB,GAAoB,IAAhBD,GAAqBpD,GAAcmC,MAAMY,SAAgC,MAArBzC,GAAW6B,MAAe,CAC1E,MAAAkB,EAAOJ,GAAQd,MAAMgB,GAEpB,MAAA,CAACE,EADKA,EAAO,EAAI,EAAI,EACV,CACpB,CA4BFC,GAAUd,gBACFe,QAAQC,IAAI,CAACC,KAAiBT,KAAkBU,OAI7CC,UACiC,IAA9BC,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,GAAWC,aAAY,IAI3BC,GAAgB,UAC4B,IAA9BL,OAAeC,iBAAkF,IAA5CD,OAAeE,0BAC9EC,GAAWC,aAAY,IAIrB,MAAAE,GAAiBnN,EAAI,GAC3B,SAAS4M,KACPQ,GACGC,iBAAiB,IACb5M,GACH0C,QAASnE,GAAMmE,QAAQ,GACvBmC,MAAO,MAERgI,MAAMC,IACY,IAAbA,EAAIC,MAAcD,EAAIE,OACTN,GAAA/B,MAAQmC,EAAIE,KAAKC,MAAA,GAEnC,CAIL,SAASC,KAED,MAAA5J,EACJjB,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWC,UAAYzF,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWsF,WAAa9K,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWuF,OAAS/K,GAAGsI,MAAM9D,cAAcvD,YAAcjB,GAAGsI,MAAMrI,MAAMiB,gBACtN4B,EAAe9C,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWsF,WAAa9K,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWuF,OAAS/K,GAAGsI,MAAM9D,cAAc1B,aAAe9C,GAAGsI,MAAMrI,MAAMkB,iBACxL,OAAO6J,EAAM/J,GAAagK,OAAO,eAAeC,OAAO,OAAOA,OAAOF,EAAMlI,GAAcmI,OAAO,eAAc,CAIhH,SAASE,KACP,GAAInL,GAAGsI,MAAMrI,MAAMc,aAAeqK,GAAYC,UACrC,OAAA,EAEH,MAAApK,EACJjB,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWC,UAAYzF,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWsF,WAAa9K,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWuF,OAAS/K,GAAGsI,MAAM9D,cAAcvD,YAAcjB,GAAGsI,MAAMrI,MAAMiB,gBACtN4B,EAAe9C,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWsF,WAAa9K,GAAGsI,MAAM9D,cAAchC,QAAUgD,GAAWuF,OAAS/K,GAAGsI,MAAM9D,cAAc1B,aAAe9C,GAAGsI,MAAMrI,MAAMkB,iBAElLmK,EAAcN,EAAM/J,GAAasK,QAAQ,OAEzCC,EADeR,EAAMlI,GAAcyI,QAAQ,OACnBE,KAAKH,EAAa,OAGzC,OAAa,IAAbE,EAAiB,EAAIA,CAAA,CAM9B,SAASE,GAAmBhK,GAC1B,MAAMiK,EAAS,IACVhO,MACC+D,GAAe,CAAEA,gBAEvB,OAAOkK,GAAUC,aAAaF,GAAQnB,MAAMC,IACzB,IAAbA,EAAIC,MAAcD,EAAIE,OACxBlM,GAAgB6J,MAAQmC,EAAIE,KAAA,GAE/B,CAMHhC,eAAeiB,KACDkC,GAAAC,iBAAiB,CAACC,GAAmBnP,GAAwBD,KAA2B4N,MAAMC,IAChG1E,GAAAuC,MAAQmC,EAAIE,KAAKsB,QAAQvD,GAAcA,EAAKwD,WAAaF,KACnD9F,GAAAoC,MAAQmC,EAAIE,KAAKsB,QAAQvD,GAAcA,EAAKwD,WAAatP,IAAwB,GAChG,CAGH+L,eAAekB,KACb,MAAMY,QAAY0B,GAAcrG,WAAWrJ,GAAUoB,MAAO,KAC5DiI,GAAWwC,MAAQmC,EAAIE,IAAA,CAsBzBhC,eAAeQ,eACP,MAAAsB,QAAY3B,GAASsD,cAAc,IACpCzO,GACH0C,QAASnE,GAAMmE,QAAQ,GACvBlB,aAAcjD,GAAMiD,eAEtBa,GAAGsI,MAAQmC,EAAIE,KACZ3K,GAAAsI,MAAMrI,MAAMC,SAAuC,IAA5BuK,EAAIE,KAAK1K,MAAMC,SAAiB,GAAKuK,EAAIE,KAAK1K,MAAMC,SAC9EvB,GAAW2J,MAAMzJ,MAAQmB,GAAGsI,MAAMrI,MAAMpB,MACxCF,GAAW2J,MAAM1J,aAAeoB,GAAGsI,MAAMrI,MAAMrB,aAC/CD,GAAW2J,MAAMxJ,UAAYkB,GAAGsI,MAAMrI,MAAMnB,UAC5CH,GAAW2J,MAAMvJ,aAAeiB,GAAGsI,MAAMrI,MAAMlB,aACxCQ,GAAA+I,MAAQmC,EAAIE,KAAK1K,MAAMV,QAG1B,OAAA8M,EAAGrM,GAAAsI,MAAM9D,oBAAT,EAAA6H,EAAwBhP,SAC1BmJ,GAAe8B,MAAMjL,MAAQ2C,GAAGsI,MAAM9D,cAAcnH,MACjD2C,GAAAsI,MAAM9D,cAAcnH,MAAQiP,GAAYtM,GAAGsI,MAAM9D,cAAcnH,MAAO,SACzE+I,GAAgBkC,MAAMjC,iBAAkB,IAGtC,OAAAkG,EAAGvM,GAAAsI,MAAM9D,oBAAT,EAAA+H,EAAwB1L,WAC1B2F,GAAe8B,MAAMzH,QAAUb,GAAGsI,MAAM9D,cAAc3D,QACnDb,GAAAsI,MAAM9D,cAAc3D,QAAUyL,GAAYtM,GAAGsI,MAAM9D,cAAc3D,QAAS,WAC7EuF,GAAgBkC,MAAM/B,mBAAoB,IAGxC,OAAAiG,EAAGxM,GAAAsI,MAAM9D,oBAAT,EAAAgI,EAAwB1H,QAC1B0B,GAAe8B,MAAMxD,KAAO9E,GAAGsI,MAAM9D,cAAcM,KAChD9E,GAAAsI,MAAM9D,cAAcM,KAAOwH,GAAYtM,GAAGsI,MAAM9D,cAAcM,KAAM,QACvEsB,GAAgBkC,MAAMhC,gBAAiB,GAGrCtG,GAAGsI,MAAMrI,MAAMpB,OACL4N,GAAAzM,GAAGsI,MAAMrI,MAAMpB,OArK7BiK,GACG4D,cAAc,IACV/O,GACH0C,QAASnE,GAAMmE,QAAQ,KAExBmK,MAAMC,IACL1D,GAAUuB,MAAQmC,EAAIE,KAAK9B,QAAU,GACrC,IAAI8D,EAAM,EAEV,IAAA,IAASC,EAAI,EAAGA,EAAI7F,GAAUuB,MAAMuE,OAAQD,IAChC,IAANA,GACMxD,GAAAd,MAAMwE,KAAK,GACbH,EAAA,GAGF5F,GAAUuB,MAAMsE,GAAG3E,aAAelB,GAAUuB,MAAMsE,EAAI,GAAG3E,aACnDmB,GAAAd,MAAMqE,IAAQ,EACdvD,GAAAd,MAAMwE,KAAK,GAEvB,IAsLKC,GAAAC,0BAA0B,CAAE7H,GAAIjJ,GAAMiD,eAAgBqL,MAAMC,IACpD,IAAbA,EAAIC,OACH1K,GAAAsI,MAAMzE,QAAU4G,EAAIE,KAAA,GAnChB,CAGb,SAASsC,KACPxP,GAAe6K,OAAQ,EAuBzBK,iBACE,MAAMgC,KAAEA,SAAeuC,GAAQC,WAAWxP,IAC1CD,GAAQ4K,MAAQqC,CAAA,CAxBLyC,EAAA,CAGb,SAASC,KACP,MAAM1B,EAAS,IACVhO,GACH0C,QAASL,GAAGsI,MAAMrI,MAAMI,QACxB8C,OAAQnD,GAAGsI,MAAMrI,MAAMkD,QAEzBmK,GAAUC,aAAa5B,GAAQnB,MAAMC,IAC/B,GAAa,IAAbA,EAAIC,KAAY,CAEZ,MAAA8C,EAAiB9P,GAAQ4K,MAAMmF,MAAM/E,GAASA,EAAKgF,WAAa1N,GAAGsI,MAAMrI,MAAMkD,SACjFqK,IACCxN,GAAAsI,MAAMrI,MAAMmD,WAAaoK,EAAeG,UAE7ClQ,GAAe6K,OAAQ,EACbsF,EAAAC,QAAQvR,GAAE,iBAAgB,IAEvC,CAiBH,SAASwR,GAAaC,GAChB/N,GAAGsI,MAAMrI,MAAMI,UACjBpB,GAAaqJ,MAAMpJ,eAAiBc,GAAGsI,MAAMrI,MAAMI,QACnDpB,GAAaqJ,MAAMnJ,aAAea,GAAGsI,MAAM9D,cAAcrF,cAE3DX,GAAQ8J,MAAQyF,EAChB/P,GAAoBsK,OAAQ,CAAA,CAG9B,SAAS0F,KACHhO,GAAGsI,MAAMrI,MAAMI,UACjBpB,GAAaqJ,MAAMpJ,eAAiBc,GAAGsI,MAAMrI,MAAMI,QACnDpB,GAAaqJ,MAAMnJ,aAAea,GAAGsI,MAAM9D,cAAcrF,cAE3Df,GAAyBkK,OAAQ,CAAA,CAGnC,SAAS2F,KACHjO,GAAGsI,MAAMrI,MAAMI,UACjBpB,GAAaqJ,MAAMpJ,eAAiBc,GAAGsI,MAAMrI,MAAMI,QACnDpB,GAAaqJ,MAAMnJ,aAAea,GAAGsI,MAAM9D,cAAcrF,cAE3Db,GAAkBgK,MAAQ,WAC1B/J,GAAyB+J,OAAQ,CAAA,CA5GnC4F,GACE,KAAO,CAAEC,SAAUjS,GAAMmE,YACzB,EAAG8N,eACchF,IAAA,IA4Gb,MAAAiF,GAAkBlR,GAAI,GAGtBmR,GAA2BnR,GAAI,GAGrC,SAASoR,KACPF,GAAgB9F,OAAQ,CAAA,CAI1B,SAASiG,KAEEzE,IAAA,CAgCL,MAAA0E,GAAuBtR,GAAI,GAEjC,SAASuR,KACHzO,GAAGsI,MAAMrI,MAAMI,UACjBpB,GAAaqJ,MAAMpJ,eAAiBc,GAAGsI,MAAMrI,MAAMI,SAErDmO,GAAqBlG,OAAQ,CAAA,CAIzB,MAAAoG,GAAqBxR,GAAI,GAE/B,SAASyR,KACP,MAAMhD,EAAS,IACVhO,GACH0C,QAASL,GAAGsI,MAAMrI,MAAMI,SAE1ByI,GAAS8F,cAAcjD,GAAQnB,MAAMC,IAClB,IAAbA,EAAIC,MACIkD,EAAAC,QAAQvR,GAAE,sBACPE,KACbkS,GAAmBpG,OAAQ,GAEjBsF,EAAAiB,MAAMpE,EAAIqE,IAAG,GAE1B,CAGH,MAAMC,GAAiB7R,EAAI,CACzBgM,SAAS,EACT9H,OAAQ,GACRf,QAAS,GACT0B,UAAW,QACXiN,iBAAkB,CAChB,CACEpH,MAAO,EACPxD,MAAO,EACPC,UAAW,GACX9B,kBAAmB,GACnB0M,UAAW,EACXzL,SAAU,EACVc,KAAM,MAKN4K,GAAehS,EAAI,CACvBmD,QAAS,GACTkB,IAAK,GACLD,MAAO,GACPD,OAAQ,GACRmC,SAAU,EACVtC,gBAAiB,GACjBC,iBAAkB,GAClBJ,YAAa,GACbb,SAAU,GACV2B,iBAAkB,GAClBjD,aAAc,GACd8B,YAAa,GACbE,YAAa,GACbT,UAAW,KAGb,SAASgP,GAAU7G,GACjB,GAAc,iBAAVA,EACoC,MAAlCtI,GAAGsI,MAAMrI,MAAMgC,eAEImN,KAGXxB,EAAA,CACRyB,QAAS5H,EAAE,MAAO,CAAEI,MAAO,wDAA0D,CACnFJ,EAAE,OAAQ,KAAM,GAAGnL,GAAE,8BACrBmL,EACE,OACA,CACEI,MAAO,oDACPyH,QAAS,IAAMtB,MAEjB,MAEFvG,EAAE,OAAQ,KAAM,KAChBA,EACE,OACA,CACEI,MAAO,sEACPyH,QAAS,IAAMF,MAEjB,UAGJ/P,KAAM,UACNkQ,WAAW,EACXC,SAAU,WAEd,GACmB,gBAAVlH,EACT,GAAsC,MAAlCtI,GAAGsI,MAAMrI,MAAMgC,eAAwB,CACzC,MAAM0J,EAAS,IACVhO,GACH0C,QAASL,GAAGsI,MAAMrI,MAAMI,SAE1ByI,GAAS2G,eAAe9D,GAAQnB,MAAMC,IACnB,IAAbA,EAAIC,MACNqE,GAAezG,MAAMlH,OAASpB,GAAGsI,MAAMrI,MAAMmB,OAC9B2N,GAAAzG,MAAMjI,QAAUoK,EAAIE,KAAKtK,QACzB0O,GAAAzG,MAAM0G,iBAAmBvE,EAAIE,KAAK+E,UACjDX,GAAezG,MAAMY,SAAU,GAErB0E,EAAAiB,MAAMpE,EAAIqE,IAAG,GAE1B,MAESlB,EAAAiB,MAAMvS,GAAE,8BAEtB,CAIF,SAAS8S,KACP/Q,GAAqBiK,OAAQ,EAC7B4G,GAAa5G,MAAMjI,QAAUL,GAAGsI,MAAMrI,MAAMI,QAC5C6O,GAAa5G,MAAM/G,IAAMvB,GAAGsI,MAAMrI,MAAMsB,IACxC2N,GAAa5G,MAAMhH,MAAQtB,GAAGsI,MAAMrI,MAAMqB,MAC1C4N,GAAa5G,MAAMjH,OAASrB,GAAGsI,MAAMrI,MAAMoB,OAC3C6N,GAAa5G,MAAM9E,SAAWxD,GAAGsI,MAAMnE,WAAWX,SAClD0L,GAAa5G,MAAMzG,iBAAmB7B,GAAGsI,MAAMrI,MAAM4B,iBACrDqN,GAAa5G,MAAM1J,aAAeoB,GAAGsI,MAAMrI,MAAMrB,aACjDsQ,GAAa5G,MAAMnI,UAAYH,GAAGsI,MAAMrI,MAAME,UAC9C+O,GAAa5G,MAAM5H,YAAcV,GAAGsI,MAAMrI,MAAMS,YAChDwO,GAAa5G,MAAM1H,YAAcZ,GAAGsI,MAAMrI,MAAMW,YAChDsO,GAAa5G,MAAMvH,YAAcf,GAAGsI,MAAMrI,MAAMc,YACnCmO,GAAA5G,MAAMpI,SAAWF,GAAGsI,MAAMrI,MAAMc,cAAgBqK,GAAYC,UAAYrL,GAAGsI,MAAMrI,MAAMC,SAAW,GAClGgP,GAAA5G,MAAMpH,gBAAkB8J,EAAMhL,GAAGsI,MAAMrI,MAAMiB,iBAAiB+J,OAAO,oBACrEiE,GAAA5G,MAAMnH,iBAAmB6J,EAAMhL,GAAGsI,MAAMrI,MAAMkB,kBAAkB8J,OAAO,mBAAkB,CAyCxG,SAAS0E,KACPpS,GAAmB+K,OAAQ,EAtC3BwD,GAAYC,iBAAiBpP,IAAW6N,MAAMC,IAC5B7K,GAAA0I,MAAQmC,EAAIE,KAAKsB,QAAQvD,GAAcA,EAAKwD,WAAarP,KACzE+C,GAAgB0I,MAAQ1I,GAAgB0I,MAAM2D,QAAQvD,GAAcA,EAAKgC,OAASU,GAAYwE,cAAgBlH,EAAKgC,OAASU,GAAYyE,gBAC5HlQ,GAAA2I,MAAQmC,EAAIE,KAAKsB,QAAQvD,GAAcA,EAAKwD,WAAatP,KACzD+C,GAAA2I,MAAQ3I,GAAY2I,MAAM2D,QAAQvD,GAA4B,MAAdA,EAAKgC,MAA8B,MAAdhC,EAAKgC,OACzE7K,GAAAyI,MAAQmC,EAAIE,KAAKsB,QAAQvD,GAAcA,EAAKwD,WAAapP,KAC9DgD,GAAAwI,MAAQmC,EAAIE,KAAKsB,QAAQvD,GAAcA,EAAKwD,WAAanP,KAC7DgD,GAAAuI,MAAQmC,EAAIE,KAAKsB,QAAQvD,GAAcA,EAAKwD,WAAalP,IAAwB,IAKvF8S,GACGC,WAAW,CACVlS,MAAOpB,GAAUoB,MACjBmS,YAAavT,GAAUqB,MACvBmS,OAAQ,IACRC,SAAU9K,GAAYC,MAEvBmF,MAAMC,IACLhL,GAAO6I,MAAQmC,EAAIE,IAAA,IAKvBmF,GACGC,WAAW,CACVlS,MAAOpB,GAAUoB,MACjBmS,YAAavT,GAAUqB,MACvBmS,OAAQ,IACRC,SAAU9K,GAAYC,MAEvBmF,MAAMC,IACL/K,GAAU4I,MAAQmC,EAAIE,IAAA,GAQb,CAIf,SAASwF,KACP,MAAMxE,EAAS,IACVhO,GACH0C,QAASL,GAAGsI,MAAMrI,MAAMI,QACxBd,OAAQS,GAAGsI,MAAMrI,MAAMV,QAEzB+N,GAAU9P,aAAamO,GAAQnB,MAAMC,IAClB,IAAbA,EAAIC,OACNlN,GAAa8K,OAAQ,EACXsF,EAAAC,QAAQvR,GAAE,kBAAgB,GAEvC,CAGH,SAAS8T,KACP,MAAMzE,EAAS,IACVhO,GACH0C,QAASL,GAAGsI,MAAMrI,MAAMI,QACxBC,WAAYN,GAAGsI,MAAMrI,MAAMK,YAE7BgN,GAAUvP,iBAAiB4N,GAAQnB,MAAMC,IACtB,IAAbA,EAAIC,OACN3M,GAAiBuK,OAAQ,EACfsF,EAAAC,QAAQvR,GAAE,kBAAgB,GAEvC,CAGH,SAAS+T,KACP/R,GAAkBgK,MAAQ,YAC1B/J,GAAyB+J,OAAQ,CAAA,CAInC,SAASgI,GAAkBC,GACrBA,GACF5R,GAAW2J,MAAMtJ,SAAU,EAC3BwR,YAAW,KACT7R,GAAW2J,MAAMtJ,SAAU,EAC3B,MAAM2M,EAAS,IACVhO,GACH6E,MAAO4C,GAAYC,IACnBoL,OAAQ,EACRC,SAAU,GACVrT,MAAOkT,GAETjD,GAAUqD,SAAShF,GAAQnB,MAAMC,IACvB/L,GAAA4J,MAAQmC,EAAIE,KAAKiG,IAAA,GAC1B,GACA,MAEHlS,GAAQ4J,MAAQ,EAClB,CAGF,SAASmE,GAAYnE,GAChBtI,GAAAsI,MAAMrI,MAAMpB,MAAQyJ,EACvB,MAAMqD,EAAS,IACVhO,GACHkB,MAAOmB,GAAGsI,MAAMrI,MAAMpB,OAExByO,GAAUuD,OAAOlF,GAAQnB,MAAMC,IACZ,IAAbA,EAAIC,OACNzN,GAAiBqL,MAAQmC,EAAIE,KAAA,GAEhC,CAIH,SAASmG,KACH1K,GAAgBkC,MAAMjC,iBAExBrG,GAAGsI,MAAM9D,cAAcnH,MAAQiP,GAAY9F,GAAe8B,MAAMjL,MAAO,SACvE+I,GAAgBkC,MAAMjC,iBAAkB,IAGxCrG,GAAGsI,MAAM9D,cAAcnH,MAAQmJ,GAAe8B,MAAMjL,MACpD+I,GAAgBkC,MAAMjC,iBAAkB,EAC1C,CAIF,SAAS0K,KACH3K,GAAgBkC,MAAMhC,gBAExBtG,GAAGsI,MAAM9D,cAAcM,KAAOwH,GAAY9F,GAAe8B,MAAMxD,KAAM,QACrEsB,GAAgBkC,MAAMhC,gBAAiB,IAGvCtG,GAAGsI,MAAM9D,cAAcM,KAAO0B,GAAe8B,MAAMxD,KACnDsB,GAAgBkC,MAAMhC,gBAAiB,EACzC,CAIF,SAAS0K,KACH5K,GAAgBkC,MAAM/B,mBAExBvG,GAAGsI,MAAM9D,cAAc3D,QAAUyL,GAAY9F,GAAe8B,MAAMzH,QAAS,WAC3EuF,GAAgBkC,MAAM/B,mBAAoB,IAG1CvG,GAAGsI,MAAM9D,cAAc3D,QAAU2F,GAAe8B,MAAMzH,QACtDuF,GAAgBkC,MAAM/B,mBAAoB,EAC5C,CAGF,SAAS0K,KACD,MAAAhR,EAAQD,GAAGsI,MAAMrI,MACjB0L,EAAc,IACfhO,GACHiB,aAAcqB,EAAMrB,aACpByB,QAASJ,EAAMI,SAEX6Q,EAAe,CACnBC,OAAQ,QACRC,MAAO,YACPC,SAAU,gBAER,GAAApR,EAAMrB,eAAiB0S,GAAaC,QACtC5F,EAAOxL,UAAY,OACd,CACL,MAAMA,EAAYF,EAAMiR,EAAajR,EAAMrB,eAC3C,IAAKuB,EAAW,CACd,MAAMqR,EAAe,CACnBL,OAAQ7U,GAAE,6BACV8U,MAAO9U,GAAE,eACT+U,SAAU/U,GAAE,kBAGd,YADAsR,EAAUiB,MAAM2C,EAAavR,EAAMrB,cACnC,CAGF+M,EAAOxL,UAAYA,CAAA,CAGrB2I,GACGlK,aAAa+M,GACbnB,MAAMC,IACY,IAAbA,EAAIC,MACIkD,EAAAC,QAAQvR,GAAE,4BACpBiB,GAAmB+K,OAAQ,EACd9L,MAEHoR,EAAAiB,MAAMpE,EAAIqE,IAAG,IAG1B2C,OAAM,KACK7D,EAAAiB,MAAMvS,GAAE,eAAc,GACjC,CAIL,SAASoV,KACP/S,GAAW2J,MAAMjL,MAAQ,GACzB2C,GAAGsI,MAAMrI,MAAMpB,MAAQF,GAAW2J,MAAMzJ,MACxCmB,GAAGsI,MAAMrI,MAAMrB,aAAeD,GAAW2J,MAAM1J,aAC/CoB,GAAGsI,MAAMrI,MAAMnB,UAAYH,GAAW2J,MAAMxJ,UAC5CkB,GAAGsI,MAAMrI,MAAMlB,aAAeJ,GAAW2J,MAAMvJ,aAC/CxB,GAAmB+K,OAAQ,EAC3B9K,GAAa8K,OAAQ,CAAA,CAIvB,SAASqJ,KACPC,EAAaC,QAAQ,WAAY,CAC/BC,kBAAmBxV,GAAE,WACrByV,iBAAkBzV,GAAE,UACpB+C,KAAM,YAELmL,MAAK,KACJ1B,GACGkJ,UAAU,IACNrU,GACHwB,aAAca,GAAGsI,MAAM9D,cAAcrF,eAEtCqL,MAAMC,IACY,IAAbA,EAAIC,OACIkD,EAAAC,QAAQvR,GAAE,mBACPE,KAAA,GAEhB,IAEJiV,OAAM,QAAQ,CAInB,SAASQ,KACP,QAA0C,IAA9BlI,OAAeC,iBAAkF,IAA5CD,OAAeE,yBAA0C,CAElH,MAAAiI,EAAe,wHAA6E5V,GAAE,yBAO7F,OANGsR,EAAA,CACR2B,WAAW,EACXF,QAAS/S,GAAE,sBAAuB,CAAE4V,iBACpC7S,KAAM,UACN8S,0BAA0B,KAErB,CAAA,CAEF,OAAA,CAAA,CAcTxJ,eAAeyJ,GAAcC,WACvB,IAACJ,KACH,OAGF,IAAKjS,GAAGsI,MAAMrI,MAAMyB,YAOlB,YANUkM,EAAA,CACR2B,WAAW,EACXF,QAAS/S,GAAE,wBACX+C,KAAM,UACN8S,0BAA0B,IAQ1B,SAHEzG,GAAmB1L,GAAGsI,MAAMrI,MAAMyB,cAGnCjD,GAAgB6J,MAOnB,YANUsF,EAAA,CACR2B,WAAW,EACXF,QAAS/S,GAAE,wBACX+C,KAAM,UACN8S,0BAA0B,IAK1BjI,GAAWoI,cACbpI,GAAWC,oBAEL,IAAIT,SAAS6I,GAAY/B,WAAW+B,EAAS,QAE1CrI,GAAAsI,gBAAgBnD,UACnB,MAAA1E,EAAO8H,KAAKC,MAAMrD,GACpB1E,IAAyB,IAAjBA,EAAKgI,SAAoBhI,EAAKiI,SAAWC,GAAiBC,gBAChEnI,EAAKoI,UACQC,EAAA,CACbC,MAAO,OACPd,0BAA0B,EAC1B9C,QAAS6D,GAAqBvI,GAC9BwI,SAAU,cACV9T,KAAM,YAGAuO,EAAA,CACRyB,QAAS/S,GAAE,oBAAqB,CAAE8W,OAAQpT,GAAGsI,MAAMrI,MAAMsB,MACzDlC,KAAM,YAGRgU,GACGC,kBAAkB,CACjBzV,MAAOpB,GAAUoB,MACjBC,MAAOrB,GAAUqB,MACjBuC,QAASL,GAAGsI,MAAMrI,MAAMI,QACxBlD,KAAM,OAAAkP,EAAArM,GAAGsI,MAAM9D,sBAAT6H,EAAwBlP,KAC9BoE,IAAKvB,GAAGsI,MAAMrI,MAAMsB,IACpBlC,KAAMgT,EAAU,IAAM,IACtBkB,WAAYvI,EAAqC,IAA/BwI,OAAO7I,EAAKoI,SAASU,SAAgBxI,OAAO,yBAE/DT,MAAMC,IACY,IAAbA,EAAIC,MACNgJ,QAAQC,IAAI,WAAU,IAGzBlC,OAAO5C,IAEE6E,QAAA7E,MAAM,YAAaA,EAAK,KAGlClE,IAAyB,IAAjBA,EAAKgI,SAAqBhI,EAAKiI,SAAWC,GAAiBC,eACjEnI,EAAKmE,KACGlB,EAAA,CACRyB,QAAS1E,EAAKmE,IACdzP,KAAM,SAEV,IAIE,MAAAuU,QAzFCzH,GAAcrG,WAAWrJ,GAAUoB,MAAO,KAAK2M,MAAMC,GACzC,IAAbA,EAAIC,KACCD,EAAIE,KAEN,KAsFT,IAAKiJ,EAEH,YADAF,QAAQ7E,MAAM,6BAIV,MAAAgF,EAAqBC,KAAKC,MAAMP,OAAOQ,SAASJ,EAAe,IAAM,KAErEK,EAAO,CACXrB,OAAQC,GAAiBC,cACzBoB,QAASzV,GAAgB6J,MAAM6L,QAC/BpB,SAAU,CACRK,OAAQpT,GAAGsI,MAAMrI,MAAMsB,IACvB6S,QAASP,EACTJ,OAAQK,KAAKC,MAAM/I,EAAMhL,GAAGsI,MAAMrI,MAAMkB,kBAAkBkT,UAAY,KACtEC,cAAc,OAAAjI,EAAA5N,GAAgB6J,YAAhB,EAAA+D,EAAuBiI,gBAAiBlP,GAAYC,IAClEkP,YAAalC,EACbmC,WAAW,OAAAjI,EAAA9N,GAAgB6J,YAAhB,EAAAiE,EAAuBiI,aAAcpP,GAAYC,IAC5D7D,OAAQxB,GAAGsI,MAAMrI,MAAMuB,OACvBC,IAAKzB,GAAGsI,MAAMrI,MAAMwB,IACpBE,QAAS3B,GAAGsI,MAAMrI,MAAM0B,QACxBC,QAAS5B,GAAGsI,MAAMrI,MAAM2B,UAKxBnD,GAAgB6J,MAAMmM,MAAQhW,GAAgB6J,MAAMmM,KAAK5H,OAAS,GAEpEpO,GAAgB6J,MAAMmM,KAAKC,SAASC,IAClCV,EAAKlB,SAAS4B,EAAMC,eAAiBD,EAAME,gBAAA,IAKzC,MAAAC,EAAarC,KAAKsC,UAAUd,GAC1BP,QAAAC,IAAI,aAAcmB,GACpB,MAAAE,EAAQC,aAAY,KACpB/K,GAAWoI,cACbpI,GAAWgL,eAAeJ,GAC1BK,cAAcH,GAAK,GAEpB,IAAG,CA+FF,MAAAI,GAAqBlY,GAAI,GAKzB,MAAAmY,GAAgBnY,GAAI,GACpBoY,GAAepY,EAAI,IACnBqY,GAAarY,GAAI,GACvB,SAASsY,KACMF,GAAAhN,MAAQtI,GAAGsI,MAAM9D,cAAckB,WAC5C6P,GAAWjN,OAAQ,EACnB+M,GAAc/M,OAAQ,CAAA,CAExB,SAASmN,KACPH,GAAahN,MAAQ0C,IAAQC,OAAO,uBACpCsK,GAAWjN,OAAQ,EACnB+M,GAAc/M,OAAQ,CAAA,CAExB,SAASoN,GAAchQ,GACrB,MAAMiG,EAAc,CAClB9N,MAAOpB,GAAUoB,MACjBC,MAAOrB,GAAUqB,MACjBuC,QAASL,GAAGsI,MAAMrI,MAAMI,QACxBlB,aAAca,GAAGsI,MAAM9D,cAAcrF,cAInCuG,IACFiG,EAAOjG,WAAaA,GAEtBoD,GAAS6M,kBAAkBhK,GAAQnB,MAAMC,IACtB,IAAbA,EAAIC,OACN2K,GAAc/M,OAAQ,EAClB5C,GACC1F,GAAAsI,MAAM9D,cAAckB,WAAa4P,GAAahN,MACjDsF,EAAUC,QAAQ0H,GAAWjN,MAAQ,SAAW,YAEhDsF,EAAUC,QAAQ,YACf7N,GAAAsI,MAAM9D,cAAckB,WAAa,IACtC,GAEH,CAGH,SAASkQ,KACPF,GAAc,GAAE,CAIlB,SAASG,GAAaC,GAEpB,MAAMC,EAAgBD,EAAKE,UAAYC,KAAKC,MAAQ,MAE9CC,EAAkBL,EAAKE,UAAYhL,EAAMhL,GAAGsI,MAAM9D,cAAcrD,kBAAkBkT,UACxF,OAAO0B,GAAiBI,CAAA,CAI1B,SAASC,KACP,MAAMC,EAAQ,GACRC,EAAetL,EAAMsK,GAAahN,OAClC4N,EAAMlL,IACNlI,EAAekI,EAAMhL,GAAGsI,MAAM9D,cAAcrD,kBAGlD,GAAImV,EAAaC,OAAOL,EAAK,OAAQ,CAC7B,MAAAM,EAAcN,EAAIO,OACxB,IAAA,IAAS7J,EAAI,EAAGA,EAAI4J,EAAa5J,IAC/ByJ,EAAMvJ,KAAKF,EACb,CAIF,GAAI0J,EAAaC,OAAOzT,EAAc,OAAQ,CAE5C,IAAA,IAAS8J,EADY9J,EAAa2T,OACN,EAAG7J,GAAK,GAAIA,IACtCyJ,EAAMvJ,KAAKF,EACb,CAGK,OAAAyJ,CAAA,CAcT,SAASK,MAVT,WACQ,MAAAC,EAAa3L,EAAMsK,GAAahN,OAChCxF,EAAekI,EAAMhL,GAAGsI,MAAM9D,cAAcrD,kBAC9C,OAAAwV,EAAWC,QAAQ9T,KACrB8K,EAAUiJ,QAAQ,iBACX,EAEF,EAIHC,IACFpB,GAAcJ,GAAahN,MAC7B,CAGFK,eAAeoO,KACb,MAAMpM,KAAEA,SAAe7B,GAASkO,eAAe,IAAKrZ,GAAa0C,QAASL,GAAGsI,MAAMrI,MAAMI,UACzF,IAAI4W,EAAW,GACfjR,GAAasC,MAAQqC,EAAKlC,KAAKC,IACzBA,EAAKuO,SAASpK,OAAS,IACzBoK,EAAWvO,EAAKuO,UAEXvO,KAETzC,GAASqC,MAAQ2O,EACjB3X,GAAUgJ,OAAQ,CAAA,CAGpB,SAAS4O,KACP,MAAMvL,EAA6B,IAC9BhO,GACH0C,QAASL,GAAGsI,MAAMrI,MAAMI,QACxBU,YAAaf,GAAGsI,MAAMrI,MAAMc,YAC5Bb,SAAU,IAGRF,GAAGsI,MAAMrI,MAAMc,aAAeqK,GAAYC,YACrCM,EAAAzL,SAAWF,GAAGsI,MAAMrI,MAAMC,UAEnC4I,GAASqO,kBAAkBxL,GAAQnB,MAAMC,IACxBtB,KACf7J,GAAUgJ,OAAQ,EACRsF,EAAAC,QAAQvR,GAAE,iBAAgB,GACrC,CAIG,MAAA8a,GAAqBC,GAAS,WAE5B,MAAAvS,EAAOsB,GAAgBkC,MAAMhC,eAAiBE,GAAe8B,MAAMxD,KAAO,OAAAuH,EAAArM,GAAGsI,MAAM9D,oBAAe,EAAA6H,EAAAvH,KAExG,GAAIA,EAAM,CAER,MAAMwS,EAAWlR,GAAgBkC,MAAMhC,eAAiBxB,EAAO0B,GAAe8B,MAAMxD,KAC7E,OAAAyS,GAAsBD,IAAa,GAAA,CAGrC,MAAA,GAAA,0hDAzOT3O,iBACM,IAACsJ,KACH,OAGF,IAAKjS,GAAGsI,MAAMrI,MAAMyB,YAOlB,YANUkM,EAAA,CACR2B,WAAW,EACXF,QAAS/S,GAAE,wBACX+C,KAAM,UACN8S,0BAA0B,IAQ1B,SAHEzG,GAAmB1L,GAAGsI,MAAMrI,MAAMyB,cAGnCjD,GAAgB6J,MAOnB,YANUsF,EAAA,CACR2B,WAAW,EACXF,QAAS/S,GAAE,wBACX+C,KAAM,UACN8S,0BAA0B,IAK1BjI,GAAWoI,cACbpI,GAAWC,oBAEL,IAAIT,SAAS6I,GAAY/B,WAAW+B,EAAS,QAE1CrI,GAAAsI,gBAAgBnD,UACnB,MAAA1E,EAAO8H,KAAKC,MAAMrD,GACpB1E,IAAyB,IAAjBA,EAAKgI,SAAoBhI,EAAKiI,SAAWC,GAAiB2E,iBAC1D5J,EAAA,CACRyB,QAAS/S,GAAE,uBACX+C,KAAM,YAGRgU,GACGC,kBAAkB,CACjBzV,MAAOpB,GAAUoB,MACjBC,MAAOrB,GAAUqB,MACjBuC,QAASL,GAAGsI,MAAMrI,MAAMI,QACxBlD,KAAM,OAAAkP,EAAArM,GAAGsI,MAAM9D,oBAAe,EAAA6H,EAAAlP,KAC9BoE,IAAKvB,GAAGsI,MAAMrI,MAAMsB,IACpBlC,KAAM,MAEPmL,MAAMC,IACY,IAAbA,EAAIC,MACNgJ,QAAQC,IAAI,WAAU,IAGzBlC,OAAO5C,IAEE6E,QAAA7E,MAAM,YAAaA,EAAK,IACjC,IAGP,MAAMoF,EAAO,CACXrB,OAAQC,GAAiB2E,eACzBtD,QAASzV,GAAgB6J,MAAM6L,SAGjC,GAAI1V,GAAgB6J,MAAMmM,MAAQhW,GAAgB6J,MAAMmM,KAAK5H,OAAS,EAAG,CAEvE,MAAMkG,EAAW,CACfvR,OAAQxB,GAAGsI,MAAMrI,MAAMuB,OACvBC,IAAKzB,GAAGsI,MAAMrI,MAAMwB,KAEtBhD,GAAgB6J,MAAMmM,KAAKC,SAASC,IACzB5B,EAAA4B,EAAMC,eAAiBD,EAAME,gBAAA,IAIxCZ,EAAKlB,SAAWA,CAAA,CAGZ,MAAA+B,EAAarC,KAAKsC,UAAUd,GAC1BP,QAAAC,IAAI,aAAcmB,GACpB,MAAAE,EAAQC,aAAY,KACpB/K,GAAWoI,cACbpI,GAAWgL,eAAeJ,GAC1BK,cAAcH,GAAK,GAEpB,IAAG,0FAMNI,GAAmB9M,OAAQ,kkCAjoBvB+B,GAAe/B,MAAQ,EACzBsJ,EAAaC,QAAQ,UAAUxH,GAAe/B,6BAA8B,SAAU,CACpFwJ,kBAAmB,OACnBC,iBAAkB,KAClB1S,KAAM,UACN8S,0BAA0B,EAC1B9C,QAAS,+EAA+EhF,GAAe/B,4GAEtGkC,MAAK,KAEJtM,GAAuBoK,OAAQ,CAAA,IAEhCmJ,OAAM,KAEL7D,EAAU6J,KAAK,YAAW,IAI9BvZ,GAAuBoK,OAAQ,g/GA1VjCnC,GAAcmC,MAAMoP,OACpBhR,GAAY4B,MAAQ,GACpB7B,GAAW6B,MAAQ,EACRtB,GAAAsB,MAAQE,EAASzB,GAAUuB,OAC3BrB,GAAAqB,MAAQE,EAASzB,GAAUuB,YAC3BrB,GAAAqB,MAAMG,KAAKC,IACpBA,EAAKtI,SAAW,EACTsI,m7eA1BLhC,GAAY4B,OACHtB,GAAAsB,MAAMG,KAAKC,IACfA,EAAAd,MAAQ4L,OAAO9M,GAAY4B,OACzBI"}