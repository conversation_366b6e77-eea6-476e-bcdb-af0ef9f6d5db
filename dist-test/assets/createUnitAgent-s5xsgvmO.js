import{d as e,aj as t,ai as l,b as a,av as i,y as d,aR as s,aq as o,u as n,o as r,c as u,f as m,w as c,h as b,Y as p,e as f,F as g,ag as v,R as _,g as h,E as y,l as V,m as S,b1 as x,j as P,k as L,b2 as N,b5 as D,aS as E,bK as U,b7 as C,s as T,b8 as A,x as j,aT as k}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css               *//* empty css               *//* empty css                  *//* empty css               */import"./el-form-item-l0sNRNKZ.js";/* empty css                       *//* empty css                 */import{c as w}from"./channel.api-CM6FWEgD.js";import{g as R}from"./generalConfig.api-CEBBd8kx.js";import{c as O}from"./customer.api-dB3m63zZ.js";import{m as M}from"./merchant.api-BtmIsRm3.js";import{u as H}from"./user.api-BYl7ypOS.js";import{B}from"./constants-Cg3j_uH4.js";import{D as F}from"./DictTypeEnum-DKIIlHnN.js";import{y as z}from"./timeutils-Ib6GkGcq.js";import{_ as I}from"./_plugin-vue_export-helper-BCo6x5W8.js";const Q={style:{height:"650px"}},G={key:0},Y={style:{margin:"0 5px"}},q=e({__name:"createUnitAgent",props:{paCode:{default:""},handle:{},isEdit:{type:Boolean}},setup(e,{expose:I}){const{t:q}=t(),K=l(),J=a(!1),X=a(),Z=a({gcode:K.gcode,belongHcode:"",belongHname:"",paCode:"",paName:"",shortName:"",legalPerson:"",telephone:"",address:"",paType:"1",channelType:"ZJ",channel:"",isShare:"0",contact:"",phone:"",email:"",seller:"",startDate:(new Date).toString(),endDate:i().add(1,"year").toString(),commissionStrategy:"",isEnable:"1",isHidePrice:B.NO,isCredit:B.NO,sellLevel:"",commissionLevel:"",remark:"",arSet:{id:"",gcode:K.gcode,arSetCode:"",arSetName:"",unitCode:"",unitName:"",creditAccName:"",creditAccType:"0",creditPayDays:"1",creditPayFix:0,creditQuotaType:B.NO,creditQuota:0,creditValidType:B.NO,creditStartDate:"",creditEndDate:"",creditMinusAcc:B.NO,isEnable:B.YES,arSetMerchants:[],isManual:""}}),W=a([]),$=a([]),ee=a({paName:[{required:!0,message:q("pleaseEnterName"),trigger:"blur"}],sellLevel:[{required:!0,message:q("pleaseSelectCustomerLevel"),trigger:"blur"}]});d((()=>{!function(){const e={gcode:K.gcode,isEnable:B.YES,isG:B.YES};w.getChannelSimpleList(e).then((e=>{0===e.code&&(W.value=e.data)}))}(),M.getSimpleUnitList(K.gcode,K.hcode).then((e=>{0===e.code&&($.value=e.data)})),H.listSeller({gcode:K.gcode}).then((e=>{de.value=e.data})),ae(),le.value=F.BROKERAGE_LEVEL,R.list({gcode:K.gcode,type:le.value}).then((e=>{ie.value=e.data}))}));const te=a([]),le=a("");function ae(){le.value="0"===Z.value.paType?F.PROTOCOL_LEVEL:F.AGENT_LEVEL,R.list({gcode:K.gcode,type:le.value}).then((e=>{te.value=e.data}))}const ie=a([]);const de=a([]);function se(){Z.value.sellLevel="",ae()}return I({submit:()=>new Promise((e=>{X.value&&X.value.validate((t=>{t&&(Z.value.startDate=z(Z.value.startDate),Z.value.endDate=z(Z.value.endDate),Z.value.arSet.creditStartDate=Z.value.arSet.creditStartDate?z(Z.value.arSet.creditStartDate):"",Z.value.arSet.creditEndDate=Z.value.arSet.creditEndDate?z(Z.value.arSet.creditEndDate):"",O.create(Z.value).then((t=>{0===t.code?(s.success({message:q("addedSuccessfully"),center:!0}),e()):s.error({message:t.msg,center:!0})})))}))}))}),(e,t)=>{const l=y,a=V,i=S,d=x,s=P,w=L,R=N,O=D,M=E,H=U,B=C,F=T,z=A,I=j,K=k;return o((r(),u("div",Q,[m(I,{ref_key:"formRef",ref:X,model:n(Z),rules:n(ee),"label-width":"180px","label-suffix":"："},{default:c((()=>[m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("type")},{default:c((()=>[m(a,{modelValue:n(Z).paType,"onUpdate:modelValue":t[0]||(t[0]=e=>n(Z).paType=e),onChange:se},{default:c((()=>[m(l,{value:"0"},{default:c((()=>[b(p(n(q)("agreementUnit")),1)])),_:1}),m(l,{value:"1"},{default:c((()=>[b(p(n(q)("agency")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>["1"===n(Z).paType?(r(),f(i,{key:0,label:n(q)("channel")},{default:c((()=>[m(w,{modelValue:n(Z).channel,"onUpdate:modelValue":t[1]||(t[1]=e=>n(Z).channel=e),placeholder:n(q)("selectChannel")},{default:c((()=>[(r(!0),u(g,null,v(n(W),(e=>(r(),f(s,{key:e.channelCode,label:e.channelName,value:e.channelCode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])):_("",!0)])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("ownershipType")},{default:c((()=>[m(a,{modelValue:n(Z).isShare,"onUpdate:modelValue":t[2]||(t[2]=e=>n(Z).isShare=e)},{default:c((()=>[m(l,{value:"0"},{default:c((()=>[b(p(n(q)("singleStoreApplicable")),1)])),_:1}),m(l,{value:"1"},{default:c((()=>[b(p(n(q)("groupShare")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("belongingStore")},{default:c((()=>[m(w,{modelValue:n(Z).belongHcode,"onUpdate:modelValue":t[3]||(t[3]=e=>n(Z).belongHcode=e),placeholder:n(q)("selectStore")},{default:c((()=>[(r(!0),u(g,null,v(n($),(e=>(r(),f(s,{key:e.hcode,label:e.hname,value:e.hcode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("allowCredit")},{default:c((()=>[m(O,{modelValue:n(Z).isCredit,"onUpdate:modelValue":t[4]||(t[4]=e=>n(Z).isCredit=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(q)("yes"),"inactive-text":n(q)("no")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("hideRoomPriceOnRegistration")},{default:c((()=>[m(O,{modelValue:n(Z).isHidePrice,"onUpdate:modelValue":t[5]||(t[5]=e=>n(Z).isHidePrice=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(q)("yes"),"inactive-text":n(q)("no")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,null,{default:c((()=>[m(d,{md:24},{default:c((()=>[m(z,{class:"demo-tabs","model-value":"first"},{default:c((()=>[m(B,{label:n(q)("companyInformation"),name:"first"},{default:c((()=>[m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("name"),prop:"paName"},{default:c((()=>[m(M,{modelValue:n(Z).paName,"onUpdate:modelValue":t[6]||(t[6]=e=>n(Z).paName=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("shortName")},{default:c((()=>[m(M,{modelValue:n(Z).shortName,"onUpdate:modelValue":t[7]||(t[7]=e=>n(Z).shortName=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("legalPerson")},{default:c((()=>[m(M,{modelValue:n(Z).legalPerson,"onUpdate:modelValue":t[8]||(t[8]=e=>n(Z).legalPerson=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("telephone")},{default:c((()=>[m(M,{modelValue:n(Z).telephone,"onUpdate:modelValue":t[9]||(t[9]=e=>n(Z).telephone=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:24},{default:c((()=>[m(i,{label:n(q)("address")},{default:c((()=>[m(M,{modelValue:n(Z).address,"onUpdate:modelValue":t[10]||(t[10]=e=>n(Z).address=e),maxlength:"255"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("startDate")},{default:c((()=>[m(H,{modelValue:n(Z).startDate,"onUpdate:modelValue":t[11]||(t[11]=e=>n(Z).startDate=e),type:"date",placeholder:n(q)("selectDate")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("endDate")},{default:c((()=>[m(H,{modelValue:n(Z).endDate,"onUpdate:modelValue":t[12]||(t[12]=e=>n(Z).endDate=e),type:"date",placeholder:n(q)("selectDate")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("salesperson")},{default:c((()=>[m(w,{modelValue:n(Z).seller,"onUpdate:modelValue":t[13]||(t[13]=e=>n(Z).seller=e)},{default:c((()=>[(r(!0),u(g,null,v(n(de),(e=>(r(),f(s,{key:e.username,label:e.nickname,value:e.username},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("status")},{default:c((()=>[m(O,{modelValue:n(Z).isEnable,"onUpdate:modelValue":t[14]||(t[14]=e=>n(Z).isEnable=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(q)("valid"),"inactive-text":n(q)("invalid")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("customerLevel"),prop:"sellLevel"},{default:c((()=>[m(w,{modelValue:n(Z).sellLevel,"onUpdate:modelValue":t[15]||(t[15]=e=>n(Z).sellLevel=e)},{default:c((()=>[(r(!0),u(g,null,v(n(te),(e=>(r(),f(s,{key:e.code,label:e.name,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("commissionLevel")},{default:c((()=>[m(w,{modelValue:n(Z).commissionLevel,"onUpdate:modelValue":t[16]||(t[16]=e=>n(Z).commissionLevel=e)},{default:c((()=>[(r(!0),u(g,null,v(n(ie),(e=>(r(),f(s,{key:e.code,label:e.name,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:24},{default:c((()=>[m(i,{label:n(q)("remark")},{default:c((()=>[m(M,{modelValue:n(Z).remark,"onUpdate:modelValue":t[17]||(t[17]=e=>n(Z).remark=e),type:"textarea",rows:4,maxlength:"250"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["label"]),m(B,{label:n(q)("contactPerson"),name:"second"},{default:c((()=>[m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("name")},{default:c((()=>[m(M,{modelValue:n(Z).contact,"onUpdate:modelValue":t[18]||(t[18]=e=>n(Z).contact=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("telephone")},{default:c((()=>[m(M,{modelValue:n(Z).phone,"onUpdate:modelValue":t[19]||(t[19]=e=>n(Z).phone=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:24},{default:c((()=>[m(i,{label:n(q)("email")},{default:c((()=>[m(M,{modelValue:n(Z).email,"onUpdate:modelValue":t[20]||(t[20]=e=>n(Z).email=e),maxlength:"50"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["label"]),"1"===n(Z).isCredit?(r(),f(B,{key:0,label:n(q)("creditSettings"),name:"third"},{default:c((()=>[m(R,{gutter:24},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("accountSetName")},{default:c((()=>[m(M,{modelValue:n(Z).arSet.arSetName,"onUpdate:modelValue":t[21]||(t[21]=e=>n(Z).arSet.arSetName=e),maxlength:"30"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1}),m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("companyName")},{default:c((()=>[b(p(n(Z).paName),1)])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:24},{default:c((()=>[m(i,{label:n(q)("accountType")},{default:c((()=>[m(a,{modelValue:n(Z).arSet.creditAccType,"onUpdate:modelValue":t[22]||(t[22]=e=>n(Z).arSet.creditAccType=e),class:"ml-4"},{default:c((()=>[m(l,{value:"0",size:"large"},{default:c((()=>[b(p(n(q)("creditAccount")),1)])),_:1}),m(l,{value:"1",size:"large"},{default:c((()=>[b(p(n(q)("prepaidAccount")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),"0"===n(Z).arSet.creditAccType?(r(),u("div",G,[m(R,{gutter:20},{default:c((()=>[m(d,{md:24},{default:c((()=>[m(i,{label:n(q)("settlementPeriod")},{default:c((()=>[m(a,{modelValue:n(Z).arSet.creditPayDays,"onUpdate:modelValue":t[24]||(t[24]=e=>n(Z).arSet.creditPayDays=e),class:"ml-4"},{default:c((()=>[m(l,{value:"1",size:"large"},{default:c((()=>[b(p(n(q)("permanentPeriod")),1)])),_:1}),m(l,{value:"0",size:"large"},{default:c((()=>[b(p(n(q)("fixedPeriod")),1)])),_:1}),"0"===n(Z).arSet.creditPayDays?(r(),f(F,{key:0,modelValue:n(Z).arSet.creditPayFix,"onUpdate:modelValue":t[23]||(t[23]=e=>n(Z).arSet.creditPayFix=e),class:"mx-2",min:0,placeholder:n(q)("pleaseEnterFixedPeriod"),"controls-position":"right",precision:0,style:{width:"200px",margin:"0 8px"}},null,8,["modelValue","placeholder"])):_("",!0)])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:24},{default:c((()=>[m(i,{label:n(q)("maximumLimit")},{default:c((()=>[m(a,{modelValue:n(Z).arSet.creditQuotaType,"onUpdate:modelValue":t[26]||(t[26]=e=>n(Z).arSet.creditQuotaType=e),class:"ml-4"},{default:c((()=>[m(l,{value:"0",size:"large"},{default:c((()=>[b(p(n(q)("unlimitedLimit")),1)])),_:1}),m(l,{value:"1",size:"large"},{default:c((()=>[b(p(n(q)("limitedLimit")),1)])),_:1}),"1"===n(Z).arSet.creditQuotaType?(r(),f(F,{key:0,modelValue:n(Z).arSet.creditQuota,"onUpdate:modelValue":t[25]||(t[25]=e=>n(Z).arSet.creditQuota=e),class:"mx-4",min:0,placeholder:n(q)("pleaseEnterMaximumLimit"),"controls-position":"right",precision:2,style:{width:"200px",margin:"0 8px"}},null,8,["modelValue","placeholder"])):_("",!0)])),_:1},8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:24},{default:c((()=>[m(i,{label:n(q)("validityPeriod")},{default:c((()=>[m(a,{modelValue:n(Z).arSet.creditValidType,"onUpdate:modelValue":t[27]||(t[27]=e=>n(Z).arSet.creditValidType=e),class:"ml-4"},{default:c((()=>[m(l,{value:"0",size:"large"},{default:c((()=>[b(p(n(q)("permanentlyValid")),1)])),_:1}),m(l,{value:"1",size:"large"},{default:c((()=>[b(p(n(q)("fixedValidity")),1)])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["label"]),"1"===n(Z).arSet.creditValidType?(r(),f(i,{key:0,label:""},{default:c((()=>[m(H,{modelValue:n(Z).arSet.creditStartDate,"onUpdate:modelValue":t[28]||(t[28]=e=>n(Z).arSet.creditStartDate=e),type:"date",placeholder:n(q)("startDate")},null,8,["modelValue","placeholder"]),h("span",Y,p(n(q)("to")),1),m(H,{modelValue:n(Z).arSet.creditEndDate,"onUpdate:modelValue":t[29]||(t[29]=e=>n(Z).arSet.creditEndDate=e),type:"date",placeholder:n(q)("endDate")},null,8,["modelValue","placeholder"])])),_:1})):_("",!0)])),_:1})])),_:1})])):_("",!0),m(R,{gutter:20},{default:c((()=>[m(d,{md:20},{default:c((()=>[m(i,{label:n(q)("allowNegativeBalance")},{default:c((()=>[m(O,{modelValue:n(Z).arSet.creditMinusAcc,"onUpdate:modelValue":t[30]||(t[30]=e=>n(Z).arSet.creditMinusAcc=e),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":n(q)("yes"),"inactive-text":n(q)("no")},null,8,["modelValue","active-text","inactive-text"])])),_:1},8,["label"])])),_:1})])),_:1}),m(R,{gutter:20},{default:c((()=>[m(d,{md:12},{default:c((()=>[m(i,{label:n(q)("hotelScope")},{default:c((()=>[m(w,{modelValue:n(Z).arSet.arSetMerchants,"onUpdate:modelValue":t[31]||(t[31]=e=>n(Z).arSet.arSetMerchants=e),multiple:"","collapse-tags":"",clearable:"",placeholder:n(q)("selectStore")},{default:c((()=>[(r(!0),u(g,null,v(n($),(e=>(r(),f(s,{key:e.hcode,label:e.hname,value:e.hcode},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1},8,["label"])):_("",!0)])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),[[K,n(J)]])}}});function K(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{pleaseEnterName:{t:0,b:{t:2,i:[{t:3}],s:"Please enter name"}},pleaseSelectCustomerLevel:{t:0,b:{t:2,i:[{t:3}],s:"Please select client level"}},addedSuccessfully:{t:0,b:{t:2,i:[{t:3}],s:"Added successfully"}},type:{t:0,b:{t:2,i:[{t:3}],s:"Type"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"Channel"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"Select channel"}},ownershipType:{t:0,b:{t:2,i:[{t:3}],s:"Belonging Type"}},belongingStore:{t:0,b:{t:2,i:[{t:3}],s:"Belonging Hotel"}},selectStore:{t:0,b:{t:2,i:[{t:3}],s:"Select hotel"}},allowCredit:{t:0,b:{t:2,i:[{t:3}],s:"Allow Credit"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"Yes"}},no:{t:0,b:{t:2,i:[{t:3}],s:"No"}},hideRoomPriceOnRegistration:{t:0,b:{t:2,i:[{t:3}],s:"Hide Price"}},companyInformation:{t:0,b:{t:2,i:[{t:3}],s:"Company Info"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"Protocol Unit"}},agency:{t:0,b:{t:2,i:[{t:3}],s:"Agent"}},singleStoreApplicable:{t:0,b:{t:2,i:[{t:3}],s:"Single hotel"}},groupShare:{t:0,b:{t:2,i:[{t:3}],s:"Group Share"}},name:{t:0,b:{t:2,i:[{t:3}],s:"Name"}},shortName:{t:0,b:{t:2,i:[{t:3}],s:"Hotel Name"}},legalPerson:{t:0,b:{t:2,i:[{t:3}],s:"Legal Person"}},telephone:{t:0,b:{t:2,i:[{t:3}],s:"Telephone"}},address:{t:0,b:{t:2,i:[{t:3}],s:"Address"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"Start Date"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"Select date"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"End Date"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"Sales Person"}},status:{t:0,b:{t:2,i:[{t:3}],s:"Status"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"Valid"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"Invalid"}},customerLevel:{t:0,b:{t:2,i:[{t:3}],s:"Customer Level"}},commissionLevel:{t:0,b:{t:2,i:[{t:3}],s:"Commission Level"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},contactPerson:{t:0,b:{t:2,i:[{t:3}],s:"Contact Person"}},email:{t:0,b:{t:2,i:[{t:3}],s:"Email"}},creditSettings:{t:0,b:{t:2,i:[{t:3}],s:"Credit Settings"}},accountSetName:{t:0,b:{t:2,i:[{t:3}],s:"AR Name"}},companyName:{t:0,b:{t:2,i:[{t:3}],s:"Company Name"}},accountType:{t:0,b:{t:2,i:[{t:3}],s:"Account Type"}},creditAccount:{t:0,b:{t:2,i:[{t:3}],s:"Credit Account"}},prepaidAccount:{t:0,b:{t:2,i:[{t:3}],s:"Prepaid Account"}},settlementPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Settlement Period"}},permanentPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Permanent Period"}},fixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Fixed Period"}},pleaseEnterFixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Please enter fixed period"}},maximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"Maximum Limit"}},unlimitedLimit:{t:0,b:{t:2,i:[{t:3}],s:"Unlimited Limit"}},limitedLimit:{t:0,b:{t:2,i:[{t:3}],s:"Limited Limit"}},pleaseEnterMaximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"Please enter maximum limit"}},validityPeriod:{t:0,b:{t:2,i:[{t:3}],s:"Validity Period"}},permanentlyValid:{t:0,b:{t:2,i:[{t:3}],s:"Permanently Valid"}},fixedValidity:{t:0,b:{t:2,i:[{t:3}],s:"Fixed Validity"}},to:{t:0,b:{t:2,i:[{t:3}],s:"To"}},allowNegativeBalance:{t:0,b:{t:2,i:[{t:3}],s:"Allow Negative"}},hotelScope:{t:0,b:{t:2,i:[{t:3}],s:"Hotel Scope"}}},"zh-cn":{pleaseEnterName:{t:0,b:{t:2,i:[{t:3}],s:"请输入名称"}},pleaseSelectCustomerLevel:{t:0,b:{t:2,i:[{t:3}],s:"请选择客户级别"}},addedSuccessfully:{t:0,b:{t:2,i:[{t:3}],s:"新增成功"}},type:{t:0,b:{t:2,i:[{t:3}],s:"类型"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"渠道"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"选择渠道"}},ownershipType:{t:0,b:{t:2,i:[{t:3}],s:"所属类型"}},belongingStore:{t:0,b:{t:2,i:[{t:3}],s:"所属门店"}},selectStore:{t:0,b:{t:2,i:[{t:3}],s:"选择门店"}},allowCredit:{t:0,b:{t:2,i:[{t:3}],s:"允许挂账"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"是"}},no:{t:0,b:{t:2,i:[{t:3}],s:"否"}},hideRoomPriceOnRegistration:{t:0,b:{t:2,i:[{t:3}],s:"登记单隐藏房价"}},companyInformation:{t:0,b:{t:2,i:[{t:3}],s:"公司信息"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"协议单位"}},agency:{t:0,b:{t:2,i:[{t:3}],s:"中介"}},singleStoreApplicable:{t:0,b:{t:2,i:[{t:3}],s:"单店适用"}},groupShare:{t:0,b:{t:2,i:[{t:3}],s:"集团共享"}},name:{t:0,b:{t:2,i:[{t:3}],s:"名称"}},shortName:{t:0,b:{t:2,i:[{t:3}],s:"简称"}},legalPerson:{t:0,b:{t:2,i:[{t:3}],s:"法人"}},telephone:{t:0,b:{t:2,i:[{t:3}],s:"电话"}},address:{t:0,b:{t:2,i:[{t:3}],s:"地址"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"开始日期"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"选择日期"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"结束日期"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"销售人员"}},status:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"有效"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"无效"}},customerLevel:{t:0,b:{t:2,i:[{t:3}],s:"客户级别"}},commissionLevel:{t:0,b:{t:2,i:[{t:3}],s:"佣金等级"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},contactPerson:{t:0,b:{t:2,i:[{t:3}],s:"联系人"}},email:{t:0,b:{t:2,i:[{t:3}],s:"Email"}},creditSettings:{t:0,b:{t:2,i:[{t:3}],s:"挂账设置"}},accountSetName:{t:0,b:{t:2,i:[{t:3}],s:"账套名称"}},companyName:{t:0,b:{t:2,i:[{t:3}],s:"公司名称"}},accountType:{t:0,b:{t:2,i:[{t:3}],s:"账户类型"}},creditAccount:{t:0,b:{t:2,i:[{t:3}],s:"信用账户"}},prepaidAccount:{t:0,b:{t:2,i:[{t:3}],s:"预付账户"}},settlementPeriod:{t:0,b:{t:2,i:[{t:3}],s:"结算账期"}},permanentPeriod:{t:0,b:{t:2,i:[{t:3}],s:"永久账期"}},fixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"固定账期"}},pleaseEnterFixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"请输入固定账期"}},maximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"最大额度"}},unlimitedLimit:{t:0,b:{t:2,i:[{t:3}],s:"不限额度"}},limitedLimit:{t:0,b:{t:2,i:[{t:3}],s:"限制额度"}},pleaseEnterMaximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"输入最大限额"}},validityPeriod:{t:0,b:{t:2,i:[{t:3}],s:"有效时间"}},permanentlyValid:{t:0,b:{t:2,i:[{t:3}],s:"永久有效"}},fixedValidity:{t:0,b:{t:2,i:[{t:3}],s:"固定有效"}},to:{t:0,b:{t:2,i:[{t:3}],s:"至"}},allowNegativeBalance:{t:0,b:{t:2,i:[{t:3}],s:"允许负账"}},hotelScope:{t:0,b:{t:2,i:[{t:3}],s:"酒店范围"}}},km:{pleaseEnterName:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះ"}},pleaseSelectCustomerLevel:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសកម្រិតអតិថិជន"}},addedSuccessfully:{t:0,b:{t:2,i:[{t:3}],s:"បានបន្ថែមដោយជោគជ័យ"}},type:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទ"}},channel:{t:0,b:{t:2,i:[{t:3}],s:"ឆានែល"}},selectChannel:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសឆានែល"}},ownershipType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទកម្មសិទ្ធិ"}},belongingStore:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារដែលជាកម្មសិទ្ធិ"}},selectStore:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសសណ្ឋាគារ"}},allowCredit:{t:0,b:{t:2,i:[{t:3}],s:"អនុញ្ញាតឱ្យឥណទាន"}},yes:{t:0,b:{t:2,i:[{t:3}],s:"បាទ/ចាស"}},no:{t:0,b:{t:2,i:[{t:3}],s:"ទេ"}},hideRoomPriceOnRegistration:{t:0,b:{t:2,i:[{t:3}],s:"លាក់តម្លៃបន្ទប់"}},companyInformation:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានក្រុមហ៊ុន"}},agreementUnit:{t:0,b:{t:2,i:[{t:3}],s:"អង្គភាពកិច្ចព្រមព្រៀង"}},agency:{t:0,b:{t:2,i:[{t:3}],s:"ភ្នាក់ងារ"}},singleStoreApplicable:{t:0,b:{t:2,i:[{t:3}],s:"សណ្ឋាគារតែមួយ"}},groupShare:{t:0,b:{t:2,i:[{t:3}],s:"ការចែករំលែកក្រុម"}},name:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះ"}},shortName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះសណ្ឋាគារ"}},legalPerson:{t:0,b:{t:2,i:[{t:3}],s:"ជនផ្លូវការ"}},telephone:{t:0,b:{t:2,i:[{t:3}],s:"ទូរស័ព្ទ"}},address:{t:0,b:{t:2,i:[{t:3}],s:"អាសយដ្ឋាន"}},startDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទចាប់ផ្តើម"}},selectDate:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសកាលបរិច្ឆេទ"}},endDate:{t:0,b:{t:2,i:[{t:3}],s:"កាលបរិច្ឆេទបញ្ចប់"}},salesperson:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកលក់"}},status:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},valid:{t:0,b:{t:2,i:[{t:3}],s:"មានសុពលភាព"}},invalid:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានសុពលភាព"}},customerLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតអតិថិជន"}},commissionLevel:{t:0,b:{t:2,i:[{t:3}],s:"កម្រិតគណនី"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}},contactPerson:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកទំនាក់ទំនង"}},email:{t:0,b:{t:2,i:[{t:3}],s:"អ៊ីមែល"}},creditSettings:{t:0,b:{t:2,i:[{t:3}],s:"ការកំណត់ឥណទាន"}},accountSetName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះគណនី"}},companyName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះក្រុមហ៊ុន"}},accountType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទគណនី"}},creditAccount:{t:0,b:{t:2,i:[{t:3}],s:"គណនីឥណទាន"}},prepaidAccount:{t:0,b:{t:2,i:[{t:3}],s:"គណនីបង់មុន"}},settlementPeriod:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលសំណង"}},permanentPeriod:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលអចិន្ត្រៃយ៍"}},fixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលថេរ"}},pleaseEnterFixedPeriod:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលរយៈពេលថេរ"}},maximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"ដែនកំណត់អតិបរមា"}},unlimitedLimit:{t:0,b:{t:2,i:[{t:3}],s:"គ្មានដែនកំណត់"}},limitedLimit:{t:0,b:{t:2,i:[{t:3}],s:"ដែនកំណត់"}},pleaseEnterMaximumLimit:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលដែនកំណត់អតិបរមា"}},validityPeriod:{t:0,b:{t:2,i:[{t:3}],s:"រយៈពេលសុពលភាព"}},permanentlyValid:{t:0,b:{t:2,i:[{t:3}],s:"មានសុពលភាពអចិន្ត្រៃយ៍"}},fixedValidity:{t:0,b:{t:2,i:[{t:3}],s:"សុពលភាពថេរ"}},to:{t:0,b:{t:2,i:[{t:3}],s:"ដល់"}},allowNegativeBalance:{t:0,b:{t:2,i:[{t:3}],s:"អនុញ្ញាតឱ្យអវិជ្ជមាន"}},hotelScope:{t:0,b:{t:2,i:[{t:3}],s:"វិសាលភាពសណ្ឋាគារ"}}}}})}K(q);const J=I(q,[["__scopeId","data-v-3a20d5d4"]]);export{J as default};
//# sourceMappingURL=createUnitAgent-s5xsgvmO.js.map
