{"version": 3, "file": "member.api-2tU9HGvl.js", "sources": ["../../src/api/modules/member/member/member.api.ts"], "sourcesContent": ["import type { getcUstomerListType, updateOutOrderNoType, updateRemarkType } from './member.type'\r\nimport { AnyFn } from '@vueuse/core'\r\nimport api from '../../../index.ts'\r\n\r\nexport default {\r\n  /**\r\n   * 新增会员\r\n   * @param data\r\n   * @returns\r\n   */\r\n  add: (data: any) => api.post('/admin-api/member/info/create', data),\r\n\r\n  /**\r\n   * 获取会员类型列表\r\n   * @param gcode\r\n   * @param isEnable\r\n   * @returns\r\n   */\r\n  listMemberType: (gcode: string, isEnable: string) =>\r\n    api.get('/admin-api/member/type/list', {\r\n      params: {\r\n        gcode,\r\n        isEnable,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 获得会员类型简单列表\r\n   * @param gcode\r\n   * @param isEnable\r\n   * @returns\r\n   */\r\n  getTypeSimpleList: (gcode: string, isEnable: string) =>\r\n    api.get('/admin-api/member/type/simple-list', {\r\n      params: {\r\n        gcode,\r\n        isEnable,\r\n      },\r\n    }),\r\n\r\n  /**\r\n   * 获取会员信息列表\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  pagelist: (data: any) => api.get('/admin-api/member/info/page', { params: data }),\r\n\r\n  /** 会员详情 */\r\n  detail: (data: any) => api.get('/admin-api/member/info/get', { params: data }),\r\n\r\n  /** 获取未脱敏会员详情 */\r\n  detailNoDes: (data: any) => api.get('/admin-api/member/info/get-no-des', { params: data }),\r\n\r\n  /**\r\n   * 获取未脱敏会员信息分页接口\r\n   */\r\n  getMemberNoPage: (data: any) => api.get('/admin-api/member/info/page-no', { params: data }),\r\n  /**\r\n   * 获得单个会员信息和会员卡\r\n   * @param data\r\n   * @returns\r\n   */\r\n  getCard: (data: any) => api.get('/admin-api/member/info/get-card', { params: data }),\r\n\r\n  /**\r\n   * 更新会员状态\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  updateStatus: (data: any) => api.put('/admin-api/member/info/update-status', data),\r\n\r\n  /**\r\n   * 更新会员信息\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  updateMember: (data: any) => api.put('/admin-api/member/info/update', data),\r\n\r\n  /**\r\n   * 会员升级\r\n   * @param gcode\r\n   * @returns\r\n   */\r\n  upgradeMember: (data: any) => api.put('/admin-api/member/info/upgrade', data),\r\n\r\n  /**\r\n   * 修改房间备注\r\n   * @param params 参数\r\n   * @returns\r\n   */\r\n  updateRemark: (params: updateRemarkType) => api.put('/admin-api/pms/order/update-remark', params),\r\n\r\n  /**\r\n   * 修改外部订单号\r\n   * @param params 参数\r\n   * @returns\r\n   */\r\n  updateOutOrderNo: (params: updateOutOrderNoType) => api.put('/admin-api/pms/order/update-out-order-no', params),\r\n  /**\r\n   * 预订信息人列表\r\n   * @param params 参数\r\n   * @returns\r\n   */\r\n  getCustomerList: (params: getcUstomerListType) => api.get('/admin-api/pms/customer/select-page', { params }),\r\n\r\n  /**\r\n   * 根据身份证号获得客历\r\n   * @param params 参数\r\n   * @returns\r\n   */\r\n  getCustomerByIdNo: (params: any) => api.get('/admin-api/pms/customer/get-by-id-no', { params }),\r\n  /**\r\n   * 更新订单的销售员\r\n   * @param params 参数\r\n   * @returns\r\n   */\r\n  updateSeller: (params: any) => api.put('/admin-api/pms/order/update-seller', params),\r\n}\r\n"], "names": ["memberApi", "add", "data", "api", "post", "listMemberType", "gcode", "isEnable", "get", "params", "getTypeSimpleList", "pagelist", "detail", "detailNoDes", "getMemberNoPage", "getCard", "updateStatus", "put", "updateMember", "upgradeMember", "updateRemark", "updateOutOrderNo", "getCustomerList", "getCustomerByIdNo", "updateSeller"], "mappings": "wCAIA,MAAeA,EAAA,CAMbC,IAAMC,GAAcC,EAAIC,KAAK,gCAAiCF,GAQ9DG,eAAgB,CAACC,EAAeC,IAC9BJ,EAAIK,IAAI,8BAA+B,CACrCC,OAAQ,CACNH,QACAC,cAUNG,kBAAmB,CAACJ,EAAeC,IACjCJ,EAAIK,IAAI,qCAAsC,CAC5CC,OAAQ,CACNH,QACAC,cASNI,SAAWT,GAAcC,EAAIK,IAAI,8BAA+B,CAAEC,OAAQP,IAG1EU,OAASV,GAAcC,EAAIK,IAAI,6BAA8B,CAAEC,OAAQP,IAGvEW,YAAcX,GAAcC,EAAIK,IAAI,oCAAqC,CAAEC,OAAQP,IAKnFY,gBAAkBZ,GAAcC,EAAIK,IAAI,iCAAkC,CAAEC,OAAQP,IAMpFa,QAAUb,GAAcC,EAAIK,IAAI,kCAAmC,CAAEC,OAAQP,IAO7Ec,aAAed,GAAcC,EAAIc,IAAI,uCAAwCf,GAO7EgB,aAAehB,GAAcC,EAAIc,IAAI,gCAAiCf,GAOtEiB,cAAgBjB,GAAcC,EAAIc,IAAI,iCAAkCf,GAOxEkB,aAAeX,GAA6BN,EAAIc,IAAI,qCAAsCR,GAO1FY,iBAAmBZ,GAAiCN,EAAIc,IAAI,2CAA4CR,GAMxGa,gBAAkBb,GAAgCN,EAAIK,IAAI,sCAAuC,CAAEC,WAOnGc,kBAAoBd,GAAgBN,EAAIK,IAAI,uCAAwC,CAAEC,WAMtFe,aAAef,GAAgBN,EAAIc,IAAI,qCAAsCR"}