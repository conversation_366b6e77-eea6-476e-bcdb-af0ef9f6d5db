{"version": 3, "file": "hotelParamConfig.api-CbdvhUfn.js", "sources": ["../../src/api/modules/pms/config/hotelParamConfig.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/pms/hotel-param-config'\r\n/**\r\n * 门店参数设置\r\n */\r\nexport default {\r\n  /**\r\n   * 创建门店参数设置\r\n   * @param data\r\n   */\r\n  createHotelParamConfig: (data: any) => {\r\n    return api.post(`${BASE_PATH}/create`, data)\r\n  },\r\n\r\n  /**\r\n   * 更新门店参数设置\r\n   * @param data\r\n   */\r\n  updateHotelParamConfig: (data: any) => {\r\n    return api.put(`${BASE_PATH}/update`, data)\r\n  },\r\n\r\n  /**\r\n   * 获得门店参数设置-前台\r\n   * @param data\r\n   */\r\n  getHotelParamConfigFront: (data: any) =>\r\n    api.get(`${BASE_PATH}/get/front`, { params: data }),\r\n\r\n  /**\r\n   * 获得门店参数设置-收押配置\r\n   * @param data\r\n   */\r\n  getHotelParamConfigDeposit: (data: any) =>\r\n    api.get(`${BASE_PATH}/get/deposit`, { params: data }),\r\n  /**\r\n   * 获得门店参数设置-班次设置.交班模式\r\n   * @param data\r\n   */\r\n  getHotelParamConfigShiftmode: (data: any) =>\r\n    api.get(`${BASE_PATH}/get/shiftmode`, {\r\n      params: data,\r\n    }),\r\n  /**\r\n   * 获得门店参数设置-早餐券设置\r\n   * @param data\r\n   */\r\n  getHotelParamConfigBreakfastTicket: (data: any) =>\r\n    api.get(`${BASE_PATH}/get/breakfast-ticket`, {\r\n      params: data,\r\n    }),\r\n\r\n  /** 更新修改 */\r\n  edit: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n\r\n  /** 获取夜审参数配置 */\r\n  nightAudit: (data: any) =>\r\n    api.get('/admin-api/pms/group-param-config/get/night-audit', {\r\n      params: data,\r\n    }),\r\n  /** 获得门店仓库配置 */\r\n  getWarehouse: (data: any) =>\r\n    api.get(`/admin-api/pms/general-config/get-warehouse`, { params: data }),\r\n  /** 更新仓库配置 */\r\n  updateWarehouse: (data: any) =>\r\n    api.put(`/admin-api/pms/general-config/update-warehouse`, data),\r\n}\r\n"], "names": ["BASE_PATH", "hotelParamConfigApi", "createHotelParamConfig", "data", "api", "post", "updateHotelParamConfig", "put", "getHotelParamConfigFront", "get", "params", "getHotelParamConfigDeposit", "getHotelParamConfigShiftmode", "getHotelParamConfigBreakfastTicket", "edit", "<PERSON><PERSON><PERSON><PERSON>", "getWarehouse", "updateWarehouse"], "mappings": "mCAEA,MAAMA,EAAY,mCAIHC,EAAA,CAKbC,uBAAyBC,GAChBC,EAAIC,KAAK,GAAGL,WAAoBG,GAOzCG,uBAAyBH,GAChBC,EAAIG,IAAI,GAAGP,WAAoBG,GAOxCK,yBAA2BL,GACzBC,EAAIK,IAAI,GAAGT,cAAuB,CAAEU,OAAQP,IAM9CQ,2BAA6BR,GAC3BC,EAAIK,IAAI,GAAGT,gBAAyB,CAAEU,OAAQP,IAKhDS,6BAA+BT,GAC7BC,EAAIK,IAAI,GAAGT,kBAA2B,CACpCU,OAAQP,IAMZU,mCAAqCV,GACnCC,EAAIK,IAAI,GAAGT,yBAAkC,CAC3CU,OAAQP,IAIZW,KAAOX,GAAcC,EAAIG,IAAI,GAAGP,WAAoBG,GAGpDY,WAAaZ,GACXC,EAAIK,IAAI,oDAAqD,CAC3DC,OAAQP,IAGZa,aAAeb,GACbC,EAAIK,IAAI,8CAA+C,CAAEC,OAAQP,IAEnEc,gBAAkBd,GAChBC,EAAIG,IAAI,iDAAkDJ"}