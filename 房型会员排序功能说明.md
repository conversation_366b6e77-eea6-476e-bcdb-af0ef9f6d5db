# 房型会员排序功能实现说明

## 功能概述

为电子房价牌页面添加了房型和会员等级的拖拽排序功能：

- **房型排序**：支持上下拖拽排序
- **会员等级排序**：支持左右拖拽排序
- **保存功能**：按排序顺序传递 `rtCode` 和 `mtCode` 数组

## 实现的功能

### 1. 房型上下排序
- 在房型选择区域下方显示可拖拽的房型列表
- 支持垂直方向的拖拽排序
- 已选中的房型会高亮显示
- 拖拽手柄图标提供视觉提示

### 2. 会员等级左右排序
- 在会员类型选择区域下方显示可拖拽的会员等级列表
- 支持水平方向的拖拽排序
- 已选中的会员等级会高亮显示
- 排序后自动更新表格列配置

### 3. 数据保存
- 点击保存按钮时，按照排序后的顺序传递数据
- `rtCode`: 房型代码按排序顺序组成的字符串（逗号分隔）
- `mtCode`: 会员等级代码按排序顺序组成的字符串（逗号分隔）

## 技术实现

### 核心依赖
- **Sortable.js**: 提供拖拽排序功能
- **Element Plus Icons**: 提供拖拽手柄图标

### 关键变量
```javascript
// 排序后的数据
const sortedRoomDataList = ref([])     // 排序后的房型列表
const sortedMtsOptions = ref([])       // 排序后的会员类型列表

// DOM 引用
const roomSortableContainer = ref()    // 房型排序容器
const memberSortableContainer = ref()  // 会员排序容器
```

### 核心方法
```javascript
// 初始化房型排序
initRoomSortable()

// 初始化会员类型排序  
initMemberSortable()

// 更新表格列配置
updateTableColumns()
```

### 保存逻辑
```javascript
// 按排序顺序传递数据
const sortedRtCodes = sortedRoomDataList.value.map(item => item.rtCode)
const sortedMtCodes = sortedMtsOptions.value.map(item => item.mtCode)

const params = {
  rtCode: sortedRtCodes.join(','),
  mtCode: sortedMtCodes.join(',')
}
```

## 样式特性

### 排序区域样式
- 灰色背景区域包含排序列表
- 清晰的标签说明排序方向
- 响应式布局适配不同屏幕

### 排序项样式
- 白色背景，圆角边框
- 悬停时蓝色边框高亮
- 选中状态蓝色背景
- 拖拽时旋转和缩放效果

### 拖拽反馈
- 半透明幽灵效果
- 选中时轻微缩放
- 拖拽时旋转角度

## 使用说明

1. **选择房型/会员等级**：在下拉选择框中选择需要显示的项目
2. **拖拽排序**：在排序区域拖拽调整顺序
   - 房型：上下拖拽
   - 会员等级：左右拖拽
3. **保存设置**：点击保存按钮，系统会按排序顺序传递数据

## 注意事项

- 排序功能只对已选中的项目生效
- 排序后会自动更新表格列的显示顺序
- 保存时会覆盖原有的排序设置
- 页面刷新后需要重新设置排序（如需持久化可考虑后端存储排序信息）
