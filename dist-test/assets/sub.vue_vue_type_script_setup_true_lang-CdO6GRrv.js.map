{"version": 3, "file": "sub.vue_vue_type_script_setup_true_lang-CdO6GRrv.js", "sources": ["../../src/layouts/components/MenuPanel/sub.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport type { OverlayScrollbarsComponentRef } from 'overlayscrollbars-vue'\r\nimport type { SubMenuProps } from './types'\r\nimport { useTimeoutFn } from '@vueuse/core'\r\nimport { OverlayScrollbarsComponent } from 'overlayscrollbars-vue'\r\nimport Item from './item.vue'\r\nimport { rootMenuInjectionKey } from './types'\r\n\r\ndefineOptions({\r\n  name: 'SubMenu',\r\n})\r\n\r\nconst props = withDefaults(\r\n  defineProps<SubMenuProps>(),\r\n  {\r\n    level: 0,\r\n  },\r\n)\r\n\r\nconst index = props.menu.path ?? JSON.stringify(props.menu)\r\nconst itemRef = useTemplateRef('itemRef')\r\nconst subMenuRef = useTemplateRef<OverlayScrollbarsComponentRef>('subMenuRef')\r\nconst rootMenu = inject(rootMenuInjectionKey)!\r\n\r\nconst opened = computed(() => {\r\n  return rootMenu.openedMenus.includes(props.uniqueKey.at(-1)!)\r\n})\r\n\r\nconst transitionEvent = {\r\n  enter(el: HTMLElement) {\r\n    if (el.offsetHeight > window.innerHeight) {\r\n      el.style.height = `${window.innerHeight}px`\r\n    }\r\n  },\r\n  afterEnter: () => {},\r\n  beforeLeave: (el: HTMLElement) => {\r\n    el.style.overflow = 'hidden'\r\n    el.style.maxHeight = `${el.offsetHeight}px`\r\n  },\r\n  leave: (el: HTMLElement) => {\r\n    el.style.maxHeight = '0'\r\n  },\r\n  afterLeave(el: HTMLElement) {\r\n    el.style.overflow = ''\r\n    el.style.maxHeight = ''\r\n  },\r\n}\r\n\r\nconst transitionClass = {\r\n  enterActiveClass: 'ease-in-out duration-300',\r\n  enterFromClass: 'opacity-0 translate-x-4',\r\n  enterToClass: 'opacity-100',\r\n  leaveActiveClass: 'ease-in-out duration-300',\r\n  leaveFromClass: 'opacity-100',\r\n  leaveToClass: 'opacity-0',\r\n}\r\n\r\nconst hasChildren = computed(() => {\r\n  let flag = true\r\n  if (props.menu.children) {\r\n    if (props.menu.children.every((item: any) => item.meta?.menu === false)) {\r\n      flag = false\r\n    }\r\n  }\r\n  else {\r\n    flag = false\r\n  }\r\n  return flag\r\n})\r\n\r\nfunction handleClick() {\r\n  if (hasChildren.value) {\r\n    return\r\n  }\r\n  rootMenu.handleMenuItemClick(index)\r\n}\r\n\r\nlet timeout: (() => void) | undefined\r\n\r\nfunction handleMouseenter() {\r\n  if (props.level !== 0) {\r\n    return\r\n  }\r\n  rootMenu.mouseInMenu = props.uniqueKey\r\n  timeout?.()\r\n  ;({ stop: timeout } = useTimeoutFn(() => {\r\n    if (hasChildren.value) {\r\n      rootMenu.openMenu(index)\r\n      nextTick(() => {\r\n        const el = itemRef.value?.ref\r\n        if (!el) {\r\n          return\r\n        }\r\n        let top = 0\r\n        let left = 0\r\n        if (rootMenu.props.mode === 'vertical' || props.level !== 0) {\r\n          top = el.getBoundingClientRect().top + el.scrollTop\r\n          left = (rootMenu.props.direction === 'ltr' ? el.getBoundingClientRect().left : document.documentElement.clientWidth - el.getBoundingClientRect().right) + el.getBoundingClientRect().width\r\n          if (top + subMenuRef.value!.getElement()!.offsetHeight > window.innerHeight) {\r\n            top = window.innerHeight - subMenuRef.value!.getElement()!.offsetHeight\r\n          }\r\n        }\r\n        else {\r\n          top = el.getBoundingClientRect().top + el.getBoundingClientRect().height\r\n          left = rootMenu.props.direction === 'ltr' ? el.getBoundingClientRect().left : document.documentElement.clientWidth - el.getBoundingClientRect().right\r\n          if (top + subMenuRef.value!.getElement()!.offsetHeight > window.innerHeight) {\r\n            subMenuRef.value!.getElement()!.style.height = `${window.innerHeight - top}px`\r\n          }\r\n          if (left + subMenuRef.value!.getElement()!.offsetWidth > document.documentElement.clientWidth) {\r\n            left = document.documentElement.clientWidth - subMenuRef.value!.getElement()!.offsetWidth\r\n          }\r\n        }\r\n        subMenuRef.value!.getElement()!.style.top = `${top}px`\r\n        subMenuRef.value!.getElement()!.style.insetInlineStart = `${left}px`\r\n      })\r\n    }\r\n    else {\r\n      const path = props.menu.children ? rootMenu.subMenus[index].indexPath.at(-1)! : rootMenu.items[index].indexPath.at(-1)!\r\n      rootMenu.openMenu(path)\r\n    }\r\n  }, 300))\r\n}\r\n\r\nfunction handleMouseleave() {\r\n  if (props.level !== 0) {\r\n    return\r\n  }\r\n  rootMenu.mouseInMenu = []\r\n  timeout?.()\r\n  ;({ stop: timeout } = useTimeoutFn(() => {\r\n    if (rootMenu.mouseInMenu.length === 0) {\r\n      rootMenu.closeMenu(props.uniqueKey)\r\n    }\r\n    else {\r\n      if (hasChildren.value) {\r\n        !rootMenu.mouseInMenu.includes(props.uniqueKey.at(-1)!) && rootMenu.closeMenu(props.uniqueKey.at(-1)!)\r\n      }\r\n    }\r\n  }, 300))\r\n}\r\n\r\nfunction handlePanelMouseenter() {\r\n  timeout?.()\r\n}\r\n</script>\r\n\r\n<template>\r\n  <Item ref=\"itemRef\" :unique-key=\"uniqueKey\" :item=\"menu\" :level=\"level\" :sub-menu=\"hasChildren\" @click=\"handleClick\" @mouseenter=\"handleMouseenter\" @mouseleave=\"handleMouseleave\" />\r\n  <Teleport v-if=\"hasChildren\" to=\"body\" :disabled=\"level !== 0\">\r\n    <Transition v-if=\"level === 0\" v-bind=\"transitionClass\" v-on=\"transitionEvent\">\r\n      <OverlayScrollbarsComponent\r\n        v-show=\"opened\" ref=\"subMenuRef\" :options=\"{ scrollbars: { visibility: 'hidden' } }\" defer class=\"sub-menu fixed z-3000 bg-[var(--g-sub-sidebar-bg)] px-1 shadow-xl ring-1 ring-stone-2 dark-ring-stone-8\" :class=\"{\r\n          'rounded-lg': rootMenu.props.rounded,\r\n        }\" @mouseenter=\"handlePanelMouseenter\" @mouseleave=\"handleMouseleave\"\r\n      >\r\n        <div class=\"columns-3 p-2 2xl:columns-4\">\r\n          <div v-for=\"item in menu.children\" :key=\"item.path ?? JSON.stringify(item)\" class=\"w-[200px] break-inside-avoid\">\r\n            <SubMenu v-if=\"item.meta?.menu !== false\" :unique-key=\"[...uniqueKey, item.path ?? JSON.stringify(item)]\" :menu=\"item\" :level=\"level + 1\" />\r\n          </div>\r\n        </div>\r\n      </OverlayScrollbarsComponent>\r\n    </Transition>\r\n    <template v-else>\r\n      <template v-for=\"item in menu.children\" :key=\"item.path ?? JSON.stringify(item)\">\r\n        <SubMenu v-if=\"item.meta?.menu !== false\" :unique-key=\"[...uniqueKey, item.path ?? JSON.stringify(item)]\" :menu=\"item\" :level=\"level + 1\" />\r\n      </template>\r\n    </template>\r\n  </Teleport>\r\n</template>\r\n"], "names": ["props", "__props", "index", "menu", "path", "JSON", "stringify", "itemRef", "useTemplateRef", "subMenuRef", "rootMenu", "inject", "rootMenuInjectionKey", "opened", "computed", "openedMenus", "includes", "<PERSON><PERSON><PERSON>", "at", "transitionEvent", "enter", "el", "offsetHeight", "window", "innerHeight", "style", "height", "afterEnter", "beforeLeave", "overflow", "maxHeight", "leave", "afterLeave", "transitionClass", "enterActiveClass", "enterFromClass", "enterToClass", "leaveActiveClass", "leaveFromClass", "leaveToClass", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flag", "children", "every", "item", "_a", "meta", "handleClick", "value", "handleMenuItemClick", "timeout", "handleMouseenter", "level", "mouseInMenu", "stop", "useTimeoutFn", "openMenu", "nextTick", "ref", "top", "left", "mode", "getBoundingClientRect", "scrollTop", "direction", "document", "documentElement", "clientWidth", "right", "width", "getElement", "offsetWidth", "insetInlineStart", "subMenus", "indexPath", "items", "handleMouseleave", "length", "closeMenu", "handlePanelMouseenter"], "mappings": "gZAYA,MAAMA,EAAQC,EAORC,EAAQF,EAAMG,KAAKC,MAAQC,KAAKC,UAAUN,EAAMG,MAChDI,EAAUC,EAAe,WACzBC,EAAaD,EAA8C,cAC3DE,EAAWC,EAAOC,GAElBC,EAASC,GAAS,IACfJ,EAASK,YAAYC,SAAShB,EAAMiB,UAAUC,UAGjDC,EAAkB,CACtB,KAAAC,CAAMC,GACAA,EAAGC,aAAeC,OAAOC,cAC3BH,EAAGI,MAAMC,OAAS,GAAGH,OAAOC,gBAEhC,EACAG,WAAY,OACZC,YAAcP,IACZA,EAAGI,MAAMI,SAAW,SACpBR,EAAGI,MAAMK,UAAY,GAAGT,EAAGC,gBAAY,EAEzCS,MAAQV,IACNA,EAAGI,MAAMK,UAAY,GAAA,EAEvB,UAAAE,CAAWX,GACTA,EAAGI,MAAMI,SAAW,GACpBR,EAAGI,MAAMK,UAAY,EAAA,GAInBG,EAAkB,CACtBC,iBAAkB,2BAClBC,eAAgB,0BAChBC,aAAc,cACdC,iBAAkB,2BAClBC,eAAgB,cAChBC,aAAc,aAGVC,EAAc1B,GAAS,KAC3B,IAAI2B,GAAO,EASJ,OARHzC,EAAMG,KAAKuC,SACT1C,EAAMG,KAAKuC,SAASC,OAAOC,UAAmB,OAAe,KAApB,OAAKC,EAAAD,EAAAE,eAAM3C,KAAS,MACxDsC,GAAA,GAIFA,GAAA,EAEFA,CAAA,IAGT,SAASM,IACHP,EAAYQ,OAGhBtC,EAASuC,oBAAoB/C,EAAK,CAGhC,IAAAgD,EAEJ,SAASC,IACa,IAAhBnD,EAAMoD,QAGV1C,EAAS2C,YAAcrD,EAAMiB,UACnB,MAAAiC,GAAAA,MACNI,KAAMJ,GAAYK,GAAa,KACjC,GAAIf,EAAYQ,MACdtC,EAAS8C,SAAStD,GAClBuD,GAAS,WACD,MAAApC,EAAK,OAAAwB,EAAQtC,EAAAyC,YAAO,EAAAH,EAAAa,IAC1B,IAAKrC,EACH,OAEF,IAAIsC,EAAM,EACNC,EAAO,EACiB,aAAxBlD,EAASV,MAAM6D,MAAuC,IAAhB7D,EAAMoD,OAC9CO,EAAMtC,EAAGyC,wBAAwBH,IAAMtC,EAAG0C,UAC1CH,GAAqC,QAA7BlD,EAASV,MAAMgE,UAAsB3C,EAAGyC,wBAAwBF,KAAOK,SAASC,gBAAgBC,YAAc9C,EAAGyC,wBAAwBM,OAAS/C,EAAGyC,wBAAwBO,MACjLV,EAAMlD,EAAWuC,MAAOsB,aAAchD,aAAeC,OAAOC,cAC9DmC,EAAMpC,OAAOC,YAAcf,EAAWuC,MAAOsB,aAAchD,gBAI7DqC,EAAMtC,EAAGyC,wBAAwBH,IAAMtC,EAAGyC,wBAAwBpC,OAClEkC,EAAoC,QAA7BlD,EAASV,MAAMgE,UAAsB3C,EAAGyC,wBAAwBF,KAAOK,SAASC,gBAAgBC,YAAc9C,EAAGyC,wBAAwBM,MAC5IT,EAAMlD,EAAWuC,MAAOsB,aAAchD,aAAeC,OAAOC,cACnDf,EAAAuC,MAAOsB,aAAc7C,MAAMC,OAAYH,OAAOC,YAAcmC,EAAxB,MAE7CC,EAAOnD,EAAWuC,MAAOsB,aAAcC,YAAcN,SAASC,gBAAgBC,cAChFP,EAAOK,SAASC,gBAAgBC,YAAc1D,EAAWuC,MAAOsB,aAAcC,cAGlF9D,EAAWuC,MAAOsB,aAAc7C,MAAMkC,IAAM,GAAGA,MAC/ClD,EAAWuC,MAAOsB,aAAc7C,MAAM+C,iBAAmB,GAAGZ,KAAI,QAG/D,CACH,MAAMxD,EAAOJ,EAAMG,KAAKuC,SAAWhC,EAAS+D,SAASvE,GAAOwE,UAAUxD,IAAG,GAAOR,EAASiE,MAAMzE,GAAOwE,UAAUxD,IAAK,GACrHR,EAAS8C,SAASpD,EAAI,IAEvB,MAAG,CAGR,SAASwE,IACa,IAAhB5E,EAAMoD,QAGV1C,EAAS2C,YAAc,GACb,MAAAH,GAAAA,MACNI,KAAMJ,GAAYK,GAAa,KACG,IAAhC7C,EAAS2C,YAAYwB,OACdnE,EAAAoE,UAAU9E,EAAMiB,WAGrBuB,EAAYQ,QACbtC,EAAS2C,YAAYrC,SAAShB,EAAMiB,UAAUC,IAAG,KAASR,EAASoE,UAAU9E,EAAMiB,UAAUC,OAChG,GAED,MAAG,CAGR,SAAS6D,IACG,MAAA7B,GAAAA,GAAA"}