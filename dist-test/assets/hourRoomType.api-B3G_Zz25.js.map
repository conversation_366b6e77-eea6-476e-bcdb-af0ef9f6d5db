{"version": 3, "file": "hourRoomType.api-B3G_Zz25.js", "sources": ["../../src/api/modules/pms/roomtype/hourRoomType.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\n// 定义API的基础路径和版本号，提高可维护性\r\nconst BASE_PATH = 'admin-api/pms/hour-room-type'\r\n/**\r\n * 钟点关联房型\r\n */\r\nexport default {\r\n  /**\r\n   * 获得钟点房房型列表\r\n   * @returns\r\n   * @param data\r\n   */\r\n  getHourRoomTypeList: (data: any) => api.get(`${BASE_PATH}/list`, { params: data }),\r\n  /**\r\n   * 获得钟点房房型\r\n   * @param id\r\n   * @returns\r\n   */\r\n  getHourRoomType: (data: any) => api.get(`${BASE_PATH}/get`, { params: data }),\r\n  /**\r\n   * 创建钟点房房型\r\n   * @param data\r\n   */\r\n  createHourRoomType: (data: any) => api.post(`${BASE_PATH}/create`, data),\r\n  /**\r\n   * 更新钟点房房型\r\n   * @param data\r\n   */\r\n  updateHourRoomType: (data: any) => api.put(`${BASE_PATH}/update`, data),\r\n  /**\r\n   * 删除钟点房房型\r\n   * @param id\r\n   */\r\n  deleteHourRoomType: (data: any) => api.delete(`${BASE_PATH}/delete`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "hourRoomTypeApi", "getHourRoomTypeList", "data", "api", "get", "params", "getHourRoomType", "createHourRoomType", "post", "updateHourRoomType", "put", "deleteHourRoomType", "delete"], "mappings": "wCAGA,MAAMA,EAAY,+BAIHC,EAAA,CAMbC,oBAAsBC,GAAcC,EAAIC,IAAI,GAAGL,SAAkB,CAAEM,OAAQH,IAM3EI,gBAAkBJ,GAAcC,EAAIC,IAAI,GAAGL,QAAiB,CAAEM,OAAQH,IAKtEK,mBAAqBL,GAAcC,EAAIK,KAAK,GAAGT,WAAoBG,GAKnEO,mBAAqBP,GAAcC,EAAIO,IAAI,GAAGX,WAAoBG,GAKlES,mBAAqBT,GAAcC,EAAIS,OAAO,GAAGb,WAAoB,CAAEM,OAAQH"}