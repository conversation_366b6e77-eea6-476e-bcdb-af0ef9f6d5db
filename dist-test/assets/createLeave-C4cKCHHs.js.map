{"version": 3, "file": "createLeave-C4cKCHHs.js", "sources": ["../../src/views/room/goods/leave/components/DetailForm/createLeave.vue"], "sourcesContent": ["<i18n>\r\n  {\r\n    \"en\": {\r\n      \"itemName\": \"Item Name\",\r\n      \"roomNumberLostLocation\": \"Room No/Location\",\r\n      \"ownerName\": \"Owner Name\",\r\n      \"ownerPhone\": \"Owner Phone\",\r\n      \"finder\": \"Finder\",\r\n      \"findTime\": \"Find Time\",\r\n      \"itemDescription\": \"Item Description\",\r\n      \"pleaseEnterItemName\": \"Please enter item name\",\r\n      \"pleaseEnterLostLocation\": \"Please enter lost location\",\r\n      \"pleaseEnterOwnerName\": \"Please enter owner name\",\r\n      \"pleaseEnterOwnerPhone\": \"Please enter owner phone\",\r\n      \"pleaseEnterFinder\": \"Please enter finder\",\r\n      \"pleaseSelectFindTime\": \"Please select find time\",\r\n      \"pleaseEnterItemDescription\": \"Please enter item description\",\r\n      \"addSuccess\": \"Added successfully\"\r\n    },\r\n    \"zh-cn\": {\r\n      \"itemName\": \"物品名称\",\r\n      \"roomNumberLostLocation\": \"房号/丢失地点\",\r\n      \"ownerName\": \"失主姓名\",\r\n      \"ownerPhone\": \"失主电话\",\r\n      \"finder\": \"捡到人\",\r\n      \"findTime\": \"捡到时间\",\r\n      \"itemDescription\": \"物品描述\",\r\n      \"pleaseEnterItemName\": \"请输入物品名称\",\r\n      \"pleaseEnterLostLocation\": \"请输入丢失地点\",\r\n      \"pleaseEnterOwnerName\": \"请输入失主姓名\",\r\n      \"pleaseEnterOwnerPhone\": \"请输入失主电话\",\r\n      \"pleaseEnterFinder\": \"请输入捡到人\",\r\n      \"pleaseSelectFindTime\": \"请选择捡到时间\",\r\n      \"pleaseEnterItemDescription\": \"请输入物品描述\",\r\n      \"addSuccess\": \"添加成功\"\r\n    },\r\n    \"km\": {\r\n      \"itemName\": \"ឈ្មោះវត្ថុ\",\r\n      \"roomNumberLostLocation\": \"លេខបន្ទប់/ទីតាំងបាត់បង់\",\r\n      \"ownerName\": \"ឈ្មោះម្ចាស់\",\r\n      \"ownerPhone\": \"ទូរស័ព្ទម្ចាស់\",\r\n      \"finder\": \"អ្នករកឃើញ\",\r\n      \"findTime\": \"ពេលវេលារកឃើញ\",\r\n      \"itemDescription\": \"ការពិពណ៌នាអំពីវត្ថុ\",\r\n      \"pleaseEnterItemName\": \"សូមបញ្ចូលឈ្មោះវត្ថុ\",\r\n      \"pleaseEnterLostLocation\": \"សូមបញ្ចូលទីតាំងបាត់បង់\",\r\n      \"pleaseEnterOwnerName\": \"សូមបញ្ចូលឈ្មោះម្ចាស់\",\r\n      \"pleaseEnterOwnerPhone\": \"សូមបញ្ចូលទូរស័ព្ទម្ចាស់\",\r\n      \"pleaseEnterFinder\": \"សូមបញ្ចូលអ្នករកឃើញ\",\r\n      \"pleaseSelectFindTime\": \"សូមជ្រើសរើសពេលវេលារកឃើញ\",\r\n      \"pleaseEnterItemDescription\": \"សូមបញ្ចូលការពិពណ៌នាអំពីវត្ថុ\",\r\n      \"addSuccess\": \"បន្ថែមដោយជោគជ័យ\"\r\n    }\r\n  }\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance, FormRules } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\nimport { leaveApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport dayjs from 'dayjs'\r\nimport { useI18n } from 'vue-i18n'\r\n\r\nconst props = withDefaults(defineProps<DetailFormProps>(), {\r\n  id: 0,\r\n})\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst loading = ref(false)\r\nconst formRef = ref<FormInstance>()\r\nconst form = ref({\r\n  id: props.id,\r\n  gcode: userStore.gcode,\r\n  /** 门店代码 */\r\n  hcode: userStore.hcode,\r\n  /** 遗留地点 */\r\n  place: '',\r\n  /** 物品描述 */\r\n  goodsDesc: '',\r\n  /** 物品名称 */\r\n  goodsName: '',\r\n  /** 失主姓名 */\r\n  owner: '',\r\n  /** 失主电话 */\r\n  ownerPhone: '',\r\n  /** 捡到人 */\r\n  picker: '',\r\n  /** 捡到时间 */\r\n  pickTime: dayjs(),\r\n  /** 状态 0:未领取 1:已领取 */\r\n  state: '0',\r\n})\r\nconst formRules = ref<FormRules>({\r\n  goodsName: [{ required: true, message: t('pleaseEnterItemName'), trigger: 'blur' }],\r\n  goodsDesc: [{ required: true, message: t('pleaseEnterItemDescription'), trigger: 'blur' }],\r\n  place: [{ required: true, message: t('pleaseEnterLostLocation'), trigger: 'blur' }],\r\n})\r\n\r\ndefineExpose({\r\n  submit() {\r\n    return new Promise<void>((resolve) => {\r\n      formRef.value &&\r\n        formRef.value.validate((valid) => {\r\n          if (valid) {\r\n            // 格式化时间字段\r\n            const submitData = {\r\n              ...form.value,\r\n              pickTime: dayjs(form.value.pickTime).format('YYYY-MM-DD HH:mm:ss'),\r\n            }\r\n            leaveApi.create(submitData).then(() => {\r\n              ElMessage.success({\r\n                message: t('addSuccess'),\r\n                center: true,\r\n              })\r\n              resolve()\r\n            })\r\n          }\r\n        })\r\n    })\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"formRules\" label-width=\"165px\" label-suffix=\"：\">\r\n      <el-form-item :label=\"t('itemName')\" prop=\"goodsName\">\r\n        <el-input v-model=\"form.goodsName\" :placeholder=\"t('pleaseEnterItemName')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('roomNumberLostLocation')\" prop=\"place\">\r\n        <el-input v-model=\"form.place\" :placeholder=\"t('pleaseEnterLostLocation')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('ownerName')\" prop=\"owner\">\r\n        <el-input v-model=\"form.owner\" :placeholder=\"t('pleaseEnterOwnerName')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('ownerPhone')\" prop=\"ownerPhone\">\r\n        <el-input v-model=\"form.ownerPhone\" :placeholder=\"t('pleaseEnterOwnerPhone')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('finder')\" prop=\"picker\">\r\n        <el-input v-model=\"form.picker\" :placeholder=\"t('pleaseEnterFinder')\" maxlength=\"30\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('findTime')\" prop=\"pickTime\">\r\n        <el-date-picker v-model=\"form.pickTime\" type=\"datetime\" :placeholder=\"t('pleaseSelectFindTime')\" value-format=\"YYYY-MM-DD HH:mm:ss\" />\r\n      </el-form-item>\r\n      <el-form-item :label=\"t('itemDescription')\" prop=\"goodsDesc\">\r\n        <el-input v-model=\"form.goodsDesc\" type=\"textarea\" :rows=\"4\" :placeholder=\"t('pleaseEnterItemDescription')\" maxlength=\"200\" />\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n// scss\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "loading", "ref", "formRef", "form", "id", "gcode", "hcode", "place", "goodsDesc", "goodsName", "owner", "ownerPhone", "picker", "pickTime", "dayjs", "state", "formRules", "required", "message", "trigger", "__expose", "submit", "Promise", "resolve", "value", "validate", "valid", "submitData", "format", "leaveApi", "create", "then", "ElMessage", "success", "center"], "mappings": "4nBAgEA,MAAMA,EAAQC,GAGRC,EAAEA,GAAMC,IACRC,EAAYC,IACZC,EAAUC,GAAI,GACdC,EAAUD,IACVE,EAAOF,EAAI,CACfG,GAAIV,EAAMU,GACVC,MAAOP,EAAUO,MAEjBC,MAAOR,EAAUQ,MAEjBC,MAAO,GAEPC,UAAW,GAEXC,UAAW,GAEXC,MAAO,GAEPC,WAAY,GAEZC,OAAQ,GAERC,SAAUC,IAEVC,MAAO,MAEHC,EAAYf,EAAe,CAC/BQ,UAAW,CAAC,CAAEQ,UAAU,EAAMC,QAAStB,EAAE,uBAAwBuB,QAAS,SAC1EX,UAAW,CAAC,CAAES,UAAU,EAAMC,QAAStB,EAAE,8BAA+BuB,QAAS,SACjFZ,MAAO,CAAC,CAAEU,UAAU,EAAMC,QAAStB,EAAE,2BAA4BuB,QAAS,kBAG/DC,EAAA,CACXC,OAAS,IACA,IAAIC,SAAeC,IACxBrB,EAAQsB,OACNtB,EAAQsB,MAAMC,UAAUC,IACtB,GAAIA,EAAO,CAET,MAAMC,EAAa,IACdxB,EAAKqB,MACRX,SAAUC,EAAMX,EAAKqB,MAAMX,UAAUe,OAAO,wBAE9CC,EAASC,OAAOH,GAAYI,MAAK,KAC/BC,EAAUC,QAAQ,CAChBf,QAAStB,EAAE,cACXsC,QAAQ,IAEFX,GAAA,GACT,IAEJ"}