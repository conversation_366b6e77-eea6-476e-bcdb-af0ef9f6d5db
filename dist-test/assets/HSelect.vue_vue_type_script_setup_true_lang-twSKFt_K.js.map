{"version": 3, "file": "HSelect.vue_vue_type_script_setup_true_lang-twSKFt_K.js", "sources": ["../../src/layouts/ui-kit/HSelect.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    options: {\r\n      label: string | number\r\n      value: string | number\r\n      disabled?: boolean\r\n    }[]\r\n    disabled?: boolean\r\n  }>(),\r\n  {\r\n    disabled: false,\r\n  },\r\n)\r\n\r\nconst value = defineModel<string | number>()\r\n\r\nconst selected = computed({\r\n  get() {\r\n    return props.options.find(option => option.value === value.value) ?? props.options[0]\r\n  },\r\n  set(val) {\r\n    value.value = val.value\r\n  },\r\n})\r\n</script>\r\n\r\n<template>\r\n  <VMenu :triggers=\"['click']\" :popper-triggers=\"['click']\" :delay=\"0\" :disabled=\"disabled\" v-bind=\"$attrs\">\r\n    <div class=\"w-full inline-flex\">\r\n      <button class=\"relative block w-full flex cursor-default items-center gap-x-2 border-0 rounded-md bg-white px-2.5 py-1.5 pe-9 text-left text-sm shadow-sm ring-1 ring-stone-2 ring-inset lg-w-48 disabled-cursor-not-allowed dark-bg-dark focus-outline-none focus-ring-2 dark-ring-stone-8 focus-ring-ui-primary\" :disabled=\"disabled\">\r\n        <span class=\"block truncate\">\r\n          {{ selected.label }}\r\n        </span>\r\n        <span class=\"pointer-events-none absolute end-0 inset-y-0 flex items-center pe-2.5\">\r\n          <SvgIcon name=\"i-carbon:chevron-down\" class=\"h-5 w-5 flex-shrink-0 text-stone-5\" />\r\n        </span>\r\n      </button>\r\n    </div>\r\n    <template #popper>\r\n      <div class=\"max-h-60 w-full scroll-py-1 overflow-y-auto p-1 lg-w-48 focus-outline-none\">\r\n        <button v-for=\"option in options\" :key=\"option.value\" :disabled=\"option.disabled\" class=\"w-full cursor-pointer truncate border-size-0 rounded-md bg-inherit px-2 py-1.5 text-start text-sm disabled-cursor-not-allowed hover-not-disabled-bg-stone-1 dark-hover-not-disabled-bg-stone-9\" :class=\"{ 'font-bold': modelValue === option.value }\" @click=\"selected = option\">\r\n          {{ option.label }}\r\n        </button>\r\n      </div>\r\n    </template>\r\n  </VMenu>\r\n</template>\r\n"], "names": ["props", "__props", "value", "_useModel", "selected", "computed", "get", "options", "find", "option", "set", "val"], "mappings": "4kBACA,MAAMA,EAAQC,EAcRC,EAAQC,kBAERC,EAAWC,EAAS,CACxBC,IAAM,IACGN,EAAMO,QAAQC,MAAeC,GAAAA,EAAOP,QAAUA,EAAMA,SAAUF,EAAMO,QAAQ,GAErF,GAAAG,CAAIC,GACFT,EAAMA,MAAQS,EAAIT,KAAA"}