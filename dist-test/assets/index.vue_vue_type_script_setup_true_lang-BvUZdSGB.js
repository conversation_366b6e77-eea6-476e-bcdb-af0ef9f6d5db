import{_ as l}from"./HDialog.vue_vue_type_script_setup_true_lang-wgEOz9-v.js";import s from"./HKbd-j4z1e5lJ.js";import{d as a,b as t,W as e,y as n,X as u,o,e as d,w as i,g as c,f as m,h as _,Y as f,u as p,c as r,R as y,i as g}from"./index-CkEhI1Zk.js";const b={class:"px-4"},v={class:"grid gap-2 sm-grid-cols-2"},x={class:"list-none ps-4 text-sm"},k={class:"py-1"},A={key:0,class:"py-1"},h={key:0},H={class:"list-none ps-4 text-sm"},j={class:"py-1"},I={key:1},S={class:"list-none ps-4 text-sm"},V={class:"py-1"},W={class:"py-1"},w={class:"py-1"},D={class:"py-1"},J={class:"py-1"},K={key:2},P={class:"list-none ps-4 text-sm"},R={class:"py-1"},U={class:"py-1"},X=a({name:"HotkeysIntro",__name:"index",setup(a){const X=t(!1),Y=e();return n((()=>{u.on("global-hotkeys-intro-toggle",(()=>{X.value=!X.value}))})),(a,t)=>{const e=s,n=l;return o(),d(n,{modelValue:p(X),"onUpdate:modelValue":t[0]||(t[0]=l=>g(X)?X.value=l:null),title:"快捷键介绍"},{default:i((()=>[c("div",b,[c("div",v,[c("div",null,[t[5]||(t[5]=c("h2",{class:"m-0 text-lg font-bold"}," 全局 ",-1)),c("ul",x,[c("li",k,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[1]||(t[1]=[_("I")]))),_:1}),t[2]||(t[2]=_(" 查看系统信息 "))]),p(Y).settings.toolbar.navSearch&&p(Y).settings.navSearch.enableHotkeys?(o(),r("li",A,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[3]||(t[3]=[_("S")]))),_:1}),t[4]||(t[4]=_(" 唤起导航搜索 "))])):y("",!0)])]),p(Y).settings.menu.enableHotkeys&&["side","head"].includes(p(Y).settings.menu.mode)?(o(),r("div",h,[t[8]||(t[8]=c("h2",{class:"m-0 text-lg font-bold"}," 主导航 ",-1)),c("ul",H,[c("li",j,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[6]||(t[6]=[_("`")]))),_:1}),t[7]||(t[7]=_(" 激活下一个主导航 "))])])])):y("",!0),p(Y).settings.tabbar.enable&&p(Y).settings.tabbar.enableHotkeys?(o(),r("div",I,[t[19]||(t[19]=c("h2",{class:"m-0 text-lg font-bold"}," 标签栏 ",-1)),c("ul",S,[c("li",V,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[9]||(t[9]=[_("←")]))),_:1}),t[10]||(t[10]=_(" 切换到上一个标签页 "))]),c("li",W,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[11]||(t[11]=[_("→")]))),_:1}),t[12]||(t[12]=_(" 切换到下一个标签页 "))]),c("li",w,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[13]||(t[13]=[_("W")]))),_:1}),t[14]||(t[14]=_(" 关闭当前标签页 "))]),c("li",D,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[15]||(t[15]=[_("1~9")]))),_:1}),t[16]||(t[16]=_(" 切换到第 n 个标签页 "))]),c("li",J,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[17]||(t[17]=[_("0")]))),_:1}),t[18]||(t[18]=_(" 切换到最后一个标签页 "))])])])):y("",!0),p(Y).settings.mainPage.enableHotkeys?(o(),r("div",K,[t[24]||(t[24]=c("h2",{class:"m-0 text-lg font-bold"}," 页面 ",-1)),c("ul",P,[c("li",R,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[20]||(t[20]=[_("↑")]))),_:1}),t[21]||(t[21]=_(" 最大化 "))]),c("li",U,[m(e,null,{default:i((()=>[_(f("mac"===p(Y).os?"⌥":"Alt"),1)])),_:1}),m(e,null,{default:i((()=>t[22]||(t[22]=[_("↓")]))),_:1}),t[23]||(t[23]=_(" 退出最大化 "))])])])):y("",!0)])])])),_:1},8,["modelValue"])}}});export{X as _};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-BvUZdSGB.js.map
