<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<Localization language="Chinese (Simplified)" description="Chinese (Simplified)" cultureName="zh-CHS">
  <A_WebViewer>
    <AbbreviatedDayFriday>星期五</AbbreviatedDayFriday>
    <AbbreviatedDayMonday>星期一</AbbreviatedDayMonday>
    <AbbreviatedDaySaturday>星期六</AbbreviatedDaySaturday>
    <AbbreviatedDaySunday>星期天</AbbreviatedDaySunday>
    <AbbreviatedDayThursday>星期四</AbbreviatedDayThursday>
    <AbbreviatedDayTuesday>星期二</AbbreviatedDayTuesday>
    <AbbreviatedDayWednesday>星期三</AbbreviatedDayWednesday>
    <Attachment>附件:</Attachment>
    <ButtonNext>下一步</ButtonNext>
    <ButtonPrev>前一步</ButtonPrev>
    <ButtonSend>发送</ButtonSend>
    <CategoryAlreadyExists>该名称类别已存在！</CategoryAlreadyExists>
    <DayFriday>周五</DayFriday>
    <DayMonday>周一</DayMonday>
    <Days>日期</Days>
    <DaySaturday>周六</DaySaturday>
    <DaySunday>周日</DaySunday>
    <DayThursday>周四</DayThursday>
    <DayTuesday>周二</DayTuesday>
    <DayWednesday>周三</DayWednesday>
    <Email>Email:</Email>
    <EmailOptions>邮件选项</EmailOptions>
    <FirstPage>首页</FirstPage>
    <Hours>小时</Hours>
    <LabelFrom>来自:</LabelFrom>
    <LabelSelectExportFormat>选择报表的保存格式:</LabelSelectExportFormat>
    <LabelTo>到:</LabelTo>
    <LastPage>末页</LastPage>
    <Loading>加载...</Loading>
    <Message>消息:</Message>
    <Minutes>分钟</Minutes>
    <MonthApril>4月</MonthApril>
    <MonthAugust>8月</MonthAugust>
    <MonthDecember>12月</MonthDecember>
    <MonthFebruary>2月</MonthFebruary>
    <MonthJanuary>1月</MonthJanuary>
    <MonthJuly>7月</MonthJuly>
    <MonthJune>6月</MonthJune>
    <MonthMarch>3月</MonthMarch>
    <MonthMay>5月</MonthMay>
    <MonthNovember>11月</MonthNovember>
    <MonthOctober>10月</MonthOctober>
    <MonthSeptember>9月</MonthSeptember>
    <NextPage>后页</NextPage>
    <OnePage>一页</OnePage>
    <Page>页</Page>
    <PageOf>of</PageOf>
    <PreviousPage>前页</PreviousPage>
    <PrintContinue>点击继续打印</PrintContinue>
    <PrintReport>打印报表</PrintReport>
    <PrintToPdf>PDF</PrintToPdf>
    <PrintToXps>打印到XPS</PrintToXps>
    <PrintWithoutPreview>直接打印</PrintWithoutPreview>
    <PrintWithPreview>打印预览</PrintWithPreview>
    <SaveReport>保存报表</SaveReport>
    <Subject>主题:</Subject>
    <TabItemContacts>联系方式</TabItemContacts>
    <TextComputer>电脑</TextComputer>
    <TextItemsRoot>根目录</TextItemsRoot>
    <TodayDate>今天</TodayDate>
    <WholeReport>整个报表</WholeReport>
  </A_WebViewer>
  <Adapters>
    <AdapterBusinessObjects>从业务对象中获取数据</AdapterBusinessObjects>
    <AdapterConnection>数据来源 {0}</AdapterConnection>
    <AdapterCrossTabDataSource>数据来自交叉表</AdapterCrossTabDataSource>
    <AdapterCsvFiles>从 CSV File 获取数据</AdapterCsvFiles>
    <AdapterDataTables>从 DataSet, DataTables 获取数据</AdapterDataTables>
    <AdapterDataViews>从 DataViews 获取数据</AdapterDataViews>
    <AdapterDB2Connection>从 IBM DB2 连接获取数据</AdapterDB2Connection>
    <AdapterDBaseFiles>从数据库文件获取数据</AdapterDBaseFiles>
    <AdapterFirebirdConnection>从 Firebird 连接获取数据</AdapterFirebirdConnection>
    <AdapterInformixConnection>数据来自Informix连接 </AdapterInformixConnection>
    <AdapterMySQLConnection>从 MySQL 连接获取数据</AdapterMySQLConnection>
    <AdapterOdbcConnection>从 ODBC 连接获取数据</AdapterOdbcConnection>
    <AdapterOleDbConnection>从 OLEDB 连接获取数据</AdapterOleDbConnection>
    <AdapterOracleConnection>从 Oracle 连接获取数据</AdapterOracleConnection>
    <AdapterOracleODPConnection>数据来自Oracle ODP.NET连接</AdapterOracleODPConnection>
    <AdapterPostgreSQLConnection>从 PostgreSQL 连接获取数据</AdapterPostgreSQLConnection>
    <AdapterSqlCeConnection>从 SQLCe 连接获取数据</AdapterSqlCeConnection>
    <AdapterSqlConnection>从 SQL 连接获取数据</AdapterSqlConnection>
    <AdapterSQLiteConnection>从 SQLite 连接获取数据</AdapterSQLiteConnection>
    <AdapterTeradataConnection>数据来自Teradata连接</AdapterTeradataConnection>
    <AdapterUniDirectConnection>Uni Direct数据库连接</AdapterUniDirectConnection>
    <AdapterUserSources>从用户源获取数据</AdapterUserSources>
    <AdapterVirtualSource>从其他数据源获取数据</AdapterVirtualSource>
    <AdapterVistaDBConnection>从 VistaDB 连接获取数据</AdapterVistaDBConnection>
  </Adapters>
  <BarCode>
    <Post>提交</Post>
    <TwoDimensional>二维的</TwoDimensional>
  </BarCode>
  <Buttons>
    <Add>添加</Add>
    <AddAllColumns>添加所有列</AddAllColumns>
    <Attach>连接</Attach>
    <Build>生成...</Build>
    <Buttons>按键</Buttons>
    <Cancel>取消(&amp;C)</Cancel>
    <Check>校验</Check>
    <Close>关闭</Close>
    <Delete>删除</Delete>
    <Design>设计</Design>
    <Down>向下</Down>
    <Duplicate>重复</Duplicate>
    <Export>导出</Export>
    <ForceDelete>强制删除</ForceDelete>
    <Help>帮助</Help>
    <Install>安装</Install>
    <InvertAll>全部颠倒</InvertAll>
    <LessOptions>减少选项</LessOptions>
    <LoadDataSet>加载数据集(*.data)</LoadDataSet>
    <More>更多</More>
    <MoreApps>更多应用</MoreApps>
    <MoreOptions>更多选项</MoreOptions>
    <MoveLeft>左移</MoveLeft>
    <MoveRight>右移</MoveRight>
    <MoveToResource>移至资源</MoveToResource>
    <No>否</No>
    <Ok>确定(&amp;O)</Ok>
    <Open>打开</Open>
    <OpenContainingFolder>打开父目录</OpenContainingFolder>
    <OpenInSafeMode>在安全模式下打开</OpenInSafeMode>
    <Print>打印</Print>
    <Publish>发布</Publish>
    <QuickPrint>快速打印</QuickPrint>
    <Remove>移除</Remove>
    <RemoveAll>全部移除</RemoveAll>
    <Rename>重命名</Rename>
    <RestoreDefaults>恢复默认值</RestoreDefaults>
    <Reverse>倒转</Reverse>
    <Save>保存</Save>
    <SaveCopy>备份</SaveCopy>
    <SetAll>显示所有</SetAll>
    <ShowLess>显示减少</ShowLess>
    <ShowMore>显示增加</ShowMore>
    <ShowSpecific>显示 '{0}'</ShowSpecific>
    <Sign>签署</Sign>
    <Submit>提交</Submit>
    <SwitchTo>切换到{0}</SwitchTo>
    <Test>测试</Test>
    <TryToReconnect>尝试重新连接</TryToReconnect>
    <Up>向上</Up>
    <Upgrade>升级</Upgrade>
    <UpgradeNow>现在升级</UpgradeNow>
    <Upload>上传</Upload>
    <Waiting>等待</Waiting>
    <Yes>是</Yes>
  </Buttons>
  <Chart>
    <AddCondition>添加条件(&amp;A)</AddCondition>
    <AddConstantLine>添加恒定线</AddConstantLine>
    <AddFilter>添加过滤(&amp;A)</AddFilter>
    <AddSeries>添加(&amp;A)</AddSeries>
    <AddStrip>添加长条</AddStrip>
    <AddTrendLine>添加趋势线</AddTrendLine>
    <Area>面积图</Area>
    <Axes>坐标</Axes>
    <AxisReverse>反转</AxisReverse>
    <AxisX>X 轴</AxisX>
    <AxisY>Y 轴</AxisY>
    <BoxAndWhisker>箱线</BoxAndWhisker>
    <Bubble>气泡图</Bubble>
    <Candlestick>烛台图</Candlestick>
    <ChartConditionsCollectionForm>条件</ChartConditionsCollectionForm>
    <ChartEditorForm>图表编辑器</ChartEditorForm>
    <ChartFiltersCollectionForm>过滤</ChartFiltersCollectionForm>
    <ChartType>图表类型</ChartType>
    <CheckBoxAutoRotation>自动旋转</CheckBoxAutoRotation>
    <ClusteredBar>簇状条形图
</ClusteredBar>
    <ClusteredColumn>簇状柱形图</ClusteredColumn>
    <Common>通用类</Common>
    <ConstantLine>恒定线</ConstantLine>
    <ConstantLinesEditorForm>恒定线编辑器</ConstantLinesEditorForm>
    <DataColumns>数据列</DataColumns>
    <Doughnut>圆环图</Doughnut>
    <Financial>金融类</Financial>
    <FullStackedArea>全堆积面积图</FullStackedArea>
    <FullStackedBar>全堆积条形图</FullStackedBar>
    <FullStackedColumn>全堆积柱形图</FullStackedColumn>
    <FullStackedLine>全堆积折线图</FullStackedLine>
    <FullStackedSpline>全堆积曲线图</FullStackedSpline>
    <FullStackedSplineArea>全堆积曲线面积图</FullStackedSplineArea>
    <Funnel>漏斗图</Funnel>
    <FunnelWeightedSlices>漏斗加权切片</FunnelWeightedSlices>
    <Gantt>甘特图</Gantt>
    <GridInterlaced>隔行</GridInterlaced>
    <GridLines>网格线</GridLines>
    <Histogram>柱状图</Histogram>
    <LabelAlignment>对齐方式:</LabelAlignment>
    <LabelAlignmentHorizontal>水平:</LabelAlignmentHorizontal>
    <LabelAlignmentVertical>垂直:</LabelAlignmentVertical>
    <LabelAngle>角:</LabelAngle>
    <LabelArgumentDataColumn>数据列变量:</LabelArgumentDataColumn>
    <LabelAutoRotation>自动旋转:</LabelAutoRotation>
    <LabelCloseValueDataColumn>收盘值数据列:</LabelCloseValueDataColumn>
    <LabelEndValueDataColumn>成交值数据列:</LabelEndValueDataColumn>
    <LabelHighValueDataColumn>高点值数据列:</LabelHighValueDataColumn>
    <LabelHorizontal>水平:</LabelHorizontal>
    <LabelLowValueDataColumn>低点值数据列:</LabelLowValueDataColumn>
    <LabelMinorCount>Minor Count:</LabelMinorCount>
    <LabelOpenValueDataColumn>开盘值数据列:</LabelOpenValueDataColumn>
    <Labels>标签</Labels>
    <LabelsCenter>居中</LabelsCenter>
    <LabelSeriesName>Series 名称:</LabelSeriesName>
    <LabelsInside>之内</LabelsInside>
    <LabelsInsideBase>基点之内</LabelsInsideBase>
    <LabelsInsideEnd>顶点之内</LabelsInsideEnd>
    <LabelsNone>无</LabelsNone>
    <LabelsOutside>之外</LabelsOutside>
    <LabelsOutsideBase>基点之外</LabelsOutsideBase>
    <LabelsOutsideEnd>顶点之外</LabelsOutsideEnd>
    <LabelsOverlay>覆盖</LabelsOverlay>
    <LabelsStyleCategory>类别</LabelsStyleCategory>
    <LabelsStyleCategoryPercentOfTotal>类别 - 总额百分比</LabelsStyleCategoryPercentOfTotal>
    <LabelsStyleCategoryValue>类别 - 值</LabelsStyleCategoryValue>
    <LabelsStylePercentOfTotal>总额百分比</LabelsStylePercentOfTotal>
    <LabelsStyleValue>值</LabelsStyleValue>
    <LabelsTwoColumns>两栏</LabelsTwoColumns>
    <LabelTextAfter>文本之后:</LabelTextAfter>
    <LabelTextBefore>文本之前:</LabelTextBefore>
    <LabelTitleAlignment>对齐方式:</LabelTitleAlignment>
    <LabelValueDataColumn>数据列值:</LabelValueDataColumn>
    <LabelValueType>值类型:</LabelValueType>
    <LabelVertical>垂直:</LabelVertical>
    <LabelVisible>可见:</LabelVisible>
    <Legend>图例</Legend>
    <LegendSpacing>间距</LegendSpacing>
    <Line>折线图</Line>
    <ListOfValues>列列表</ListOfValues>
    <Marker>标记</Marker>
    <MoveConstantLineDown>下移恒定线</MoveConstantLineDown>
    <MoveConstantLineUp>上移恒定线</MoveConstantLineUp>
    <MoveSeriesDown>下移序列</MoveSeriesDown>
    <MoveSeriesUp>上移序列</MoveSeriesUp>
    <MoveStripDown>下移长条</MoveStripDown>
    <MoveStripUp>上移长条</MoveStripUp>
    <NoConditions>无条件</NoConditions>
    <NoFilters>无过滤</NoFilters>
    <Pareto>Pareto</Pareto>
    <Pictorial>图示</Pictorial>
    <PictorialStacked>图形叠加</PictorialStacked>
    <Pie>饼图</Pie>
    <Radar>雷达图</Radar>
    <RadarArea>雷达面积图</RadarArea>
    <RadarColumn>雷达条形图</RadarColumn>
    <RadarLine>雷达线形图</RadarLine>
    <RadarPoint>雷达点状图</RadarPoint>
    <Range>范围图</Range>
    <RangeBar>范围柱状图</RangeBar>
    <RemoveCondition>移除条件(&amp;R)</RemoveCondition>
    <RemoveConstantLine>移除恒定线</RemoveConstantLine>
    <RemoveFilter>移除过滤(&amp;R)</RemoveFilter>
    <RemoveSeries>移除序列(&amp;R)</RemoveSeries>
    <RemoveStrip>移除长条</RemoveStrip>
    <Ribbon>功能区</Ribbon>
    <RunChartWizard>运行图表 &amp;向导</RunChartWizard>
    <Scatter>矢量图</Scatter>
    <ScatterLine>矢量直线</ScatterLine>
    <ScatterSpline>矢量曲线</ScatterSpline>
    <Series>序列</Series>
    <SeriesColorsCollectionForm>序列颜色</SeriesColorsCollectionForm>
    <SeriesEditorForm>序列编辑器</SeriesEditorForm>
    <Serieses>序列</Serieses>
    <SparklinesArea>区域</SparklinesArea>
    <SparklinesColumn>列</SparklinesColumn>
    <SparklinesLine>行</SparklinesLine>
    <SparklinesWinLoss>赢 / 输</SparklinesWinLoss>
    <Spline>曲线图</Spline>
    <SplineArea>曲线面积图</SplineArea>
    <SplineRange>平滑曲边范围图</SplineRange>
    <StackedArea>堆积面积图</StackedArea>
    <StackedBar>堆积条形图</StackedBar>
    <StackedColumn>堆积柱形图</StackedColumn>
    <StackedLine>堆积折线图</StackedLine>
    <StackedSpline>堆积曲线图</StackedSpline>
    <StackedSplineArea>堆积曲线面积图</StackedSplineArea>
    <SteppedArea>阶梯面积图</SteppedArea>
    <SteppedLine>阶梯折线图</SteppedLine>
    <SteppedRange>阶梯范围图</SteppedRange>
    <Stock>股票图</Stock>
    <Strip>长条</Strip>
    <StripsEditorForm>长条编辑表单</StripsEditorForm>
    <Style>样式</Style>
    <Sunburst>射线图</Sunburst>
    <Treemap>树状图</Treemap>
    <TrendLinesEditorForm>趋势线编辑器</TrendLinesEditorForm>
    <Waterfall>瀑布图</Waterfall>
  </Chart>
  <CharterMapEditor>
    <Characters>字符</Characters>
  </CharterMapEditor>
  <ChartRibbon>
    <Axes>轴</Axes>
    <AxesArrowStyle>箭头样式</AxesArrowStyle>
    <AxesArrowStyleLines>直线</AxesArrowStyleLines>
    <AxesArrowStyleNone>无</AxesArrowStyleNone>
    <AxesArrowStyleTriangle>三角形</AxesArrowStyleTriangle>
    <AxesLabel>标签位置</AxesLabel>
    <AxesLabelsNone>无</AxesLabelsNone>
    <AxesLabelsOneLine>单线</AxesLabelsOneLine>
    <AxesLabelsTwoLines>双线</AxesLabelsTwoLines>
    <AxesReverseHorizontal>水平反转</AxesReverseHorizontal>
    <AxesReverseVertical>垂直反转</AxesReverseVertical>
    <AxesTicks>刻度</AxesTicks>
    <AxesTicksMajor>主刻度</AxesTicksMajor>
    <AxesTicksMinor>次刻度</AxesTicksMinor>
    <AxesTicksNone>无</AxesTicksNone>
    <AxesVisible>可见</AxesVisible>
    <AxesXAxis>X轴</AxesXAxis>
    <AxesXTopAxis>上X轴</AxesXTopAxis>
    <AxesYAxis>Y 轴</AxesYAxis>
    <AxesYRightAxis>右Y轴</AxesYRightAxis>
    <CenterLabels>中心</CenterLabels>
    <ChangeType>更改类型</ChangeType>
    <GridLines>网格线</GridLines>
    <GridLinesHorizontal>水平网格线</GridLinesHorizontal>
    <GridLinesVertical>垂直网格线</GridLinesVertical>
    <HorAlCenter>&lt;b&gt;中心叠加图例&lt;/b&gt;&lt;br&gt;在图表中心显示图例&lt;/br&gt;&lt;br&gt;不改变尺寸&lt;/br&gt;</HorAlCenter>
    <HorAlLeft>&lt;b&gt;左侧叠加图例&lt;/b&gt;&lt;br&gt;在图表左侧显示图例;&lt;/br&gt;&lt;br&gt;不改变尺寸;&lt;/br&gt;</HorAlLeft>
    <HorAlLeftOutside>&lt;b&gt;左侧显示图例&lt;/b&gt;&lt;br&gt;显示图例并左对齐&lt;/br&gt;</HorAlLeftOutside>
    <HorAlRight>&lt;b&gt;右侧叠加图层图例&lt;/b&gt;&lt;br&gt;在图表右侧显示图例&lt;/br&gt;&lt;br&gt;图表左侧显示图例&lt;/br&gt;</HorAlRight>
    <HorAlRightOutside>&lt;b&gt;右侧显示图例&lt;/b&gt;&lt;br&gt;显示图例并右对齐&lt;/br&gt;</HorAlRightOutside>
    <HorizontalMajor>&lt;b&gt;主要的&lt;/b&gt;&lt;br&gt;为主单位显示水平网格线&lt;/br&gt;</HorizontalMajor>
    <HorizontalMajorMinor>&lt;b&gt;主要&amp;&amp;次要网格线&lt;/b&gt;&lt;br&gt;为主单位和次单位显示水平网格线&lt;/br&gt;</HorizontalMajorMinor>
    <HorizontalMinor>&lt;b&gt;次要&lt;/b&gt;&lt;br&gt;为次要单位显示水平网格线&lt;/br&gt;</HorizontalMinor>
    <HorizontalNone>&lt;b&gt;无&lt;/b&gt;&lt;br&gt;不显示水平网格线&lt;/br&gt;</HorizontalNone>
    <InsideBaseLabels>内侧面</InsideBaseLabels>
    <InsideEndLabels>内侧端</InsideEndLabels>
    <Labels>系列标签</Labels>
    <Legend>图例</Legend>
    <LegendHorizontalAlignment>水平对齐</LegendHorizontalAlignment>
    <LegendMarker>标记</LegendMarker>
    <LegendMarkerAlignmentLeft>左对齐</LegendMarkerAlignmentLeft>
    <LegendMarkerAlignmentRight>右对齐</LegendMarkerAlignmentRight>
    <LegendMarkerVisible>可见</LegendMarkerVisible>
    <LegendVerticalAlignment>垂直对齐</LegendVerticalAlignment>
    <LegendVisible>可见</LegendVisible>
    <NoneLabels>无</NoneLabels>
    <OutsideBaseLabels>外侧面</OutsideBaseLabels>
    <OutsideEndLabels>外侧端</OutsideEndLabels>
    <OutsideLabels>外侧</OutsideLabels>
    <ribbonBarAxis>轴</ribbonBarAxis>
    <ribbonBarChartStyles>图表样式</ribbonBarChartStyles>
    <ribbonBarChartType>图表类型</ribbonBarChartType>
    <ribbonBarLabels>标签</ribbonBarLabels>
    <ribbonBarLegend>图例</ribbonBarLegend>
    <Style>更改样式</Style>
    <TwoColumnsPieLabels>两列</TwoColumnsPieLabels>
    <VertAlBottom>&lt;b&gt;底部叠加图例&lt;/b&gt;&lt;br&gt;在图表底部显示图例&lt;/br&gt;&lt;br&gt;不改变尺寸&lt;/br&gt;</VertAlBottom>
    <VertAlBottomOutside>&lt;b&gt;底部显示图例&lt;/b&gt;&lt;br&gt;显示图例并底边对齐&lt;/br&gt;</VertAlBottomOutside>
    <VertAlCenter>&lt;b&gt;中心叠加图例&lt;/b&gt;&lt;br&gt;图表中心显示图例&lt;/br&gt;&lt;br&gt;不改变尺寸&lt;/br&gt;</VertAlCenter>
    <VertAlTop>&lt;b&gt;顶部叠加图例&lt;/b&gt;&lt;br&gt;在图表顶部显示图例&lt;/br&gt;&lt;br&gt;without resizing&lt;/br&gt;</VertAlTop>
    <VertAlTopOutside>&lt;b&gt;顶部显示图例&lt;/b&gt;&lt;br&gt;显示图例并顶部对齐&lt;/br&gt;</VertAlTopOutside>
    <VerticalMajor>&lt;b&gt;主要&lt;/b&gt;&lt;br&gt;为主单位显示垂直网格线&lt;/br&gt;</VerticalMajor>
    <VerticalMajorMinor>&lt;b&gt;主 &amp;&amp; 次网格线&lt;/b&gt;&lt;br&gt;为主次单为显示垂直网格线&lt;/br&gt;</VerticalMajorMinor>
    <VerticalMinor>&lt;b&gt;Min次要&lt;/b&gt;&lt;br&gt;为次单位显示垂直网格线&lt;/br&gt;</VerticalMinor>
    <VerticalNone>&lt;b&gt;无&lt;/b&gt;&lt;br&gt;不显示垂直网格线&lt;/br&gt;</VerticalNone>
  </ChartRibbon>
  <Cloud>
    <AcceptTermsAndPrivacyPolicy>我已阅读并接受 {0} 和 {1}</AcceptTermsAndPrivacyPolicy>
    <Account>账户</Account>
    <AccountSettings>帐户设置</AccountSettings>
    <AddAPlace>增加一个位置</AddAPlace>
    <AreYouSureYouWantDeleteReport>您确定要从磁盘中删除"{0}" 报告吗?</AreYouSureYouWantDeleteReport>
    <Authorize>授权</Authorize>
    <AuthorizeWithLicenseKey>许可证密钥授权</AuthorizeWithLicenseKey>
    <ButtonChangePassword>更改密码</ButtonChangePassword>
    <ButtonDeleteAll>全部删除</ButtonDeleteAll>
    <ButtonDesign>设计报表</ButtonDesign>
    <ButtonLater>请稍后</ButtonLater>
    <ButtonLogInWith>登录 {0}</ButtonLogInWith>
    <ButtonLogout>注销</ButtonLogout>
    <ButtonPublish>发布</ButtonPublish>
    <ButtonPurchase>购买</ButtonPurchase>
    <ButtonRecover>恢复</ButtonRecover>
    <ButtonRenew>更新</ButtonRenew>
    <ButtonResendEmail>重发邮件</ButtonResendEmail>
    <ButtonResetPassword>重置密码</ButtonResetPassword>
    <ButtonRun>运行</ButtonRun>
    <ButtonScheduler>调度</ButtonScheduler>
    <ButtonShare>分享</ButtonShare>
    <ButtonSignUp>注册</ButtonSignUp>
    <ButtonSignUpWith>注册{0}</ButtonSignUpWith>
    <ButtonSkip>跳过</ButtonSkip>
    <ButtonView>视图</ButtonView>
    <ButtonWhereUsed>用于何处？</ButtonWhereUsed>
    <Cancel>取消</Cancel>
    <CheckBoxMoveToRecycleBin>移至回收站</CheckBoxMoveToRecycleBin>
    <CheckBoxRememberMe>记住账号</CheckBoxRememberMe>
    <CheckForUpdate>检查更新</CheckForUpdate>
    <Cloud>云端</Cloud>
    <Collection>集合</Collection>
    <Create>创建</Create>
    <CreateError>创建报表时出错！</CreateError>
    <CreateNewCollection>创建新集合</CreateNewCollection>
    <CreateYourTeam>创建您的团队</CreateYourTeam>
    <CreatingReport>创建报表...</CreatingReport>
    <DashboardWindowTitleNew>新仪表盘</DashboardWindowTitleNew>
    <DeleteFile>删除文档</DeleteFile>
    <DoNotAskMe>不再询问</DoNotAskMe>
    <EnterUserNameToLogin>输入用户名（电子邮件）以登录</EnterUserNameToLogin>
    <ExecutionError>执行错误</ExecutionError>
    <ExpiredDate>日期已失效</ExpiredDate>
    <FileStorageWindowTitleEdit>编辑文件存储</FileStorageWindowTitleEdit>
    <FileStorageWindowTitleNew>新文件存储</FileStorageWindowTitleNew>
    <FolderWindowTitleEdit>编辑文件夹</FolderWindowTitleEdit>
    <FolderWindowTitleNew>新文件夹</FolderWindowTitleNew>
    <ForExample>例如</ForExample>
    <GroupBoxAttachedItems>附加项目</GroupBoxAttachedItems>
    <HyperlinkAgreeToTerms>注册即表示您同意条款</HyperlinkAgreeToTerms>
    <HyperlinkAlreadyHaveAccount>已有账户</HyperlinkAlreadyHaveAccount>
    <HyperlinkForgotPassword>忘记密码</HyperlinkForgotPassword>
    <HyperlinkHavePassword>有密码</HyperlinkHavePassword>
    <HyperlinkRegisterAccount>注册免费账户</HyperlinkRegisterAccount>
    <InstallSamples>示例安装</InstallSamples>
    <Invitations>邀请函</Invitations>
    <Invite>邀请</Invite>
    <LabelAddCloudFolder>连接您的Stimulsoft云文件夹与报表,仪表盘</LabelAddCloudFolder>
    <LabelAddFolder>添加含报表和仪表盘的本地文件夹</LabelAddFolder>
    <labelCollectionName>集合名称:</labelCollectionName>
    <LabelCreated>创建:</LabelCreated>
    <LabelCreateFolder>创建一个可以放置其他项目的新类别</LabelCreateFolder>
    <LabelCreateNewDashboard>创建新仪表盘</LabelCreateNewDashboard>
    <LabelCreateReportTemplate>创建报告模板</LabelCreateReportTemplate>
    <LabelCurrentPassword>当前密码:</LabelCurrentPassword>
    <LabelDataFile>数据文件:</LabelDataFile>
    <LabelDataUrl>数据网址:</LabelDataUrl>
    <LabelEndDate>结束日期:</LabelEndDate>
    <labelFileName>文档名称:</labelFileName>
    <LabelForeground>前景:</LabelForeground>
    <LabelFromReport>来自报告</LabelFromReport>
    <LabelFromReportCode>来自报告 (define in code)</LabelFromReportCode>
    <LabelInviteUser>邀请新用户加入您的团队。给他们的电子邮件发送邀请函。</LabelInviteUser>
    <LabelLastLogin>最后一次登录:</LabelLastLogin>
    <LabelLastTime>最后一次:</LabelLastTime>
    <LabelModified>修改:</LabelModified>
    <LabelNewPassword>新密码:</LabelNewPassword>
    <LabelNextTime>下一次:</LabelNextTime>
    <labelPassword>密码:</labelPassword>
    <LabelPermission>许可:</LabelPermission>
    <LabelPicture>图片:</LabelPicture>
    <LabelRenderedReport>已渲染的报表</LabelRenderedReport>
    <LabelResponseAsFile>响应为文件</LabelResponseAsFile>
    <LabelResultType>结果类型:</LabelResultType>
    <LabelSeparateReport>单独的报表（和脚本）文件</LabelSeparateReport>
    <LabelShowReport>显示报告:</LabelShowReport>
    <LabelStartDate>开始日期:</LabelStartDate>
    <labelUserName>用户名:</labelUserName>
    <License>许可证</License>
    <LicenseInformation>许可证信息</LicenseInformation>
    <LicenseKey>许可证密钥</LicenseKey>
    <Login>登陆</Login>
    <MyTeam>我的团队</MyTeam>
    <NofM>{0}: {2} 的 {1}</NofM>
    <Open>打开</Open>
    <OpenFile>打开文档</OpenFile>
    <OperationCreate>创建 '{0}'</OperationCreate>
    <OperationDelete>从服务器上删除 '{0}' </OperationDelete>
    <OperationDownload>从服务器下载</OperationDownload>
    <OperationGetList>从服务器获得文档列表</OperationGetList>
    <OperationLogin>登陆服务器</OperationLogin>
    <OperationRename>重命名 '{0}' 到 '{1}'</OperationRename>
    <OperationUpload>上传 '{0}' 到服务器</OperationUpload>
    <page>页面</page>
    <Platforms>平台</Platforms>
    <Port>端口</Port>
    <PrivacyPolicy>隐私</PrivacyPolicy>
    <Products>产品</Products>
    <Proxy>代理</Proxy>
    <PublishMessage>要发布此报告，您应将其提交至我们的服务器publish.stimulsoft.com</PublishMessage>
    <questionOpenThisFile>你确定要打开 '{0}'项吗 ?</questionOpenThisFile>
    <questionOverrideItem>你真的想要覆盖 '{0}' 项吗?</questionOverrideItem>
    <questionRemoveItem>你确定要移除 '{0}'项吗 ?</questionRemoveItem>
    <RefreshList>刷新列表</RefreshList>
    <ReportDocumentFormatNotRecognized>“{ 0 }”项目的格式不能被识别为被渲染的报表格式!</ReportDocumentFormatNotRecognized>
    <ReportTemplateFormatNotRecognized>“{ 0 }”项目的格式不能被识别为报告模板格式!</ReportTemplateFormatNotRecognized>
    <RequestChangesWhenSavingToCloud>保存到云端时请求更改</RequestChangesWhenSavingToCloud>
    <RibbonButtonAddRole>增加角色</RibbonButtonAddRole>
    <RibbonButtonAddUser>增加用户</RibbonButtonAddUser>
    <RibbonButtonAddUserParameter>添加用户参数</RibbonButtonAddUserParameter>
    <RibbonButtonAddWorkspace>增加工作区</RibbonButtonAddWorkspace>
    <RibbonButtonFolder>文件夹</RibbonButtonFolder>
    <RibbonTabUsers>用户</RibbonTabUsers>
    <Root>根</Root>
    <RootFolder>根文件夹</RootFolder>
    <Save>保存</Save>
    <SaveAccountSettings>保存帐户设置</SaveAccountSettings>
    <SaveAsType>保存类型</SaveAsType>
    <SaveFile>保存文档</SaveFile>
    <SavingToStimulsoftCloudPleaseWait>正在将该报告保存至 Stimulsoft 云端，请耐心等待!</SavingToStimulsoftCloudPleaseWait>
    <SearchForOnlineTemplates>搜索在线模板</SearchForOnlineTemplates>
    <ShareWindowTitleNew>分享</ShareWindowTitleNew>
    <ShowAllFiles>显示所有文档</ShowAllFiles>
    <ShowNotificationMessages>显示通知信息</ShowNotificationMessages>
    <Submissions>提交</Submissions>
    <Subscriptions>技术服务</Subscriptions>
    <TabItemEmbedCode>嵌入代码</TabItemEmbedCode>
    <TabItemQRCode>二维码</TabItemQRCode>
    <TabItemShare>分享链接</TabItemShare>
    <Team>团队</Team>
    <TermsOfUse>条款</TermsOfUse>
    <TextActivated>激活</TextActivated>
    <TextActivationDate>激活日期</TextActivationDate>
    <TextDelete>删除</TextDelete>
    <TextDeletingItems>删除项目...</TextDeletingItems>
    <TextDescriptionChanges>请键入更改说明:</TextDescriptionChanges>
    <TextFirstName>名字</TextFirstName>
    <TextFromTo>从 {0} 到{1} (Total - {2})</TextFromTo>
    <TextItemsWorkspace>工作区</TextItemsWorkspace>
    <TextLastName>姓</TextLastName>
    <TextModify>修改</TextModify>
    <TextNewsLetters>函件通知</TextNewsLetters>
    <TextNoFavoriteFiles>无收藏文件夹</TextNoFavoriteFiles>
    <TextNoFiles>无文件夹</TextNoFiles>
    <TextNoNotifications>没有通知</TextNoNotifications>
    <TextNoRecentFiles>无最近文件夹</TextNoRecentFiles>
    <TextOwner>所有者</TextOwner>
    <TextProfile>简况</TextProfile>
    <TextReports>报表</TextReports>
    <TextRestoringItems>恢复项目...</TextRestoringItems>
    <TextRole>角色</TextRole>
    <TextRun>运行</TextRun>
    <TextUser>用户</TextUser>
    <TextUserName>用户名 (Email)</TextUserName>
    <TimeHoursAgoFive>五小时前, {0:t}</TimeHoursAgoFive>
    <TimeHoursAgoFour>四小时前, {0:t}</TimeHoursAgoFour>
    <TimeHoursAgoOne>一小时前, {0:t}</TimeHoursAgoOne>
    <TimeHoursAgoThree>三小时前, {0:t}</TimeHoursAgoThree>
    <TimeHoursAgoTwo>两小时前, {0:t}</TimeHoursAgoTwo>
    <TimeMinutesAgoFive>五分钟前, {0:t}</TimeMinutesAgoFive>
    <TimeMinutesAgoFour>四分钟前, {0:t}</TimeMinutesAgoFour>
    <TimeMinutesAgoLessOne>不到一分钟前</TimeMinutesAgoLessOne>
    <TimeMinutesAgoN>{0} 分钟前, {1:t}</TimeMinutesAgoN>
    <TimeMinutesAgoOne>一分钟前o, {0:t}</TimeMinutesAgoOne>
    <TimeMinutesAgoThree>三分钟前, {0:t}</TimeMinutesAgoThree>
    <TimeMinutesAgoTwo>两分钟前, {0:t}</TimeMinutesAgoTwo>
    <TimeToday>今天, {0:t}</TimeToday>
    <TimeYesterday>昨天, {0:t}</TimeYesterday>
    <ToolTipAddRole>在工作区创建新角色.</ToolTipAddRole>
    <ToolTipAddUser>在所选角色中创建新用户.</ToolTipAddUser>
    <ToolTipAspNet>将所选报表发送到ASP.NET应用程序.</ToolTipAspNet>
    <ToolTipAspNetMvc>将所选报表发送到ASP.NET MVC应用程序.</ToolTipAspNetMvc>
    <ToolTipAttach>将项目附加到所选报表.</ToolTipAttach>
    <ToolTipCreate>新建项目.</ToolTipCreate>
    <ToolTipDelete>删除所选项目.</ToolTipDelete>
    <ToolTipDeleted>显示已删除的项目</ToolTipDeleted>
    <ToolTipDownload>下载所选项目.</ToolTipDownload>
    <ToolTipEdit>编辑所选项目.</ToolTipEdit>
    <ToolTipGridMode>列表显示</ToolTipGridMode>
    <ToolTipInfo>查看详情</ToolTipInfo>
    <ToolTipJs>将所选报表发布为HTML页面</ToolTipJs>
    <ToolTipPublish>准备即将部署在应用程序中的所选报表.</ToolTipPublish>
    <ToolTipRecover>恢复所选项目.</ToolTipRecover>
    <ToolTipRunWithoutPreview>无需预览渲染所选报表</ToolTipRunWithoutPreview>
    <ToolTipShare>分享所选文件.</ToolTipShare>
    <ToolTipSort>给选项排序</ToolTipSort>
    <ToolTipThumbnailMode>缩略图视图</ToolTipThumbnailMode>
    <ToolTipViewFile>显示所选文件.</ToolTipViewFile>
    <ToolTipViewReport>在查看器中显示所选报表.</ToolTipViewReport>
    <WeDidntFindAnything>我们在这里找不到任何要展示的东西.</WeDidntFindAnything>
    <WindowDescriptionDelete>您确定要删除所选项目吗?</WindowDescriptionDelete>
    <WindowDescriptionRecover>所选项目将从回收站中恢复.</WindowDescriptionRecover>
    <WindowTitleDelete>删除项目</WindowTitleDelete>
    <WindowTitleForgotPassword>忘记密码</WindowTitleForgotPassword>
    <WindowTitleLogin>登录</WindowTitleLogin>
    <WindowTitleRecover>恢复项目</WindowTitleRecover>
    <WindowTitleRoleEdit>编辑角色</WindowTitleRoleEdit>
    <WindowTitleRoleNew>新建角色</WindowTitleRoleNew>
    <WindowTitleSignUp>注册</WindowTitleSignUp>
    <WindowTitleUserEdit>编辑用户</WindowTitleUserEdit>
    <WindowTitleUserNew>新建用户</WindowTitleUserNew>
    <WindowTitleUserParameterEdit>编辑用户参数</WindowTitleUserParameterEdit>
    <WindowTitleUserParameterNew>新用户参数</WindowTitleUserParameterNew>
    <WindowTitleWorkspaceEdit>编辑工作区</WindowTitleWorkspaceEdit>
    <WindowTitleWorkspaceNew>新建工作区</WindowTitleWorkspaceNew>
    <WizardBlankReportDescription>新建空白报表</WizardBlankReportDescription>
    <WizardExcelDescription>使用Excel数据新建报表.</WizardExcelDescription>
    <WizardJsonDescription>使用JSON数据新建报表.</WizardJsonDescription>
    <WizardPrivateShare>禁止访问</WizardPrivateShare>
    <WizardPrivateShareDescription>对项目的外部访问受到限制.</WizardPrivateShareDescription>
    <WizardPublicShare>公共访问</WizardPublicShare>
    <WizardPublicShareDescription>任何未授权用户的外部访问.</WizardPublicShareDescription>
    <WizardRegisteredShare>授权访问</WizardRegisteredShare>
    <WizardRegisteredShareDescription>仅对任何工作区的注册用户进行外部访问.</WizardRegisteredShareDescription>
    <WizardTeamShare>团队访问</WizardTeamShare>
    <WizardTeamShareDescription>仅供一个工作空间的用户进行外部访问</WizardTeamShareDescription>
    <WizardXmlDescription>使用XML数据创建报表.</WizardXmlDescription>
    <YourPlan>您的计划</YourPlan>
  </Cloud>
  <Components>
    <StiBarCode>条形码</StiBarCode>
    <StiButton>按钮</StiButton>
    <StiCards>卡片</StiCards>
    <StiChart>图表</StiChart>
    <StiCheckBox>复选框</StiCheckBox>
    <StiChildBand>子级区</StiChildBand>
    <StiClone>克隆</StiClone>
    <StiColumnFooterBand>栏尾</StiColumnFooterBand>
    <StiColumnHeaderBand>栏首</StiColumnHeaderBand>
    <StiComboBox>组合框</StiComboBox>
    <StiComponent>组件</StiComponent>
    <StiContainer>容器</StiContainer>
    <StiContourText>交互式轮廓文本</StiContourText>
    <StiCrossColumn>交叉-列</StiCrossColumn>
    <StiCrossColumnTotal>交叉-列合计</StiCrossColumnTotal>
    <StiCrossDataBand>交叉-数据区</StiCrossDataBand>
    <StiCrossFooterBand>交叉-页脚</StiCrossFooterBand>
    <StiCrossGroupFooterBand>交叉-分组页脚</StiCrossGroupFooterBand>
    <StiCrossGroupHeaderBand>交叉-分组页眉</StiCrossGroupHeaderBand>
    <StiCrossHeaderBand>交叉-页眉</StiCrossHeaderBand>
    <StiCrossRow>交叉-行</StiCrossRow>
    <StiCrossRowTotal>交叉-行合计</StiCrossRowTotal>
    <StiCrossSummary>交叉-合计</StiCrossSummary>
    <StiCrossSummaryHeader>交叉汇总头</StiCrossSummaryHeader>
    <StiCrossTab>交叉-Tab</StiCrossTab>
    <StiCrossTitle>交叉标题</StiCrossTitle>
    <StiDashboard>仪表盘</StiDashboard>
    <StiDataBand>数据区</StiDataBand>
    <StiDatePicker>日期选择器</StiDatePicker>
    <StiElectronicSignature>电子签名</StiElectronicSignature>
    <StiEmptyBand>空白区</StiEmptyBand>
    <StiFooterBand>页脚</StiFooterBand>
    <StiGauge>仪表</StiGauge>
    <StiGroupFooterBand>分组页脚</StiGroupFooterBand>
    <StiGroupHeaderBand>分组页眉</StiGroupHeaderBand>
    <StiHeaderBand>页眉</StiHeaderBand>
    <StiHierarchicalBand>分级区</StiHierarchicalBand>
    <StiHorizontalLinePrimitive>横线</StiHorizontalLinePrimitive>
    <StiImage>图片</StiImage>
    <StiIndicator>指示符</StiIndicator>
    <StiListBox>列表框</StiListBox>
    <StiMap>地图</StiMap>
    <StiMathFormula>数学公式</StiMathFormula>
    <StiMultipleSelection>多选</StiMultipleSelection>
    <StiNumberBox>数字框</StiNumberBox>
    <StiOnlineMap>在线地图</StiOnlineMap>
    <StiOverlayBand>覆盖</StiOverlayBand>
    <StiPage>页</StiPage>
    <StiPageFooterBand>页脚</StiPageFooterBand>
    <StiPageHeaderBand>页眉</StiPageHeaderBand>
    <StiPanel>面板</StiPanel>
    <StiPdfDigitalSignature>PDF数字签名</StiPdfDigitalSignature>
    <StiPdfDigitalSignatureElement>PDF元素</StiPdfDigitalSignatureElement>
    <StiPivotColumn>关键列</StiPivotColumn>
    <StiPivotRow>关键行</StiPivotRow>
    <StiPivotSummary>关键总结</StiPivotSummary>
    <StiPivotTable>关键表格</StiPivotTable>
    <StiProgress>进程</StiProgress>
    <StiRectanglePrimitive>矩形</StiRectanglePrimitive>
    <StiRegionMap>区域地图</StiRegionMap>
    <StiReport>报表</StiReport>
    <StiReportSummaryBand>报表合计区</StiReportSummaryBand>
    <StiReportTitleBand>报表标题区</StiReportTitleBand>
    <StiRichText>富文本</StiRichText>
    <StiRoundedRectanglePrimitive>圆角矩形</StiRoundedRectanglePrimitive>
    <StiScreen>屏幕</StiScreen>
    <StiShape>形状</StiShape>
    <StiSignature>署名</StiSignature>
    <StiSingleSelection>单选</StiSingleSelection>
    <StiSparkline> 火花谱线</StiSparkline>
    <StiSubReport>子报表</StiSubReport>
    <StiSystemText>系统文本</StiSystemText>
    <StiTable>表格</StiTable>
    <StiTableOfContents> 目录 </StiTableOfContents>
    <StiText>文本</StiText>
    <StiTextInCells>单元格文本</StiTextInCells>
    <StiTreeView>树视图</StiTreeView>
    <StiTreeViewBox>树视图框</StiTreeViewBox>
    <StiVerticalLinePrimitive>竖线</StiVerticalLinePrimitive>
    <StiWinControl>Windows 控件</StiWinControl>
    <StiZipCode>邮政编码</StiZipCode>
  </Components>
  <CustomMapWindow>
    <CheckedAll>全选</CheckedAll>
    <HideText>隐藏文本</HideText>
    <KeysChangePosition>键(左，上，右，下)改变文本的位置  </KeysChangePosition>
    <KeysChangeSize>Shift +键(左，上，右，下)改变文本区域的大小  </KeysChangeSize>
    <UncheckedAll>取消全选</UncheckedAll>
  </CustomMapWindow>
  <Dashboard>
    <AddRange>添加范围</AddRange>
    <AfterGroupingData>分组数据后应用</AfterGroupingData>
    <AfterSortingData>在分组和整理数据后应用</AfterSortingData>
    <AllowUserColumnSelection>允许用户选择列</AllowUserColumnSelection>
    <AllowUserDrillDown>允许用户下行查看</AllowUserDrillDown>
    <AllowUserFiltering>允许用户筛选</AllowUserFiltering>
    <AllowUserSorting>允许用户排序</AllowUserSorting>
    <BeforeTransformation>在数据转换之前应用</BeforeTransformation>
    <Blank>(空白)</Blank>
    <Blanks>(Blanks)</Blanks>
    <BooleanFilters>布尔过滤器</BooleanFilters>
    <CannotLoadDashboard>无法使用仪表盘加载报表.</CannotLoadDashboard>
    <ChangeChartType>更改图表类型</ChangeChartType>
    <ChangeMapType>更改地图类型</ChangeMapType>
    <ClearAllFormatting>清除所有格式</ClearAllFormatting>
    <ClearFilterFrom>从 '{0}'清除过滤器</ClearFilterFrom>
    <ColorScale>色标</ColorScale>
    <ColumnInteractions>纵列交互</ColumnInteractions>
    <CustomFilter>自定义 &amp;过滤器...</CustomFilter>
    <DashboardsNotSupportedIntheWPFDesigner>WPF设计器不支持仪表板.</DashboardsNotSupportedIntheWPFDesigner>
    <DataBars>数据条</DataBars>
    <DataFilterGrouping>此操作仅用于对不同的列进行分组.</DataFilterGrouping>
    <DataNotDefined>数据未定义</DataNotDefined>
    <DateFilters>日期过滤器</DateFilters>
    <Dimension>维</Dimension>
    <Dimensions>维</Dimensions>
    <DragDropBlocks>从面板中拖放</DragDropBlocks>
    <DragDropData>拖动 &amp; 丢弃数据</DragDropData>
    <DragDropDataFromDictionary>拖&amp;从字典中删除数据</DragDropDataFromDictionary>
    <DrillDown>下行</DrillDown>
    <DrillDownFiltered>向下过滤</DrillDownFiltered>
    <DrillDownSelected>下行选择</DrillDownSelected>
    <DrillUp>上行</DrillUp>
    <DuplicateField>重复字段</DuplicateField>
    <EditExpression>编辑表达式</EditExpression>
    <EditField>编辑字段</EditField>
    <EmptyDashboardFooter>拖 &amp; 从工具箱中删除组件或从字典中删除数据</EmptyDashboardFooter>
    <EmptyDashboardHeader>仪表盘为空</EmptyDashboardHeader>
    <FieldInteractions>字段交互</FieldInteractions>
    <FieldTypeRestrictionHint>只能使用 '{0}' 类型字段!</FieldTypeRestrictionHint>
    <FirstLastPoints>第一/最后一点</FirstLastPoints>
    <FirstRowIndex>第一行索引</FirstRowIndex>
    <FullRowSelect>全行选择</FullRowSelect>
    <HighLowPoints>高 / 低点</HighLowPoints>
    <ImageNotSpecified>未指定图像</ImageNotSpecified>
    <Indicator>指示器</Indicator>
    <InitialValue>初始值</InitialValue>
    <LimitRows>跳过和限制行</LimitRows>
    <Measure>测量</Measure>
    <Measures>测量</Measures>
    <Metrics>指标</Metrics>
    <NewDimension>新尺寸</NewDimension>
    <NewField>新领域</NewField>
    <NewMeasure>新测量</NewMeasure>
    <NoRanges>没有范围</NoRanges>
    <NoResult>没有结果</NoResult>
    <NSelected>{0} 选择</NSelected>
    <Nulls>(Nulls)</Nulls>
    <NumberFilters>数字过滤器</NumberFilters>
    <ParentElement>父元素</ParentElement>
    <Priority>优先</Priority>
    <RangeMode>范围模式</RangeMode>
    <RangeType>范围类型</RangeType>
    <RemoveActions>R&amp;删除动作</RemoveActions>
    <RemoveAllFields>删除所有字段</RemoveAllFields>
    <RemoveField>删除字段</RemoveField>
    <RemoveMobileSurface>Remove Mobile Surface</RemoveMobileSurface>
    <ReplaceValues>替换值</ReplaceValues>
    <ReportSnapshot>报告快照</ReportSnapshot>
    <RowsCount>行数</RowsCount>
    <RunFieldsEditor>运行字段编辑器</RunFieldsEditor>
    <RunFieldsEditorInfo>运行字段编辑器来更改每个表纵列的交互。</RunFieldsEditorInfo>
    <SelectAll>(Select &amp;All)</SelectAll>
    <ShowAllValue>显示 (All)值</ShowAllValue>
    <ShowAsPercentages>显示为百分比</ShowAsPercentages>
    <ShowBlanks>显示空白 </ShowBlanks>
    <SkipFirstRows>跳过第一行</SkipFirstRows>
    <SortAZ>从A到Z排序</SortAZ>
    <SortLargestToSmallest>从最大到最小排序</SortLargestToSmallest>
    <SortNewestToOldest>从最新到最旧排序</SortNewestToOldest>
    <SortOldestToNewest>从最旧到最新排序</SortOldestToNewest>
    <SortSmallestToLargest>从最小到最大排序</SortSmallestToLargest>
    <SortZA>从Z到A排序</SortZA>
    <Sparklines>走势图</Sparklines>
    <StringFilters>字符串筛选器</StringFilters>
    <TransformationHint>单击表标题去应用排序，过滤或更改数据。此转换仅适用于此数据项目。</TransformationHint>
    <Trend>趋向</Trend>
    <ViewEditCompilationNotice>您不能编辑 “Report.CalculationMode” 属性设置为 “Compilation” 的报表。请将该属性设置为“Interpretation”</ViewEditCompilationNotice>
    <ViewModeDesktop>桌面端</ViewModeDesktop>
    <ViewModeMobile>移动端</ViewModeMobile>
  </Dashboard>
  <Database>
    <Connection>连接</Connection>
    <Database>{0}连接</Database>
    <DatabaseDB2>IBM DB2 连接</DatabaseDB2>
    <DatabaseFirebird>Firebird 连接</DatabaseFirebird>
    <DatabaseInformix>Informix连接</DatabaseInformix>
    <DatabaseJson>JSON数据</DatabaseJson>
    <DatabaseMySQL>MySQL 连接</DatabaseMySQL>
    <DatabaseOdbc>ODBC 连接</DatabaseOdbc>
    <DatabaseOleDb>OLE DB 连接</DatabaseOleDb>
    <DatabaseOracle>Oracle 连接</DatabaseOracle>
    <DatabaseOracleODP>Oracle ODP.NET连接</DatabaseOracleODP>
    <DatabasePostgreSQL>PostgreSQL 连接</DatabasePostgreSQL>
    <DatabaseSql>SQL 连接</DatabaseSql>
    <DatabaseSqlCe>SQLServerCE 连接</DatabaseSqlCe>
    <DatabaseSQLite>SQLite连接</DatabaseSQLite>
    <DatabaseTeradata>Teradata连接</DatabaseTeradata>
    <DatabaseUniDirect>Uni直接连接</DatabaseUniDirect>
    <DatabaseVistaDB>VistaDB 连接</DatabaseVistaDB>
    <DatabaseXml>Xml 数据</DatabaseXml>
  </Database>
  <DatePickerRanges>
    <CurrentMonth>本月</CurrentMonth>
    <CurrentQuarter>本季度</CurrentQuarter>
    <CurrentWeek>本周</CurrentWeek>
    <CurrentYear>本年</CurrentYear>
    <DaysAgo>{0}天以前</DaysAgo>
    <FirstQuarter>第一季度</FirstQuarter>
    <FourthQuarter>第四季度</FourthQuarter>
    <Index>指数</Index>
    <Last14Days>最近14天</Last14Days>
    <Last30Days>最近30天</Last30Days>
    <Last7Days>最近7天</Last7Days>
    <MonthToDate>本月至今</MonthToDate>
    <NextMonth>下月</NextMonth>
    <NextQuarter>下季度</NextQuarter>
    <NextWeek>下周</NextWeek>
    <NextYear>下年</NextYear>
    <PreviousMonth>上月</PreviousMonth>
    <PreviousQuarter>上季度</PreviousQuarter>
    <PreviousWeek>上周</PreviousWeek>
    <PreviousYear>上年</PreviousYear>
    <Quarter>季度</Quarter>
    <QuarterToDate>本季度至今</QuarterToDate>
    <SecondQuarter>第二季度</SecondQuarter>
    <ThirdQuarter>第三季度</ThirdQuarter>
    <Today>今日</Today>
    <Tomorrow>明日</Tomorrow>
    <WeekToDate>本周至今</WeekToDate>
    <Year>年</Year>
    <YearToDate>本年至今</YearToDate>
    <Yesterday>昨天</Yesterday>
  </DatePickerRanges>
  <DesignerFx>
    <AlreadyExists>'{0}' 已存在.</AlreadyExists>
    <CanNotLoadThisReportTemplate>无法加载此报表模板.</CanNotLoadThisReportTemplate>
    <CloseDataSourceEditor>你想要关闭数据源编辑器吗?</CloseDataSourceEditor>
    <CloseEditor>你想关闭编辑器吗?</CloseEditor>
    <CompilingReport>编译报表</CompilingReport>
    <Connecting>连接到服务器</Connecting>
    <ConnectionError>连接错误</ConnectionError>
    <ConnectionSuccessfull>连接成功</ConnectionSuccessfull>
    <Continue>继续</Continue>
    <DecryptionError>解密错误: 密码错误或文件被损.</DecryptionError>
    <EmailSuccessfullySent>邮件已成功发送.</EmailSuccessfullySent>
    <ErrorAtSaving>保存错误</ErrorAtSaving>
    <ErrorCode>保存错误. 错误代码: {0}</ErrorCode>
    <ErrorServer>保存错误. 服务器无反应.</ErrorServer>
    <ExportingReport>导出报表</ExportingReport>
    <LoadingCode>加载代码</LoadingCode>
    <LoadingConfiguration>加载配置</LoadingConfiguration>
    <LoadingData>加载数据</LoadingData>
    <LoadingDocument>加载文档</LoadingDocument>
    <LoadingImages>加载图像</LoadingImages>
    <LoadingLanguage>加载语言</LoadingLanguage>
    <LoadingReport>加载报表</LoadingReport>
    <PreviewAs>如 {0}所示</PreviewAs>
    <RenderingReport>渲染报表</RenderingReport>
    <ReportSuccessfullySaved>报表成功保存.</ReportSuccessfullySaved>
    <RetrieveError>检索列错误</RetrieveError>
    <RetrievingColumns>检索列</RetrievingColumns>
    <Saving>Saving</Saving>
    <SavingConfiguration>保存配置</SavingConfiguration>
    <SavingReport>保存报表</SavingReport>
    <TestConnection>测试连接</TestConnection>
    <TextNotFound>无法找到指定文本. 文本: {0}</TextNotFound>
  </DesignerFx>
  <Desktop>
    <Beginner>初学者</Beginner>
    <ButtonAddCloud>新增云文件夹</ButtonAddCloud>
    <ButtonAddFolder>新增文件夹</ButtonAddFolder>
    <ButtonCreateDashboard>创建仪表盘</ButtonCreateDashboard>
    <ButtonCreateReport>创建报表</ButtonCreateReport>
    <ChooseYourSkillLevel>选择你的技能等级</ChooseYourSkillLevel>
    <Creator>报表创建者</Creator>
    <Developer>开发者</Developer>
    <DoYouWantToInstallReports>是否安装报表和仪表盘模板？</DoYouWantToInstallReports>
    <InstallSamplesDesc>安装示例项目。它将帮助您学习使用本软件的基础知识。</InstallSamplesDesc>
    <PleaseAnswerWhoAreYou>请问您是?</PleaseAnswerWhoAreYou>
    <SkillLevel>技能等级</SkillLevel>
    <WhoAreYou>您是谁？</WhoAreYou>
    <WhoAreYouBeginnerDescription>非常适合初次设计报表者</WhoAreYouBeginnerDescription>
    <WhoAreYouCreatorDescription>非常适合报表创建者，设计所需功能一应俱全</WhoAreYouCreatorDescription>
    <WhoAreYouDeveloperDescription>非常适合创建应用程序，包括报表设计和嵌入组件的完整功能</WhoAreYouDeveloperDescription>
  </Desktop>
  <Dialogs>
    <StiButtonControl>按钮</StiButtonControl>
    <StiCheckBoxControl>复选框</StiCheckBoxControl>
    <StiCheckedListBoxControl>复选列表框</StiCheckedListBoxControl>
    <StiComboBoxControl>下拉框</StiComboBoxControl>
    <StiDateTimePickerControl>日期时间</StiDateTimePickerControl>
    <StiForm>窗体</StiForm>
    <StiGridControl>网格</StiGridControl>
    <StiGroupBoxControl>分组框</StiGroupBoxControl>
    <StiLabelControl>标签</StiLabelControl>
    <StiListBoxControl>列表框</StiListBoxControl>
    <StiListViewControl>查看列表</StiListViewControl>
    <StiLookUpBoxControl>查找框</StiLookUpBoxControl>
    <StiNumericUpDownControl>数值</StiNumericUpDownControl>
    <StiPanelControl>面板</StiPanelControl>
    <StiPictureBoxControl>图片框</StiPictureBoxControl>
    <StiRadioButtonControl>单选框</StiRadioButtonControl>
    <StiRadioControl>输入框</StiRadioControl>
    <StiReportControl>报表控件</StiReportControl>
    <StiRichTextBoxControl>Rich Text Box</StiRichTextBoxControl>
    <StiTextBoxControl>文件框</StiTextBoxControl>
    <StiTreeViewControl>树视图</StiTreeViewControl>
  </Dialogs>
  <Editor>
    <AllowSQLFormatting>允许SQL格式</AllowSQLFormatting>
    <BetweenMetrics>可从1到10中选取。</BetweenMetrics>
    <CantFind>找不到您所要查找的数据.</CantFind>
    <CollapseToDefinitions>折叠到定义(&amp;O)</CollapseToDefinitions>
    <Column>列: {0}</Column>
    <EntireScope>整个范围(&amp;E)</EntireScope>
    <Find>查找(&amp;F)</Find>
    <FindNext>查找下一个(&amp;F)</FindNext>
    <FindWhat>查找内容:</FindWhat>
    <FromCursor>从光标</FromCursor>
    <GotoLine>跳到行(&amp;L)</GotoLine>
    <InsertLink>插入链接</InsertLink>
    <InsertSymbol>插入符号</InsertSymbol>
    <Line>行: {0}</Line>
    <LineNumber>行号:</LineNumber>
    <LineNumberIndex>行号 ({0} - {1})</LineNumberIndex>
    <MarkAll>全部标注(&amp;M)</MarkAll>
    <MatchCase>区分大小写(&amp;M)</MatchCase>
    <MatchWholeWord>全字匹配(&amp;W)</MatchWholeWord>
    <MaximumDimensions>最多可选择7个维度</MaximumDimensions>
    <Outlining>大纲视图(&amp;L)</Outlining>
    <PromptOnReplace>替换时提示</PromptOnReplace>
    <Replace>替换(&amp;R)</Replace>
    <ReplaceAll>全部替换(&amp;A)</ReplaceAll>
    <ReplaceWith>替换为:</ReplaceWith>
    <Search>查找</Search>
    <SearchHiddenText>隐藏文本</SearchHiddenText>
    <SearchUp>向上查找(&amp;U)</SearchUp>
    <SelectionOnly>选定文本(&amp;O)</SelectionOnly>
    <ShowAutoGeneratedCode>显示自动生成代码</ShowAutoGeneratedCode>
    <ShowLineNumbers>显示行号</ShowLineNumbers>
    <StopOutlining>停止大纲视图(&amp;P)</StopOutlining>
    <titleFind>查找</titleFind>
    <titleGotoLine>跳到行</titleGotoLine>
    <titleReplace>替换</titleReplace>
    <ToggleAllOutlining>全部切换到大纲视图(&amp;L)</ToggleAllOutlining>
    <ToggleOutliningExpansion>切换到大纲视图扩展(&amp;T)</ToggleOutliningExpansion>
    <TypeToSearch>输入搜索</TypeToSearch>
    <UseRegularExpressions>正则表达式(&amp;R)</UseRegularExpressions>
  </Editor>
  <Errors>
    <ComponentIsNotRelease>组件未释放 "{0}".</ComponentIsNotRelease>
    <ContainerIsNotValidForComponent>组件 {1} 不能使用容器 {0}.</ContainerIsNotValidForComponent>
    <DataNotFound>数据未找到.</DataNotFound>
    <DataNotLoaded>无数据加载</DataNotLoaded>
    <Error>出错!</Error>
    <ErrorsList>错误列表</ErrorsList>
    <FieldRequire>字段 "{0}" 需要填充.</FieldRequire>
    <FileNotFound>文件 "{0}" 未找到.</FileNotFound>
    <IdentifierIsNotValid>标识 '{0}' 不合法.</IdentifierIsNotValid>
    <ImpossibleFindDataSource>无法找到数据源.</ImpossibleFindDataSource>
    <NameExists>已经有名称为 '{0}' 的对象存在. 对象名区分大小字且必须唯一.</NameExists>
    <NoServices>在 '{0}'找不到服务</NoServices>
    <NotAssign>数据源未定义.</NotAssign>
    <NotCorrectFormat>输入的字符串格式不正确.</NotCorrectFormat>
    <OneTableOfContentsAllowed>一个报表中只能同时使用一个“目录”组件 !</OneTableOfContentsAllowed>
    <PrimaryColumnAction>无法将此操作应用于 '{0}'的主列.</PrimaryColumnAction>
    <RelationsNotFound>找不到关系定义.</RelationsNotFound>
    <ReportCannotBeSaveDueToErrors>由于错误,报表无法保存!</ReportCannotBeSaveDueToErrors>
    <ServiceNotFound>找不到服务 '{0}'.</ServiceNotFound>
  </Errors>
  <ExceptionProvider>
    <Assemblies>组合</Assemblies>
    <ClickIgnore>点击“忽略”删除所有自动保存的文件。</ClickIgnore>
    <Exception>异常</Exception>
    <ExceptionReport>异常报告</ExceptionReport>
    <Framework>框架</Framework>
    <General>常规</General>
    <Message>消息</Message>
    <Number>编号</Number>
    <PleaseEnterDetailedInformation>请输入导致此异常事件的详细信息.</PleaseEnterDetailedInformation>
    <SaveToFile>保存到文件</SaveToFile>
    <SendErrorFailed>无法传递出错消息.</SendErrorFailed>
    <SendErrorSuccess>出错消息传递成功.</SendErrorSuccess>
    <Source>信息来源</Source>
    <StackTrace>堆栈跟踪</StackTrace>
    <YouCanRestore>您可以从备份文件中恢复未保存的更改。</YouCanRestore>
    <YourPreviousWorkSession>在您之前的工作对话中，设计器意外停止。</YourPreviousWorkSession>
  </ExceptionProvider>
  <Export>
    <AddPageBreaks>增加分页符</AddPageBreaks>
    <AllBands>所有带区</AllBands>
    <AllowAddOrModifyTextAnnotations>允许添加或修改文本附注</AllowAddOrModifyTextAnnotations>
    <AllowCopyTextAndGraphics>允许复制文字和图片</AllowCopyTextAndGraphics>
    <AllowEditable>允许编辑:</AllowEditable>
    <AllowModifyContents>允许修改内容</AllowModifyContents>
    <AllowPrintDocument>允许打印文档</AllowPrintDocument>
    <Auto>自动的</Auto>
    <BandsFilter>带区过滤器:</BandsFilter>
    <CancelExport>取消输出</CancelExport>
    <Color>颜色</Color>
    <Compressed>压缩</Compressed>
    <CompressToArchive>压缩存档</CompressToArchive>
    <ContinuousPages>连续页码</ContinuousPages>
    <DataAndHeaders>数据和标题</DataAndHeaders>
    <DataAndHeadersFooters>数据和页眉/页脚</DataAndHeadersFooters>
    <DataOnly>纯数据</DataOnly>
    <DigitalSignature>数字签名</DigitalSignature>
    <DigitalSignatureCertificateNotSelected>认证未被选择</DigitalSignatureCertificateNotSelected>
    <DigitalSignatureError>数字签名步骤错误</DigitalSignatureError>
    <DocumentSecurity>文档安全</DocumentSecurity>
    <DotMatrixMode>点阵模式</DotMatrixMode>
    <EmbeddedFonts>嵌入字体</EmbeddedFonts>
    <EmbeddedImageData>嵌入式图像数据</EmbeddedImageData>
    <EnableAnimation>动画效果</EnableAnimation>
    <Encoding>编码:</Encoding>
    <EncryptionError>加密步骤错误</EncryptionError>
    <EscapeCodes>转义代码</EscapeCodes>
    <Exactly>精确地</Exactly>
    <ExceptEditableFields>除可编辑字段外</ExceptEditableFields>
    <ExportDataOnly>仅导出数据</ExportDataOnly>
    <ExportEachPageToSheet>导出每页到工作表</ExportEachPageToSheet>
    <Exporting>输出</Exporting>
    <ExportingCalculatingCoordinates>正在计算坐标</ExportingCalculatingCoordinates>
    <ExportingCreatingDocument>创建文档</ExportingCreatingDocument>
    <ExportingFormatingObjects>正在格式化对象</ExportingFormatingObjects>
    <ExportingReport>正在导出报表</ExportingReport>
    <ExportMode>导出模式:</ExportMode>
    <ExportModeFrame>框架</ExportModeFrame>
    <ExportModeTable>表</ExportModeTable>
    <ExportObjectFormatting>导出格式化对象</ExportObjectFormatting>
    <ExportPageBreaks>导出分页符</ExportPageBreaks>
    <ExportRtfTextAsImage>将RTF文字导出为图片</ExportRtfTextAsImage>
    <ExportTypeBmpFile>BMP 图...</ExportTypeBmpFile>
    <ExportTypeCalcFile>打开 Calc File 文档...</ExportTypeCalcFile>
    <ExportTypeCsvFile>CSV 文件...</ExportTypeCsvFile>
    <ExportTypeDataFile>数据文件...</ExportTypeDataFile>
    <ExportTypeDbfFile>DBF 文件...</ExportTypeDbfFile>
    <ExportTypeDifFile>数据交换格式(DIF)文件...</ExportTypeDifFile>
    <ExportTypeExcel2007File>Microsoft Excel 2007/2013 文件...</ExportTypeExcel2007File>
    <ExportTypeExcelFile>Microsoft Excel 文件...</ExportTypeExcelFile>
    <ExportTypeExcelXmlFile>Microsoft Excel Xml 文件...</ExportTypeExcelXmlFile>
    <ExportTypeGifFile>GIF 图片...</ExportTypeGifFile>
    <ExportTypeHtml5File>HTML5 文件...</ExportTypeHtml5File>
    <ExportTypeHtmlFile>HTML 文件...</ExportTypeHtmlFile>
    <ExportTypeImageFile>图像文件...</ExportTypeImageFile>
    <ExportTypeJpegFile>JPEG 图片...</ExportTypeJpegFile>
    <ExportTypeJsonFile>JSON文件...</ExportTypeJsonFile>
    <ExportTypeMetafile>Windows 图元文件...</ExportTypeMetafile>
    <ExportTypeMhtFile>MHT Web Archive文件...</ExportTypeMhtFile>
    <ExportTypePcxFile>PCX 图像...</ExportTypePcxFile>
    <ExportTypePdfFile>Adobe PDF 文件...</ExportTypePdfFile>
    <ExportTypePngFile>PNG 图片...</ExportTypePngFile>
    <ExportTypePpt2007File>Microsoft PowerPoint文件...</ExportTypePpt2007File>
    <ExportTypeRtfFile>RTF 文件...</ExportTypeRtfFile>
    <ExportTypeSvgFile>可伸缩矢量图形(SVG)文件...</ExportTypeSvgFile>
    <ExportTypeSvgzFile>压缩的 SVG (SVGZ) 文件...</ExportTypeSvgzFile>
    <ExportTypeSylkFile>符号链接(SYLK)文件...</ExportTypeSylkFile>
    <ExportTypeTiffFile>TIFF 图片...</ExportTypeTiffFile>
    <ExportTypeTxtFile>Text 文件...</ExportTypeTxtFile>
    <ExportTypeWord2007File>Microsoft Word 2007/2013 文件...</ExportTypeWord2007File>
    <ExportTypeWriterFile>打开 Writer File 文档...</ExportTypeWriterFile>
    <ExportTypeXmlFile>XML 文件...</ExportTypeXmlFile>
    <ExportTypeXpsFile>Microsoft XPS 文件...</ExportTypeXpsFile>
    <GetCertificateFromCryptoUI>从 Crypto UI 获取证书</GetCertificateFromCryptoUI>
    <ImageCompressionMethod>图像压缩方法:</ImageCompressionMethod>
    <ImageCutEdges>裁边</ImageCutEdges>
    <ImageFormat>图片格式:</ImageFormat>
    <ImageGrayscale>灰阶</ImageGrayscale>
    <ImageMonochrome>单色</ImageMonochrome>
    <ImageQuality>图像质量</ImageQuality>
    <ImageResolution>清晰度:</ImageResolution>
    <ImageResolutionMode>图像分辨率模式:</ImageResolutionMode>
    <ImageType>图片类型</ImageType>
    <labelEncryptionKeyLength>密钥长度:</labelEncryptionKeyLength>
    <labelOwnerPassword>所有者密码:</labelOwnerPassword>
    <labelSubjectNameString>主题命名字符串:</labelSubjectNameString>
    <labelUserPassword>用户密码:</labelUserPassword>
    <MonochromeDitheringType>单色抖动式:</MonochromeDitheringType>
    <MoreSettings>更多设置</MoreSettings>
    <MultipleFiles>Multiple 文件</MultipleFiles>
    <NoMoreThan>不超过</NoMoreThan>
    <OpenAfterExport>导出后打开</OpenAfterExport>
    <PdfACompliance>PDF/A规格</PdfACompliance>
    <PrintingReport>打印报告</PrintingReport>
    <RemoveEmptySpaceAtBottom>移除页面底部的空白空间</RemoveEmptySpaceAtBottom>
    <RestrictEditing>编辑限制:</RestrictEditing>
    <Scale>缩放:</Scale>
    <Separator>分隔符:</Separator>
    <Settings>设置</Settings>
    <SkipColumnHeaders>略过列头</SkipColumnHeaders>
    <StandardPDFFonts>标准PDF字体</StandardPDFFonts>
    <TiffCompressionScheme>TIFF压缩方案 :</TiffCompressionScheme>
    <title>导出设置</title>
    <TxtBorderType>边框类型</TxtBorderType>
    <TxtBorderTypeDouble>Unicode-双线</TxtBorderTypeDouble>
    <TxtBorderTypeSimple>单线</TxtBorderTypeSimple>
    <TxtBorderTypeSingle>Unicode-单线</TxtBorderTypeSingle>
    <TxtCutLongLines>截断长行</TxtCutLongLines>
    <TxtDrawBorder>绘制边框</TxtDrawBorder>
    <TxtKillSpaceGraphLines>消除空行</TxtKillSpaceGraphLines>
    <TxtKillSpaceLines>去除空行</TxtKillSpaceLines>
    <TxtPutFeedPageCode>放置页码</TxtPutFeedPageCode>
    <Type>打印:</Type>
    <UseDefaultSystemEncoding>使用默认的系统编码</UseDefaultSystemEncoding>
    <UseDigitalSignature>使用数字签名</UseDigitalSignature>
    <UseEscapeCodes>使用转义代码</UseEscapeCodes>
    <UseOnePageHeaderAndFooter>用同一页眉和页脚</UseOnePageHeaderAndFooter>
    <UsePageHeadersAndFooters>使用页眉页尾</UsePageHeadersAndFooters>
    <UseUnicode>用 Unicode 编码</UseUnicode>
    <X>X:</X>
    <Y>Y:</Y>
    <Zoom>缩放:</Zoom>
  </Export>
  <FileFilters>
    <AllFiles>所有文件</AllFiles>
    <AllImageFiles>全部图片文件</AllImageFiles>
    <BitmapFiles>Bitmap 文件</BitmapFiles>
    <BmpFiles>BMP 图片 (*.bmp)|*.bmp</BmpFiles>
    <CalcFiles>打开 Calc files (*.ods)|*.ods 文档</CalcFiles>
    <ConditionsFiles>Stimulsoft 条件（*.stc）|*.stc</ConditionsFiles>
    <CsvFiles>CSV 文件 (*.csv)|*.csv</CsvFiles>
    <DataSetXmlData>DataSet XML data (*.xml)|*.xml</DataSetXmlData>
    <DataSetXmlSchema>DataSet XML schema (*.xsd)|*.xsd</DataSetXmlSchema>
    <DbfFiles>DBF 文件 (*.dbf)|*.dbf</DbfFiles>
    <DictionaryFiles>Files字典 (*.dct)|*.dct</DictionaryFiles>
    <DifFiles>DIF文件(*.dif)|*.dif</DifFiles>
    <DllFiles>DLL 文件 (*.dll)|*.dll</DllFiles>
    <DocumentFiles>Files document (*.mdc)|*.mdc</DocumentFiles>
    <EmfFiles>Metafile (*.emf)|*.emf</EmfFiles>
    <EncryptedDocumentFiles>加密报表文件 (*.mdx)|*.mdx</EncryptedDocumentFiles>
    <EncryptedReportFiles>加密报表模板 (*.mrx)|*.mrx</EncryptedReportFiles>
    <Excel2007Files>Microsoft Excel 2007/2013 files (*.xlsx)|*.xlsx</Excel2007Files>
    <ExcelAllFiles>微软电子表格文件 (*.xlsx;*.xls)|*.xlsx;*.xls</ExcelAllFiles>
    <ExcelCsvAllFiles>Microsoft Excel 文件 (*.xlsx;*.xls;*.csv)|*.xlsx;*.xls;*.csv</ExcelCsvAllFiles>
    <ExcelFiles>Microsoft Excel 文件 (*.xls)|*.xls</ExcelFiles>
    <ExcelXmlFiles>Microsoft Excel XML 文件 (*.xls)|*.xls</ExcelXmlFiles>
    <ExeFiles>EXE 文件 (*.exe)|*.exe</ExeFiles>
    <GifFiles>GIF 图片 (*.gif)|*.gif</GifFiles>
    <HtmlFiles>HTML 文件 (*.html)|*.html</HtmlFiles>
    <InheritedLanguageFiles>{0} 继承报表文件 (*.{1})|*.{2}</InheritedLanguageFiles>
    <JpegFiles>JPEG 图片 (*.jpg;*.jpeg)|*.jpg;*.jpeg</JpegFiles>
    <JsonDocumentFiles>JSON报表文件 (*.mdc)|*.mdc</JsonDocumentFiles>
    <JsonFiles>JSON文件 (*.json)|*.json</JsonFiles>
    <JsonReportFiles>JSON报表模板 (*.mrt)|*.mrt</JsonReportFiles>
    <LanguageFiles>{0} 文件 (*.{1})|*.{2}</LanguageFiles>
    <LanguageForSilverlightFiles>{0} Silverligh报表类 (*.{1})|*.{2}</LanguageForSilverlightFiles>
    <MetaFiles>元文件</MetaFiles>
    <MhtFiles>MHT Web Archive 文件 (*.mht)|*.mht</MhtFiles>
    <PackedDocumentFiles>Files packed document (*.mdz)|*.mdz</PackedDocumentFiles>
    <PackedReportFiles>Files packed report (*.mrz)|*.mrz</PackedReportFiles>
    <PageFiles>Files page (*.pg)|*.pg</PageFiles>
    <PcxFiles>PCX 图像 (*.pcx)|*.pcx</PcxFiles>
    <PdfFiles>Adobe PDF files (*.pdf)|*.pdf</PdfFiles>
    <PngFiles>PNG 图片 (*.png)|*.png</PngFiles>
    <Ppt2007Files>Microsoft PowerPoint 2007/2013文件 (*.pptx)|*.pptx</Ppt2007Files>
    <ReportEmbededDataFiles>报告嵌入数据的模板(*.mrt)|*.mrt</ReportEmbededDataFiles>
    <ReportFiles>Files report (*.mrt)|*.mrt</ReportFiles>
    <RtfFiles>Rich Text (*.rtf)|*.rtf</RtfFiles>
    <StandaloneReportFiles>独立报表(*.exe)|*.exe</StandaloneReportFiles>
    <StylesFiles>Files style (*.sts)|*.sts</StylesFiles>
    <SvgFiles>SVG 图像(*.svg)|*.svg</SvgFiles>
    <SvgzFiles>压缩的SVG图像(*.svgz)|*.svgz</SvgzFiles>
    <SylkFiles>SYLK文件(*.slk)|*.slk</SylkFiles>
    <TiffFiles>TIFF 图片 (*.tiff)|*.tiff</TiffFiles>
    <TxtFiles>Text (*.txt)|*.txt</TxtFiles>
    <Word2007Files>Microsoft Word 2007-2016 files (*.docx)|*.docx</Word2007Files>
    <WordFiles>微软文字处理软件97-2003文件 (*.doc)|*.doc</WordFiles>
    <WriterFiles>打开 Writer files (*.odt)|*.odt 文档</WriterFiles>
    <XmlFiles>XML 文件 (*.xml)|*.xml</XmlFiles>
    <XpsFiles>Microsoft XPS files (*.xps)|*.xps</XpsFiles>
    <ZipArchives>ZIP档案 (*.zip)|*.zip</ZipArchives>
  </FileFilters>
  <Formats>
    <custom01>d</custom01>
    <custom02>D</custom02>
    <custom03>f</custom03>
    <custom04>F</custom04>
    <custom05>yy/MM/dd</custom05>
    <custom06>yyyy/MM/dd</custom06>
    <custom07>[green]+0.00;[red]-0.00;[blue](0)</custom07>
    <custom08>G</custom08>
    <custom09>$0.00</custom09>
    <custom10>$0</custom10>
    <custom11>c</custom11>
    <custom12>c1</custom12>
    <custom13>c2</custom13>
    <custom14>#.00</custom14>
    <custom15>#.#</custom15>
    <custom16>n</custom16>
    <custom17>n1</custom17>
    <custom18>n2</custom18>
    <custom19>(###) ### - ####</custom19>
    <date01>*d</date01>
    <date02>*D</date02>
    <date03>M.dd</date03>
    <date04>yy.M.dd</date04>
    <date05>yy.MM.dd</date05>
    <date06>MMM.dd</date06>
    <date07>yy.MMM.dd</date07>
    <date08>yyyy, MMMM</date08>
    <date09>*f</date09>
    <date10>*F</date10>
    <date11>MM.dd.yyyy</date11>
    <date12>dd/MM/yyyy</date12>
    <date13>*g</date13>
    <date14>*G</date14>
    <date15>*y</date15>
    <date16>*m</date16>
    <date17>s</date17>
    <date18>u</date18>
    <date19>*Q</date19>
    <date20>*YQ</date20>
    <date21>QI</date21>
    <date22>YQI</date22>
    <time01>t</time01>
    <time02>T</time02>
    <time03>HH:mm</time03>
    <time04>H:mm</time04>
    <time06>HH:mm:ss</time06>
  </Formats>
  <FormBand>
    <AddFilter>添加过滤(&amp;A)</AddFilter>
    <AddGroup>添加分组(&amp;A)</AddGroup>
    <AddResult>添加结果(&amp;A)</AddResult>
    <AddSort>添加排序(&amp;A)</AddSort>
    <And>和</And>
    <Ascending>升序</Ascending>
    <Descending>降序</Descending>
    <NoFilters>不过滤</NoFilters>
    <NoSort>没有排序</NoSort>
    <RemoveFilter>移除过滤(&amp;R)</RemoveFilter>
    <RemoveGroup>移除分组(&amp;R)</RemoveGroup>
    <RemoveResult>移除结果(&amp;R)</RemoveResult>
    <RemoveSort>移除排序(&amp;R)</RemoveSort>
    <SortBy>主排序</SortBy>
    <ThenBy>次排序</ThenBy>
    <title>数据设置</title>
  </FormBand>
  <FormColorBoxPopup>
    <Color>颜色</Color>
    <Custom>自定义</Custom>
    <NoColor>没有颜色</NoColor>
    <Others>其它...</Others>
    <System>系统</System>
    <Web>Web</Web>
  </FormColorBoxPopup>
  <FormConditions>
    <AaBbCcYyZz>AaBbCcYyZz</AaBbCcYyZz>
    <AddCondition>添加条件(&amp;A)</AddCondition>
    <AddLevel>添加级别</AddLevel>
    <AssignExpression>分配表达式</AssignExpression>
    <BreakIfTrue>如果属实就中断</BreakIfTrue>
    <BreakIfTrueToolTip>如果满足当前条件，则中断条件处理</BreakIfTrueToolTip>
    <ChangeBorder>边界变更...</ChangeBorder>
    <ChangeFont>改变字体</ChangeFont>
    <ComponentIsEnabled>组件已激活</ComponentIsEnabled>
    <NoConditions>无条件</NoConditions>
    <RemoveCondition>移除条件(&amp;R)</RemoveCondition>
    <SelectStyle>选择样式</SelectStyle>
    <title>条件</title>
  </FormConditions>
  <FormCrossTabDesigner>
    <Columns>列:</Columns>
    <DataSource>数据源:</DataSource>
    <Properties>属性:</Properties>
    <Rows>行:</Rows>
    <Summary>统计:</Summary>
    <Swap>交换行列</Swap>
    <title>交叉报表设计</title>
  </FormCrossTabDesigner>
  <FormDatabaseEdit>
    <AccessToken>访问令牌</AccessToken>
    <AccountKey>账户密钥</AccountKey>
    <AccountName>账户名称</AccountName>
    <AuthorizationCode>授权码模式</AuthorizationCode>
    <BlobContentType>Blob内容类型</BlobContentType>
    <BlobName>Blob名称</BlobName>
    <ClientId>用户ID</ClientId>
    <ClientSecret>用户隐私</ClientSecret>
    <ConnectionString>连接字符串:</ConnectionString>
    <ContainerName>容器名称</ContainerName>
    <DashboardConnections>仪表盘连接</DashboardConnections>
    <DatabaseSecret>数据库秘钥</DatabaseSecret>
    <DatasetId>数据集Id</DatasetId>
    <DB2Edit>编辑 IBM DB2 连接</DB2Edit>
    <DB2New>新建 IBM DB2 连接</DB2New>
    <EditConnection>编辑 {0} 连接</EditConnection>
    <Favorites>偏好</Favorites>
    <FirebirdEdit>编辑 Firebird 连接</FirebirdEdit>
    <FirebirdNew>新建 Firebird 连接</FirebirdNew>
    <FirstRowIsHeader>第一行为标题</FirstRowIsHeader>
    <GetAuthorizationCode>获取授权码</GetAuthorizationCode>
    <GetTokens>获取令牌</GetTokens>
    <GisEdit>编辑GIS数据 </GisEdit>
    <GisNew>新GIS数据 a</GisNew>
    <ImportData>导入数据</ImportData>
    <InformixEdit>编辑Informix连接</InformixEdit>
    <InformixNew>新建Informix连接</InformixNew>
    <InitialCatalog>初始化分类:</InitialCatalog>
    <JsonEdit>编辑JSON数据</JsonEdit>
    <JsonNew>更新JSON数据</JsonNew>
    <MySQLEdit>编辑 MySQL 连接</MySQLEdit>
    <MySQLNew>新建 MySQL 连接</MySQLNew>
    <NewConnection>新建{0}连接</NewConnection>
    <OdbcEdit>编辑 ODBC 连接</OdbcEdit>
    <OdbcNew>新建 ODBX 连接</OdbcNew>
    <OleDbEdit>编辑 OLE DB 连接</OleDbEdit>
    <OleDbNew>新建 OLE DB 连接</OleDbNew>
    <OracleEdit>编辑 Oracle 连接</OracleEdit>
    <OracleNew>新建 Oracle 连接</OracleNew>
    <OracleODPEdit>编辑Oracle ODP.NET连接</OracleODPEdit>
    <OracleODPNew>新建Oracle ODP.NET连接</OracleODPNew>
    <PathData>路径到 XML Data:</PathData>
    <PathGisData>Gis数据路径:</PathGisData>
    <PathJsonData>JSON数据的路径:</PathJsonData>
    <PathSchema>路径到 XSD Schema:</PathSchema>
    <PathToData>数据路径:</PathToData>
    <Pin>固定</Pin>
    <PostgreSQLEdit>编辑 PostgreSQL 连接</PostgreSQLEdit>
    <PostgreSQLNew>新建 PostgreSQL 连接</PostgreSQLNew>
    <ProjectId>项目Id</ProjectId>
    <PromptUserNameAndPassword>提示用户名和密码</PromptUserNameAndPassword>
    <RecentConnections>近期</RecentConnections>
    <RedirectUrl>重定向URL</RedirectUrl>
    <RefreshAccessToken>刷新访问令牌</RefreshAccessToken>
    <RefreshToken>刷新令牌</RefreshToken>
    <RelationDirection>关系方向:</RelationDirection>
    <ReportConnections>报表连接</ReportConnections>
    <SelectData>选择数据</SelectData>
    <ServiceAccountKeyFile>服务帐户密钥文件</ServiceAccountKeyFile>
    <SpreadsheetId>电子表格ID</SpreadsheetId>
    <SqlCeEdit>编辑 SQLServerCE 连接</SqlCeEdit>
    <SqlCeNew>新建 SQLServerCE 连接</SqlCeNew>
    <SqlEdit>编辑 SQL 连接</SqlEdit>
    <SQLiteEdit>编辑 SQLite 连接</SQLiteEdit>
    <SQLiteNew>新建 SQLite 连接</SQLiteNew>
    <SqlNew>新建 Sql 连接</SqlNew>
    <TeradataEdit>编辑Teradata连接</TeradataEdit>
    <TeradataNew>新建Teradata连接</TeradataNew>
    <Token>临时令牌</Token>
    <UniDirectEdit>编辑Uni Direct连接</UniDirectEdit>
    <UniDirectNew>新建Uni Direct连接</UniDirectNew>
    <Unpin>取消固定</Unpin>
    <UseBearerAuthentication>使用不记名身份认证</UseBearerAuthentication>
    <UseOwnClientID>使用自己的“客户ID”和“客户秘钥”</UseOwnClientID>
    <UseToken>使用令牌</UseToken>
    <VistaDBEdit>编辑 VistaDB 连接</VistaDBEdit>
    <VistaDBNew>新建 VistaDB 连接</VistaDBNew>
    <XmlEdit>编辑 XML Data</XmlEdit>
    <XmlNew>新建 XML Data</XmlNew>
    <XmlType>XML类型:</XmlType>
  </FormDatabaseEdit>
  <FormDesigner>
    <Code>代码</Code>
    <ColumnsOne>一</ColumnsOne>
    <ColumnsThree>三</ColumnsThree>
    <ColumnsTwo>二</ColumnsTwo>
    <CompilingReport>编译报表</CompilingReport>
    <CopyPropertyName>复制属性名称</CopyPropertyName>
    <DockingPanels>面板</DockingPanels>
    <HtmlPreview>HTML 预览</HtmlPreview>
    <JsPreview>JS预览</JsPreview>
    <labelPleaseSelectTypeOfInterface>请选择接口类型</labelPleaseSelectTypeOfInterface>
    <LoadImage>加载图片...</LoadImage>
    <LocalizePropertyGrid>本地化属性</LocalizePropertyGrid>
    <MarginsNarrow>窄</MarginsNarrow>
    <MarginsNormal>正常</MarginsNormal>
    <MarginsWide>宽</MarginsWide>
    <OrderToolbars>重置工具栏</OrderToolbars>
    <Others>其它</Others>
    <Pages>页面</Pages>
    <Preview>预览</Preview>
    <PropertyChange>属性改变 '{0}'</PropertyChange>
    <RTPreview>WinRT预览</RTPreview>
    <SetupToolbox>设计工具栏</SetupToolbox>
    <ShowDescription>显示描述</ShowDescription>
    <SLPreview>Silverlight预览</SLPreview>
    <title>设计器</title>
    <WebPreview>Web预览</WebPreview>
  </FormDesigner>
  <FormDictionaryDesigner>
    <Actions>动作</Actions>
    <AutoSort>自动排序</AutoSort>
    <BusinessObjectEdit>编辑Business Object</BusinessObjectEdit>
    <CalcColumnEdit>编辑计算列</CalcColumnEdit>
    <CalcColumnNew>新建计算列</CalcColumnNew>
    <CategoryEdit>编辑分类</CategoryEdit>
    <CategoryNew>新建分类</CategoryNew>
    <Child>儿童</Child>
    <ChildOfBusinessObject>Business Object的子项</ChildOfBusinessObject>
    <ChildSource>子数据源:</ChildSource>
    <ClickHere>单击此处</ClickHere>
    <ColumnEdit>编辑列</ColumnEdit>
    <ColumnNew>新建列</ColumnNew>
    <CreateNewDataSource>创建新数据源</CreateNewDataSource>
    <CreateNewReport>创建新报表</CreateNewReport>
    <CsvSeparatorComma>停顿</CsvSeparatorComma>
    <CsvSeparatorOther>其他</CsvSeparatorOther>
    <CsvSeparatorSemicolon>分号</CsvSeparatorSemicolon>
    <CsvSeparatorSpace>空格</CsvSeparatorSpace>
    <CsvSeparatorSystem>系统</CsvSeparatorSystem>
    <CsvSeparatorTab>Tab</CsvSeparatorTab>
    <DatabaseEdit>编辑数据库</DatabaseEdit>
    <DatabaseNew>新建数据库</DatabaseNew>
    <DataMonitor>数据监控</DataMonitor>
    <DataParameterEdit>编辑参数</DataParameterEdit>
    <DataParameterNew>新建参数</DataParameterNew>
    <DataSetToBusinessObjects>Business Object的数据集</DataSetToBusinessObjects>
    <DataSourceEdit>编辑数据源</DataSourceEdit>
    <DataSourceNew>新建数据源</DataSourceNew>
    <DataSourcesNew>新建数据源</DataSourcesNew>
    <DataTransformationEdit>编辑数据转换</DataTransformationEdit>
    <DataTransformationNew>更新数据转换</DataTransformationNew>
    <Delete>删除</Delete>
    <DesignTimeQueryText>设计时查询文本</DesignTimeQueryText>
    <DictionaryMerge>合并字典...</DictionaryMerge>
    <DictionaryNew>新建字典...</DictionaryNew>
    <DictionaryOpen>打开字典...</DictionaryOpen>
    <DictionarySaveAs>保存字典为...</DictionarySaveAs>
    <DragNewDataSource>直接将您的数据拖动至此面板</DragNewDataSource>
    <DragNewReport>直接将您的报表拖动至此面板</DragNewReport>
    <EditQuery>编辑查询</EditQuery>
    <EnableDataMonitor>启用数据监视器</EnableDataMonitor>
    <ExecutedSQLStatementSuccessfully>SQL脚本执行成功</ExecutedSQLStatementSuccessfully>
    <ExpressionNew>新表达式</ExpressionNew>
    <GetColumnsFromAssembly>从集合获取列</GetColumnsFromAssembly>
    <HideUnsupportedDatabases>隐藏不支持的数据源</HideUnsupportedDatabases>
    <ImportRelations>导入关系</ImportRelations>
    <LabelSeparator>分隔符:</LabelSeparator>
    <MarkUsedItems>标注已使用项目</MarkUsedItems>
    <NewBusinessObject>新建Business Object</NewBusinessObject>
    <NewItem>新项目</NewItem>
    <OpenAssembly>打开集合</OpenAssembly>
    <Parent>父母</Parent>
    <ParentSource>父数据库:</ParentSource>
    <Queries>查询</Queries>
    <QueryNew>新建查询</QueryNew>
    <QueryText>查询文本</QueryText>
    <QueryTimeout>查询超时</QueryTimeout>
    <RelationEdit>编辑关系</RelationEdit>
    <RelationNew>新建关系</RelationNew>
    <ResourceEdit>编辑资源</ResourceEdit>
    <ResourceNew>更新资源</ResourceNew>
    <RetrieveColumns>获取所有列</RetrieveColumns>
    <RetrieveColumnsAllowRun>允许存储过程运行</RetrieveColumnsAllowRun>
    <RetrieveColumnsAndParameters>检索列和参数</RetrieveColumnsAndParameters>
    <RetrieveParameters>检索参数</RetrieveParameters>
    <RetrievingDatabaseInformation>检索数据库信息...</RetrievingDatabaseInformation>
    <Run>运行</Run>
    <SelectTypeOfBusinessObject>选择Business Object类型</SelectTypeOfBusinessObject>
    <SkipSchemaWizard>跳过模式向导</SkipSchemaWizard>
    <SortItems>排序项目</SortItems>
    <Synchronize>同步</Synchronize>
    <SynchronizeHint>同步数据源和字典内容</SynchronizeHint>
    <TextDropDataFileHere>将数据文件拖至此处</TextDropDataFileHere>
    <TextDropFileHere>将文件拖至此处</TextDropFileHere>
    <TextDropImageHere>将图像文件拖至此处</TextDropImageHere>
    <title>字典设计器</title>
    <ValueNew>新值</ValueNew>
    <VariableEdit>编辑变量</VariableEdit>
    <VariableNew>新建变量</VariableNew>
    <ViewData>查看数据</ViewData>
    <ViewQuery>查看查询</ViewQuery>
  </FormDictionaryDesigner>
  <FormFormatEditor>
    <Boolean>布尔型</Boolean>
    <BooleanDisplay>显示:</BooleanDisplay>
    <BooleanValue>值:</BooleanValue>
    <Currency>货币</Currency>
    <CurrencySymbol>货币符号:</CurrencySymbol>
    <Custom>自定义</Custom>
    <Date>日期</Date>
    <DateTimeFormat>日期时间格式</DateTimeFormat>
    <DecimalDigits>数值:</DecimalDigits>
    <DecimalSeparator>数值分隔符:</DecimalSeparator>
    <FormatMask>格式:</FormatMask>
    <Formats>格式:</Formats>
    <General>常规</General>
    <GroupSeparator>分组符:</GroupSeparator>
    <GroupSize>分组尺寸:</GroupSize>
    <nameFalse>假</nameFalse>
    <nameNo>否</nameNo>
    <nameOff>关</nameOff>
    <nameOn>开</nameOn>
    <nameTrue>真</nameTrue>
    <nameYes>是</nameYes>
    <NegativeInRed>负数为红色</NegativeInRed>
    <NegativePattern>负数:</NegativePattern>
    <Number>数字</Number>
    <Percentage>百分数</Percentage>
    <PercentageSymbol>百分号:</PercentageSymbol>
    <PositivePattern>正数:</PositivePattern>
    <Properties>属性</Properties>
    <Sample>示例</Sample>
    <SampleText>示例文本</SampleText>
    <TextFormat>文本格式</TextFormat>
    <Time>时间</Time>
    <title>格式</title>
    <UseAbbreviation>使用缩写词</UseAbbreviation>
    <UseGroupSeparator>使用分组符</UseGroupSeparator>
    <UseLocalSetting>使用本地设置</UseLocalSetting>
  </FormFormatEditor>
  <FormGlobalizationEditor>
    <AddCulture>添加 Culture(&amp;A)</AddCulture>
    <AutoLocalizeReportOnRun>运行时自动本地化报表</AutoLocalizeReportOnRun>
    <CreateNewCulture>创建新文化</CreateNewCulture>
    <GetCulture>从报表中获取区域性设置</GetCulture>
    <qnGetCulture>你确定要从报告中获得区域性设置并且覆盖当前的区域性设置吗?</qnGetCulture>
    <qnSetCulture>你确定要把区域性设置设定到报表组件里吗?</qnSetCulture>
    <RemoveCulture>移除 Culture(&amp;R)</RemoveCulture>
    <SetCulture>将区域性设置设定到报告</SetCulture>
    <title>全局编辑器</title>
  </FormGlobalizationEditor>
  <FormInteraction>
    <HyperlinkExternalDocuments>超链接至外部文档</HyperlinkExternalDocuments>
    <HyperlinkUsingInteractionBookmark>超链接使用书签</HyperlinkUsingInteractionBookmark>
    <HyperlinkUsingInteractionTag>超链接使用标签</HyperlinkUsingInteractionTag>
  </FormInteraction>
  <FormOptions>
    <AutoSave>自动保存</AutoSave>
    <AutoSaveReportToReportClass>自动保存报表到 C# 或 VB.NET 文件</AutoSaveReportToReportClass>
    <BlankDashboard>空白仪表盘</BlankDashboard>
    <BlankForm>空白格式</BlankForm>
    <BlankReport>空白报表</BlankReport>
    <BlankScreen>黑屏</BlankScreen>
    <Default>默认</Default>
    <DefaultScriptMode>默认脚本模式</DefaultScriptMode>
    <Drawing>图纸</Drawing>
    <DrawMarkersWhenMoving>移动时绘制标注</DrawMarkersWhenMoving>
    <EditAfterInsert>插入后编辑</EditAfterInsert>
    <EnableAutoSaveMode>开启自动保存模式</EnableAutoSaveMode>
    <FillBands>填充区</FillBands>
    <FillComponents>填充组件</FillComponents>
    <FillContainers>填充容器</FillContainers>
    <FillCrossBands>填充交叉区</FillCrossBands>
    <GenerateLocalizedName>生成本地化名称</GenerateLocalizedName>
    <Grid>网格</Grid>
    <GridDots>点</GridDots>
    <GridLines>线</GridLines>
    <GridMode>网格模式</GridMode>
    <GridSize>网格大小</GridSize>
    <groupAutoSaveOptions>自动保存选项</groupAutoSaveOptions>
    <groupColorScheme>请选择Gui配色方案</groupColorScheme>
    <groupGridDrawingOptions>网格绘制选项</groupGridDrawingOptions>
    <groupGridOptions>网格选项</groupGridOptions>
    <groupGridSize>网格大小</groupGridSize>
    <groupMainOptions>工作报表设计主要选项</groupMainOptions>
    <groupMarkersStyle>标志样式</groupMarkersStyle>
    <groupOptionsOfQuickInfo>快速信息选项</groupOptionsOfQuickInfo>
    <groupPleaseSelectTypeOfGui>请选择GUI类型</groupPleaseSelectTypeOfGui>
    <groupReportDisplayOptions>报表显示选项</groupReportDisplayOptions>
    <labelColorScheme>配色方案：</labelColorScheme>
    <labelInfoAutoSave>改变报表自动保存参数</labelInfoAutoSave>
    <labelInfoDrawing>设置报表绘制参数</labelInfoDrawing>
    <labelInfoGrid>在所用的报表中如何显示网格</labelInfoGrid>
    <labelInfoGui>在报表设计中选择所使用的GUI模式</labelInfoGui>
    <labelInfoMain>设置报表设计基本参数</labelInfoMain>
    <labelInfoQuickInfo>组件提示信息页</labelInfoQuickInfo>
    <Main>主体</Main>
    <MarkersStyle>标注样式</MarkersStyle>
    <MarkersStyleCorners>三角</MarkersStyleCorners>
    <MarkersStyleDashedRectangle>虚线矩形</MarkersStyleDashedRectangle>
    <MarkersStyleNone>无</MarkersStyleNone>
    <MessageLeftRightNotValid>左值和右值无效.</MessageLeftRightNotValid>
    <MessageTopBottomNotValid>顶部和底部的价值无效.</MessageTopBottomNotValid>
    <Minutes>{0} 分钟</Minutes>
    <SaveReportEvery>保存全部报表</SaveReportEvery>
    <ScaleMode>缩放模式</ScaleMode>
    <SelectUILanguage>选择界面语言</SelectUILanguage>
    <ShowDialogForms>显示对话框窗口</ShowDialogForms>
    <ShowDimensionLines>显示尺寸线</ShowDimensionLines>
    <ShowOldGaugeEditor>显示旧测量编辑器</ShowOldGaugeEditor>
    <ShowSmartGuides>显示智能向导</ShowSmartGuides>
    <StartScreen>启动屏幕</StartScreen>
    <title>选项</title>
    <UseComponentColor>用组件颜色填充</UseComponentColor>
    <UseLastFormat>使用上次格式</UseLastFormat>
    <Welcome>欢迎</Welcome>
  </FormOptions>
  <FormPageSetup>
    <ApplyTo>应用于</ApplyTo>
    <Bottom>下:</Bottom>
    <Columns>栏</Columns>
    <groupColumns>页面栏</groupColumns>
    <groupImage>水印图像</groupImage>
    <groupMargins>页边距</groupMargins>
    <groupOrientation>纸张方向</groupOrientation>
    <groupPaper>纸张大小</groupPaper>
    <groupPaperSource>纸张来源</groupPaperSource>
    <groupText>水印文字</groupText>
    <Height>高:</Height>
    <labelAngle>角度：</labelAngle>
    <labelColumnGaps>列距：</labelColumnGaps>
    <labelColumnWidth>列宽:</labelColumnWidth>
    <labelImageAlignment>图像对齐：</labelImageAlignment>
    <labelImageTransparency>图像透明度：</labelImageTransparency>
    <labelInfoColumns>设置页面栏数</labelInfoColumns>
    <labelInfoPaper>设置当前页的大小和方向</labelInfoPaper>
    <labelInfoUnit>指定当前单元格的页边距</labelInfoUnit>
    <labelInfoWatermark>设置水印显示参数</labelInfoWatermark>
    <labelMultipleFactor>多重因素:</labelMultipleFactor>
    <labelPaperSourceOfFirstPage>首页纸张来源：</labelPaperSourceOfFirstPage>
    <labelPaperSourceOfOtherPages>其它页纸张来源:</labelPaperSourceOfOtherPages>
    <labelSelectBrush>选择笔刷:</labelSelectBrush>
    <labelSelectColor>选择颜色:</labelSelectColor>
    <labelSelectFont>选择字体:</labelSelectFont>
    <labelSelectImage>选择图像:</labelSelectImage>
    <labelText>文字:</labelText>
    <Left>左:</Left>
    <Margins>页边距</Margins>
    <NumberOfColumns>栏数:</NumberOfColumns>
    <Orientation>方向</Orientation>
    <PageOrientationLandscape>横向</PageOrientationLandscape>
    <PageOrientationPortrait>纵向</PageOrientationPortrait>
    <Paper>纸张:</Paper>
    <RebuildReport>重新生成报表</RebuildReport>
    <Right>右:</Right>
    <ScaleContent>缩放内容</ScaleContent>
    <Size>尺寸:</Size>
    <title>页面设置</title>
    <Top>上:</Top>
    <Width>宽:</Width>
  </FormPageSetup>
  <FormReportSetup>
    <groupDates>报表数据创建和最新数据变化</groupDates>
    <groupDescription>报表描述</groupDescription>
    <groupMainParameters>报表渲染影响参数</groupMainParameters>
    <groupNames>报表名称, 报表别名, 报表作者</groupNames>
    <groupScript>报表脚本语言</groupScript>
    <groupUnits>报表的大小和坐标在指定单位内</groupUnits>
    <labelInfoDescription>显示报表信息</labelInfoDescription>
    <labelInfoMain>改变报表基本参数</labelInfoMain>
    <labelNumberOfPass>密码数字:</labelNumberOfPass>
    <labelReportCacheMode>报告缓存模式：</labelReportCacheMode>
    <ReportChanged>修改时间:</ReportChanged>
    <ReportCreated>创建时间:</ReportCreated>
    <title>报表设置</title>
  </FormReportSetup>
  <FormRichTextEditor>
    <Bullets>项目符号</Bullets>
    <FontName>字体名称</FontName>
    <FontSize>字体大小</FontSize>
    <Insert>插入表达式</Insert>
    <title>Rich Text 编辑器</title>
  </FormRichTextEditor>
  <FormStyleDesigner>
    <Add>添加样式</Add>
    <AddCollectionName>添加集合名称</AddCollectionName>
    <ApplyStyleCollectionToReportComponents>应用样式集合到报告组件</ApplyStyleCollectionToReportComponents>
    <ApplyStyles>应用样式</ApplyStyles>
    <ColorCollectionEditor>集合编辑器涂色</ColorCollectionEditor>
    <CreateNewComponentStyle>创建新组建样式</CreateNewComponentStyle>
    <CreateStyleCollection>创建样式集</CreateStyleCollection>
    <CreateStyleOnBase>基于其他样式创建样式</CreateStyleOnBase>
    <Duplicate>复制样式</Duplicate>
    <EditColors>编辑颜色 </EditColors>
    <FromStyle>来自风格</FromStyle>
    <GetStyle>从所选组件中获得样式</GetStyle>
    <MoreStyles>更多样式</MoreStyles>
    <NotSpecified>未指定</NotSpecified>
    <Open>打开样式</Open>
    <Predefined>预定义</Predefined>
    <qnApplyStyleCollection>你要把样式集应用到报表组件里吗?</qnApplyStyleCollection>
    <Remove>移除样式</Remove>
    <RemoveExistingStyles>移除已有样式</RemoveExistingStyles>
    <Save>保存样式</Save>
    <Style>样式</Style>
    <StyleCollectionsNotFound>没有找到样式集!</StyleCollectionsNotFound>
    <title>样式设计器</title>
  </FormStyleDesigner>
  <FormSystemTextEditor>
    <Condition>条件</Condition>
    <LabelDataBand>数据绑定</LabelDataBand>
    <LabelDataColumn>数据列</LabelDataColumn>
    <LabelShowInsteadNullValues>显示替代null值:</LabelShowInsteadNullValues>
    <LabelSummaryFunction>聚合函数</LabelSummaryFunction>
    <pageExpression>表达式</pageExpression>
    <pageSummary>合计</pageSummary>
    <pageSystemVariable>系统变量</pageSystemVariable>
    <RunningTotal>运行总计</RunningTotal>
    <SummaryRunning>运行时聚合</SummaryRunning>
    <SummaryRunningByColumn>列</SummaryRunningByColumn>
    <SummaryRunningByPage>页</SummaryRunningByPage>
    <SummaryRunningByReport>报表</SummaryRunningByReport>
  </FormSystemTextEditor>
  <FormTitles>
    <ChartWizardForm>图表向导</ChartWizardForm>
    <ConditionEditorForm>条件</ConditionEditorForm>
    <ConnectionSelectForm>选择连接类型</ConnectionSelectForm>
    <ContainerSelectForm>选择容器</ContainerSelectForm>
    <DataAdapterServiceSelectForm>选择数据类型</DataAdapterServiceSelectForm>
    <DataRelationSelectForm>选择数据关系</DataRelationSelectForm>
    <DataSetName>输入 DataSet 名称</DataSetName>
    <DataSourceSelectForm>选择数据源</DataSourceSelectForm>
    <DataSourcesNewForm>新建数据源</DataSourcesNewForm>
    <DataStoreViewerForm>数据存储浏览器</DataStoreViewerForm>
    <DesignerApplication>报表设计器</DesignerApplication>
    <EventEditorForm>事件编辑器</EventEditorForm>
    <ExpressionEditorForm>表达式编辑器</ExpressionEditorForm>
    <GroupConditionForm>分组条件</GroupConditionForm>
    <InteractionDrillDownPageSelectForm>选择钻取页</InteractionDrillDownPageSelectForm>
    <MasterComponentSelectForm>选择父级组件</MasterComponentSelectForm>
    <PageAddForm>添加页面</PageAddForm>
    <PageSizeForm>页面尺寸</PageSizeForm>
    <PagesManagerForm>页面管理器</PagesManagerForm>
    <PromptForm>输入登录信息到数据库</PromptForm>
    <ReportWizard>报表向导</ReportWizard>
    <ServiceSelectForm>选择服务</ServiceSelectForm>
    <SqlExpressionsForm>SQL 表达式</SqlExpressionsForm>
    <SubReportPageSelectForm>选择子报表页面</SubReportPageSelectForm>
    <TextEditorForm>文本编辑器</TextEditorForm>
    <ViewDataForm>查看数据</ViewDataForm>
    <ViewerApplication>报表查看器</ViewerApplication>
  </FormTitles>
  <FormViewer>
    <Bookmarks>书签</Bookmarks>
    <Close>关闭</Close>
    <CollapseAll>全部折叠</CollapseAll>
    <CompressedDocumentFile>压缩文档文件</CompressedDocumentFile>
    <ContextMenu>右键菜单</ContextMenu>
    <DocumentFile>文档 File...</DocumentFile>
    <Editor>编辑器</Editor>
    <EncryptedDocumentFile>加密文档文件</EncryptedDocumentFile>
    <ExpandAll>全部展开</ExpandAll>
    <Export>导出...</Export>
    <Find>查找</Find>
    <FirstPage>首页</FirstPage>
    <FullScreen>全屏</FullScreen>
    <GoToPage>转到页</GoToPage>
    <HorScrollBar>水平滚动条</HorScrollBar>
    <JumpToPage>跳到页</JumpToPage>
    <LabelPageN>页:</LabelPageN>
    <LastPage>末页</LastPage>
    <NextPage>下页</NextPage>
    <Open>打开...</Open>
    <PageControl>页面控制</PageControl>
    <PageDelete>删除页面</PageDelete>
    <PageDesign>编辑页面...</PageDesign>
    <PageNew>新页</PageNew>
    <PageNofM>{0} / {1}</PageNofM>
    <PageofM>/ {0}</PageofM>
    <PageSize>页面大小...</PageSize>
    <PageViewModeContinuous>连续</PageViewModeContinuous>
    <PageViewModeMultiplePages>多页</PageViewModeMultiplePages>
    <PageViewModeSinglePage>单页</PageViewModeSinglePage>
    <Parameters>参数</Parameters>
    <PrevPage>上页</PrevPage>
    <Print>打印...</Print>
    <qnPageDelete>您是否要删除页面?</qnPageDelete>
    <Save>保存...</Save>
    <SendEMail>发送邮件...</SendEMail>
    <StatusBar>状态栏</StatusBar>
    <Thumbnails>缩略图</Thumbnails>
    <title>预览</title>
    <titlePageSettings>页面设置</titlePageSettings>
    <Toolbar>工具栏</Toolbar>
    <VerScrollBar>垂直滚动条</VerScrollBar>
    <ViewMode>查看方式</ViewMode>
    <Zoom>缩放</Zoom>
    <ZoomMultiplePages>多页</ZoomMultiplePages>
    <ZoomOnePage>整页</ZoomOnePage>
    <ZoomPageWidth>页宽</ZoomPageWidth>
    <ZoomTwoPages>双页</ZoomTwoPages>
    <ZoomXXPages>{0} X {1} 页</ZoomXXPages>
    <ZoomXXPagesCancel>取消</ZoomXXPagesCancel>
  </FormViewer>
  <FormViewerFind>
    <Close>关闭</Close>
    <FindNext>查找下一个</FindNext>
    <FindPrevious>搜索前一个</FindPrevious>
    <FindWhat>查找内容:</FindWhat>
  </FormViewerFind>
  <Gauge>
    <AddNewItem>新增项目</AddNewItem>
    <BarRangeList>条范围列表</BarRangeList>
    <GaugeEditorForm>测量编辑器</GaugeEditorForm>
    <Kind>类</Kind>
    <LinearBar>线性条</LinearBar>
    <LinearMarker>线性标志</LinearMarker>
    <LinearRange>线性范围</LinearRange>
    <LinearRangeList>线性范围列表</LinearRangeList>
    <LinearScale>线性标尺</LinearScale>
    <LinearTickLabelCustom>线性刻度标签自定义</LinearTickLabelCustom>
    <LinearTickLabelMajor>线性刻度主要标签</LinearTickLabelMajor>
    <LinearTickLabelMinor>线性刻度次要标签</LinearTickLabelMinor>
    <LinearTickMarkCustom>线性刻度标志自定义</LinearTickMarkCustom>
    <LinearTickMarkMajor>线性刻度主要标志</LinearTickMarkMajor>
    <LinearTickMarkMinor>线性刻度次要标志</LinearTickMarkMinor>
    <Needle>指针</Needle>
    <RadialBar>径向条</RadialBar>
    <RadialMarker>径向标记</RadialMarker>
    <RadialRange>径向范围</RadialRange>
    <RadialRangeList>径向范围列表</RadialRangeList>
    <RadialScale>径向缩放</RadialScale>
    <RadialTickLabelCustom>径向刻度标签自定义</RadialTickLabelCustom>
    <RadialTickLabelMajor>径向刻度主要标签</RadialTickLabelMajor>
    <RadialTickLabelMinor>径向刻度次要标签</RadialTickLabelMinor>
    <RadialTickMarkCustom>径向刻度标志自定义</RadialTickMarkCustom>
    <RadialTickMarkMajor>径向刻度主要标志</RadialTickMarkMajor>
    <RadialTickMarkMinor>径向刻度次要标志</RadialTickMarkMinor>
    <StateIndicator>状态指示器</StateIndicator>
    <StateIndicatorFilter>状态指示筛选</StateIndicatorFilter>
    <TickCustomValue>刻度自定义值</TickCustomValue>
  </Gauge>
  <Gui>
    <AccentColor>色调</AccentColor>
    <barname_cancel>取消</barname_cancel>
    <barname_caption>新建工具栏</barname_caption>
    <barname_msginvalidname>工具栏名称不能为空。</barname_msginvalidname>
    <barname_name>工具栏名称(&amp;T):</barname_name>
    <barname_ok>确定</barname_ok>
    <barrename_caption>重命名工具栏</barrename_caption>
    <barsys_autohide_tooltip>自动隐藏</barsys_autohide_tooltip>
    <barsys_close_tooltip>关闭</barsys_close_tooltip>
    <barsys_customize_tooltip>自定义</barsys_customize_tooltip>
    <ClickAndDragPickColor>点击拖动以选择颜色</ClickAndDragPickColor>
    <colorpicker_morecolors>更多颜色(&amp;M)...</colorpicker_morecolors>
    <colorpicker_nofill>无填充(&amp;N)</colorpicker_nofill>
    <colorpicker_standardcolorslabel>标准颜色</colorpicker_standardcolorslabel>
    <colorpicker_themecolorslabel>主题颜色</colorpicker_themecolorslabel>
    <colorpickerdialog_alphalabel />
    <colorpickerdialog_bluelabel>蓝(&amp;B):</colorpickerdialog_bluelabel>
    <colorpickerdialog_cancelbutton>取消</colorpickerdialog_cancelbutton>
    <colorpickerdialog_caption>颜色</colorpickerdialog_caption>
    <colorpickerdialog_colormodellabel>颜色模式:</colorpickerdialog_colormodellabel>
    <colorpickerdialog_currentcolorlabel>当前</colorpickerdialog_currentcolorlabel>
    <colorpickerdialog_customcolorslabel>颜色:</colorpickerdialog_customcolorslabel>
    <colorpickerdialog_greenlabel>绿(&amp;G):</colorpickerdialog_greenlabel>
    <colorpickerdialog_newcolorlabel>新建</colorpickerdialog_newcolorlabel>
    <colorpickerdialog_okbutton>确定</colorpickerdialog_okbutton>
    <colorpickerdialog_redlabel>红(&amp;R):</colorpickerdialog_redlabel>
    <colorpickerdialog_rgblabel>RGB</colorpickerdialog_rgblabel>
    <colorpickerdialog_standardcolorslabel>颜色:</colorpickerdialog_standardcolorslabel>
    <colorpickerdialog_tabcustom>典型</colorpickerdialog_tabcustom>
    <colorpickerdialog_tabstandard>标准</colorpickerdialog_tabstandard>
    <cust_btn_close>关闭</cust_btn_close>
    <cust_btn_delete>删除</cust_btn_delete>
    <cust_btn_keyboard>键盘(&amp;K)...</cust_btn_keyboard>
    <cust_btn_new>新建(&amp;N)...</cust_btn_new>
    <cust_btn_rename>重命名(&amp;R)...</cust_btn_rename>
    <cust_btn_reset>重置(&amp;R)...</cust_btn_reset>
    <cust_btn_resetusage>重置我的使用情况数据(&amp;R)</cust_btn_resetusage>
    <cust_caption>自定义</cust_caption>
    <cust_cbo_fade>褪色度</cust_cbo_fade>
    <cust_cbo_none>(无)</cust_cbo_none>
    <cust_cbo_random>随机</cust_cbo_random>
    <cust_cbo_slide>幻灯片</cust_cbo_slide>
    <cust_cbo_system>系统默认设置</cust_cbo_system>
    <cust_cbo_unfold>展开</cust_cbo_unfold>
    <cust_chk_delay>在短暂延迟后显示所有菜单</cust_chk_delay>
    <cust_chk_fullmenus>始终显示所有菜单</cust_chk_fullmenus>
    <cust_chk_showsk>在屏幕提示中显示快捷键(&amp;S)</cust_chk_showsk>
    <cust_chk_showst>在工具栏上显示屏幕提示(&amp;T)</cust_chk_showst>
    <cust_lbl_cats>类别(&amp;G):</cust_lbl_cats>
    <cust_lbl_cmds>命令(&amp;D):</cust_lbl_cmds>
    <cust_lbl_cmdsins>要向工具栏添加命令，请选择类别，并将命令从此框中拖放到某个工具栏上。</cust_lbl_cmdsins>
    <cust_lbl_menuan>菜单动画:</cust_lbl_menuan>
    <cust_lbl_other>其他:</cust_lbl_other>
    <cust_lbl_pmt>个性化菜单和工具栏</cust_lbl_pmt>
    <cust_lbl_tlbs>工具栏(&amp;A):</cust_lbl_tlbs>
    <cust_mnu_addremove>添加或删除按钮(&amp;A)</cust_mnu_addremove>
    <cust_mnu_cust>自定义...</cust_mnu_cust>
    <cust_mnu_reset>重置工具栏</cust_mnu_reset>
    <cust_mnu_tooltip>工具栏选项</cust_mnu_tooltip>
    <cust_msg_delete>是否确实要删除 &lt;barname&gt; 工具栏？</cust_msg_delete>
    <cust_pm_begingroup>开始编组</cust_pm_begingroup>
    <cust_pm_delete>删除</cust_pm_delete>
    <cust_pm_name>名称:</cust_pm_name>
    <cust_pm_reset>重置</cust_pm_reset>
    <cust_pm_stydef>默认样式</cust_pm_stydef>
    <cust_pm_styimagetext>图像和文字 (始终)</cust_pm_styimagetext>
    <cust_pm_stytextonly>仅文字 (始终)</cust_pm_stytextonly>
    <cust_tab_commands>命令</cust_tab_commands>
    <cust_tab_options>选项</cust_tab_options>
    <cust_tab_toolbar_alignment>工具栏对齐</cust_tab_toolbar_alignment>
    <cust_tab_toolbars>工具栏</cust_tab_toolbars>
    <mdisysmenu_close>关闭</mdisysmenu_close>
    <mdisysmenu_maximize>最大化</mdisysmenu_maximize>
    <mdisysmenu_minimize>最小化</mdisysmenu_minimize>
    <mdisysmenu_move>移动</mdisysmenu_move>
    <mdisysmenu_next>下一个</mdisysmenu_next>
    <mdisysmenu_restore>恢复</mdisysmenu_restore>
    <mdisysmenu_size>大小</mdisysmenu_size>
    <mdisystt_close>关闭</mdisystt_close>
    <mdisystt_minimize>最小化</mdisystt_minimize>
    <mdisystt_restore>恢复</mdisystt_restore>
    <monthcalendar_clearbutton>清除</monthcalendar_clearbutton>
    <monthcalendar_todaybutton>今天</monthcalendar_todaybutton>
    <navbar_navpaneoptions>导航面板选项(&amp;V)...</navbar_navpaneoptions>
    <navbar_showfewerbuttons>显示较少按钮(&amp;F)</navbar_showfewerbuttons>
    <navbar_showmorebuttons>显示较多按钮(&amp;M)</navbar_showmorebuttons>
    <navPaneCollapseTooltip>折叠导航面板</navPaneCollapseTooltip>
    <navPaneExpandTooltip>展开导航面板</navPaneExpandTooltip>
    <sys_custombar>自定义列</sys_custombar>
    <sys_morebuttons>其他按钮</sys_morebuttons>
  </Gui>
  <HelpComponents>
    <StiBarCode>该组件允许在报表中显示条形码.条形码数据以字符串的形式发送给对象.字符串可包含任意字符,但只允许被选定的图表可以显示符号.</StiBarCode>
    <StiButtonElement>该组件用于在用户交互时执行事件脚本</StiButtonElement>
    <StiCardsElement>此组件用于将分组数据显示为卡片</StiCardsElement>
    <StiChart>该组件允许在报表中显示图表.提供了不同类型的图表,,例如条形图, 折线图, 饼图, 圆环图, 线形表,面积图, 甘特图, 散点图等. </StiChart>
    <StiChartElement>该元素允许图表显示在仪表盘中。不同类型的图表均可使用。</StiChartElement>
    <StiCheckBox>该组件用于在报告中显示复选框.它可以显示两种模式:“启用”或“禁用”.</StiCheckBox>
    <StiChildBand>子级区用于把两个带区输出到一个数据行.</StiChildBand>
    <StiClone>该组件用于克隆报表的一部分到报表的所需部分.克隆只适用于面板的内容.</StiClone>
    <StiColumnFooterBand>该组件用于在数据区上输出栏尾.栏尾在每个栏尾只能输出一次.放置在该组件上的所有组件也会在每个栏位下输出.</StiColumnFooterBand>
    <StiColumnHeaderBand>该组件用于在数据区上输出栏首.栏尾在每个栏首只能输出一次.放置在该组件上的所有组件也会在每个栏位下输出.</StiColumnHeaderBand>
    <StiComboBoxElement>表示直接在控件中键入值或从现有选项列表中进行选择的组合框。</StiComboBoxElement>
    <StiContainer>该组件是可放置其他组件的矩形区域.当容器移动时,放置在其中的其他组件也会跟着移动. 容器可放置在带区和页面上.</StiContainer>
    <StiCrossDataBand>该带区是连接到数据源的.在数据源中有多少行它就会输出多少次.交叉区从左往右输出.</StiCrossDataBand>
    <StiCrossFooterBand>该带区用于输出一个交叉数据区的页脚.该带区在数据区的所有行后输出一次.交叉区从左往右输出.</StiCrossFooterBand>
    <StiCrossGroupFooterBand>该带区用于输出一个分组的页脚.它放置在交叉数据区的后面.每个交叉页脚都属于指定的交叉页眉.交叉区从左往右输出.</StiCrossGroupFooterBand>
    <StiCrossGroupHeaderBand>在使用交叉数据时,该带区是渲染分组报表的基础之一.没有改带区是不可能创建分组的.该组件分组的显示信息放置在分组页眉上.该信息可以是分组名,日期,分组条件等.该带区在每个分组的开始输出.交叉区从左往右输出.</StiCrossGroupHeaderBand>
    <StiCrossHeaderBand>该带区用于输出页眉.它用于与交叉区建立关系.交叉区从左往右输出.</StiCrossHeaderBand>
    <StiCrossTab>交叉表组件跟表格一样用于表示结构化数据.</StiCrossTab>
    <StiDataBand>数据区要连接到数据源,数据源有多少笔数据,它就输出多少次.</StiDataBand>
    <StiDatePickerElement>该元素用于选择日期和时间。</StiDatePickerElement>
    <StiElectronicSignature>此组件用于添加图形签名、图像、首字母缩写和文本</StiElectronicSignature>
    <StiEmptyBand>该带区用于填充页面底部的空白区.其填充报表每页的空白区.</StiEmptyBand>
    <StiFilterCategory>用于选择和筛选值的一种元素。</StiFilterCategory>
    <StiFooterBand>该带区用于输出数据区的总计.它放置在数据区的下面且在每次数据区的所有行输出后输出一次.</StiFooterBand>
    <StiGauge>组件允许您在预定义的范围内显示值。它可以有不同的图形表示。</StiGauge>
    <StiGaugeElement>元素允许您在预定义的范围内显示值。它可以有不同的图形表示。</StiGaugeElement>
    <StiGroupFooterBand>分组页脚区用于输出分组页脚.该带区放置在数据区的后面.并且该数据区是与分组页眉绑定的.每个分组属于特定的分组页眉.在没有分组页眉时分组页脚将不会输出.</StiGroupFooterBand>
    <StiGroupHeaderBand>该带区是渲染分组报表的基础之一.该组件的分组的显示信息放置在分组页眉上.该信息可以是分组名,日期,分组条件等.该带区在每个分组的开始输出.</StiGroupHeaderBand>
    <StiHeaderBand>该带区用于输出页眉.它与数据区一起使用.</StiHeaderBand>
    <StiHierarchicalBand>该带区要连接到数据源,数据源有多少笔数据,它就输出多少次.数据以树状结构输出.</StiHierarchicalBand>
    <StiHorizontalLinePrimitive>该组件用于输出横线.</StiHorizontalLinePrimitive>
    <StiImage>该组件用于输出图片.它支持下面的图片格式,像BMP, JPG, JPEG, GIF, TIFF, PNG, ICO, WMF, EMF.</StiImage>
    <StiImageElement>该元素用于输出图像，它支持以下图形格式，如BMP, JPG, JPEG, GIF, TIFF, PNG, ICO, WMF, EMF.</StiImageElement>
    <StiIndicatorElement>元素显示输入数据之间的值差。</StiIndicatorElement>
    <StiListBoxElement>元素允许从列表中选择项。</StiListBoxElement>
    <StiMap>该组件用于显示地理目标数据。</StiMap>
    <StiMapCategory>用于处理地图和数据的一组元素。</StiMapCategory>
    <StiMapElement>该元素用于显示地理目标数据。</StiMapElement>
    <StiMathFormula>此组件用于在报表中显示数学公式</StiMathFormula>
    <StiNumberBoxElement>此元素用于选择数值或设置数值范围</StiNumberBoxElement>
    <StiOnlineMapElement>该元素用于在在线地图上显示地理标记。</StiOnlineMapElement>
    <StiOverlayBand>该带区用于在页面上输出水印.</StiOverlayBand>
    <StiPageFooterBand>该带区用于在每页底端输出信息，像页码,日期及其他附加信息.</StiPageFooterBand>
    <StiPageHeaderBand>该带区用于输出页眉如页码,日期及其他附加信息.它在每页的顶部输出.</StiPageHeaderBand>
    <StiPanel>它是一个矩形区域,其他组件如带区都可放置在其中.当移动面板时,其上面的组件也会跟着移动.面板可放置在带区和页面上.</StiPanel>
    <StiPanelElement>矩形区域可以包含其他元素。当面板被移动时，其中的元素也被移动。</StiPanelElement>
    <StiPdfDigitalSignature>此组件用于在报告导出到Adobe PDF后指定数字签名的位置</StiPdfDigitalSignature>
    <StiPivotTableElement>数据透视表元素用于将结构化数据表示为表。</StiPivotTableElement>
    <StiProgressElement>元素显示输入数据的比率。它可以用不同的方式呈现-饼状图，环状图和条形图。</StiProgressElement>
    <StiRectanglePrimitive>该组件用于输出矩形.</StiRectanglePrimitive>
    <StiRegionMapElement>该元素用于显示地理目标数据。</StiRegionMapElement>
    <StiReportSummaryBand>该带区用于输出整个报表的总计.它在报表的结尾输出一次.</StiReportSummaryBand>
    <StiReportTitleBand>该带区用于输出报表的标题.在报表的开始输出.</StiReportTitleBand>
    <StiRichText>该组件用于输出和编辑RTF文本.它也支持加载和保存RTF格式的文件.</StiRichText>
    <StiRoundedRectanglePrimitive>该组件用于输出圆角矩形.</StiRoundedRectanglePrimitive>
    <StiShape>该组件用于插入现成的形状,如箭头,斜线,反斜线,水平线,双竖线,椭圆,矩形,圆角矩形,双横线,三角形,垂直线.</StiShape>
    <StiShapeElement>该元素用于插入现成的形状，如箭头、对角线向下、对角线向上、水平线、左右线、椭圆、矩形，圆角矩形，上下线，三角形，垂直线。</StiShapeElement>
    <StiSignature>该组件用于向报表添加电子或数字签名。</StiSignature>
    <StiSparkline>此组件用于在报表中创建缩微图形。你可以动态跟踪变化的数据，如条，线，区域</StiSparkline>
    <StiSubReport>该组件用于在报表的不同位置输出附加数据.</StiSubReport>
    <StiTable>该组件为有组织的使用垂直列和水平行模型的数据元素的集合.</StiTable>
    <StiTableElement>该元素是一组使用垂直列和水平行模型组织的数据元素。</StiTableElement>
    <StiTableOfContents>此组件用于在报表中创建目录</StiTableOfContents>
    <StiText>该组件是输出数据作为文本的基本对象.</StiText>
    <StiTextElement>该元素是将数据输出为文本的基本对象。</StiTextElement>
    <StiTextInCells>该组件用于在单元格中输出文本.它经常用于创建表格.</StiTextInCells>
    <StiTreeViewBoxElement>元素用于以树的形式在下拉列表中选择值。</StiTreeViewBoxElement>
    <StiTreeViewElement>表示以树的形式显示分层数据的元素。</StiTreeViewElement>
    <StiVerticalLinePrimitive>该组件用于输出竖线.</StiVerticalLinePrimitive>
    <StiWinControl>该组件允许显示.NET Framework中的可见对象.</StiWinControl>
    <StiZipCode>该组件用于输出邮政编码.</StiZipCode>
  </HelpComponents>
  <HelpDesigner>
    <ActiveRelation>设置绑定两个表的优先关系。该关系用于数据转换和仪表盘。每个数据源中只能有一个活跃的关系。</ActiveRelation>
    <Align>改变选中组件的位置.</Align>
    <AlignBottom>组件内容底部对齐.</AlignBottom>
    <AlignCenter>组件内容中间对齐.</AlignCenter>
    <AlignComponentBottom>允许沿着选中组件底端缘水平对齐对象.</AlignComponentBottom>
    <AlignComponentCenter>允许沿着选中组件中间边缘垂直对齐对象.</AlignComponentCenter>
    <AlignComponentLeft>允许沿着选中组件左边缘垂直对齐对象.</AlignComponentLeft>
    <AlignComponentMiddle>允许沿着选中组件中间边缘水平对齐对象.</AlignComponentMiddle>
    <AlignComponentRight>允许沿着选中组件右边缘垂直对齐对象.</AlignComponentRight>
    <AlignComponentTop>允许沿着选中组件顶端边缘水平对齐对象.</AlignComponentTop>
    <AlignLeft>组件内容左对齐.</AlignLeft>
    <AlignMiddle>通过组件的上下边界使内容居中对齐.</AlignMiddle>
    <AlignRight>组件内容右对齐.</AlignRight>
    <AlignToGrid>所选组件网格点对齐.</AlignToGrid>
    <AlignTop>组件内容顶端对齐.</AlignTop>
    <AlignWidth>调整组件内容.</AlignWidth>
    <Angle>旋转组件内容.</Angle>
    <AngleWatermark>水印文本旋转角度.</AngleWatermark>
    <Background>更改所选组件背景.</Background>
    <biConditions>选中组件条件控制列表.</biConditions>
    <BorderColor>选择所选组件的边框颜色.</BorderColor>
    <BorderSidesAll>开启所选组件的所有边框.</BorderSidesAll>
    <BorderSidesBottom>开启所选组件的下边边框.</BorderSidesBottom>
    <BorderSidesLeft>开启所选组件的左边边框.</BorderSidesLeft>
    <BorderSidesNone>切断所选组件的所有边框.</BorderSidesNone>
    <BorderSidesRight>开启所选组件的右边边框.</BorderSidesRight>
    <BorderSidesTop>开启所选组件的上边边框.</BorderSidesTop>
    <BorderStyle>选择所选边框的边框样式.</BorderStyle>
    <BringToFront>将所选组件移到前面.</BringToFront>
    <CenterHorizontally>允许对应组件放置的容器边缘使组件水平对齐.</CenterHorizontally>
    <CenterVertically>允许对应组件放置的容器边缘使组件垂直对齐.</CenterVertically>
    <Close>关闭报表设计器.</Close>
    <Columns>将页面分为多栏.</Columns>
    <CompilationAccess1>本文档使用编译过程。如果文档来自可信来源，您可以单击“打开”按钮访问它</CompilationAccess1>
    <CompilationAccess2>如果您不确定文档的来源，但仍想查看，可以单击“在安全模式下打开”按钮。在这种情况下，您将使用解释而不是编译，这使您能够安全地运行文档.</CompilationAccess2>
    <CompilationAccess3>此文档使用编译过程，可能不安全。你无法打开它</CompilationAccess3>
    <CompilationAccessAllow>允许在没有任何警告或限制的情况下打开编译文档</CompilationAccessAllow>
    <CompilationAccessAsk>如果文档使用编译过程，将显示带有预定义操作的警告窗口</CompilationAccessAsk>
    <CompilationAccessDeny>拒绝打开任何使用编译过程的文档。将显示一个特殊窗口</CompilationAccessDeny>
    <CompilationAccessForceInterpretation>编译后的文件将在没有任何提示的情况下打开，但将自动转换为解释模式，该模式不使用编译过程.</CompilationAccessForceInterpretation>
    <CompilationModeEnabling>您正在切换到编译模式。这种模式允许运行不同的代码，有时可能是有害的。请仅在您了解并同意所有风险的情况下使用此模式。单击｛0｝按钮继续.</CompilationModeEnabling>
    <ComponentSize>更改所选组件的尺寸.</ComponentSize>
    <CopyStyle>复本样式</CopyStyle>
    <CopyToClipboard>复制到剪贴板</CopyToClipboard>
    <CurrencySymbol>选择货币符号.</CurrencySymbol>
    <DashboardNew>创建新仪表盘。</DashboardNew>
    <DataStore>显示已注册在报表里的数据.</DataStore>
    <DateTimeFormat>选择所选组件的日期和时间样式.</DateTimeFormat>
    <DockingPanels>面板设置.</DockingPanels>
    <DockStyleBottom>将所选组件停靠到底部.</DockStyleBottom>
    <DockStyleFill>将所选组件停靠到各边.</DockStyleFill>
    <DockStyleLeft>将所选组件停靠到左边.</DockStyleLeft>
    <DockStyleNone>所选组件解除停靠.</DockStyleNone>
    <DockStyleRight>将所选组件停靠到右边.</DockStyleRight>
    <DockStyleTop>将所选组件停靠到上边.</DockStyleTop>
    <FontGrow>扩大文本尺寸.</FontGrow>
    <FontName>文本字体.</FontName>
    <FontNameWatermark>水印文本字体.</FontNameWatermark>
    <FontShrink>减小文本尺寸.</FontShrink>
    <FontSize>字体尺寸.</FontSize>
    <FontSizeWatermark>更改字体尺寸.</FontSizeWatermark>
    <FontStyleBold>文本加粗.</FontStyleBold>
    <FontStyleBoldWatermark>水印文本加粗.</FontStyleBoldWatermark>
    <FontStyleItalic>文本倾斜.</FontStyleItalic>
    <FontStyleItalicWatermark>水印文本倾斜.</FontStyleItalicWatermark>
    <FontStyleStrikeout>删去这段文字</FontStyleStrikeout>
    <FontStyleUnderline>文本加下划线.</FontStyleUnderline>
    <FontStyleUnderlineWatermark>水印文本加下划线.</FontStyleUnderlineWatermark>
    <FormatBoolean>这种格式是用来格式化布尔类型的值.</FormatBoolean>
    <FormatCurrency>显示为货币格式值.它允许显示带有默认货币符号的值.</FormatCurrency>
    <FormatCustom>该类型用于根据定制要求显示数值.该类型是掩码格式下允许的数据格式.</FormatCustom>
    <FormatDate>显示日期格式的数值.日期格式基于区域的日期设置.</FormatDate>
    <FormatGeneral>显示无指定格式的数值.</FormatGeneral>
    <FormatNumber>用于数字的常规显示.</FormatNumber>
    <FormatPercentage>显示百分数格式的数值.数字被100整除后转换为百分数.</FormatPercentage>
    <FormatTime>显示时间格式的数值.时间格式基于区域的时间设置.</FormatTime>
    <FormNew>创建一个新的对话框窗体.</FormNew>
    <GridMode>用线或点显示网格.</GridMode>
    <ImageAlignment>将水印图像放到页面上.</ImageAlignment>
    <ImageTransparency>改变水印图像的透明度.</ImageTransparency>
    <Interaction>控制选定组件的交互</Interaction>
    <LineSpacing>选择文本之间或段落之间的间距。</LineSpacing>
    <Link>把组件链接到当前的容器.</Link>
    <LoadImage>从文件加载水印图像.</LoadImage>
    <Lock>锁定组件. 组件不能移动和变更尺寸.</Lock>
    <MainMenu>单击此处查看报表的可操作项, 包括打开、关闭、预览.</MainMenu>
    <MakeHorizontalSpacingEqual>允许你在所选组件中设置相等水平间距.</MakeHorizontalSpacingEqual>
    <MakeVerticalSpacingEqual>允许你在所选组件中设置相等垂直间距.</MakeVerticalSpacingEqual>
    <Margins>选择当前页面的边缘.</Margins>
    <menuCheckIssues>查看全篇报表，找出错误、警告并更改修正.</menuCheckIssues>
    <menuDesignerOptions>设置报表设计器选项.</menuDesignerOptions>
    <menuEditClearContents>清除内容.</menuEditClearContents>
    <menuEditCopy>复制所选组件并把它放到剪贴板上.</menuEditCopy>
    <menuEditCut>剪切报表的所选组件并把它放到剪贴板上.</menuEditCut>
    <menuEditDelete>删除所选组件.</menuEditDelete>
    <menuEditPaste>粘贴剪贴板的内容到报表里.</menuEditPaste>
    <menuFAQPage>转到网页页面询问常见问题.</menuFAQPage>
    <menuGlobalizationStrings>调用当前的全球化字符串编辑报告.</menuGlobalizationStrings>
    <menuHelpAboutProgramm>获得关于报表生成器和.NET框架版本的信息.</menuHelpAboutProgramm>
    <menuHomePage>转到产品的主页.</menuHomePage>
    <menuPageOptions>设置当前页面的基本参数. 使用属性面板可以变更所有页面选项.</menuPageOptions>
    <menuPagesManager>运行页面管理器. 包括移动、删除和创建新页面.</menuPagesManager>
    <menuPreviewSettings>调用当前报表的预览设置编辑. 设置只能在查看当前报表时应用.</menuPreviewSettings>
    <menuPrint>在打印前选择打印机，打印数量和其他选项.</menuPrint>
    <menuPrintPreview>打印前预览报表.</menuPrintPreview>
    <menuPrintQuick>直接用默认打印机打印报表，不显示打印对话框.</menuPrintQuick>
    <menuReportOptions>设置报表的基本选项.所有选项均可通过属性面板变更.</menuReportOptions>
    <menuStyleDesigner>调用当前报表的样式设计器.</menuStyleDesigner>
    <menuSupport>在支持页面咨询问题.</menuSupport>
    <menuViewAlignToGrid>组件网格对齐.</menuViewAlignToGrid>
    <menuViewNormal>页面的标准视图.</menuViewNormal>
    <menuViewPageBreakPreview>带有部分边框的页面显示模式.</menuViewPageBreakPreview>
    <menuViewQuickInfo>显示组件的快捷信息：组件名称，替换入口，内容，时间等</menuViewQuickInfo>
    <menuViewShowGrid>打开网格线直到使报表对象对齐.</menuViewShowGrid>
    <menuViewShowHeaders>显示带区页眉.</menuViewShowHeaders>
    <menuViewShowOrder>在页面显示组件顺序.</menuViewShowOrder>
    <menuViewShowRulers>查看标尺,它用于在页面上测量和整理对象.</menuViewShowRulers>
    <menuViewShowSmartGuides>显示智能向导以定位组件</menuViewShowSmartGuides>
    <MoveBackward>按页面上放置组件的顺序将组件移至更高一级.</MoveBackward>
    <MoveForward>按页面上放置组件的顺序将组件移至更低一级..</MoveForward>
    <Orientation>使页面在纵向和横向间切换.</Orientation>
    <PageDelete>删除报表的当前页面.</PageDelete>
    <PageNew>创建一个新页面.</PageNew>
    <PageSetup>显示页面设置对话窗.</PageSetup>
    <PageSize>选择当前报表页面的纸张大小.</PageSize>
    <PagesManager>运行页面管理器.</PagesManager>
    <PressF1>按F1查询更多资料</PressF1>
    <Redo>重做报表里前面取消的变更.</Redo>
    <ReportNew>创建一个新报表.</ReportNew>
    <ReportOpen>在设计器里打开报表.</ReportOpen>
    <ReportPreview>在查看器预览编辑了的报告.</ReportPreview>
    <ReportSave>保存当前编辑的报表.</ReportSave>
    <SelectAll>在当前页面全选组件 .</SelectAll>
    <SelectUILanguage>选择用户界面语言.</SelectUILanguage>
    <SendToBack>将所选组件移至相对于其他组件的后面.</SendToBack>
    <ServicesConfigurator>运行服务配置器.</ServicesConfigurator>
    <Shadow>显示组件阴影.</Shadow>
    <ShowBehind>在组件后显示水印文本.</ShowBehind>
    <ShowImageBehind>把水印图像放到页面所有组件后.</ShowImageBehind>
    <ShowToolbox>显示工具箱.</ShowToolbox>
    <StimulsoftHelp>帮助 Stimulsoft</StimulsoftHelp>
    <StyleDesigner>运行样式设计器.</StyleDesigner>
    <TellMeMore>阐述更多</TellMeMore>
    <Text>水印文本.</Text>
    <TextBrush>更改文本颜色.</TextBrush>
    <TextBrushWatermark>更改水印文本的颜色.</TextBrushWatermark>
    <TextColor>更改文本颜色.</TextColor>
    <TextFormat>选择值格式.</TextFormat>
    <ToolbarStyle>将所选样式应用到所有选择组件.</ToolbarStyle>
    <Undo>取消报告的最新变更.</Undo>
    <WordWrap>组件文本自动换行.</WordWrap>
    <Zoom>指定报表的缩放比例.</Zoom>
  </HelpDesigner>
  <HelpDialogs>
    <StiButtonControl>代表按钮控件.</StiButtonControl>
    <StiCheckBoxControl>代表标志控件.</StiCheckBoxControl>
    <StiCheckedListBoxControl>显示列表对象,在每个元素的左边有一个选择标志.</StiCheckedListBoxControl>
    <StiComboBoxControl>代表组合框,它要么用于直接把值键入控件,要么从现有的选项列表中选择.</StiComboBoxControl>
    <StiDateTimePickerControl>代表允许选择日期和时间的控件. 同时它也允许你以指定的格式输出日期和时间.</StiDateTimePickerControl>
    <StiGridControl>代表包括列与行的网格控件.</StiGridControl>
    <StiGroupBoxControl>代表为界面内容创建的由边框和标题栏组成的容器控件.</StiGroupBoxControl>
    <StiLabelControl>标签控件是在表格上呈现文本的一个用户界面控件. 它通常是没有互动性的静态控件.</StiLabelControl>
    <StiListBoxControl>包含元素选择列表.</StiListBoxControl>
    <StiListViewControl>代表显示数据元素列表的列表视图控件.</StiListViewControl>
    <StiLookUpBoxControl>包含可选择的元素列表.</StiLookUpBoxControl>
    <StiNumericUpDownControl>代表显示数值的控件.</StiNumericUpDownControl>
    <StiPanelControl>面板元素用于放置和排列对象.</StiPanelControl>
    <StiPictureBoxControl>代表显示图像的控件.</StiPictureBoxControl>
    <StiRadioButtonControl>代表单选按钮控件,只允许用户在预定义选择项中选择一个. </StiRadioButtonControl>
    <StiRichTextBoxControl>代表编辑量扩大的富文本控件.</StiRichTextBoxControl>
    <StiTextBoxControl>代表用于显示或编辑文本的文本控件.</StiTextBoxControl>
    <StiTreeViewControl>代表用于将分级式数据显示为树的组件.</StiTreeViewControl>
  </HelpDialogs>
  <HelpViewer>
    <AddPageBreaks>报表页面的可视化分隔符.</AddPageBreaks>
    <AllowAddOrModifyTextAnnotations>限制访问文档中的注释.</AllowAddOrModifyTextAnnotations>
    <AllowCopyTextAndGraphics>限制访问复制信息.</AllowCopyTextAndGraphics>
    <AllowEditable>允许改变组件的可编辑属性的启用性.</AllowEditable>
    <AllowModifyContents>限制对文本编辑访问.</AllowModifyContents>
    <AllowPrintDocument>限制打印操作的访问.</AllowPrintDocument>
    <Bookmarks>显示书签面板,用于快速导航直接跳到书签的位置.</Bookmarks>
    <BorderType>组件边框类型:简单——画边框的组件与字符+,-; 单个字符-用单个方框绘制字符画边框, 双字符-用双方框绘制字符画边框.</BorderType>
    <Close>关闭报表预览.</Close>
    <CloseDotMatrix>关闭点矩阵查看器.</CloseDotMatrix>
    <Compressed>压缩备好的文档. 建议通常包括文件压缩.</Compressed>
    <CompressToArchive>打包所有文件和文件夹压缩存档.</CompressToArchive>
    <ContinuousPages>报表页的放置模式为一个垂直条带.</ContinuousPages>
    <CurrentPage>处理当前页面. 如果选择此选项，所选报表页面将随之处理.</CurrentPage>
    <CutEdges>修正报告页面的边框.</CutEdges>
    <CutLongLines>通过组件边框修正文本行.</CutLongLines>
    <DashboardContains1>很抱歉，此报表包含一个仪表板</DashboardContains1>
    <DashboardContains2>您应该直接在设计器中打开此报表</DashboardContains2>
    <DashboardContains3>您不能从报表查看器执行此操作</DashboardContains3>
    <DigitalSignature>文件的数字签名.</DigitalSignature>
    <DitheringType>抖动类型：无-没有抖动，有序， FloydSt. - 带有抖动.</DitheringType>
    <DotMatrixMode>此模式允许您看到如果使用点阵打印机打印报表时的样子.</DotMatrixMode>
    <DrawBorder>用图形字符绘制组件边框.</DrawBorder>
    <Edit>编辑组件.</Edit>
    <EmbeddedFonts>将字体文件嵌入到PDF文件里.</EmbeddedFonts>
    <EmbeddedImageData>将图像直接嵌入到HTML文件里.</EmbeddedImageData>
    <Encoding>导出后报表文本的编码.</Encoding>
    <EncodingData>创建文件时使用的编码.</EncodingData>
    <EncryptionKeyLength>密码键的长度. 密码越长，文件越难被解密,因此,文档安全性级别也更高.</EncryptionKeyLength>
    <ExportDataOnly>只导出数据带区(表格控件，分级带区).</ExportDataOnly>
    <ExportEachPageToSheet>每页报表页分别导出到分开的Excel工作表.</ExportEachPageToSheet>
    <ExportMode>导出时运用筛选条件. 仅数据- 只有数据带区(表格控件，分级带区)将会导出. 数据和页眉/页脚 - 数据带去(表格控件，分级带区)和它们的页眉/页脚将会导出. 所有带区-报表的所有带区都会导出.</ExportMode>
    <ExportModeHtml> HTML 页面标记的方式.</ExportModeHtml>
    <ExportModeRtf>导出后报告数据的呈现. 表格 - 报告看起来像一个表格，其中每个报告组件是一个表格单元. 框架-每个控件就像一个单一的框架，而他们彼此之间也没有任何关系.</ExportModeRtf>
    <ExportObjectFormatting>从数据带应用格式设置导出数据(表格控件，层次带).</ExportObjectFormatting>
    <ExportPageBreaks>在 Excel工作表上显示报告页面的边框.</ExportPageBreaks>
    <ExportRtfTextAsImage>将RTF 文件转换成图像. 如果选项可用，那么当导出时， RichText分解成更简单的原语支持PDF格式. 带有复杂格式的RichText (带有图像，表格) 可能会出现转换错误. 这种情况建议启用这个选项.</ExportRtfTextAsImage>
    <Find>在报告里找到文本.</Find>
    <FullScreen>全屏阅读.</FullScreen>
    <GetCertificateFromCryptoUI>使用系统密码库界面.</GetCertificateFromCryptoUI>
    <ImageCompressionMethod>压缩方法: JPEG - th可能会引起质量损失, Flate – 无质量损失，简单、有序, FloydSt. - 图像单色输出.</ImageCompressionMethod>
    <ImageFormat>完成文件里的图像格式.</ImageFormat>
    <ImageQuality>允许选择文件图像质量和尺寸的比率. 质量越高，完成文件的尺寸越大.</ImageQuality>
    <ImageQualityPdf>此选项只适用于JPEG 压缩, 允许选择文件图像质量和尺寸的比率.质量越高，完成文件的尺寸越大.</ImageQualityPdf>
    <ImageResolution>象素每英寸数. 象素数越高，图像质量越好。 完成文件的尺寸会大很多.</ImageResolution>
    <ImageType>图像的配色方案 : 颜色 - 导出后的图片会与查看器的图片完全匹配; 灰色 – 导出后的图像呈灰度; 单色 - 图像将完全为黑白色.同时,其应该有三种模式None, Ordered, FloydSt.</ImageType>
    <KillSpaceLines>移除文档中的空白行/列.</KillSpaceLines>
    <MultipleFiles>每个报表页面可以是一个分离的文件.</MultipleFiles>
    <Open>在预览窗口上打开以前保存的报表.</Open>
    <OpenAfterExport>通过文件类型的程序设置在导出后自动打开创建的文件.</OpenAfterExport>
    <OwnerPassword>文件存取操作的密码.</OwnerPassword>
    <PageAll>处理所有报表页.</PageAll>
    <PageDelete>删除所选报表页.</PageDelete>
    <PageDesign>编辑报表设计器里的所选页面.</PageDesign>
    <PageFirst>转到报表的第一页.</PageFirst>
    <PageGoTo>转到指定的报表页.</PageGoTo>
    <PageLast>转到报表的最后一页.</PageLast>
    <PageNew>添加新页面到报表.</PageNew>
    <PageNext>转到下一报表页.</PageNext>
    <PagePrevious>转到上一报表页.</PagePrevious>
    <PageSize>更改报表的页面参数.</PageSize>
    <Parameters>显示参数面板,在报表渲染时使用.</Parameters>
    <PdfACompliance>支持长期存档和储存电子文档的标准.</PdfACompliance>
    <Print>打印报表.</Print>
    <PutFeedPageCode>用指定字符在最终文档里插入页面.</PutFeedPageCode>
    <RangePages>处理页面数目. 你可以指定单一页面，页面列表(用逗号隔开), 或者指定范围，可通过设置范围的起始页和结束页，用 "-" 隔开。 例如: 1,3,5-12.</RangePages>
    <RemoveEmptySpaceAtBottom>最小化页面底部的空白空间.</RemoveEmptySpaceAtBottom>
    <Resources>显示资源面板</Resources>
    <RestrictEditing>限制Word文档的变化.</RestrictEditing>
    <Save>保存报表以便进一步使用.</Save>
    <ScaleHtml>导出后报表页面和项的尺寸.</ScaleHtml>
    <ScaleImage>导出后报表的尺寸. 尺寸越小，每英寸的像素数越大，反之亦然.</ScaleImage>
    <SendEMail>通过邮件发送报表.</SendEMail>
    <Separator>CSV 文件中数据的分隔符.</Separator>
    <SkipColumnHeaders>启用/禁用列标题.</SkipColumnHeaders>
    <StandardPdfFonts>14 标准Adobe字体. 如果此项可用，那么在PDF文件中只能使用标准的14个字体.所有报表的字体都会转换为他们.</StandardPdfFonts>
    <SubjectNameString>凭证识别符.这个识别符是凭证拥有者的名字或者是名字的一部分.</SubjectNameString>
    <Thumbnails>显示缩略图，它可以用于快速导航找到你想要跳转的报表部分.</Thumbnails>
    <TiffCompressionScheme>TIFF文件的压缩方案.</TiffCompressionScheme>
    <ToolEditor>该工具允许在报表视图器中直接对文本组件内容进行编辑.</ToolEditor>
    <TypeExport>报表将转换到的文件.</TypeExport>
    <UnsafeCompilation1>本文档使用编译过程。在某些情况下，这可能不安全。因此，文件将以安全模式加载，以最大限度地减少任何潜在风险.</UnsafeCompilation1>
    <UnsafeCompilation2>如果您要求以原始形式使用此文档，请联系您的开发人员</UnsafeCompilation2>
    <UseDefaultSystemEncoding>使用默认的系统编码.</UseDefaultSystemEncoding>
    <UseOnePageHeaderAndFooter>只使用首个页眉和最后一个的页脚. 移除剩下的页眉和页脚.</UseOnePageHeaderAndFooter>
    <UsePageHeadersAndFooters>导出页面页眉和页脚作为MS Word文档的页眉和页脚.</UsePageHeadersAndFooters>
    <UserPassword>打开文件所需的密码.</UserPassword>
    <UseUnicode>编码字符扩展支持. 它影响PDF文件里的内部字符编码, 并提高从PDF文件中复制文本的能力.</UseUnicode>
    <ViewModeContinuous>连续显示所有报表页.</ViewModeContinuous>
    <ViewModeMultiplePages>缩放报表以便在窗口中显示更多的页面.</ViewModeMultiplePages>
    <ViewModeSinglePage>在预览窗口显示一个单一页面.</ViewModeSinglePage>
    <ZoomMultiplePages>缩放报表使得所选页面适应窗口大小.</ZoomMultiplePages>
    <ZoomOnePage>缩放报表以便在窗口中显示整个页面.</ZoomOnePage>
    <ZoomPageWidth>缩放报表以便报表的宽度与窗口的宽度相适应.</ZoomPageWidth>
    <ZoomTwoPages>缩放报表以便在窗口中显示双页.</ZoomTwoPages>
    <ZoomTxt>报表尺寸: X - 改变水平尺寸, Y - 改变垂直尺寸.</ZoomTxt>
  </HelpViewer>
  <Interface>
    <Mouse>鼠标</Mouse>
    <MouseDescription>优化鼠标使用</MouseDescription>
    <Touch>触摸</Touch>
    <TouchDescription>优化触摸使用</TouchDescription>
  </Interface>
  <MainMenu>
    <menuCheckIssues>检查问题</menuCheckIssues>
    <menuContextClone>克隆...</menuContextClone>
    <menuContextDesign>设计...</menuContextDesign>
    <menuContextTextFormat>文字格式...</menuContextTextFormat>
    <menuConvertToCheckBox>转换到复选框</menuConvertToCheckBox>
    <menuConvertToImage>转换到图像</menuConvertToImage>
    <MenuConvertToRichText>转换到富文本</MenuConvertToRichText>
    <menuConvertToText>转换到文本</menuConvertToText>
    <menuDeleteColumn>删除列</menuDeleteColumn>
    <menuDeleteRow>删除行</menuDeleteRow>
    <menuEdit>编辑(&amp;E)</menuEdit>
    <menuEditBusinessObjectFromDataSetNew>来自数据组的新Business Object...</menuEditBusinessObjectFromDataSetNew>
    <menuEditBusinessObjectNew>新建Business Object...</menuEditBusinessObjectNew>
    <menuEditCalcColumnNew>新建计算列...</menuEditCalcColumnNew>
    <menuEditCantRedo>无法重做</menuEditCantRedo>
    <menuEditCantUndo>无法撤销</menuEditCantUndo>
    <menuEditCategoryNew>新建分类...</menuEditCategoryNew>
    <menuEditClearContents>清除内容</menuEditClearContents>
    <menuEditColumnNew>新建列...</menuEditColumnNew>
    <menuEditConnectionNew>新建连接...</menuEditConnectionNew>
    <menuEditCopy>复制(&amp;C)</menuEditCopy>
    <menuEditCut>剪切(&amp;U)</menuEditCut>
    <menuEditDataParameterNew>新建参数...</menuEditDataParameterNew>
    <menuEditDataSourceNew>新建数据源...</menuEditDataSourceNew>
    <menuEditDataSourcesNew>新建数据源...</menuEditDataSourcesNew>
    <menuEditDataTransformationNew>更新数据转换...</menuEditDataTransformationNew>
    <menuEditDelete>删除(&amp;D)</menuEditDelete>
    <menuEditEdit>编辑</menuEditEdit>
    <menuEditImportRelations>导入关系...</menuEditImportRelations>
    <menuEditPaste>粘贴(&amp;P)</menuEditPaste>
    <menuEditRedo>重做(&amp;R)</menuEditRedo>
    <menuEditRedoText>重做(&amp;R){0}</menuEditRedoText>
    <menuEditRelationNew>新建关系...</menuEditRelationNew>
    <menuEditRemoveUnused>移除未使用的项目</menuEditRemoveUnused>
    <menuEditResourceNew>New Resource...</menuEditResourceNew>
    <menuEditSelectAll>全选(&amp;A)</menuEditSelectAll>
    <menuEditSynchronize>同步</menuEditSynchronize>
    <menuEditUndo>撤销(&amp;U)</menuEditUndo>
    <menuEditUndoText>撤销(&amp;U) {0}</menuEditUndoText>
    <menuEditVariableNew>新建变量...</menuEditVariableNew>
    <menuEditViewData>查看数据...</menuEditViewData>
    <menuEmbedAllDataToResources>嵌入所有数据至资源</menuEmbedAllDataToResources>
    <menuFile>文件(&amp;F)</menuFile>
    <menuFileClose>关闭(&amp;C)</menuFileClose>
    <menuFileDashboardDelete>删除仪表盘</menuFileDashboardDelete>
    <menuFileDashboardNew>更新仪表盘</menuFileDashboardNew>
    <menuFileDashboardOpen>&amp;打开仪表盘...</menuFileDashboardOpen>
    <menuFileDashboardSaveAs>保存仪表盘 &amp;为...</menuFileDashboardSaveAs>
    <menuFileExit>退出(&amp;X)</menuFileExit>
    <menuFileExportXMLSchema>导出为XML Schema...</menuFileExportXMLSchema>
    <menuFileFormNew>新建窗体</menuFileFormNew>
    <menuFileImportXMLSchema>导入XML Schema...</menuFileImportXMLSchema>
    <menuFileMerge>合并...</menuFileMerge>
    <menuFileMergeXMLSchema>合并XML Schema...</menuFileMergeXMLSchema>
    <menuFileNew>新建(&amp;N)</menuFileNew>
    <menuFileOpen>打开(&amp;O)</menuFileOpen>
    <menuFilePageDelete>删除页面</menuFilePageDelete>
    <menuFilePageNew>新建页面</menuFilePageNew>
    <menuFilePageOpen>打开页面...</menuFilePageOpen>
    <menuFilePageSaveAs>保存页面为...</menuFilePageSaveAs>
    <menuFilePageSetup>页面设置...</menuFilePageSetup>
    <menuFileRecentDocuments>最近文档</menuFileRecentDocuments>
    <menuFileRecentLocations>最近的位置</menuFileRecentLocations>
    <menuFileReportNew>新建报表(&amp;N)...</menuFileReportNew>
    <menuFileReportOpen>打开报表(&amp;O)...</menuFileReportOpen>
    <menuFileReportOpenFromGoogleDocs>打开Google Docs 报表...</menuFileReportOpenFromGoogleDocs>
    <menuFileReportPreview>预览报表(&amp;P)...</menuFileReportPreview>
    <menuFileReportSave>保存报表(&amp;S)...</menuFileReportSave>
    <menuFileReportSaveAs>保存报表为(&amp;A)...</menuFileReportSaveAs>
    <menuFileReportSaveAsToGoogleDocs>保存报表到Google Docs...</menuFileReportSaveAsToGoogleDocs>
    <menuFileReportSetup>报表设置(&amp;S)...</menuFileReportSetup>
    <menuFileReportWizardNew>新建报表向导...</menuFileReportWizardNew>
    <menuFileSave>保存报表(&amp;S)...</menuFileSave>
    <menuFileSaveAs>另存为...</menuFileSaveAs>
    <menuFileScreenNew>新屏幕</menuFileScreenNew>
    <menuHelp>帮助(&amp;H)</menuHelp>
    <menuHelpAboutProgramm>关于(&amp;A)...</menuHelpAboutProgramm>
    <menuHelpContents>内容(&amp;C)...</menuHelpContents>
    <menuHelpDemos>演示</menuHelpDemos>
    <menuHelpDocumentation>文档</menuHelpDocumentation>
    <menuHelpFAQPage>FAQ</menuHelpFAQPage>
    <menuHelpForum>论坛</menuHelpForum>
    <menuHelpHowToRegister>如何注册</menuHelpHowToRegister>
    <menuHelpProductHomePage>产品主页</menuHelpProductHomePage>
    <menuHelpSamples>示例</menuHelpSamples>
    <menuHelpSupport>技术支持(&amp;S)</menuHelpSupport>
    <menuHelpTrainingCourses>培训课程</menuHelpTrainingCourses>
    <menuHelpVideos>视频</menuHelpVideos>
    <menuInsertColumnToLeft>左侧插入列</menuInsertColumnToLeft>
    <menuInsertColumnToRight>右侧插入列</menuInsertColumnToRight>
    <menuInsertRowAbove>上方插入行</menuInsertRowAbove>
    <menuInsertRowBelow>下方插入行</menuInsertRowBelow>
    <menuJoinCells>连接单元格</menuJoinCells>
    <menuMakeThisRelationActive>激活该关系</menuMakeThisRelationActive>
    <menuSelectColumn>选择列</menuSelectColumn>
    <menuSelectRow>选择行</menuSelectRow>
    <menuTable>表格</menuTable>
    <menuTools>工具(&amp;T)</menuTools>
    <menuToolsDataStore>数据存储(&amp;S)...</menuToolsDataStore>
    <menuToolsDictionary>字典(&amp;D)...</menuToolsDictionary>
    <menuToolsOptions>选项(&amp;O)...</menuToolsOptions>
    <menuToolsPagesManager>页面管理(&amp;P)...</menuToolsPagesManager>
    <menuToolsServicesConfigurator>服务设置(&amp;C)...</menuToolsServicesConfigurator>
    <menuToolsStyleDesigner>样式设计(&amp;D)...</menuToolsStyleDesigner>
    <menuView>查看(&amp;V)</menuView>
    <menuViewAlignToGrid>对齐到网格</menuViewAlignToGrid>
    <menuViewNormal>普通(&amp;N)</menuViewNormal>
    <menuViewOptions>选项</menuViewOptions>
    <menuViewPageBreakPreview>分页预览(&amp;B)</menuViewPageBreakPreview>
    <menuViewQuickInfo>提示信息</menuViewQuickInfo>
    <menuViewQuickInfoNone>无</menuViewQuickInfoNone>
    <menuViewQuickInfoOverlay>显示于组件之上</menuViewQuickInfoOverlay>
    <menuViewQuickInfoShowAliases>显示别名</menuViewQuickInfoShowAliases>
    <menuViewQuickInfoShowComponentsNames>显示组件名称</menuViewQuickInfoShowComponentsNames>
    <menuViewQuickInfoShowContent>显示内容</menuViewQuickInfoShowContent>
    <menuViewQuickInfoShowEvents>显示事件</menuViewQuickInfoShowEvents>
    <menuViewQuickInfoShowFields>显示字段</menuViewQuickInfoShowFields>
    <menuViewQuickInfoShowFieldsOnly>只显示字段</menuViewQuickInfoShowFieldsOnly>
    <menuViewShowGrid>显示网格</menuViewShowGrid>
    <menuViewShowHeaders>显示标题栏</menuViewShowHeaders>
    <menuViewShowInsertTab>显示 '插入' 选项卡</menuViewShowInsertTab>
    <menuViewShowOrder>显示次序</menuViewShowOrder>
    <menuViewShowRulers>显示标尺</menuViewShowRulers>
    <menuViewShowToolbox>显示工具箱</menuViewShowToolbox>
    <menuViewToolbars>工具栏</menuViewToolbars>
  </MainMenu>
  <Map>
    <LinkDataForm>链接数据</LinkDataForm>
    <MapEditorForm>地图编辑器</MapEditorForm>
  </Map>
  <MathFormula>
    <Alphabets>字母</Alphabets>
    <Arrows>箭头</Arrows>
    <Formulas>公式</Formulas>
    <Maths>数学</Maths>
    <Operators>运算符</Operators>
  </MathFormula>
  <Messages>
    <ChangeRequestTimeout>SQL查询的请求超时超过报表设计器的请求超时。将设计器组件的请求超时设置为不小于超过{0}秒。</ChangeRequestTimeout>
    <DoNotShowAgain>不再显示</DoNotShowAgain>
    <LatexFormat>LaTeX格式用于显示数学公式。</LatexFormat>
    <MessageTimeOutExpired>命令超时已过期!</MessageTimeOutExpired>
    <MustBeFile>必须是｛0｝文件。</MustBeFile>
    <RenderingWillOccurInTheInterpretationMode>查看共享报告时，以解释模式呈现!</RenderingWillOccurInTheInterpretationMode>
    <ResourceCannotBeDeleted>资源 "{0}" 在报表中使用，无法删除!</ResourceCannotBeDeleted>
    <ShareURLOfTheItemHasBeenUpdated>该项目的共享URL已更新!</ShareURLOfTheItemHasBeenUpdated>
    <ShareYourReportYouShouldSave>为分享您的报表，请您将其保存到Stimulsoft云端!</ShareYourReportYouShouldSave>
    <SwitchingBetweenModes>在模式之间切换将导致更改无法生效。你想继续吗?</SwitchingBetweenModes>
    <SwitchParametersOrientation>请将“ParametersOrientation”报表属性的值更改为“｛0｝”，以便在参数面板上显示此类别.</SwitchParametersOrientation>
    <TextRegistrationSuccessfully>
     注册成功完成!

      电子邮件已发送至"{0}".
      该邮件包含一个确认注册的链接。请您按照邮件中的链接进行操作，完成注册。
    </TextRegistrationSuccessfully>
    <ThisFieldIsNotSpecified>未指定该字段!</ThisFieldIsNotSpecified>
    <ThisFunctionEmbedsAllReportDataToTheReport>此功能将嵌入所有报表数据至报表资源中，并使您的报表独立。小心！您的所有数据设置将被更改，且不可复原。请先备份您的报表。{0}您确定吗?</ThisFunctionEmbedsAllReportDataToTheReport>
    <YouNeedToLoginFirstToStartUsingTheSoftware>您需要先登录才能开始使用本软件。如果您退出登录，应用程序将关闭。</YouNeedToLoginFirstToStartUsingTheSoftware>
  </Messages>
  <Notices>
    <AccessDenied>拒绝访问!</AccessDenied>
    <AccountLocked>您的账户已被锁定。为解除锁定，请将您的请求发送至********************!</AccountLocked>
    <ActivationContact1>请联系我们的销售部门</ActivationContact1>
    <ActivationContact2> 解决此问题</ActivationContact2>
    <ActivationExpiriedBeforeFirstRelease>您订阅的报表编辑软件比我们的第一个版本早过期。服务器已发布！</ActivationExpiriedBeforeFirstRelease>
    <ActivationLicenseIsNotCorrect>许可证文件不正确！请联系我们的销售部门********************!</ActivationLicenseIsNotCorrect>
    <ActivationLockedAccount>您的账户已被锁定。请联系我们的销售部门********************!</ActivationLockedAccount>
    <ActivationLockedAccountExt>您的帐户已锁定</ActivationLockedAccountExt>
    <ActivationMaxActivationsReached>您已达到最大激活次数。请联系我们的销售部门********************!</ActivationMaxActivationsReached>
    <ActivationMaxActivationsReached1>您可以通过</ActivationMaxActivationsReached1>
    <ActivationMaxActivationsReached2>Stimulsoft账户.</ActivationMaxActivationsReached2>
    <ActivationMaxComputersReached>您的机器注册数量已经达到最上限</ActivationMaxComputersReached>
    <ActivationServerIsNotAvailableNow>报表编辑软件服务器现在不可用！请稍后再试！.</ActivationServerIsNotAvailableNow>
    <ActivationServerVersionNotAllowed>您无法激活此版本的Stimulsoft Reports.服务器，因为您订阅的程序已过期！请登录您的账户，检查可以安装和激活的版本。</ActivationServerVersionNotAllowed>
    <ActivationSomeTroublesOccurred>激活期间出错。请稍后再试。</ActivationSomeTroublesOccurred>
    <ActivationTrialExpired>请您购买以继续使用Stimulsoft Designer</ActivationTrialExpired>
    <ActivationTrialExtend1>如果你有兴趣延长试用期，请</ActivationTrialExtend1>
    <ActivationTrialExtend2>告诉我们为什么</ActivationTrialExtend2>
    <ActivationUserNameOrPasswordIsWrong>您的用户名（电子邮件）或密码错误!</ActivationUserNameOrPasswordIsWrong>
    <ActivationWrongAccountType>您的账户类型不允许激活服务器!</ActivationWrongAccountType>
    <Alert>警告</Alert>
    <AuthAccountAlreadyExists>此用户名（电子邮件）的帐户已存在。若要将其与您的｛0｝帐户关联，请使用您的Stimulsoft帐户密码.</AuthAccountAlreadyExists>
    <AuthAccountCantBeUsedNow>该账户现在不能使用!</AuthAccountCantBeUsedNow>
    <AuthAccountIsNotActivated>账户尚未激活! 请按照注册时发送到电子邮件的说明进行操作。</AuthAccountIsNotActivated>
    <AuthCantChangeRoleBecauseLastAdministratorUser>无法更改用户角色，因为这是此工作区中的最后一个管理员用户!</AuthCantChangeRoleBecauseLastAdministratorUser>
    <AuthCantChangeRoleBecauseLastSupervisorUser>无法更改用户角色，因为这是此服务器上的最后一个主管用户!</AuthCantChangeRoleBecauseLastSupervisorUser>
    <AuthCantChangeSystemRole>无法更改系统角色!</AuthCantChangeSystemRole>
    <AuthCantDeleteHimselfUser>用户不能删除自己!</AuthCantDeleteHimselfUser>
    <AuthCantDeleteLastAdministratorUser>无法删除该用户，因为这是此工作区的最后一个管理员用户!</AuthCantDeleteLastAdministratorUser>
    <AuthCantDeleteLastSupervisorUser>无法删除用户，因为这是此服务器上的最后一个主管用户!</AuthCantDeleteLastSupervisorUser>
    <AuthCantDeleteSystemRole>无法删除该角色，因为它是系统角色!</AuthCantDeleteSystemRole>
    <AuthCantDisableUserBecauseLastAdministratorUser>无法禁用用户，因为这是此工作区的最后一个管理员用户!</AuthCantDisableUserBecauseLastAdministratorUser>
    <AuthCantDisableUserBecauseLastSupervisorUser>无法禁用用户，因为这是此服务器上的最后一个主管用户!</AuthCantDisableUserBecauseLastSupervisorUser>
    <AuthFirstNameIsNotSpecified>未指定名字!</AuthFirstNameIsNotSpecified>
    <AuthLastNameIsNotSpecified>未指定姓氏!</AuthLastNameIsNotSpecified>
    <AuthOAuthIdNotSpecified>未指定OAuth标识!</AuthOAuthIdNotSpecified>
    <AuthPasswordIsNotCorrect>密码错误!</AuthPasswordIsNotCorrect>
    <AuthPasswordIsNotSpecified>未指定密码!</AuthPasswordIsNotSpecified>
    <AuthPasswordIsTooLong>密码太长</AuthPasswordIsTooLong>
    <AuthPasswordIsTooShort>密码太短 (最小长度为6个字符)!</AuthPasswordIsTooShort>
    <AuthRoleCantBeDeletedBecauseUsedByUsers>无法删除该角色，其他用户正在使用.</AuthRoleCantBeDeletedBecauseUsedByUsers>
    <AuthRoleNameAlreadyExists>具有指定名称 "{0}"的用户已存在!</AuthRoleNameAlreadyExists>
    <AuthRoleNameIsSystemRole>具有指定名称"{0}"的角色是系统角色!</AuthRoleNameIsSystemRole>
    <AuthSendMessageWithInstructions>带有进一步说明的消息已发送到"{0}"!</AuthSendMessageWithInstructions>
    <AuthTokenIsNotCorrect>Token 不正确!</AuthTokenIsNotCorrect>
    <AuthUserHasLoggedOut>您已注销!</AuthUserHasLoggedOut>
    <AuthUserNameAlreadyExists>用户名（电子邮件）已存在!</AuthUserNameAlreadyExists>
    <AuthUserNameEmailIsBlocked>无法使用此电子邮件地址!</AuthUserNameEmailIsBlocked>
    <AuthUserNameIsNotSpecified>未指定用户名（电子邮件）!</AuthUserNameIsNotSpecified>
    <AuthUserNameIsTooLong>用户名（电子邮件）太长</AuthUserNameIsTooLong>
    <AuthUserNameNotAssociatedWithYourAccount>用户名(电子邮件){0}与您的{1}帐户无法关联!</AuthUserNameNotAssociatedWithYourAccount>
    <AuthUserNameOrPasswordIsNotCorrect>用户名（电子邮件）或密码错误!</AuthUserNameOrPasswordIsNotCorrect>
    <AuthUserNameShouldLookLikeAnEmailAddress>用户名应类似于电子邮件地址!</AuthUserNameShouldLookLikeAnEmailAddress>
    <AuthWorkspaceNameAlreadyInUse>工作区名称已存在!</AuthWorkspaceNameAlreadyInUse>
    <CommandTimeOut>处理命令的等待时间已过!</CommandTimeOut>
    <Congratulations>祝贺您!</Congratulations>
    <DeveloperInformation>开发者信息</DeveloperInformation>
    <EndDateShouldBeGreaterThanCurrentDate>结束日期应大于当前日期!</EndDateShouldBeGreaterThanCurrentDate>
    <EndDateShouldBeGreaterThanStartDate>结束日期应大于开始日期!</EndDateShouldBeGreaterThanStartDate>
    <ExecutionError>执行错误</ExecutionError>
    <GracePeriod>您的Subscription宽限期将在｛0｝天后到期。</GracePeriod>
    <IsIdentical>"{0}" 和 "{1}" 是相同的."</IsIdentical>
    <IsNotAuthorized>无法访问"{0}"!</IsNotAuthorized>
    <IsNotCorrect>"{0}" 错误!</IsNotCorrect>
    <IsNotDeleted>"{0}"未删除!</IsNotDeleted>
    <IsNotEqual>"{0}" 不相等!</IsNotEqual>
    <IsNotFound>未找到"{0}" !</IsNotFound>
    <IsNotRecognized>"{0}"无法识别!</IsNotRecognized>
    <IsNotSpecified>"{0}"未指定!</IsNotSpecified>
    <IsRequiredFile>您应该至少添加一个文件!</IsRequiredFile>
    <ItemCantBeAttachedToItself>项目本身不能附加!</ItemCantBeAttachedToItself>
    <ItemCantBeDeletedBecauseItemIsAttachedToOtherItems>无法删除元素"{0}" ，因为他们被附加到其他元素上!</ItemCantBeDeletedBecauseItemIsAttachedToOtherItems>
    <ItemCantBeMovedToSpecifiedPlace>无法将项目移动至指定位置!</ItemCantBeMovedToSpecifiedPlace>
    <ItemDoesNotSupport>该项不支持"{0}"!</ItemDoesNotSupport>
    <KeyAndToKeyAreEqual>Key键和ToKey键是相等的!</KeyAndToKeyAreEqual>
    <MaximumComputers>机器上限</MaximumComputers>
    <MessageMaximumFileSizeExceeded>注意! 您试图添加的文件大小超过了允许的最大量，是否仍要添加此文件（报表处理可能会显著减慢）?</MessageMaximumFileSizeExceeded>
    <NewDesignerAvailable>Stimulsoft Designer {0}可用</NewDesignerAvailable>
    <NewProduct>新产品已添加到您的订阅中!</NewProduct>
    <NewVersionsAvailable>新版本可用!</NewVersionsAvailable>
    <NotificationConnectPackage>连接“｛0｝”包以获得正确的工作</NotificationConnectPackage>
    <NotificationFailed>结果失败!</NotificationFailed>
    <NotificationFailedAddFollowingFiles>无法添加以下文件。超过了{0}MB的大小</NotificationFailedAddFollowingFiles>
    <NotificationFilesUploadingComplete>文件上传成功.</NotificationFilesUploadingComplete>
    <NotificationFileUploading>文件"{0}"正在上传.</NotificationFileUploading>
    <NotificationItemDelete>项目正在删除.</NotificationItemDelete>
    <NotificationItemDeleteComplete>已成功删除项目.</NotificationItemDeleteComplete>
    <NotificationItemExtraComponentRequired>需要额外的组件</NotificationItemExtraComponentRequired>
    <NotificationItemInstallWebView>请安装WebView2 Runtime以正确使用Blockly编辑器</NotificationItemInstallWebView>
    <NotificationItemRestore>正在恢复项目.</NotificationItemRestore>
    <NotificationItemRestoreComplete>已成功恢复项目.</NotificationItemRestoreComplete>
    <NotificationItemTransfer>项目正在转移.</NotificationItemTransfer>
    <NotificationItemTransferComplete>已成功转移项目.</NotificationItemTransferComplete>
    <NotificationItemWaitingProcessing>项目正在等待处理.</NotificationItemWaitingProcessing>
    <NotificationMailing>发送</NotificationMailing>
    <NotificationMailingComplete>邮件"{0}" 已完成.</NotificationMailingComplete>
    <NotificationMailingWaitingProcessing>邮件"{0}"正在等待处理.</NotificationMailingWaitingProcessing>
    <NotificationOperationAborted>操作已终止!</NotificationOperationAborted>
    <NotificationPackageRequiredToDisplay>若要显示此内容，请从NuGet下载“｛0｝”包</NotificationPackageRequiredToDisplay>
    <NotificationRecycleBinCleaning>回收站正在清理.</NotificationRecycleBinCleaning>
    <NotificationRecycleBinCleaningComplete>回收站已成功清理.</NotificationRecycleBinCleaningComplete>
    <NotificationRecycleBinWaitingProcessing>回收站清理正在等待处理.</NotificationRecycleBinWaitingProcessing>
    <NotificationReportExporting>报表"{0}" 正在导出</NotificationReportExporting>
    <NotificationReportExportingComplete>报表"{0}"已成功导出.</NotificationReportExportingComplete>
    <NotificationReportRendering>正在打开报表"{0}"。</NotificationReportRendering>
    <NotificationReportRenderingComplete>报表 "{0}" 已呈现.</NotificationReportRenderingComplete>
    <NotificationReportWaitingProcessing>报表 "{0}"正在等待处理.</NotificationReportWaitingProcessing>
    <NotificationReturnToDesigner>返回Stimulsoft设计器继续</NotificationReturnToDesigner>
    <NotificationSchedulerRunning>调度程序 "{0}" 正在运行.</NotificationSchedulerRunning>
    <NotificationSchedulerRunningComplete>调度程序 "{0}" 完成运行.</NotificationSchedulerRunningComplete>
    <NotificationSchedulerWaitingProcessing>调度程序 "{0}" 正在等待处理.</NotificationSchedulerWaitingProcessing>
    <NotificationTitleFilesUploading>上传文件</NotificationTitleFilesUploading>
    <NotificationTitleItemRefreshing>正在刷新"{0}"项目</NotificationTitleItemRefreshing>
    <NotificationTitleItemTransferring>正在转移"{0}"项目</NotificationTitleItemTransferring>
    <NotificationTitleMailing>正在邮寄"{0}"</NotificationTitleMailing>
    <NotificationTitleReportExporting>正在导出"{0}"报告</NotificationTitleReportExporting>
    <NotificationTitleReportRendering>正在打开"{0}"报告</NotificationTitleReportRendering>
    <NotificationTitleSchedulerRunning>调度程序"{0}"正在运行</NotificationTitleSchedulerRunning>
    <NotificationTransferring>"{0}"正在转移到"{1}".</NotificationTransferring>
    <NotificationTransferringComplete>"{0}" 成功转移到"{1}".</NotificationTransferringComplete>
    <NotificationValueIsNotCorrect>此值对类型{0}不正确</NotificationValueIsNotCorrect>
    <OutOfRange>超出范围("{0}")!</OutOfRange>
    <ParsingCommandException>处理指定的可拓展标记语言命令中的异常: {0}</ParsingCommandException>
    <PleaseLogin>请在生成报表前使用您的Stimusoft账户登录或注册一个新账户。</PleaseLogin>
    <QuotaMaximumComputingCyclesCountExceeded>超出最大计算周期数。</QuotaMaximumComputingCyclesCountExceeded>
    <QuotaMaximumDataRowsCountExceeded>超出数据源中的最大数据行。</QuotaMaximumDataRowsCountExceeded>
    <QuotaMaximumFileSizeExceeded>超出最大文件大小。</QuotaMaximumFileSizeExceeded>
    <QuotaMaximumItemsCountExceeded>超出最大项目数。</QuotaMaximumItemsCountExceeded>
    <QuotaMaximumRefreshCountExceeded>超过最大刷新次数</QuotaMaximumRefreshCountExceeded>
    <QuotaMaximumReportPagesCountExceeded>超出最大报表页数。</QuotaMaximumReportPagesCountExceeded>
    <QuotaMaximumResourcesCountExceeded>超出最大资源数。</QuotaMaximumResourcesCountExceeded>
    <QuotaMaximumResourceSizeExceeded>超过最大资源大小</QuotaMaximumResourceSizeExceeded>
    <QuotaMaximumUsersCountExceeded>超出最大用户数。</QuotaMaximumUsersCountExceeded>
    <QuotaMaximumWorkspacesCountExceeded>超出最大工作区数。</QuotaMaximumWorkspacesCountExceeded>
    <SchedulerCantRunItSelf>调度程序无法自行运行！</SchedulerCantRunItSelf>
    <SessionTimeOut>会话超时</SessionTimeOut>
    <SnapshotAlreadyProcessed>报表快照已处理完毕!</SnapshotAlreadyProcessed>
    <SpecifiedItemIsNot>"{0}"不是指定项!</SpecifiedItemIsNot>
    <SubscriptionExpired>您的订阅已过期!</SubscriptionExpired>
    <SubscriptionExpiredDate>您的订阅将于{0}过期</SubscriptionExpiredDate>
    <SubscriptionExpiredDays>在｛0｝天后过期</SubscriptionExpiredDays>
    <SubscriptionExpiredExt>您的订阅已过期，更新您的订阅以获得最新版本</SubscriptionExpiredExt>
    <SubscriptionExpiredShort>Subscription 到期</SubscriptionExpiredShort>
    <SubscriptionsOut10>订阅还有10天到期!</SubscriptionsOut10>
    <SubscriptionsOut20>订阅将在20天后到期!</SubscriptionsOut20>
    <SuccessfullyRenewed>您的订阅已成功更新!</SuccessfullyRenewed>
    <TrialToLicense>感谢您选择了Stimulsoft!</TrialToLicense>
    <VersionCopyFromItem>从项目"{0}"中复制。</VersionCopyFromItem>
    <VersionCreatedFromFile>从文件"{0}"中创建。</VersionCreatedFromFile>
    <VersionCreatedFromItem>从项目"{0}"中创建。</VersionCreatedFromItem>
    <VersionLoadedFromFile>从文件"{0}"中加载。</VersionLoadedFromFile>
    <VersionNewItemCreation>新项目创建。</VersionNewItemCreation>
    <Warning>警告</Warning>
    <WindowClosePreventWhileUploading>部分文件正在上传，现在关闭页面会丢失。您确定要关闭页面吗？</WindowClosePreventWhileUploading>
    <WithSpecifiedKeyIsNotFound>找不到具有指定键的"{0}"!</WithSpecifiedKeyIsNotFound>
    <WouldYouLikeToUpdateNow>您想现在更新吗?</WouldYouLikeToUpdateNow>
    <YourTimeSessionHasExpired>您的时间会话已过期!</YourTimeSessionHasExpired>
    <YourTrialHasExpired>您的试用期已过</YourTrialHasExpired>
    <YourTrialWillExpire>您的试用将在{0}天内过期</YourTrialWillExpire>
    <YouUsingTrialVersion>您正在使用试用版!</YouUsingTrialVersion>
  </Notices>
  <NuGet>
    <AlreadyDownloaded>已下载</AlreadyDownloaded>
    <AssemblyLoadedSuccessfully>程序集已成功加载。</AssemblyLoadedSuccessfully>
    <AssemblyNotFound>不能找到程序集{0}来使用{1}连接!</AssemblyNotFound>
    <Author>作者</Author>
    <Dependencies>依赖关系:</Dependencies>
    <Download>下载</Download>
    <DownloadAll>全部下载</DownloadAll>
    <DownloadAndInstall>下载并安装</DownloadAndInstall>
    <DownloadDataAdapter>下载数据适配器</DownloadDataAdapter>
    <Downloads>下载:</Downloads>
    <IAccept>我接受</IAccept>
    <IDecline>我拒绝</IDecline>
    <LicenceFormDesc>以下软件包要求您在安装前接受其许可条款。</LicenceFormDesc>
    <LicenceFormDesc1>单击“我接受”即表示您同意上述软件包的许可条款。如果您不同意许可条款，请点击“我拒绝”。</LicenceFormDesc1>
    <LicenceFormTitle>接受许可证</LicenceFormTitle>
    <License>许可证:</License>
    <ProjectUrl>项目网址:</ProjectUrl>
    <ReportAbuse>报表滥用:</ReportAbuse>
    <RetrievingInformation>正在检索信息...</RetrievingInformation>
    <Tags>标签:</Tags>
    <Title>NuGet的数据适配器</Title>
    <ViewLicense>查看许可证</ViewLicense>
  </NuGet>
  <Panels>
    <Dictionary>字典</Dictionary>
    <Messages>消息</Messages>
    <Properties>属性</Properties>
    <ReportTree>报表树</ReportTree>
  </Panels>
  <Password>
    <gbPassword>文件加密</gbPassword>
    <lbPasswordLoad>输入密码打开文件</lbPasswordLoad>
    <lbPasswordSave>密码:</lbPasswordSave>
    <PasswordNotEntered>密码无法登陆</PasswordNotEntered>
    <StiLoadPasswordForm>文档加密</StiLoadPasswordForm>
    <StiSavePasswordForm>密码</StiSavePasswordForm>
  </Password>
  <Permissions>
    <AdminAPI>应用程序编程接口</AdminAPI>
    <AdminBackgroundTasks>后台任务</AdminBackgroundTasks>
    <AdminPermissions>权限</AdminPermissions>
    <AdminRecycleBin>回收站</AdminRecycleBin>
    <AdminShare>分享</AdminShare>
    <AdminTransfers>转移</AdminTransfers>
    <ItemCalendars>日历</ItemCalendars>
    <ItemCloudStorages>云储存</ItemCloudStorages>
    <ItemContactLists>联系人列表</ItemContactLists>
    <ItemDashboards>仪表盘</ItemDashboards>
    <ItemDataSources>数据源</ItemDataSources>
    <ItemFiles>文件</ItemFiles>
    <ItemFolders>文件夹</ItemFolders>
    <ItemForms>表单</ItemForms>
    <ItemReportSnapshots>报表快照</ItemReportSnapshots>
    <ItemReportTemplates>报表模板</ItemReportTemplates>
    <ItemSchedulers>调度程序</ItemSchedulers>
    <ReportDesignerBusinessObjects>业务对象</ReportDesignerBusinessObjects>
    <ReportDesignerDataColumns>数据列</ReportDesignerDataColumns>
    <ReportDesignerDataConnections>数据连接</ReportDesignerDataConnections>
    <ReportDesignerDataRelations>数据关系</ReportDesignerDataRelations>
    <ReportDesignerDataSources>数据源</ReportDesignerDataSources>
    <ReportDesignerDictionaryActions>字典操作</ReportDesignerDictionaryActions>
    <ReportDesignerRestrictions>限制</ReportDesignerRestrictions>
    <ReportDesignerVariables>变量</ReportDesignerVariables>
    <SystemBackupRestore>备份&amp; 恢复</SystemBackupRestore>
    <SystemEmailTemplates>邮件模板</SystemEmailTemplates>
    <SystemLicensing>许可</SystemLicensing>
    <SystemMonitoring>监控</SystemMonitoring>
    <SystemUpdate>更新</SystemUpdate>
    <SystemWorkspaces>工作区</SystemWorkspaces>
    <TextAdministration>管理</TextAdministration>
    <TextItems>项目</TextItems>
    <TextReportDesigner>报表设计器</TextReportDesigner>
    <TextSystem>系统</TextSystem>
    <TextUsers>用户</TextUsers>
    <UserHimself>自己</UserHimself>
    <UserRoles>角色</UserRoles>
    <Users>用户</Users>
    <UserWorkspace>工作区</UserWorkspace>
  </Permissions>
  <PlacementComponent>
    <MoveLeftFreeSpace>将控件移动到空白区域的左边，将控件高度增加到空白空间高度.</MoveLeftFreeSpace>
    <MoveRightFreeSpace>将控件移动到空白空间的右边，将控件高度增加到空白空间高度.</MoveRightFreeSpace>
  </PlacementComponent>
  <PromptForm>
    <CacheLoginData>缓存当前会话的登录数据</CacheLoginData>
  </PromptForm>
  <PropertyCategory>
    <AppearanceCategory>外观</AppearanceCategory>
    <AreaCategory>区域</AreaCategory>
    <ArgumentCategory>参数</ArgumentCategory>
    <AxisCategory>轴线</AxisCategory>
    <BarCodeAdditionalCategory>附加条形码</BarCodeAdditionalCategory>
    <BarCodeCategory>条形码</BarCodeCategory>
    <BehaviorCategory>行为</BehaviorCategory>
    <ButtonCategory>按钮</ButtonCategory>
    <CapNeedle>帽针</CapNeedle>
    <CardsCategory>卡片</CardsCategory>
    <CellCategory>单元格</CellCategory>
    <ChartAdditionalCategory>附加图表</ChartAdditionalCategory>
    <ChartCategory>图标</ChartCategory>
    <ChartMap>Map地图</ChartMap>
    <CheckCategory>复选</CheckCategory>
    <ColorsCategory>颜色</ColorsCategory>
    <ColumnsCategory>分栏</ColumnsCategory>
    <ComboBoxCategory>组合框</ComboBoxCategory>
    <CommonCategory>共同</CommonCategory>
    <ControlCategory>控件</ControlCategory>
    <ControlsEventsCategory>控件事件</ControlsEventsCategory>
    <CrossTabCategory>交叉 Tab</CrossTabCategory>
    <DashboardCategory>仪表盘</DashboardCategory>
    <DataCategory>数据</DataCategory>
    <DataCells>数据单元</DataCells>
    <DatePickerCategory>日期选择器</DatePickerCategory>
    <DescriptionCategory>描述</DescriptionCategory>
    <DesignCategory>设计</DesignCategory>
    <DisplayCategory>显示</DisplayCategory>
    <EngineCategory>工具</EngineCategory>
    <ExportCategory>导出</ExportCategory>
    <ExportEventsCategory>导出事件</ExportEventsCategory>
    <FooterTableCategory>页脚表</FooterTableCategory>
    <GaugeCategory>量规</GaugeCategory>
    <GlobalizationCategory>全球化</GlobalizationCategory>
    <GridLinesCategory>网格线</GridLinesCategory>
    <HeaderTableCategory>标题表</HeaderTableCategory>
    <HierarchicalCategory>分级</HierarchicalCategory>
    <ImageAdditionalCategory>附加图片</ImageAdditionalCategory>
    <ImageCategory>图片</ImageCategory>
    <IndicatorCategory>指示符</IndicatorCategory>
    <InterlacingCategory>交错</InterlacingCategory>
    <LabelsCategory>标签</LabelsCategory>
    <LegendCategory>图例</LegendCategory>
    <LinearScaleBarCategory>线性标尺栏</LinearScaleBarCategory>
    <LinearScaleCategory>线性标尺</LinearScaleCategory>
    <ListBoxCategory>列表框</ListBoxCategory>
    <MainCategory>Main</MainCategory>
    <MarkerCategory>标注</MarkerCategory>
    <MathFormulaCategory>数学公式</MathFormulaCategory>
    <MiscCategory>其他</MiscCategory>
    <MouseEventsCategory>鼠标事件</MouseEventsCategory>
    <NavigationCategory>导航</NavigationCategory>
    <NavigationEventsCategory>导航事件</NavigationEventsCategory>
    <Needle>指针</Needle>
    <NeedleCategory>指针</NeedleCategory>
    <OnlineMapCategory>在线地图</OnlineMapCategory>
    <OptionsCategory>选项</OptionsCategory>
    <PageAdditionalCategory>附加页面</PageAdditionalCategory>
    <PageCategory>页面</PageCategory>
    <PageColumnBreakCategory>分页</PageColumnBreakCategory>
    <ParametersCategory>参数</ParametersCategory>
    <PivotTableCategory>数据透视表</PivotTableCategory>
    <PositionCategory>位置</PositionCategory>
    <PrimitiveCategory>图元</PrimitiveCategory>
    <PrintEventsCategory>打印事件</PrintEventsCategory>
    <ProgressCategory>进程</ProgressCategory>
    <RadialScaleBarCategory>径向比例尺</RadialScaleBarCategory>
    <RegionMapCategory>区域地图</RegionMapCategory>
    <RenderEventsCategory>渲染事件</RenderEventsCategory>
    <SeriesCategory>系列</SeriesCategory>
    <SeriesLabelsCategory>系列标签</SeriesLabelsCategory>
    <ShapeCategory>形状</ShapeCategory>
    <Size>尺寸</Size>
    <SubReportCategory>子报表</SubReportCategory>
    <TableCategory>表</TableCategory>
    <TableOfContents>目录</TableOfContents>
    <TextAdditionalCategory>附加文本</TextAdditionalCategory>
    <TextCategory>文本</TextCategory>
    <TickLabelMajorCategory>勾选主标签</TickLabelMajorCategory>
    <TickLabelMinorCategory>勾选分标签</TickLabelMinorCategory>
    <TickMarkMajorCategory>勾选主标志</TickMarkMajorCategory>
    <TickMarkMinorCategory>勾选分标志</TickMarkMinorCategory>
    <TitleCategory>标题</TitleCategory>
    <TreeViewBoxCategory>树视图框</TreeViewBoxCategory>
    <TreeViewCategory>树视图</TreeViewCategory>
    <TrendLineCategory>趋势线</TrendLineCategory>
    <ValueCategory>值</ValueCategory>
    <ValueCloseCategory>收盘值</ValueCloseCategory>
    <ValueEndCategory>Value End</ValueEndCategory>
    <ValueEventsCategory>值事件</ValueEventsCategory>
    <ValueHighCategory>最高值</ValueHighCategory>
    <ValueLowCategory>最低值</ValueLowCategory>
    <ValueOpenCategory>开盘值</ValueOpenCategory>
    <ViewCategory>视图</ViewCategory>
    <WeightCategory>加权</WeightCategory>
    <WinControlCategory>Windows 控件</WinControlCategory>
    <ZipCodeCategory>邮码</ZipCodeCategory>
  </PropertyCategory>
  <PropertyColor>
    <AliceBlue>湖色</AliceBlue>
    <AntiqueWhite>古典白色</AntiqueWhite>
    <Aqua>浅绿色</Aqua>
    <Aquamarine>海蓝色</Aquamarine>
    <Azure>天蓝色</Azure>
    <Beige>米色</Beige>
    <Bisque>桔黄色</Bisque>
    <Black>黑色</Black>
    <BlanchedAlmond>白杏色</BlanchedAlmond>
    <Blue>蓝色</Blue>
    <BlueViolet>蓝紫色</BlueViolet>
    <Brown>褐色</Brown>
    <BurlyWood>实木色</BurlyWood>
    <CadetBlue>刺桧蓝色</CadetBlue>
    <Carmine>紫红色</Carmine>
    <Chartreuse>亮黄绿色</Chartreuse>
    <Chocolate>巧克力色</Chocolate>
    <Coral>珊瑚色</Coral>
    <CornflowerBlue>矢车菊色</CornflowerBlue>
    <Cornsilk>谷丝色</Cornsilk>
    <Crimson>深红色</Crimson>
    <Cyan>青色</Cyan>
    <DarkBlue>深蓝色</DarkBlue>
    <DarkCyan>深青色</DarkCyan>
    <DarkGoldenrod>深金杆色</DarkGoldenrod>
    <DarkGray>深灰色</DarkGray>
    <DarkGreen>深绿色</DarkGreen>
    <DarkKhaki>深黄褐色</DarkKhaki>
    <DarkMagenta>深洋红色</DarkMagenta>
    <DarkOliveGreen>深橄榄绿色</DarkOliveGreen>
    <DarkOrange>深橙色</DarkOrange>
    <DarkOrchid>深紫色</DarkOrchid>
    <DarkRed>深红色</DarkRed>
    <DarkSalmon>深肉色</DarkSalmon>
    <DarkSeaGreen>深海绿色</DarkSeaGreen>
    <DarkSlateBlue>深暗灰蓝色</DarkSlateBlue>
    <DarkSlateGray>深暗蓝灰色</DarkSlateGray>
    <DarkTurquoise>深青绿色</DarkTurquoise>
    <DarkViolet>深紫色</DarkViolet>
    <DeepPink>深粉色</DeepPink>
    <DeepSkyBlue>深天蓝色</DeepSkyBlue>
    <DimGray>暗灰色</DimGray>
    <DodgerBlue>遮板蓝色</DodgerBlue>
    <Firebrick>砖色</Firebrick>
    <FloralWhite>花白色</FloralWhite>
    <ForestGreen>葱绿色</ForestGreen>
    <Fuchsia>紫红色</Fuchsia>
    <Gainsboro>庚斯博罗灰色</Gainsboro>
    <GhostWhite>幽灵白色</GhostWhite>
    <Gold>金黄色</Gold>
    <Goldenrod>金杆黄色</Goldenrod>
    <Gray>灰色</Gray>
    <Green>绿色</Green>
    <GreenYellow>绿黄色</GreenYellow>
    <Honeydew>蜜汁色</Honeydew>
    <HotPink>亮粉色</HotPink>
    <IndianRed>印第安红色</IndianRed>
    <Indigo>靛青色</Indigo>
    <Ivory>象牙色</Ivory>
    <Khaki>黄褐色</Khaki>
    <Lavender>淡紫色</Lavender>
    <LavenderBlush>浅紫红色</LavenderBlush>
    <LawnGreen>草绿色</LawnGreen>
    <LemonChiffon>柠檬纱色</LemonChiffon>
    <LightBlue>浅蓝色</LightBlue>
    <LightCoral>浅珊瑚色</LightCoral>
    <LightCyan>浅青色</LightCyan>
    <LightGoldenrodYellow>浅金杆黄色</LightGoldenrodYellow>
    <LightGray>浅灰色</LightGray>
    <LightGreen>浅绿色</LightGreen>
    <LightPink>浅粉色</LightPink>
    <LightSalmon>浅肉色</LightSalmon>
    <LightSeaGreen>浅海绿色</LightSeaGreen>
    <LightSkyBlue>浅天蓝色</LightSkyBlue>
    <LightSlateGray>浅暗蓝灰色</LightSlateGray>
    <LightSteelBlue>浅钢青色</LightSteelBlue>
    <LightYellow>浅黄色</LightYellow>
    <Lime>酸橙色</Lime>
    <LimeGreen>橙绿色</LimeGreen>
    <Linen>亚麻色</Linen>
    <Magenta>洋红色</Magenta>
    <Maroon>栗色
</Maroon>
    <MediumAquamarine>间绿色</MediumAquamarine>
    <MediumBlue>间蓝色</MediumBlue>
    <MediumOrchid>间紫色</MediumOrchid>
    <MediumPurple>间紫红色
</MediumPurple>
    <MediumSeaGreen>间海绿色</MediumSeaGreen>
    <MediumSlateBlue>间暗蓝色</MediumSlateBlue>
    <MediumSpringGreen>间春绿色</MediumSpringGreen>
    <MediumTurquoise>间绿宝石色</MediumTurquoise>
    <MediumVioletRed>间紫罗兰色</MediumVioletRed>
    <MidnightBlue>中灰蓝色</MidnightBlue>
    <MintCream>薄荷色</MintCream>
    <MistyRose>浅玫瑰色</MistyRose>
    <Moccasin>鹿皮色</Moccasin>
    <NavajoWhite>纳瓦白</NavajoWhite>
    <Navy>藏青色</Navy>
    <OldLace>老花色</OldLace>
    <Olive>橄榄色</Olive>
    <OliveDrab>深绿褐色</OliveDrab>
    <Orange>橙色</Orange>
    <OrangeRed>橙红色</OrangeRed>
    <Orchid>淡紫色</Orchid>
    <PaleGoldenrod>苍麒麟色</PaleGoldenrod>
    <PaleGreen>苍绿色</PaleGreen>
    <PaleTurquoise>苍绿松色</PaleTurquoise>
    <PaleVioletRed>苍紫罗蓝色</PaleVioletRed>
    <PapayaWhip>番木色</PapayaWhip>
    <PeachPuff>桃色</PeachPuff>
    <Peru>秘鲁色</Peru>
    <Pink>粉红色</Pink>
    <Plum>杨李色</Plum>
    <PowderBlue>粉蓝色</PowderBlue>
    <Purple>紫红色</Purple>
    <Red>红色</Red>
    <RosyBrown>褐玫瑰红</RosyBrown>
    <RoyalBlue>宝蓝色</RoyalBlue>
    <SaddleBrown>重褐色</SaddleBrown>
    <Salmon>鲜肉色</Salmon>
    <SandyBrown>沙褐色</SandyBrown>
    <SeaGreen>海绿色</SeaGreen>
    <SeaShell>海贝色</SeaShell>
    <Sienna>赭色</Sienna>
    <Silver>银白色</Silver>
    <SkyBlue>天蓝色</SkyBlue>
    <SlateBlue>石蓝色</SlateBlue>
    <SlateGray>灰石色</SlateGray>
    <Snow>雪白色</Snow>
    <SpringGreen>春绿色</SpringGreen>
    <SteelBlue>钢蓝色</SteelBlue>
    <Tan>茶色</Tan>
    <Teal>水鸭色</Teal>
    <Thistle>蓟色</Thistle>
    <Tomato>番茄色</Tomato>
    <Transparent>透明色</Transparent>
    <Turquoise>翠蓝色</Turquoise>
    <VeryDarkGray>深灰色</VeryDarkGray>
    <Violet>紫色</Violet>
    <Wheat>浅黄色</Wheat>
    <White>白色</White>
    <WhiteSmoke>烟白色</WhiteSmoke>
    <Yellow>黄色</Yellow>
    <YellowGreen>黄绿色</YellowGreen>
  </PropertyColor>
  <PropertyEnum>
    <boolFalse>假</boolFalse>
    <boolTrue>真</boolTrue>
    <BorderStyleFixed3D>Fixed 3D</BorderStyleFixed3D>
    <BorderStyleFixedSingle>Fixed Single</BorderStyleFixedSingle>
    <BorderStyleNone>无</BorderStyleNone>
    <ChartAxesTicksAll>全部</ChartAxesTicksAll>
    <ChartAxesTicksMajor>主要</ChartAxesTicksMajor>
    <ChartAxesTicksNone>无</ChartAxesTicksNone>
    <ChartGridLinesAll>全部</ChartGridLinesAll>
    <ChartGridLinesMajor>主要</ChartGridLinesMajor>
    <ChartGridLinesNone>无</ChartGridLinesNone>
    <ComboBoxStyleDropDown>下拉</ComboBoxStyleDropDown>
    <ComboBoxStyleDropDownList>下拉列表</ComboBoxStyleDropDownList>
    <ComboBoxStyleSimple>简单</ComboBoxStyleSimple>
    <ContentAlignmentBottomCenter>底端居中</ContentAlignmentBottomCenter>
    <ContentAlignmentBottomLeft>底端居左</ContentAlignmentBottomLeft>
    <ContentAlignmentBottomRight>底端居右</ContentAlignmentBottomRight>
    <ContentAlignmentMiddleCenter>中间居中</ContentAlignmentMiddleCenter>
    <ContentAlignmentMiddleLeft>中间居左</ContentAlignmentMiddleLeft>
    <ContentAlignmentMiddleRight>中间居右</ContentAlignmentMiddleRight>
    <ContentAlignmentTopCenter>顶端居中</ContentAlignmentTopCenter>
    <ContentAlignmentTopLeft>顶端居左</ContentAlignmentTopLeft>
    <ContentAlignmentTopRight>顶端居右</ContentAlignmentTopRight>
    <DataGridLineStyleNone>无</DataGridLineStyleNone>
    <DataGridLineStyleSolid>实线</DataGridLineStyleSolid>
    <DateTimePickerFormatCustom>自定义</DateTimePickerFormatCustom>
    <DateTimePickerFormatLong>长</DateTimePickerFormatLong>
    <DateTimePickerFormatShort>短</DateTimePickerFormatShort>
    <DateTimePickerFormatTime>时间</DateTimePickerFormatTime>
    <DialogResultAbort>中断</DialogResultAbort>
    <DialogResultCancel>取消</DialogResultCancel>
    <DialogResultIgnore>忽略</DialogResultIgnore>
    <DialogResultNo>否</DialogResultNo>
    <DialogResultNone>无</DialogResultNone>
    <DialogResultOK>是</DialogResultOK>
    <DialogResultRetry>重试</DialogResultRetry>
    <DialogResultYes>是</DialogResultYes>
    <DockStyleBottom>按钮</DockStyleBottom>
    <DockStyleFill>填充</DockStyleFill>
    <DockStyleLeft>左</DockStyleLeft>
    <DockStyleNone>无</DockStyleNone>
    <DockStyleRight>右</DockStyleRight>
    <DockStyleTop>上</DockStyleTop>
    <DuplexDefault>默认</DuplexDefault>
    <DuplexHorizontal>水平</DuplexHorizontal>
    <DuplexSimplex>单一</DuplexSimplex>
    <DuplexVertical>垂直</DuplexVertical>
    <FormStartPositionCenterParent>父窗体居中</FormStartPositionCenterParent>
    <FormStartPositionCenterScreen>屏幕居中</FormStartPositionCenterScreen>
    <FormStartPositionManual>手动</FormStartPositionManual>
    <FormStartPositionWindowsDefaultBounds>窗口默认范围</FormStartPositionWindowsDefaultBounds>
    <FormStartPositionWindowsDefaultLocation>窗口默认位置</FormStartPositionWindowsDefaultLocation>
    <FormWindowStateMaximized>最大化</FormWindowStateMaximized>
    <FormWindowStateMinimized>最小化</FormWindowStateMinimized>
    <FormWindowStateNormal>正常</FormWindowStateNormal>
    <HorizontalAlignmentCenter>居中</HorizontalAlignmentCenter>
    <HorizontalAlignmentLeft>居左</HorizontalAlignmentLeft>
    <HorizontalAlignmentRight>居右</HorizontalAlignmentRight>
    <HotkeyPrefixHide>隐藏</HotkeyPrefixHide>
    <HotkeyPrefixNone>无</HotkeyPrefixNone>
    <HotkeyPrefixShow>显示</HotkeyPrefixShow>
    <LeftRightAlignmentLeft>居左</LeftRightAlignmentLeft>
    <LeftRightAlignmentRight>居右</LeftRightAlignmentRight>
    <PictureBoxSizeModeAutoSize>自动调整</PictureBoxSizeModeAutoSize>
    <PictureBoxSizeModeCenterImage>图片中间</PictureBoxSizeModeCenterImage>
    <PictureBoxSizeModeNormal>正常</PictureBoxSizeModeNormal>
    <PictureBoxSizeModeStretchImage>适应图片</PictureBoxSizeModeStretchImage>
    <RelationDirectionChildToParent>子父</RelationDirectionChildToParent>
    <RelationDirectionParentToChild>父子</RelationDirectionParentToChild>
    <RightToLeftInherit>继承</RightToLeftInherit>
    <RightToLeftNo>否</RightToLeftNo>
    <RightToLeftYes>是</RightToLeftYes>
    <SelectionModeMultiExtended>扩展复选</SelectionModeMultiExtended>
    <SelectionModeMultiSimple>简单复选</SelectionModeMultiSimple>
    <SelectionModeNone>无</SelectionModeNone>
    <SelectionModeOne>单选</SelectionModeOne>
    <StiAnchorModeBottom>底部</StiAnchorModeBottom>
    <StiAnchorModeLeft>左边</StiAnchorModeLeft>
    <StiAnchorModeRight>右边</StiAnchorModeRight>
    <StiAnchorModeTop>顶端</StiAnchorModeTop>
    <StiAngleAngle0>0度</StiAngleAngle0>
    <StiAngleAngle180>180度</StiAngleAngle180>
    <StiAngleAngle270>270度</StiAngleAngle270>
    <StiAngleAngle45>45度</StiAngleAngle45>
    <StiAngleAngle90>90度</StiAngleAngle90>
    <StiArrowStyleArc>弧</StiArrowStyleArc>
    <StiArrowStyleArcAndCircle>圆弧</StiArrowStyleArcAndCircle>
    <StiArrowStyleCircle>圆</StiArrowStyleCircle>
    <StiArrowStyleLines>线</StiArrowStyleLines>
    <StiArrowStyleNone>无</StiArrowStyleNone>
    <StiArrowStyleTriangle>三角形</StiArrowStyleTriangle>
    <StiBorderSidesAll>全部</StiBorderSidesAll>
    <StiBorderSidesBottom>底部</StiBorderSidesBottom>
    <StiBorderSidesLeft>左侧</StiBorderSidesLeft>
    <StiBorderSidesNone>无</StiBorderSidesNone>
    <StiBorderSidesRight>右侧</StiBorderSidesRight>
    <StiBorderSidesTop>上部</StiBorderSidesTop>
    <StiBorderStyleBump>隆起</StiBorderStyleBump>
    <StiBorderStyleEtched>蚀化</StiBorderStyleEtched>
    <StiBorderStyleFlat>平直</StiBorderStyleFlat>
    <StiBorderStyleNone>无</StiBorderStyleNone>
    <StiBorderStyleRaised>凸起</StiBorderStyleRaised>
    <StiBorderStyleRaisedInner>内部凸起</StiBorderStyleRaisedInner>
    <StiBorderStyleRaisedOuter>外部凸起</StiBorderStyleRaisedOuter>
    <StiBorderStyleSunken>凹陷</StiBorderStyleSunken>
    <StiBorderStyleSunkenInner>内部凹陷</StiBorderStyleSunkenInner>
    <StiBorderStyleSunkenOuter>外部凹陷</StiBorderStyleSunkenOuter>
    <StiBrushTypeGlare>眩光刷</StiBrushTypeGlare>
    <StiBrushTypeGradient0>渐变刷, 角度 0</StiBrushTypeGradient0>
    <StiBrushTypeGradient180>渐变刷, 180度</StiBrushTypeGradient180>
    <StiBrushTypeGradient270>渐变刷, 270度</StiBrushTypeGradient270>
    <StiBrushTypeGradient45>渐变刷, 45度</StiBrushTypeGradient45>
    <StiBrushTypeGradient90>渐变刷, 90度</StiBrushTypeGradient90>
    <StiBrushTypeSolid>实心刷</StiBrushTypeSolid>
    <StiButtonSendTypeFDF>FDF</StiButtonSendTypeFDF>
    <StiButtonSendTypeHTML>HTML</StiButtonSendTypeHTML>
    <StiButtonSendTypePDF>PDF</StiButtonSendTypePDF>
    <StiButtonSendTypeXFDF>XFDF</StiButtonSendTypeXFDF>
    <StiCalculationModeCompilation>编译</StiCalculationModeCompilation>
    <StiCalculationModeInterpretation>说明</StiCalculationModeInterpretation>
    <StiCapStyleArrow>箭头</StiCapStyleArrow>
    <StiCapStyleDiamond>菱形</StiCapStyleDiamond>
    <StiCapStyleNone>无</StiCapStyleNone>
    <StiCapStyleOpen>打开</StiCapStyleOpen>
    <StiCapStyleOval>椭圆</StiCapStyleOval>
    <StiCapStyleSquare>正方形</StiCapStyleSquare>
    <StiCapStyleStealth>隐形</StiCapStyleStealth>
    <StiChartLabelsStyleCategory>类别</StiChartLabelsStyleCategory>
    <StiChartLabelsStyleCategoryPercentOfTotal>类别 - 总额百分比</StiChartLabelsStyleCategoryPercentOfTotal>
    <StiChartLabelsStyleCategoryValue>类别 - 值</StiChartLabelsStyleCategoryValue>
    <StiChartLabelsStylePercentOfTotal>总额百分比</StiChartLabelsStylePercentOfTotal>
    <StiChartLabelsStyleValue>值</StiChartLabelsStyleValue>
    <StiChartTitleDockBottom>底部</StiChartTitleDockBottom>
    <StiChartTitleDockLeft>左端</StiChartTitleDockLeft>
    <StiChartTitleDockRight>右端</StiChartTitleDockRight>
    <StiChartTitleDockTop>顶部</StiChartTitleDockTop>
    <StiChartTrendLineTypeExponential>指数</StiChartTrendLineTypeExponential>
    <StiChartTrendLineTypeLinear>线型</StiChartTrendLineTypeLinear>
    <StiChartTrendLineTypeLogarithmic>对数</StiChartTrendLineTypeLogarithmic>
    <StiChartTrendLineTypeNone>无</StiChartTrendLineTypeNone>
    <StiCheckStateChecked>选中</StiCheckStateChecked>
    <StiCheckStateIndeterminate>模糊</StiCheckStateIndeterminate>
    <StiCheckStateUnchecked>取消</StiCheckStateUnchecked>
    <StiCheckStyleCheck>复选</StiCheckStyleCheck>
    <StiCheckStyleCheckRectangle>Check Rectangle</StiCheckStyleCheckRectangle>
    <StiCheckStyleCross>交叉</StiCheckStyleCross>
    <StiCheckStyleCrossCircle>Cross Circle</StiCheckStyleCrossCircle>
    <StiCheckStyleCrossRectangle>Cross Rectangle</StiCheckStyleCrossRectangle>
    <StiCheckStyleDotCircle>Dot Circle</StiCheckStyleDotCircle>
    <StiCheckStyleDotRectangle>Dot Rectangle</StiCheckStyleDotRectangle>
    <StiCheckStyleNone>None</StiCheckStyleNone>
    <StiCheckStyleNoneCircle>None Circle</StiCheckStyleNoneCircle>
    <StiCheckStyleNoneRectangle>None Rectangle</StiCheckStyleNoneRectangle>
    <StiCheckSumNo>否</StiCheckSumNo>
    <StiCheckSumYes>是</StiCheckSumYes>
    <StiCode11CheckSumAuto>自动</StiCode11CheckSumAuto>
    <StiCode11CheckSumNone>无</StiCode11CheckSumNone>
    <StiCode11CheckSumOneDigit>一位数字</StiCode11CheckSumOneDigit>
    <StiCode11CheckSumTwoDigits>两位数字</StiCode11CheckSumTwoDigits>
    <StiColorScaleTypeColor2>2-颜色刻度</StiColorScaleTypeColor2>
    <StiColorScaleTypeColor3>3-颜色刻度</StiColorScaleTypeColor3>
    <StiColumnDirectionAcrossThenDown>交叉再向下</StiColumnDirectionAcrossThenDown>
    <StiColumnDirectionDownThenAcross>向下再交叉</StiColumnDirectionDownThenAcross>
    <StiColumnShape3DBox>Box</StiColumnShape3DBox>
    <StiColumnShape3DPartialPyramid>Partial Pyramid</StiColumnShape3DPartialPyramid>
    <StiColumnShape3DPyramid>Pyramid</StiColumnShape3DPyramid>
    <StiCompilationAccessAllow>允许</StiCompilationAccessAllow>
    <StiCompilationAccessAsk>请求;</StiCompilationAccessAsk>
    <StiCompilationAccessDeny>拒绝</StiCompilationAccessDeny>
    <StiCompilationAccessForceInterpretation>Force Interpretation</StiCompilationAccessForceInterpretation>
    <StiCrossHorAlignmentCenter>居中</StiCrossHorAlignmentCenter>
    <StiCrossHorAlignmentLeft>居左</StiCrossHorAlignmentLeft>
    <StiCrossHorAlignmentNone>无</StiCrossHorAlignmentNone>
    <StiCrossHorAlignmentRight>居右</StiCrossHorAlignmentRight>
    <StiDashboardContentAlignmentCenter>中间</StiDashboardContentAlignmentCenter>
    <StiDashboardContentAlignmentLeft>左边</StiDashboardContentAlignmentLeft>
    <StiDashboardContentAlignmentRight>右边</StiDashboardContentAlignmentRight>
    <StiDashboardContentAlignmentStretchX>拉伸X</StiDashboardContentAlignmentStretchX>
    <StiDashboardContentAlignmentStretchXY>拉伸XY</StiDashboardContentAlignmentStretchXY>
    <StiDateSelectionModeAutoRange>自动范围</StiDateSelectionModeAutoRange>
    <StiDateSelectionModeRange>范围</StiDateSelectionModeRange>
    <StiDateSelectionModeSingle>单独</StiDateSelectionModeSingle>
    <StiDateTimeTypeDate>日期</StiDateTimeTypeDate>
    <StiDateTimeTypeDateAndTime>时间和日期</StiDateTimeTypeDateAndTime>
    <StiDateTimeTypeTime>时间</StiDateTimeTypeTime>
    <StiDesignerScaleModeAutomaticScaling>自动缩放</StiDesignerScaleModeAutomaticScaling>
    <StiDesignerScaleModeScaling100>100%缩放</StiDesignerScaleModeScaling100>
    <StiDesignerSpecificationAuto>自动决定</StiDesignerSpecificationAuto>
    <StiDesignerSpecificationBeginner>我是初学者</StiDesignerSpecificationBeginner>
    <StiDesignerSpecificationBICreator>我是BI创建者</StiDesignerSpecificationBICreator>
    <StiDesignerSpecificationDeveloper>我是开发者</StiDesignerSpecificationDeveloper>
    <StiDirectionBottomToTop>下到上</StiDirectionBottomToTop>
    <StiDirectionLeftToRight>左到右</StiDirectionLeftToRight>
    <StiDirectionRightToLeft>右到左</StiDirectionRightToLeft>
    <StiDirectionTopToBottom>上到下</StiDirectionTopToBottom>
    <StiDisplayNameTypeFull>完整</StiDisplayNameTypeFull>
    <StiDisplayNameTypeNone>无</StiDisplayNameTypeNone>
    <StiDisplayNameTypeShort>简短</StiDisplayNameTypeShort>
    <StiDockStyleBottom>底部</StiDockStyleBottom>
    <StiDockStyleFill>填充</StiDockStyleFill>
    <StiDockStyleLeft>左</StiDockStyleLeft>
    <StiDockStyleNone>无</StiDockStyleNone>
    <StiDockStyleRight>右</StiDockStyleRight>
    <StiDockStyleTop>顶部</StiDockStyleTop>
    <StiDrillDownModeMultiPage>多页</StiDrillDownModeMultiPage>
    <StiDrillDownModeSinglePage>单页</StiDrillDownModeSinglePage>
    <StiEanSupplementTypeFiveDigit>五位</StiEanSupplementTypeFiveDigit>
    <StiEanSupplementTypeNone>无</StiEanSupplementTypeNone>
    <StiEanSupplementTypeTwoDigit>两位</StiEanSupplementTypeTwoDigit>
    <StiEmptyCellsAsConnectPointsWithLine>连接点线</StiEmptyCellsAsConnectPointsWithLine>
    <StiEmptyCellsAsGap>差距</StiEmptyCellsAsGap>
    <StiEmptyCellsAsZero>零</StiEmptyCellsAsZero>
    <StiEmptySizeModeAlignFooterToBottom>页脚底端对齐</StiEmptySizeModeAlignFooterToBottom>
    <StiEmptySizeModeAlignFooterToTop>页脚顶端对齐</StiEmptySizeModeAlignFooterToTop>
    <StiEmptySizeModeDecreaseLastRow>减少末行</StiEmptySizeModeDecreaseLastRow>
    <StiEmptySizeModeIncreaseLastRow>增加末行</StiEmptySizeModeIncreaseLastRow>
    <StiEnumeratorTypeABC>英文字母</StiEnumeratorTypeABC>
    <StiEnumeratorTypeArabic>阿拉伯数字</StiEnumeratorTypeArabic>
    <StiEnumeratorTypeNone>无</StiEnumeratorTypeNone>
    <StiEnumeratorTypeRoman>罗马数字</StiEnumeratorTypeRoman>
    <StiExtendedStyleBoolFalse>错误</StiExtendedStyleBoolFalse>
    <StiExtendedStyleBoolFromStyle>选择风格</StiExtendedStyleBoolFromStyle>
    <StiExtendedStyleBoolTrue>正确</StiExtendedStyleBoolTrue>
    <StiFilterConditionBeginningWith>开始于</StiFilterConditionBeginningWith>
    <StiFilterConditionBetween>在两者之间</StiFilterConditionBetween>
    <StiFilterConditionContaining>包含</StiFilterConditionContaining>
    <StiFilterConditionEndingWith>结束于</StiFilterConditionEndingWith>
    <StiFilterConditionEqualTo>等于</StiFilterConditionEqualTo>
    <StiFilterConditionEqualToVariableOrVariableIsNull>等于变量或变量为null</StiFilterConditionEqualToVariableOrVariableIsNull>
    <StiFilterConditionGreaterThan>大于</StiFilterConditionGreaterThan>
    <StiFilterConditionGreaterThanOrEqualTo>大于或等于</StiFilterConditionGreaterThanOrEqualTo>
    <StiFilterConditionIsBlank>为空白</StiFilterConditionIsBlank>
    <StiFilterConditionIsNotBlank>不为空白</StiFilterConditionIsNotBlank>
    <StiFilterConditionIsNotNull>不为空</StiFilterConditionIsNotNull>
    <StiFilterConditionIsNull>空</StiFilterConditionIsNull>
    <StiFilterConditionLessThan>小于</StiFilterConditionLessThan>
    <StiFilterConditionLessThanOrEqualTo>小于或等于</StiFilterConditionLessThanOrEqualTo>
    <StiFilterConditionNotBetween>不在之间</StiFilterConditionNotBetween>
    <StiFilterConditionNotContaining>未包含</StiFilterConditionNotContaining>
    <StiFilterConditionNotEqualTo>不等于</StiFilterConditionNotEqualTo>
    <StiFilterDataTypeBoolean>布尔型</StiFilterDataTypeBoolean>
    <StiFilterDataTypeDateTime>日期时间</StiFilterDataTypeDateTime>
    <StiFilterDataTypeExpression>表达式</StiFilterDataTypeExpression>
    <StiFilterDataTypeNumeric>数值型</StiFilterDataTypeNumeric>
    <StiFilterDataTypeString>字符型</StiFilterDataTypeString>
    <StiFilterEngineReportEngine>报表引擎</StiFilterEngineReportEngine>
    <StiFilterEngineSQLQuery>SQL查询</StiFilterEngineSQLQuery>
    <StiFilterItemArgument>参数</StiFilterItemArgument>
    <StiFilterItemExpression>表达式</StiFilterItemExpression>
    <StiFilterItemValue>值</StiFilterItemValue>
    <StiFilterItemValueClose>收盘值</StiFilterItemValueClose>
    <StiFilterItemValueEnd>交易值</StiFilterItemValueEnd>
    <StiFilterItemValueHigh>最高值</StiFilterItemValueHigh>
    <StiFilterItemValueLow>最低值</StiFilterItemValueLow>
    <StiFilterItemValueOpen>开盘值</StiFilterItemValueOpen>
    <StiFilterModeAnd>与</StiFilterModeAnd>
    <StiFilterModeOr>或</StiFilterModeOr>
    <StiFontIconGroupAccessibilityIcons>可及性</StiFontIconGroupAccessibilityIcons>
    <StiFontIconGroupBrandIcons>品牌</StiFontIconGroupBrandIcons>
    <StiFontIconGroupDirectionalIcons>方向性</StiFontIconGroupDirectionalIcons>
    <StiFontIconGroupGenderIcons>性别</StiFontIconGroupGenderIcons>
    <StiFontIconGroupMedicalIcons>医学</StiFontIconGroupMedicalIcons>
    <StiFontIconGroupPaymentIcons>支付</StiFontIconGroupPaymentIcons>
    <StiFontIconGroupSpinnerIcons>微调</StiFontIconGroupSpinnerIcons>
    <StiFontIconGroupTransportationIcons>运输</StiFontIconGroupTransportationIcons>
    <StiFontIconGroupVideoPlayerIcons>视频</StiFontIconGroupVideoPlayerIcons>
    <StiFontIconGroupWebApplicationIcons>应用</StiFontIconGroupWebApplicationIcons>
    <StiFontSizeModeAuto>自动</StiFontSizeModeAuto>
    <StiFontSizeModeTarget>目标</StiFontSizeModeTarget>
    <StiFontSizeModeValue>值</StiFontSizeModeValue>
    <StiFormStartModeOnEnd>在结束</StiFormStartModeOnEnd>
    <StiFormStartModeOnPreview>在预览</StiFormStartModeOnPreview>
    <StiFormStartModeOnStart>在开始</StiFormStartModeOnStart>
    <StiGaugeCalculationModeAuto>自动</StiGaugeCalculationModeAuto>
    <StiGaugeCalculationModeCustom>自定义</StiGaugeCalculationModeCustom>
    <StiGaugeRangeModePercentage>百分比</StiGaugeRangeModePercentage>
    <StiGaugeRangeModeValue>值</StiGaugeRangeModeValue>
    <StiGaugeRangeTypeColor>颜色</StiGaugeRangeTypeColor>
    <StiGaugeRangeTypeNone>无</StiGaugeRangeTypeNone>
    <StiGaugeTypeBullet>子弹图</StiGaugeTypeBullet>
    <StiGaugeTypeFullCircular>整圆</StiGaugeTypeFullCircular>
    <StiGaugeTypeHalfCircular>半圆</StiGaugeTypeHalfCircular>
    <StiGaugeTypeHorizontalLinear>水平线</StiGaugeTypeHorizontalLinear>
    <StiGaugeTypeLinear>线性</StiGaugeTypeLinear>
    <StiGroupSortDirectionAscending>升序</StiGroupSortDirectionAscending>
    <StiGroupSortDirectionDescending>降序</StiGroupSortDirectionDescending>
    <StiGroupSortDirectionNone>无</StiGroupSortDirectionNone>
    <StiHorAlignmentCenter>居中</StiHorAlignmentCenter>
    <StiHorAlignmentLeft>居左</StiHorAlignmentLeft>
    <StiHorAlignmentRight>居右</StiHorAlignmentRight>
    <StiIconAlignmentBottom>底部</StiIconAlignmentBottom>
    <StiIconAlignmentLeft>左边</StiIconAlignmentLeft>
    <StiIconAlignmentNone>无</StiIconAlignmentNone>
    <StiIconAlignmentRight>右边</StiIconAlignmentRight>
    <StiIconAlignmentTop>顶端</StiIconAlignmentTop>
    <StiImageProcessingDuplicatesTypeGlobalHide>全部隐藏</StiImageProcessingDuplicatesTypeGlobalHide>
    <StiImageProcessingDuplicatesTypeGlobalMerge>全部合并</StiImageProcessingDuplicatesTypeGlobalMerge>
    <StiImageProcessingDuplicatesTypeGlobalRemoveImage>全部移动图像</StiImageProcessingDuplicatesTypeGlobalRemoveImage>
    <StiImageProcessingDuplicatesTypeHide>隐藏</StiImageProcessingDuplicatesTypeHide>
    <StiImageProcessingDuplicatesTypeMerge>合并</StiImageProcessingDuplicatesTypeMerge>
    <StiImageProcessingDuplicatesTypeNone>无</StiImageProcessingDuplicatesTypeNone>
    <StiImageProcessingDuplicatesTypeRemoveImage>移动图像</StiImageProcessingDuplicatesTypeRemoveImage>
    <StiImageRotationFlipHorizontal>水平翻转</StiImageRotationFlipHorizontal>
    <StiImageRotationFlipVertical>垂直翻转</StiImageRotationFlipVertical>
    <StiImageRotationNone>无</StiImageRotationNone>
    <StiImageRotationRotate180>旋转 180°</StiImageRotationRotate180>
    <StiImageRotationRotate90CCW>旋转 90° CCW</StiImageRotationRotate90CCW>
    <StiImageRotationRotate90CW>旋转 90° CW</StiImageRotationRotate90CW>
    <StiInteractionOnClick>无</StiInteractionOnClick>
    <StiInteractionOnClickApplyFilter>应用过滤器</StiInteractionOnClickApplyFilter>
    <StiInteractionOnClickDrillDown>下行</StiInteractionOnClickDrillDown>
    <StiInteractionOnClickOpenHyperlink>打开超链接</StiInteractionOnClickOpenHyperlink>
    <StiInteractionOnClickShowDashboard>显示仪表盘</StiInteractionOnClickShowDashboard>
    <StiInteractionOnHoverNone>无</StiInteractionOnHoverNone>
    <StiInteractionOnHoverShowHyperlink>显示超链接</StiInteractionOnHoverShowHyperlink>
    <StiInteractionOnHoverShowToolTip>显示工具提示</StiInteractionOnHoverShowToolTip>
    <StiInteractionOpenHyperlinkDestinationCurrentTab>当前标签</StiInteractionOpenHyperlinkDestinationCurrentTab>
    <StiInteractionOpenHyperlinkDestinationNewTab>新标签</StiInteractionOpenHyperlinkDestinationNewTab>
    <StiItemOrientationHorizontal>横向</StiItemOrientationHorizontal>
    <StiItemOrientationVertical>纵向</StiItemOrientationVertical>
    <StiItemSelectionModeMulti>多种</StiItemSelectionModeMulti>
    <StiItemSelectionModeOne>一种</StiItemSelectionModeOne>
    <StiKeepDetailsKeepDetailsTogether>明细始终一起</StiKeepDetailsKeepDetailsTogether>
    <StiKeepDetailsKeepFirstDetailTogether>第一条明细始终一起</StiKeepDetailsKeepFirstDetailTogether>
    <StiKeepDetailsKeepFirstRowTogether>第一行始终一起</StiKeepDetailsKeepFirstRowTogether>
    <StiKeepDetailsNone>无</StiKeepDetailsNone>
    <StiLabelPlacementInside>内</StiLabelPlacementInside>
    <StiLabelPlacementOutside>外</StiLabelPlacementOutside>
    <StiLabelsPlacementAutoRotation>自动旋转</StiLabelsPlacementAutoRotation>
    <StiLabelsPlacementNone>无</StiLabelsPlacementNone>
    <StiLabelsPlacementOneLine>单线</StiLabelsPlacementOneLine>
    <StiLabelsPlacementTwoLines>双线</StiLabelsPlacementTwoLines>
    <StiLegendDirectionBottomToTop>从底部到顶端</StiLegendDirectionBottomToTop>
    <StiLegendDirectionLeftToRight>由左到右</StiLegendDirectionLeftToRight>
    <StiLegendDirectionRightToLeft>由右到左</StiLegendDirectionRightToLeft>
    <StiLegendDirectionTopToBottom>从上至下</StiLegendDirectionTopToBottom>
    <StiLegendHorAlignmentCenter>居中</StiLegendHorAlignmentCenter>
    <StiLegendHorAlignmentLeft>居左</StiLegendHorAlignmentLeft>
    <StiLegendHorAlignmentLeftOutside>左外侧</StiLegendHorAlignmentLeftOutside>
    <StiLegendHorAlignmentRight>居右</StiLegendHorAlignmentRight>
    <StiLegendHorAlignmentRightOutside>右外侧</StiLegendHorAlignmentRightOutside>
    <StiLegendVertAlignmentBottom>底端</StiLegendVertAlignmentBottom>
    <StiLegendVertAlignmentBottomOutside>底端外侧</StiLegendVertAlignmentBottomOutside>
    <StiLegendVertAlignmentCenter>居中</StiLegendVertAlignmentCenter>
    <StiLegendVertAlignmentTop>居顶</StiLegendVertAlignmentTop>
    <StiLegendVertAlignmentTopOutside>顶端外侧</StiLegendVertAlignmentTopOutside>
    <StiLineWeightNormal>正常</StiLineWeightNormal>
    <StiLineWeightThick>厚</StiLineWeightThick>
    <StiLineWeightThin>薄</StiLineWeightThin>
    <StiMapModeChoropleth>专题地图</StiMapModeChoropleth>
    <StiMapModeOnline>在线</StiMapModeOnline>
    <StiMapTypeGroup>组</StiMapTypeGroup>
    <StiMapTypeHeatmap>热图</StiMapTypeHeatmap>
    <StiMapTypeHeatmapWithGroup>群热图</StiMapTypeHeatmapWithGroup>
    <StiMapTypeIndividual>个体</StiMapTypeIndividual>
    <StiMapTypeNone>无</StiMapTypeNone>
    <StiMapTypePoints>点</StiMapTypePoints>
    <StiMarkerAlignmentCenter>居中</StiMarkerAlignmentCenter>
    <StiMarkerAlignmentLeft>居左</StiMarkerAlignmentLeft>
    <StiMarkerAlignmentRight>居右</StiMarkerAlignmentRight>
    <StiMarkerTypeCircle>圆</StiMarkerTypeCircle>
    <StiMarkerTypeHalfCircle>半圆</StiMarkerTypeHalfCircle>
    <StiMarkerTypeHexagon>六边形</StiMarkerTypeHexagon>
    <StiMarkerTypeRectangle>矩形</StiMarkerTypeRectangle>
    <StiMarkerTypeStar5>五角星</StiMarkerTypeStar5>
    <StiMarkerTypeStar6>六角星</StiMarkerTypeStar6>
    <StiMarkerTypeStar7>七角星</StiMarkerTypeStar7>
    <StiMarkerTypeStar8>Star 8</StiMarkerTypeStar8>
    <StiMarkerTypeTriangle>三角形</StiMarkerTypeTriangle>
    <StiNestedFactorHigh>高</StiNestedFactorHigh>
    <StiNestedFactorLow>低</StiNestedFactorLow>
    <StiNestedFactorNormal>常规</StiNestedFactorNormal>
    <StiNumberOfPassDoublePass>双通道</StiNumberOfPassDoublePass>
    <StiNumberOfPassSinglePass>单通道</StiNumberOfPassSinglePass>
    <StiOnlineMapHeatmapColorGradientTypeAddRange>添加范围</StiOnlineMapHeatmapColorGradientTypeAddRange>
    <StiOnlineMapHeatmapColorGradientTypeBlackAquaWhite>黑白</StiOnlineMapHeatmapColorGradientTypeBlackAquaWhite>
    <StiOnlineMapHeatmapColorGradientTypeBlueRed>蓝红</StiOnlineMapHeatmapColorGradientTypeBlueRed>
    <StiOnlineMapHeatmapColorGradientTypeColorSpectrum>色谱</StiOnlineMapHeatmapColorGradientTypeColorSpectrum>
    <StiOnlineMapHeatmapColorGradientTypeDeepSea>深蓝</StiOnlineMapHeatmapColorGradientTypeDeepSea>
    <StiOnlineMapHeatmapColorGradientTypeHeatedMetal>金属</StiOnlineMapHeatmapColorGradientTypeHeatedMetal>
    <StiOnlineMapHeatmapColorGradientTypeIncandescent>白炽</StiOnlineMapHeatmapColorGradientTypeIncandescent>
    <StiOnlineMapHeatmapColorGradientTypeSteppedColors>层进颜色</StiOnlineMapHeatmapColorGradientTypeSteppedColors>
    <StiOnlineMapHeatmapColorGradientTypeSunrise>日出</StiOnlineMapHeatmapColorGradientTypeSunrise>
    <StiOnlineMapHeatmapColorGradientTypeVisibleSpectrum>可见光谱</StiOnlineMapHeatmapColorGradientTypeVisibleSpectrum>
    <StiOnlineMapLocationTypeAdminDivision1>管理分配1</StiOnlineMapLocationTypeAdminDivision1>
    <StiOnlineMapLocationTypeAdminDivision2>管理分配2</StiOnlineMapLocationTypeAdminDivision2>
    <StiOnlineMapLocationTypeAuto>自动</StiOnlineMapLocationTypeAuto>
    <StiOnlineMapLocationTypeCountryRegion>国家范围</StiOnlineMapLocationTypeCountryRegion>
    <StiOnlineMapLocationTypeNeighborhood>邻居</StiOnlineMapLocationTypeNeighborhood>
    <StiOnlineMapLocationTypePopulatedPlace>居住地</StiOnlineMapLocationTypePopulatedPlace>
    <StiOnlineMapLocationTypePostcode1>邮编1</StiOnlineMapLocationTypePostcode1>
    <StiOnlineMapLocationTypePostcode2>邮编2</StiOnlineMapLocationTypePostcode2>
    <StiOnlineMapLocationTypePostcode3>邮编3</StiOnlineMapLocationTypePostcode3>
    <StiOnlineMapLocationTypePostcode4>邮编4</StiOnlineMapLocationTypePostcode4>
    <StiOrientationHorizontal>水平</StiOrientationHorizontal>
    <StiOrientationHorizontalRight>水平靠右</StiOrientationHorizontalRight>
    <StiOrientationVertical>垂直</StiOrientationVertical>
    <StiPageOrientationLandscape>横向</StiPageOrientationLandscape>
    <StiPageOrientationPortrait>纵向</StiPageOrientationPortrait>
    <StiPdfDigitalSignatureAppearanceTypeDraw>画</StiPdfDigitalSignatureAppearanceTypeDraw>
    <StiPdfDigitalSignatureAppearanceTypeImage>图片</StiPdfDigitalSignatureAppearanceTypeImage>
    <StiPdfDigitalSignatureAppearanceTypeNone>无</StiPdfDigitalSignatureAppearanceTypeNone>
    <StiPdfDigitalSignatureAppearanceTypeText>文本</StiPdfDigitalSignatureAppearanceTypeText>
    <StiPenStyleDash>虚线</StiPenStyleDash>
    <StiPenStyleDashDot>点画线</StiPenStyleDashDot>
    <StiPenStyleDashDotDot>双点画线</StiPenStyleDashDotDot>
    <StiPenStyleDot>点线</StiPenStyleDot>
    <StiPenStyleDouble>Double</StiPenStyleDouble>
    <StiPenStyleNone>无</StiPenStyleNone>
    <StiPenStyleSolid>实线</StiPenStyleSolid>
    <StiPlacementInside>内</StiPlacementInside>
    <StiPlacementOutside>外</StiPlacementOutside>
    <StiPlacementOverlay>覆盖</StiPlacementOverlay>
    <StiPlesseyCheckSumModulo10>Modulo10</StiPlesseyCheckSumModulo10>
    <StiPlesseyCheckSumModulo11>Modulo11</StiPlesseyCheckSumModulo11>
    <StiPlesseyCheckSumNone>无</StiPlesseyCheckSumNone>
    <StiPreviewModeDotMatrix>矩阵</StiPreviewModeDotMatrix>
    <StiPreviewModeStandard>标准</StiPreviewModeStandard>
    <StiPreviewModeStandardAndDotMatrix>标准和点阵</StiPreviewModeStandardAndDotMatrix>
    <StiPrintOnEvenOddPagesTypeIgnore>忽略</StiPrintOnEvenOddPagesTypeIgnore>
    <StiPrintOnEvenOddPagesTypePrintOnEvenPages>打印偶数页</StiPrintOnEvenOddPagesTypePrintOnEvenPages>
    <StiPrintOnEvenOddPagesTypePrintOnOddPages>打印奇数页</StiPrintOnEvenOddPagesTypePrintOnOddPages>
    <StiPrintOnTypeAllPages>全部页</StiPrintOnTypeAllPages>
    <StiPrintOnTypeExceptFirstAndLastPage>去除首页和末页</StiPrintOnTypeExceptFirstAndLastPage>
    <StiPrintOnTypeExceptFirstPage>去除首页</StiPrintOnTypeExceptFirstPage>
    <StiPrintOnTypeExceptLastPage>去除末页</StiPrintOnTypeExceptLastPage>
    <StiPrintOnTypeOnlyFirstAndLastPage>仅首页和末页</StiPrintOnTypeOnlyFirstAndLastPage>
    <StiPrintOnTypeOnlyFirstPage>仅首页</StiPrintOnTypeOnlyFirstPage>
    <StiPrintOnTypeOnlyLastPage>仅末页</StiPrintOnTypeOnlyLastPage>
    <StiProcessAtEndOfPage>页面结束</StiProcessAtEndOfPage>
    <StiProcessAtEndOfReport>报表结束</StiProcessAtEndOfReport>
    <StiProcessAtNone>无</StiProcessAtNone>
    <StiProcessingDuplicatesTypeBasedOnTagHide>根据标记隐藏</StiProcessingDuplicatesTypeBasedOnTagHide>
    <StiProcessingDuplicatesTypeBasedOnTagMerge>根据标记合并</StiProcessingDuplicatesTypeBasedOnTagMerge>
    <StiProcessingDuplicatesTypeBasedOnTagRemoveText>根据标记移动文本</StiProcessingDuplicatesTypeBasedOnTagRemoveText>
    <StiProcessingDuplicatesTypeBasedOnValueAndTagHide>根据值和标记隐藏</StiProcessingDuplicatesTypeBasedOnValueAndTagHide>
    <StiProcessingDuplicatesTypeBasedOnValueAndTagMerge>根据值和标记合并</StiProcessingDuplicatesTypeBasedOnValueAndTagMerge>
    <StiProcessingDuplicatesTypeBasedOnValueRemoveText>根据值移动</StiProcessingDuplicatesTypeBasedOnValueRemoveText>
    <StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagHide>根据值和标记全部隐藏</StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagHide>
    <StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagMerge>根据值和标记全部合并</StiProcessingDuplicatesTypeGlobalBasedOnValueAndTagMerge>
    <StiProcessingDuplicatesTypeGlobalBasedOnValueRemoveText>根据值全部移动</StiProcessingDuplicatesTypeGlobalBasedOnValueRemoveText>
    <StiProcessingDuplicatesTypeGlobalHide>全局隐藏</StiProcessingDuplicatesTypeGlobalHide>
    <StiProcessingDuplicatesTypeGlobalMerge>全局合并</StiProcessingDuplicatesTypeGlobalMerge>
    <StiProcessingDuplicatesTypeGlobalRemoveText>全局移除文字</StiProcessingDuplicatesTypeGlobalRemoveText>
    <StiProcessingDuplicatesTypeHide>隐藏</StiProcessingDuplicatesTypeHide>
    <StiProcessingDuplicatesTypeMerge>合并</StiProcessingDuplicatesTypeMerge>
    <StiProcessingDuplicatesTypeNone>无</StiProcessingDuplicatesTypeNone>
    <StiProcessingDuplicatesTypeRemoveText>移除文本</StiProcessingDuplicatesTypeRemoveText>
    <StiProgressElementModeCircle>圆圈</StiProgressElementModeCircle>
    <StiProgressElementModeDataBars>数据条</StiProgressElementModeDataBars>
    <StiProgressElementModePie>饼状图</StiProgressElementModePie>
    <StiQRCodeBodyShapeTypeCircle>圆形</StiQRCodeBodyShapeTypeCircle>
    <StiQRCodeBodyShapeTypeCircular>圆形的</StiQRCodeBodyShapeTypeCircular>
    <StiQRCodeBodyShapeTypeDiamond>菱形</StiQRCodeBodyShapeTypeDiamond>
    <StiQRCodeBodyShapeTypeDockedDiamonds>停靠菱形</StiQRCodeBodyShapeTypeDockedDiamonds>
    <StiQRCodeBodyShapeTypeDot>点</StiQRCodeBodyShapeTypeDot>
    <StiQRCodeBodyShapeTypeRoundedSquare>圆角正方形</StiQRCodeBodyShapeTypeRoundedSquare>
    <StiQRCodeBodyShapeTypeSquare>正方形</StiQRCodeBodyShapeTypeSquare>
    <StiQRCodeBodyShapeTypeStar>星形</StiQRCodeBodyShapeTypeStar>
    <StiQRCodeBodyShapeTypeZebraCross>Zebra十字形</StiQRCodeBodyShapeTypeZebraCross>
    <StiQRCodeBodyShapeTypeZebraHorizontal>Zebra横向</StiQRCodeBodyShapeTypeZebraHorizontal>
    <StiQRCodeBodyShapeTypeZebraVertical>Zebra纵向</StiQRCodeBodyShapeTypeZebraVertical>
    <StiQRCodeEyeBallShapeTypeCircle>圆形</StiQRCodeEyeBallShapeTypeCircle>
    <StiQRCodeEyeBallShapeTypeDots>点</StiQRCodeEyeBallShapeTypeDots>
    <StiQRCodeEyeBallShapeTypeRound>球形</StiQRCodeEyeBallShapeTypeRound>
    <StiQRCodeEyeBallShapeTypeSquare>正方形</StiQRCodeEyeBallShapeTypeSquare>
    <StiQRCodeEyeBallShapeTypeStar>星形</StiQRCodeEyeBallShapeTypeStar>
    <StiQRCodeEyeBallShapeTypeZebraHorizontal>Zebra横向</StiQRCodeEyeBallShapeTypeZebraHorizontal>
    <StiQRCodeEyeBallShapeTypeZebraVertical>Zebra纵向</StiQRCodeEyeBallShapeTypeZebraVertical>
    <StiRadarStyleXFCircle>圆</StiRadarStyleXFCircle>
    <StiRadarStyleXFPolygon>多边形</StiRadarStyleXFPolygon>
    <StiReportCacheModeAuto>自动</StiReportCacheModeAuto>
    <StiReportCacheModeOff>关闭</StiReportCacheModeOff>
    <StiReportCacheModeOn>开启</StiReportCacheModeOn>
    <StiReportUnitTypeCentimeters>厘米</StiReportUnitTypeCentimeters>
    <StiReportUnitTypeHundredthsOfInch>百分之一英寸</StiReportUnitTypeHundredthsOfInch>
    <StiReportUnitTypeInches>英寸</StiReportUnitTypeInches>
    <StiReportUnitTypeMillimeters>毫米</StiReportUnitTypeMillimeters>
    <StiReportUnitTypePixels>像素</StiReportUnitTypePixels>
    <StiReportUnitTypePoints>点</StiReportUnitTypePoints>
    <StiRestrictionsAll>全部</StiRestrictionsAll>
    <StiRestrictionsAllowChange>允许改变</StiRestrictionsAllowChange>
    <StiRestrictionsAllowDelete>允许删除</StiRestrictionsAllowDelete>
    <StiRestrictionsAllowMove>允许移动</StiRestrictionsAllowMove>
    <StiRestrictionsAllowResize>允许改变大小</StiRestrictionsAllowResize>
    <StiRestrictionsAllowSelect>允许选择</StiRestrictionsAllowSelect>
    <StiRestrictionsNone>无</StiRestrictionsNone>
    <StiSelectionModeFirst>第一</StiSelectionModeFirst>
    <StiSelectionModeFromVariable>来自变量</StiSelectionModeFromVariable>
    <StiSelectionModeNothing>无</StiSelectionModeNothing>
    <StiSeriesLabelsValueTypeArgument>参数</StiSeriesLabelsValueTypeArgument>
    <StiSeriesLabelsValueTypeArgumentValue>参数 - 值</StiSeriesLabelsValueTypeArgumentValue>
    <StiSeriesLabelsValueTypeSeriesTitle>序列标题</StiSeriesLabelsValueTypeSeriesTitle>
    <StiSeriesLabelsValueTypeSeriesTitleArgument>序列标题 - 参数</StiSeriesLabelsValueTypeSeriesTitleArgument>
    <StiSeriesLabelsValueTypeSeriesTitleValue>序列标题 - 值</StiSeriesLabelsValueTypeSeriesTitleValue>
    <StiSeriesLabelsValueTypeTag>标记</StiSeriesLabelsValueTypeTag>
    <StiSeriesLabelsValueTypeValue>值</StiSeriesLabelsValueTypeValue>
    <StiSeriesLabelsValueTypeValueArgument>值 - 参数</StiSeriesLabelsValueTypeValueArgument>
    <StiSeriesLabelsValueTypeWeight>加权</StiSeriesLabelsValueTypeWeight>
    <StiSeriesSortDirectionAscending>升序</StiSeriesSortDirectionAscending>
    <StiSeriesSortDirectionDescending>降序</StiSeriesSortDirectionDescending>
    <StiSeriesSortTypeArgument>参数</StiSeriesSortTypeArgument>
    <StiSeriesSortTypeNone>无</StiSeriesSortTypeNone>
    <StiSeriesSortTypeValue>值</StiSeriesSortTypeValue>
    <StiSeriesXAxisBottomXAxis>底部X轴</StiSeriesXAxisBottomXAxis>
    <StiSeriesXAxisTopXAxis>顶部X轴</StiSeriesXAxisTopXAxis>
    <StiSeriesYAxisLeftYAxis>Y 轴左端</StiSeriesYAxisLeftYAxis>
    <StiSeriesYAxisRightYAxis>Y 轴右端</StiSeriesYAxisRightYAxis>
    <StiShapeDirectionDown>向下</StiShapeDirectionDown>
    <StiShapeDirectionLeft>向左</StiShapeDirectionLeft>
    <StiShapeDirectionRight>向右</StiShapeDirectionRight>
    <StiShapeDirectionUp>向上</StiShapeDirectionUp>
    <StiShiftModeDecreasingSize>减少尺寸</StiShiftModeDecreasingSize>
    <StiShiftModeIncreasingSize>增加尺寸</StiShiftModeIncreasingSize>
    <StiShiftModeNone>无</StiShiftModeNone>
    <StiShiftModeOnlyInWidthOfComponent>仅在组件宽度内</StiShiftModeOnlyInWidthOfComponent>
    <StiShowEmptyCellsAsConnectPointsWithLine>用直线连接点</StiShowEmptyCellsAsConnectPointsWithLine>
    <StiShowEmptyCellsAsGap>间隔</StiShowEmptyCellsAsGap>
    <StiShowEmptyCellsAsZero>零</StiShowEmptyCellsAsZero>
    <StiShowSeriesLabelsFromChart>从图表</StiShowSeriesLabelsFromChart>
    <StiShowSeriesLabelsFromSeries>从系列</StiShowSeriesLabelsFromSeries>
    <StiShowSeriesLabelsNone>无</StiShowSeriesLabelsNone>
    <StiShowXAxisBoth>全部</StiShowXAxisBoth>
    <StiShowXAxisBottom>底部</StiShowXAxisBottom>
    <StiShowXAxisCenter>中间</StiShowXAxisCenter>
    <StiShowYAxisBoth>全部</StiShowYAxisBoth>
    <StiShowYAxisCenter>中间</StiShowYAxisCenter>
    <StiShowYAxisLeft>左</StiShowYAxisLeft>
    <StiSignatureModeDraw>画</StiSignatureModeDraw>
    <StiSignatureModeType>类型</StiSignatureModeType>
    <StiSignatureTypeDigitalSignature>数字签名</StiSignatureTypeDigitalSignature>
    <StiSignatureTypeDraw>手写</StiSignatureTypeDraw>
    <StiSignatureTypeSelectStyle>选择风格</StiSignatureTypeSelectStyle>
    <StiSignatureTypeType>类型</StiSignatureTypeType>
    <StiSizeModeAutoSize>自动调整尺寸</StiSizeModeAutoSize>
    <StiSizeModeFit>适配</StiSizeModeFit>
    <StiSortDirectionAsc>升序</StiSortDirectionAsc>
    <StiSortDirectionDesc>降序</StiSortDirectionDesc>
    <StiSortDirectionNone>无</StiSortDirectionNone>
    <StiSortTypeByDisplayValue>按显示值</StiSortTypeByDisplayValue>
    <StiSortTypeByValue>按值</StiSortTypeByValue>
    <StiSqlSourceTypeStoredProcedure>存储过程</StiSqlSourceTypeStoredProcedure>
    <StiSqlSourceTypeTable>表</StiSqlSourceTypeTable>
    <StiStyleComponentTypeChart>图表</StiStyleComponentTypeChart>
    <StiStyleComponentTypeCheckBox>复选框</StiStyleComponentTypeCheckBox>
    <StiStyleComponentTypeCrossTab>交叉表</StiStyleComponentTypeCrossTab>
    <StiStyleComponentTypeImage>图像</StiStyleComponentTypeImage>
    <StiStyleComponentTypePrimitive>基元</StiStyleComponentTypePrimitive>
    <StiStyleComponentTypeText>文本</StiStyleComponentTypeText>
    <StiStyleConditionTypeComponentName>组件名</StiStyleConditionTypeComponentName>
    <StiStyleConditionTypeComponentType>组件类型</StiStyleConditionTypeComponentType>
    <StiStyleConditionTypeLocation>位置</StiStyleConditionTypeLocation>
    <StiStyleConditionTypePlacement>布局</StiStyleConditionTypePlacement>
    <StiSummaryValuesAllValues>所有值</StiSummaryValuesAllValues>
    <StiSummaryValuesSkipNulls>忽略无效值</StiSummaryValuesSkipNulls>
    <StiSummaryValuesSkipZerosAndNulls>忽略零和无效值</StiSummaryValuesSkipZerosAndNulls>
    <StiTablceCellTypeCheckBox>复选框</StiTablceCellTypeCheckBox>
    <StiTablceCellTypeImage>图像</StiTablceCellTypeImage>
    <StiTablceCellTypeRichText>富文本</StiTablceCellTypeRichText>
    <StiTablceCellTypeText>文本</StiTablceCellTypeText>
    <StiTableAutoWidthNone>无</StiTableAutoWidthNone>
    <StiTableAutoWidthPage>页面</StiTableAutoWidthPage>
    <StiTableAutoWidthTable>表</StiTableAutoWidthTable>
    <StiTableAutoWidthTypeFullTable>整张表</StiTableAutoWidthTypeFullTable>
    <StiTableAutoWidthTypeLastColumns>最后一列</StiTableAutoWidthTypeLastColumns>
    <StiTableAutoWidthTypeNone>无</StiTableAutoWidthTypeNone>
    <StiTableSizeModeAutoSize>自动调整大小</StiTableSizeModeAutoSize>
    <StiTableSizeModeFit>适配</StiTableSizeModeFit>
    <StiTargetModePercentage>百分比</StiTargetModePercentage>
    <StiTargetModeVariation>变化</StiTargetModeVariation>
    <StiTextHorAlignmentCenter>居中</StiTextHorAlignmentCenter>
    <StiTextHorAlignmentLeft>居左</StiTextHorAlignmentLeft>
    <StiTextHorAlignmentRight>居右</StiTextHorAlignmentRight>
    <StiTextHorAlignmentWidth>宽度</StiTextHorAlignmentWidth>
    <StiTextPositionCenterBottom>底部居中</StiTextPositionCenterBottom>
    <StiTextPositionCenterTop>顶部居中</StiTextPositionCenterTop>
    <StiTextPositionLeftBottom>底部居左</StiTextPositionLeftBottom>
    <StiTextPositionLeftTop>顶部居左</StiTextPositionLeftTop>
    <StiTextPositionRightBottom>底部居右</StiTextPositionRightBottom>
    <StiTextPositionRightTop>顶部居右</StiTextPositionRightTop>
    <StiTextQualityStandard>标准</StiTextQualityStandard>
    <StiTextQualityTypographic>印刷质量</StiTextQualityTypographic>
    <StiTextQualityWysiwyg>所见即所得</StiTextQualityWysiwyg>
    <StiTextSizeModeFit>适应</StiTextSizeModeFit>
    <StiTextSizeModeTrimming>修整</StiTextSizeModeTrimming>
    <StiTextSizeModeWordWrap>自动换行</StiTextSizeModeWordWrap>
    <StiThemeAppearanceAuto>自动</StiThemeAppearanceAuto>
    <StiThemeAppearanceDark>深</StiThemeAppearanceDark>
    <StiThemeAppearanceLight>浅</StiThemeAppearanceLight>
    <StiThemeAppearanceLightGray>浅灰</StiThemeAppearanceLightGray>
    <StiTimeDateStepDay>天</StiTimeDateStepDay>
    <StiTimeDateStepHour>小时</StiTimeDateStepHour>
    <StiTimeDateStepMinute>分</StiTimeDateStepMinute>
    <StiTimeDateStepMonth>月</StiTimeDateStepMonth>
    <StiTimeDateStepNone>空</StiTimeDateStepNone>
    <StiTimeDateStepSecond>秒</StiTimeDateStepSecond>
    <StiTimeDateStepYear>年</StiTimeDateStepYear>
    <StiTitlePositionInside>里面</StiTitlePositionInside>
    <StiTitlePositionOutside>外面</StiTitlePositionOutside>
    <StiTopNModeBottom>下</StiTopNModeBottom>
    <StiTopNModeNone>空</StiTopNModeNone>
    <StiTopNModeTop>上</StiTopNModeTop>
    <StiTypeModeList>列表</StiTypeModeList>
    <StiTypeModeNullableValue>空值</StiTypeModeNullableValue>
    <StiTypeModeRange>范围</StiTypeModeRange>
    <StiTypeModeValue>值</StiTypeModeValue>
    <StiUnitAlignmentPrefix>前缀</StiUnitAlignmentPrefix>
    <StiUnitAlignmentPrefixInside>内部前缀</StiUnitAlignmentPrefixInside>
    <StiUnitAlignmentPrefixInsideWithSpace>内部加空格前缀</StiUnitAlignmentPrefixInsideWithSpace>
    <StiUnitAlignmentSuffix>后缀</StiUnitAlignmentSuffix>
    <StiUnitAlignmentSuffixInside>内部后缀</StiUnitAlignmentSuffixInside>
    <StiUnitAlignmentSuffixInsideWithSpace>内部带空格的后缀</StiUnitAlignmentSuffixInsideWithSpace>
    <StiVertAlignmentBottom>底部</StiVertAlignmentBottom>
    <StiVertAlignmentCenter>居中</StiVertAlignmentCenter>
    <StiVertAlignmentTop>顶端</StiVertAlignmentTop>
    <StiViewModeNormal>正常</StiViewModeNormal>
    <StiViewModePageBreakPreview>跨页预览</StiViewModePageBreakPreview>
    <StiXmlTypeAdoNetXml>ADO.NET XML</StiXmlTypeAdoNetXml>
    <StiXmlTypeXml>XML</StiXmlTypeXml>
    <StringAlignmentCenter>居中</StringAlignmentCenter>
    <StringAlignmentFar>远</StringAlignmentFar>
    <StringAlignmentNear>近</StringAlignmentNear>
    <StringTrimmingCharacter>字符</StringTrimmingCharacter>
    <StringTrimmingEllipsisCharacter>省略字符</StringTrimmingEllipsisCharacter>
    <StringTrimmingEllipsisPath>省略路径</StringTrimmingEllipsisPath>
    <StringTrimmingEllipsisWord>省略字</StringTrimmingEllipsisWord>
    <StringTrimmingNone>无</StringTrimmingNone>
    <StringTrimmingWord>字</StringTrimmingWord>
  </PropertyEnum>
  <PropertyEvents>
    <AfterPrintEvent>打印后于</AfterPrintEvent>
    <AfterSelectEvent>选择后</AfterSelectEvent>
    <BeforePrintEvent>打印前于</BeforePrintEvent>
    <BeginRenderEvent>开始渲染</BeginRenderEvent>
    <CheckedChangedEvent>选择改变后</CheckedChangedEvent>
    <ClickEvent>点击</ClickEvent>
    <ClosedFormEvent>窗体关闭后</ClosedFormEvent>
    <ClosingFormEvent>窗体关闭时</ClosingFormEvent>
    <ColumnBeginRenderEvent>列开始渲染</ColumnBeginRenderEvent>
    <ColumnEndRenderEvent>列结束渲染</ColumnEndRenderEvent>
    <ConnectedEvent>ConnectedEvent</ConnectedEvent>
    <ConnectingEvent>ConnectingEvent</ConnectingEvent>
    <DisconnectedEvent>Disconnected</DisconnectedEvent>
    <DisconnectingEvent>Disconnecting</DisconnectingEvent>
    <DoubleClickEvent>双击</DoubleClickEvent>
    <EndRenderEvent>结束渲染</EndRenderEvent>
    <EnterEvent>键入</EnterEvent>
    <ExportedEvent>导出后</ExportedEvent>
    <ExportingEvent>导出时</ExportingEvent>
    <FillParametersEvent>填入参数</FillParametersEvent>
    <GetArgumentEvent>获取参数</GetArgumentEvent>
    <GetBarCodeEvent>获得条码</GetBarCodeEvent>
    <GetBookmarkEvent>获取书签</GetBookmarkEvent>
    <GetCheckedEvent>复选打勾</GetCheckedEvent>
    <GetCollapsedEvent>获取折叠</GetCollapsedEvent>
    <GetCrossValueEvent>获取 Cross 值</GetCrossValueEvent>
    <GetCutPieListEvent>获取Cut Pie列表</GetCutPieListEvent>
    <GetDataUrlEvent>获取DataUrl</GetDataUrlEvent>
    <GetDisplayCrossValueEvent>获取显示 Cross 值</GetDisplayCrossValueEvent>
    <GetDrillDownReportEvent>获取钻取报表</GetDrillDownReportEvent>
    <GetExcelSheetEvent>得到 Excel Sheet</GetExcelSheetEvent>
    <GetExcelValueEvent>获取 Excel 值</GetExcelValueEvent>
    <GetHyperlinkEvent>获取超链接</GetHyperlinkEvent>
    <GetImageDataEvent>获取图片数据</GetImageDataEvent>
    <GetImageURLEvent>获取图片 URL</GetImageURLEvent>
    <GetListOfArgumentsEvent>获取参数列表</GetListOfArgumentsEvent>
    <GetListOfHyperlinksEvent>获取超链接列表</GetListOfHyperlinksEvent>
    <GetListOfTagsEvent>获取标记列表</GetListOfTagsEvent>
    <GetListOfToolTipsEvent>获取工具栏列表</GetListOfToolTipsEvent>
    <GetListOfValuesEndEvent>获取末端值列表</GetListOfValuesEndEvent>
    <GetListOfValuesEvent>获取参数值</GetListOfValuesEvent>
    <GetListOfWeights>获取加权列表</GetListOfWeights>
    <GetListOfWeightsEvent>获取加权列表</GetListOfWeightsEvent>
    <GetPointerEvent>取回指标</GetPointerEvent>
    <GetSummaryExpressionEvent>获取统计表达式</GetSummaryExpressionEvent>
    <GetTagEvent>获取 Tag</GetTagEvent>
    <GetTitleEvent>得到标题</GetTitleEvent>
    <GetToolTipEvent>获取快速提示</GetToolTipEvent>
    <GetValueEndEvent>获取末端值</GetValueEndEvent>
    <GetValueEvent>获取值</GetValueEvent>
    <GetWeightEvent>获取加权值</GetWeightEvent>
    <LeaveEvent>离开</LeaveEvent>
    <LoadFormEvent>加载窗体</LoadFormEvent>
    <MouseDownEvent>鼠标下移</MouseDownEvent>
    <MouseEnterEvent>鼠标键入</MouseEnterEvent>
    <MouseLeaveEvent>鼠标离开</MouseLeaveEvent>
    <MouseMoveEvent>鼠标移动</MouseMoveEvent>
    <MouseUpEvent>鼠标上移</MouseUpEvent>
    <NewAutoSeriesEvent>新的自动序列</NewAutoSeriesEvent>
    <PositionChangedEvent>位置改变后</PositionChangedEvent>
    <PrintedEvent>打印后</PrintedEvent>
    <PrintingEvent>打印时</PrintingEvent>
    <ProcessCellEvent>处理单元格</ProcessCellEvent>
    <ProcessChartEvent>处理图表</ProcessChartEvent>
    <RefreshingEvent>正在刷新</RefreshingEvent>
    <RenderingEvent>正在渲染</RenderingEvent>
    <ReportCacheProcessingEvent>报告缓存处理</ReportCacheProcessingEvent>
    <SelectedIndexChangedEvent>所选索引改变后</SelectedIndexChangedEvent>
    <StateRestoreEvent>状态还原</StateRestoreEvent>
    <StateSaveEvent>状态保存</StateSaveEvent>
    <ValueChangedEvent>值改变后</ValueChangedEvent>
  </PropertyEvents>
  <PropertyHatchStyle>
    <BackwardDiagonal>反向对角线</BackwardDiagonal>
    <Cross>斜线</Cross>
    <DarkDownwardDiagonal>粗反向对角线</DarkDownwardDiagonal>
    <DarkHorizontal>粗横线</DarkHorizontal>
    <DarkUpwardDiagonal>粗向上对角线</DarkUpwardDiagonal>
    <DarkVertical>粗竖线</DarkVertical>
    <DashedDownwardDiagonal>虚向下对角线</DashedDownwardDiagonal>
    <DashedHorizontal>虚横线</DashedHorizontal>
    <DashedUpwardDiagonal>虚向上对角线</DashedUpwardDiagonal>
    <DashedVertical>虚竖线</DashedVertical>
    <DiagonalBrick>对角砖块</DiagonalBrick>
    <DiagonalCross>对角交叉线</DiagonalCross>
    <Divot>草痕</Divot>
    <DottedDiamond>菱形虚框</DottedDiamond>
    <DottedGrid>虚线网格</DottedGrid>
    <ForwardDiagonal>正向对角线</ForwardDiagonal>
    <Horizontal>横线</Horizontal>
    <HorizontalBrick>水平砖块</HorizontalBrick>
    <LargeCheckerBoard>大棋盘</LargeCheckerBoard>
    <LargeConfetti>大碎花</LargeConfetti>
    <LargeGrid>大网格</LargeGrid>
    <LightDownwardDiagonal>细向下对角线</LightDownwardDiagonal>
    <LightHorizontal>细横线</LightHorizontal>
    <LightUpwardDiagonal>细向上对角线</LightUpwardDiagonal>
    <LightVertical>细竖线</LightVertical>
    <NarrowHorizontal>窄横线</NarrowHorizontal>
    <NarrowVertical>窄竖线</NarrowVertical>
    <OutlinedDiamond>菱形实框</OutlinedDiamond>
    <Percent05>05%</Percent05>
    <Percent10>10%</Percent10>
    <Percent20>20%</Percent20>
    <Percent25>25%</Percent25>
    <Percent30>30%</Percent30>
    <Percent40>40%</Percent40>
    <Percent50>50%</Percent50>
    <Percent60>60%</Percent60>
    <Percent70>70%</Percent70>
    <Percent75>75%</Percent75>
    <Percent80>80%</Percent80>
    <Percent90>90%</Percent90>
    <Plaid>方格花纹</Plaid>
    <Shingle>瓦格</Shingle>
    <SmallCheckerBoard>小棋盘</SmallCheckerBoard>
    <SmallConfetti>小碎花</SmallConfetti>
    <SmallGrid>小网格</SmallGrid>
    <SolidDiamond>实心菱形</SolidDiamond>
    <Sphere>球形</Sphere>
    <Trellis>方格</Trellis>
    <Vertical>竖线</Vertical>
    <Wave>波</Wave>
    <Weave>编织线</Weave>
    <WideDownwardDiagonal>宽向下对角线</WideDownwardDiagonal>
    <WideUpwardDiagonal>宽向上对角线</WideUpwardDiagonal>
    <ZigZag>锯齿线</ZigZag>
  </PropertyHatchStyle>
  <PropertyMain>
    <AcceptsReturn>接受返回</AcceptsReturn>
    <AcceptsTab>接受Tab</AcceptsTab>
    <Actual>实际</Actual>
    <AddClearZone>添加空白区</AddClearZone>
    <Advanced>高级</Advanced>
    <AggregateFunction>聚合函数</AggregateFunction>
    <AggregateFunctions>聚合函数</AggregateFunctions>
    <Alias>别名</Alias>
    <Alignment>对齐</Alignment>
    <AllowApplyBorderColor>允许应用边框颜色</AllowApplyBorderColor>
    <AllowApplyBrush>允许应用画刷</AllowApplyBrush>
    <AllowApplyBrushNegative>允许应用负数画刷</AllowApplyBrushNegative>
    <AllowApplyColorNegative>允许应用负数颜色</AllowApplyColorNegative>
    <AllowApplyLineColor>允许应用线条颜色</AllowApplyLineColor>
    <AllowApplyStyle>允许套用样式</AllowApplyStyle>
    <AllowCleanSignature>允许干净签名</AllowCleanSignature>
    <AllowExpressions>允许表达式</AllowExpressions>
    <AllowHtmlTags>允许Html标签</AllowHtmlTags>
    <AllowNull>允许Null</AllowNull>
    <AllowSeries>允许系列</AllowSeries>
    <AllowSeriesElements>允许系列元素</AllowSeriesElements>
    <AllowSorting>允许排序</AllowSorting>
    <AllowUseBackColor>允许使用背景颜色</AllowUseBackColor>
    <AllowUseBorder>允许使用边界</AllowUseBorder>
    <AllowUseBorderFormatting>允许使用边框格式</AllowUseBorderFormatting>
    <AllowUseBorderSides>允许使用边框面</AllowUseBorderSides>
    <AllowUseBorderSidesFromLocation>允许从位置使用边框面</AllowUseBorderSidesFromLocation>
    <AllowUseBrush>允许使用格式刷</AllowUseBrush>
    <AllowUseFont>允许使用字体</AllowUseFont>
    <AllowUseForeColor>允许使用前景色</AllowUseForeColor>
    <AllowUseHorAlignment>允许使用水平对齐</AllowUseHorAlignment>
    <AllowUseImage>允许使用图像</AllowUseImage>
    <AllowUseNegativeTextBrush>允许使用负文本画笔</AllowUseNegativeTextBrush>
    <AllowUserValues>允许用户值</AllowUserValues>
    <AllowUseTextBrush>允许使用文本刷</AllowUseTextBrush>
    <AllowUseTextFormat>允许使用文本格式</AllowUseTextFormat>
    <AllowUseTextOptions>允许使用文本选项</AllowUseTextOptions>
    <AllowUseVertAlignment>允许使用垂直对齐</AllowUseVertAlignment>
    <AllowUsingAsSqlParameter>允许作为结构化查询语言参数使用</AllowUsingAsSqlParameter>
    <AlternatingBackColor>交替背景色</AlternatingBackColor>
    <AlternatingCellBackColor>交替单元格背景色</AlternatingCellBackColor>
    <AlternatingCellForeColor>交替单元格前景色</AlternatingCellForeColor>
    <AlternatingDataColor>交替数据颜色</AlternatingDataColor>
    <AlternatingDataForeground>交替数据前景</AlternatingDataForeground>
    <Always>一贯</Always>
    <Anchor>锚点</Anchor>
    <Angle>角度</Angle>
    <Antialiasing>混叠效应</Antialiasing>
    <Apply>应用</Apply>
    <Area>区域</Area>
    <Argument>参数</Argument>
    <ArgumentDataColumn>参数列</ArgumentDataColumn>
    <ArgumentFormat>参数格式</ArgumentFormat>
    <Arguments>参数</Arguments>
    <ArrowHeight>箭头高度</ArrowHeight>
    <ArrowStyle>箭头样式</ArrowStyle>
    <ArrowWidth>箭头宽度</ArrowWidth>
    <AspectRatio>长宽比</AspectRatio>
    <Author>作者</Author>
    <Auto>自动</Auto>
    <AutoCalculateCenterPoint>自动计算中心点</AutoCalculateCenterPoint>
    <AutoDataColumns>自动数据列</AutoDataColumns>
    <AutoDataRows>自动数据行</AutoDataRows>
    <AutoLocalizeReportOnRun>运行时自动本地化报表</AutoLocalizeReportOnRun>
    <AutoRefresh>自动更新</AutoRefresh>
    <AutoRotate>自动旋转</AutoRotate>
    <AutoScale>自动缩放</AutoScale>
    <AutoSeriesColorDataColumn>自动序列数据列颜色</AutoSeriesColorDataColumn>
    <AutoSeriesKeyDataColumn>自动序列数据列关键字</AutoSeriesKeyDataColumn>
    <AutoSeriesTitleDataColumn>自动序列数据列标题</AutoSeriesTitleDataColumn>
    <AutoWidth>自动宽度</AutoWidth>
    <AutoWidthType>自动宽度类型</AutoWidthType>
    <AvailableInTheViewer>在查看器中可用</AvailableInTheViewer>
    <AxisLabelsColor>轴标签颜色</AxisLabelsColor>
    <AxisLineColor>轴线颜色</AxisLineColor>
    <AxisTitleColor>轴标题颜色</AxisTitleColor>
    <AxisValue>轴值</AxisValue>
    <BackColor>背景色</BackColor>
    <Background>背景</Background>
    <BackgroundColor>背景颜色</BackgroundColor>
    <BandColor>条纹颜色</BandColor>
    <BarCodeType>条形码类型</BarCodeType>
    <BasicStyleColor>基本类型颜色</BasicStyleColor>
    <Blend>混合</Blend>
    <Blocks>Blocks</Blocks>
    <BodyBrush>刷子</BodyBrush>
    <BodyShape>内部形状</BodyShape>
    <Bold>粗体</Bold>
    <Bookmark>书签</Bookmark>
    <Border>边框</Border>
    <BorderBrush>边界刷</BorderBrush>
    <BorderColor>边框颜色</BorderColor>
    <BorderColorNegative>负值边框颜色</BorderColorNegative>
    <Borders>边框</Borders>
    <BorderSize>边框尺寸</BorderSize>
    <BorderStyle>边框样式</BorderStyle>
    <BorderThickness>边框粗细</BorderThickness>
    <BorderWidth>边框宽度</BorderWidth>
    <Bottom>底端</Bottom>
    <BottomLeft>左下</BottomLeft>
    <BottomRight>右下</BottomRight>
    <BottomSide>底部大小</BottomSide>
    <BreakIfLessThan>小于时截断</BreakIfLessThan>
    <Brush>画笔</Brush>
    <BrushNegative>负数刷</BrushNegative>
    <BrushType>格式刷类型</BrushType>
    <BubbleBackColor>气泡背景颜色</BubbleBackColor>
    <BubbleBorderColor>气泡边框颜色</BubbleBorderColor>
    <BubbleScale>气泡比例</BubbleScale>
    <BusinessObject>Business Object</BusinessObject>
    <CacheAllData>缓存全部数据</CacheAllData>
    <CacheTotals>缓存总计</CacheTotals>
    <CalcInvisible>计算列不可见</CalcInvisible>
    <CalculatedDataColumn>计算列</CalculatedDataColumn>
    <CalculationMode>计算模式</CalculationMode>
    <CanBreak>跨页</CanBreak>
    <Cancel>取消</Cancel>
    <CanGrow>自动伸展</CanGrow>
    <CanShrink>自动收缩</CanShrink>
    <Categories>分类</Categories>
    <Category>分类</Category>
    <CategoryConnections>连接</CategoryConnections>
    <CellAlignment>单元格对齐</CellAlignment>
    <CellBackColor>单元格背景色</CellBackColor>
    <CellDockStyle>单元停靠样式</CellDockStyle>
    <CellForeColor>单元格前景色</CellForeColor>
    <CellHeight>单元格高度</CellHeight>
    <CellType>单元类型</CellType>
    <CellWidth>单元格宽度</CellWidth>
    <Center>中心</Center>
    <CenterPoint>中心点</CenterPoint>
    <ChartAreaBorderColor>图表区边框颜色</ChartAreaBorderColor>
    <ChartAreaBorderThickness>图表区域边框厚度</ChartAreaBorderThickness>
    <ChartAreaBrush>图表区刷</ChartAreaBrush>
    <ChartAreaShowShadow>图表区显示为阴影</ChartAreaShowShadow>
    <ChartType>图表类型</ChartType>
    <CheckAlignment>对齐</CheckAlignment>
    <CheckColor>颜色选择</CheckColor>
    <Checked>选中</Checked>
    <CheckedIcon>检查图标</CheckedIcon>
    <CheckOnClick>点击选择</CheckOnClick>
    <CheckStyle>选择样式</CheckStyle>
    <CheckStyleForFalse>未选中样式</CheckStyleForFalse>
    <CheckStyleForTrue>选中样式</CheckStyleForTrue>
    <Checksum>Checksum</Checksum>
    <CheckSum>选择 Sum</CheckSum>
    <CheckSum1>选择 Sum1</CheckSum1>
    <CheckSum2>选择 Sum2</CheckSum2>
    <Child>子级</Child>
    <ChildColumns>子栏</ChildColumns>
    <ChildSource>子数据源</ChildSource>
    <City>城市</City>
    <ClearFormat>清除格式</ClearFormat>
    <CloneContainer>克隆容器</CloneContainer>
    <CloseValues>关闭值</CloseValues>
    <Code>代码</Code>
    <CodePage>代码页</CodePage>
    <Collapsed>折叠</Collapsed>
    <CollapseGroupFooter>折叠分组底部</CollapseGroupFooter>
    <CollapsingEnabled>可折叠</CollapsingEnabled>
    <Collate>比较</Collate>
    <CollectionName>集合名称</CollectionName>
    <Color>颜色</Color>
    <ColorDataColumn>数据列涂色</ColorDataColumn>
    <ColorEach>颜色每</ColorEach>
    <ColorMeter>颜色</ColorMeter>
    <Colors>颜色</Colors>
    <ColorScaleCondition>色度条件</ColorScaleCondition>
    <ColorScaleType>色度类型</ColorScaleType>
    <Column>栏</Column>
    <ColumnCount>列个数</ColumnCount>
    <ColumnDirection>方向</ColumnDirection>
    <ColumnGaps>间距</ColumnGaps>
    <ColumnHeaderBackColor>列标头背景色</ColumnHeaderBackColor>
    <ColumnHeaderForeColor>列标头前景色</ColumnHeaderForeColor>
    <ColumnHeadersVisible>标题可见</ColumnHeadersVisible>
    <Columns>栏数</Columns>
    <ColumnShape>柱形</ColumnShape>
    <ColumnWidth>栏宽</ColumnWidth>
    <CommandTimeout>命令超时</CommandTimeout>
    <CompanyPrefix>公司前缀</CompanyPrefix>
    <ComponentStyle>组件样式</ComponentStyle>
    <Condition>条件</Condition>
    <ConditionOptions>条件选项</ConditionOptions>
    <Conditions>条件</Conditions>
    <ConnectionString>连接字符串</ConnectionString>
    <ConnectOnStart>开始连接</ConnectOnStart>
    <ConstantLines>恒定线</ConstantLines>
    <Container>容器</Container>
    <ContentAlignment>内容对齐</ContentAlignment>
    <ContinuousText>连续文本</ContinuousText>
    <ContourColor>轮廓线颜色</ContourColor>
    <Converting>正在转换</Converting>
    <ConvertNulls>无效转换</ConvertNulls>
    <Copies>拷贝</Copies>
    <CornerRadius>圆角半径</CornerRadius>
    <Count>计数</Count>
    <CountData>行数</CountData>
    <Country>国家</Country>
    <Create>创建</Create>
    <CreateFieldOnDoubleClick>双击创建字段</CreateFieldOnDoubleClick>
    <CreateLabel>创建标签</CreateLabel>
    <CrossFiltering>交叉过滤</CrossFiltering>
    <Culture>区域</Culture>
    <CustomFonts>自定义字体</CustomFonts>
    <CustomFormat>自定义格式</CustomFormat>
    <CutPieList>Cut Pie列表</CutPieList>
    <Data>数据</Data>
    <DataAdapter>数据适配器</DataAdapter>
    <DataAdapters>数据适配器</DataAdapters>
    <DataBarCondition>数据条条件</DataBarCondition>
    <DataBindings>数据绑定</DataBindings>
    <DataCells>数据单元</DataCells>
    <DataColor>数据涂色</DataColor>
    <DataColumn>数据列</DataColumn>
    <DataColumns>数据列</DataColumns>
    <DataField>数据字段</DataField>
    <DataForeground>数据前景</DataForeground>
    <DataRelation>数据关系</DataRelation>
    <DataRows>数据行</DataRows>
    <DataSource>数据源</DataSource>
    <DataSources>数据源</DataSources>
    <DataTextField>数据字段文本</DataTextField>
    <DataTransformation>数据转换</DataTransformation>
    <DataType>数据类型</DataType>
    <DataUrl>数据网址</DataUrl>
    <DateInfo>数据信息</DateInfo>
    <DateTimeMode>日期时间模式</DateTimeMode>
    <DateTimeStep>日期时间间隔</DateTimeStep>
    <DecimalDigits>小数位数</DecimalDigits>
    <Default>默认</Default>
    <DefaultColor>默认颜色</DefaultColor>
    <DefaultHeightCell>默认单元高度</DefaultHeightCell>
    <DefaultNamespace>默认命名空间</DefaultNamespace>
    <DefaultSettings>默认设置</DefaultSettings>
    <DependentColumn>依赖列</DependentColumn>
    <DependentValue>依赖值</DependentValue>
    <Description>描述</Description>
    <Destination>终点</Destination>
    <DetectUrls>指向 Urls</DetectUrls>
    <DeviceWidth>设备宽度</DeviceWidth>
    <DialogResult>对话框结果</DialogResult>
    <Diameter>直径</Diameter>
    <Direction>方向</Direction>
    <Disabled>残缺</Disabled>
    <DisplayNameType>显示名称类型</DisplayNameType>
    <DisplayValue>显示值</DisplayValue>
    <Distance>距离</Distance>
    <DistanceBetweenTabs>制表符间距</DistanceBetweenTabs>
    <DistinguishedName>可分辨名称</DistinguishedName>
    <Dock>停靠</Dock>
    <DockableTable>可停靠的表格</DockableTable>
    <DockStyle>停靠样式</DockStyle>
    <DrawBorder>绘制边框</DrawBorder>
    <DrawHatch>绘制影线</DrawHatch>
    <DrawLine>绘制线</DrawLine>
    <DrillDown>钻取</DrillDown>
    <DrillDownEnabled>可钻取</DrillDownEnabled>
    <DrillDownMode>钻取模式</DrillDownMode>
    <DrillDownPage>钻取页</DrillDownPage>
    <DrillDownParameter1>钻取参数1</DrillDownParameter1>
    <DrillDownParameter2>钻取参数2</DrillDownParameter2>
    <DrillDownParameter3>钻取参数3</DrillDownParameter3>
    <DrillDownParameter4>钻取参数4</DrillDownParameter4>
    <DrillDownParameter5>钻取参数5</DrillDownParameter5>
    <DrillDownParameters>钻取参数</DrillDownParameters>
    <DrillDownReport>钻取报表</DrillDownReport>
    <DropDownAlign>下拉对齐</DropDownAlign>
    <DropDownStyle>下拉样式</DropDownStyle>
    <DropDownWidth>下拉宽度</DropDownWidth>
    <DropShadow>下拉阴影</DropShadow>
    <Duplex>全双工</Duplex>
    <Editable>可编辑</Editable>
    <Effects>效果</Effects>
    <EmptyBorderBrush>清空边框刷</EmptyBorderBrush>
    <EmptyBorderWidth>清空边框宽度</EmptyBorderWidth>
    <EmptyBrush>清空画刷</EmptyBrush>
    <EmptyValue>空值</EmptyValue>
    <Enabled>可用</Enabled>
    <EnableLog>使用日志</EnableLog>
    <EncodingMode>编码模式</EncodingMode>
    <EncodingType>编码类型</EncodingType>
    <EndCap>结束端</EndCap>
    <EndColor>结束颜色</EndColor>
    <EndValue>结束值</EndValue>
    <EndValues>结束值</EndValues>
    <EndWidth>结束宽度</EndWidth>
    <EngineVersion>引擎版本</EngineVersion>
    <EnumeratorSeparator>Enumerator 分隔符</EnumeratorSeparator>
    <EnumeratorType>Enumerator 类型</EnumeratorType>
    <ErrorCorrectionLevel>误差修正级别</ErrorCorrectionLevel>
    <ErrorsCorrectionLevel>误差修正级别</ErrorsCorrectionLevel>
    <EvenStyle>偶数行样式</EvenStyle>
    <ExceedMargins>超出</ExceedMargins>
    <ExcelSheet>Excel Sheet</ExcelSheet>
    <ExcelValue>Excel值</ExcelValue>
    <Expand>扩展</Expand>
    <Exponential>指数</Exponential>
    <ExportAsImage>导出为图像</ExportAsImage>
    <Expression>表达式</Expression>
    <ExtensionDigit>扩展数字</ExtensionDigit>
    <EyeBallBrush>码眼刷</EyeBallBrush>
    <EyeBallShape>码眼形状</EyeBallShape>
    <EyeFrameBrush>码眼边框刷</EyeFrameBrush>
    <EyeFrameShape>码眼边框形状</EyeFrameShape>
    <FaqPage>FAQ</FaqPage>
    <Field>领域</Field>
    <FieldIs>字段是</FieldIs>
    <Fifth>第五的</Fifth>
    <File>文件</File>
    <Fill>填充</Fill>
    <FillColor>颜色填充</FillColor>
    <Filter>过滤</Filter>
    <FilterElements>过滤元素</FilterElements>
    <FilterEngine>过滤引擎</FilterEngine>
    <FilterMode>过滤模式</FilterMode>
    <FilterOn>启用过滤</FilterOn>
    <Filters>过滤</Filters>
    <First>第一</First>
    <FirstTabOffset>首个制表符偏移</FirstTabOffset>
    <FixedWidth>固定宽度</FixedWidth>
    <Flat>平滑</Flat>
    <FlatMode>平滑模式</FlatMode>
    <Focus>得到焦点</Focus>
    <Font>字体</Font>
    <FontBold>粗体</FontBold>
    <FontItalic>斜体</FontItalic>
    <FontName>字体名称</FontName>
    <FontSize>字体尺寸</FontSize>
    <FontSizeMode>字体大小模式</FontSizeMode>
    <FontStrikeout>删除线</FontStrikeout>
    <FontSubscript>下标</FontSubscript>
    <FontSuperscript>上标</FontSuperscript>
    <FontUnderline>下划线</FontUnderline>
    <FontUnit>字体单位</FontUnit>
    <FooterCanBreak>页脚可以中断</FooterCanBreak>
    <FooterCanGrow>页脚可以放大</FooterCanGrow>
    <FooterCanShrink>页脚可以收缩</FooterCanShrink>
    <FooterColor>页脚颜色</FooterColor>
    <FooterFont>页脚字体</FooterFont>
    <FooterForeColor>页脚前色</FooterForeColor>
    <FooterForeground>页脚前景</FooterForeground>
    <FooterPrintAtBottom>底部打印页脚</FooterPrintAtBottom>
    <FooterPrintIfEmpty>空白处打印页脚</FooterPrintIfEmpty>
    <FooterPrintOn>页脚打印</FooterPrintOn>
    <FooterPrintOnAllPages>所有页面打印页脚</FooterPrintOnAllPages>
    <FooterPrintOnEvenOddPages>奇偶页打印页脚</FooterPrintOnEvenOddPages>
    <FooterRowsCount>页脚行数</FooterRowsCount>
    <Footers>页脚</Footers>
    <ForeColor>前景色</ForeColor>
    <Foreground>前景</Foreground>
    <Format>格式</Format>
    <FormName>表单名称</FormName>
    <FormSettings>表单设置</FormSettings>
    <Fourth>第四</Fourth>
    <From>来自</From>
    <FrozenColumns>冻结列</FrozenColumns>
    <FullConvertExpression>完整转换表达式</FullConvertExpression>
    <FullName>全名</FullName>
    <Function>函数</Function>
    <Functions>函数</Functions>
    <GapAfterLastColumn>最后一列后的间隔</GapAfterLastColumn>
    <GisSettings>Gis设置</GisSettings>
    <GlobalizationStrings>全局字符串</GlobalizationStrings>
    <GlobalizedName>全局名称</GlobalizedName>
    <GlyphColor>字形颜色</GlyphColor>
    <GridColor>网格颜色</GridColor>
    <GridColumns>网格列</GridColumns>
    <GridLineColor>网格线颜色</GridLineColor>
    <GridLinesHor>水平网格线</GridLinesHor>
    <GridLinesHorColor>网格线水平颜色</GridLinesHorColor>
    <GridLinesHorRight>网格线水平向右</GridLinesHorRight>
    <GridLineStyle>网格线样式</GridLineStyle>
    <GridLinesVert>垂直网格线</GridLinesVert>
    <GridLinesVertColor>网格线垂直颜色</GridLinesVertColor>
    <GridOutline>网格大纲</GridOutline>
    <GridVerticalStep>网格垂直阶梯</GridVerticalStep>
    <Group>组</Group>
    <GroupDataColumn>组数据列</GroupDataColumn>
    <GroupMeter>组</GroupMeter>
    <GrowToHeight>适应高度</GrowToHeight>
    <Header>页眉</Header>
    <HeaderAlignment>标题对齐</HeaderAlignment>
    <HeaderBackColor>标题背景色</HeaderBackColor>
    <HeaderCanBreak>页眉可以中断</HeaderCanBreak>
    <HeaderCanGrow>页眉可以放大</HeaderCanGrow>
    <HeaderCanShrink>页眉可以缩小</HeaderCanShrink>
    <HeaderColor>标题颜色</HeaderColor>
    <HeaderFont>标题字体</HeaderFont>
    <HeaderForeColor>标题前景色</HeaderForeColor>
    <HeaderForeground>标题前景</HeaderForeground>
    <HeaderPrintAtBottom>底部打印页眉</HeaderPrintAtBottom>
    <HeaderPrintIfEmpty>空白处打印页眉</HeaderPrintIfEmpty>
    <HeaderPrintOn>页眉打印</HeaderPrintOn>
    <HeaderPrintOnAllPages>所有页面打印页眉</HeaderPrintOnAllPages>
    <HeaderPrintOnEvenOddPages>奇偶页打印页眉</HeaderPrintOnEvenOddPages>
    <HeaderRowsCount>页眉行数</HeaderRowsCount>
    <Headers>页眉</Headers>
    <HeaderText>标题文本</HeaderText>
    <Heading>标题</Heading>
    <Heatmap>热力图</Heatmap>
    <HeatmapColors>热图颜色</HeatmapColors>
    <HeatmapWithGroup>带分组热力图</HeatmapWithGroup>
    <Height>高度</Height>
    <Hidden>隐藏</Hidden>
    <HideSeriesWithEmptyTitle>空标题的隐藏系列</HideSeriesWithEmptyTitle>
    <HideZeros>零不显示</HideZeros>
    <High>高</High>
    <HighlightCondition>高亮条件</HighlightCondition>
    <HighValues>高值</HighValues>
    <HorAlignment>水平位置</HorAlignment>
    <HorSpacing>水平间距</HorSpacing>
    <HotBackColor>热门背景色</HotBackColor>
    <HotColumnHeaderBackColor>热门列标题背景色</HotColumnHeaderBackColor>
    <HotForeColor>热门前景色</HotForeColor>
    <HotGlyphColor>热门字形色</HotGlyphColor>
    <HotHeaderColor>热门标题颜色</HotHeaderColor>
    <HotkeyPrefix>热键前缀</HotkeyPrefix>
    <HotRowHeaderBackColor>热门行标题背景色</HotRowHeaderBackColor>
    <HotSelectedBackColor>热门选定背景色</HotSelectedBackColor>
    <HotSelectedForeColor>热门选定前景色</HotSelectedForeColor>
    <HotSelectedGlyphColor>热门选定字形色</HotSelectedGlyphColor>
    <HtmlPreviewMode>HTML预览模式</HtmlPreviewMode>
    <HtmlTags>Html Tag</HtmlTags>
    <Hyperlink>超链接</Hyperlink>
    <HyperlinkDataColumn>超链接数据列</HyperlinkDataColumn>
    <HyperlinkPattern>超链接模式</HyperlinkPattern>
    <Icon>图标</Icon>
    <IconAlignment>图标对齐</IconAlignment>
    <IconBrush>图标刷</IconBrush>
    <IconColor>图标颜色</IconColor>
    <IconSet>图标集</IconSet>
    <IconSetCondition>图标集条件</IconSetCondition>
    <Idents>页面边框</Idents>
    <Image>图片</Image>
    <ImageAlign>图像对齐</ImageAlign>
    <ImageAlignment>对齐</ImageAlignment>
    <ImageAspectRatio>图像纵横比</ImageAspectRatio>
    <ImageData>数据</ImageData>
    <ImageEnabled>图像已启用</ImageEnabled>
    <ImageHorAlignment>图像水平对齐</ImageHorAlignment>
    <ImageMultipleFactor>缩放</ImageMultipleFactor>
    <ImageRotation>图像旋转</ImageRotation>
    <ImageShowBehind>图像显示在后面</ImageShowBehind>
    <ImageStretch>拉伸</ImageStretch>
    <ImageTiling>平铺</ImageTiling>
    <ImageTransparency>图像透明度</ImageTransparency>
    <ImageURL>URL</ImageURL>
    <ImageVertAlignment>图像垂直对齐</ImageVertAlignment>
    <ImportRelations>导入关系</ImportRelations>
    <Increment>增量</Increment>
    <Indent>缩进</Indent>
    <Indicator>指示器</Indicator>
    <IndicatorValue>指示值</IndicatorValue>
    <IndividualColor>个人颜色</IndividualColor>
    <InitBy>初始化通过</InitBy>
    <Initials>缩写</Initials>
    <InitialSelection>初始选择</InitialSelection>
    <InitialSelectionSource>最初选择来源</InitialSelectionSource>
    <InitialValue>初始值</InitialValue>
    <InnerText>内部文本</InnerText>
    <Input>输入</Input>
    <Insert>插入</Insert>
    <Interaction>互动</Interaction>
    <InterlacedBrush>画笔行距</InterlacedBrush>
    <InterlacingHor>水平行距</InterlacingHor>
    <InterlacingHorBrush>隔行水平刷</InterlacingHorBrush>
    <InterlacingVert>垂直行距</InterlacingVert>
    <InterlacingVertBrush>隔行垂直刷</InterlacingVertBrush>
    <Interpolation>插值</Interpolation>
    <IsReversed>反向</IsReversed>
    <Italic>斜体</Italic>
    <Item>项目</Item>
    <ItemHeight>项目高度</ItemHeight>
    <Items>项目</Items>
    <KeepChildTogether>保持子区一起</KeepChildTogether>
    <KeepCrossTabTogether>保持跨表连接</KeepCrossTabTogether>
    <KeepDetails>保持细节</KeepDetails>
    <KeepDetailsTogether>保持明细在一起</KeepDetailsTogether>
    <KeepFooterTogether>保持页脚一起</KeepFooterTogether>
    <KeepGroupFooterTogether>保持分组尾在一起</KeepGroupFooterTogether>
    <KeepGroupHeaderTogether>保持分组头在一起</KeepGroupHeaderTogether>
    <KeepGroupTogether>保持分组一起</KeepGroupTogether>
    <KeepHeaderTogether>保持页眉一起</KeepHeaderTogether>
    <KeepMergedCellsTogether>将合并单元格保持在一起</KeepMergedCellsTogether>
    <KeepReportSummaryTogether>保持报表合计在一起</KeepReportSummaryTogether>
    <KeepSubReportTogether>保持子报表连接</KeepSubReportTogether>
    <Key>键值</Key>
    <KeyDataColumn>数据列key</KeyDataColumn>
    <KeyMeter>键</KeyMeter>
    <KeyMeters>键</KeyMeters>
    <Keys>密钥</Keys>
    <Label>标签</Label>
    <LabelColor>标签颜色</LabelColor>
    <LabelForeground>前景加标签</LabelForeground>
    <LabelRotationMode>旋转模式加标签</LabelRotationMode>
    <Labels>标签</Labels>
    <LabelsColor>标签颜色</LabelsColor>
    <LabelShadowForeground>前景加标签阴影</LabelShadowForeground>
    <LabelsOffset>Labels Offset</LabelsOffset>
    <Language>语言</Language>
    <LargeHeight>高度拉伸</LargeHeight>
    <LargeHeightFactor>高度拉伸倍数</LargeHeightFactor>
    <LaTexExpression>LaTex表达式</LaTexExpression>
    <Latitude>纬度</Latitude>
    <Layout>布局</Layout>
    <Left>左</Left>
    <LeftSide>左边</LeftSide>
    <Legend>图例</Legend>
    <LegendBorderColor>图例边框颜色</LegendBorderColor>
    <LegendBrush>图例画刷</LegendBrush>
    <LegendLabelsColor>图例标签颜色</LegendLabelsColor>
    <LegendTitleColor>图例标题颜色</LegendTitleColor>
    <LegendValueType>图例值类型</LegendValueType>
    <Length>长度</Length>
    <LengthUnderLabels>标签下的长度</LengthUnderLabels>
    <Lighting>加亮</Lighting>
    <LimitRows>限制行</LimitRows>
    <Linear>线性</Linear>
    <LinearBarBorderBrush>线形条边框刷</LinearBarBorderBrush>
    <LinearBarBrush>线形条刷</LinearBarBrush>
    <LinearBarEmptyBorderBrush>线形条空白边框刷 </LinearBarEmptyBorderBrush>
    <LinearBarEmptyBrush>线形条空白刷</LinearBarEmptyBrush>
    <LinearScaleBrush>线性范围</LinearScaleBrush>
    <LineColor>线颜色</LineColor>
    <LineColorNegative>负值线条颜色</LineColorNegative>
    <LineLength>行长</LineLength>
    <LineLimit>线界限</LineLimit>
    <LineMarker>标记线</LineMarker>
    <LinesOfUnderline>下划线</LinesOfUnderline>
    <LineSpacing>行间距</LineSpacing>
    <LineStyle>线样式</LineStyle>
    <LineWidth>线宽</LineWidth>
    <Linked>链接</Linked>
    <ListOfArguments>参数列表</ListOfArguments>
    <ListOfHyperlinks>超链接列表</ListOfHyperlinks>
    <ListOfTags>标记列表</ListOfTags>
    <ListOfToolTips>工具栏列表</ListOfToolTips>
    <ListOfValues>值列表</ListOfValues>
    <ListOfValuesClose>收盘值列表</ListOfValuesClose>
    <ListOfValuesEnd>成交值列表</ListOfValuesEnd>
    <ListOfValuesHigh>最高值列表</ListOfValuesHigh>
    <ListOfValuesLow>最低值列表</ListOfValuesLow>
    <ListOfValuesOpen>开盘值列表</ListOfValuesOpen>
    <ListOfWeights>加权列表</ListOfWeights>
    <Localizable>可定位</Localizable>
    <Location>位置</Location>
    <Locked>固定</Locked>
    <Logarithmic>对数的</Logarithmic>
    <LogarithmicScale>对数刻度</LogarithmicScale>
    <Logo>Logo</Logo>
    <Longitude>经度</Longitude>
    <Low>低</Low>
    <LowValues>低值</LowValues>
    <MajorInterval>主区间</MajorInterval>
    <MapAfrica>非洲</MapAfrica>
    <MapAsia>亚洲</MapAsia>
    <MapCustom>自定义</MapCustom>
    <MapEurope>欧洲</MapEurope>
    <MapID>地图ID</MapID>
    <MapNorthAmerica>北美洲</MapNorthAmerica>
    <MapOceania>大洋洲</MapOceania>
    <MapPopularMaps>热门地图</MapPopularMaps>
    <Maps>地图</Maps>
    <MapSouthAmerica>南美洲</MapSouthAmerica>
    <MapStyle>地图风格</MapStyle>
    <MapType>地图类型</MapType>
    <Margin>边缘</Margin>
    <Margins>页边距</Margins>
    <Marker>标记</Marker>
    <MarkerAlignment>对齐标注</MarkerAlignment>
    <MarkerAngle>标记角</MarkerAngle>
    <MarkerBorder>书签边框</MarkerBorder>
    <MarkerBrush>标记刷</MarkerBrush>
    <MarkerColor>标注颜色</MarkerColor>
    <MarkerSize>标注尺寸</MarkerSize>
    <MarkerType>标注类型</MarkerType>
    <MarkerVisible>标注可见</MarkerVisible>
    <MasterComponent>父级组件</MasterComponent>
    <MasterKeyDataColumn>数据列主键</MasterKeyDataColumn>
    <MatrixSize>矩阵大小</MatrixSize>
    <MaxDate>最大日期</MaxDate>
    <MaxDropDownItems>最大下拉项目</MaxDropDownItems>
    <MaxHeight>最大高度</MaxHeight>
    <Maximum>最大值</Maximum>
    <MaximumValue>最大值</MaximumValue>
    <MaxLength>最大长度</MaxLength>
    <MaxNumberOfLines>最大行号</MaxNumberOfLines>
    <MaxSize>最大尺寸</MaxSize>
    <MaxValue>最大值</MaxValue>
    <MaxWidth>最大宽度</MaxWidth>
    <MergeDuplicates>合并重复</MergeDuplicates>
    <MergeHeaders>合并列头</MergeHeaders>
    <Mid>中间</Mid>
    <MinDate>最小日期</MinDate>
    <MinHeight>最小高度</MinHeight>
    <Minimum>最小值</Minimum>
    <MinimumFontSize>最小字体尺寸</MinimumFontSize>
    <MinimumValue>最低值</MinimumValue>
    <MinorColor>Minor 颜色</MinorColor>
    <MinorCount>Minor 数量</MinorCount>
    <MinorInterval>次要间隔</MinorInterval>
    <MinorLength>Minor 长度</MinorLength>
    <MinorStyle>Minor 样式</MinorStyle>
    <MinorVisible>Minor 可见</MinorVisible>
    <MinRowsInColumn>栏内最少行数</MinRowsInColumn>
    <MinSize>最小尺寸</MinSize>
    <MinValue>最小值</MinValue>
    <MinWidth>最小宽度</MinWidth>
    <MirrorMargins>镜像边缘</MirrorMargins>
    <Mode>模式</Mode>
    <Module>模式</Module>
    <Move>移动</Move>
    <Multiline>多行</Multiline>
    <MultipleFactor>放大倍数</MultipleFactor>
    <MultipleInitialization>多次初始化</MultipleInitialization>
    <Name>名称</Name>
    <NameDataColumn>名称数据列</NameDataColumn>
    <NameInSource>命名源</NameInSource>
    <NameMeter>名称</NameMeter>
    <NameParent>父级名称</NameParent>
    <Namespaces>命名空间</Namespaces>
    <NeedleBorderBrush>针形边框刷</NeedleBorderBrush>
    <NeedleBorderWidth>针形边框宽度</NeedleBorderWidth>
    <NeedleBrush>针形刷</NeedleBrush>
    <NeedleCapBorderBrush>针帽刷</NeedleCapBorderBrush>
    <NeedleCapBrush>针帽刷</NeedleCapBrush>
    <Negative>负</Negative>
    <NegativeColor>负色</NegativeColor>
    <NegativeSeriesColors>负值系列颜色</NegativeSeriesColors>
    <NegativeTextBrush>负值文本画刷</NegativeTextBrush>
    <NestedLevel>嵌套层级</NestedLevel>
    <NewColumnAfter>在后插入新列</NewColumnAfter>
    <NewColumnBefore>在前插入新列</NewColumnBefore>
    <NewPageAfter>在后插入新页</NewPageAfter>
    <NewPageBefore>在前插入新面</NewPageBefore>
    <NextPage>后页</NextPage>
    <NoElements>无元素</NoElements>
    <NoIcon>无图标</NoIcon>
    <NullText>无效文本</NullText>
    <NumberOfColumns>栏数</NumberOfColumns>
    <NumberOfCopies>复制数</NumberOfCopies>
    <NumberOfPass>通道数</NumberOfPass>
    <NumberOfValues>值数量</NumberOfValues>
    <OddStyle>奇数行样式</OddStyle>
    <Offset>偏移</Offset>
    <OffsetAngle>偏移角度</OffsetAngle>
    <OnClick>点击</OnClick>
    <OnDataManipulation>数据处理中</OnDataManipulation>
    <OnePointZoom>单点缩放</OnePointZoom>
    <OnHover>停留</OnHover>
    <OnlyText>仅文本</OnlyText>
    <Opacity>不透明</Opacity>
    <OpenValues>打开值</OpenValues>
    <Operation>操作</Operation>
    <Options>选项</Options>
    <Options3D>3D选项</Options3D>
    <Orientation>定向</Orientation>
    <OthersText>其他文本</OthersText>
    <OverlappedColor>颜色重叠</OverlappedColor>
    <Padding>填充</Padding>
    <PageHeight>页高</PageHeight>
    <PageNumbers>页码</PageNumbers>
    <PageTurnTime>翻页时间</PageTurnTime>
    <PageWidth>页宽</PageWidth>
    <Paper>纸张</Paper>
    <PaperSize>页面大小</PaperSize>
    <PaperSourceFirstPage>首页纸张</PaperSourceFirstPage>
    <PaperSourceOfFirstPage>首页纸张来源</PaperSourceOfFirstPage>
    <PaperSourceOfOtherPages>其它页纸张来源</PaperSourceOfOtherPages>
    <PaperSourceOtherPages>其它页纸张</PaperSourceOtherPages>
    <Parameter>参数</Parameter>
    <Parameters>参数</Parameters>
    <ParametersDateFormat>参数日期格式</ParametersDateFormat>
    <ParametersOrientation>方向参数</ParametersOrientation>
    <ParameterWidth>宽度参数</ParameterWidth>
    <ParentColumns>父级栏</ParentColumns>
    <ParentSource>父级数据源</ParentSource>
    <ParentValue>父类值</ParentValue>
    <ParetoSeriesColors>帕累托颜色系列</ParetoSeriesColors>
    <PasswordChar>密码字符</PasswordChar>
    <Path>路径</Path>
    <PathData>路径数据</PathData>
    <PathSchema>路径图示</PathSchema>
    <Pattern>模式</Pattern>
    <PDFPreview>PDF预览</PDFPreview>
    <Placeholder>占位符</Placeholder>
    <Placement>放置</Placement>
    <PlaceOnToolbox>放置工具箱</PlaceOnToolbox>
    <PointAtCenter>指向中心</PointAtCenter>
    <Position>位置</Position>
    <Positive>正</Positive>
    <PositiveColor>正色</PositiveColor>
    <PreferredColumnWidth>首选列宽</PreferredColumnWidth>
    <PreferredRowHeight>首选行高</PreferredRowHeight>
    <PreventIntersection>防止重叠</PreventIntersection>
    <PreviewMode>预览模式</PreviewMode>
    <PreviewSettings>预览设置</PreviewSettings>
    <Printable>可打印</Printable>
    <PrintAtBottom>在底部打印</PrintAtBottom>
    <PrinterName>打印机名称</PrinterName>
    <PrinterSettings>打印机设置</PrinterSettings>
    <PrintHeadersFootersFromPreviousPage>从前页打印页眉和页脚</PrintHeadersFootersFromPreviousPage>
    <PrintIfDetailEmpty>明细为空时打印</PrintIfDetailEmpty>
    <PrintIfEmpty>为空时打印</PrintIfEmpty>
    <PrintIfParentDisabled>父级禁用时打印</PrintIfParentDisabled>
    <PrintOn>打印在</PrintOn>
    <PrintOnAllPages>打印全部页</PrintOnAllPages>
    <PrintOnEvenOddPages>打印偶数奇数页</PrintOnEvenOddPages>
    <PrintOnPreviousPage>打印前页</PrintOnPreviousPage>
    <PrintTitleOnAllPages>在所有页面打印标题</PrintTitleOnAllPages>
    <PrintVerticalBars>打印垂直条</PrintVerticalBars>
    <ProcessAt>过程</ProcessAt>
    <ProcessAtEnd>在结束时处理</ProcessAtEnd>
    <ProcessingDuplicates>正在处理重复</ProcessingDuplicates>
    <ProcessTilde>工艺波浪</ProcessTilde>
    <ProductHomePage>产品主页</ProductHomePage>
    <Property>属性</Property>
    <Provider>事件提供程序</Provider>
    <RadarStyle>雷达样式</RadarStyle>
    <RadialBarBorderBrush>径向栏边框刷</RadialBarBorderBrush>
    <RadialBarBrush>径向栏画刷</RadialBarBrush>
    <RadialBarEmptyBorderBrush>径向栏空白边框刷</RadialBarEmptyBorderBrush>
    <RadialBarEmptyBrush>径向栏空白刷</RadialBarEmptyBrush>
    <Radius>半径</Radius>
    <RadiusMode>半径模式</RadiusMode>
    <Range>范围</Range>
    <RangeColorMode>范围内颜色模式</RangeColorMode>
    <RangeFrom>来自</RangeFrom>
    <RangeMode>范围模式</RangeMode>
    <RangeScrollEnabled>范围滚动启用</RangeScrollEnabled>
    <RangeTo>到</RangeTo>
    <RangeType>范围类型</RangeType>
    <Ratio>比率</Ratio>
    <RatioY>Y比例</RatioY>
    <ReadOnly>只读</ReadOnly>
    <Reason>原因</Reason>
    <RecentFonts>最近使用的字体</RecentFonts>
    <ReconnectOnEachRow>每行重新连接</ReconnectOnEachRow>
    <ReferencedAssemblies>引用程序集</ReferencedAssemblies>
    <Refresh>刷新</Refresh>
    <RefreshTime>刷新时间</RefreshTime>
    <Regular>常规</Regular>
    <Relation>关系</Relation>
    <RelationName>关系名称</RelationName>
    <Relations>关系</Relations>
    <RelativeHeight>相对高度</RelativeHeight>
    <RelativeWidth>相对宽度</RelativeWidth>
    <RemoveUnusedDataBeforeStart>在开始前移除未使用数据</RemoveUnusedDataBeforeStart>
    <RenderTo>渲染到</RenderTo>
    <ReportAlias>报表别名</ReportAlias>
    <ReportAuthor>报表作者</ReportAuthor>
    <ReportCacheMode>报表缓存模式</ReportCacheMode>
    <ReportDescription>报表描述</ReportDescription>
    <ReportIcon>报告图标</ReportIcon>
    <ReportImage>报表图像</ReportImage>
    <ReportName>报表名称</ReportName>
    <ReportUnit>报表单位</ReportUnit>
    <RequestFromUser>要求用户输入</RequestFromUser>
    <RequestParameters>参数要求</RequestParameters>
    <Required>必须</Required>
    <ResetAllFilters>重置过滤</ResetAllFilters>
    <ResetDataSource>重设数据源</ResetDataSource>
    <ResetPageNumber>重设页码</ResetPageNumber>
    <Resize>调整大小</Resize>
    <Resource>资源</Resource>
    <Resources>资源</Resources>
    <Restrictions>限制</Restrictions>
    <RetrieveOnlyUsedData>仅检索已用数据</RetrieveOnlyUsedData>
    <ReturnValue>返回值</ReturnValue>
    <ReverseHor>水平翻转</ReverseHor>
    <ReverseVert>垂直翻转</ReverseVert>
    <Right>右</Right>
    <RightSide>右边</RightSide>
    <RightToLeft>从右到左</RightToLeft>
    <Rotation>旋转</Rotation>
    <RotationLabels>旋转标签</RotationLabels>
    <RotationMode>旋转模式</RotationMode>
    <Round>环绕</Round>
    <RoundValues>圆值</RoundValues>
    <RowCount>行数</RowCount>
    <RowHeaderBackColor>行标背景色</RowHeaderBackColor>
    <RowHeaderForeColor>行标前景色</RowHeaderForeColor>
    <RowHeadersVisible>行标题可见</RowHeadersVisible>
    <RowHeaderWidth>行标题宽度</RowHeaderWidth>
    <Rows>行</Rows>
    <RowsPerPage>每页行数</RowsPerPage>
    <Scale>缩放</Scale>
    <ScaleHor>水平缩放</ScaleHor>
    <ScriptLanguage>脚本语言</ScriptLanguage>
    <ScrollTime>滚动时间</ScrollTime>
    <Second>第二</Second>
    <SegmentPerHeight>纵向连页</SegmentPerHeight>
    <SegmentPerWidth>横向连页</SegmentPerWidth>
    <SelectedBackColor>选定背景色</SelectedBackColor>
    <SelectedCellBackColor>选定单元格背景色</SelectedCellBackColor>
    <SelectedCellForeColor>选定单元格前景色</SelectedCellForeColor>
    <SelectedDataColor>选定数据颜色</SelectedDataColor>
    <SelectedDataForeground>选定数据前景</SelectedDataForeground>
    <SelectedForeColor>选定前景色</SelectedForeColor>
    <SelectedGlyphColor>选定字形色</SelectedGlyphColor>
    <SelectedIndex>选择索引</SelectedIndex>
    <SelectedItem>选择项目</SelectedItem>
    <SelectedKey>所选键</SelectedKey>
    <SelectedValue>选择值</SelectedValue>
    <Selection>选择</Selection>
    <SelectionBackColor>选择背景色</SelectionBackColor>
    <SelectionEnabled>选择启用</SelectionEnabled>
    <SelectionForeColor>选择前景色</SelectionForeColor>
    <SelectionMode>选择模式</SelectionMode>
    <SelectionType>选择类型</SelectionType>
    <SelectStyle>选择样式</SelectStyle>
    <SendType>发送类型</SendType>
    <SeparatorColor>分隔符颜色</SeparatorColor>
    <SerialNumber>序列号</SerialNumber>
    <Series>序列</Series>
    <SeriesBorderColor>系列边框颜色</SeriesBorderColor>
    <SeriesBorderThickness>边框厚度</SeriesBorderThickness>
    <SeriesColors>颜色系列</SeriesColors>
    <SeriesCornerRadius>角半径</SeriesCornerRadius>
    <SeriesLabels>序列标签</SeriesLabels>
    <SeriesLabelsBorderColor>标签边框颜色系列</SeriesLabelsBorderColor>
    <SeriesLabelsBrush>标签画刷系列</SeriesLabelsBrush>
    <SeriesLabelsColor>标签颜色系列</SeriesLabelsColor>
    <SeriesLabelsLineColor>标签线颜色</SeriesLabelsLineColor>
    <SeriesLighting>系列高亮</SeriesLighting>
    <SeriesShowBorder>系列展示边框</SeriesShowBorder>
    <SeriesShowShadow>阴影展示系列</SeriesShowShadow>
    <SeriesTitle>标题系列</SeriesTitle>
    <Shadow>阴影</Shadow>
    <ShadowBrush>阴影刷</ShadowBrush>
    <ShadowColor>阴影颜色</ShadowColor>
    <ShadowSize>阴影尺寸</ShadowSize>
    <ShapeType>形状类型</ShapeType>
    <Shift>Shift</Shift>
    <ShiftMode>Shift 模式</ShiftMode>
    <ShortName>简称</ShortName>
    <ShortValue>简值</ShortValue>
    <ShowAllValue>显示所有值</ShowAllValue>
    <ShowBehind>显示于后面</ShowBehind>
    <ShowBlanks>显示空白</ShowBlanks>
    <ShowBubble>显示气泡</ShowBubble>
    <ShowDialog>显示对话框</ShowDialog>
    <ShowEdgeValues>显示边框值</ShowEdgeValues>
    <ShowHyperlink>显示超链接</ShowHyperlink>
    <ShowImageBehind>显示图片于后面</ShowImageBehind>
    <ShowInLegend>显示图例</ShowInLegend>
    <ShowInnerPoints>显示内部点</ShowInnerPoints>
    <ShowInPercent>显示为百分比</ShowInPercent>
    <ShowLabel>显示标签</ShowLabel>
    <ShowLabels>显示标签</ShowLabels>
    <ShowLabelText>显示标签文本</ShowLabelText>
    <ShowLegend>显示图例</ShowLegend>
    <ShowMarker>显示标注</ShowMarker>
    <ShowMeanMarkers>显示平均标记</ShowMeanMarkers>
    <ShowNulls>显示空值</ShowNulls>
    <ShowOnZeroValues>显示零值</ShowOnZeroValues>
    <ShowOthers>显示其他</ShowOthers>
    <ShowPercents>显示百分比</ShowPercents>
    <ShowPlacemark>显示Show Placemark</ShowPlacemark>
    <ShowQuietZoneIndicator>显示空白区指示符</ShowQuietZoneIndicator>
    <ShowQuietZones>显示空白区</ShowQuietZones>
    <ShowScrollBar>显示滚动条</ShowScrollBar>
    <ShowSelectAll>显示全选</ShowSelectAll>
    <ShowSeriesLabels>显示系列标签</ShowSeriesLabels>
    <ShowShadow>显示阴影</ShowShadow>
    <ShowTotal>显示合计</ShowTotal>
    <ShowTotalSummary>显示Total Summary</ShowTotalSummary>
    <ShowUpDown>显示升降</ShowUpDown>
    <ShowValue>显示值</ShowValue>
    <ShowXAxis>显示横坐标</ShowXAxis>
    <ShowYAxis>显示纵坐标</ShowYAxis>
    <ShowZeros>显示零</ShowZeros>
    <ShrinkFontToFit>压缩</ShrinkFontToFit>
    <ShrinkFontToFitMinimumSize>压缩为最小尺寸</ShrinkFontToFitMinimumSize>
    <Side>边</Side>
    <SideBySide>并排</SideBySide>
    <Sides>边</Sides>
    <SignatureType>类型</SignatureType>
    <Simple>简单</Simple>
    <Size>尺寸</Size>
    <SizeMode>尺寸模式</SizeMode>
    <Skin>皮肤</Skin>
    <SkipFirst>忽略第一个</SkipFirst>
    <SkipIndices>跳过索引</SkipIndices>
    <SkipIndicesObj>跳过索引</SkipIndicesObj>
    <SkipMajorValues>跳过主要值</SkipMajorValues>
    <SkipValues>跳过值</SkipValues>
    <SkipValuesObj>跳过值</SkipValuesObj>
    <Smoothing>平滑</Smoothing>
    <Sort>排序</Sort>
    <SortBy>排序为</SortBy>
    <SortDirection>排序方向</SortDirection>
    <Sorted>已排序</Sorted>
    <SortingColumn>列排序</SortingColumn>
    <SortingEnabled>可排序</SortingEnabled>
    <SortLetters>A-Z</SortLetters>
    <SortType>排序类型</SortType>
    <Space>空格</Space>
    <SpaceRatio>空间比</SpaceRatio>
    <Spacing>间隔</Spacing>
    <SqlCommand>Sql命令</SqlCommand>
    <StartAngle>开始角度</StartAngle>
    <StartCap>起始端</StartCap>
    <StartColor>开始颜色</StartColor>
    <StartFromZero>从零开始</StartFromZero>
    <StartMode>开始模式</StartMode>
    <StartNewPage>开始新页</StartNewPage>
    <StartNewPageIfLessThan>开始新页如果少于</StartNewPageIfLessThan>
    <StartPosition>起始位置</StartPosition>
    <StartValue>起始值</StartValue>
    <StartWidth>起始宽度</StartWidth>
    <State>国家</State>
    <Step>步骤</Step>
    <Stop>停止</Stop>
    <StopBeforePage>停止于页面</StopBeforePage>
    <StopBeforePrint>停止打印于</StopBeforePrint>
    <StoreImagesInResources>将图像存储在资源</StoreImagesInResources>
    <StreetAddress>街道地址</StreetAddress>
    <Stretch>拉伸</Stretch>
    <StretchToPrintArea>拉伸至打印区域</StretchToPrintArea>
    <Strikeout>删除线</Strikeout>
    <StripBrush>长条刷</StripBrush>
    <Strips>长条</Strips>
    <Stroke>笔画</Stroke>
    <StructuredAppendPosition>结构化附加位置</StructuredAppendPosition>
    <StructuredAppendTotal>结构化附加所有</StructuredAppendTotal>
    <Style>样式</Style>
    <StyleColors>样式颜色</StyleColors>
    <Styles>样式</Styles>
    <SubmissionMessage>提交消息</SubmissionMessage>
    <SubReportPage>子报表页面</SubReportPage>
    <Summaries>总结</Summaries>
    <Summary>聚合</Summary>
    <SummaryAlignment>摘要对齐</SummaryAlignment>
    <SummaryExpression>统计表达式</SummaryExpression>
    <SummarySortDirection>统计排序方向</SummarySortDirection>
    <SummaryType>统计类型</SummaryType>
    <SummaryValues>合计值</SummaryValues>
    <SupplementCode>附加码</SupplementCode>
    <SupplementType>附加码类型</SupplementType>
    <SweepAngle>扫略角度</SweepAngle>
    <SystemFonts>系统字体</SystemFonts>
    <SystemVariable>系统变量</SystemVariable>
    <SystemVariables>系统变量</SystemVariables>
    <Table>表</Table>
    <Tag>标识</Tag>
    <TagDataColumn>标签数据列</TagDataColumn>
    <TagValue>标识值</TagValue>
    <Target>目标</Target>
    <TargetColor>目标颜色</TargetColor>
    <TargetFormat>目标格式</TargetFormat>
    <TargetIcon>目标图标</TargetIcon>
    <TargetMode>靶模型</TargetMode>
    <TargetSettings>目标</TargetSettings>
    <Tension>拉伸</Tension>
    <Text>文本</Text>
    <TextAfter>文本于后</TextAfter>
    <TextAlign>对齐文本</TextAlign>
    <TextAlignment>文本对齐</TextAlignment>
    <TextAngle>文字角度</TextAngle>
    <TextBefore>文本于前</TextBefore>
    <TextBrush>文本刷</TextBrush>
    <TextColor>文本颜色</TextColor>
    <TextEnabled>文本已启用</TextEnabled>
    <TextFont>文本字体</TextFont>
    <TextFormat>文本格式</TextFormat>
    <TextOnly>仅文本</TextOnly>
    <TextOptions>文本选项</TextOptions>
    <TextQuality>文本质量</TextQuality>
    <TextRightToLeft>文本从右到左</TextRightToLeft>
    <TextShowBehind>文本显示在后面</TextShowBehind>
    <Third>第三</Third>
    <TickLabelMajorFont>刻度标签主要字体</TickLabelMajorFont>
    <TickLabelMajorTextBrush>刻度标签主要文本刷</TickLabelMajorTextBrush>
    <TickLabelMinorFont>刻度标签次要字体</TickLabelMinorFont>
    <TickLabelMinorTextBrush>刻度标签次要文本刷</TickLabelMinorTextBrush>
    <TickMarkMajorBorder>刻度线主要边框</TickMarkMajorBorder>
    <TickMarkMajorBorderWidth>刻度线主要边框宽度</TickMarkMajorBorderWidth>
    <TickMarkMajorBrush>刻度线主要画刷</TickMarkMajorBrush>
    <TickMarkMinorBorder>刻度线次要边框</TickMarkMinorBorder>
    <TickMarkMinorBorderWidth>刻度线次要边框宽度</TickMarkMinorBorderWidth>
    <TickMarkMinorBrush>刻度线次要画刷</TickMarkMinorBrush>
    <Ticks>记号</Ticks>
    <Title>标题</Title>
    <TitleBeforeHeader>标题在页眉前</TitleBeforeHeader>
    <TitleColor>标题颜色</TitleColor>
    <TitleDirection>题目方向</TitleDirection>
    <TitleFont>标题字体</TitleFont>
    <TitleVisible>可见</TitleVisible>
    <To>到</To>
    <Today>当天</Today>
    <ToolTip>提示信息</ToolTip>
    <ToolTipBorder>工具提示边框</ToolTipBorder>
    <ToolTipBrush>工具提示刷</ToolTipBrush>
    <ToolTipCornerRadius>工具提示圆角</ToolTipCornerRadius>
    <ToolTipDataColumn>工具提示数据列</ToolTipDataColumn>
    <ToolTipTextBrush>工具提示文本刷</ToolTipTextBrush>
    <Top>顶端</Top>
    <TopLeft>左上</TopLeft>
    <Topmost>顶端的</Topmost>
    <TopmostLine>顶端线</TopmostLine>
    <TopN>Top N</TopN>
    <TopRight>右上</TopRight>
    <TopSide>顶部</TopSide>
    <Total>总计</Total>
    <TotalCellColumnBackColor>所有单元列背景颜色</TotalCellColumnBackColor>
    <TotalCellColumnForeColor>所有单元列前景颜色</TotalCellColumnForeColor>
    <TotalCellRowBackColor>所有单元行背景颜色</TotalCellRowBackColor>
    <TotalCellRowForeColor>所有单元行前景颜色</TotalCellRowForeColor>
    <TotalLabel>标签</TotalLabel>
    <Totals>总计</Totals>
    <TrackColor>轨迹颜色</TrackColor>
    <TransparentColor>透明色</TransparentColor>
    <TrendLine>趋势线</TrendLine>
    <TrendLineColor>趋线颜色</TrendLineColor>
    <TrendLines>趋势线</TrendLines>
    <TrendLineShowShadow>趋线显示阴影</TrendLineShowShadow>
    <TrimExcessData>清理多余数据</TrimExcessData>
    <Trimming>截断</Trimming>
    <Type>类型</Type>
    <TypeName>类型名称</TypeName>
    <Types>类型</Types>
    <UncheckedIcon>未选中图标</UncheckedIcon>
    <Underline>下划线</Underline>
    <UndoLimit>撤销限制</UndoLimit>
    <Unit>单位</Unit>
    <UnitAlignment>单元对齐</UnitAlignment>
    <UnlimitedBreakable>不限制分页</UnlimitedBreakable>
    <UnlimitedHeight>不限制高度</UnlimitedHeight>
    <UnlimitedWidth>不限制宽度</UnlimitedWidth>
    <UpperMarks>上标记</UpperMarks>
    <URL>URL</URL>
    <UseAliases>使用别名</UseAliases>
    <UseExternalReport>使用外部报表</UseExternalReport>
    <UseParentStyles>使用父级样式</UseParentStyles>
    <UseRangeColor>使用范围颜色</UseRangeColor>
    <UseRectangularSymbols>使用矩形符号</UseRectangularSymbols>
    <UseSeriesColor>使用序列颜色</UseSeriesColor>
    <UseStyleOfSummaryInColumnTotal>显示列汇总</UseStyleOfSummaryInColumnTotal>
    <UseStyleOfSummaryInRowTotal>显示行汇总</UseStyleOfSummaryInRowTotal>
    <UseUserPicture>使用用户图片</UseUserPicture>
    <UseValuesFromTheSpecifiedRange>使用指定范围内的值</UseValuesFromTheSpecifiedRange>
    <Value>值</Value>
    <ValueClose>收盘值</ValueClose>
    <ValueDataColumn>值数据列</ValueDataColumn>
    <ValueDataColumnClose>收盘值数据列</ValueDataColumnClose>
    <ValueDataColumnEnd>成交值数据列</ValueDataColumnEnd>
    <ValueDataColumnHigh>最高值数据列</ValueDataColumnHigh>
    <ValueDataColumnLow>最低值数据列</ValueDataColumnLow>
    <ValueDataColumnOpen>开盘值数据列值</ValueDataColumnOpen>
    <ValueEnd>成交值</ValueEnd>
    <ValueFormat>值的格式</ValueFormat>
    <ValueHigh>最高值</ValueHigh>
    <ValueLow>最低值</ValueLow>
    <ValueMeter>值</ValueMeter>
    <ValueOpen>开盘值</ValueOpen>
    <Values>值</Values>
    <ValueType>值类型</ValueType>
    <ValueTypeSeparator>值类型分隔符</ValueTypeSeparator>
    <Variable>变量</Variable>
    <Variables>变量</Variables>
    <Variation>变化</Variation>
    <Version>版本</Version>
    <VertAlignment>垂直位置</VertAlignment>
    <VertSpacing>垂直间距</VertSpacing>
    <ViewMode>查看模式</ViewMode>
    <Visibility>透明度</Visibility>
    <Visible>可见</Visible>
    <VisualStates>视觉状态</VisualStates>
    <Watermark>水印</Watermark>
    <WatermarkStyle>水印样式</WatermarkStyle>
    <WeaveAngle>Weave角度</WeaveAngle>
    <WeaveDistance>Weave距离</WeaveDistance>
    <WeaveEnabled>已启用Weave</WeaveEnabled>
    <WeaveMajorColor>Weave 主要颜色</WeaveMajorColor>
    <WeaveMajorIcon>Weave 主图标</WeaveMajorIcon>
    <WeaveMajorSize>Weave 主要尺寸</WeaveMajorSize>
    <WeaveMinorColor>Weave 次要颜色</WeaveMinorColor>
    <WeaveMinorIcon>Weave 次要图标</WeaveMinorIcon>
    <WeaveMinorSize>Weave 次要尺寸</WeaveMinorSize>
    <Weight>加权</Weight>
    <WeightDataColumn>加权数据列</WeightDataColumn>
    <Weights>权重</Weights>
    <Width>宽度</Width>
    <WindowState>窗口状态</WindowState>
    <WordWrap>自动换行</WordWrap>
    <Wrap>换行</Wrap>
    <WrapGap>行距</WrapGap>
    <WrapLine>经位置线</WrapLine>
    <XAxis>X 坐标</XAxis>
    <XTopAxis>X 上坐标</XTopAxis>
    <YAxis>Y 坐标</YAxis>
    <YRightAxis>Y 右坐标</YRightAxis>
    <ZeroColor>Zero颜色</ZeroColor>
    <Zoom>缩放</Zoom>
  </PropertyMain>
  <PropertySystemColors>
    <ActiveBorder>活动边框</ActiveBorder>
    <ActiveCaption>活动标题栏</ActiveCaption>
    <ActiveCaptionText>活动标题栏文本</ActiveCaptionText>
    <AppWorkspace>应用程序工作区</AppWorkspace>
    <Control>按钮表面</Control>
    <ControlDark>按钮亮阴影</ControlDark>
    <ControlDarkDark>按钮暗阴影</ControlDarkDark>
    <ControlLight>按钮阴影</ControlLight>
    <ControlLightLight>按钮突出显示</ControlLightLight>
    <ControlText>按钮文本</ControlText>
    <Desktop>桌面</Desktop>
    <GrayText>无效文本</GrayText>
    <Highlight>突出显示</Highlight>
    <HighlightText>突出显示文本</HighlightText>
    <HotTrack>热跟踪</HotTrack>
    <InactiveBorder>非活动边框</InactiveBorder>
    <InactiveCaption>非活动标题栏</InactiveCaption>
    <InactiveCaptionText>非活动标题栏文本</InactiveCaptionText>
    <Info>工具提示</Info>
    <InfoText>工具提示文本</InfoText>
    <Menu>菜单条</Menu>
    <MenuText>菜单文本</MenuText>
    <ScrollBar>滚动条</ScrollBar>
    <Window>窗口背景</Window>
    <WindowFrame>窗口框架</WindowFrame>
    <WindowText>窗口文本</WindowText>
  </PropertySystemColors>
  <QueryBuilder>
    <AddObject>增加对象</AddObject>
    <AddSubQuery>增加派生表</AddSubQuery>
    <AllObjects>(所有对象)</AllObjects>
    <BadFromObjectExpression>无效的 FROM 表达式!</BadFromObjectExpression>
    <BadObjectName>无效对象名!</BadObjectName>
    <BadSelectStatement>无效 SELECT 语句!</BadSelectStatement>
    <Collections>集合</Collections>
    <CreateLinksFromForeignKeys>通过外键创建链接</CreateLinksFromForeignKeys>
    <CriteriaAlias>别名</CriteriaAlias>
    <CriteriaCriteria>过滤</CriteriaCriteria>
    <CriteriaExpression>表达式</CriteriaExpression>
    <CriteriaGroupBy>分组</CriteriaGroupBy>
    <CriteriaOr>或...</CriteriaOr>
    <CriteriaOutput>输出</CriteriaOutput>
    <CriteriaSortOrder>排序</CriteriaSortOrder>
    <CriteriaSortType>排序方式</CriteriaSortType>
    <Database>数据库</Database>
    <DataSourceProperties>数据源属性</DataSourceProperties>
    <DialectDontSupportDatabases>服务器不支持从不同数据库查询对象.</DialectDontSupportDatabases>
    <DialectDontSupportSchemas>服务器不支持 schemas.</DialectDontSupportSchemas>
    <DialectDontSupportUnions>服务器不支持联合查询.</DialectDontSupportUnions>
    <DialectDontSupportUnionsBrackets>服务器不支持在联合查询中使用括号.</DialectDontSupportUnionsBrackets>
    <DialectDontSupportUnionsBracketsInSubQuery>服务器不支持子查询.</DialectDontSupportUnionsBracketsInSubQuery>
    <DialectDontSupportUnionsInSubQueries>服务器不支持在子查询中使用 unions.</DialectDontSupportUnionsInSubQueries>
    <Edit>编辑</Edit>
    <EncloseWithBrackets>使用括号</EncloseWithBrackets>
    <Expressions>表达式</Expressions>
    <InsertEmptyItem>插入空项</InsertEmptyItem>
    <JoinExpression>Join 表达式</JoinExpression>
    <LabelAlias>别名:</LabelAlias>
    <LabelFilterObjectsBySchemaName>用 Schema 名过滤对象:</LabelFilterObjectsBySchemaName>
    <LabelJoinExpression>Join 表达式n:</LabelJoinExpression>
    <LabelLeftColumn>左边列:</LabelLeftColumn>
    <LabelLeftObject>左边对象:</LabelLeftObject>
    <LabelObject>对象:</LabelObject>
    <LabelRightColumn>右边列:</LabelRightColumn>
    <LabelRightObject>右边对象:</LabelRightObject>
    <LinkProperties>链接属性</LinkProperties>
    <MetadataProviderCantExecSQL>所使用的元数据提供者不能执行SQL查询.</MetadataProviderCantExecSQL>
    <MetaProviderCantLoadMetadata>所使用的元数据提供者不能自动导入元数据.</MetaProviderCantLoadMetadata>
    <MetaProviderCantLoadMetadataForDatabase>所使用的元数据提供者不能自动从数据库导入元数据: {0}</MetaProviderCantLoadMetadataForDatabase>
    <MoveDown>下移</MoveDown>
    <MoveUp>上移</MoveUp>
    <NewUnionSubQuery>新建 union 子查询</NewUnionSubQuery>
    <NoConnectionObject>无连接对象 (属性 {0} 未分配).</NoConnectionObject>
    <NoTransactionObject>无事务对象 (属性 {0} 未分配).</NoTransactionObject>
    <Objects>对象</Objects>
    <ProcedureParameters>存储过程参数</ProcedureParameters>
    <Procedures>存储过程</Procedures>
    <qnSaveChanges>您是否确定保存查询改变?</qnSaveChanges>
    <Query>查询</Query>
    <QueryBuilder>查询构造器</QueryBuilder>
    <QueryParameters>查询参数</QueryParameters>
    <QueryProperties>查询属性</QueryProperties>
    <Remove>移除</Remove>
    <RemoveBrackets>移除括号</RemoveBrackets>
    <RunQueryBuilder>运行查询构造器</RunQueryBuilder>
    <SelectAllFromLeft>左边所有数据</SelectAllFromLeft>
    <SelectAllFromRight>右边所有数据</SelectAllFromRight>
    <SwitchToDerivedTable>切换到派生表</SwitchToDerivedTable>
    <Tables>表</Tables>
    <UnexpectedTokenAt>未知错误 "{0}" 在行 {1}, 列 {2}!</UnexpectedTokenAt>
    <Unions>联合查询</Unions>
    <UnionSubMenu>联合查询</UnionSubMenu>
    <ViewQuery>视图查询</ViewQuery>
    <Views>视图</Views>
  </QueryBuilder>
  <Questions>
    <qnConfiguration>请为属性面板选择配置的类型. 所选配置的类型依赖于课件属性的个数和它们对报表开发人员的复杂性.您可以通过属性面板的上下文菜单更改配置类型.</qnConfiguration>
    <qnCopyCategory>是否要复制此类别的所有变量</qnCopyCategory>
    <qnDictionaryNew>您是否想要创建新的字典?</qnDictionaryNew>
    <qnLanguageNew>您已经改变报表语言。这将导致新的报表代码生成。 您是否确定要保存当前语言?</qnLanguageNew>
    <qnPageDelete>您是否想要删除页面?</qnPageDelete>
    <qnRemove>您是否想要移除?</qnRemove>
    <qnRemoveService>您是否想要移除服务?</qnRemoveService>
    <qnRemoveServiceCategory>您是否想要移除分类?</qnRemoveServiceCategory>
    <qnRemoveUnused>您是否想要移除未使用的项目?</qnRemoveUnused>
    <qnReplace>是否要替换现有项{0}?</qnReplace>
    <qnRestoreDefault>恢复默认?</qnRestoreDefault>
    <qnSaveChanges>保存改变在{0}?</qnSaveChanges>
    <qnSaveChangesToPreviewPage>您是否要保存页面改变?</qnSaveChangesToPreviewPage>
    <qnSynchronize>是否同步数据存储和数据字典的内容?</qnSynchronize>
    <qnSynchronizeServices>是否同步服务?</qnSynchronizeServices>
  </Questions>
  <RecentFiles>
    <AddAPlace>添加位置</AddAPlace>
    <AddAPlaceDesc>您可以添加位置以便更容易地将Reports保存到云中</AddAPlaceDesc>
    <ChangeFileType>更改文件类型</ChangeFileType>
    <ChooseALocation>选择位置</ChooseALocation>
    <ClearUnpinnedItems>清除未固定项目</ClearUnpinnedItems>
    <CopyPathToClipboard>复制路径到剪贴板</CopyPathToClipboard>
    <DateModified>修改日期</DateModified>
    <Documents>文档</Documents>
    <DoYouWantTo>您想要:</DoYouWantTo>
    <LastWeek>上周</LastWeek>
    <LocalDisk>本地磁盘({0})</LocalDisk>
    <MessageInvalidFolderName>无效的文件夹名称</MessageInvalidFolderName>
    <MessageThatNameAlreadyExists>该名称已经存在了，请使用不同的名称</MessageThatNameAlreadyExists>
    <MoreLocations>更多位置→</MoreLocations>
    <Older>旧的</Older>
    <OtherLocations>其它位置</OtherLocations>
    <Personal>个人的</Personal>
    <Pinned>已固定的</Pinned>
    <PinToList>固定要列表</PinToList>
    <PinToRecentList>固定到近期列表</PinToRecentList>
    <RemoveFromList>从列表中删除</RemoveFromList>
    <ReplaceExistingFile>替换现有的文件</ReplaceExistingFile>
    <SaveChangesWithADifferentName>使用其他名称保存更改</SaveChangesWithADifferentName>
    <SaveThisFile>保存此文件</SaveThisFile>
    <SelectToNavigateUpOneLevel>选择向上一层</SelectToNavigateUpOneLevel>
    <SetAsDefaultLocation>设置为默认位置</SetAsDefaultLocation>
    <ThisPC>此电脑</ThisPC>
    <UnpinFromList>从列表中取消固定</UnpinFromList>
  </RecentFiles>
  <Report>
    <ActiveRelation>激活关系</ActiveRelation>
    <Address>地址</Address>
    <Alphabetical>按字母顺序</Alphabetical>
    <Bands>栏</Bands>
    <Basic>基本</Basic>
    <BasicConfiguration>对象属性的最小数字, 是渲染基本报告类型所需要的.</BasicConfiguration>
    <BusinessObjects>Business Objects</BusinessObjects>
    <Categorized>分类</Categorized>
    <Charts>图表</Charts>
    <Checking>检查...</Checking>
    <ClickForMoreDetails>单击获得详细资料</ClickForMoreDetails>
    <CollapseAll>全部折叠</CollapseAll>
    <Collection>集合</Collection>
    <CompilationAccess>编译访问</CompilationAccess>
    <CompilingReport>正在编译报表</CompilingReport>
    <Complete>完成</Complete>
    <Components>组件</Components>
    <ConnectingToData>正在连接数据</ConnectingToData>
    <CopyOf>复制</CopyOf>
    <CreateNewReportPageForm>创建一个新报表，添加页面，添加窗体</CreateNewReportPageForm>
    <CreatingReport>正在创建报表</CreatingReport>
    <CrossBands>交叉栏</CrossBands>
    <Dialogs>报表控件</Dialogs>
    <EditStyles>编辑样式</EditStyles>
    <Enhancements>增强</Enhancements>
    <EnterDataManually>手动输入数据</EnterDataManually>
    <Errors>错误</Errors>
    <EventsTab>事件表</EventsTab>
    <ExpandAll>全部展开</ExpandAll>
    <FilterAnd>并且</FilterAnd>
    <FilterOr>或</FilterOr>
    <FinishingReport>正在完成报表</FinishingReport>
    <FirstPass>第一通道</FirstPass>
    <FixedBugs>修复错误</FixedBugs>
    <FromCurrentState>从当前状态</FromCurrentState>
    <FromDefaultState>从默认状态</FromDefaultState>
    <Gallery>图库</Gallery>
    <GenerateNewCode>生成新代码</GenerateNewCode>
    <History>历史记录</History>
    <Infographics>信息图</Infographics>
    <InfoMessage>{0} - {1} 查找.</InfoMessage>
    <InformationMessages>信息类消息</InformationMessages>
    <LabelAlias>别名:</LabelAlias>
    <LabelAuthor>作者:</LabelAuthor>
    <LabelBackground>背景:</LabelBackground>
    <LabelCategory>分类:</LabelCategory>
    <LabelCentimeters>厘米:</LabelCentimeters>
    <LabelCollectionName>集合名称:</LabelCollectionName>
    <LabelColor>颜色:</LabelColor>
    <LabelCountData>行数:</LabelCountData>
    <LabelDataBand>数据绑定:</LabelDataBand>
    <LabelDataColumn>数据列:</LabelDataColumn>
    <LabelDefaultValue>默认值:</LabelDefaultValue>
    <LabelExpression>表达式:</LabelExpression>
    <LabelFactorLevel>嵌套系数:</LabelFactorLevel>
    <LabelFontName>字体名称:</LabelFontName>
    <LabelFunction>函数:</LabelFunction>
    <LabelHundredthsOfInch>百分之一英寸:</LabelHundredthsOfInch>
    <LabelInches>英寸:</LabelInches>
    <LabelMillimeters>毫米:</LabelMillimeters>
    <LabelName>名称:</LabelName>
    <LabelNameInSource>命名源:</LabelNameInSource>
    <LabelNestedLevel>嵌套等级:</LabelNestedLevel>
    <LabelPassword>密码:</LabelPassword>
    <LabelPixels>像素:</LabelPixels>
    <LabelQueryTimeout>查询超时:</LabelQueryTimeout>
    <LabelSystemVariable>系统变量:</LabelSystemVariable>
    <LabelTableOfContentsHint>请检查一下目录中应该包含的组件。一个组件等于一个标题级别.</LabelTableOfContentsHint>
    <LabelTotals>合计</LabelTotals>
    <LabelType>类型:</LabelType>
    <LabelUserName>用户:</LabelUserName>
    <LabelValue>值:</LabelValue>
    <LoadingReport>正在加载报表</LoadingReport>
    <nameAssembly>程序集</nameAssembly>
    <NewFeatures>新功能</NewFeatures>
    <No>否</No>
    <NoFixes>没有修复或更新的版本!</NoFixes>
    <NoIssues>没有问题</NoIssues>
    <NoNewVersions>没有可用的新版本!</NoNewVersions>
    <NotAssigned>未设置</NotAssigned>
    <Null>空值</Null>
    <Obsoleted>或已废除</Obsoleted>
    <Office2010Back>返回</Office2010Back>
    <PageNofM>{0} / {1}页</PageNofM>
    <PreparingReport>正在准备报表</PreparingReport>
    <Professional>专业</Professional>
    <ProfessionalConfiguration>所有对象属性.</ProfessionalConfiguration>
    <PropertiesTab>属性表</PropertiesTab>
    <RangeAll>全部</RangeAll>
    <RangeCurrentPage>当前页</RangeCurrentPage>
    <RangeInfo>输入页码并且/或页码范围用逗号分隔。例如: 1, 3, 5-12</RangeInfo>
    <RangePage>页面范围</RangePage>
    <RangePages>页数:</RangePages>
    <ReportChecker>报告检查器</ReportChecker>
    <ReportRenderingMessages>报表渲染信息</ReportRenderingMessages>
    <RestartDesigner>您需要重新启动报表设计器</RestartDesigner>
    <SaveReportPagesOrFormsFromReport>保存报表、页面或窗体</SaveReportPagesOrFormsFromReport>
    <SavingReport>正在保存报表</SavingReport>
    <SecondPass>第二通道</SecondPass>
    <Shapes>形状</Shapes>
    <ShowOnParametersPanel>在参数面板上显示</ShowOnParametersPanel>
    <Standard>标准</Standard>
    <StandardConfiguration>主要的对象属性，除了很少使用的</StandardConfiguration>
    <StiEmptyBrush>无</StiEmptyBrush>
    <StiGlareBrush>中空</StiGlareBrush>
    <StiGlassBrush>玻璃刷</StiGlassBrush>
    <StiGradientBrush>渐变</StiGradientBrush>
    <StiHatchBrush>斜线</StiHatchBrush>
    <StiSolidBrush>实心</StiSolidBrush>
    <StoreUserFirstLastNameInReportAuthor>的名字和姓氏存储在“Report Author”中</StoreUserFirstLastNameInReportAuthor>
    <StoreUserNameInReportAuthor>将用户名存储在'Report Author'中</StoreUserNameInReportAuthor>
    <StyleBad>坏</StyleBad>
    <StyleGood>好</StyleGood>
    <StyleNeutral>空档</StyleNeutral>
    <StyleNormal>正常</StyleNormal>
    <StyleNote>注释</StyleNote>
    <StyleWarning>警告</StyleWarning>
    <UseDataFields>使用数据字段</UseDataFields>
    <VisualChecked>已选取</VisualChecked>
    <VisualDefault>默认值</VisualDefault>
    <VisualDisabled>禁用</VisualDisabled>
    <VisualHover>悬停</VisualHover>
    <VisualPressed>扁平</VisualPressed>
    <Warnings>警告</Warnings>
    <WhatsNewInVersion>{0}中的新功能</WhatsNewInVersion>
    <When>当{0} {1}</When>
    <WhenAnd>当 {0} {1} 及</WhenAnd>
    <WhenValueIs>当值是</WhenValueIs>
  </Report>
  <ReportInfo>
    <CheckIssuesAdditionalDescription>检查报表中的错误并找出其中的不准确之处。</CheckIssuesAdditionalDescription>
    <EncryptWithPassword>使用密码加密</EncryptWithPassword>
    <EncryptWithPasswordAdditionalDescription>设置密码，以防止未经授权使用报表。</EncryptWithPasswordAdditionalDescription>
    <EncryptWithPasswordDescription>保护报表</EncryptWithPasswordDescription>
    <Info>信息</Info>
    <ReportOptions>报表选项</ReportOptions>
    <ReportOptionsAdditionalDescription>设置报表中的基本信息和参数。</ReportOptionsAdditionalDescription>
  </ReportInfo>
  <ReportOpen>
    <Browse>浏览</Browse>
    <Import>输入</Import>
  </ReportOpen>
  <Scheduler>
    <ActionRunReport>运行报表</ActionRunReport>
    <AdvancedSettings>高级设置</AdvancedSettings>
    <CheckBoxOverwriteIfExists>覆盖如果存在</CheckBoxOverwriteIfExists>
    <DayAll>所有</DayAll>
    <DayDayX>天{0}</DayDayX>
    <DayLast>最近</DayLast>
    <DayNone>空</DayNone>
    <DaysOfWeekAll>所有</DaysOfWeekAll>
    <DaysOfWeekNone>空</DaysOfWeekNone>
    <DelayTaskForUpToRandomDelay>任务延迟多达(随机延迟)</DelayTaskForUpToRandomDelay>
    <EditScheduler>编辑调度程序</EditScheduler>
    <EnableSSL>启用SSL</EnableSSL>
    <Expire>过期</Expire>
    <ForADurationOf>持续一段时间</ForADurationOf>
    <GetFolder>获取文件夹</GetFolder>
    <GroupBoxFiles>文件</GroupBoxFiles>
    <Host>主机</Host>
    <Info>Windows任务调度程序用于运行调度程序</Info>
    <LabelDaysOfWeek>天/周</LabelDaysOfWeek>
    <LabelMessage>信息</LabelMessage>
    <LabelResultName>名称</LabelResultName>
    <LabelRunAtDay>按天</LabelRunAtDay>
    <LabelRunAtMonth>按月</LabelRunAtMonth>
    <LabelRunAtTime>按次</LabelRunAtTime>
    <LabelRunEvery>所有</LabelRunEvery>
    <LabelSubject>主题</LabelSubject>
    <LabelTo>到</LabelTo>
    <Logs>日志</Logs>
    <MessageInvalidName>任务名称不能包含对文件名无效的任何字符</MessageInvalidName>
    <MessageInvalidTaskName>由于本机库中的错误，无法检索以句号后跟三个或更少字符结尾的任务名</MessageInvalidTaskName>
    <MessagePropertyNotSet>'{0}' 未设置!</MessagePropertyNotSet>
    <MonthAll>所有</MonthAll>
    <MonthNone>空</MonthNone>
    <NewAction>新操作</NewAction>
    <NewScheduler>新调度</NewScheduler>
    <NoLogs>无日志</NoLogs>
    <NoSchedulers>没有调度程序</NoSchedulers>
    <NoShedulers>没有调度程序</NoShedulers>
    <NumberOfDayAll>所有</NumberOfDayAll>
    <NumberOfDayFirst>第一</NumberOfDayFirst>
    <NumberOfDayFourth>第四</NumberOfDayFourth>
    <NumberOfDayLast>最后</NumberOfDayLast>
    <NumberOfDayNone>空</NumberOfDayNone>
    <NumberOfDaySecond>第二</NumberOfDaySecond>
    <NumberOfDayThird>第三</NumberOfDayThird>
    <OpenWindowsTaskScheduler>打开Windows任务计划程序</OpenWindowsTaskScheduler>
    <PleaseSelectSchedulerType>请选择调度程序类型</PleaseSelectSchedulerType>
    <RadioButtonDay>天</RadioButtonDay>
    <RadioButtonOn>On</RadioButtonOn>
    <RepeatTaskEvery>重复每个任务</RepeatTaskEvery>
    <Schedule>调度</Schedule>
    <Scheduler>调度</Scheduler>
    <Schedulers>调度</Schedulers>
    <SenderEmail>发送者的电子邮件</SenderEmail>
    <SmtpSettings>Smtp设置</SmtpSettings>
    <StopAllRunningTasksAtEndOfRepetitionDuration>在重复时间结束时停止所有正在运行的任务  </StopAllRunningTasksAtEndOfRepetitionDuration>
    <StopTasksIfItRunsLongerThan>停止任务，如果任务运行时间超过</StopTasksIfItRunsLongerThan>
    <TextDragDropDestinationHere>在这里拖放目标文件夹</TextDragDropDestinationHere>
    <TextDragDropReportHere>在这里拖放报告</TextDragDropReportHere>
    <TextPressPlusIconAddNewAction>单击图标+添加一个新操作!</TextPressPlusIconAddNewAction>
    <ToolTipSetParameters>定义报表参数</ToolTipSetParameters>
    <WizardDaily>按天</WizardDaily>
    <WizardDailyDescription>每天一次，在指定的时间</WizardDailyDescription>
    <WizardHourly>按小时</WizardHourly>
    <WizardHourlyDescription>一小时一次</WizardHourlyDescription>
    <WizardMonthly>按月</WizardMonthly>
    <WizardMonthlyDescription>每月，在指定的时间，在一周中的选定日期或一个月中的某天</WizardMonthlyDescription>
    <WizardOnce>一次</WizardOnce>
    <WizardOnceDescription>在任何时候运行一次调度程序</WizardOnceDescription>
    <WizardWeekly>按周</WizardWeekly>
    <WizardWeeklyDescription>在一周中选定的日期和指定的时间</WizardWeeklyDescription>
  </Scheduler>
  <Services>
    <categoryContextTools>文本工具</categoryContextTools>
    <categoryDesigner>设计</categoryDesigner>
    <categoryDictionary>字典</categoryDictionary>
    <categoryExport>导出</categoryExport>
    <categoryLanguages>语言</categoryLanguages>
    <categoryPanels>面板</categoryPanels>
    <categoryRender>渲染</categoryRender>
    <categoryShapes>形状</categoryShapes>
    <categorySL>保存/加载</categorySL>
    <categorySystem>系统</categorySystem>
    <categoryTextFormat>文本格式</categoryTextFormat>
  </Services>
  <Shapes>
    <Arrow>箭头</Arrow>
    <BasicShapes>基本图形</BasicShapes>
    <BentArrow>弯曲箭头</BentArrow>
    <BlockArrows>Block Arrows</BlockArrows>
    <Chevron>Chevron</Chevron>
    <ComplexArrow>复杂箭头</ComplexArrow>
    <DiagonalDownLine>反斜线</DiagonalDownLine>
    <DiagonalUpLine>斜线</DiagonalUpLine>
    <Division>分割</Division>
    <Equal>等于</Equal>
    <EquationShapes>Equation Shapes</EquationShapes>
    <Flowchart>流程图</Flowchart>
    <FlowchartCard>流程图：卡片</FlowchartCard>
    <FlowchartCollate>流程图：整理</FlowchartCollate>
    <FlowchartDecision>流程图：决策</FlowchartDecision>
    <FlowchartManualInput>流程图：手工输入</FlowchartManualInput>
    <FlowchartOffPageConnector>流程图: 端口连接</FlowchartOffPageConnector>
    <FlowchartPreparation>流程图：准备</FlowchartPreparation>
    <FlowchartSort>流程图：分类</FlowchartSort>
    <Frame>框架</Frame>
    <HorizontalLine>水平线</HorizontalLine>
    <InsertShapes>插入形状</InsertShapes>
    <LeftAndRightLine>双竖线</LeftAndRightLine>
    <Lines>线</Lines>
    <Minus>负号</Minus>
    <Multiply>乘</Multiply>
    <Octagon>八角形</Octagon>
    <Oval>椭圆</Oval>
    <Parallelogram>平行四边形</Parallelogram>
    <Plus>加</Plus>
    <Rectangle>矩形</Rectangle>
    <Rectangles>矩形</Rectangles>
    <RegularPentagon>规则的：五角形</RegularPentagon>
    <RoundedRectangle>圆角矩形</RoundedRectangle>
    <ServiceCategory>形状</ServiceCategory>
    <ShapeStyles>形状样式</ShapeStyles>
    <SnipDiagonalSideCornerRectangle>矩形剪斜一边角落</SnipDiagonalSideCornerRectangle>
    <SnipSameSideCornerRectangle>矩形剪斜用边角落</SnipSameSideCornerRectangle>
    <TopAndBottomLine>双横线</TopAndBottomLine>
    <Trapezoid>梯形</Trapezoid>
    <Triangle>三角形</Triangle>
    <VerticalLine>垂直线</VerticalLine>
  </Shapes>
  <Signature>
    <ClearSignature>清除签名</ClearSignature>
    <DigitalSignatureEmptyWatermark>PDF查看器中的数字签名</DigitalSignatureEmptyWatermark>
    <FullName>全名</FullName>
    <Initials>大写字母</Initials>
    <InsertImage>插入图片</InsertImage>
    <InsertText>插入文本</InsertText>
    <LoadSignature>加载签名</LoadSignature>
    <NoSavedItems>没有保存的项目</NoSavedItems>
    <PdfDigitalSignatureTitle>选择要在PDF文件的签名中显示的对象</PdfDigitalSignatureTitle>
    <SaveSignature>保存签名</SaveSignature>
    <SignatureSavedSuccessfully>签名保存成功.</SignatureSavedSuccessfully>
    <UseBrush>使用画笔</UseBrush>
  </Signature>
  <SystemVariables>
    <Column>返回当前的列号(从1开始).</Column>
    <GroupLine>返回当前的组行号(从1开始）.</GroupLine>
    <IsFirstPage> 如果目前报表的首页已打印，则返回true.</IsFirstPage>
    <IsFirstPageThrough>如果目前报表的首页已打印，则返回true. 当计算IsFirstPageThrough, 所有 ResetPageNumber 属性 都忽视，数目从报表开头开始. 为了变量的正确计算，需要执行两次处理.</IsFirstPageThrough>
    <IsLastPage>如果目前报表的尾页已打印，则返回true. 为了变量的正确计算，需要执行两次处理.</IsLastPage>
    <IsLastPageThrough>如果目前报表的尾页已打印，则返回true. 当计算IsFirstPageThrough, 所有 ResetPageNumber 属性 被忽视，数目从报表开头开始. 为了变量的正确计算，需要执行两次处理.</IsLastPageThrough>
    <Line>返回当前行号(从1开始).</Line>
    <LineABC>返回当前行号的字母模拟.</LineABC>
    <LineRoman>用罗马数字返回当前行号.</LineRoman>
    <LineThrough>返回当前行号(从1开始). 当计算数字, 所有分组都忽略，从打印开头开始计数.</LineThrough>
    <PageCopyNumber>返回当前页面副本的数目 (从1开始).</PageCopyNumber>
    <PageNofM>返回一个本地化的字符串, 显示 "页面 N of M"， N 是当前页面数， M 是报表的页面总计.</PageNofM>
    <PageNofMThrough>返回一个本地化的字符串, 显示 "页面 N of M"， N 是当前页面数， M 是报表的页面总计. 返回当前页面数，（从1开始）.</PageNofMThrough>
    <PageNumber>返回当前页面数，（从1开始）.</PageNumber>
    <PageNumberThrough>返回当前页面数，（从1开始）. 当计算IsFirstPageThrough, 所有 ResetPageNumber属性被忽视，将从报表开头开始重新计算.</PageNumberThrough>
    <ReportAlias>返回报表别名. 你可以在报表的ReportAlias属性帮助下改变报表别名.</ReportAlias>
    <ReportAuthor>返回报表作者. 你可以在报表的ReportAuthor属性帮助下改变报表作者.</ReportAuthor>
    <ReportChanged>更改报表时的日期.</ReportChanged>
    <ReportCreated>创建报表时的日期.</ReportCreated>
    <ReportDescription>返回报表说明. 你可以在报表的ReportDescription属性帮助下改变ReportName.</ReportDescription>
    <ReportName>返回报表名. 你可以在报表的ReportName属性帮助下更改报表名称.</ReportName>
    <Time>返回当前时间.</Time>
    <Today>返回当前日期.</Today>
    <TotalPageCount>返回报表的页面数.</TotalPageCount>
    <TotalPageCountThrough>返回报表的页面数. 当计算TotalPageCountThrough时, 所有的ResetPageNumber属性都被忽视，将从报表开头开始重新计算.</TotalPageCountThrough>
  </SystemVariables>
  <TableRibbon>
    <BuiltIn>内置</BuiltIn>
    <Delete>删除</Delete>
    <DeleteColumns>删除列</DeleteColumns>
    <DeleteRows>删除行</DeleteRows>
    <DeleteTable>删除表格</DeleteTable>
    <DistributeColumns>分布列</DistributeColumns>
    <DistributeRows>分布行</DistributeRows>
    <InsertAbove>上面插入</InsertAbove>
    <InsertBelow>下面插入</InsertBelow>
    <InsertLeft>左边插入</InsertLeft>
    <InsertRight>右边插入</InsertRight>
    <PlainTables>平板表格</PlainTables>
    <ribbonBarRowsColumns>行和列</ribbonBarRowsColumns>
    <ribbonBarTable>表格</ribbonBarTable>
    <ribbonBarTableStyles>表格样式</ribbonBarTableStyles>
    <Select>选择</Select>
    <SelectColumn>选择列</SelectColumn>
    <SelectRow>选择行</SelectRow>
    <SelectTable>选择表格</SelectTable>
  </TableRibbon>
  <Toolbars>
    <Align>对齐</Align>
    <AlignBottom>底端对齐</AlignBottom>
    <AlignCenter>居中对齐</AlignCenter>
    <AlignLeft>左对齐</AlignLeft>
    <AlignMiddle>中间对齐</AlignMiddle>
    <AlignRight>右对齐</AlignRight>
    <AlignToGrid>对齐到网格</AlignToGrid>
    <AlignTop>顶端对齐</AlignTop>
    <AlignWidth>宽度相同</AlignWidth>
    <BringToFront>前置</BringToFront>
    <CenterHorizontally>水平居中</CenterHorizontally>
    <CenterVertically>垂直居中</CenterVertically>
    <Conditions>条件</Conditions>
    <FontGrow>增大字体</FontGrow>
    <FontName>字体名称</FontName>
    <FontShrink>减小字体</FontShrink>
    <FontSize>字体尺寸</FontSize>
    <FontStyleBold>粗体</FontStyleBold>
    <FontStyleItalic>斜体</FontStyleItalic>
    <FontStyleUnderline>下划线</FontStyleUnderline>
    <Link>链接</Link>
    <Lock>固定</Lock>
    <MakeHorizontalSpacingEqual>水平间距相同</MakeHorizontalSpacingEqual>
    <MakeSameHeight>与 {0} 高度相同</MakeSameHeight>
    <MakeSameSize>与 {0} 大小相同</MakeSameSize>
    <MakeSameWidth>与 {0} 宽度相同</MakeSameWidth>
    <MakeVerticalSpacingEqual>垂直间距相同</MakeVerticalSpacingEqual>
    <MoveBackward>后移</MoveBackward>
    <MoveForward>前移</MoveForward>
    <Order>排序</Order>
    <RibbonClassic>经典功能区</RibbonClassic>
    <RibbonSingleLine>单线功能区</RibbonSingleLine>
    <SendToBack>后置</SendToBack>
    <Size>尺寸</Size>
    <StyleDesigner>样式设计</StyleDesigner>
    <Styles>样式列表</Styles>
    <TabHome>主页</TabHome>
    <TabLayout>层</TabLayout>
    <TabPage>页</TabPage>
    <TabView>视图</TabView>
    <TextBrush>画刷</TextBrush>
    <ToolbarAlignment>对齐</ToolbarAlignment>
    <ToolbarArrange>排列</ToolbarArrange>
    <ToolbarBorders>边框</ToolbarBorders>
    <ToolbarClipboard>剪贴板</ToolbarClipboard>
    <ToolbarDockStyle>停靠样式</ToolbarDockStyle>
    <ToolbarFont>字体</ToolbarFont>
    <ToolbarFormatting>格式</ToolbarFormatting>
    <ToolbarLayout>布局</ToolbarLayout>
    <ToolbarPageSetup>页面设置</ToolbarPageSetup>
    <ToolbarStandard>标准</ToolbarStandard>
    <ToolbarStyle>样式</ToolbarStyle>
    <ToolbarTextFormat>文本格式</ToolbarTextFormat>
    <ToolbarTools>工具</ToolbarTools>
    <ToolbarViewOptions>视图选项</ToolbarViewOptions>
    <ToolbarWatermarkImage>水印图像</ToolbarWatermarkImage>
    <ToolbarWatermarkText>水印文本</ToolbarWatermarkText>
  </Toolbars>
  <Toolbox>
    <Create>创建组件</Create>
    <Hand>手形工具</Hand>
    <Select>选择工具</Select>
    <Style>格式刷工具</Style>
    <TextEditor>文本编辑器</TextEditor>
    <title>工具箱</title>
  </Toolbox>
  <WelcomeScreen>
    <AllDownloadsWillCanceled>确定要关闭此窗口吗？所有下载将被取消</AllDownloadsWillCanceled>
    <Description>我们已为您挑选了我们认为最适合快速入门的报表。</Description>
    <GetStarted>开始</GetStarted>
    <GetStartedWithDashboards>开始使用控制面板</GetStartedWithDashboards>
    <GetStartedWithReports>开始使用报表</GetStartedWithReports>
    <MoreReports>更多报表</MoreReports>
    <ShowNextTime>下次展示 '{0}' </ShowNextTime>
    <Title>欢迎使用Stimulsoft演示版</Title>
  </WelcomeScreen>
  <Wizards>
    <BlankDashboard>空白仪表盘</BlankDashboard>
    <BlankForm>空白格式</BlankForm>
    <BlankReport>空白报表</BlankReport>
    <BlankScreen>黑屏</BlankScreen>
    <ButtonBack>&lt; 上一步(&amp;B)</ButtonBack>
    <ButtonCancel>取消</ButtonCancel>
    <ButtonFinish>完成(&amp;F)</ButtonFinish>
    <ButtonNext>下一步(&amp;N) &gt;</ButtonNext>
    <ColumnsOrder>列的顺序</ColumnsOrder>
    <Company>公司</Company>
    <Custom>自定义</Custom>
    <DataRelation>关系</DataRelation>
    <DataSource>数据源</DataSource>
    <DataSources>数据源</DataSources>
    <DefaultThemes>默认主题</DefaultThemes>
    <Filters>过滤</Filters>
    <FromReportTemplate>从报表模板</FromReportTemplate>
    <GetData>检索数据</GetData>
    <groupCreateNewDashboard>新建仪表盘</groupCreateNewDashboard>
    <groupCreateNewPageOrForm>创建新页面或窗体</groupCreateNewPageOrForm>
    <groupCreateNewReport>创建新报表</groupCreateNewReport>
    <Groups>分组</Groups>
    <groupTemplates>模板</groupTemplates>
    <groupWizards>向导</groupWizards>
    <infoColumnsOrder>按需要顺序调整列。</infoColumnsOrder>
    <infoCompanyInfo>输入您的公司信息。</infoCompanyInfo>
    <infoDataSource>选择一个数据源。</infoDataSource>
    <infoDataSources>选择现有的数据源.</infoDataSources>
    <infoFilters>为您的报表过滤数据</infoFilters>
    <infoGroups>按需要选择列进行分组。</infoGroups>
    <infoLabelSettings>进行标签设置。</infoLabelSettings>
    <infoLanguages>选择报表文化。</infoLanguages>
    <infoLayout>指定报表布局.</infoLayout>
    <infoRelation>选择一个现有的数据关系.</infoRelation>
    <infoSelectColumns>按所要显示的信息选择列。</infoSelectColumns>
    <infoSelectTemplate>根据需要选择合适的模板。</infoSelectTemplate>
    <infoSort>设置数据排序。您可以选择多列进行排序。</infoSort>
    <infoThemes>选择报表主题.</infoThemes>
    <infoTotals>为您的报表添加摘要信息。</infoTotals>
    <LabelDirection>说明:</LabelDirection>
    <LabelHeight>高:</LabelHeight>
    <LabelHorizontalGap>水平间距:</LabelHorizontalGap>
    <LabelLabelType>标签类型:</LabelLabelType>
    <LabelLeftMargin>左边距:</LabelLeftMargin>
    <LabelNumberOfColumns>栏数:</LabelNumberOfColumns>
    <LabelNumberOfRows>行数:</LabelNumberOfRows>
    <LabelPageHeight>页高:</LabelPageHeight>
    <LabelPageWidth>页宽:</LabelPageWidth>
    <LabelReport>报表标签</LabelReport>
    <LabelSettings>标签设置</LabelSettings>
    <LabelSize>尺寸:</LabelSize>
    <LabelTopMargin>上边距:</LabelTopMargin>
    <LabelVerticalGap>垂直间距:</LabelVerticalGap>
    <LabelWidth>宽:</LabelWidth>
    <Layout>布局</Layout>
    <Mapping>制图</Mapping>
    <MarkAll>全选(&amp;A)</MarkAll>
    <MasterDetailReport>报表主要细节</MasterDetailReport>
    <NoFunction>[无]</NoFunction>
    <OpenExistingReport>打开已经存在的报表</OpenExistingReport>
    <OpenFrom>从{0}中打开</OpenFrom>
    <Preview>预览</Preview>
    <Reset>重置(&amp;R)</Reset>
    <Results>结果</Results>
    <RunWizard>运行向导</RunWizard>
    <SelectColumns>选择列</SelectColumns>
    <SelectTemplate>模板</SelectTemplate>
    <Sort>排序</Sort>
    <StandardReport>标准报表</StandardReport>
    <Themes>主题</Themes>
    <title>新建报表</title>
    <Totals>总计</Totals>
    <UseDemoData>使用演示数据</UseDemoData>
    <UsingReportWizard>使用报表向导</UsingReportWizard>
    <YouHaveNotOpenedAnyReportRecently>您最近没有打开任何报表。要浏览报告，请先单击“打开现有文件”。</YouHaveNotOpenedAnyReportRecently>
  </Wizards>
  <Zoom>
    <EmptyValue>空值</EmptyValue>
    <MultiplePages>多页</MultiplePages>
    <OnePage>整页</OnePage>
    <PageHeight>页高</PageHeight>
    <PageWidth>页宽</PageWidth>
    <TwoPages>双页</TwoPages>
    <ZoomTo100>原始大小</ZoomTo100>
  </Zoom>
</Localization>