{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-BoxF5NeN.js", "sources": ["../../src/layouts/components/Topbar/Toolbar/ColorScheme/index.vue", "../../src/utils/composables/useViewTransition.ts"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport useSettingsStore from '@/store/modules/settings'\r\n\r\ndefineOptions({\r\n  name: 'ColorScheme',\r\n})\r\n\r\nconst settingsStore = useSettingsStore()\r\n\r\nfunction toggleColorScheme(event: MouseEvent) {\r\n  const { startViewTransition } = useViewTransition(() => {\r\n    settingsStore.currentColorScheme && settingsStore.setColorScheme(settingsStore.currentColorScheme === 'dark' ? 'light' : 'dark')\r\n  })\r\n  startViewTransition()?.ready.then(() => {\r\n    const x = event.clientX\r\n    const y = event.clientY\r\n    const endRadius = Math.hypot(\r\n      Math.max(x, innerWidth - x),\r\n      Math.max(y, innerHeight - y),\r\n    )\r\n    const clipPath = [\r\n      `circle(0px at ${x}px ${y}px)`,\r\n      `circle(${endRadius}px at ${x}px ${y}px)`,\r\n    ]\r\n    document.documentElement.animate(\r\n      {\r\n        clipPath: settingsStore.settings.app.colorScheme !== 'dark' ? clipPath : clipPath.reverse(),\r\n      },\r\n      {\r\n        duration: 300,\r\n        easing: 'ease-out',\r\n        pseudoElement: settingsStore.settings.app.colorScheme !== 'dark' ? '::view-transition-new(root)' : '::view-transition-old(root)',\r\n      },\r\n    )\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <HDropdown class=\"flex-center cursor-pointer p-2\">\r\n    <SvgIcon\r\n      :name=\"{\r\n        'light': 'i-ri:sun-line',\r\n        'dark': 'i-ri:moon-line',\r\n        '': 'i-codicon:color-mode',\r\n      }[settingsStore.settings.app.colorScheme]\" @click=\"toggleColorScheme\"\r\n    />\r\n    <template #dropdown>\r\n      <HTabList\r\n        v-model=\"settingsStore.settings.app.colorScheme\"\r\n        :options=\"[\r\n          { icon: 'i-ri:sun-line', label: '', value: 'light' },\r\n          { icon: 'i-ri:moon-line', label: '', value: 'dark' },\r\n          { icon: 'i-codicon:color-mode', label: '', value: '' },\r\n        ]\"\r\n        class=\"m-3\"\r\n      />\r\n    </template>\r\n  </HDropdown>\r\n</template>\r\n", "export default function useViewTransition(callback: () => void) {\r\n  function startViewTransition() {\r\n    if (!document.startViewTransition || window.matchMedia('(prefers-reduced-motion: reduce)').matches) {\r\n      callback()\r\n      return\r\n    }\r\n    return document.startViewTransition(async () => {\r\n      await Promise.resolve(callback())\r\n    })\r\n  }\r\n\r\n  return {\r\n    startViewTransition,\r\n  }\r\n}\r\n"], "names": ["settingsStore", "useSettingsStore", "toggleColorScheme", "event", "startViewTransition", "callback", "currentColorScheme", "setColorScheme", "document", "window", "matchMedia", "matches", "async", "Promise", "resolve", "_a", "ready", "then", "x", "clientX", "y", "clientY", "clipPath", "Math", "hypot", "max", "innerWidth", "innerHeight", "documentElement", "animate", "settings", "app", "colorScheme", "reverse", "duration", "easing", "pseudoElement"], "mappings": "qQAOA,MAAMA,EAAgBC,IAEtB,SAASC,EAAkBC,SACzB,MAAMC,oBAAEA,ICVgCC,EDUU,KAChDL,EAAcM,oBAAsBN,EAAcO,eAAoD,SAArCP,EAAcM,mBAAgC,QAAU,OAAM,ECA1H,CACLF,oBAXF,WACE,GAAKI,SAASJ,sBAAuBK,OAAOC,WAAW,oCAAoCC,QAIpF,OAAAH,SAASJ,qBAAoBQ,gBAC5BC,QAAQC,QAAQT,IAAU,IAJvBA,GAKV,IARL,IAA0CA,EDapB,OAAAU,EAAAX,MAAAW,EAAGC,MAAMC,MAAK,KAChC,MAAMC,EAAIf,EAAMgB,QACVC,EAAIjB,EAAMkB,QAKVC,EAAW,CACf,iBAAiBJ,OAAOE,OACxB,UANgBG,KAAKC,MACrBD,KAAKE,IAAIP,EAAGQ,WAAaR,GACzBK,KAAKE,IAAIL,EAAGO,YAAcP,YAIEF,OAAOE,QAErCZ,SAASoB,gBAAgBC,QACvB,CACEP,SAAqD,SAA3CtB,EAAc8B,SAASC,IAAIC,YAAyBV,EAAWA,EAASW,WAEpF,CACEC,SAAU,IACVC,OAAQ,WACRC,cAA0D,SAA3CpC,EAAc8B,SAASC,IAAIC,YAAyB,8BAAgC,+BAEvG,GACD"}