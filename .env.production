# 应用配置面板
VITE_APP_SETTING = false
# 页面标题
VITE_APP_TITLE = 订单蚁来-酒店智能体
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = https://api.yiduohua.net/
# 前端链接前缀
VITE_BASE_PREFIX = /dist/
# 体验账号和密码
VITE_APP_EXPERIENCE_ACCOUNT = ***********
VITE_APP_EXPERIENCE_PASSWORD = 123456
# 报表前端地址
VITE_APP_REPORT_URL = https://ai.dingdanyilai.com/dist-report/index.html

# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =
# 是否禁用开发者工具，可防止被调试
VITE_APP_DISABLE_DEVTOOL = false
#pms client download url
VITE_APP_CLIENT_DOWNLOAD_URL = https://pms-client-update-**********.cos.ap-guangzhou.myqcloud.com/Hotel-Agent-Windows.exe

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = false
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = false
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip,brotli
# 是否在打包后生成存档，支持 zip 和 tar
VITE_BUILD_ARCHIVE =
# API请求前缀
VITE_APP_BASE_API = '/proxy'

