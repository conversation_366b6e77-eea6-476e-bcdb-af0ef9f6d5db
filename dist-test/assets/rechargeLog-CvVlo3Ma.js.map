{"version": 3, "file": "rechargeLog-CvVlo3Ma.js", "sources": ["../../src/views/customer/member/info/components/DetailForm/rechargeLog.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"businessDate\": \"Biz Date\",\r\n    \"name\": \"Name\",\r\n    \"memberCardNumber\": \"Member Card\",\r\n    \"rechargeChannel\": \"Recharge Channel\",\r\n    \"store\": \"Hotel\",\r\n    \"miniProgram\": \"Mini Program\",\r\n    \"rechargeAmount\": \"Recharge\",\r\n    \"giftAmount\": \"Gift\",\r\n    \"paymentMethod\": \"Payment Method\",\r\n    \"operatorTime\": \"Op/Time\",\r\n    \"storeName\": \"Hotel\",\r\n    \"rangeSeparator\": \"To\",\r\n    \"startPlaceholder\": \"Start\",\r\n    \"endPlaceholder\": \"End\",\r\n    \"noData\": \"No data available\",\r\n    \"unknownError\": \"Unknown error\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"businessDate\": \"营业日期\",\r\n    \"name\": \"姓名\",\r\n    \"memberCardNumber\": \"会员卡号\",\r\n    \"rechargeChannel\": \"充值渠道\",\r\n    \"store\": \"门店\",\r\n    \"miniProgram\": \"小程序\",\r\n    \"rechargeAmount\": \"充值金额(元)\",\r\n    \"giftAmount\": \"赠送金额(元)\",\r\n    \"paymentMethod\": \"支付方式\",\r\n    \"operatorTime\": \"操作员/时间\",\r\n    \"storeName\": \"发生门店\",\r\n    \"rangeSeparator\": \"至\",\r\n    \"startPlaceholder\": \"开始\",\r\n    \"endPlaceholder\": \"结束\",\r\n    \"noData\": \"暂无数据\",\r\n    \"unknownError\": \"未知错误\"\r\n  },\r\n  \"km\": {\r\n    \"businessDate\": \"កាលបរិច្ឆេទអាជីវកម្ម\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"memberCardNumber\": \"លេខកាតសមាជិក\",\r\n    \"rechargeChannel\": \"ឆានែលបញ្ចូល\",\r\n    \"store\": \"សាខា\",\r\n    \"miniProgram\": \"កម្មវិធីតូច\",\r\n    \"rechargeAmount\": \"ចំនួនទឹកប្រាក់បញ្ចូល(រៀល)\",\r\n    \"giftAmount\": \"ចំនួនទឹកប្រាក់ផ្តល់ជូន(រៀល)\",\r\n    \"paymentMethod\": \"វិធីសាក\",\r\n    \"operatorTime\": \"ប្រតិបត្តិការ/ពេលវេលា\",\r\n    \"storeName\": \"សាខាកើតឡើង\",\r\n    \"rangeSeparator\": \"ដល់\",\r\n    \"startPlaceholder\": \"ចាប់ផ្តើម\",\r\n    \"endPlaceholder\": \"បញ្ចប់\",\r\n    \"noData\": \"គ្មានទិន្នន័យ\",\r\n    \"unknownError\": \"កំហុសមិនស្គាល់\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel, MemberRechargeLogModel } from '@/models/index'\r\nimport { generalConfigApi, memberLogApi } from '@/api/modules/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport ymdate from '@/utils/timeutils'\r\nimport dayjs from 'dayjs'\r\n\r\nconst props = defineProps<{\r\n  mcode: string\r\n  isEdit: boolean\r\n}>()\r\n\r\nconst { t } = useI18n()\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: true,\r\n  // 搜索\r\n  search: {\r\n    bizDate: '',\r\n  },\r\n  dataList: [] as MemberRechargeLogModel[],\r\n})\r\n\r\n/** 支付方式列表 */\r\nconst payTypes = ref<DictDataModel[]>([])\r\n\r\nonMounted(() => {\r\n  getPayTypes()\r\n  getDataList()\r\n})\r\n\r\ndefineExpose({ getDataList })\r\n\r\n/**\r\n * 获取支付方式\r\n */\r\nfunction getPayTypes() {\r\n  generalConfigApi\r\n    .getPayAccountList(userStore.gcode)\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        payTypes.value = res.data\r\n      }\r\n    })\r\n    .catch(() => {\r\n      ElMessage.error({\r\n        message: t('unknownError'),\r\n        center: true,\r\n      })\r\n    })\r\n}\r\n\r\n/**\r\n * 获取充值记录列表\r\n */\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  // 格式化 bizDate 为 \"yyyy-MM-dd\" 格式\r\n  const formattedBizDate = data.value.search.bizDate ? data.value.search.bizDate.map((date: any) => dayjs(date).format('YYYY-MM-DD')).join(',') : []\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    // hcode: userStore.hcode,\r\n    mcode: props.mcode,\r\n    bizDate: formattedBizDate,\r\n    ...getParams(),\r\n  }\r\n  memberLogApi\r\n    .rechargeLoglist(params)\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        data.value.loading = false\r\n        data.value.dataList = res.data.list\r\n        pagination.value.total = res.data.total\r\n      } else {\r\n        data.value.loading = false\r\n        ElMessage.error({\r\n          message: t('unknownError'),\r\n          center: true,\r\n        })\r\n      }\r\n    })\r\n    .catch(() => {\r\n      data.value.loading = false\r\n      ElMessage.error({\r\n        message: t('unknownError'),\r\n        center: true,\r\n      })\r\n    })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\n/**\r\n * 将支付方式编码转换为名称\r\n */\r\nfunction payTypes2Name(row: any) {\r\n  return payTypes.value.find((item: any) => item.code === row.payMethod)?.name || t('noData')\r\n}\r\n\r\n/**\r\n * 格式化金额\r\n */\r\nfunction formatMoney(amount: number): string {\r\n  return amount.toFixed(2)\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <search-bar :show-toggle=\"false\">\r\n      <el-form :model=\"data.search\" size=\"default\" label-width=\"150px\" inline-message inline class=\"search-form\">\r\n        <el-form-item :label=\"t('businessDate')\">\r\n          <el-date-picker v-model=\"data.search.bizDate\" type=\"daterange\" :range-separator=\"t('rangeSeparator')\" :start-placeholder=\"t('startPlaceholder')\" :end-placeholder=\"t('endPlaceholder')\" style=\"width: 300px\" @change=\"getDataList()\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </search-bar>\r\n    <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" highlight-current-row height=\"100%\" @sort-change=\"sortChange\">\r\n      <el-table-column :label=\"t('name')\" min-width=\"180\" show-overflow-tooltip>\r\n        <template #default=\"scope\">\r\n          <div class=\"member-info\">\r\n            <div class=\"info-row\">\r\n              <span class=\"label\">姓名：</span>\r\n              <span class=\"value\">{{ scope.row.name }}</span>\r\n            </div>\r\n            <div class=\"info-row\">\r\n              <span class=\"label\">卡号：</span>\r\n              <span class=\"value\">{{ scope.row.storeCardNo }}</span>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('rechargeChannel')\" width=\"100\">\r\n        <template #default=\"scope\">\r\n          <el-tag v-if=\"scope.row.rechargeChannel === 'lobby'\" size=\"small\">\r\n            {{ t('store') }}\r\n          </el-tag>\r\n          <el-tag v-else size=\"small\">\r\n            {{ t('miniProgram') }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('rechargeAmount')\" align=\"right\" width=\"120\">\r\n        <template #default=\"scope\">\r\n          {{ formatMoney(scope.row.fee) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"giveMoney\" :label=\"t('giftAmount')\" align=\"right\" width=\"120\">\r\n        <template #default=\"scope\">\r\n          {{ formatMoney(scope.row.giveMoney) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :label=\"t('paymentMethod')\" min-width=\"150\" show-overflow-tooltip>\r\n        <template #default=\"scope\">\r\n          <span>{{ payTypes2Name(scope.row) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"bizDate\" :label=\"t('businessDate')\" width=\"120\" />\r\n      <el-table-column :label=\"t('operatorTime')\" min-width=\"150\">\r\n        <template #default=\"scope\">\r\n          <div class=\"operator-time\">\r\n            <span>{{ scope.row.operator }}</span>\r\n            <br />\r\n            <span>{{ ymdate(scope.row.createTime) }}</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"hname\" :label=\"t('storeName')\" show-overflow-tooltip />\r\n    </el-table>\r\n    <el-pagination\r\n      v-if=\"pagination.total > 10\"\r\n      :current-page=\"pagination.pageNo\"\r\n      :total=\"pagination.total\"\r\n      :page-size=\"pagination.pageSize\"\r\n      :page-sizes=\"pagination.sizes\"\r\n      :layout=\"pagination.layout\"\r\n      :hide-on-single-page=\"false\"\r\n      class=\"pagination\"\r\n      background\r\n      @size-change=\"sizeChange\"\r\n      @current-change=\"currentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.member-info {\r\n  .info-row {\r\n    display: flex;\r\n    align-items: center;\r\n    line-height: 20px;\r\n\r\n    .label {\r\n      color: #909399;\r\n      width: 45px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .value {\r\n      flex: 1;\r\n    }\r\n  }\r\n}\r\n.operator-time {\r\n  line-height: 1.5;\r\n\r\n  span {\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n.list-table {\r\n  :deep(.el-table__cell) {\r\n    white-space: nowrap;\r\n  }\r\n}\r\n.absolute-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "t", "useI18n", "userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "data", "ref", "loading", "tableAutoHeight", "search", "bizDate", "dataList", "payTypes", "getDataList", "value", "formattedBizDate", "map", "date", "dayjs", "format", "join", "params", "gcode", "mcode", "memberLogApi", "rechargeLoglist", "then", "res", "code", "list", "total", "ElMessage", "error", "message", "center", "catch", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "formatMoney", "amount", "toFixed", "onMounted", "generalConfigApi", "getPayAccountList", "__expose", "row", "_a", "find", "item", "payMethod", "name"], "mappings": "ytCAkEA,MAAMA,EAAQC,GAKRC,EAAEA,GAAMC,IACRC,EAAYC,KACZC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IAEzEC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAEjBC,OAAQ,CACNC,QAAS,IAEXC,SAAU,KAINC,EAAWN,EAAqB,IA+BtC,SAASO,IACPR,EAAKS,MAAMP,SAAU,EAEf,MAAAQ,EAAmBV,EAAKS,MAAML,OAAOC,QAAUL,EAAKS,MAAML,OAAOC,QAAQM,KAAKC,GAAcC,EAAMD,GAAME,OAAO,gBAAeC,KAAK,KAAO,GAC1IC,EAAS,CACbC,MAAOzB,EAAUyB,MAEjBC,MAAO9B,EAAM8B,MACbb,QAASK,KACNf,KAELwB,EACGC,gBAAgBJ,GAChBK,MAAMC,IACY,IAAbA,EAAIC,MACNvB,EAAKS,MAAMP,SAAU,EAChBF,EAAAS,MAAMH,SAAWgB,EAAItB,KAAKwB,KACpB9B,EAAAe,MAAMgB,MAAQH,EAAItB,KAAKyB,QAElCzB,EAAKS,MAAMP,SAAU,EACrBwB,EAAUC,MAAM,CACdC,QAAStC,EAAE,gBACXuC,QAAQ,IACT,IAGJC,OAAM,KACL9B,EAAKS,MAAMP,SAAU,EACrBwB,EAAUC,MAAM,CACdC,QAAStC,EAAE,gBACXuC,QAAQ,GACT,GACF,CAIL,SAASE,EAAWC,GAClBpC,EAAaoC,GAAMX,MAAK,IAAMb,KAAa,CAIpC,SAAAyB,EAAcC,EAAO,GAC5BrC,EAAgBqC,GAAMb,MAAK,IAAMb,KAAa,CAIhD,SAAS2B,GAAWC,KAAEA,EAAMC,MAAAA,IAC1BvC,EAAasC,EAAMC,GAAOhB,MAAK,IAAMb,KAAa,CAapD,SAAS8B,EAAYC,GACZ,OAAAA,EAAOC,QAAQ,EAAC,QA1FzBC,GAAU,KAWRC,EACGC,kBAAkBnD,EAAUyB,OAC5BI,MAAMC,IACY,IAAbA,EAAIC,OACNhB,EAASE,MAAQa,EAAItB,KAAA,IAGxB8B,OAAM,KACLJ,EAAUC,MAAM,CACdC,QAAStC,EAAE,gBACXuC,QAAQ,GACT,IApBOrB,GAAA,IAGDoC,EAAA,CAAEpC,s4DA6EQqC,SACd,OAAAC,EAASvC,EAAAE,MAAMsC,MAAMC,GAAcA,EAAKzB,OAASsB,EAAII,kBAAY,EAAAH,EAAAI,OAAQ5D,EAAE,gBADpF,IAAuBuD"}