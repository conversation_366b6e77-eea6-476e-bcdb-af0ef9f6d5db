<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <handoverReportCashRealization Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>handoverReportCashRealization</Alias>
        <HeadersString />
        <Key />
        <Name>handoverReportCashRealization</Name>
        <PathData>C:\Users\<USER>\Desktop\需求文档\2\交班\handoverReportCashRealization.json</PathData>
      </handoverReportCashRealization>
    </Databases>
    <DataSources isList="true" count="10">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="4">
          <value>code,System.Decimal</value>
          <value>data,System.String</value>
          <value>msg,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>8fdc8f2b7f0240b2b63769f50269e87f</Key>
        <Name>root</Name>
        <NameInSource>handoverReportCashRealization.root</NameInSource>
      </root>
      <root_data Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_data</Alias>
        <Columns isList="true" count="10">
          <value>hname,System.String</value>
          <value>hcode,System.String</value>
          <value>shiftNo,System.String</value>
          <value>bizDate,System.String</value>
          <value>lastSelectTime,System.String</value>
          <value>operator,System.String</value>
          <value>handoverDetail,System.String</value>
          <value>consumeSubjects,System.String</value>
          <value>paymentSubjects,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>141b77f4f3a94f10b12e7350e33a4698</Key>
        <Name>root_data</Name>
        <NameInSource>handoverReportCashRealization.root_data</NameInSource>
      </root_data>
      <root_data_consumeSubjects Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_data_consumeSubjects</Alias>
        <Columns isList="true" count="5">
          <value>subjectCode,System.String</value>
          <value>subjectName,System.String</value>
          <value>amount,System.Decimal</value>
          <value>todaySettle,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>04559a19b7244270bb4819d27db0741b</Key>
        <Name>root_data_consumeSubjects</Name>
        <NameInSource>handoverReportCashRealization.root_data_consumeSubjects</NameInSource>
      </root_data_consumeSubjects>
      <root_data_handoverDetail Ref="6" type="DataTableSource" isKey="true">
        <Alias>root_data_handoverDetail</Alias>
        <Columns isList="true" count="6">
          <value>previousShiftAmount,System.String</value>
          <value>currentShiftAmount,System.String</value>
          <value>totalAmount,System.String</value>
          <value>handoverAmount,System.String</value>
          <value>remainingAmount,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>67bd61c8658e4fc6839cf3483ae12bf9</Key>
        <Name>root_data_handoverDetail</Name>
        <NameInSource>handoverReportCashRealization.root_data_handoverDetail</NameInSource>
      </root_data_handoverDetail>
      <root_data_handoverDetail_currentShiftAmount Ref="7" type="DataTableSource" isKey="true">
        <Alias>root_data_handoverDetail_currentShiftAmount</Alias>
        <Columns isList="true" count="6">
          <value>cashAmount,System.Decimal</value>
          <value>bankCardAmount,System.Decimal</value>
          <value>wechatAmount,System.Decimal</value>
          <value>alipayAmount,System.Decimal</value>
          <value>totalAmount,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>78bc722289ca46dbba6ec6a1d372ed62</Key>
        <Name>root_data_handoverDetail_currentShiftAmount</Name>
        <NameInSource>handoverReportCashRealization.root_data_handoverDetail_currentShiftAmount</NameInSource>
      </root_data_handoverDetail_currentShiftAmount>
      <root_data_handoverDetail_handoverAmount Ref="8" type="DataTableSource" isKey="true">
        <Alias>root_data_handoverDetail_handoverAmount</Alias>
        <Columns isList="true" count="6">
          <value>cashAmount,System.Decimal</value>
          <value>bankCardAmount,System.Decimal</value>
          <value>wechatAmount,System.Decimal</value>
          <value>alipayAmount,System.Decimal</value>
          <value>totalAmount,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>38b085f910b54a0bb21aa137ae8862bb</Key>
        <Name>root_data_handoverDetail_handoverAmount</Name>
        <NameInSource>handoverReportCashRealization.root_data_handoverDetail_handoverAmount</NameInSource>
      </root_data_handoverDetail_handoverAmount>
      <root_data_handoverDetail_previousShiftAmount Ref="9" type="DataTableSource" isKey="true">
        <Alias>root_data_handoverDetail_previousShiftAmount</Alias>
        <Columns isList="true" count="6">
          <value>cashAmount,System.Decimal</value>
          <value>bankCardAmount,System.Decimal</value>
          <value>wechatAmount,System.Decimal</value>
          <value>alipayAmount,System.Decimal</value>
          <value>totalAmount,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>181b09b42b214b67b4333875a25afb2a</Key>
        <Name>root_data_handoverDetail_previousShiftAmount</Name>
        <NameInSource>handoverReportCashRealization.root_data_handoverDetail_previousShiftAmount</NameInSource>
      </root_data_handoverDetail_previousShiftAmount>
      <root_data_handoverDetail_remainingAmount Ref="10" type="DataTableSource" isKey="true">
        <Alias>root_data_handoverDetail_remainingAmount</Alias>
        <Columns isList="true" count="6">
          <value>cashAmount,System.Decimal</value>
          <value>bankCardAmount,System.Decimal</value>
          <value>wechatAmount,System.Decimal</value>
          <value>alipayAmount,System.Decimal</value>
          <value>totalAmount,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>f24036ec360c40158b827ecaa1bda2d7</Key>
        <Name>root_data_handoverDetail_remainingAmount</Name>
        <NameInSource>handoverReportCashRealization.root_data_handoverDetail_remainingAmount</NameInSource>
      </root_data_handoverDetail_remainingAmount>
      <root_data_handoverDetail_totalAmount Ref="11" type="DataTableSource" isKey="true">
        <Alias>root_data_handoverDetail_totalAmount</Alias>
        <Columns isList="true" count="6">
          <value>cashAmount,System.Decimal</value>
          <value>bankCardAmount,System.Decimal</value>
          <value>wechatAmount,System.Decimal</value>
          <value>alipayAmount,System.Decimal</value>
          <value>totalAmount,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>0d368dea88574524b47303dc35414799</Key>
        <Name>root_data_handoverDetail_totalAmount</Name>
        <NameInSource>handoverReportCashRealization.root_data_handoverDetail_totalAmount</NameInSource>
      </root_data_handoverDetail_totalAmount>
      <root_data_paymentSubjects Ref="12" type="DataTableSource" isKey="true">
        <Alias>root_data_paymentSubjects</Alias>
        <Columns isList="true" count="5">
          <value>subjectCode,System.String</value>
          <value>subjectName,System.String</value>
          <value>amount,System.Decimal</value>
          <value>todaySettle,System.Decimal</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>613c1acb65ff498fb62ceeba474076fa</Key>
        <Name>root_data_paymentSubjects</Name>
        <NameInSource>handoverReportCashRealization.root_data_paymentSubjects</NameInSource>
      </root_data_paymentSubjects>
    </DataSources>
    <Relations isList="true" count="9">
      <root Ref="13" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_data</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>data</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root_data Ref="14" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_consumeSubjects</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>consumeSubjects</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data Ref="15" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="6" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_handoverDetail</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>handoverDetail</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
      <root_data_handoverDetail Ref="16" type="DataRelation" isKey="true">
        <Alias>root_data_handoverDetail</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="7" />
        <Dictionary isRef="1" />
        <Name>root_data_handoverDetail</Name>
        <NameInSource>root_data_handoverDetail_currentShiftAmount</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>currentShiftAmount</value>
        </ParentColumns>
        <ParentSource isRef="6" />
      </root_data_handoverDetail>
      <root_data_handoverDetail Ref="17" type="DataRelation" isKey="true">
        <Alias>root_data_handoverDetail</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="8" />
        <Dictionary isRef="1" />
        <Name>root_data_handoverDetail</Name>
        <NameInSource>root_data_handoverDetail_handoverAmount</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>handoverAmount</value>
        </ParentColumns>
        <ParentSource isRef="6" />
      </root_data_handoverDetail>
      <root_data_handoverDetail Ref="18" type="DataRelation" isKey="true">
        <Alias>root_data_handoverDetail</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="9" />
        <Dictionary isRef="1" />
        <Name>root_data_handoverDetail</Name>
        <NameInSource>root_data_handoverDetail_previousShiftAmount</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>previousShiftAmount</value>
        </ParentColumns>
        <ParentSource isRef="6" />
      </root_data_handoverDetail>
      <root_data_handoverDetail Ref="19" type="DataRelation" isKey="true">
        <Alias>root_data_handoverDetail</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="10" />
        <Dictionary isRef="1" />
        <Name>root_data_handoverDetail</Name>
        <NameInSource>root_data_handoverDetail_remainingAmount</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>remainingAmount</value>
        </ParentColumns>
        <ParentSource isRef="6" />
      </root_data_handoverDetail>
      <root_data_handoverDetail Ref="20" type="DataRelation" isKey="true">
        <Alias>root_data_handoverDetail</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="11" />
        <Dictionary isRef="1" />
        <Name>root_data_handoverDetail</Name>
        <NameInSource>root_data_handoverDetail_totalAmount</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>totalAmount</value>
        </ParentColumns>
        <ParentSource isRef="6" />
      </root_data_handoverDetail>
      <root_data Ref="21" type="DataRelation" isKey="true">
        <Alias>root_data</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="12" />
        <Dictionary isRef="1" />
        <Name>root_data</Name>
        <NameInSource>root_data_paymentSubjects</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>paymentSubjects</value>
        </ParentColumns>
        <ParentSource isRef="4" />
      </root_data>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>d14ee217327145f6a1e013005dff104a</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="22" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="12">
        <页眉1 Ref="23" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text2 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text2</Name>
              <Page isRef="22" />
              <Parent isRef="23" />
              <Text>{root_data.hname}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,5.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Name>Text6</Name>
              <Page isRef="22" />
              <Parent isRef="23" />
              <Text>第{PageNumber}页,共{TotalPageCount}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </页眉1>
        <报表标题区1 Ref="26" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2,19,1.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,12,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="22" />
              <Parent isRef="26" />
              <Text>交班报表(收付实现制)</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text5 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <Name>Text5</Name>
              <Page isRef="22" />
              <Parent isRef="26" />
              <Text>门店:{root_data.hname}   营业日:{root_data.bizDate}  最后查询时间：{root_data.lastSelectTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>报表标题区1</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </报表标题区1>
        <页眉2 Ref="29" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.4,19,0.8</ClientRectangle>
          <Components isList="true" count="5">
            <Text3 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text3</Name>
              <Page isRef="22" />
              <Parent isRef="29" />
              <Text>本班交班概览</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <文本1 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本1</Name>
              <Page isRef="22" />
              <Parent isRef="29" />
              <Text>现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
            <文本2 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本2</Name>
              <Page isRef="22" />
              <Parent isRef="29" />
              <Text>银行卡</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
            <文本3 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本3</Name>
              <Page isRef="22" />
              <Parent isRef="29" />
              <Text>微信</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本3>
            <文本4 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本4</Name>
              <Page isRef="22" />
              <Parent isRef="29" />
              <Text>支付宝</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本4>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </页眉2>
        <数据区1 Ref="35" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,19,4.4</ClientRectangle>
          <Components isList="true" count="26">
            <文本5 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本5</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>上班留存</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本5>
            <文本669 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.8,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本669</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_previousShiftAmount.cashAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本669>
            <文本6 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.6,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本6</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_previousShiftAmount.bankCardAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本6>
            <文本7 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.4,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本7</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_previousShiftAmount.wechatAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本7>
            <文本8 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.2,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本8</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_previousShiftAmount.alipayAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本8>
            <文本9 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本9</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>本班收款</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本9>
            <文本10 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.8,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本10</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_currentShiftAmount.cashAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本10>
            <文本11 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.6,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本11</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_currentShiftAmount.bankCardAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本11>
            <文本12 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.4,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本12</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_currentShiftAmount.wechatAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本12>
            <文本13 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.2,0.8,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本13</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_currentShiftAmount.alipayAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本13>
            <文本14 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本14</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>本班合计在手金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本14>
            <文本15 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.8,1.6,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本15</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_totalAmount.cashAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本15>
            <文本16 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.4,1.6,1.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本16</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>交班</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本16>
            <文本17 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,2.4,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本17</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>上缴金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本17>
            <文本18 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,3.2,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本18</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>本班留存</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本18>
            <文本19 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.8,2.4,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本19</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_handoverAmount.cashAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本19>
            <文本20 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.8,3.2,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本20</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_remainingAmount.cashAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本20>
            <文本21 Ref="53" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.6,1.6,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本21</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_totalAmount.bankCardAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本21>
            <文本22 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.4,1.6,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本22</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_totalAmount.wechatAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本22>
            <文本23 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.2,1.6,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本23</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_totalAmount.alipayAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本23>
            <文本24 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.6,2.4,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本24</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_handoverAmount.bankCardAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本24>
            <文本25 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.4,2.4,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本25</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_handoverAmount.wechatAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本25>
            <文本26 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.2,2.4,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本26</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_handoverAmount.alipayAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本26>
            <文本27 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.6,3.2,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本27</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_remainingAmount.bankCardAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本27>
            <文本28 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.4,3.2,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本28</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_remainingAmount.wechatAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本28>
            <文本29 Ref="61" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.2,3.2,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>5,5,0,0</Margins>
              <Name>文本29</Name>
              <Page isRef="22" />
              <Parent isRef="35" />
              <Text>{root_data_handoverDetail_remainingAmount.alipayAmount}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本29>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_handoverDetail</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区1</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
          <Sort isList="true" count="0" />
        </数据区1>
        <栏首1 Ref="62" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11.2,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <文本30 Ref="63" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本30</Name>
              <Page isRef="22" />
              <Parent isRef="62" />
              <Text>消费科目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本30>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首1</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </栏首1>
        <页眉3 Ref="64" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,12.8,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本31 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本31</Name>
              <Page isRef="22" />
              <Parent isRef="64" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本31>
            <文本32 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本32</Name>
              <Page isRef="22" />
              <Parent isRef="64" />
              <Text>科目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本32>
            <文本33 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.6,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本33</Name>
              <Page isRef="22" />
              <Parent isRef="64" />
              <Text>今日发生</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本33>
            <文本34 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本34</Name>
              <Page isRef="22" />
              <Parent isRef="64" />
              <Text>今日(结账)收回</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本34>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉3</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </页眉3>
        <数据区2 Ref="69" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,14.4,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本35 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本35</Name>
              <Page isRef="22" />
              <Parent isRef="69" />
              <Text>{Line}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </文本35>
            <文本36 Ref="71" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.4,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本36</Name>
              <Page isRef="22" />
              <Parent isRef="69" />
              <Text>{root_data_consumeSubjects.subjectName}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本36>
            <文本37 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.6,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_consumeSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本37</Name>
              <Page isRef="22" />
              <Parent isRef="69" />
              <Text>{root_data_consumeSubjects.amount}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本37>
            <文本46 Ref="73" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_consumeSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本46</Name>
              <Page isRef="22" />
              <Parent isRef="69" />
              <Text>{root_data_consumeSubjects.todaySettle}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本46>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_consumeSubjects</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区2</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
          <Sort isList="true" count="0" />
        </数据区2>
        <页脚1 Ref="74" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,16,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本48 Ref="75" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,8.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本48</Name>
              <Page isRef="22" />
              <Parent isRef="74" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本48>
            <文本49 Ref="76" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.6,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_consumeSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本49</Name>
              <Page isRef="22" />
              <Parent isRef="74" />
              <Text>{Sum(root_data_consumeSubjects.amount)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本49>
            <文本50 Ref="77" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierConsumption&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_consumeSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本50</Name>
              <Page isRef="22" />
              <Parent isRef="74" />
              <Text>{Sum(root_data_consumeSubjects.todaySettle)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本50>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚1</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </页脚1>
        <栏首2 Ref="78" type="Stimulsoft.Report.Components.StiColumnHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,17.6,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <文本38 Ref="79" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本38</Name>
              <Page isRef="22" />
              <Parent isRef="78" />
              <Text>付款科目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本38>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>栏首2</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </栏首2>
        <页眉4 Ref="80" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,19.2,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本39 Ref="81" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本39</Name>
              <Page isRef="22" />
              <Parent isRef="80" />
              <Text>序号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本39>
            <文本40 Ref="82" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本40</Name>
              <Page isRef="22" />
              <Parent isRef="80" />
              <Text>科目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本40>
            <文本41 Ref="83" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.6,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本41</Name>
              <Page isRef="22" />
              <Parent isRef="80" />
              <Text>今日发生</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本41>
            <文本42 Ref="84" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本42</Name>
              <Page isRef="22" />
              <Parent isRef="80" />
              <Text>今日(结账)收回</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本42>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉4</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </页眉4>
        <数据区3 Ref="85" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,20.8,19,0.8</ClientRectangle>
          <Components isList="true" count="4">
            <文本43 Ref="86" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本43</Name>
              <Page isRef="22" />
              <Parent isRef="85" />
              <Text>{Line}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>SystemVariables</Type>
              <VertAlignment>Center</VertAlignment>
            </文本43>
            <文本44 Ref="87" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.4,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>5,5,0,0</Margins>
              <Name>文本44</Name>
              <Page isRef="22" />
              <Parent isRef="85" />
              <Text>{root_data_paymentSubjects.subjectName}</Text>
              <TextBrush>[0:0:0]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本44>
            <文本45 Ref="88" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.6,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_paymentSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本45</Name>
              <Page isRef="22" />
              <Parent isRef="85" />
              <Text>{root_data_paymentSubjects.amount}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本45>
            <文本47 Ref="89" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_paymentSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本47</Name>
              <Page isRef="22" />
              <Parent isRef="85" />
              <Text>{root_data_paymentSubjects.todaySettle}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </文本47>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>root_data_paymentSubjects</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>数据区3</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
          <Sort isList="true" count="0" />
        </数据区3>
        <页脚2 Ref="90" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,22.4,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <文本51 Ref="91" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,8.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>文本51</Name>
              <Page isRef="22" />
              <Parent isRef="90" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本51>
            <文本52 Ref="92" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.6,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_paymentSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本52</Name>
              <Page isRef="22" />
              <Parent isRef="90" />
              <Text>{Sum(root_data_paymentSubjects.amount)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本52>
            <文本53 Ref="93" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.8,0,5.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>微软雅黑,10</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Hyperlink>/dist-report/index.html#/?name=StoreCashierPayment&amp;hcode={root_data.hcode}&amp;startDate={root_data.bizDate}&amp;endDate={root_data.bizDate}&amp;subCode={root_data_paymentSubjects.subCode}&amp;shiftNo={root_data.shiftNo}</Hyperlink>
              <Margins>5,5,0,0</Margins>
              <Name>文本53</Name>
              <Page isRef="22" />
              <Parent isRef="90" />
              <Text>{Sum(root_data_paymentSubjects.todaySettle)}</Text>
              <TextBrush>[0:176:240]</TextBrush>
              <TextOptions>,,,,WordWrap=True,A=0</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </文本53>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页脚2</Name>
          <Page isRef="22" />
          <Parent isRef="22" />
        </页脚2>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>fa79b351691443f5aeb6db9fe4b30e23</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>报表</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>7/29/2025 11:58:01 AM</ReportChanged>
  <ReportCreated>7/24/2025 1:41:22 PM</ReportCreated>
  <ReportFile>D:\11\2\aflower-pms-front\public\reports\handoverReportCashRealization.mrt</ReportFile>
  <ReportGuid>0b214b66fff94782b5af93b3fa4c5558</ReportGuid>
  <ReportName>报表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.3.5.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 报表 : Stimulsoft.Report.StiReport
    {
        public 报表()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>