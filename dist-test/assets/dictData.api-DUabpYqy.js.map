{"version": 3, "file": "dictData.api-DUabpYqy.js", "sources": ["../../src/api/modules/system/dict/dictData.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = 'admin-api/system/dict-data/get'\r\n/**\r\n * 常量接口\r\n */\r\nexport default {\r\n  /**\r\n   * 根据常量类型获取字典列表\r\n   */\r\n  list: (data: any) => api.get(`${BASE_PATH}/by-type`, { params: data }),\r\n  /**\r\n   * 批量获取字典数据\r\n   * @param dictTypes\r\n   */\r\n  getDictDataBatch: (dictTypes: string[]) =>\r\n    api.get(`${BASE_PATH}/batch`, {\r\n      params: {\r\n        dictTypes: dictTypes.join(','),\r\n      },\r\n    }),\r\n  /**\r\n   * 获得支付方式\r\n   */\r\n  getPayMode: () =>\r\n    api.get(`${BASE_PATH}/paymode`, {}),\r\n\r\n  /**\r\n   * 获得床型列表\r\n   */\r\n  getBedType: () =>\r\n    api.get(`${BASE_PATH}/bed-type`, {}),\r\n\r\n  /**\r\n   * 获得床型尺寸列表\r\n   */\r\n  getBedSize: () =>\r\n    api.get(`${BASE_PATH}/bed-size`, {}),\r\n\r\n  /**\r\n   * 获得星级列表\r\n   */\r\n  getStar: () =>\r\n    api.get(`${BASE_PATH}/star`, {}),\r\n\r\n  /**\r\n   * 获得酒店状态列表\r\n   */\r\n  getHotelStatus: () =>\r\n    api.get(`${BASE_PATH}/hotel-status`, {}),\r\n\r\n  /**\r\n   * 获得酒店服务项目列表\r\n   */\r\n  getHotelServices: () =>\r\n    api.get(`${BASE_PATH}/services`, {}),\r\n\r\n  /**\r\n   * 获得会员升级模式列表\r\n   */\r\n  getMemberUpmode: () =>\r\n    api.get(`${BASE_PATH}/upmode`, {}),\r\n}\r\n"], "names": ["BASE_PATH", "dict<PERSON>ata<PERSON><PERSON>", "list", "data", "api", "get", "params", "getDictDataBatch", "dictTypes", "join", "getPayMode", "getBedType", "getBedSize", "getStar", "getHotelStatus", "getHotelServices", "getMemberUpmode"], "mappings": "wCAEA,MAAMA,EAAY,iCAIHC,EAAA,CAIbC,KAAOC,GAAcC,EAAIC,IAAI,GAAGL,YAAqB,CAAEM,OAAQH,IAK/DI,iBAAmBC,GACjBJ,EAAIC,IAAI,GAAGL,UAAmB,CAC5BM,OAAQ,CACNE,UAAWA,EAAUC,KAAK,QAMhCC,WAAY,IACVN,EAAIC,IAAI,GAAGL,YAAqB,IAKlCW,WAAY,IACVP,EAAIC,IAAI,GAAGL,aAAsB,IAKnCY,WAAY,IACVR,EAAIC,IAAI,GAAGL,aAAsB,IAKnCa,QAAS,IACPT,EAAIC,IAAI,GAAGL,SAAkB,IAK/Bc,eAAgB,IACdV,EAAIC,IAAI,GAAGL,iBAA0B,IAKvCe,iBAAkB,IAChBX,EAAIC,IAAI,GAAGL,aAAsB,IAKnCgB,gBAAiB,IACfZ,EAAIC,IAAI,GAAGL,WAAoB,CAAE"}