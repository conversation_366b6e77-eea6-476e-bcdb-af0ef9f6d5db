import{d as e,aj as t,ai as o,b as s,y as a,aR as l,aq as d,u as i,o as r,c as p,f as m,w as c,aS as n,m as b,x as u,aT as h}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{d as g}from"./deposit.api-CnI8geOU.js";import{_ as f}from"./_plugin-vue_export-helper-BCo6x5W8.js";const P=e({__name:"depositCreate",setup(e,{expose:f}){const{t:P}=t(),_=o(),V=s(!1),k=s(),x=s({gcode:_.gcode,hcode:_.hcode,rNo:"",depositor:"",depositCode:"",goods:"",depositTime:"",depositOpt:"",claimant:"",claimantPhone:"",claimOpt:"",claimTime:"",state:"0",remark:"",depositPhone:""});a((()=>{}));const N=s({goods:[{required:!0,message:P("deposit.create.validation.goods"),trigger:"blur"}],depositor:[{required:!0,message:P("deposit.create.validation.depositor"),trigger:"blur"}]});return f({submit:()=>new Promise((e=>{k.value&&k.value.validate((t=>{t&&g.create(x.value).then((()=>{l.success({message:P("deposit.create.success.add"),center:!0}),e()}))}))}))}),(e,t)=>{const o=n,s=b,a=u,l=h;return d((r(),p("div",null,[m(a,{ref_key:"formRef",ref:k,model:i(x),rules:i(N),"label-width":"150px","label-suffix":"："},{default:c((()=>[m(s,{label:i(P)("deposit.create.labels.goods"),prop:"goods"},{default:c((()=>[m(o,{modelValue:i(x).goods,"onUpdate:modelValue":t[0]||(t[0]=e=>i(x).goods=e),type:"textarea",rows:3,placeholder:i(P)("deposit.create.placeholders.goods"),maxlength:"200"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(s,{label:i(P)("deposit.create.labels.depositor"),prop:"depositor"},{default:c((()=>[m(o,{modelValue:i(x).depositor,"onUpdate:modelValue":t[1]||(t[1]=e=>i(x).depositor=e),placeholder:i(P)("deposit.create.placeholders.depositor"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(s,{label:i(P)("deposit.create.labels.depositPhone"),prop:"depositPhone"},{default:c((()=>[m(o,{modelValue:i(x).depositPhone,"onUpdate:modelValue":t[2]||(t[2]=e=>i(x).depositPhone=e),placeholder:i(P)("deposit.create.placeholders.depositPhone"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(s,{label:i(P)("deposit.create.labels.depositCode"),prop:"depositCode"},{default:c((()=>[m(o,{modelValue:i(x).depositCode,"onUpdate:modelValue":t[3]||(t[3]=e=>i(x).depositCode=e),placeholder:i(P)("deposit.create.placeholders.depositCode"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(s,{label:i(P)("deposit.create.labels.roomNo")},{default:c((()=>[m(o,{modelValue:i(x).rNo,"onUpdate:modelValue":t[4]||(t[4]=e=>i(x).rNo=e),placeholder:i(P)("deposit.create.placeholders.roomNo"),maxlength:"30"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),m(s,{label:i(P)("deposit.create.labels.remark"),prop:"remark"},{default:c((()=>[m(o,{modelValue:i(x).remark,"onUpdate:modelValue":t[5]||(t[5]=e=>i(x).remark=e),type:"textarea",rows:3,placeholder:i(P)("deposit.create.placeholders.remark"),maxlength:"200"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1},8,["model","rules"])])),[[l,i(V)]])}}});function _(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{deposit:{create:{validation:{goods:{t:0,b:{t:2,i:[{t:3}],s:"Please enter deposit items"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"Please enter depositor name"}}},success:{add:{t:0,b:{t:2,i:[{t:3}],s:"Added successfully"}}},labels:{goods:{t:0,b:{t:2,i:[{t:3}],s:"Item Name"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"Depositor Name"}},depositPhone:{t:0,b:{t:2,i:[{t:3}],s:"Depositor Phone"}},depositCode:{t:0,b:{t:2,i:[{t:3}],s:"Deposit Code"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"Room No."}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}}},placeholders:{goods:{t:0,b:{t:2,i:[{t:3}],s:"Please enter item information"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"Please enter depositor"}},depositPhone:{t:0,b:{t:2,i:[{t:3}],s:"Please enter depositor phone"}},depositCode:{t:0,b:{t:2,i:[{t:3}],s:"Please enter deposit code"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"Please enter room number"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Please enter key features of the deposit item, such as color, size, quantity, etc."}}}}}},"zh-cn":{deposit:{create:{validation:{goods:{t:0,b:{t:2,i:[{t:3}],s:"请输入寄存物品"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"请输入寄存人姓名"}}},success:{add:{t:0,b:{t:2,i:[{t:3}],s:"添加成功"}}},labels:{goods:{t:0,b:{t:2,i:[{t:3}],s:"物品名称"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"寄存人姓名"}},depositPhone:{t:0,b:{t:2,i:[{t:3}],s:"寄存人电话"}},depositCode:{t:0,b:{t:2,i:[{t:3}],s:"寄存凭证号"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}}},placeholders:{goods:{t:0,b:{t:2,i:[{t:3}],s:"请输入物品信息"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"请输入寄存人"}},depositPhone:{t:0,b:{t:2,i:[{t:3}],s:"请输入寄存人电话"}},depositCode:{t:0,b:{t:2,i:[{t:3}],s:"请输入寄存凭证号"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"请输入房号"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"请输入寄存物品的关键特征，如颜色、大小、数量等"}}}}}},km:{deposit:{create:{validation:{goods:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលវត្ថុដាក់បញ្ចាំ"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលឈ្មោះអ្នកដាក់បញ្ចាំ"}}},success:{add:{t:0,b:{t:2,i:[{t:3}],s:"បានបន្ថែមដោយជោគជ័យ"}}},labels:{goods:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះវត្ថុ"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះអ្នកដាក់បញ្ចាំ"}},depositPhone:{t:0,b:{t:2,i:[{t:3}],s:"លេខទូរស័ព្ទអ្នកដាក់បញ្ចាំ"}},depositCode:{t:0,b:{t:2,i:[{t:3}],s:"លេខបញ្ជាក់ការដាក់បញ្ចាំ"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}}},placeholders:{goods:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលព័ត៌មានវត្ថុ"}},depositor:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលអ្នកដាក់បញ្ចាំ"}},depositPhone:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលលេខទូរស័ព្ទអ្នកដាក់បញ្ចាំ"}},depositCode:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលលេខបញ្ជាក់ការដាក់បញ្ចាំ"}},roomNo:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលលេខបន្ទប់"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលលក្ខណៈសំខាន់នៃវត្ថុដាក់បញ្ចាំ ដូចជាពណ៌ ទំហំ បរិមាណ ជាដើម"}}}}}}}})}_(P);const V=f(P,[["__scopeId","data-v-b0360159"]]);export{V as default};
//# sourceMappingURL=depositCreate-CeD-WR4r.js.map
