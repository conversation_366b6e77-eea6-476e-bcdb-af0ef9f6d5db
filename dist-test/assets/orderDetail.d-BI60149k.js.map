{"version": 3, "file": "orderDetail.d-BI60149k.js", "sources": ["../../src/views/order/info/components/orderdetail/orderDetail.d.ts"], "sourcesContent": ["/** 订单详情 */\r\ndeclare namespace orderDetail {\r\n  /** 查询列表条件 */\r\n  interface queryParams extends PageQuery, hgCode {\r\n    startTime?: string\r\n    endTime?: string\r\n    rtCode?: string\r\n  }\r\n  /**\r\n   * 获根据会员代码获取会员详情\r\n   */\r\n  interface MemberDetail {\r\n    name: string\r\n    mtName: string\r\n    phone: string\r\n    storeCardBalance: number\r\n  }\r\n  /** 订单价格 */\r\n  interface OrderPrice {\r\n    price: number\r\n    priceDate: string\r\n    vipPrice: number\r\n    week: number\r\n  }\r\n  /** 价格类型 */\r\n  interface pricesType {\r\n    /** 房型代码 */\r\n    rtCode: string\r\n    /** 房型名称 */\r\n    rtName: string\r\n    /** 房间代码 */\r\n    rCode: string\r\n    /** 房号 */\r\n    rNo: string\r\n    /** 原始价格 */\r\n    price: number\r\n    /** 调整后的价格 */\r\n    vipPrice: number\r\n    /** 价格类型 0 放盘价 1 手工价 */\r\n    priceType: string\r\n    /** 价格开始时间 */\r\n    priceStartTime: string\r\n    /** 价格结束时间 */\r\n    priceEndTime: string\r\n    /** 是否过夜审 0:否 1：是 */\r\n    isNight: string\r\n  }\r\n  /** 类型列表 */\r\n  interface typeList {\r\n    code: string\r\n    label: string\r\n  }\r\n  /** 会员 */\r\n  interface membersType {\r\n    mcode: string\r\n    name: string\r\n    phone: string\r\n    idNo: string\r\n  }\r\n  /** 会员提交 */\r\n  interface memberFormType {\r\n    guestSrcType: string\r\n    /** 手机号 */\r\n    phone?: string\r\n    mcode: string\r\n    agentCode: string\r\n    protocolCode: string\r\n    loading?: boolean\r\n  }\r\n  /** 展示入账页面form */\r\n  interface orderVisibleType {\r\n    accountOrderNo: string\r\n    togetherCode: string\r\n    tabId?: string\r\n    type: string\r\n  }\r\n  /** 改价窗口 */\r\n  interface ParameterMaps {\r\n    bkNum: number\r\n    price: number\r\n    priceDate: string\r\n    priceStrategyCode: string\r\n    roomBkNum: number\r\n    vipPrice: number\r\n    week: number\r\n  }\r\n  /** 获取早餐 */\r\n  interface tableData {\r\n    bkNum?: number //赠早\r\n    buyedBkNum?: number //购早\r\n    priceDate?: string //日期\r\n    roomBkNum?: number //房包早\r\n    buyBkNum?: number //总购买数\r\n    bkPrice?: number //价格\r\n  }\r\n\r\n  /* 下面的方法(脑力精髓-简洁-代码量极少)*/\r\n  interface TableKeyTypes {\r\n    eximType: string\r\n    dndFeeType: string\r\n    period: string\r\n    '20GP': string\r\n    '40GP': string\r\n    '40HQ': string\r\n    currency: string\r\n    [key: string]: string\r\n  }\r\n\r\n  interface SpanMethodProps {\r\n    row: TableKeyTypes\r\n    column: Record<'property', keyof TableKeyTypes>\r\n    rowIndex: number\r\n    columnIndex: number\r\n  }\r\n  /** 入住类型 */\r\n  interface OrderCheckinTypeRespVO {\r\n    /** 入住类型 */\r\n    checkinType?: string\r\n    /** 入住类型名称 */\r\n    checkinTypeName?: string\r\n    /** 钟点房集合 */\r\n    hourList?: HourRoom[]\r\n  }\r\n  /** 钟点房集合 */\r\n  interface HourRoom {\r\n    /** 钟点房代码 */\r\n    hourCode?: string\r\n    /** 钟点房名称 */\r\n    hourName?: string\r\n  }\r\n  /** 获得订单的能修改入住类型 */\r\n  interface getCheckinTypes extends hgCode {\r\n    /** 订单号 */\r\n    orderNo?: string\r\n  }\r\n  /** 更新订单的入住类型类型 */\r\n  interface updateCheckinTypes extends getCheckinTypes {\r\n    /** 入住类型 */\r\n    checkinType?: string\r\n    /** 钟点房代码 */\r\n    hourCode?: string\r\n  }\r\n}\r\nexport = orderDetail\r\n"], "names": ["require_orderDetail_d_03Z", "exports", "module", "orderDetail"], "mappings": "qCA+IAA,KAAA,CAAA,mCAAAC,EAAAC,GAAAA,EAASD,QAAAE,WAAA"}