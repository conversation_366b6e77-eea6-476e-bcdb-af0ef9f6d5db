{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-CwaeZmUr.js", "sources": ["../../src/layouts/components/BackTop/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\ndefineOptions({\r\n  name: 'BackTop',\r\n})\r\n\r\nconst transitionClass = {\r\n  enterActiveClass: 'ease-out duration-300',\r\n  enterFromClass: 'opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95',\r\n  enterToClass: 'opacity-100 translate-y-0 lg-scale-100',\r\n  leaveActiveClass: 'ease-in duration-200',\r\n  leaveFromClass: 'opacity-100 translate-y-0 lg-scale-100',\r\n  leaveToClass: 'opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95',\r\n}\r\n\r\nonMounted(() => {\r\n  window.addEventListener('scroll', handleScroll)\r\n  handleScroll()\r\n})\r\n\r\nonBeforeUnmount(() => {\r\n  window.removeEventListener('scroll', handleScroll)\r\n})\r\n\r\nconst scrollTop = ref<number | null>(null)\r\nfunction handleScroll() {\r\n  scrollTop.value = document.documentElement.scrollTop\r\n}\r\n\r\nfunction handleClick() {\r\n  document.documentElement.scrollTo({\r\n    top: 0,\r\n    behavior: 'smooth',\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <Teleport to=\"body\">\r\n    <Transition v-bind=\"transitionClass\">\r\n      <div v-if=\"scrollTop && scrollTop >= 200\" class=\"fixed bottom-4 right-4 z-1000 h-12 w-12 flex cursor-pointer items-center justify-center rounded-full bg-white shadow-lg ring-1 ring-stone-3 ring-inset dark-bg-dark hover-bg-stone-1 dark-ring-stone-7 dark-hover-bg-dark/50\" @click=\"handleClick\">\r\n        <SvgIcon name=\"i-icon-park-outline:to-top-one\" :size=\"24\" />\r\n      </div>\r\n    </Transition>\r\n  </Teleport>\r\n</template>\r\n"], "names": ["transitionClass", "enterActiveClass", "enterFromClass", "enterToClass", "leaveActiveClass", "leaveFromClass", "leaveToClass", "onMounted", "window", "addEventListener", "handleScroll", "onBeforeUnmount", "removeEventListener", "scrollTop", "ref", "value", "document", "documentElement", "handleClick", "scrollTo", "top", "behavior"], "mappings": "yLAKA,MAAMA,EAAkB,CACtBC,iBAAkB,wBAClBC,eAAgB,uDAChBC,aAAc,yCACdC,iBAAkB,uBAClBC,eAAgB,yCAChBC,aAAc,wDAGhBC,GAAU,KACDC,OAAAC,iBAAiB,SAAUC,GACrBA,GAAA,IAGfC,GAAgB,KACPH,OAAAI,oBAAoB,SAAUF,EAAY,IAG7C,MAAAG,EAAYC,EAAmB,MACrC,SAASJ,IACGG,EAAAE,MAAQC,SAASC,gBAAgBJ,SAAA,CAG7C,SAASK,IACPF,SAASC,gBAAgBE,SAAS,CAChCC,IAAK,EACLC,SAAU,UACX"}