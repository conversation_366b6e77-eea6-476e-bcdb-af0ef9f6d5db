{"version": 3, "file": "BatchRoomStateConfig-ToU7IdaO.js", "sources": ["../../src/views/room/realtime/components/BatchRoomStateConfig.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"pleaseSelectCleaner\": \"Please select a cleaner\",\r\n    \"pleaseSelectAtLeastOneRoom\": \"Please select at least one room\",\r\n    \"batchOperationSuccess\": \"Batch operation successful!\",\r\n    \"batchCleanDirty\": \"Batch Clean/Dirty\",\r\n    \"batchClean\": \"Batch Clean\",\r\n    \"batchDirty\": \"Batch Dirty\",\r\n    \"selectCleaner\": \"Select Cleaner\",\r\n    \"close\": \"Close\",\r\n    \"save\": \"Save\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"pleaseSelectCleaner\": \"请选择保洁员\",\r\n    \"pleaseSelectAtLeastOneRoom\":\"请至少选择一间房\",\r\n    \"batchOperationSuccess\": \"批量操作成功！\",\r\n    \"batchCleanDirty\": \"批量置净置脏\",\r\n    \"batchClean\": \"批量置净\",\r\n    \"batchDirty\": \"批量置脏\",\r\n    \"selectCleaner\": \"选择保洁员\",\r\n    \"close\": \"关闭\",\r\n    \"save\": \"保存\"\r\n  },\r\n  \"km\": {\r\n    \"pleaseSelectCleaner\": \"សូមជ្រើសរើសអ្នកសម្អាត\",\r\n    \"pleaseSelectAtLeastOneRoom\": \"សូមជ្រើសរើសយ៉ាងហោចណាស់មួយបន្ទប់\",\r\n    \"batchOperationSuccess\": \"ប្រតិបត្តិការជាក្រុមជោគជ័យ!\",\r\n    \"batchCleanDirty\": \"ក្រុមស្អាត/ក្រុមខ្វះ\",\r\n    \"batchClean\": \"ក្រុមស្អាត\",\r\n    \"batchDirty\": \"ក្រុមខ្វះ\",\r\n    \"selectCleaner\": \"ជ្រើសរើសអ្នកសម្អាត\",\r\n    \"close\": \"បិទ\",\r\n    \"save\": \"រក្សាទុក\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { roomApi } from '@/api/modules/index'\r\nimport userApi from '@/api/modules/system/user/user.api'\r\nimport useUserStore from '@/store/modules/user'\r\n\r\nimport CleanDirty from './cleanDirty.vue'\r\n/**\r\n * 批量置净置脏设置\r\n */\r\nconst emits = defineEmits<{\r\n  success: []\r\n}>()\r\n\r\nonMounted(() => {})\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\nconst cleanRef = ref()\r\nconst dirtyRef = ref()\r\nconst loading = ref(false)\r\nconst myVisible = ref(false)\r\nconst active = ref('clean')\r\nconst options = ref<{ username: string; nickname: string }[]>([])\r\nconst cleaner = ref(undefined)\r\nfunction handleClick() {}\r\nasync function getCleaner() {\r\n  await userApi\r\n    .getCleanerList({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        options.value = res.data\r\n      } else {\r\n        ElMessage.error(res.msg)\r\n      }\r\n    })\r\n}\r\nasync function onSubmit() {\r\n  if (active.value === 'clean' && !cleaner.value) {\r\n    return ElMessage.warning(t('pleaseSelectCleaner'))\r\n  }\r\n  const data = active.value === 'clean' ? cleanRef.value.submit() : dirtyRef.value.submit()\r\n  if (data.length < 1) {\r\n    return ElMessage.warning(t('pleaseSelectAtLeastOneRoom'))\r\n  }\r\n  loading.value = true\r\n  await roomApi\r\n    .updateBatchClean({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      rCodes: data,\r\n      state: active.value === 'clean' ? '1' : '0',\r\n      cleaner: cleaner.value,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('batchOperationSuccess'))\r\n        myVisible.value = false\r\n        active.value = 'clean'\r\n        emits('success')\r\n      } else {\r\n        ElMessage.error(res.msg)\r\n      }\r\n    })\r\n    .finally(() => {\r\n      loading.value = false\r\n    })\r\n}\r\n\r\nfunction onCancel() {\r\n  myVisible.value = false\r\n}\r\nasync function open() {\r\n  myVisible.value = true\r\n  getCleaner()\r\n}\r\ndefineExpose({\r\n  open,\r\n})\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-drawer v-model=\"myVisible\" :z-index=\"2000\" :title=\"t('batchCleanDirty')\" size=\"600px\" :close-on-click-modal=\"true\" destroy-on-close :modal=\"false\">\r\n      <el-tabs v-model=\"active\" class=\"demo-tabs\" @tab-click=\"handleClick\">\r\n        <el-tab-pane :label=\"t('batchClean')\" name=\"clean\">\r\n          <CleanDirty ref=\"cleanRef\" type=\"clean\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane :label=\"t('batchDirty')\" name=\"dirty\">\r\n          <CleanDirty ref=\"dirtyRef\" type=\"dirty\" />\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <template #footer>\r\n        <span>\r\n          <span v-if=\"active === 'clean'\">\r\n            {{ t('selectCleaner') }}\r\n            <el-select v-model=\"cleaner\" :placeholder=\"t('pleaseSelectCleaner')\" style=\"width: 240px\">\r\n              <el-option v-for=\"item in options\" :key=\"item.username\" :label=\"item.nickname\" :value=\"item.username\" />\r\n            </el-select>\r\n          </span>\r\n        </span>\r\n        <span>\r\n          <el-button size=\"large\" @click=\"onCancel\">\r\n            {{ t('close') }}\r\n          </el-button>\r\n          <el-button type=\"primary\" size=\"large\" :loading=\"loading\" @click=\"onSubmit\">\r\n            {{ t('save') }}\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n:deep(.el-drawer__header) {\r\n  margin-bottom: 0;\r\n}\r\n\r\n:deep(.el-drawer__footer) {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"], "names": ["emits", "__emit", "onMounted", "userStore", "useUserStore", "t", "useI18n", "cleanRef", "ref", "dirtyRef", "loading", "myVisible", "active", "options", "cleaner", "handleClick", "async", "onSubmit", "value", "ElMessage", "warning", "data", "submit", "length", "roomApi", "updateBatchClean", "gcode", "hcode", "rCodes", "state", "then", "res", "code", "success", "error", "msg", "finally", "onCancel", "__expose", "open", "userApi", "getCleanerList", "get<PERSON><PERSON><PERSON>"], "mappings": "m3BA+CA,MAAMA,EAAQC,EAIdC,GAAU,SACV,MAAMC,EAAYC,KACZC,EAAEA,GAAMC,IACRC,EAAWC,IACXC,EAAWD,IACXE,EAAUF,GAAI,GACdG,EAAYH,GAAI,GAChBI,EAASJ,EAAI,SACbK,EAAUL,EAA8C,IACxDM,EAAUN,OAAI,GACpB,SAASO,IAAc,CAevBC,eAAeC,IACb,GAAqB,UAAjBL,EAAOM,QAAsBJ,EAAQI,MACvC,OAAOC,EAAUC,QAAQf,EAAE,wBAEvB,MAAAgB,EAAwB,UAAjBT,EAAOM,MAAoBX,EAASW,MAAMI,SAAWb,EAASS,MAAMI,SAC7E,GAAAD,EAAKE,OAAS,EAChB,OAAOJ,EAAUC,QAAQf,EAAE,+BAE7BK,EAAQQ,OAAQ,QACVM,EACHC,iBAAiB,CAChBC,MAAOvB,EAAUuB,MACjBC,MAAOxB,EAAUwB,MACjBC,OAAQP,EACRQ,MAAwB,UAAjBjB,EAAOM,MAAoB,IAAM,IACxCJ,QAASA,EAAQI,QAElBY,MAAMC,IACY,IAAbA,EAAIC,MACIb,EAAAc,QAAQ5B,EAAE,0BACpBM,EAAUO,OAAQ,EAClBN,EAAOM,MAAQ,QACflB,EAAM,YAEImB,EAAAe,MAAMH,EAAII,IAAG,IAG1BC,SAAQ,KACP1B,EAAQQ,OAAQ,CAAA,GACjB,CAGL,SAASmB,IACP1B,EAAUO,OAAQ,CAAA,QAMPoB,EAAA,CACXC,KALFvB,iBACEL,EAAUO,OAAQ,EAlDpBF,uBACQwB,EACHC,eAAe,CACdf,MAAOvB,EAAUuB,MACjBC,MAAOxB,EAAUwB,QAElBG,MAAMC,IACY,IAAbA,EAAIC,KACNnB,EAAQK,MAAQa,EAAIV,KAEVF,EAAAe,MAAMH,EAAII,IAAG,GAE1B,CAuCQO,EAAA"}