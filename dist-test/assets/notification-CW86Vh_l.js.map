{"version": 3, "file": "notification-CW86Vh_l.js", "sources": ["../../src/views/personal/notification.vue"], "sourcesContent": ["<route lang=\"yaml\">\r\nname: personalNotification\r\nmeta:\r\n  title: 通知中心\r\n</route>\r\n\r\n<script setup lang=\"ts\">\r\nimport useNotificationStore from '@/store/modules/notification'\r\n\r\ndefineOptions({\r\n  name: 'PersonalNotification',\r\n})\r\n\r\nconst notificationStore = useNotificationStore()\r\n\r\nfunction messagePlus() {\r\n  notificationStore.$patch((state) => {\r\n    state.message += 1\r\n  })\r\n}\r\nfunction messageMinus() {\r\n  notificationStore.$patch((state) => {\r\n    state.message -= state.message > 0 ? 1 : 0\r\n  })\r\n}\r\n\r\nfunction todoPlus() {\r\n  notificationStore.$patch((state) => {\r\n    state.todo += 1\r\n  })\r\n}\r\nfunction todoMinus() {\r\n  notificationStore.$patch((state) => {\r\n    state.todo -= state.todo > 0 ? 1 : 0\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <PageHeader title=\"通知中心\" content=\"本页面仅模拟右上角通知数变化，具体业务逻辑请到 /src/store/modules/notification.ts 文件中编写\" />\r\n    <PageMain title=\"消息\">\r\n      <HButton @click=\"messagePlus\">\r\n        <SvgIcon name=\"i-ep:plus\" />\r\n        1\r\n      </HButton>\r\n      <HButton ml-2 @click=\"messageMinus\">\r\n        <SvgIcon name=\"i-ep:minus\" />\r\n        1\r\n      </HButton>\r\n    </PageMain>\r\n    <PageMain title=\"待办\">\r\n      <HButton @click=\"todoPlus\">\r\n        <SvgIcon name=\"i-ep:plus\" />\r\n        1\r\n      </HButton>\r\n      <HButton ml-2 @click=\"todoMinus\">\r\n        <SvgIcon name=\"i-ep:minus\" />\r\n        1\r\n      </HButton>\r\n    </PageMain>\r\n  </div>\r\n</template>\r\n"], "names": ["notificationStore", "useNotificationStore", "messagePlus", "$patch", "state", "message", "messageMinus", "todoPlus", "todo", "todoMinus"], "mappings": "gZAaA,MAAMA,EAAoBC,IAE1B,SAASC,IACWF,EAAAG,QAAQC,IACxBA,EAAMC,SAAW,CAAA,GAClB,CAEH,SAASC,IACWN,EAAAG,QAAQC,IACxBA,EAAMC,SAAWD,EAAMC,QAAU,EAAI,EAAI,CAAA,GAC1C,CAGH,SAASE,IACWP,EAAAG,QAAQC,IACxBA,EAAMI,MAAQ,CAAA,GACf,CAEH,SAASC,IACWT,EAAAG,QAAQC,IACxBA,EAAMI,MAAQJ,EAAMI,KAAO,EAAI,EAAI,CAAA,GACpC"}