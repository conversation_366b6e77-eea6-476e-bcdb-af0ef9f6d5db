{"version": 3, "file": "retailGoods.api-CPINo1es.js", "sources": ["../../src/api/modules/pms/goods/retailGoods.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n\r\nconst BASE_PATH = '/admin-api/erp'\r\n/** 零售商品 */\r\nexport default {\r\n  /** 零售商品列表-分页 */\r\n  pageList: (data: any) =>\r\n    api.get(`${BASE_PATH}/product/page/stock`, { params: data }),\r\n\r\n  /** 零售商品列表 */\r\n  list: (data: any) =>\r\n    api.get(`${BASE_PATH}/product/page/pay-stock`, { params: data }),\r\n\r\n  /** 获得分类列表 */\r\n  getProductListCategory: (data: any) =>\r\n    api.get(`${BASE_PATH}/product-category/list`, { params: data }),\r\n\r\n  /** 获得产品分类精简列表 */\r\n  getProductSimpleListCategory: (data: any) =>\r\n    api.get(`${BASE_PATH}/product-category/simple-list`, { params: data }),\r\n  /** 创建分类 */\r\n  createProductCategory: (data: any) =>\r\n    api.post(`${BASE_PATH}/product-category/create`, data),\r\n  /** 更新分类 */\r\n  updateProductCategory: (data: any) =>\r\n    api.put(`${BASE_PATH}/product-category/update`, data),\r\n  /** 删除分类 */\r\n  deleteProductCategory: (data: any) =>\r\n    api.delete(`${BASE_PATH}/product-category/delete`, { params: data }),\r\n\r\n  /** 创建产品 */\r\n  createProduct: (data: any) => api.post(`${BASE_PATH}/product/create`, data),\r\n  /** 更新产品 */\r\n  updateProduct: (data: any) => api.put(`${BASE_PATH}/product/update`, data),\r\n  /** 产品修改状态 */\r\n  changeStatus: (data: any) =>\r\n    api.put(`${BASE_PATH}/retail-goods/update-status`, data),\r\n  /** 删除产品 */\r\n  deleteProduct: (data: any) =>\r\n    api.delete(`${BASE_PATH}/product/delete`, { params: data }),\r\n\r\n  /** 获得产品单位精简列表 */\r\n  getSimpleList: (data: any) =>\r\n    api.get(`${BASE_PATH}/product-unit/simple-list`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "retailGoodsApi", "pageList", "data", "api", "get", "params", "list", "getProductListCategory", "getProductSimpleListCategory", "createProductCategory", "post", "updateProductCategory", "put", "deleteProductCategory", "delete", "createProduct", "updateProduct", "changeStatus", "deleteProduct", "getSimpleList"], "mappings": "wCAEA,MAAMA,EAAY,iBAEHC,EAAA,CAEbC,SAAWC,GACTC,EAAIC,IAAI,GAAGL,uBAAgC,CAAEM,OAAQH,IAGvDI,KAAOJ,GACLC,EAAIC,IAAI,GAAGL,2BAAoC,CAAEM,OAAQH,IAG3DK,uBAAyBL,GACvBC,EAAIC,IAAI,GAAGL,0BAAmC,CAAEM,OAAQH,IAG1DM,6BAA+BN,GAC7BC,EAAIC,IAAI,GAAGL,iCAA0C,CAAEM,OAAQH,IAEjEO,sBAAwBP,GACtBC,EAAIO,KAAK,GAAGX,4BAAqCG,GAEnDS,sBAAwBT,GACtBC,EAAIS,IAAI,GAAGb,4BAAqCG,GAElDW,sBAAwBX,GACtBC,EAAIW,OAAO,GAAGf,4BAAqC,CAAEM,OAAQH,IAG/Da,cAAgBb,GAAcC,EAAIO,KAAK,GAAGX,mBAA4BG,GAEtEc,cAAgBd,GAAcC,EAAIS,IAAI,GAAGb,mBAA4BG,GAErEe,aAAef,GACbC,EAAIS,IAAI,GAAGb,+BAAwCG,GAErDgB,cAAgBhB,GACdC,EAAIW,OAAO,GAAGf,mBAA4B,CAAEM,OAAQH,IAGtDiB,cAAgBjB,GACdC,EAAIC,IAAI,GAAGL,6BAAsC,CAAEM,OAAQH"}