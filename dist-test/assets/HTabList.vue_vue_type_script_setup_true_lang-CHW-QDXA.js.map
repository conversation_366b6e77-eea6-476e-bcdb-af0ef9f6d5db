{"version": 3, "file": "HTabList.vue_vue_type_script_setup_true_lang-CHW-QDXA.js", "sources": ["../../node_modules/@headlessui/vue/dist/internal/focus-sentinel.js", "../../node_modules/@headlessui/vue/dist/components/tabs/tabs.js", "../../src/layouts/ui-kit/HTabList.vue"], "sourcesContent": ["import{defineComponent as i,h as m,ref as f}from\"vue\";import{Features as l,Hidden as F}from'./hidden.js';let d=i({props:{onFocus:{type:Function,required:!0}},setup(t){let n=f(!0);return()=>n.value?m(F,{as:\"button\",type:\"button\",features:l.Focusable,onFocus(o){o.preventDefault();let e,a=50;function r(){var u;if(a--<=0){e&&cancelAnimationFrame(e);return}if((u=t.onFocus)!=null&&u.call(t)){n.value=!1,cancelAnimationFrame(e);return}e=requestAnimationFrame(r)}e=requestAnimationFrame(r)}}):null}});export{d as FocusSentinel};\n", "import{computed as v,defineComponent as L,Fragment as z,h as A,inject as j,onMounted as F,onUnmounted as K,provide as N,ref as P,watch as _,watchEffect as J}from\"vue\";import{useId as $}from'../../hooks/use-id.js';import{useResolveButtonType as Q}from'../../hooks/use-resolve-button-type.js';import{FocusSentinel as V}from'../../internal/focus-sentinel.js';import{Hidden as X}from'../../internal/hidden.js';import{Keys as S}from'../../keyboard.js';import{dom as f}from'../../utils/dom.js';import{Focus as g,focusIn as D,FocusResult as B,sortByDomNode as k}from'../../utils/focus-management.js';import{match as H}from'../../utils/match.js';import{microTask as Y}from'../../utils/micro-task.js';import{getOwnerDocument as Z}from'../../utils/owner.js';import{Features as q,omit as ee,render as M}from'../../utils/render.js';var te=(s=>(s[s.Forwards=0]=\"Forwards\",s[s.Backwards=1]=\"Backwards\",s))(te||{}),le=(d=>(d[d.Less=-1]=\"Less\",d[d.Equal=0]=\"Equal\",d[d.Greater=1]=\"Greater\",d))(le||{});let U=Symbol(\"TabsContext\");function C(a){let b=j(U,null);if(b===null){let s=new Error(`<${a} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(s,C),s}return b}let G=Symbol(\"TabsSSRContext\"),me=L({name:\"TabGroup\",emits:{change:a=>!0},props:{as:{type:[Object,String],default:\"template\"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(a,{slots:b,attrs:s,emit:d}){var E;let i=P((E=a.selectedIndex)!=null?E:a.defaultIndex),l=P([]),r=P([]),p=v(()=>a.selectedIndex!==null),R=v(()=>p.value?a.selectedIndex:i.value);function y(t){var c;let n=k(u.tabs.value,f),o=k(u.panels.value,f),e=n.filter(I=>{var m;return!((m=f(I))!=null&&m.hasAttribute(\"disabled\"))});if(t<0||t>n.length-1){let I=H(i.value===null?0:Math.sign(t-i.value),{[-1]:()=>1,[0]:()=>H(Math.sign(t),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0}),m=H(I,{[0]:()=>n.indexOf(e[0]),[1]:()=>n.indexOf(e[e.length-1])});m!==-1&&(i.value=m),u.tabs.value=n,u.panels.value=o}else{let I=n.slice(0,t),h=[...n.slice(t),...I].find(W=>e.includes(W));if(!h)return;let O=(c=n.indexOf(h))!=null?c:u.selectedIndex.value;O===-1&&(O=u.selectedIndex.value),i.value=O,u.tabs.value=n,u.panels.value=o}}let u={selectedIndex:v(()=>{var t,n;return(n=(t=i.value)!=null?t:a.defaultIndex)!=null?n:null}),orientation:v(()=>a.vertical?\"vertical\":\"horizontal\"),activation:v(()=>a.manual?\"manual\":\"auto\"),tabs:l,panels:r,setSelectedIndex(t){R.value!==t&&d(\"change\",t),p.value||y(t)},registerTab(t){var o;if(l.value.includes(t))return;let n=l.value[i.value];if(l.value.push(t),l.value=k(l.value,f),!p.value){let e=(o=l.value.indexOf(n))!=null?o:i.value;e!==-1&&(i.value=e)}},unregisterTab(t){let n=l.value.indexOf(t);n!==-1&&l.value.splice(n,1)},registerPanel(t){r.value.includes(t)||(r.value.push(t),r.value=k(r.value,f))},unregisterPanel(t){let n=r.value.indexOf(t);n!==-1&&r.value.splice(n,1)}};N(U,u);let T=P({tabs:[],panels:[]}),x=P(!1);F(()=>{x.value=!0}),N(G,v(()=>x.value?null:T.value));let w=v(()=>a.selectedIndex);return F(()=>{_([w],()=>{var t;return y((t=a.selectedIndex)!=null?t:a.defaultIndex)},{immediate:!0})}),J(()=>{if(!p.value||R.value==null||u.tabs.value.length<=0)return;let t=k(u.tabs.value,f);t.some((o,e)=>f(u.tabs.value[e])!==f(o))&&u.setSelectedIndex(t.findIndex(o=>f(o)===f(u.tabs.value[R.value])))}),()=>{let t={selectedIndex:i.value};return A(z,[l.value.length<=0&&A(V,{onFocus:()=>{for(let n of l.value){let o=f(n);if((o==null?void 0:o.tabIndex)===0)return o.focus(),!0}return!1}}),M({theirProps:{...s,...ee(a,[\"selectedIndex\",\"defaultIndex\",\"manual\",\"vertical\",\"onChange\"])},ourProps:{},slot:t,slots:b,attrs:s,name:\"TabGroup\"})])}}}),pe=L({name:\"TabList\",props:{as:{type:[Object,String],default:\"div\"}},setup(a,{attrs:b,slots:s}){let d=C(\"TabList\");return()=>{let i={selectedIndex:d.selectedIndex.value},l={role:\"tablist\",\"aria-orientation\":d.orientation.value};return M({ourProps:l,theirProps:a,slot:i,attrs:b,slots:s,name:\"TabList\"})}}}),xe=L({name:\"Tab\",props:{as:{type:[Object,String],default:\"button\"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(a,{attrs:b,slots:s,expose:d}){var o;let i=(o=a.id)!=null?o:`headlessui-tabs-tab-${$()}`,l=C(\"Tab\"),r=P(null);d({el:r,$el:r}),F(()=>l.registerTab(r)),K(()=>l.unregisterTab(r));let p=j(G),R=v(()=>{if(p.value){let e=p.value.tabs.indexOf(i);return e===-1?p.value.tabs.push(i)-1:e}return-1}),y=v(()=>{let e=l.tabs.value.indexOf(r);return e===-1?R.value:e}),u=v(()=>y.value===l.selectedIndex.value);function T(e){var I;let c=e();if(c===B.Success&&l.activation.value===\"auto\"){let m=(I=Z(r))==null?void 0:I.activeElement,h=l.tabs.value.findIndex(O=>f(O)===m);h!==-1&&l.setSelectedIndex(h)}return c}function x(e){let c=l.tabs.value.map(m=>f(m)).filter(Boolean);if(e.key===S.Space||e.key===S.Enter){e.preventDefault(),e.stopPropagation(),l.setSelectedIndex(y.value);return}switch(e.key){case S.Home:case S.PageUp:return e.preventDefault(),e.stopPropagation(),T(()=>D(c,g.First));case S.End:case S.PageDown:return e.preventDefault(),e.stopPropagation(),T(()=>D(c,g.Last))}if(T(()=>H(l.orientation.value,{vertical(){return e.key===S.ArrowUp?D(c,g.Previous|g.WrapAround):e.key===S.ArrowDown?D(c,g.Next|g.WrapAround):B.Error},horizontal(){return e.key===S.ArrowLeft?D(c,g.Previous|g.WrapAround):e.key===S.ArrowRight?D(c,g.Next|g.WrapAround):B.Error}}))===B.Success)return e.preventDefault()}let w=P(!1);function E(){var e;w.value||(w.value=!0,!a.disabled&&((e=f(r))==null||e.focus({preventScroll:!0}),l.setSelectedIndex(y.value),Y(()=>{w.value=!1})))}function t(e){e.preventDefault()}let n=Q(v(()=>({as:a.as,type:b.type})),r);return()=>{var m,h;let e={selected:u.value,disabled:(m=a.disabled)!=null?m:!1},{...c}=a,I={ref:r,onKeydown:x,onMousedown:t,onClick:E,id:i,role:\"tab\",type:n.value,\"aria-controls\":(h=f(l.panels.value[y.value]))==null?void 0:h.id,\"aria-selected\":u.value,tabIndex:u.value?0:-1,disabled:a.disabled?!0:void 0};return M({ourProps:I,theirProps:c,slot:e,attrs:b,slots:s,name:\"Tab\"})}}}),Ie=L({name:\"TabPanels\",props:{as:{type:[Object,String],default:\"div\"}},setup(a,{slots:b,attrs:s}){let d=C(\"TabPanels\");return()=>{let i={selectedIndex:d.selectedIndex.value};return M({theirProps:a,ourProps:{},slot:i,attrs:s,slots:b,name:\"TabPanels\"})}}}),ye=L({name:\"TabPanel\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(a,{attrs:b,slots:s,expose:d}){var T;let i=(T=a.id)!=null?T:`headlessui-tabs-panel-${$()}`,l=C(\"TabPanel\"),r=P(null);d({el:r,$el:r}),F(()=>l.registerPanel(r)),K(()=>l.unregisterPanel(r));let p=j(G),R=v(()=>{if(p.value){let x=p.value.panels.indexOf(i);return x===-1?p.value.panels.push(i)-1:x}return-1}),y=v(()=>{let x=l.panels.value.indexOf(r);return x===-1?R.value:x}),u=v(()=>y.value===l.selectedIndex.value);return()=>{var n;let x={selected:u.value},{tabIndex:w,...E}=a,t={ref:r,id:i,role:\"tabpanel\",\"aria-labelledby\":(n=f(l.tabs.value[y.value]))==null?void 0:n.id,tabIndex:u.value?w:-1};return!u.value&&a.unmount&&!a.static?A(X,{as:\"span\",\"aria-hidden\":!0,...t}):M({ourProps:t,theirProps:E,slot:x,attrs:b,slots:s,features:q.Static|q.RenderStrategy,visible:u.value,name:\"TabPanel\"})}}});export{xe as Tab,me as TabGroup,pe as TabList,ye as TabPanel,Ie as TabPanels};\n", "<script setup lang=\"ts\" generic=\"T\">\r\nimport { Tab, TabGroup, TabList } from '@headlessui/vue'\r\n\r\nconst props = defineProps<{\r\n  options: {\r\n    icon?: string\r\n    label: any\r\n    value: T\r\n  }[]\r\n}>()\r\n\r\nconst emits = defineEmits<{\r\n  change: [T]\r\n}>()\r\n\r\nconst value = defineModel<T>()\r\n\r\nconst selectedIndex = computed({\r\n  get() {\r\n    return props.options.findIndex(option => option.value === value.value)\r\n  },\r\n  set(val) {\r\n    value.value = props.options[val].value\r\n  },\r\n})\r\n\r\nwatch(value, (val) => {\r\n  val && emits('change', val)\r\n})\r\n\r\nfunction handleChange(index: number) {\r\n  value.value = props.options[index].value\r\n}\r\n</script>\r\n\r\n<template>\r\n  <TabGroup :selected-index=\"selectedIndex\" @change=\"handleChange\">\r\n    <TabList class=\"inline-flex select-none items-center justify-center rounded-md bg-stone-1 p-1 ring-1 ring-stone-2 dark-bg-stone-9 dark-ring-stone-8\">\r\n      <Tab v-for=\"(option, index) in options\" :key=\"index\" v-slot=\"{ selected }\" as=\"template\">\r\n        <button\r\n          class=\"w-full inline-flex items-center justify-center gap-1 whitespace-nowrap border-size-0 rounded-md bg-inherit px-2 py-1.5 text-sm text-dark ring-stone-2 ring-inset dark-text-white focus-outline-none focus-ring-2 dark-ring-stone-8\" :class=\"{\r\n            'cursor-default bg-white dark-bg-dark-9': selected,\r\n            'cursor-pointer opacity-50 hover-opacity-100': !selected,\r\n          }\"\r\n        >\r\n          <SvgIcon v-if=\"option.icon\" :name=\"option.icon\" class=\"flex-shrink-0\" />\r\n          {{ option.label }}\r\n        </button>\r\n      </Tab>\r\n    </TabList>\r\n  </TabGroup>\r\n</template>\r\n"], "names": ["d", "i", "props", "onFocus", "type", "Function", "required", "setup", "t", "n", "f", "value", "m", "F", "as", "features", "l", "Focusable", "o", "preventDefault", "e", "a", "requestAnimationFrame", "r", "u", "call", "cancelAnimationFrame", "s", "te", "Forwards", "Backwards", "le", "Less", "Equal", "Greater", "U", "Symbol", "C", "b", "j", "Error", "captureStackTrace", "G", "me", "L", "name", "emits", "change", "Object", "String", "default", "selectedIndex", "Number", "defaultIndex", "vertical", "Boolean", "manual", "inheritAttrs", "slots", "attrs", "emit", "E", "P", "p", "v", "R", "y", "c", "k", "tabs", "panels", "filter", "I", "hasAttribute", "length", "H", "Math", "sign", "indexOf", "slice", "h", "find", "W", "includes", "O", "orientation", "activation", "setSelectedIndex", "registerTab", "push", "unregisterTab", "splice", "registerPanel", "unregisterPanel", "N", "T", "x", "w", "watch", "immediate", "J", "some", "findIndex", "A", "z", "V", "tabIndex", "focus", "M", "theirProps", "ee", "ourProps", "slot", "pe", "role", "xe", "disabled", "id", "expose", "$", "el", "$el", "K", "B", "Success", "Z", "activeElement", "map", "key", "S", "Space", "Enter", "stopPropagation", "Home", "PageUp", "D", "g", "First", "End", "PageDown", "Last", "ArrowUp", "Previous", "WrapAround", "ArrowDown", "Next", "horizontal", "ArrowLeft", "ArrowRight", "preventScroll", "Y", "Q", "selected", "ref", "onKeydown", "onMousedown", "onClick", "__props", "__emit", "_useModel", "computed", "get", "options", "option", "set", "val", "handleChange", "index"], "mappings": "wYAAyG,IAAIA,EAAEC,EAAE,CAACC,MAAM,CAACC,QAAQ,CAACC,KAAKC,SAASC,UAAS,IAAK,KAAAC,CAAMC,GAAO,IAAAC,EAAEC,GAAE,GAAI,MAAM,IAAID,EAAEE,MAAMC,EAAEC,EAAE,CAACC,GAAG,SAASV,KAAK,SAASW,SAASC,EAAEC,UAAU,OAAAd,CAAQe,GAAGA,EAAEC,iBAAiB,IAAIC,EAAEC,EAAE,GAA2KD,EAAEE,uBAA1K,SAASC,IAAQC,IAAAA,EAAE,KAAGH,KAAK,GAAqC,OAAkB,OAAdG,EAAEhB,EAAEL,UAAgBqB,EAAEC,KAAKjB,IAAMC,EAAAE,OAAM,OAAGe,qBAAqBN,SAAUA,EAAEE,sBAAsBC,IAAvIH,GAAGM,qBAAqBN,EAAiH,GAA2B,IAAI,IAAI,ICAwU,IAAoFpB,EAA5E2B,EAAJC,IAAID,EAAgEC,GAAI,IAA9DD,EAAEE,SAAS,GAAG,WAAWF,EAAEA,EAAEG,UAAU,GAAG,YAAYH,GAAYI,IAAI/B,EAA0E+B,GAAI,CAAE,GAA1E/B,EAAEgC,MAAO,GAAE,OAAOhC,EAAEA,EAAEiC,MAAM,GAAG,QAAQjC,EAAEA,EAAEkC,QAAQ,GAAG,UAAUlC,GAAY,IAAImC,EAAEC,OAAO,eAAe,SAASC,EAAEhB,GAAO,IAAAiB,EAAEC,EAAEJ,EAAE,MAAM,GAAO,OAAJG,EAAS,CAAC,IAAIX,EAAE,IAAIa,MAAM,IAAInB,oDAAoD,MAAMmB,MAAMC,mBAAmBD,MAAMC,kBAAkBd,EAAEU,GAAGV,CAAC,CAAQ,OAAAW,CAAC,CAAI,IAACI,EAAEN,OAAO,kBAAkBO,EAAGC,EAAE,CAACC,KAAK,WAAWC,MAAM,CAACC,OAAU1B,IAAA,GAAInB,MAAM,CAACY,GAAG,CAACV,KAAK,CAAC4C,OAAOC,QAAQC,QAAQ,YAAYC,cAAc,CAAC/C,KAAK,CAACgD,QAAQF,QAAQ,MAAMG,aAAa,CAACjD,KAAK,CAACgD,QAAQF,QAAQ,GAAGI,SAAS,CAAClD,KAAK,CAACmD,SAASL,SAAQ,GAAIM,OAAO,CAACpD,KAAK,CAACmD,SAASL,SAAQ,IAAKO,cAAa,EAAG,KAAAlD,CAAMc,GAAGqC,MAAMpB,EAAEqB,MAAMhC,EAAEiC,KAAK5D,IAAQ,IAAA6D,EAAE,IAAI5D,EAAE6D,EAAuB,OAApBD,EAAExC,EAAE8B,eAAqBU,EAAExC,EAAEgC,cAAcrC,EAAE8C,EAAE,IAAIvC,EAAEuC,EAAE,IAAIC,EAAEC,GAAE,IAAsB,OAAlB3C,EAAE8B,gBAAsBc,EAAED,GAAE,IAAID,EAAEpD,MAAMU,EAAE8B,cAAclD,EAAEU,QAAO,SAASuD,EAAE1D,GAAO,IAAA2D,EAAE,IAAI1D,EAAE2D,EAAE5C,EAAE6C,KAAK1D,MAAMD,GAAGQ,EAAEkD,EAAE5C,EAAE8C,OAAO3D,MAAMD,GAAGU,EAAEX,EAAE8D,QAAUC,IAAK,IAAA5D,EAAQ,QAAY,OAATA,EAAEF,EAAE8D,KAAW5D,EAAE6D,aAAa,YAAU,IAAK,GAAGjE,EAAE,GAAGA,EAAEC,EAAEiE,OAAO,EAAE,CAAC,IAAIF,EAAEG,EAAY,OAAV1E,EAAEU,MAAa,EAAEiE,KAAKC,KAAKrE,EAAEP,EAAEU,OAAO,CAAC,EAAC,GAAI,IAAI,EAAE,EAAI,IAAIgE,EAAEC,KAAKC,KAAKrE,GAAG,CAAC,EAAC,GAAI,IAAI,EAAE,EAAI,IAAI,EAAE,EAAI,IAAI,IAAI,EAAI,IAAI,IAAII,EAAE+D,EAAEH,EAAE,CAAC,EAAI,IAAI/D,EAAEqE,QAAQ1D,EAAE,IAAI,EAAI,IAAIX,EAAEqE,QAAQ1D,EAAEA,EAAEsD,OAAO,OAAU,IAAA9D,IAAKX,EAAEU,MAAMC,GAAGY,EAAE6C,KAAK1D,MAAMF,EAAEe,EAAE8C,OAAO3D,MAAMO,CAAC,KAAK,CAAK,IAAAsD,EAAE/D,EAAEsE,MAAM,EAAEvE,GAAGwE,EAAE,IAAIvE,EAAEsE,MAAMvE,MAAMgE,GAAGS,MAAQC,GAAA9D,EAAE+D,SAASD,KAAI,IAAIF,EAAE,OAAWI,IAAAA,EAAoB,OAAjBjB,EAAE1D,EAAEqE,QAAQE,IAAUb,EAAE3C,EAAE2B,cAAcxC,OAAU,IAAJyE,IAASA,EAAE5D,EAAE2B,cAAcxC,OAAOV,EAAEU,MAAMyE,EAAE5D,EAAE6C,KAAK1D,MAAMF,EAAEe,EAAE8C,OAAO3D,MAAMO,CAAC,CAAC,CAAC,IAAIM,EAAE,CAAC2B,cAAca,GAAE,KAAK,IAAIxD,EAAEC,EAAS,OAAuC,OAAvCA,EAAe,OAAZD,EAAEP,EAAEU,OAAaH,EAAEa,EAAEgC,cAAoB5C,EAAE,IAAA,IAAO4E,YAAYrB,GAAE,IAAI3C,EAAEiC,SAAS,WAAW,eAAcgC,WAAWtB,GAAE,IAAI3C,EAAEmC,OAAO,SAAS,SAAQa,KAAKrD,EAAEsD,OAAO/C,EAAE,gBAAAgE,CAAiB/E,GAAKyD,EAAAtD,QAAQH,GAAGR,EAAE,SAASQ,GAAGuD,EAAEpD,OAAOuD,EAAE1D,EAAE,EAAE,WAAAgF,CAAYhF,GAAOU,IAAAA,EAAE,GAAGF,EAAEL,MAAMwE,SAAS3E,GAAG,OAAO,IAAIC,EAAEO,EAAEL,MAAMV,EAAEU,OAAO,GAAGK,EAAEL,MAAM8E,KAAKjF,GAAGQ,EAAEL,MAAMyD,EAAEpD,EAAEL,MAAMD,IAAIqD,EAAEpD,MAAM,CAAK,IAAAS,EAA0B,OAAvBF,EAAEF,EAAEL,MAAMmE,QAAQrE,IAAUS,EAAEjB,EAAEU,OAAU,IAAAS,IAAKnB,EAAEU,MAAMS,EAAE,CAAC,EAAE,aAAAsE,CAAclF,GAAG,IAAIC,EAAEO,EAAEL,MAAMmE,QAAQtE,IAAW,IAARC,GAAQO,EAAEL,MAAMgF,OAAOlF,EAAE,EAAE,EAAE,aAAAmF,CAAcpF,GAAGe,EAAEZ,MAAMwE,SAAS3E,KAAKe,EAAEZ,MAAM8E,KAAKjF,GAAGe,EAAEZ,MAAMyD,EAAE7C,EAAEZ,MAAMD,GAAG,EAAE,eAAAmF,CAAgBrF,GAAG,IAAIC,EAAEc,EAAEZ,MAAMmE,QAAQtE,IAAW,IAARC,GAAQc,EAAEZ,MAAMgF,OAAOlF,EAAE,EAAE,GAAGqF,EAAE3D,EAAEX,GAAG,IAAIuE,EAAEjC,EAAE,CAACO,KAAK,GAAGC,OAAO,KAAK0B,EAAElC,GAAE,GAAIjD,GAAE,KAAKmF,EAAErF,OAAM,CAAA,IAAKmF,EAAEpD,EAAEsB,GAAE,IAAIgC,EAAErF,MAAM,KAAKoF,EAAEpF,SAAQ,IAAIsF,EAAEjC,GAAE,IAAI3C,EAAE8B,gBAAe,OAAOtC,GAAE,KAAOqF,EAAA,CAACD,IAAG,KAASzF,IAAAA,EAAE,OAAO0D,EAAuB,OAApB1D,EAAEa,EAAE8B,eAAqB3C,EAAEa,EAAEgC,aAAY,GAAG,CAAC8C,WAAU,GAAG,IAAIC,GAAE,KAAQ,IAACrC,EAAEpD,OAAgB,MAATsD,EAAEtD,OAAaa,EAAE6C,KAAK1D,MAAM+D,QAAQ,EAAE,OAAO,IAAIlE,EAAE4D,EAAE5C,EAAE6C,KAAK1D,MAAMD,GAAGF,EAAE6F,MAAK,CAACnF,EAAEE,IAAIV,EAAEc,EAAE6C,KAAK1D,MAAMS,MAAMV,EAAEQ,MAAKM,EAAE+D,iBAAiB/E,EAAE8F,WAAUpF,GAAGR,EAAEQ,KAAKR,EAAEc,EAAE6C,KAAK1D,MAAMsD,EAAEtD,UAAQ,IAAI,KAAK,IAAIH,EAAE,CAAC2C,cAAclD,EAAEU,OAAc4F,OAAAA,EAAEC,EAAE,CAACxF,EAAEL,MAAM+D,QAAQ,GAAG6B,EAAEE,EAAE,CAACtG,QAAQ,KAAa,IAAA,IAAAM,KAAKO,EAAEL,MAAM,CAAKO,IAAAA,EAAER,EAAED,GAAOS,GAA6B,KAA1B,MAAHA,OAAQ,EAAOA,EAAEwF,UAAc,OAAOxF,EAAEyF,SAAQ,CAAE,CAAO,OAAA,CAAA,IAAMC,EAAE,CAACC,WAAW,IAAIlF,KAAKmF,EAAGzF,EAAE,CAAC,gBAAgB,eAAe,SAAS,WAAW,cAAc0F,SAAS,CAAE,EAACC,KAAKxG,EAAEkD,MAAMpB,EAAEqB,MAAMhC,EAAEkB,KAAK,cAAa,CAAE,IAAIoE,EAAGrE,EAAE,CAACC,KAAK,UAAU3C,MAAM,CAACY,GAAG,CAACV,KAAK,CAAC4C,OAAOC,QAAQC,QAAQ,QAAQ,KAAA3C,CAAMc,GAAGsC,MAAMrB,EAAEoB,MAAM/B,IAAQ3B,IAAAA,EAAEqC,EAAE,WAAW,MAAM,KAAK,IAAIpC,EAAE,CAACkD,cAAcnD,EAAEmD,cAAcxC,OAAOK,EAAE,CAACkG,KAAK,UAAU,mBAAmBlH,EAAEqF,YAAY1E,OAAO,OAAOiG,EAAE,CAACG,SAAS/F,EAAE6F,WAAWxF,EAAE2F,KAAK/G,EAAE0D,MAAMrB,EAAEoB,MAAM/B,EAAEkB,KAAK,WAAU,CAAE,IAAIsE,EAAGvE,EAAE,CAACC,KAAK,MAAM3C,MAAM,CAACY,GAAG,CAACV,KAAK,CAAC4C,OAAOC,QAAQC,QAAQ,UAAUkE,SAAS,CAAChH,KAAK,CAACmD,SAASL,SAAQ,GAAImE,GAAG,CAACjH,KAAK6C,OAAOC,QAAQ,OAAO,KAAA3C,CAAMc,GAAGsC,MAAMrB,EAAEoB,MAAM/B,EAAE2F,OAAOtH,IAAQkB,IAAAA,EAAE,IAAIjB,EAAY,OAATiB,EAAEG,EAAEgG,IAAUnG,EAAE,uBAAuBqG,MAAMvG,EAAEqB,EAAE,OAAOd,EAAEuC,EAAE,MAAM9D,EAAE,CAACwH,GAAGjG,EAAEkG,IAAIlG,IAAIV,GAAE,IAAIG,EAAEwE,YAAYjE,KAAImG,GAAE,IAAI1G,EAAE0E,cAAcnE,KAAI,IAAIwC,EAAExB,EAAEG,GAAGuB,EAAED,GAAE,KAAK,GAAGD,EAAEpD,MAAM,CAAC,IAAIS,EAAE2C,EAAEpD,MAAM0D,KAAKS,QAAQ7E,GAAU,WAAAmB,EAAO2C,EAAEpD,MAAM0D,KAAKoB,KAAKxF,GAAG,EAAEmB,CAAC,CAAO,OAAA,CAAA,IAAK8C,EAAEF,GAAE,KAAK,IAAI5C,EAAEJ,EAAEqD,KAAK1D,MAAMmE,QAAQvD,GAAU,OAAO,IAAPH,EAAO6C,EAAEtD,MAAMS,CAAA,IAAII,EAAEwC,GAAE,IAAIE,EAAEvD,QAAQK,EAAEmC,cAAcxC,QAAO,SAASoF,EAAE3E,GAAO,IAAAoD,EAAE,IAAIL,EAAE/C,IAAI,GAAG+C,IAAIwD,EAAEC,SAA8B,SAArB5G,EAAEsE,WAAW3E,MAAe,CAAC,IAAIC,EAAY,OAAT4D,EAAEqD,EAAEtG,SAAU,EAAOiD,EAAEsD,cAAc9C,EAAEhE,EAAEqD,KAAK1D,MAAM2F,WAAUlB,GAAG1E,EAAE0E,KAAKxE,KAAO,IAAJoE,GAAQhE,EAAEuE,iBAAiBP,EAAE,CAAQ,OAAAb,CAAC,CAAC,SAAS6B,EAAE5E,GAAO,IAAA+C,EAAEnD,EAAEqD,KAAK1D,MAAMoH,KAAOrH,GAAAA,EAAEE,KAAI2D,OAAOhB,SAAS,GAAGnC,EAAE4G,MAAMC,EAAEC,OAAO9G,EAAE4G,MAAMC,EAAEE,MAA0E,OAAjE/G,EAAAD,iBAAiBC,EAAEgH,uBAAkBpH,EAAEuE,iBAAiBrB,EAAEvD,OAAc,OAAOS,EAAE4G,KAAK,KAAKC,EAAEI,KAAK,KAAKJ,EAAEK,OAAO,OAAOlH,EAAED,iBAAiBC,EAAEgH,kBAAkBrC,GAAE,IAAIwC,EAAEpE,EAAEqE,EAAEC,SAAQ,KAAKR,EAAES,IAAI,KAAKT,EAAEU,SAAS,OAAOvH,EAAED,iBAAiBC,EAAEgH,kBAAkBrC,GAAE,IAAIwC,EAAEpE,EAAEqE,EAAEI,QAAO,OAAG7C,GAAE,IAAIpB,EAAE3D,EAAEqE,YAAY1E,MAAM,CAAC2C,SAAU,IAAQlC,EAAE4G,MAAMC,EAAEY,QAAQN,EAAEpE,EAAEqE,EAAEM,SAASN,EAAEO,YAAY3H,EAAE4G,MAAMC,EAAEe,UAAUT,EAAEpE,EAAEqE,EAAES,KAAKT,EAAEO,YAAYpB,EAAEnF,MAAO0G,WAAY,IAAQ9H,EAAE4G,MAAMC,EAAEkB,UAAUZ,EAAEpE,EAAEqE,EAAEM,SAASN,EAAEO,YAAY3H,EAAE4G,MAAMC,EAAEmB,WAAWb,EAAEpE,EAAEqE,EAAES,KAAKT,EAAEO,YAAYpB,EAAEnF,YAAYmF,EAAEC,QAAexG,EAAED,sBAA3S,CAA2T,CAAK,IAAA8E,EAAEnC,GAAE,GAAI,SAASD,IAAQ,IAAAzC,EAAI6E,EAAAtF,QAAQsF,EAAEtF,OAAM,GAAIU,EAAE+F,WAAqB,OAAThG,EAAEV,EAAEa,KAAWH,EAAEuF,MAAM,CAAC0C,eAAc,IAAKrI,EAAEuE,iBAAiBrB,EAAEvD,OAAO2I,GAAE,KAAKrD,EAAEtF,OAAM,CAAA,KAAM,CAAC,SAASH,EAAEY,GAAGA,EAAED,gBAAgB,CAAC,IAAIV,EAAE8I,EAAEvF,GAAE,KAAA,CAAMlD,GAAGO,EAAEP,GAAGV,KAAKkC,EAAElC,SAAQmB,GAAG,MAAM,KAAK,IAAIX,EAAEoE,EAAE,IAAI5D,EAAE,CAACoI,SAAShI,EAAEb,MAAMyG,SAAyB,OAAfxG,EAAES,EAAE+F,WAAgBxG,OAAUuD,GAAG9C,EAAEmD,EAAE,CAACiF,IAAIlI,EAAEmI,UAAU1D,EAAE2D,YAAYnJ,EAAEoJ,QAAQ/F,EAAEwD,GAAGpH,EAAEiH,KAAK,MAAM9G,KAAKK,EAAEE,MAAM,gBAAgD,OAA/BqE,EAAEtE,EAAEM,EAAEsD,OAAO3D,MAAMuD,EAAEvD,cAAe,EAAOqE,EAAEqC,GAAG,gBAAgB7F,EAAEb,MAAM+F,SAASlF,EAAEb,MAAM,GAAK,EAAAyG,WAAS/F,EAAE+F,eAAY,GAAQ,OAAOR,EAAE,CAACG,SAASvC,EAAEqC,WAAW1C,EAAE6C,KAAK5F,EAAEuC,MAAMrB,EAAEoB,MAAM/B,EAAEkB,KAAK,OAAM,CAAE,qJCGr8L,MAAM3C,EAAQ2J,EAQR/G,EAAQgH,EAIRnJ,EAAQoJ,kBAER5G,EAAgB6G,EAAS,CAC7BC,IAAM,IACG/J,EAAMgK,QAAQ5D,cAAoB6D,EAAOxJ,QAAUA,EAAMA,QAElE,GAAAyJ,CAAIC,GACF1J,EAAMA,MAAQT,EAAMgK,QAAQG,GAAK1J,KAAA,IAQrC,SAAS2J,EAAaC,GACpB5J,EAAMA,MAAQT,EAAMgK,QAAQK,GAAO5J,KAAA,QAL/BuF,EAAAvF,GAAQ0J,IACLA,GAAAvH,EAAM,SAAUuH,EAAG", "x_google_ignoreList": [0, 1]}