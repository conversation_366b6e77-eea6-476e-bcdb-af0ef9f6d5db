{"version": 3, "file": "index.vue_vue_type_script_setup_true_lang-TsQ6GRZR.js", "sources": ["../../src/views/sell/price/calendar/components/FormMode/index.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport {\r\n  Close,\r\n} from '@element-plus/icons-vue'\r\nimport type { DetailFormProps } from '../../types'\r\nimport DetailForm from '../DetailForm/detail.vue'\r\nimport CreateCalendar from '../DetailForm/createCalendar.vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<{\r\n    modelValue?: boolean\r\n  } & DetailFormProps>(),\r\n  {\r\n    modelValue: false,\r\n  },\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [\r\n    value: boolean,\r\n  ]\r\n  'editStatus': [\r\n    value: boolean,\r\n  ]\r\n  'success': []\r\n}>()\r\n\r\nconst formRef = ref()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst doEdit = computed({\r\n  get() {\r\n    return props.isEdit\r\n  },\r\n  set(val) {\r\n    emits('editStatus', val)\r\n  },\r\n})\r\n\r\nconst title = computed(() => {\r\n  let t = '新增集团酒店日历'\r\n  if (props.handle === 'create') {\r\n    t = '新增集团酒店日历'\r\n  }\r\n  else if (props.handle === 'edit') {\r\n    t = '编辑集团酒店日历'\r\n  }\r\n  else {\r\n    t = '集团酒店日历'\r\n  }\r\n  return t\r\n})\r\n\r\nfunction onSubmit() {\r\n  // submit() 为组件内部方法\r\n  formRef.value.submit().then(() => {\r\n    emits('success')\r\n    onCancel('')\r\n  })\r\n}\r\n\r\nfunction onCancel(who: string) {\r\n  myVisible.value = false\r\n  if (who === 'edit') {\r\n    doEdit.value = false\r\n  }\r\n  if (who === 'create') {\r\n    doEdit.value = false\r\n  }\r\n}\r\n\r\nfunction toEdit() {\r\n  doEdit.value = true\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog\r\n      v-if=\"props.handle === 'create'\" v-model=\"myVisible\" width=\"600px\" :title=\"title\"\r\n      :close-on-click-modal=\"false\" append-to-body :modal=\"true\" destroy-on-close\r\n    >\r\n      <CreateCalendar ref=\"formRef\" v-bind=\"props\" />\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel('create')\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          保存\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n    <el-drawer\r\n      v-else v-model=\"myVisible\" :show-close=\"false\" :z-index=\"2000\" size=\"600px\" :close-on-click-modal=\"!doEdit\"\r\n      :modal=\"doEdit\" destroy-on-close\r\n    >\r\n      <template #header=\"{ close, titleId, titleClass }\">\r\n        <h4 :id=\"titleId\" :class=\"titleClass\">\r\n          {{ title }}\r\n        </h4>\r\n        <el-button link disabled>\r\n          &lt;上一个\r\n        </el-button><el-button link>\r\n          下一个&gt;\r\n        </el-button><el-divider\r\n          direction=\"vertical\"\r\n        /><el-button :icon=\"Close\" circle style=\"font-size: 14px;\" @click=\"close\" />\r\n      </template>\r\n      <DetailForm ref=\"formRef\" v-bind=\"props\" />\r\n      <template v-if=\"doEdit\" #footer>\r\n        <el-button size=\"large\" @click=\"onCancel('edit')\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          保存\r\n        </el-button>\r\n      </template>\r\n      <template v-else #footer>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"toEdit()\">\r\n          编辑当前集团酒店日历\r\n        </el-button>\r\n      </template>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n"], "names": ["props", "__props", "emits", "__emit", "formRef", "ref", "myVisible", "computed", "get", "modelValue", "set", "val", "doEdit", "isEdit", "title", "t", "handle", "onSubmit", "value", "submit", "then", "onCancel", "who"], "mappings": "6mBAQA,MAAMA,EAAQC,EASRC,EAAQC,EAURC,EAAUC,IAEVC,EAAYC,EAAS,CACzBC,IAAM,IACGR,EAAMS,WAEf,GAAAC,CAAIC,GACFT,EAAM,oBAAqBS,EAAG,IAI5BC,EAASL,EAAS,CACtBC,IAAM,IACGR,EAAMa,OAEf,GAAAH,CAAIC,GACFT,EAAM,aAAcS,EAAG,IAIrBG,EAAQP,GAAS,KACrB,IAAIQ,EAAI,WAUD,OARDA,EADe,WAAjBf,EAAMgB,OACJ,WAEoB,SAAjBhB,EAAMgB,OACT,WAGA,SAECD,CAAA,IAGT,SAASE,IAEPb,EAAQc,MAAMC,SAASC,MAAK,KAC1BlB,EAAM,WACNmB,EAAS,GAAE,GACZ,CAGH,SAASA,EAASC,GAChBhB,EAAUY,OAAQ,EACN,SAARI,IACFV,EAAOM,OAAQ,GAEL,WAARI,IACFV,EAAOM,OAAQ,EACjB,s/CAIAN,EAAOM,OAAQ"}