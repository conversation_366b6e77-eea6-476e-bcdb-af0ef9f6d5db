{"version": 3, "file": "qrCode.api-CbQbYUHU.js", "sources": ["../../src/api/modules/qrCode/qrCode.api.ts"], "sourcesContent": ["import type { qrCodeRequest, uuidTypes } from './qrCode.ts'\r\nimport api from '../../index.ts'\r\n\r\nconst BASE_PATH = '/admin-api'\r\n\r\nexport default {\r\n  /**\r\n   * 绑定二维码\r\n   * @param data\r\n   * @returns\r\n   */\r\n  bindQrCode: (data: qrCodeRequest) => api.get(`${BASE_PATH}/system/auth/bind/qrcode`, { params: data }),\r\n  /**\r\n   * 二维码状态检查\r\n   * @param data\r\n   */\r\n  qrCodeCheck: (data: uuidTypes) => api.get(`${BASE_PATH}/system/auth/qrcode/check`, { params: data }),\r\n  /**\r\n   * 登录二维码\r\n   * @param data\r\n   * @returns\r\n   */\r\n  loginQrCode: (data: uuidTypes) => api.get(`${BASE_PATH}/system/auth/qrcode`, { params: data }),\r\n  /**\r\n   * 解除微信绑定\r\n   * @param data\r\n   * @returns\r\n   */\r\n  unBindQrCode: (data: qrCodeRequest) => api.get(`${BASE_PATH}/system/auth/unBind`, { params: data }),\r\n}\r\n"], "names": ["BASE_PATH", "qrCodeApi", "bindQrCode", "data", "api", "get", "params", "qrCodeCheck", "loginQrCode", "unBindQrCode"], "mappings": "wCAGA,MAAMA,EAAY,aAEHC,EAAA,CAMbC,WAAaC,GAAwBC,EAAIC,IAAI,GAAGL,4BAAqC,CAAEM,OAAQH,IAK/FI,YAAcJ,GAAoBC,EAAIC,IAAI,GAAGL,6BAAsC,CAAEM,OAAQH,IAM7FK,YAAcL,GAAoBC,EAAIC,IAAI,GAAGL,uBAAgC,CAAEM,OAAQH,IAMvFM,aAAeN,GAAwBC,EAAIC,IAAI,GAAGL,uBAAgC,CAAEM,OAAQH"}