{"version": 3, "file": "roomLog.api-D1y-EVTQ.js", "sources": ["../../src/api/modules/pms/room/roomLog.api.ts"], "sourcesContent": ["import api from '../../../index.ts'\r\n/**\r\n * 房间操作日志接口\r\n */\r\nexport default {\r\n  /**\r\n   * 房间操作日志列表\r\n   * @param data\r\n   * @returns\r\n   */\r\n  page: (data: {\r\n    gcode: string\r\n    hcode: string\r\n    rCode: string\r\n    createTime: string\r\n    pageNo: number\r\n    pageSize: number\r\n  }) => api.get('admin-api/pms/room-log/page', {\r\n    params: data,\r\n  }),\r\n\r\n  /**\r\n   * 房间详情\r\n   *\r\n   * @param data\r\n   */\r\n  logPage: (data: any) => api.get('/admin-api/pms/order-log/page', { params: data }),\r\n}\r\n"], "names": ["roomLogApi", "page", "data", "api", "get", "params", "logPage"], "mappings": "mCAIA,MAAeA,EAAA,CAMbC,KAAOC,GAODC,EAAIC,IAAI,8BAA+B,CAC3CC,OAAQH,IAQVI,QAAUJ,GAAcC,EAAIC,IAAI,gCAAiC,CAAEC,OAAQH"}