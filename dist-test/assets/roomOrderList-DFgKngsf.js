import{_ as e}from"./index-3RMLzyhA.js";import{d as a,aj as l,ai as t,B as o,b as n,r as i,am as s,o as r,c as u,F as c,ag as m,e as d,w as p,f,h as g,Y as v,u as h,av as y,g as b,R as _,bM as N,bB as D,aR as k,bz as w,b1 as C,aX as x,bG as Y,bE as T,q as I,b2 as M,aD as R,cR as j,by as $,cS as P}from"./index-CkEhI1Zk.js";/* empty css                             *//* empty css                *//* empty css                   *//* empty css               *//* empty css                  */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                 *//* empty css               *//* empty css               */import{g as O}from"./generalConfig.api-CEBBd8kx.js";import{c as L}from"./customer.api-dB3m63zZ.js";import{b as S}from"./book.api-ERXvEXQF.js";import{r as q}from"./roomstatus.api-DLV-fDUJ.js";import{o as A}from"./serviceintegration.api-ByMiQtUi.js";import{C as E}from"./CardReader-BeR26SIt.js";import{_ as z}from"./_plugin-vue_export-helper-BCo6x5W8.js";const F={class:"meituan"},H={class:"cursor-pointer"},B={class:"cursor-pointer"},J={key:2,class:"cursor-pointer"},Q=a({name:"RoomOrderList",__name:"roomOrderList",props:{dataList:{},otaName:{},total:{},otaApiList:{},otaOptions:{}},emits:["submit"],setup(a,{emit:z}){const Q=a,G=z,{t:U}=l(),V=t(),X=o((()=>Q.dataList.length==Q.total)),K=n(),W=n(!1),Z=n(),ee=i({gcode:V.gcode,hcode:V.hcode}),ae=n([{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/tiktok.png",channelCode:"tiktok",channelName:"抖音"},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/fliggy.png",channelCode:"fliggy",channelName:"飞猪"},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/meituan.png",channelCode:"meituan",channelName:"美团"},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/store.png",channelCode:"lobby",channelName:"门店"},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/logo.png",channelCode:"elong",channelName:"艺龙"},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/ctrip.png",channelCode:"ctrip",channelName:"携程"},{image:"https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/mini-app-png/logo.png",channelCode:"mini_app",channelName:"小程序"}]),le=n(),te=n([]),oe=n([]),ne=n();function ie(){G("submit",{meituan:10})}function se(){let e;if("ctrip"==Q.otaName&&-1==K.value.liveDays)e=`${K.value.arrival} ${te.value[0]}`;else if("ctrip"==Q.otaName&&K.value.liveDays>=1){const a=K.value.arrivalEarlyAndLatestTime.split(" - ").map((e=>e.split(" ")));e=`${K.value.arrivalAndDeparture.split(" - ")[0]} ${"00:00"==a[0][1]?`${a[0][1]}12:00`:a[0][1]}`.replace(new RegExp("/","g"),"-")}else{const a=y(K.value.arriveTime).format("YYYY-MM-DD HH:mm");e="00:00"==a.split(" ")[1]?`${a.split(" ")[0]} 12:00`:a}return e}function re(){let e;if("ctrip"==Q.otaName&&-1==K.value.liveDays)e=`${K.value.arrival} ${te.value[1]}`;else if("ctrip"==Q.otaName&&K.value.liveDays>=1){const a=K.value.arrivalEarlyAndLatestTime.split(" - ").map((e=>e.split(" ")));e=`${K.value.arrivalAndDeparture.split(" - ")[1]} ${"00:00"==a[1][1]?`${a[1][1]}12:00`:a[1][1]}`.replace(new RegExp("/","g"),"-")}else{const a=y(K.value.checkOutDate).format("YYYY-MM-DD HH:mm");e="00:00"==a.split(" ")[1]?`${a.split(" ")[0]} ${ne.value.value}`:a}return e}function ue(e,a){const l=y(e);return y(a).diff(l,"day")}const ce=n([]);function me(e){return["星期日","星期一","星期二","星期三","星期四","星期五","星期六"][y(e).day()]}async function de(e){var a;await async function(){const{data:e}=await A.getRoomTypeRefList({...ee,channel:Q.otaName});ce.value=e}(),await async function(){const{data:e}=await L.simpleList({gcode:V.gcode,belongHcode:V.hcode,paType:1,isEnable:1});oe.value=e}(),await async function(){const{data:e}=await O.getCheckOutTime({gcode:V.gcode});ne.value=e}();const{data:l}=await q.getExistOtaOrder({...ee,outOrderNo:e.orderId||e.orderID});W.value=l;const t="ctrip";if(Q.otaName==t){Z.value=N.service({lock:!1,text:"Loading",background:"rgba(0, 0, 0, 0.7)"});const l=null==(a=Q.otaApiList)?void 0:a.filter((e=>e.channel==t&&"order_detail"==e.method))[0],o=JSON.parse(null==l?void 0:l.body);o.orderID=e.OrderID,o.formID=e.FormID,o.token=e.Token;const n={method:"otaRequest",api:{url:null==l?void 0:l.url,requestMethod:null==l?void 0:l.requestMethod,body:JSON.stringify(o),channel:t,headers:null==l?void 0:l.headers}},i=JSON.stringify(n);void 0===window.CallBridge&&void 0===window.__RUNNING_IN_PMS_AGENT__||!E.isConnected||E.isConnected&&(E.closeSocket(),await new Promise((e=>setTimeout(e,300)))),E.initCardReader((e=>{const{response:a}=JSON.parse(e);if(K.value=a.data,Z.value.close(),K.value){le.value.show();const e=[];a.data.clientName.split(",").forEach(((a,l)=>{e.push({name:a,roomIndex:l})})),K.value.clientName=e,te.value=K.value.arrivalEarlyAndLatestTime.split("-")}}));const s=setInterval((()=>{E.isConnected&&(E.otaRequest(i),clearInterval(s))}),200)}else le.value.show(),K.value=e}async function pe(){var e,a,l;const t=se().split(" "),o=re().split(" "),n=`${t[0]}/${o[0]}`,i=(Q.otaName,0),s=[];D(K.value.priceInfo)&&K.value.priceInfo.forEach((e=>{s.push({price:Number.parseFloat((e.floorPrice/100).toFixed(2)),priceDate:y(e.date).format("YYYY-MM-DD"),vipPrice:Number.parseFloat((e.floorPrice/100).toFixed(2))})})),D(K.value.orderRoomPrices)&&K.value.orderRoomPrices.forEach((e=>{s.push({price:e.roomPrice,priceDate:y(e.originLivingDate).format("YYYY-MM-DD"),vipPrice:e.roomPrice})}));const r=K.value.roomCount||K.value.Quantity;let u="";if("meituan"===Q.otaName?u=(null==(e=function(e,a){for(const l of e)for(const e of l.otaRoomTypes||[])if(e.otaRoomTypeCode==a)return l;return null}(ce.value,K.value.roomId))?void 0:e.roomTypeCode)||"":"ctrip"===Q.otaName&&(u=(null==(a=function(e,a){for(const l of e)for(const e of l.otaRoomTypes||[])for(const t of e.otaProducts||[])if(t.productCode==a)return l;return null}(ce.value,K.value.roomID))?void 0:a.roomTypeCode)||""),!u){const e=null==(l=Q.otaOptions)?void 0:l.filter((e=>e.value==Q.otaName))[0].label;return k({showClose:!0,message:`当前《${e}》产品未关联 pms房型，请前往 渠道-订单同步助手-${e}渠道-进行渠道房型绑定`,type:"warning",dangerouslyUseHTMLString:!0})}const c=ue(K.value.checkInDate||K.value.Arrival,K.value.checkOutDate||K.value.Departure),m=se(),d=re(),p=D(K.value.guests)&&K.value.guests.map((e=>e.name)).join(",")||D(K.value.clientName)&&K.value.clientName.map((e=>e.name)).join(","),f="meituan"===Q.otaName&&!K.value.hasPromotion||-1===K.value.liveDays?"hour_room":"all_day",g="ctrip"===Q.otaName&&-1===K.value.liveDays?y(d).diff(m,"hours"):"",v="meituan"===Q.otaName&&!K.value.hasPromotion||-1===K.value.liveDays?`${K.value.hourRoomTime||g}hour`:"",h=K.value.orderId||K.value.orderID,b=K.value.allinanceName||Q.otaName,_=K.value.contacts&&K.value.contacts[0].name||K.value.clientName&&K.value.clientName[0].name,N=oe.value.filter((e=>e.channel===Q.otaName&&e.paName.includes("预付")))[0].paCode,w={gcode:V.gcode,hcode:V.hcode,batches:[{batchNo:n,bookRoomTypes:[{bkNum:i,dayPrices:s,roomNum:r,rtCode:u}],days:c,planCheckinTime:m,planCheckoutTime:d}],guestName:p,planCheckinTime:m,planCheckoutTime:d,checkinPerson:p,checkinType:f,hourCode:v,bookType:"general",orderSource:"agent",outOrderNo:h,guestSrcType:"agent",isSendSms:"0",channelCode:b,contact:_,guestCode:N};le.value.loading=!0,S.createBook(w).then((()=>{k.success("预定成功"),le.value.visible=!1})).finally((()=>{le.value.loading=!1}))}return(a,l)=>{const t=w,o=C,n=x,i=Y,N=s("QuestionFilled"),D=T,k=I,O=M,L=R,S=j,q=$,A=P,E=e;return r(),u("div",F,[(r(!0),u(c,null,m(a.dataList,((e,s)=>(r(),d(O,{key:s,class:"meituan-row"},{default:p((()=>[f(o,{span:4,class:"tag"},{default:p((()=>[f(t,{type:"D"===e.OrderType?"primary":"success",effect:"dark"},{default:p((()=>[g(v(e.orderDisplayLabel||e.orderTypeDisplay),1)])),_:2},1032,["type"])])),_:2},1024),f(o,{span:10},{default:p((()=>[g(v(e.orderId||e.orderID),1)])),_:2},1024),f(o,{span:2},{default:p((()=>[f(n,{class:"h-[15px] w-[15px]",src:h(ae).filter((e=>e.channelCode===a.otaName))[0].image,fit:"cover"},null,8,["src"])])),_:2},1024),f(o,{span:5},{default:p((()=>[g(v(h(y)(e.bookSucTime||e.FormDate).format("MM/DD HH:mm")),1)])),_:2},1024),f(o,{span:3,class:"text-right"},{default:p((()=>l[0]||(l[0]=[g(" 已接单 ")]))),_:1}),f(o,{span:4,class:"line"},{default:p((()=>[g(v(e.roomCount||e.Quantity)+"间 ",1)])),_:2},1024),f(o,{span:16,class:"roomName"},{default:p((()=>[e.roomName.length>23?(r(),d(i,{key:0,effect:"dark",placement:"top-start",content:e.roomName},{default:p((()=>[b("span",null,v(e.roomName),1)])),_:2},1032,["content"])):(r(),u(c,{key:1},[g(v(e.roomName),1)],64))])),_:2},1024),f(o,{span:4,class:"text-right"},{default:p((()=>{var l,t,o,n;return[(null==(l=e.guests)?void 0:l.length)>1&&"meituan"===a.otaName?(r(),d(i,{key:0,effect:"dark",placement:"top-start"},{content:p((()=>[(r(!0),u(c,null,m(e.guests,(e=>(r(),u("div",{key:e.roomIndex},v(e.name),1)))),128))])),default:p((()=>{var a;return[b("span",H,[g(v(null==(a=e.guests[0])?void 0:a.name)+" ",1),f(D,{class:"ml-[2px]"},{default:p((()=>[f(N)])),_:1})])]})),_:2},1024)):(null==(t=e.clientName)?void 0:t.length)>1&&"ctrip"===a.otaName?(r(),d(i,{key:1,effect:"dark",placement:"top-start"},{content:p((()=>[(r(!0),u(c,null,m(e.clientName,(e=>(r(),u("div",{key:e.roomIndex},v(e.name),1)))),128))])),default:p((()=>{var a;return[b("span",B,[g(v(null==(a=e.clientName[0])?void 0:a.name)+" ",1),f(D,{class:"ml-[2px]"},{default:p((()=>[f(N)])),_:1})])]})),_:2},1024)):(r(),u("span",J,v((null==(o=e.guests[0])?void 0:o.name)||(null==(n=e.clientName[0])?void 0:n.name)),1))]})),_:2},1024),f(o,{span:4,class:"line"},{default:p((()=>[g(v(ue(e.checkInDate||e.Arrival,e.checkOutDate||e.Departure))+"晚 ",1)])),_:2},1024),f(o,{span:16},{default:p((()=>[g(v(h(y)(e.checkInDate||e.Arrival).format("YYYY/MM/DD"))+" - "+v(h(y)(e.checkOutDate||e.Departure).format("YYYY/MM/DD")),1)])),_:2},1024),f(o,{span:4,class:"text-right"},{default:p((()=>[f(k,{type:"primary",size:"small",link:"",onClick:a=>de(e)},{default:p((()=>[g(v(h(U)("viewDetails")),1)])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128)),h(X)?_("",!0):(r(),d(L,{key:0},{default:p((()=>[f(k,{type:"primary",size:"small",link:"",onClick:ie},{default:p((()=>[g(v(h(U)("more")),1)])),_:1})])),_:1})),f(E,{ref_key:"easyDialogRef",ref:le,title:h(U)("viewDetails"),"is-body":"","show-cancel-button":"","show-confirm-button":!h(W),"confirm-text":"创建订单","cance-text":"关闭","dialog-width":"600",onSubmit:pe},{default:p((()=>[h(K)?(r(),d(A,{key:0,column:1,border:""},{default:p((()=>[f(S,{label:"订单号","label-align":"right","label-class-name":"my-label"},{default:p((()=>[g(v(h(K).orderId||h(K).orderID),1)])),_:1}),f(S,{label:"PMS订单同步状态","label-align":"right","label-class-name":"my-label"},{default:p((()=>[h(W)?(r(),d(t,{key:0,type:"success"},{default:p((()=>l[1]||(l[1]=[g(" 已创建 ")]))),_:1})):(r(),d(t,{key:1,type:"danger"},{default:p((()=>l[2]||(l[2]=[g(" 未创建 ")]))),_:1}))])),_:1}),f(S,{label:"酒店名称","label-align":"right","label-class-name":"my-label"},{default:p((()=>[g(v(h(K).poiName||h(K).HotelName),1)])),_:1}),f(S,{label:"客人姓名","label-align":"right","label-class-name":"my-label","class-name":"my-box"},{default:p((()=>{var e,a;return[(null==(e=h(K).guests)?void 0:e.length)>0?(r(!0),u(c,{key:0},m(h(K).guests,((e,a)=>(r(),d(q,{key:a,class:"mx-1"},{default:p((()=>[g(v(e.name),1)])),_:2},1024)))),128)):_("",!0),(null==(a=h(K).clientName)?void 0:a.length)>0?(r(!0),u(c,{key:1},m(h(K).clientName,((e,a)=>(r(),d(q,{key:a,class:"mx-1"},{default:p((()=>[g(v(e.name),1)])),_:2},1024)))),128)):_("",!0)]})),_:1}),f(S,{label:"房型名称","label-align":"right","label-class-name":"my-label"},{default:p((()=>[g(v(h(K).roomName||h(K).BedType),1)])),_:1}),f(S,{label:"间数","label-align":"right","label-class-name":"my-label"},{default:p((()=>[f(t,null,{default:p((()=>[g(v(h(K).roomCount||h(K).Quantity)+"间",1)])),_:1})])),_:1}),f(S,{label:"入住时间","label-align":"right","label-class-name":"my-label"},{default:p((()=>[g(v(se()),1)])),_:1}),f(S,{label:"离店时间","label-align":"right","label-class-name":"my-label"},{default:p((()=>[g(v(re()),1)])),_:1}),"meituan"===a.otaName&&!h(K).hasPromotion||-1===h(K).liveDays?(r(),d(S,{key:0,label:"入住类型","label-align":"right","label-class-name":"my-label"},{default:p((()=>[f(t,{type:"danger"},{default:p((()=>l[3]||(l[3]=[g(" 钟点房 ")]))),_:1})])),_:1})):_("",!0),f(S,{label:"天数","label-align":"right","label-class-name":"my-label"},{default:p((()=>[g(v(ue(h(K).checkInDate||h(K).Arrival,h(K).checkOutDate||h(K).Departure))+"晚 ",1)])),_:1}),f(S,{label:"下单时间","label-align":"right","label-class-name":"my-label"},{default:p((()=>[g(v(h(K).bookingTimeString||h(K).formDate),1)])),_:1}),f(S,{label:"底价构成","label-align":"right","label-class-name":"my-label"},{default:p((()=>["meituan"===a.otaName?(r(),d(O,{key:0},{default:p((()=>[(r(!0),u(c,null,m(h(K).priceInfo,((e,a)=>(r(),d(o,{key:a,span:24},{default:p((()=>[f(q,{type:"danger"},{default:p((()=>[g(v(`￥${parseFloat((e.floorPrice/100).toFixed(2))}`),1)])),_:2},1024),f(q,{class:"!ml-[20px]"},{default:p((()=>[g(v(h(y)(e.date).format("YYYY-MM-DD")),1)])),_:2},1024),f(q,{class:"!ml-[20px]"},{default:p((()=>[g(v(`(${me(e.date)})`),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):_("",!0),"ctrip"===a.otaName?(r(),d(O,{key:1,style:{"max-height":"200px","overflow-y":"auto"}},{default:p((()=>[(r(!0),u(c,null,m(h(K).orderRoomPrices,((e,a)=>(r(),d(o,{key:a,span:24},{default:p((()=>[f(q,{type:"danger"},{default:p((()=>[g(v(`￥${e.roomPrice}`),1)])),_:2},1024),f(q,{class:"!ml-[20px]"},{default:p((()=>[g(v(h(y)(e.originLivingDate).format("YYYY-MM-DD")),1)])),_:2},1024),f(q,{class:"!ml-[20px]"},{default:p((()=>[g(v(`(${me(e.originLivingDate)})`),1)])),_:2},1024),f(q,{class:"!ml-[20px]"},{default:p((()=>[g(v(e.mealInfo),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):_("",!0)])),_:1})])),_:1})):_("",!0)])),_:1},8,["title","show-confirm-button"])])}}});function G(e){const a=e;a.__i18n=a.__i18n||[],a.__i18n.push({locale:"",resource:{en:{viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"View details"}},more:{t:0,b:{t:2,i:[{t:3}],s:"Load more"}}},"zh-cn":{viewDetails:{t:0,b:{t:2,i:[{t:3}],s:"查看详情"}},more:{t:0,b:{t:2,i:[{t:3}],s:"加载更多"}}}}})}G(Q);const U=z(Q,[["__scopeId","data-v-0bd3a7cc"]]);export{U as default};
//# sourceMappingURL=roomOrderList-DFgKngsf.js.map
