{"root": ["./src/main.ts", "./src/settings.default.ts", "./src/settings.ts", "./src/api/index.ts", "./src/api/modules/app.ts", "./src/api/modules/user.ts", "./src/components/sparkline/sparkline.ts", "./src/components/spinkitloading/index.ts", "./src/iconify/index.ts", "./src/layouts/components/menu/types.ts", "./src/layouts/components/menupanel/types.ts", "./src/locales/index.ts", "./src/menu/index.ts", "./src/menu/modules/multilevel.menu.example.ts", "./src/mock/app.ts", "./src/mock/user.ts", "./src/router/guards.ts", "./src/router/index.ts", "./src/router/routes.ts", "./src/router/modules/multilevel.menu.example.ts", "./src/router/modules/test.ts", "./src/router/modules/test2.ts", "./src/router/modules/test3.ts", "./src/store/index.ts", "./src/store/modules/favorites.ts", "./src/store/modules/iframe.ts", "./src/store/modules/keepalive.ts", "./src/store/modules/menu.ts", "./src/store/modules/notification.ts", "./src/store/modules/route.ts", "./src/store/modules/settings.ts", "./src/store/modules/tabbar.ts", "./src/store/modules/user.ts", "./src/store/modules/watermark.ts", "./src/types/auto-imports.d.ts", "./src/types/components.d.ts", "./src/types/global.d.ts", "./src/types/shims.d.ts", "./src/ui-provider/index.ts", "./src/utils/dayjs.ts", "./src/utils/directive.ts", "./src/utils/error.log.ts", "./src/utils/eventbus.ts", "./src/utils/index.ts", "./src/utils/storage.ts", "./src/utils/system.copyright.ts", "./src/utils/composables/useauth.ts", "./src/utils/composables/useglobalproperties.ts", "./src/utils/composables/usemainpage.ts", "./src/utils/composables/usemenu.ts", "./src/utils/composables/usepagination.ts", "./src/utils/composables/usetabbar.ts", "./src/utils/composables/usetimeago.ts", "./src/utils/composables/useviewtransition.ts", "./src/app.vue", "./src/components/auth/index.vue", "./src/components/chip/index.vue", "./src/components/colorfulcard/index.vue", "./src/components/fileupload/index.vue", "./src/components/fixedactionbar/index.vue", "./src/components/iconpicker/index.vue", "./src/components/imagepreview/index.vue", "./src/components/imageupload/index.vue", "./src/components/imagesupload/index.vue", "./src/components/layoutcontainer/index.vue", "./src/components/loginform/index.vue", "./src/components/notallowed/index.vue", "./src/components/pageheader/index.vue", "./src/components/pagemain/index.vue", "./src/components/pcascascader/index.vue", "./src/components/registerform/index.vue", "./src/components/resetpasswordform/index.vue", "./src/components/searchbar/index.vue", "./src/components/sparkline/index.vue", "./src/components/spinkitloading/index.vue", "./src/components/storagebox/index.vue", "./src/components/svgicon/index.vue", "./src/components/systeminfo/index.vue", "./src/components/trend/index.vue", "./src/layouts/index.vue", "./src/layouts/components/appsetting/index.vue", "./src/layouts/components/backtop/index.vue", "./src/layouts/components/breadcrumb/index.vue", "./src/layouts/components/breadcrumb/item.vue", "./src/layouts/components/copyright/index.vue", "./src/layouts/components/header/index.vue", "./src/layouts/components/hotkeysintro/index.vue", "./src/layouts/components/logo/index.vue", "./src/layouts/components/mainsidebar/index.vue", "./src/layouts/components/menu/index.vue", "./src/layouts/components/menu/item.vue", "./src/layouts/components/menu/sub.vue", "./src/layouts/components/menupanel/index.vue", "./src/layouts/components/menupanel/item.vue", "./src/layouts/components/menupanel/sub.vue", "./src/layouts/components/preferences/index.vue", "./src/layouts/components/search/index.vue", "./src/layouts/components/submainsidebar/index.vue", "./src/layouts/components/subsidebar/index.vue", "./src/layouts/components/topbar/index.vue", "./src/layouts/components/topbar/tabbar/index.vue", "./src/layouts/components/topbar/tabbar/moreaction.vue", "./src/layouts/components/topbar/toolbar/index.vue", "./src/layouts/components/topbar/toolbar/leftside.vue", "./src/layouts/components/topbar/toolbar/rightside.vue", "./src/layouts/components/topbar/toolbar/tools.vue", "./src/layouts/components/topbar/toolbar/breadcrumb/index.vue", "./src/layouts/components/topbar/toolbar/colorscheme/index.vue", "./src/layouts/components/topbar/toolbar/favorites/index.vue", "./src/layouts/components/topbar/toolbar/favorites/panel.vue", "./src/layouts/components/topbar/toolbar/fullscreen/index.vue", "./src/layouts/components/topbar/toolbar/i18n/index.vue", "./src/layouts/components/topbar/toolbar/navsearch/index.vue", "./src/layouts/components/topbar/toolbar/notification/index.vue", "./src/layouts/components/topbar/toolbar/notification/panel.vue", "./src/layouts/components/topbar/toolbar/pagereload/index.vue", "./src/layouts/components/views/iframe.vue", "./src/layouts/components/views/link.vue", "./src/layouts/ui-kit/hbadge.vue", "./src/layouts/ui-kit/hbutton.vue", "./src/layouts/ui-kit/hchecklist.vue", "./src/layouts/ui-kit/hdialog.vue", "./src/layouts/ui-kit/hdropdown.vue", "./src/layouts/ui-kit/hdropdownmenu.vue", "./src/layouts/ui-kit/hinput.vue", "./src/layouts/ui-kit/hkbd.vue", "./src/layouts/ui-kit/hselect.vue", "./src/layouts/ui-kit/hslideover.vue", "./src/layouts/ui-kit/htablist.vue", "./src/layouts/ui-kit/htoggle.vue", "./src/layouts/ui-kit/htooltip.vue", "./src/ui-provider/index.vue", "./src/views/[...all].vue", "./src/views/index.vue", "./src/views/login.vue", "./src/views/reload.vue", "./src/views/multilevel_menu_example/page.vue", "./src/views/multilevel_menu_example/level2/page.vue", "./src/views/multilevel_menu_example/level2/level3/page1.vue", "./src/views/multilevel_menu_example/level2/level3/page2.vue", "./src/views/personal/notification.vue", "./src/views/test/index.vue"], "version": "5.6.3"}