import{d as t,aj as a,ai as l,y as r,B as n,b as e,o,c as s,g as u,Y as d,F as m,ag as i,R as f}from"./index-CkEhI1Zk.js";import{_ as c}from"./_plugin-vue_export-helper-BCo6x5W8.js";const h={class:"pos-print-container"},p={class:"hotel-title"},D={class:"order-info"},v={class:"order-no"},g={class:"print-time"},b={class:"info-table"},N={colspan:"3"},y={colspan:"3"},k={colspan:"3"},_={colspan:"3"},S={colspan:"3"},j={colspan:"3"},x={colspan:"3"},F={colspan:"3"},O={colspan:"3"},R={colspan:"3"},T={colspan:"3"},$={colspan:"3"},w={colspan:"3"},B={key:0,class:"together-guests"},I={class:"info-table together-table"},L={class:"footer"},Y={class:"footer-line"},q={class:"footer-line"},A=c(t({__name:"posCheckInForm",props:{formData:{type:Object,required:!0}},setup(t){a(),l();const c=t;r((()=>{}));const A=t=>t||"",C=n((()=>({name:c.formData.hname,phone:c.formData.frontPhone||"",address:c.formData.address||""}))),M=e(c.formData.printDate||(new Date).toLocaleString()),P=n((()=>c.formData.togetherRespVOList||[])),V=t=>{if(!t)return"";try{const a=new Date(t);return`${a.getFullYear()}/${String(a.getMonth()+1).padStart(2,"0")}/${String(a.getDate()).padStart(2,"0")}`}catch(a){return t}};return(t,a)=>(o(),s("div",h,[u("div",p,d(C.value.name),1),a[20]||(a[20]=u("div",{class:"form-title"},"入住登记单",-1)),u("div",D,[u("div",v,"NO:"+d(c.formData.orderNo),1),u("div",g,"打印时间:"+d(M.value),1)]),u("table",b,[u("tbody",null,[u("tr",null,[a[0]||(a[0]=u("th",null,"房号",-1)),u("td",N,d(c.formData.rNo)+"("+d(c.formData.buildingName)+" - "+d(c.formData.floorName)+")",1)]),u("tr",null,[a[1]||(a[1]=u("th",null,"首日价",-1)),u("td",null,d(c.formData.firstDayFee),1),a[2]||(a[2]=u("th",null,"房型",-1)),u("td",null,d(c.formData.rtName),1)]),u("tr",null,[a[3]||(a[3]=u("th",null,"房包早",-1)),u("td",null,d(c.formData.roomBkNum||0),1),a[4]||(a[4]=u("th",null,"赠早",-1)),u("td",null,d(c.formData.bkNum||0),1)]),u("tr",null,[a[5]||(a[5]=u("th",null,"姓名",-1)),u("td",null,d(c.formData.name),1),a[6]||(a[6]=u("th",null,"民族",-1)),u("td",null,d(c.formData.nation||""),1)]),u("tr",null,[a[7]||(a[7]=u("th",null,"手机号",-1)),u("td",y,d(c.formData.phone||""),1)]),u("tr",null,[a[8]||(a[8]=u("th",null,"证件类型",-1)),u("td",k,d(c.formData.idTypeName),1)]),u("tr",null,[a[9]||(a[9]=u("th",null,"证件号",-1)),u("td",_,d(c.formData.idNo),1)]),u("tr",null,[a[10]||(a[10]=u("th",null,"性别",-1)),u("td",S,d(c.formData.sex),1)]),u("tr",null,[a[11]||(a[11]=u("th",null,"出生日期",-1)),u("td",j,d(V(c.formData.birthday)),1)]),u("tr",null,[a[12]||(a[12]=u("th",null,"地址",-1)),u("td",x,d(c.formData.userAddress||""),1)]),u("tr",null,[a[13]||(a[13]=u("th",null,"入住时间",-1)),u("td",F,d(A(c.formData.checkinTime)),1)]),u("tr",null,[a[14]||(a[14]=u("th",null,"离店时间",-1)),u("td",O,d(A(c.formData.checkoutTime)),1)]),u("tr",null,[a[15]||(a[15]=u("th",null,"入住天数",-1)),u("td",R,d(c.formData.nights),1)]),u("tr",null,[u("th",null,d(c.formData.subName),1),u("td",T,d(c.formData.fee||0),1)]),u("tr",null,[a[16]||(a[16]=u("th",null,"订单备注",-1)),u("td",$,d(c.formData.orderRemark||""),1)]),u("tr",null,[a[17]||(a[17]=u("th",null,"备注",-1)),u("td",w,d(c.formData.remark||""),1)])])]),P.value.length>0?(o(),s("div",B,[a[18]||(a[18]=u("div",{class:"section-title"},"同住人",-1)),u("table",I,[u("tbody",null,[(o(!0),s(m,null,i(P.value,((t,a)=>(o(),s("tr",{key:a},[u("td",null,"姓名："+d(t.name||"-")+" 手机号："+d(t.phone||"-")+" 证件号："+d(t.idNo||"-"),1)])))),128))])])])):f("",!0),u("div",L,[a[19]||(a[19]=u("div",{class:"footer-line"},"责任签名：",-1)),u("div",Y,"酒店电话："+d(C.value.phone),1),u("div",q,"酒店地址："+d(C.value.address),1)])]))}}),[["__scopeId","data-v-1b8ec56f"]]);export{A as default};
//# sourceMappingURL=posCheckInForm-BJfHKK6-.js.map
