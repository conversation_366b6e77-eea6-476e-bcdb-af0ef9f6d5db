// vite.config.ts
import fs from "node:fs";
import path2 from "node:path";
import process2 from "node:process";
import dayjs from "file:///D:/11/2/aflower-pms-front/node_modules/dayjs/dayjs.min.js";
import { defineConfig, loadEnv as loadEnv2 } from "file:///D:/11/2/aflower-pms-front/node_modules/vite/dist/node/index.js";

// package.json
var package_default = {
  type: "module",
  version: "4.12.0",
  engines: {
    node: "^18.18.0 || ^20.9.0 || >=21.1.0"
  },
  scripts: {
    dev: "vite",
    build: "vite build",
    "build:test": "node --max_old_space_size=1024000 ./node_modules/vite/bin/vite.js build --mode test",
    serve: "http-server ./dist -o",
    "serve:test": "http-server ./dist-test -o",
    svgo: "svgo -f src/assets/icons",
    new: "plop",
    "generate:icons": "esno ./scripts/generate.icons.ts",
    lint: "npm-run-all -s lint:tsc lint:eslint lint:stylelint",
    "lint:tsc": "vue-tsc -b",
    "lint:eslint": "eslint . --cache --fix",
    "lint:stylelint": 'stylelint "src/**/*.{css,scss,vue}" --cache --fix',
    postinstall: "simple-git-hooks",
    preinstall: "npx only-allow pnpm",
    release: "bumpp"
  },
  dependencies: {
    "@headlessui/vue": "^1.7.23",
    "@imengyu/vue3-context-menu": "^1.4.3",
    "@vueuse/components": "^11.1.0",
    "@vueuse/core": "^11.1.0",
    "@vueuse/integrations": "^11.1.0",
    axios: "^1.7.7",
    dayjs: "^1.11.13",
    "decimal.js": "^10.5.0",
    defu: "^6.1.4",
    "disable-devtool": "^0.3.8",
    "element-plus": "^2.8.5",
    eruda: "^3.4.0",
    "event-source-polyfill": "^1.0.31",
    "floating-vue": "5.2.2",
    "hotkeys-js": "^3.13.7",
    "lodash-es": "^4.17.21",
    "medium-zoom": "^1.1.0",
    mitt: "^3.0.1",
    mockjs: "^1.1.0",
    nprogress: "^0.2.0",
    overlayscrollbars: "^2.10.0",
    "overlayscrollbars-vue": "^0.5.9",
    "path-browserify": "^1.0.1",
    "path-to-regexp": "^8.2.0",
    pinia: "^2.2.4",
    "pinyin-pro": "^3.25.0",
    qs: "^6.13.0",
    scule: "^1.3.0",
    sortablejs: "^1.15.3",
    spinkit: "^2.0.1",
    splitpanes: "^3.1.5",
    "timeago.js": "^4.0.2",
    "v-wave": "^3.0.2",
    vconsole: "^3.15.1",
    vue: "^3.5.12",
    "vue-currency-input": "^3.1.0",
    "vue-i18n": "^10.0.4",
    "vue-m-message": "^4.0.2",
    "vue-router": "^4.4.5",
    "watermark-js-plus": "^1.5.7",
    uuid: "^11.1.0"
  },
  devDependencies: {
    "@antfu/eslint-config": "3.8.0",
    "@iconify/json": "^2.2.261",
    "@iconify/vue": "^4.1.2",
    "@intlify/unplugin-vue-i18n": "^5.2.0",
    "@stylistic/stylelint-config": "^2.0.0",
    "@types/lodash-es": "^4.17.12",
    "@types/mockjs": "^1.0.10",
    "@types/nprogress": "^0.2.3",
    "@types/path-browserify": "^1.0.3",
    "@types/qs": "^6.9.16",
    "@types/sortablejs": "^1.15.8",
    "@unocss/eslint-plugin": "^0.63.4",
    "@vitejs/plugin-legacy": "^5.4.2",
    "@vitejs/plugin-vue": "^5.1.4",
    "@vitejs/plugin-vue-jsx": "^4.0.1",
    autoprefixer: "^10.4.20",
    boxen: "^8.0.1",
    bumpp: "^9.7.1",
    "cz-git": "^1.10.1",
    eslint: "^9.12.0",
    esno: "^4.8.0",
    "fs-extra": "^11.2.0",
    "http-server": "^14.1.1",
    inquirer: "^12.0.0",
    "lint-staged": "^15.2.10",
    "npm-run-all2": "^6.2.4",
    picocolors: "^1.1.1",
    plop: "^4.0.1",
    postcss: "^8.4.47",
    "postcss-nested": "^6.2.0",
    "sass-embedded": "^1.80.2",
    "simple-git-hooks": "^2.11.1",
    stylelint: "^16.10.0",
    "stylelint-config-recess-order": "^5.1.1",
    "stylelint-config-standard-scss": "^13.1.0",
    "stylelint-config-standard-vue": "^1.0.0",
    "stylelint-scss": "^6.8.1",
    svgo: "^3.3.2",
    typescript: "^5.6.3",
    unocss: "^0.63.4",
    "unocss-preset-scrollbar": "^0.3.1",
    "unplugin-auto-import": "^0.18.3",
    "unplugin-icons": "^0.16.6",
    "unplugin-turbo-console": "^1.10.4",
    "unplugin-vue-components": "^0.27.4",
    vite: "^5.4.9",
    "vite-plugin-app-loading": "^0.3.0",
    "vite-plugin-archiver": "^0.1.1",
    "vite-plugin-banner": "^0.8.0",
    "vite-plugin-compression2": "^1.3.0",
    "vite-plugin-fake-server": "^2.1.2",
    "vite-plugin-pages": "^0.32.3",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.5.2",
    "vite-plugin-vue-meta-layouts": "^0.4.3",
    "vue-tsc": "^2.1.6"
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite/plugins.ts
import path from "node:path";
import process from "node:process";
import vueI18n from "file:///D:/11/2/aflower-pms-front/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
import vueLegacy from "file:///D:/11/2/aflower-pms-front/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import vue from "file:///D:/11/2/aflower-pms-front/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/11/2/aflower-pms-front/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import boxen from "file:///D:/11/2/aflower-pms-front/node_modules/boxen/index.js";
import picocolors from "file:///D:/11/2/aflower-pms-front/node_modules/picocolors/picocolors.js";
import Unocss from "file:///D:/11/2/aflower-pms-front/node_modules/unocss/dist/vite.mjs";
import autoImport from "file:///D:/11/2/aflower-pms-front/node_modules/unplugin-auto-import/dist/vite.js";
import TurboConsole from "file:///D:/11/2/aflower-pms-front/node_modules/unplugin-turbo-console/dist/vite.mjs";
import components from "file:///D:/11/2/aflower-pms-front/node_modules/unplugin-vue-components/dist/vite.js";
import { loadEnv } from "file:///D:/11/2/aflower-pms-front/node_modules/vite/dist/node/index.js";
import AppLoading from "file:///D:/11/2/aflower-pms-front/node_modules/vite-plugin-app-loading/dist/index.js";
import Archiver from "file:///D:/11/2/aflower-pms-front/node_modules/vite-plugin-archiver/dist/index.js";
import { compression } from "file:///D:/11/2/aflower-pms-front/node_modules/vite-plugin-compression2/dist/index.mjs";
import Pages from "file:///D:/11/2/aflower-pms-front/node_modules/vite-plugin-pages/dist/index.js";
import { createSvgIconsPlugin } from "file:///D:/11/2/aflower-pms-front/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import VueDevTools from "file:///D:/11/2/aflower-pms-front/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import Layouts from "file:///D:/11/2/aflower-pms-front/node_modules/vite-plugin-vue-meta-layouts/dist/index.mjs";
import { ElementPlusResolver } from "file:///D:/11/2/aflower-pms-front/node_modules/unplugin-vue-components/dist/resolvers.js";
import IconsResolver from "file:///D:/11/2/aflower-pms-front/node_modules/unplugin-icons/dist/resolver.mjs";
import Icons from "file:///D:/11/2/aflower-pms-front/node_modules/unplugin-icons/dist/vite.mjs";
function createVitePlugins(mode, isBuild = false) {
  const viteEnv = loadEnv(mode, process.cwd());
  const vitePlugins = [
    vue(),
    vueJsx(),
    vueLegacy({
      renderLegacyChunks: false,
      modernPolyfills: ["es.array.at", "es.array.find-last"]
    }),
    // https://github.com/vuejs/devtools-next
    viteEnv.VITE_OPEN_DEVTOOLS === "true" && VueDevTools(),
    // https://github.com/unplugin/unplugin-auto-import
    autoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ["vue", "vue-router", "pinia", "vue-i18n"],
      dts: false,
      // 配置文件生成位置(false:关闭自动生成)
      // dts: './src/types/auto-imports.d.ts', // 配置文件生成位置(false:关闭自动生成)
      dirs: ["./src/utils/composables/**"],
      // 指定自定义组件位置(默认:src/components)
      // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
      resolvers: [ElementPlusResolver(), IconsResolver({})],
      eslintrc: {
        enabled: false,
        filepath: "./.eslintrc-auto-import.json",
        globalsPropValue: true
      },
      vueTemplate: true
    }),
    // https://github.com/unplugin/unplugin-vue-components
    components({
      dirs: ["src/components", "src/layouts/ui-kit"],
      // 按需加载的文件夹
      include: [/\.vue$/, /\.vue\?vue/, /\.tsx$/],
      dts: "./src/types/components.d.ts",
      // 配置文件位置 (false:关闭自动生成)
      resolvers: [
        // 自动导入 Element Plus 组件
        ElementPlusResolver(),
        // 自动注册图标组件
        IconsResolver({ enabledCollections: ["ep"] })
      ]
    }),
    Unocss({
      hmrTopLevelAwait: false
    }),
    Icons({
      autoInstall: true
    }),
    // https://github.com/vbenjs/vite-plugin-svg-icons
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), "src/assets/icons/")],
      // 指定需要缓存的图标文件夹
      symbolId: "icon-[dir]-[name]",
      // 指定symbolId格式
      svgoOptions: isBuild
    }),
    // https://github.com/intlify/vue-i18n
    vueI18n({
      include: path.resolve(process.cwd(), "src/locales/lang/**")
    }),
    // https://github.com/dishait/vite-plugin-vue-meta-layouts
    Layouts({
      defaultLayout: "index"
    }),
    // https://github.com/hannoeru/vite-plugin-pages
    Pages({
      dirs: "src/views",
      exclude: ["**/components/**/*.vue"]
    }),
    // https://github.com/nonzzz/vite-plugin-compression
    viteEnv.VITE_BUILD_COMPRESS?.split(",").includes("gzip") && compression(),
    viteEnv.VITE_BUILD_COMPRESS?.split(",").includes("brotli") && compression({
      exclude: [/\.(br)$/, /\.(gz)$/],
      algorithm: "brotliCompress"
    }),
    viteEnv.VITE_BUILD_ARCHIVE && Archiver({
      archiveType: viteEnv.VITE_BUILD_ARCHIVE
    }),
    AppLoading("loading.html"),
    // https://github.com/unplugin/unplugin-turbo-console
    TurboConsole(),
    {
      name: "vite-plugin-debug-plugin",
      enforce: "pre",
      transform: (code, id) => {
        if (/src\/main.ts$/.test(id)) {
          if (viteEnv.VITE_APP_DEBUG_TOOL === "eruda") {
            code = code.concat(`
              import eruda from 'eruda'
              eruda.init()
            `);
          } else if (viteEnv.VITE_APP_DEBUG_TOOL === "vconsole") {
            code = code.concat(`
              import VConsole from 'vconsole'
              new VConsole()
            `);
          }
          return {
            code,
            map: null
          };
        }
      }
    },
    {
      name: "vite-plugin-disable-devtool",
      enforce: "pre",
      transform: (code, id) => {
        if (/src\/main.ts$/.test(id)) {
          if (viteEnv.VITE_APP_DISABLE_DEVTOOL === "true") {
            code = code.concat(`
              import DisableDevtool from 'disable-devtool'
              DisableDevtool()
            `);
          }
          return {
            code,
            map: null
          };
        }
      }
    },
    {
      name: "vite-plugin-terminal-info",
      apply: "serve",
      async buildStart() {
        const { bold, green, magenta, bgGreen, underline } = picocolors;
        console.log(
          boxen(
            `${bold(
              green(`\u7531 ${bgGreen("\u4E00\u6735\u82B1\u9152\u5E97\u79D1\u6280")} \u9A71\u52A8`)
            )}

${underline(
              "https://www.yiduohua.net"
            )}

\u5F53\u524D\u4F7F\u7528\uFF1A${magenta("\u4E13\u4E1A\u7248")}`,
            {
              padding: 1,
              margin: 1,
              borderStyle: "double",
              textAlignment: "center"
            }
          )
        );
      }
    }
  ];
  return vitePlugins;
}

// vite.config.ts
var __vite_injected_original_dirname = "D:\\11\\2\\aflower-pms-front";
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv2(mode, process2.cwd());
  const scssResources = [];
  fs.readdirSync("src/assets/styles/resources").forEach((dirname) => {
    if (fs.statSync(`src/assets/styles/resources/${dirname}`).isFile()) {
      scssResources.push(`@use "/src/assets/styles/resources/${dirname}" as *;`);
    }
  });
  return {
    base: env.VITE_BASE_PREFIX,
    // 开发服务器选项 https://cn.vitejs.dev/config/server-options
    server: {
      open: true,
      port: 9006,
      proxy: {
        /**
         * 反向代理解决跨域配置
         * http://localhost:3000/proxy/users (F12可见请求路径) => http://localhost:8989/users (实际请求后端 API 路径)
         *
         * env.VITE_APP_BASE_API: /dev-apizhebus
         * env.VITE_APP_API_BASEURL: http://localhost:8989
         */
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_API_BASEURL,
          changeOrigin: true,
          rewrite: (path3) => path3.replace(`${env.VITE_APP_BASE_API}`, "")
        }
      },
      host: "0.0.0.0"
    },
    // 构建选项 https://cn.vitejs.dev/config/build-options
    build: {
      outDir: mode === "production" ? "dist" : `dist-${mode}`,
      sourcemap: env.VITE_BUILD_SOURCEMAP === "true",
      chunkSizeWarningLimit: 2e3,
      // 消除打包大小超过500kb警告
      minify: "terser",
      // Vite 2.6.x 以上需要配置 minify: "terser", terserOptions 才能生效
      terserOptions: {
        compress: {
          keep_infinity: true,
          // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
          drop_console: false,
          // 生产环境去除 console
          drop_debugger: false
          // 生产环境去除 debugger
        },
        format: {
          comments: false
          // 删除注释
        }
      }
    },
    // 预加载项目必需的组件
    optimizeDeps: {
      include: [
        "vue",
        "vue-router",
        "pinia",
        "axios",
        "element-plus/es/components/form/style/css",
        "element-plus/es/components/form-item/style/css",
        "element-plus/es/components/button/style/css",
        "element-plus/es/components/input/style/css",
        "element-plus/es/components/input-number/style/css",
        "element-plus/es/components/switch/style/css",
        "element-plus/es/components/upload/style/css",
        "element-plus/es/components/menu/style/css",
        "element-plus/es/components/col/style/css",
        "element-plus/es/components/icon/style/css",
        "element-plus/es/components/row/style/css",
        "element-plus/es/components/tag/style/css",
        "element-plus/es/components/dialog/style/css",
        "element-plus/es/components/loading/style/css",
        "element-plus/es/components/radio/style/css",
        "element-plus/es/components/radio-group/style/css",
        "element-plus/es/components/popover/style/css",
        "element-plus/es/components/scrollbar/style/css",
        "element-plus/es/components/tooltip/style/css",
        "element-plus/es/components/dropdown/style/css",
        "element-plus/es/components/dropdown-menu/style/css",
        "element-plus/es/components/dropdown-item/style/css",
        "element-plus/es/components/sub-menu/style/css",
        "element-plus/es/components/menu-item/style/css",
        "element-plus/es/components/divider/style/css",
        "element-plus/es/components/card/style/css",
        "element-plus/es/components/link/style/css",
        "element-plus/es/components/breadcrumb/style/css",
        "element-plus/es/components/breadcrumb-item/style/css",
        "element-plus/es/components/table/style/css",
        "element-plus/es/components/tree-select/style/css",
        "element-plus/es/components/table-column/style/css",
        "element-plus/es/components/select/style/css",
        "element-plus/es/components/option/style/css",
        "element-plus/es/components/pagination/style/css",
        "element-plus/es/components/tree/style/css",
        "element-plus/es/components/alert/style/css",
        "element-plus/es/components/radio-button/style/css",
        "element-plus/es/components/checkbox-group/style/css",
        "element-plus/es/components/checkbox/style/css",
        "element-plus/es/components/tabs/style/css",
        "element-plus/es/components/tab-pane/style/css",
        "element-plus/es/components/rate/style/css",
        "element-plus/es/components/date-picker/style/css",
        "element-plus/es/components/notification/style/css",
        "element-plus/es/components/image/style/css",
        "element-plus/es/components/statistic/style/css",
        "element-plus/es/components/watermark/style/css",
        "element-plus/es/components/config-provider/style/css",
        "@vueuse/core",
        "sortablejs",
        "path-to-regexp",
        "echarts",
        "@wangeditor/editor",
        "@wangeditor/editor-for-vue",
        "vue-i18n"
      ]
    },
    define: {
      __SYSTEM_INFO__: JSON.stringify({
        pkg: {
          version: package_default.version,
          dependencies: package_default.dependencies,
          devDependencies: package_default.devDependencies
        },
        lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
      })
    },
    plugins: createVitePlugins(mode),
    resolve: {
      alias: {
        "@": path2.resolve(__vite_injected_original_dirname, "./src"),
        "#": path2.resolve(__vite_injected_original_dirname, "./src/types")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
          additionalData: scssResources.join("")
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAicGFja2FnZS5qc29uIiwgInZpdGUvcGx1Z2lucy50cyJdLAogICJzb3VyY2VzQ29udGVudCI6IFsiY29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2Rpcm5hbWUgPSBcIkQ6XFxcXDExXFxcXDJcXFxcYWZsb3dlci1wbXMtZnJvbnRcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkQ6XFxcXDExXFxcXDJcXFxcYWZsb3dlci1wbXMtZnJvbnRcXFxcdml0ZS5jb25maWcudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0Q6LzExLzIvYWZsb3dlci1wbXMtZnJvbnQvdml0ZS5jb25maWcudHNcIjtpbXBvcnQgdHlwZSB7IENvbmZpZ0VudiwgVXNlckNvbmZpZyB9IGZyb20gJ3ZpdGUnXHJcbmltcG9ydCBmcyBmcm9tICdub2RlOmZzJ1xyXG5pbXBvcnQgcGF0aCBmcm9tICdub2RlOnBhdGgnXHJcbmltcG9ydCBwcm9jZXNzIGZyb20gJ25vZGU6cHJvY2VzcydcclxuaW1wb3J0IGRheWpzIGZyb20gJ2RheWpzJ1xyXG5pbXBvcnQgeyBkZWZpbmVDb25maWcsIGxvYWRFbnYgfSBmcm9tICd2aXRlJ1xyXG5pbXBvcnQgcGtnIGZyb20gJy4vcGFja2FnZS5qc29uJ1xyXG5pbXBvcnQgY3JlYXRlVml0ZVBsdWdpbnMgZnJvbSAnLi92aXRlL3BsdWdpbnMnXHJcblxyXG4vLyAgaHR0cHM6Ly9jbi52aXRlanMuZGV2L2NvbmZpZ1xyXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoKHsgbW9kZSB9OiBDb25maWdFbnYpOiBVc2VyQ29uZmlnID0+IHtcclxuICBjb25zdCBlbnYgPSBsb2FkRW52KG1vZGUsIHByb2Nlc3MuY3dkKCkpXHJcbiAgLy8gXHU1MTY4XHU1QzQwIHNjc3MgXHU4RDQ0XHU2RTkwXHJcbiAgY29uc3Qgc2Nzc1Jlc291cmNlczogc3RyaW5nW10gPSBbXVxyXG4gIGZzLnJlYWRkaXJTeW5jKCdzcmMvYXNzZXRzL3N0eWxlcy9yZXNvdXJjZXMnKS5mb3JFYWNoKChkaXJuYW1lKSA9PiB7XHJcbiAgICBpZiAoZnMuc3RhdFN5bmMoYHNyYy9hc3NldHMvc3R5bGVzL3Jlc291cmNlcy8ke2Rpcm5hbWV9YCkuaXNGaWxlKCkpIHtcclxuICAgICAgc2Nzc1Jlc291cmNlcy5wdXNoKGBAdXNlIFwiL3NyYy9hc3NldHMvc3R5bGVzL3Jlc291cmNlcy8ke2Rpcm5hbWV9XCIgYXMgKjtgKVxyXG4gICAgfVxyXG4gIH0pXHJcbiAgcmV0dXJuIHtcclxuICAgIGJhc2U6IGVudi5WSVRFX0JBU0VfUFJFRklYLFxyXG4gICAgLy8gXHU1RjAwXHU1M0QxXHU2NzBEXHU1MkExXHU1NjY4XHU5MDA5XHU5ODc5IGh0dHBzOi8vY24udml0ZWpzLmRldi9jb25maWcvc2VydmVyLW9wdGlvbnNcclxuICAgIHNlcnZlcjoge1xyXG4gICAgICBvcGVuOiB0cnVlLFxyXG4gICAgICBwb3J0OiA5MDA2LFxyXG4gICAgICBwcm94eToge1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIFx1NTNDRFx1NTQxMVx1NEVFM1x1NzQwNlx1ODlFM1x1NTFCM1x1OERFOFx1NTdERlx1OTE0RFx1N0Y2RVxyXG4gICAgICAgICAqIGh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9wcm94eS91c2VycyAoRjEyXHU1M0VGXHU4OUMxXHU4QkY3XHU2QzQyXHU4REVGXHU1Rjg0KSA9PiBodHRwOi8vbG9jYWxob3N0Ojg5ODkvdXNlcnMgKFx1NUI5RVx1OTY0NVx1OEJGN1x1NkM0Mlx1NTQwRVx1N0FFRiBBUEkgXHU4REVGXHU1Rjg0KVxyXG4gICAgICAgICAqXHJcbiAgICAgICAgICogZW52LlZJVEVfQVBQX0JBU0VfQVBJOiAvZGV2LWFwaXpoZWJ1c1xyXG4gICAgICAgICAqIGVudi5WSVRFX0FQUF9BUElfQkFTRVVSTDogaHR0cDovL2xvY2FsaG9zdDo4OTg5XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgW2Vudi5WSVRFX0FQUF9CQVNFX0FQSV06IHtcclxuICAgICAgICAgIHRhcmdldDogZW52LlZJVEVfQVBQX0FQSV9CQVNFVVJMLFxyXG4gICAgICAgICAgY2hhbmdlT3JpZ2luOiB0cnVlLFxyXG4gICAgICAgICAgcmV3cml0ZTogKHBhdGgpID0+IHBhdGgucmVwbGFjZShgJHtlbnYuVklURV9BUFBfQkFTRV9BUEl9YCwgJycpLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcblxyXG4gICAgICBob3N0OiAnMC4wLjAuMCcsXHJcbiAgICB9LFxyXG4gICAgLy8gXHU2Nzg0XHU1RUZBXHU5MDA5XHU5ODc5IGh0dHBzOi8vY24udml0ZWpzLmRldi9jb25maWcvYnVpbGQtb3B0aW9uc1xyXG4gICAgYnVpbGQ6IHtcclxuICAgICAgb3V0RGlyOiBtb2RlID09PSAncHJvZHVjdGlvbicgPyAnZGlzdCcgOiBgZGlzdC0ke21vZGV9YCxcclxuICAgICAgc291cmNlbWFwOiBlbnYuVklURV9CVUlMRF9TT1VSQ0VNQVAgPT09ICd0cnVlJyxcclxuICAgICAgY2h1bmtTaXplV2FybmluZ0xpbWl0OiAyMDAwLCAvLyBcdTZEODhcdTk2NjRcdTYyNTNcdTUzMDVcdTU5MjdcdTVDMEZcdThEODVcdThGQzc1MDBrYlx1OEI2Nlx1NTQ0QVxyXG4gICAgICBtaW5pZnk6ICd0ZXJzZXInLCAvLyBWaXRlIDIuNi54IFx1NEVFNVx1NEUwQVx1OTcwMFx1ODk4MVx1OTE0RFx1N0Y2RSBtaW5pZnk6IFwidGVyc2VyXCIsIHRlcnNlck9wdGlvbnMgXHU2MjREXHU4MEZEXHU3NTFGXHU2NTQ4XHJcbiAgICAgIHRlcnNlck9wdGlvbnM6IHtcclxuICAgICAgICBjb21wcmVzczoge1xyXG4gICAgICAgICAga2VlcF9pbmZpbml0eTogdHJ1ZSwgLy8gXHU5NjMyXHU2QjYyIEluZmluaXR5IFx1ODhBQlx1NTM4Qlx1N0YyOVx1NjIxMCAxLzBcdUZGMENcdThGRDlcdTUzRUZcdTgwRkRcdTRGMUFcdTVCRkNcdTgxRjQgQ2hyb21lIFx1NEUwQVx1NzY4NFx1NjAyN1x1ODBGRFx1OTVFRVx1OTg5OFxyXG4gICAgICAgICAgZHJvcF9jb25zb2xlOiBmYWxzZSwgLy8gXHU3NTFGXHU0RUE3XHU3M0FGXHU1ODgzXHU1M0JCXHU5NjY0IGNvbnNvbGVcclxuICAgICAgICAgIGRyb3BfZGVidWdnZXI6IGZhbHNlLCAvLyBcdTc1MUZcdTRFQTdcdTczQUZcdTU4ODNcdTUzQkJcdTk2NjQgZGVidWdnZXJcclxuICAgICAgICB9LFxyXG4gICAgICAgIGZvcm1hdDoge1xyXG4gICAgICAgICAgY29tbWVudHM6IGZhbHNlLCAvLyBcdTUyMjBcdTk2NjRcdTZDRThcdTkxQ0FcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIC8vIFx1OTg4NFx1NTJBMFx1OEY3RFx1OTg3OVx1NzZFRVx1NUZDNVx1OTcwMFx1NzY4NFx1N0VDNFx1NEVGNlxyXG4gICAgb3B0aW1pemVEZXBzOiB7XHJcbiAgICAgIGluY2x1ZGU6IFtcclxuICAgICAgICAndnVlJyxcclxuICAgICAgICAndnVlLXJvdXRlcicsXHJcbiAgICAgICAgJ3BpbmlhJyxcclxuICAgICAgICAnYXhpb3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9mb3JtL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2Zvcm0taXRlbS9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9idXR0b24vc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvaW5wdXQvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvaW5wdXQtbnVtYmVyL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3N3aXRjaC9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy91cGxvYWQvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvbWVudS9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9jb2wvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvaWNvbi9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9yb3cvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGFnL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2RpYWxvZy9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9sb2FkaW5nL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3JhZGlvL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3JhZGlvLWdyb3VwL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3BvcG92ZXIvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc2Nyb2xsYmFyL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3Rvb2x0aXAvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZHJvcGRvd24vc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZHJvcGRvd24tbWVudS9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kcm9wZG93bi1pdGVtL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3N1Yi1tZW51L3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL21lbnUtaXRlbS9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kaXZpZGVyL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NhcmQvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvbGluay9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9icmVhZGNydW1iL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2JyZWFkY3J1bWItaXRlbS9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90YWJsZS9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90cmVlLXNlbGVjdC9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90YWJsZS1jb2x1bW4vc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc2VsZWN0L3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL29wdGlvbi9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9wYWdpbmF0aW9uL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3RyZWUvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYWxlcnQvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvcmFkaW8tYnV0dG9uL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NoZWNrYm94LWdyb3VwL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NoZWNrYm94L3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3RhYnMvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGFiLXBhbmUvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvcmF0ZS9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kYXRlLXBpY2tlci9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9ub3RpZmljYXRpb24vc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvaW1hZ2Uvc3R5bGUvY3NzJyxcclxuICAgICAgICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc3RhdGlzdGljL3N0eWxlL2NzcycsXHJcbiAgICAgICAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3dhdGVybWFyay9zdHlsZS9jc3MnLFxyXG4gICAgICAgICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9jb25maWctcHJvdmlkZXIvc3R5bGUvY3NzJyxcclxuICAgICAgICAnQHZ1ZXVzZS9jb3JlJyxcclxuICAgICAgICAnc29ydGFibGVqcycsXHJcbiAgICAgICAgJ3BhdGgtdG8tcmVnZXhwJyxcclxuICAgICAgICAnZWNoYXJ0cycsXHJcbiAgICAgICAgJ0B3YW5nZWRpdG9yL2VkaXRvcicsXHJcbiAgICAgICAgJ0B3YW5nZWRpdG9yL2VkaXRvci1mb3ItdnVlJyxcclxuICAgICAgICAndnVlLWkxOG4nLFxyXG4gICAgICBdLFxyXG4gICAgfSxcclxuICAgIGRlZmluZToge1xyXG4gICAgICBfX1NZU1RFTV9JTkZPX186IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICBwa2c6IHtcclxuICAgICAgICAgIHZlcnNpb246IHBrZy52ZXJzaW9uLFxyXG4gICAgICAgICAgZGVwZW5kZW5jaWVzOiBwa2cuZGVwZW5kZW5jaWVzLFxyXG4gICAgICAgICAgZGV2RGVwZW5kZW5jaWVzOiBwa2cuZGV2RGVwZW5kZW5jaWVzLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgbGFzdEJ1aWxkVGltZTogZGF5anMoKS5mb3JtYXQoJ1lZWVktTU0tREQgSEg6bW06c3MnKSxcclxuICAgICAgfSksXHJcbiAgICB9LFxyXG4gICAgcGx1Z2luczogY3JlYXRlVml0ZVBsdWdpbnMobW9kZSksXHJcbiAgICByZXNvbHZlOiB7XHJcbiAgICAgIGFsaWFzOiB7XHJcbiAgICAgICAgJ0AnOiBwYXRoLnJlc29sdmUoX19kaXJuYW1lLCAnLi9zcmMnKSxcclxuICAgICAgICAnIyc6IHBhdGgucmVzb2x2ZShfX2Rpcm5hbWUsICcuL3NyYy90eXBlcycpLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIGNzczoge1xyXG4gICAgICBwcmVwcm9jZXNzb3JPcHRpb25zOiB7XHJcbiAgICAgICAgc2Nzczoge1xyXG4gICAgICAgICAgYXBpOiAnbW9kZXJuLWNvbXBpbGVyJyxcclxuICAgICAgICAgIGFkZGl0aW9uYWxEYXRhOiBzY3NzUmVzb3VyY2VzLmpvaW4oJycpLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gIH1cclxufSlcclxuIiwgIntcclxuICBcInR5cGVcIjogXCJtb2R1bGVcIixcclxuICBcInZlcnNpb25cIjogXCI0LjEyLjBcIixcclxuICBcImVuZ2luZXNcIjoge1xyXG4gICAgXCJub2RlXCI6IFwiXjE4LjE4LjAgfHwgXjIwLjkuMCB8fCA+PTIxLjEuMFwiXHJcbiAgfSxcclxuICBcInNjcmlwdHNcIjoge1xyXG4gICAgXCJkZXZcIjogXCJ2aXRlXCIsXHJcbiAgICBcImJ1aWxkXCI6IFwidml0ZSBidWlsZFwiLFxyXG4gICAgXCJidWlsZDp0ZXN0XCI6IFwibm9kZSAtLW1heF9vbGRfc3BhY2Vfc2l6ZT0xMDI0MDAwIC4vbm9kZV9tb2R1bGVzL3ZpdGUvYmluL3ZpdGUuanMgYnVpbGQgLS1tb2RlIHRlc3RcIixcclxuICAgIFwic2VydmVcIjogXCJodHRwLXNlcnZlciAuL2Rpc3QgLW9cIixcclxuICAgIFwic2VydmU6dGVzdFwiOiBcImh0dHAtc2VydmVyIC4vZGlzdC10ZXN0IC1vXCIsXHJcbiAgICBcInN2Z29cIjogXCJzdmdvIC1mIHNyYy9hc3NldHMvaWNvbnNcIixcclxuICAgIFwibmV3XCI6IFwicGxvcFwiLFxyXG4gICAgXCJnZW5lcmF0ZTppY29uc1wiOiBcImVzbm8gLi9zY3JpcHRzL2dlbmVyYXRlLmljb25zLnRzXCIsXHJcbiAgICBcImxpbnRcIjogXCJucG0tcnVuLWFsbCAtcyBsaW50OnRzYyBsaW50OmVzbGludCBsaW50OnN0eWxlbGludFwiLFxyXG4gICAgXCJsaW50OnRzY1wiOiBcInZ1ZS10c2MgLWJcIixcclxuICAgIFwibGludDplc2xpbnRcIjogXCJlc2xpbnQgLiAtLWNhY2hlIC0tZml4XCIsXHJcbiAgICBcImxpbnQ6c3R5bGVsaW50XCI6IFwic3R5bGVsaW50IFxcXCJzcmMvKiovKi57Y3NzLHNjc3MsdnVlfVxcXCIgLS1jYWNoZSAtLWZpeFwiLFxyXG4gICAgXCJwb3N0aW5zdGFsbFwiOiBcInNpbXBsZS1naXQtaG9va3NcIixcclxuICAgIFwicHJlaW5zdGFsbFwiOiBcIm5weCBvbmx5LWFsbG93IHBucG1cIixcclxuICAgIFwicmVsZWFzZVwiOiBcImJ1bXBwXCJcclxuICB9LFxyXG4gIFwiZGVwZW5kZW5jaWVzXCI6IHtcclxuICAgIFwiQGhlYWRsZXNzdWkvdnVlXCI6IFwiXjEuNy4yM1wiLFxyXG4gICAgXCJAaW1lbmd5dS92dWUzLWNvbnRleHQtbWVudVwiOiBcIl4xLjQuM1wiLFxyXG4gICAgXCJAdnVldXNlL2NvbXBvbmVudHNcIjogXCJeMTEuMS4wXCIsXHJcbiAgICBcIkB2dWV1c2UvY29yZVwiOiBcIl4xMS4xLjBcIixcclxuICAgIFwiQHZ1ZXVzZS9pbnRlZ3JhdGlvbnNcIjogXCJeMTEuMS4wXCIsXHJcbiAgICBcImF4aW9zXCI6IFwiXjEuNy43XCIsXHJcbiAgICBcImRheWpzXCI6IFwiXjEuMTEuMTNcIixcclxuICAgIFwiZGVjaW1hbC5qc1wiOiBcIl4xMC41LjBcIixcclxuICAgIFwiZGVmdVwiOiBcIl42LjEuNFwiLFxyXG4gICAgXCJkaXNhYmxlLWRldnRvb2xcIjogXCJeMC4zLjhcIixcclxuICAgIFwiZWxlbWVudC1wbHVzXCI6IFwiXjIuOC41XCIsXHJcbiAgICBcImVydWRhXCI6IFwiXjMuNC4wXCIsXHJcbiAgICBcImV2ZW50LXNvdXJjZS1wb2x5ZmlsbFwiOiBcIl4xLjAuMzFcIixcclxuICAgIFwiZmxvYXRpbmctdnVlXCI6IFwiNS4yLjJcIixcclxuICAgIFwiaG90a2V5cy1qc1wiOiBcIl4zLjEzLjdcIixcclxuICAgIFwibG9kYXNoLWVzXCI6IFwiXjQuMTcuMjFcIixcclxuICAgIFwibWVkaXVtLXpvb21cIjogXCJeMS4xLjBcIixcclxuICAgIFwibWl0dFwiOiBcIl4zLjAuMVwiLFxyXG4gICAgXCJtb2NranNcIjogXCJeMS4xLjBcIixcclxuICAgIFwibnByb2dyZXNzXCI6IFwiXjAuMi4wXCIsXHJcbiAgICBcIm92ZXJsYXlzY3JvbGxiYXJzXCI6IFwiXjIuMTAuMFwiLFxyXG4gICAgXCJvdmVybGF5c2Nyb2xsYmFycy12dWVcIjogXCJeMC41LjlcIixcclxuICAgIFwicGF0aC1icm93c2VyaWZ5XCI6IFwiXjEuMC4xXCIsXHJcbiAgICBcInBhdGgtdG8tcmVnZXhwXCI6IFwiXjguMi4wXCIsXHJcbiAgICBcInBpbmlhXCI6IFwiXjIuMi40XCIsXHJcbiAgICBcInBpbnlpbi1wcm9cIjogXCJeMy4yNS4wXCIsXHJcbiAgICBcInFzXCI6IFwiXjYuMTMuMFwiLFxyXG4gICAgXCJzY3VsZVwiOiBcIl4xLjMuMFwiLFxyXG4gICAgXCJzb3J0YWJsZWpzXCI6IFwiXjEuMTUuM1wiLFxyXG4gICAgXCJzcGlua2l0XCI6IFwiXjIuMC4xXCIsXHJcbiAgICBcInNwbGl0cGFuZXNcIjogXCJeMy4xLjVcIixcclxuICAgIFwidGltZWFnby5qc1wiOiBcIl40LjAuMlwiLFxyXG4gICAgXCJ2LXdhdmVcIjogXCJeMy4wLjJcIixcclxuICAgIFwidmNvbnNvbGVcIjogXCJeMy4xNS4xXCIsXHJcbiAgICBcInZ1ZVwiOiBcIl4zLjUuMTJcIixcclxuICAgIFwidnVlLWN1cnJlbmN5LWlucHV0XCI6IFwiXjMuMS4wXCIsXHJcbiAgICBcInZ1ZS1pMThuXCI6IFwiXjEwLjAuNFwiLFxyXG4gICAgXCJ2dWUtbS1tZXNzYWdlXCI6IFwiXjQuMC4yXCIsXHJcbiAgICBcInZ1ZS1yb3V0ZXJcIjogXCJeNC40LjVcIixcclxuICAgIFwid2F0ZXJtYXJrLWpzLXBsdXNcIjogXCJeMS41LjdcIixcclxuXHRcdFwidXVpZFwiOiBcIl4xMS4xLjBcIlxyXG5cdH0sXHJcbiAgXCJkZXZEZXBlbmRlbmNpZXNcIjoge1xyXG4gICAgXCJAYW50ZnUvZXNsaW50LWNvbmZpZ1wiOiBcIjMuOC4wXCIsXHJcbiAgICBcIkBpY29uaWZ5L2pzb25cIjogXCJeMi4yLjI2MVwiLFxyXG4gICAgXCJAaWNvbmlmeS92dWVcIjogXCJeNC4xLjJcIixcclxuICAgIFwiQGludGxpZnkvdW5wbHVnaW4tdnVlLWkxOG5cIjogXCJeNS4yLjBcIixcclxuICAgIFwiQHN0eWxpc3RpYy9zdHlsZWxpbnQtY29uZmlnXCI6IFwiXjIuMC4wXCIsXHJcbiAgICBcIkB0eXBlcy9sb2Rhc2gtZXNcIjogXCJeNC4xNy4xMlwiLFxyXG4gICAgXCJAdHlwZXMvbW9ja2pzXCI6IFwiXjEuMC4xMFwiLFxyXG4gICAgXCJAdHlwZXMvbnByb2dyZXNzXCI6IFwiXjAuMi4zXCIsXHJcbiAgICBcIkB0eXBlcy9wYXRoLWJyb3dzZXJpZnlcIjogXCJeMS4wLjNcIixcclxuICAgIFwiQHR5cGVzL3FzXCI6IFwiXjYuOS4xNlwiLFxyXG4gICAgXCJAdHlwZXMvc29ydGFibGVqc1wiOiBcIl4xLjE1LjhcIixcclxuICAgIFwiQHVub2Nzcy9lc2xpbnQtcGx1Z2luXCI6IFwiXjAuNjMuNFwiLFxyXG4gICAgXCJAdml0ZWpzL3BsdWdpbi1sZWdhY3lcIjogXCJeNS40LjJcIixcclxuICAgIFwiQHZpdGVqcy9wbHVnaW4tdnVlXCI6IFwiXjUuMS40XCIsXHJcbiAgICBcIkB2aXRlanMvcGx1Z2luLXZ1ZS1qc3hcIjogXCJeNC4wLjFcIixcclxuICAgIFwiYXV0b3ByZWZpeGVyXCI6IFwiXjEwLjQuMjBcIixcclxuICAgIFwiYm94ZW5cIjogXCJeOC4wLjFcIixcclxuICAgIFwiYnVtcHBcIjogXCJeOS43LjFcIixcclxuICAgIFwiY3otZ2l0XCI6IFwiXjEuMTAuMVwiLFxyXG4gICAgXCJlc2xpbnRcIjogXCJeOS4xMi4wXCIsXHJcbiAgICBcImVzbm9cIjogXCJeNC44LjBcIixcclxuICAgIFwiZnMtZXh0cmFcIjogXCJeMTEuMi4wXCIsXHJcbiAgICBcImh0dHAtc2VydmVyXCI6IFwiXjE0LjEuMVwiLFxyXG4gICAgXCJpbnF1aXJlclwiOiBcIl4xMi4wLjBcIixcclxuICAgIFwibGludC1zdGFnZWRcIjogXCJeMTUuMi4xMFwiLFxyXG4gICAgXCJucG0tcnVuLWFsbDJcIjogXCJeNi4yLjRcIixcclxuICAgIFwicGljb2NvbG9yc1wiOiBcIl4xLjEuMVwiLFxyXG4gICAgXCJwbG9wXCI6IFwiXjQuMC4xXCIsXHJcbiAgICBcInBvc3Rjc3NcIjogXCJeOC40LjQ3XCIsXHJcbiAgICBcInBvc3Rjc3MtbmVzdGVkXCI6IFwiXjYuMi4wXCIsXHJcbiAgICBcInNhc3MtZW1iZWRkZWRcIjogXCJeMS44MC4yXCIsXHJcbiAgICBcInNpbXBsZS1naXQtaG9va3NcIjogXCJeMi4xMS4xXCIsXHJcbiAgICBcInN0eWxlbGludFwiOiBcIl4xNi4xMC4wXCIsXHJcbiAgICBcInN0eWxlbGludC1jb25maWctcmVjZXNzLW9yZGVyXCI6IFwiXjUuMS4xXCIsXHJcbiAgICBcInN0eWxlbGludC1jb25maWctc3RhbmRhcmQtc2Nzc1wiOiBcIl4xMy4xLjBcIixcclxuICAgIFwic3R5bGVsaW50LWNvbmZpZy1zdGFuZGFyZC12dWVcIjogXCJeMS4wLjBcIixcclxuICAgIFwic3R5bGVsaW50LXNjc3NcIjogXCJeNi44LjFcIixcclxuICAgIFwic3Znb1wiOiBcIl4zLjMuMlwiLFxyXG4gICAgXCJ0eXBlc2NyaXB0XCI6IFwiXjUuNi4zXCIsXHJcbiAgICBcInVub2Nzc1wiOiBcIl4wLjYzLjRcIixcclxuICAgIFwidW5vY3NzLXByZXNldC1zY3JvbGxiYXJcIjogXCJeMC4zLjFcIixcclxuICAgIFwidW5wbHVnaW4tYXV0by1pbXBvcnRcIjogXCJeMC4xOC4zXCIsXHJcbiAgICBcInVucGx1Z2luLWljb25zXCI6IFwiXjAuMTYuNlwiLFxyXG4gICAgXCJ1bnBsdWdpbi10dXJiby1jb25zb2xlXCI6IFwiXjEuMTAuNFwiLFxyXG4gICAgXCJ1bnBsdWdpbi12dWUtY29tcG9uZW50c1wiOiBcIl4wLjI3LjRcIixcclxuICAgIFwidml0ZVwiOiBcIl41LjQuOVwiLFxyXG4gICAgXCJ2aXRlLXBsdWdpbi1hcHAtbG9hZGluZ1wiOiBcIl4wLjMuMFwiLFxyXG4gICAgXCJ2aXRlLXBsdWdpbi1hcmNoaXZlclwiOiBcIl4wLjEuMVwiLFxyXG4gICAgXCJ2aXRlLXBsdWdpbi1iYW5uZXJcIjogXCJeMC44LjBcIixcclxuICAgIFwidml0ZS1wbHVnaW4tY29tcHJlc3Npb24yXCI6IFwiXjEuMy4wXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLWZha2Utc2VydmVyXCI6IFwiXjIuMS4yXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLXBhZ2VzXCI6IFwiXjAuMzIuM1wiLFxyXG4gICAgXCJ2aXRlLXBsdWdpbi1zdmctaWNvbnNcIjogXCJeMi4wLjFcIixcclxuICAgIFwidml0ZS1wbHVnaW4tdnVlLWRldnRvb2xzXCI6IFwiXjcuNS4yXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLXZ1ZS1tZXRhLWxheW91dHNcIjogXCJeMC40LjNcIixcclxuICAgIFwidnVlLXRzY1wiOiBcIl4yLjEuNlwiXHJcbiAgfSxcclxuICBcImNvbmZpZ1wiOiB7XHJcbiAgICBcImNvbW1pdGl6ZW5cIjoge1xyXG4gICAgICBcInBhdGhcIjogXCJub2RlX21vZHVsZXMvY3otZ2l0XCJcclxuICAgIH1cclxuICB9XHJcbn1cclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJEOlxcXFwxMVxcXFwyXFxcXGFmbG93ZXItcG1zLWZyb250XFxcXHZpdGVcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkQ6XFxcXDExXFxcXDJcXFxcYWZsb3dlci1wbXMtZnJvbnRcXFxcdml0ZVxcXFxwbHVnaW5zLnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9EOi8xMS8yL2FmbG93ZXItcG1zLWZyb250L3ZpdGUvcGx1Z2lucy50c1wiO2ltcG9ydCBwYXRoIGZyb20gJ25vZGU6cGF0aCdcclxuaW1wb3J0IHByb2Nlc3MgZnJvbSAnbm9kZTpwcm9jZXNzJ1xyXG5pbXBvcnQgdnVlSTE4biBmcm9tICdAaW50bGlmeS91bnBsdWdpbi12dWUtaTE4bi92aXRlJ1xyXG5pbXBvcnQgdnVlTGVnYWN5IGZyb20gJ0B2aXRlanMvcGx1Z2luLWxlZ2FjeSdcclxuaW1wb3J0IHZ1ZSBmcm9tICdAdml0ZWpzL3BsdWdpbi12dWUnXHJcbmltcG9ydCB2dWVKc3ggZnJvbSAnQHZpdGVqcy9wbHVnaW4tdnVlLWpzeCdcclxuaW1wb3J0IGJveGVuIGZyb20gJ2JveGVuJ1xyXG5pbXBvcnQgcGljb2NvbG9ycyBmcm9tICdwaWNvY29sb3JzJ1xyXG5pbXBvcnQgVW5vY3NzIGZyb20gJ3Vub2Nzcy92aXRlJ1xyXG5pbXBvcnQgYXV0b0ltcG9ydCBmcm9tICd1bnBsdWdpbi1hdXRvLWltcG9ydC92aXRlJ1xyXG5pbXBvcnQgVHVyYm9Db25zb2xlIGZyb20gJ3VucGx1Z2luLXR1cmJvLWNvbnNvbGUvdml0ZSdcclxuaW1wb3J0IGNvbXBvbmVudHMgZnJvbSAndW5wbHVnaW4tdnVlLWNvbXBvbmVudHMvdml0ZSdcclxuaW1wb3J0IHsgbG9hZEVudiwgdHlwZSBQbHVnaW5PcHRpb24gfSBmcm9tICd2aXRlJ1xyXG5pbXBvcnQgQXBwTG9hZGluZyBmcm9tICd2aXRlLXBsdWdpbi1hcHAtbG9hZGluZydcclxuaW1wb3J0IEFyY2hpdmVyIGZyb20gJ3ZpdGUtcGx1Z2luLWFyY2hpdmVyJ1xyXG5pbXBvcnQgeyBjb21wcmVzc2lvbiB9IGZyb20gJ3ZpdGUtcGx1Z2luLWNvbXByZXNzaW9uMidcclxuaW1wb3J0IFBhZ2VzIGZyb20gJ3ZpdGUtcGx1Z2luLXBhZ2VzJ1xyXG5pbXBvcnQgeyBjcmVhdGVTdmdJY29uc1BsdWdpbiB9IGZyb20gJ3ZpdGUtcGx1Z2luLXN2Zy1pY29ucydcclxuaW1wb3J0IFZ1ZURldlRvb2xzIGZyb20gJ3ZpdGUtcGx1Z2luLXZ1ZS1kZXZ0b29scydcclxuaW1wb3J0IExheW91dHMgZnJvbSAndml0ZS1wbHVnaW4tdnVlLW1ldGEtbGF5b3V0cydcclxuaW1wb3J0IHsgRWxlbWVudFBsdXNSZXNvbHZlciB9IGZyb20gJ3VucGx1Z2luLXZ1ZS1jb21wb25lbnRzL3Jlc29sdmVycydcclxuaW1wb3J0IEljb25zUmVzb2x2ZXIgZnJvbSAndW5wbHVnaW4taWNvbnMvcmVzb2x2ZXInXHJcbmltcG9ydCBJY29ucyBmcm9tICd1bnBsdWdpbi1pY29ucy92aXRlJ1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY3JlYXRlVml0ZVBsdWdpbnMobW9kZTogc3RyaW5nLCBpc0J1aWxkID0gZmFsc2UpIHtcclxuICBjb25zdCB2aXRlRW52ID0gbG9hZEVudihtb2RlLCBwcm9jZXNzLmN3ZCgpKVxyXG4gIGNvbnN0IHZpdGVQbHVnaW5zOiAoUGx1Z2luT3B0aW9uIHwgUGx1Z2luT3B0aW9uW10pW10gPSBbXHJcbiAgICB2dWUoKSxcclxuICAgIHZ1ZUpzeCgpLFxyXG4gICAgdnVlTGVnYWN5KHtcclxuICAgICAgcmVuZGVyTGVnYWN5Q2h1bmtzOiBmYWxzZSxcclxuICAgICAgbW9kZXJuUG9seWZpbGxzOiBbJ2VzLmFycmF5LmF0JywgJ2VzLmFycmF5LmZpbmQtbGFzdCddLFxyXG4gICAgfSksXHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3Z1ZWpzL2RldnRvb2xzLW5leHRcclxuICAgIHZpdGVFbnYuVklURV9PUEVOX0RFVlRPT0xTID09PSAndHJ1ZScgJiYgVnVlRGV2VG9vbHMoKSxcclxuXHJcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vdW5wbHVnaW4vdW5wbHVnaW4tYXV0by1pbXBvcnRcclxuICAgIGF1dG9JbXBvcnQoe1xyXG4gICAgICAvLyBcdTgxRUFcdTUyQThcdTVCRkNcdTUxNjUgVnVlIFx1NzZGOFx1NTE3M1x1NTFGRFx1NjU3MFx1RkYwQ1x1NTk4Mlx1RkYxQXJlZiwgcmVhY3RpdmUsIHRvUmVmIFx1N0I0OVxyXG4gICAgICBpbXBvcnRzOiBbJ3Z1ZScsICd2dWUtcm91dGVyJywgJ3BpbmlhJywgJ3Z1ZS1pMThuJ10sXHJcbiAgICAgIGR0czogZmFsc2UsIC8vIFx1OTE0RFx1N0Y2RVx1NjU4N1x1NEVGNlx1NzUxRlx1NjIxMFx1NEY0RFx1N0Y2RShmYWxzZTpcdTUxNzNcdTk1RURcdTgxRUFcdTUyQThcdTc1MUZcdTYyMTApXHJcbiAgICAgIC8vIGR0czogJy4vc3JjL3R5cGVzL2F1dG8taW1wb3J0cy5kLnRzJywgLy8gXHU5MTREXHU3RjZFXHU2NTg3XHU0RUY2XHU3NTFGXHU2MjEwXHU0RjREXHU3RjZFKGZhbHNlOlx1NTE3M1x1OTVFRFx1ODFFQVx1NTJBOFx1NzUxRlx1NjIxMClcclxuICAgICAgZGlyczogWycuL3NyYy91dGlscy9jb21wb3NhYmxlcy8qKiddLCAvLyBcdTYzMDdcdTVCOUFcdTgxRUFcdTVCOUFcdTRFNDlcdTdFQzRcdTRFRjZcdTRGNERcdTdGNkUoXHU5RUQ4XHU4QkE0OnNyYy9jb21wb25lbnRzKVxyXG5cclxuICAgICAgLy8gXHU4MUVBXHU1MkE4XHU1QkZDXHU1MTY1IEVsZW1lbnQgUGx1cyBcdTc2RjhcdTUxNzNcdTUxRkRcdTY1NzBcdUZGMENcdTU5ODJcdUZGMUFFbE1lc3NhZ2UsIEVsTWVzc2FnZUJveC4uLiAoXHU1RTI2XHU2ODM3XHU1RjBGKVxyXG4gICAgICByZXNvbHZlcnM6IFtFbGVtZW50UGx1c1Jlc29sdmVyKCksIEljb25zUmVzb2x2ZXIoe30pXSxcclxuICAgICAgZXNsaW50cmM6IHtcclxuICAgICAgICBlbmFibGVkOiBmYWxzZSxcclxuICAgICAgICBmaWxlcGF0aDogJy4vLmVzbGludHJjLWF1dG8taW1wb3J0Lmpzb24nLFxyXG4gICAgICAgIGdsb2JhbHNQcm9wVmFsdWU6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgIHZ1ZVRlbXBsYXRlOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3VucGx1Z2luL3VucGx1Z2luLXZ1ZS1jb21wb25lbnRzXHJcbiAgICBjb21wb25lbnRzKHtcclxuICAgICAgZGlyczogWydzcmMvY29tcG9uZW50cycsICdzcmMvbGF5b3V0cy91aS1raXQnXSwgLy8gXHU2MzA5XHU5NzAwXHU1MkEwXHU4RjdEXHU3Njg0XHU2NTg3XHU0RUY2XHU1OTM5XHJcbiAgICAgIGluY2x1ZGU6IFsvXFwudnVlJC8sIC9cXC52dWVcXD92dWUvLCAvXFwudHN4JC9dLFxyXG4gICAgICBkdHM6ICcuL3NyYy90eXBlcy9jb21wb25lbnRzLmQudHMnLCAvLyBcdTkxNERcdTdGNkVcdTY1ODdcdTRFRjZcdTRGNERcdTdGNkUgKGZhbHNlOlx1NTE3M1x1OTVFRFx1ODFFQVx1NTJBOFx1NzUxRlx1NjIxMClcclxuXHJcbiAgICAgIHJlc29sdmVyczogW1xyXG4gICAgICAgIC8vIFx1ODFFQVx1NTJBOFx1NUJGQ1x1NTE2NSBFbGVtZW50IFBsdXMgXHU3RUM0XHU0RUY2XHJcbiAgICAgICAgRWxlbWVudFBsdXNSZXNvbHZlcigpLFxyXG4gICAgICAgIC8vIFx1ODFFQVx1NTJBOFx1NkNFOFx1NTE4Q1x1NTZGRVx1NjgwN1x1N0VDNFx1NEVGNlxyXG4gICAgICAgIEljb25zUmVzb2x2ZXIoeyBlbmFibGVkQ29sbGVjdGlvbnM6IFsnZXAnXSB9KSxcclxuICAgICAgXSxcclxuICAgIH0pLFxyXG5cclxuICAgIFVub2Nzcyh7XHJcbiAgICAgIGhtclRvcExldmVsQXdhaXQ6IGZhbHNlLFxyXG4gICAgfSksXHJcbiAgICBJY29ucyh7XHJcbiAgICAgIGF1dG9JbnN0YWxsOiB0cnVlLFxyXG4gICAgfSksXHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3ZiZW5qcy92aXRlLXBsdWdpbi1zdmctaWNvbnNcclxuICAgIGNyZWF0ZVN2Z0ljb25zUGx1Z2luKHtcclxuICAgICAgaWNvbkRpcnM6IFtwYXRoLnJlc29sdmUocHJvY2Vzcy5jd2QoKSwgJ3NyYy9hc3NldHMvaWNvbnMvJyldLCAvLyBcdTYzMDdcdTVCOUFcdTk3MDBcdTg5ODFcdTdGMTNcdTVCNThcdTc2ODRcdTU2RkVcdTY4MDdcdTY1ODdcdTRFRjZcdTU5MzlcclxuICAgICAgc3ltYm9sSWQ6ICdpY29uLVtkaXJdLVtuYW1lXScsIC8vIFx1NjMwN1x1NUI5QXN5bWJvbElkXHU2ODNDXHU1RjBGXHJcbiAgICAgIHN2Z29PcHRpb25zOiBpc0J1aWxkLFxyXG4gICAgfSksXHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2ludGxpZnkvdnVlLWkxOG5cclxuICAgIHZ1ZUkxOG4oe1xyXG4gICAgICBpbmNsdWRlOiBwYXRoLnJlc29sdmUocHJvY2Vzcy5jd2QoKSwgJ3NyYy9sb2NhbGVzL2xhbmcvKionKSxcclxuICAgIH0pLFxyXG5cclxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9kaXNoYWl0L3ZpdGUtcGx1Z2luLXZ1ZS1tZXRhLWxheW91dHNcclxuICAgIExheW91dHMoe1xyXG4gICAgICBkZWZhdWx0TGF5b3V0OiAnaW5kZXgnLFxyXG4gICAgfSksXHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2hhbm5vZXJ1L3ZpdGUtcGx1Z2luLXBhZ2VzXHJcbiAgICBQYWdlcyh7XHJcbiAgICAgIGRpcnM6ICdzcmMvdmlld3MnLFxyXG4gICAgICBleGNsdWRlOiBbJyoqL2NvbXBvbmVudHMvKiovKi52dWUnXSxcclxuICAgIH0pLFxyXG5cclxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9ub256enovdml0ZS1wbHVnaW4tY29tcHJlc3Npb25cclxuICAgIHZpdGVFbnYuVklURV9CVUlMRF9DT01QUkVTUz8uc3BsaXQoJywnKS5pbmNsdWRlcygnZ3ppcCcpICYmIGNvbXByZXNzaW9uKCksXHJcbiAgICB2aXRlRW52LlZJVEVfQlVJTERfQ09NUFJFU1M/LnNwbGl0KCcsJykuaW5jbHVkZXMoJ2Jyb3RsaScpICYmXHJcbiAgICAgIGNvbXByZXNzaW9uKHtcclxuICAgICAgICBleGNsdWRlOiBbL1xcLihicikkLywgL1xcLihneikkL10sXHJcbiAgICAgICAgYWxnb3JpdGhtOiAnYnJvdGxpQ29tcHJlc3MnLFxyXG4gICAgICB9KSxcclxuXHJcbiAgICB2aXRlRW52LlZJVEVfQlVJTERfQVJDSElWRSAmJlxyXG4gICAgICBBcmNoaXZlcih7XHJcbiAgICAgICAgYXJjaGl2ZVR5cGU6IHZpdGVFbnYuVklURV9CVUlMRF9BUkNISVZFLFxyXG4gICAgICB9KSxcclxuXHJcbiAgICBBcHBMb2FkaW5nKCdsb2FkaW5nLmh0bWwnKSxcclxuXHJcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vdW5wbHVnaW4vdW5wbHVnaW4tdHVyYm8tY29uc29sZVxyXG4gICAgVHVyYm9Db25zb2xlKCksXHJcblxyXG4gICAge1xyXG4gICAgICBuYW1lOiAndml0ZS1wbHVnaW4tZGVidWctcGx1Z2luJyxcclxuICAgICAgZW5mb3JjZTogJ3ByZScsXHJcbiAgICAgIHRyYW5zZm9ybTogKGNvZGUsIGlkKSA9PiB7XHJcbiAgICAgICAgaWYgKC9zcmNcXC9tYWluLnRzJC8udGVzdChpZCkpIHtcclxuICAgICAgICAgIGlmICh2aXRlRW52LlZJVEVfQVBQX0RFQlVHX1RPT0wgPT09ICdlcnVkYScpIHtcclxuICAgICAgICAgICAgY29kZSA9IGNvZGUuY29uY2F0KGBcclxuICAgICAgICAgICAgICBpbXBvcnQgZXJ1ZGEgZnJvbSAnZXJ1ZGEnXHJcbiAgICAgICAgICAgICAgZXJ1ZGEuaW5pdCgpXHJcbiAgICAgICAgICAgIGApXHJcbiAgICAgICAgICB9IGVsc2UgaWYgKHZpdGVFbnYuVklURV9BUFBfREVCVUdfVE9PTCA9PT0gJ3Zjb25zb2xlJykge1xyXG4gICAgICAgICAgICBjb2RlID0gY29kZS5jb25jYXQoYFxyXG4gICAgICAgICAgICAgIGltcG9ydCBWQ29uc29sZSBmcm9tICd2Y29uc29sZSdcclxuICAgICAgICAgICAgICBuZXcgVkNvbnNvbGUoKVxyXG4gICAgICAgICAgICBgKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgY29kZSxcclxuICAgICAgICAgICAgbWFwOiBudWxsLFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuICAgIH0sXHJcblxyXG4gICAge1xyXG4gICAgICBuYW1lOiAndml0ZS1wbHVnaW4tZGlzYWJsZS1kZXZ0b29sJyxcclxuICAgICAgZW5mb3JjZTogJ3ByZScsXHJcbiAgICAgIHRyYW5zZm9ybTogKGNvZGUsIGlkKSA9PiB7XHJcbiAgICAgICAgaWYgKC9zcmNcXC9tYWluLnRzJC8udGVzdChpZCkpIHtcclxuICAgICAgICAgIGlmICh2aXRlRW52LlZJVEVfQVBQX0RJU0FCTEVfREVWVE9PTCA9PT0gJ3RydWUnKSB7XHJcbiAgICAgICAgICAgIGNvZGUgPSBjb2RlLmNvbmNhdChgXHJcbiAgICAgICAgICAgICAgaW1wb3J0IERpc2FibGVEZXZ0b29sIGZyb20gJ2Rpc2FibGUtZGV2dG9vbCdcclxuICAgICAgICAgICAgICBEaXNhYmxlRGV2dG9vbCgpXHJcbiAgICAgICAgICAgIGApXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBjb2RlLFxyXG4gICAgICAgICAgICBtYXA6IG51bGwsXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuXHJcbiAgICB7XHJcbiAgICAgIG5hbWU6ICd2aXRlLXBsdWdpbi10ZXJtaW5hbC1pbmZvJyxcclxuICAgICAgYXBwbHk6ICdzZXJ2ZScsXHJcbiAgICAgIGFzeW5jIGJ1aWxkU3RhcnQoKSB7XHJcbiAgICAgICAgY29uc3QgeyBib2xkLCBncmVlbiwgbWFnZW50YSwgYmdHcmVlbiwgdW5kZXJsaW5lIH0gPSBwaWNvY29sb3JzXHJcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnNvbGVcclxuICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgIGJveGVuKFxyXG4gICAgICAgICAgICBgJHtib2xkKFxyXG4gICAgICAgICAgICAgIGdyZWVuKGBcdTc1MzEgJHtiZ0dyZWVuKCdcdTRFMDBcdTY3MzVcdTgyQjFcdTkxNTJcdTVFOTdcdTc5RDFcdTYyODAnKX0gXHU5QTcxXHU1MkE4YClcclxuICAgICAgICAgICAgKX1cXG5cXG4ke3VuZGVybGluZShcclxuICAgICAgICAgICAgICAnaHR0cHM6Ly93d3cueWlkdW9odWEubmV0J1xyXG4gICAgICAgICAgICApfVxcblxcblx1NUY1M1x1NTI0RFx1NEY3Rlx1NzUyOFx1RkYxQSR7bWFnZW50YSgnXHU0RTEzXHU0RTFBXHU3MjQ4Jyl9YCxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDEsXHJcbiAgICAgICAgICAgICAgbWFyZ2luOiAxLFxyXG4gICAgICAgICAgICAgIGJvcmRlclN0eWxlOiAnZG91YmxlJyxcclxuICAgICAgICAgICAgICB0ZXh0QWxpZ25tZW50OiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgKVxyXG4gICAgICAgIClcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgXVxyXG4gIHJldHVybiB2aXRlUGx1Z2luc1xyXG59XHJcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFDQSxPQUFPLFFBQVE7QUFDZixPQUFPQSxXQUFVO0FBQ2pCLE9BQU9DLGNBQWE7QUFDcEIsT0FBTyxXQUFXO0FBQ2xCLFNBQVMsY0FBYyxXQUFBQyxnQkFBZTs7O0FDTHRDO0FBQUEsRUFDRSxNQUFRO0FBQUEsRUFDUixTQUFXO0FBQUEsRUFDWCxTQUFXO0FBQUEsSUFDVCxNQUFRO0FBQUEsRUFDVjtBQUFBLEVBQ0EsU0FBVztBQUFBLElBQ1QsS0FBTztBQUFBLElBQ1AsT0FBUztBQUFBLElBQ1QsY0FBYztBQUFBLElBQ2QsT0FBUztBQUFBLElBQ1QsY0FBYztBQUFBLElBQ2QsTUFBUTtBQUFBLElBQ1IsS0FBTztBQUFBLElBQ1Asa0JBQWtCO0FBQUEsSUFDbEIsTUFBUTtBQUFBLElBQ1IsWUFBWTtBQUFBLElBQ1osZUFBZTtBQUFBLElBQ2Ysa0JBQWtCO0FBQUEsSUFDbEIsYUFBZTtBQUFBLElBQ2YsWUFBYztBQUFBLElBQ2QsU0FBVztBQUFBLEVBQ2I7QUFBQSxFQUNBLGNBQWdCO0FBQUEsSUFDZCxtQkFBbUI7QUFBQSxJQUNuQiw4QkFBOEI7QUFBQSxJQUM5QixzQkFBc0I7QUFBQSxJQUN0QixnQkFBZ0I7QUFBQSxJQUNoQix3QkFBd0I7QUFBQSxJQUN4QixPQUFTO0FBQUEsSUFDVCxPQUFTO0FBQUEsSUFDVCxjQUFjO0FBQUEsSUFDZCxNQUFRO0FBQUEsSUFDUixtQkFBbUI7QUFBQSxJQUNuQixnQkFBZ0I7QUFBQSxJQUNoQixPQUFTO0FBQUEsSUFDVCx5QkFBeUI7QUFBQSxJQUN6QixnQkFBZ0I7QUFBQSxJQUNoQixjQUFjO0FBQUEsSUFDZCxhQUFhO0FBQUEsSUFDYixlQUFlO0FBQUEsSUFDZixNQUFRO0FBQUEsSUFDUixRQUFVO0FBQUEsSUFDVixXQUFhO0FBQUEsSUFDYixtQkFBcUI7QUFBQSxJQUNyQix5QkFBeUI7QUFBQSxJQUN6QixtQkFBbUI7QUFBQSxJQUNuQixrQkFBa0I7QUFBQSxJQUNsQixPQUFTO0FBQUEsSUFDVCxjQUFjO0FBQUEsSUFDZCxJQUFNO0FBQUEsSUFDTixPQUFTO0FBQUEsSUFDVCxZQUFjO0FBQUEsSUFDZCxTQUFXO0FBQUEsSUFDWCxZQUFjO0FBQUEsSUFDZCxjQUFjO0FBQUEsSUFDZCxVQUFVO0FBQUEsSUFDVixVQUFZO0FBQUEsSUFDWixLQUFPO0FBQUEsSUFDUCxzQkFBc0I7QUFBQSxJQUN0QixZQUFZO0FBQUEsSUFDWixpQkFBaUI7QUFBQSxJQUNqQixjQUFjO0FBQUEsSUFDZCxxQkFBcUI7QUFBQSxJQUN2QixNQUFRO0FBQUEsRUFDVDtBQUFBLEVBQ0MsaUJBQW1CO0FBQUEsSUFDakIsd0JBQXdCO0FBQUEsSUFDeEIsaUJBQWlCO0FBQUEsSUFDakIsZ0JBQWdCO0FBQUEsSUFDaEIsOEJBQThCO0FBQUEsSUFDOUIsK0JBQStCO0FBQUEsSUFDL0Isb0JBQW9CO0FBQUEsSUFDcEIsaUJBQWlCO0FBQUEsSUFDakIsb0JBQW9CO0FBQUEsSUFDcEIsMEJBQTBCO0FBQUEsSUFDMUIsYUFBYTtBQUFBLElBQ2IscUJBQXFCO0FBQUEsSUFDckIseUJBQXlCO0FBQUEsSUFDekIseUJBQXlCO0FBQUEsSUFDekIsc0JBQXNCO0FBQUEsSUFDdEIsMEJBQTBCO0FBQUEsSUFDMUIsY0FBZ0I7QUFBQSxJQUNoQixPQUFTO0FBQUEsSUFDVCxPQUFTO0FBQUEsSUFDVCxVQUFVO0FBQUEsSUFDVixRQUFVO0FBQUEsSUFDVixNQUFRO0FBQUEsSUFDUixZQUFZO0FBQUEsSUFDWixlQUFlO0FBQUEsSUFDZixVQUFZO0FBQUEsSUFDWixlQUFlO0FBQUEsSUFDZixnQkFBZ0I7QUFBQSxJQUNoQixZQUFjO0FBQUEsSUFDZCxNQUFRO0FBQUEsSUFDUixTQUFXO0FBQUEsSUFDWCxrQkFBa0I7QUFBQSxJQUNsQixpQkFBaUI7QUFBQSxJQUNqQixvQkFBb0I7QUFBQSxJQUNwQixXQUFhO0FBQUEsSUFDYixpQ0FBaUM7QUFBQSxJQUNqQyxrQ0FBa0M7QUFBQSxJQUNsQyxpQ0FBaUM7QUFBQSxJQUNqQyxrQkFBa0I7QUFBQSxJQUNsQixNQUFRO0FBQUEsSUFDUixZQUFjO0FBQUEsSUFDZCxRQUFVO0FBQUEsSUFDViwyQkFBMkI7QUFBQSxJQUMzQix3QkFBd0I7QUFBQSxJQUN4QixrQkFBa0I7QUFBQSxJQUNsQiwwQkFBMEI7QUFBQSxJQUMxQiwyQkFBMkI7QUFBQSxJQUMzQixNQUFRO0FBQUEsSUFDUiwyQkFBMkI7QUFBQSxJQUMzQix3QkFBd0I7QUFBQSxJQUN4QixzQkFBc0I7QUFBQSxJQUN0Qiw0QkFBNEI7QUFBQSxJQUM1QiwyQkFBMkI7QUFBQSxJQUMzQixxQkFBcUI7QUFBQSxJQUNyQix5QkFBeUI7QUFBQSxJQUN6Qiw0QkFBNEI7QUFBQSxJQUM1QixnQ0FBZ0M7QUFBQSxJQUNoQyxXQUFXO0FBQUEsRUFDYjtBQUFBLEVBQ0EsUUFBVTtBQUFBLElBQ1IsWUFBYztBQUFBLE1BQ1osTUFBUTtBQUFBLElBQ1Y7QUFBQSxFQUNGO0FBQ0Y7OztBQ2pJOFEsT0FBTyxVQUFVO0FBQy9SLE9BQU8sYUFBYTtBQUNwQixPQUFPLGFBQWE7QUFDcEIsT0FBTyxlQUFlO0FBQ3RCLE9BQU8sU0FBUztBQUNoQixPQUFPLFlBQVk7QUFDbkIsT0FBTyxXQUFXO0FBQ2xCLE9BQU8sZ0JBQWdCO0FBQ3ZCLE9BQU8sWUFBWTtBQUNuQixPQUFPLGdCQUFnQjtBQUN2QixPQUFPLGtCQUFrQjtBQUN6QixPQUFPLGdCQUFnQjtBQUN2QixTQUFTLGVBQWtDO0FBQzNDLE9BQU8sZ0JBQWdCO0FBQ3ZCLE9BQU8sY0FBYztBQUNyQixTQUFTLG1CQUFtQjtBQUM1QixPQUFPLFdBQVc7QUFDbEIsU0FBUyw0QkFBNEI7QUFDckMsT0FBTyxpQkFBaUI7QUFDeEIsT0FBTyxhQUFhO0FBQ3BCLFNBQVMsMkJBQTJCO0FBQ3BDLE9BQU8sbUJBQW1CO0FBQzFCLE9BQU8sV0FBVztBQUVILFNBQVIsa0JBQW1DLE1BQWMsVUFBVSxPQUFPO0FBQ3ZFLFFBQU0sVUFBVSxRQUFRLE1BQU0sUUFBUSxJQUFJLENBQUM7QUFDM0MsUUFBTSxjQUFpRDtBQUFBLElBQ3JELElBQUk7QUFBQSxJQUNKLE9BQU87QUFBQSxJQUNQLFVBQVU7QUFBQSxNQUNSLG9CQUFvQjtBQUFBLE1BQ3BCLGlCQUFpQixDQUFDLGVBQWUsb0JBQW9CO0FBQUEsSUFDdkQsQ0FBQztBQUFBO0FBQUEsSUFHRCxRQUFRLHVCQUF1QixVQUFVLFlBQVk7QUFBQTtBQUFBLElBR3JELFdBQVc7QUFBQTtBQUFBLE1BRVQsU0FBUyxDQUFDLE9BQU8sY0FBYyxTQUFTLFVBQVU7QUFBQSxNQUNsRCxLQUFLO0FBQUE7QUFBQTtBQUFBLE1BRUwsTUFBTSxDQUFDLDRCQUE0QjtBQUFBO0FBQUE7QUFBQSxNQUduQyxXQUFXLENBQUMsb0JBQW9CLEdBQUcsY0FBYyxDQUFDLENBQUMsQ0FBQztBQUFBLE1BQ3BELFVBQVU7QUFBQSxRQUNSLFNBQVM7QUFBQSxRQUNULFVBQVU7QUFBQSxRQUNWLGtCQUFrQjtBQUFBLE1BQ3BCO0FBQUEsTUFDQSxhQUFhO0FBQUEsSUFDZixDQUFDO0FBQUE7QUFBQSxJQUdELFdBQVc7QUFBQSxNQUNULE1BQU0sQ0FBQyxrQkFBa0Isb0JBQW9CO0FBQUE7QUFBQSxNQUM3QyxTQUFTLENBQUMsVUFBVSxjQUFjLFFBQVE7QUFBQSxNQUMxQyxLQUFLO0FBQUE7QUFBQSxNQUVMLFdBQVc7QUFBQTtBQUFBLFFBRVQsb0JBQW9CO0FBQUE7QUFBQSxRQUVwQixjQUFjLEVBQUUsb0JBQW9CLENBQUMsSUFBSSxFQUFFLENBQUM7QUFBQSxNQUM5QztBQUFBLElBQ0YsQ0FBQztBQUFBLElBRUQsT0FBTztBQUFBLE1BQ0wsa0JBQWtCO0FBQUEsSUFDcEIsQ0FBQztBQUFBLElBQ0QsTUFBTTtBQUFBLE1BQ0osYUFBYTtBQUFBLElBQ2YsQ0FBQztBQUFBO0FBQUEsSUFHRCxxQkFBcUI7QUFBQSxNQUNuQixVQUFVLENBQUMsS0FBSyxRQUFRLFFBQVEsSUFBSSxHQUFHLG1CQUFtQixDQUFDO0FBQUE7QUFBQSxNQUMzRCxVQUFVO0FBQUE7QUFBQSxNQUNWLGFBQWE7QUFBQSxJQUNmLENBQUM7QUFBQTtBQUFBLElBR0QsUUFBUTtBQUFBLE1BQ04sU0FBUyxLQUFLLFFBQVEsUUFBUSxJQUFJLEdBQUcscUJBQXFCO0FBQUEsSUFDNUQsQ0FBQztBQUFBO0FBQUEsSUFHRCxRQUFRO0FBQUEsTUFDTixlQUFlO0FBQUEsSUFDakIsQ0FBQztBQUFBO0FBQUEsSUFHRCxNQUFNO0FBQUEsTUFDSixNQUFNO0FBQUEsTUFDTixTQUFTLENBQUMsd0JBQXdCO0FBQUEsSUFDcEMsQ0FBQztBQUFBO0FBQUEsSUFHRCxRQUFRLHFCQUFxQixNQUFNLEdBQUcsRUFBRSxTQUFTLE1BQU0sS0FBSyxZQUFZO0FBQUEsSUFDeEUsUUFBUSxxQkFBcUIsTUFBTSxHQUFHLEVBQUUsU0FBUyxRQUFRLEtBQ3ZELFlBQVk7QUFBQSxNQUNWLFNBQVMsQ0FBQyxXQUFXLFNBQVM7QUFBQSxNQUM5QixXQUFXO0FBQUEsSUFDYixDQUFDO0FBQUEsSUFFSCxRQUFRLHNCQUNOLFNBQVM7QUFBQSxNQUNQLGFBQWEsUUFBUTtBQUFBLElBQ3ZCLENBQUM7QUFBQSxJQUVILFdBQVcsY0FBYztBQUFBO0FBQUEsSUFHekIsYUFBYTtBQUFBLElBRWI7QUFBQSxNQUNFLE1BQU07QUFBQSxNQUNOLFNBQVM7QUFBQSxNQUNULFdBQVcsQ0FBQyxNQUFNLE9BQU87QUFDdkIsWUFBSSxnQkFBZ0IsS0FBSyxFQUFFLEdBQUc7QUFDNUIsY0FBSSxRQUFRLHdCQUF3QixTQUFTO0FBQzNDLG1CQUFPLEtBQUssT0FBTztBQUFBO0FBQUE7QUFBQSxhQUdsQjtBQUFBLFVBQ0gsV0FBVyxRQUFRLHdCQUF3QixZQUFZO0FBQ3JELG1CQUFPLEtBQUssT0FBTztBQUFBO0FBQUE7QUFBQSxhQUdsQjtBQUFBLFVBQ0g7QUFDQSxpQkFBTztBQUFBLFlBQ0w7QUFBQSxZQUNBLEtBQUs7QUFBQSxVQUNQO0FBQUEsUUFDRjtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBQUEsSUFFQTtBQUFBLE1BQ0UsTUFBTTtBQUFBLE1BQ04sU0FBUztBQUFBLE1BQ1QsV0FBVyxDQUFDLE1BQU0sT0FBTztBQUN2QixZQUFJLGdCQUFnQixLQUFLLEVBQUUsR0FBRztBQUM1QixjQUFJLFFBQVEsNkJBQTZCLFFBQVE7QUFDL0MsbUJBQU8sS0FBSyxPQUFPO0FBQUE7QUFBQTtBQUFBLGFBR2xCO0FBQUEsVUFDSDtBQUNBLGlCQUFPO0FBQUEsWUFDTDtBQUFBLFlBQ0EsS0FBSztBQUFBLFVBQ1A7QUFBQSxRQUNGO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxJQUVBO0FBQUEsTUFDRSxNQUFNO0FBQUEsTUFDTixPQUFPO0FBQUEsTUFDUCxNQUFNLGFBQWE7QUFDakIsY0FBTSxFQUFFLE1BQU0sT0FBTyxTQUFTLFNBQVMsVUFBVSxJQUFJO0FBRXJELGdCQUFRO0FBQUEsVUFDTjtBQUFBLFlBQ0UsR0FBRztBQUFBLGNBQ0QsTUFBTSxVQUFLLFFBQVEsNENBQVMsQ0FBQyxlQUFLO0FBQUEsWUFDcEMsQ0FBQztBQUFBO0FBQUEsRUFBTztBQUFBLGNBQ047QUFBQSxZQUNGLENBQUM7QUFBQTtBQUFBLGdDQUFZLFFBQVEsb0JBQUssQ0FBQztBQUFBLFlBQzNCO0FBQUEsY0FDRSxTQUFTO0FBQUEsY0FDVCxRQUFRO0FBQUEsY0FDUixhQUFhO0FBQUEsY0FDYixlQUFlO0FBQUEsWUFDakI7QUFBQSxVQUNGO0FBQUEsUUFDRjtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBQUEsRUFDRjtBQUNBLFNBQU87QUFDVDs7O0FGekxBLElBQU0sbUNBQW1DO0FBVXpDLElBQU8sc0JBQVEsYUFBYSxDQUFDLEVBQUUsS0FBSyxNQUE2QjtBQUMvRCxRQUFNLE1BQU1DLFNBQVEsTUFBTUMsU0FBUSxJQUFJLENBQUM7QUFFdkMsUUFBTSxnQkFBMEIsQ0FBQztBQUNqQyxLQUFHLFlBQVksNkJBQTZCLEVBQUUsUUFBUSxDQUFDLFlBQVk7QUFDakUsUUFBSSxHQUFHLFNBQVMsK0JBQStCLE9BQU8sRUFBRSxFQUFFLE9BQU8sR0FBRztBQUNsRSxvQkFBYyxLQUFLLHNDQUFzQyxPQUFPLFNBQVM7QUFBQSxJQUMzRTtBQUFBLEVBQ0YsQ0FBQztBQUNELFNBQU87QUFBQSxJQUNMLE1BQU0sSUFBSTtBQUFBO0FBQUEsSUFFVixRQUFRO0FBQUEsTUFDTixNQUFNO0FBQUEsTUFDTixNQUFNO0FBQUEsTUFDTixPQUFPO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQSxRQVFMLENBQUMsSUFBSSxpQkFBaUIsR0FBRztBQUFBLFVBQ3ZCLFFBQVEsSUFBSTtBQUFBLFVBQ1osY0FBYztBQUFBLFVBQ2QsU0FBUyxDQUFDQyxVQUFTQSxNQUFLLFFBQVEsR0FBRyxJQUFJLGlCQUFpQixJQUFJLEVBQUU7QUFBQSxRQUNoRTtBQUFBLE1BQ0Y7QUFBQSxNQUVBLE1BQU07QUFBQSxJQUNSO0FBQUE7QUFBQSxJQUVBLE9BQU87QUFBQSxNQUNMLFFBQVEsU0FBUyxlQUFlLFNBQVMsUUFBUSxJQUFJO0FBQUEsTUFDckQsV0FBVyxJQUFJLHlCQUF5QjtBQUFBLE1BQ3hDLHVCQUF1QjtBQUFBO0FBQUEsTUFDdkIsUUFBUTtBQUFBO0FBQUEsTUFDUixlQUFlO0FBQUEsUUFDYixVQUFVO0FBQUEsVUFDUixlQUFlO0FBQUE7QUFBQSxVQUNmLGNBQWM7QUFBQTtBQUFBLFVBQ2QsZUFBZTtBQUFBO0FBQUEsUUFDakI7QUFBQSxRQUNBLFFBQVE7QUFBQSxVQUNOLFVBQVU7QUFBQTtBQUFBLFFBQ1o7QUFBQSxNQUNGO0FBQUEsSUFDRjtBQUFBO0FBQUEsSUFFQSxjQUFjO0FBQUEsTUFDWixTQUFTO0FBQUEsUUFDUDtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxJQUNBLFFBQVE7QUFBQSxNQUNOLGlCQUFpQixLQUFLLFVBQVU7QUFBQSxRQUM5QixLQUFLO0FBQUEsVUFDSCxTQUFTLGdCQUFJO0FBQUEsVUFDYixjQUFjLGdCQUFJO0FBQUEsVUFDbEIsaUJBQWlCLGdCQUFJO0FBQUEsUUFDdkI7QUFBQSxRQUNBLGVBQWUsTUFBTSxFQUFFLE9BQU8scUJBQXFCO0FBQUEsTUFDckQsQ0FBQztBQUFBLElBQ0g7QUFBQSxJQUNBLFNBQVMsa0JBQWtCLElBQUk7QUFBQSxJQUMvQixTQUFTO0FBQUEsTUFDUCxPQUFPO0FBQUEsUUFDTCxLQUFLQSxNQUFLLFFBQVEsa0NBQVcsT0FBTztBQUFBLFFBQ3BDLEtBQUtBLE1BQUssUUFBUSxrQ0FBVyxhQUFhO0FBQUEsTUFDNUM7QUFBQSxJQUNGO0FBQUEsSUFDQSxLQUFLO0FBQUEsTUFDSCxxQkFBcUI7QUFBQSxRQUNuQixNQUFNO0FBQUEsVUFDSixLQUFLO0FBQUEsVUFDTCxnQkFBZ0IsY0FBYyxLQUFLLEVBQUU7QUFBQSxRQUN2QztBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBQUEsRUFDRjtBQUNGLENBQUM7IiwKICAibmFtZXMiOiBbInBhdGgiLCAicHJvY2VzcyIsICJsb2FkRW52IiwgImxvYWRFbnYiLCAicHJvY2VzcyIsICJwYXRoIl0KfQo=
