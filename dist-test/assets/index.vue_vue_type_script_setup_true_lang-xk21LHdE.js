import{a as e,d as a,ai as l,b as t,B as d,D as o,o as u,e as i,w as n,g as r,f as s,h as c,Y as m,u as p,R as v,aq as f,i as g,aR as h,aS as b,m as y,x as _,q as I,ay as D,aT as R}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import V from"./DynamicConfigForm-DqS7---Y.js";const E={create:a=>e.post("/admin-api/touch/platform-config/create",a),getConfig:a=>e.get("/admin-api/touch/platform-config/select",{params:{id:a}}),getAllConfig:a=>e.get("/admin-api/touch/platform-config/get-all-config",{params:{hcode:a}}),updateData:a=>e.post("/admin-api/touch/platform-config/updateData",a),updateState:a=>e.get("/admin-api/touch/platform-config/updateState",{params:{id:a}})},S={class:"dialog-footer"},w=a({__name:"index",props:{visible:{type:Boolean,default:!1},handle:{default:"create"},configId:{default:null},rowData:{default:null}},emits:["update:visible","success"],setup(e,{emit:a}){const w=e,A=a,O=l(),j=t(),k=t(!1),U=t(!1),C=t([{label:"小红书",value:"READ_BOOK"},{label:"抖音",value:"TIK_TOK"},{label:"系统",value:"SYSTEM"},{label:"携程",value:"CTRIP"},{label:"美团",value:"MEI_TUAN"}]),q=t([{label:"个人主页",value:"PERSONAL_URL"},{label:"笔记分享",value:"SHARE_NORMAL"},{label:"视频分享",value:"SHARE_VIDEO"},{label:"店铺主页",value:"SHOP_URL"},{label:"WIFI",value:"WIFI"},{label:"用户评价",value:"USER_REVIEWS"}]),x=t({gcode:O.gcode,hcode:O.hcode,platform:"",code:"",name:"",type:"",value:"",state:!0}),T={platform:[{required:!0,message:"请选择平台",trigger:"change"}],code:[{required:!0,message:"请输入配置代码",trigger:"blur"}],type:[{required:!0,message:"请选择配置类型",trigger:"change"}],name:[{required:!0,message:"请输入备注名称",trigger:"change"}]},H=["SHARE_NORMAL","SHARE_VIDEO","WIFI","USER_REVIEWS"],L=d((()=>"create"===w.handle?"新增配置":"detail"===w.handle?U.value?"编辑配置":"配置详情":void 0)),W=d({get:()=>w.visible,set:e=>A("update:visible",e)});function F(){var e;x.value={gcode:O.gcode,hcode:O.hcode,platform:"",code:"",name:"",type:"",value:"",state:!0},"create"===w.handle&&w.rowData&&(x.value.platform=w.rowData.platform,x.value.type=w.rowData.type),null==(e=j.value)||e.clearValidate()}function K(){w.configId&&(k.value=!0,E.getConfig(w.configId).then((e=>{k.value=!1,0===e.code&&(x.value={...e.data})})).catch((()=>{k.value=!1})))}function M(){U.value=!0}function N(){var e;"detail"===w.handle&&U.value?function(){var e;null==(e=j.value)||e.validate((e=>{e&&(k.value=!0,E.updateData({...x.value,id:w.configId}).then((e=>{k.value=!1,0===e.code?(h.success({message:"更新成功",center:!0}),U.value=!1,A("success")):h.error({message:e.msg,center:!0})})).catch((()=>{k.value=!1})))}))}():null==(e=j.value)||e.validate((e=>{if(e){k.value=!0;("create"===w.handle?E.create(x.value):E.update({...x.value,id:w.configId})).then((e=>{k.value=!1,0===e.code?(h.success({message:"create"===w.handle?"创建成功":"更新成功",center:!0}),W.value=!1,A("success")):h.error({message:e.msg,center:!0})})).catch((()=>{k.value=!1}))}}))}function B(){if("detail"===w.handle&&U.value)return U.value=!1,void(w.rowData?x.value={...w.rowData}:w.configId&&K());W.value=!1}const P=d((()=>H.includes(x.value.type)));return o([()=>w.rowData,()=>w.visible,()=>w.handle],(([,e])=>{e&&(U.value=!1,"detail"===w.handle&&w.rowData?x.value={...w.rowData}:"create"===w.handle?F():w.configId&&!w.rowData?K():F())}),{deep:!0,immediate:!0}),(e,a)=>{const l=b,t=y,d=_,o=I,h=D,E=R;return u(),i(h,{modelValue:p(W),"onUpdate:modelValue":a[3]||(a[3]=e=>g(W)?W.value=e:null),title:p(L),width:"600px","close-on-click-modal":!1},{footer:n((()=>[r("div",S,[s(o,{onClick:B},{default:n((()=>[c(m("detail"===w.handle&&p(U)?"取消编辑":"取消"),1)])),_:1}),"detail"!==w.handle||p(U)?v("",!0):(u(),i(o,{key:0,type:"primary",onClick:M},{default:n((()=>a[4]||(a[4]=[c(" 编辑 ")]))),_:1})),"create"===w.handle||"detail"===w.handle&&p(U)?(u(),i(o,{key:1,type:"primary",loading:p(k),onClick:N},{default:n((()=>[c(m("create"===w.handle?"确定":"保存"),1)])),_:1},8,["loading"])):v("",!0)])])),default:n((()=>[f((u(),i(d,{ref_key:"formRef",ref:j,model:p(x),rules:T,"label-width":"120px"},{default:n((()=>[s(t,{label:"平台"},{default:n((()=>{var e;return[s(l,{value:(null==(e=p(C).find((e=>e.value===p(x).platform)))?void 0:e.label)||p(x).platform,disabled:"",placeholder:"平台"},null,8,["value"])]})),_:1}),s(t,{label:"配置类型"},{default:n((()=>{var e;return[s(l,{value:(null==(e=p(q).find((e=>e.value===p(x).type)))?void 0:e.label)||p(x).type,disabled:"",placeholder:"配置类型"},null,8,["value"])]})),_:1}),p(P)?(u(),i(V,{key:0,"config-type":p(x).type,modelValue:p(x).code,"onUpdate:modelValue":a[0]||(a[0]=e=>p(x).code=e)},null,8,["config-type","modelValue"])):(u(),i(t,{key:1,label:"商家链接",prop:"code"},{default:n((()=>[s(l,{modelValue:p(x).code,"onUpdate:modelValue":a[1]||(a[1]=e=>p(x).code=e),placeholder:"请输入商家链接",disabled:"detail"===w.handle&&!p(U)},null,8,["modelValue","disabled"])])),_:1})),s(t,{label:"备注",prop:"name"},{default:n((()=>[s(l,{modelValue:p(x).name,"onUpdate:modelValue":a[2]||(a[2]=e=>p(x).name=e),placeholder:"请输入备注名称",disabled:"detail"===w.handle&&!p(U)},null,8,["modelValue","disabled"])])),_:1})])),_:1},8,["model"])),[[E,p(k)]])])),_:1},8,["modelValue","title"])}}});export{w as _,E as t};
//# sourceMappingURL=index.vue_vue_type_script_setup_true_lang-xk21LHdE.js.map
