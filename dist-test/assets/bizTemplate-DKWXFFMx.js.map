{"version": 3, "file": "bizTemplate-DKWXFFMx.js", "sources": ["../../src/views/marketing/sms/template/bizTemplate.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"type\": \"Type\",\r\n    \"groupBusiness\": \"Group Business\",\r\n    \"storeBusiness\": \"Store Business\",\r\n    \"store\": \"Store\",\r\n    \"pleaseSelectStore\": \"Please select a store\",\r\n    \"businessType\": \"Business\",\r\n    \"pleaseSelectBusinessType\": \"Please select business type\",\r\n    \"id\": \"ID\",\r\n    \"templateName\": \"Template Name\",\r\n    \"templateContent\": \"Template Content\",\r\n    \"yes\": \"Yes\",\r\n    \"no\": \"No\",\r\n    \"auditStatus\": \"Audit Status\",\r\n    \"pendingReview\": \"Pending Review\",\r\n    \"auditSuccess\": \"Audit Success\",\r\n    \"rejected\": \"Rejected\",\r\n    \"auditResult\": \"Audit Result\",\r\n    \"isEnabled\": \"Is Enabled\",\r\n    \"operation\": \"Operation\",\r\n    \"edit\": \"Edit\",\r\n    \"templateApplication\": \"Business Template Application\",\r\n    \"changeStatusSuccess\": \"Change Status Success\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"type\": \"类型\",\r\n    \"groupBusiness\": \"集团业务\",\r\n    \"storeBusiness\": \"门店业务\",\r\n    \"store\": \"门店\",\r\n    \"pleaseSelectStore\": \"请选择门店\",\r\n    \"businessType\": \"业务类型\",\r\n    \"pleaseSelectBusinessType\": \"请选择业务类型\",\r\n    \"id\": \"id\",\r\n    \"templateName\": \"模板名称\",\r\n    \"templateContent\": \"模板内容\",\r\n    \"yes\": \"是\",\r\n    \"no\": \"否\",\r\n    \"auditStatus\": \"审核状态\",\r\n    \"pendingReview\": \"待审核\",\r\n    \"auditSuccess\": \"审核成功\",\r\n    \"rejected\": \"被拒绝\",\r\n    \"auditResult\": \"审核结果\",\r\n    \"isEnabled\": \"是否启用\",\r\n    \"operation\": \"操作\",\r\n    \"edit\": \"编辑\",\r\n    \"templateApplication\": \"业务模板申请\",\r\n    \"changeStatusSuccess\": \"修改成功\"\r\n  },\r\n  \"km\": {\r\n    \"type\": \"ប្រភេទ\",\r\n    \"groupBusiness\": \"អាជីវកម្មក្រុម\",\r\n    \"storeBusiness\": \"អាជីវកម្មហាង\",\r\n    \"store\": \"ហាង\",\r\n    \"pleaseSelectStore\": \"សូមជ្រើសរើសហាង\",\r\n    \"businessType\": \"ប្រភេទអាជីវកម្ម\",\r\n    \"pleaseSelectBusinessType\": \"សូមជ្រើសរើសប្រភេទអាជីវកម្ម\",\r\n    \"id\": \"លេខសម្គាល់\",\r\n    \"templateName\": \"ឈ្មោះគំរូ\",\r\n    \"templateContent\": \"ខ្លឹមសារគំរូ\",\r\n    \"yes\": \"បាទ/ចាស\",\r\n    \"no\": \"ទេ\",\r\n    \"auditStatus\": \"ស្ថានភាពត្រួតពិនិត្យ\",\r\n    \"pendingReview\": \"កំពុងរង់ចាំត្រួតពិនិត្យ\",\r\n    \"auditSuccess\": \"ត្រួតពិនិត្យជោគជ័យ\",\r\n    \"rejected\": \"ត្រូវបានបដិសេធ\",\r\n    \"auditResult\": \"លទ្ធផលត្រួតពិនិត្យ\",\r\n    \"isEnabled\": \"ត្រូវបានអនុញ្ញាត\",\r\n    \"operation\": \"ប្រតិបត្តិការ\",\r\n    \"edit\": \"កែសម្រួល\",\r\n    \"templateApplication\": \"ការដាក់ពាក្យគំរូអាជីវកម្ម\",\r\n    \"changeStatusSuccess\": \"ផ្លាស់ប្តូរស្ថានភាពជោគជ័យ\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { DictDataModel, SmsTemplateModel } from '@/models/index'\r\nimport { dictDataApi, merchantApi, smsRechargeOrderApi } from '@/api/modules/index'\r\nimport { SYSTEM_SMS_TEMPLATE_TYPE } from '@/models/dict/constants.ts'\r\nimport { DICT_TYPE_ID_TYPE, DictTypeEnum } from '@/models/index'\r\nimport useSettingsStore from '@/store/modules/settings'\r\n\r\nimport useUserStore from '@/store/modules/user'\r\nimport BizTemplateEditForm from './components/DetailForm/bizTemplateEdit.vue'\r\n\r\ndefineOptions({\r\n  name: 'MarketingSmsTemplateList',\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst router = useRouter()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\nconst tabbar = useTabbar()\r\nconst settingsStore = useSettingsStore()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // 表格是否自适应高度\r\n  tableAutoHeight: false,\r\n  /**\r\n   * 详情展示模式\r\n   * router 路由跳转\r\n   * dialog 对话框\r\n   * drawer 抽屉\r\n   */\r\n  formMode: 'dialog' as 'router' | 'dialog' | 'drawer',\r\n  // 详情\r\n  formModeProps: {\r\n    visible: false,\r\n    templateCode: '',\r\n  },\r\n  // 搜索\r\n  search: {\r\n    /** 是否集团模板   0 否 1 是 */\r\n    isGroup: 1,\r\n    /** 业务类型编码 */\r\n    sendScene: '',\r\n    hcode: userStore.hcode,\r\n  },\r\n  // 批量操作\r\n  batch: {\r\n    selectionDataList: [],\r\n  },\r\n  // 列表数据\r\n  dataList: [] as SmsTemplateModel[],\r\n})\r\n\r\nconst merchants = ref<{ hcode: string; hname: string }[]>([])\r\nconst bizTypes = ref<DictDataModel[]>([])\r\nconst dictTypes = [SYSTEM_SMS_TEMPLATE_TYPE]\r\nfunction getBizTupes() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    bizTypes.value = res.data.filter((item: any) => item.dictType === SYSTEM_SMS_TEMPLATE_TYPE)\r\n  })\r\n}\r\n\r\nonMounted(() => {\r\n  getMercharnts()\r\n  getDataList()\r\n  getBizTupes()\r\n})\r\n\r\nfunction getMercharnts() {\r\n  merchantApi.list({ gcode: userStore.gcode }).then((res: any) => {\r\n    merchants.value = res.data\r\n  })\r\n}\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    isGroup: !!data.value.search.isGroup,\r\n    hcode: data.value.search.hcode,\r\n    sendScene: data.value.search.sendScene,\r\n  }\r\n  smsRechargeOrderApi.templatePageList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    data.value.dataList = res.data.list\r\n    pagination.value.total = res.data.total\r\n  })\r\n}\r\n\r\n// 每页数量切换\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// 当前页码切换（翻页）\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// 字段排序\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nfunction onCreate() {\r\n  data.value.formModeProps.templateCode = ''\r\n  data.value.formModeProps.visible = true\r\n}\r\n\r\nconst info = ref<any>({})\r\nfunction onEdit(row: any) {\r\n  info.value = row\r\n  data.value.formModeProps.templateCode = row.templateCode\r\n  data.value.formModeProps.visible = true\r\n}\r\nfunction onChangeStatus(row: any) {\r\n  return new Promise<boolean>((resolve) => {\r\n    row.statusLoading = true\r\n    const params = {\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      id: row.id,\r\n      enabled: row.enabled === true ? 'false' : 'true',\r\n    }\r\n    smsRechargeOrderApi\r\n      .templateUpdate(params)\r\n      .then((res: any) => {\r\n        row.statusLoading = false\r\n        if (res.code === 0) {\r\n          const action = row.enabled ? t('enabled') : t('disable')\r\n          ElMessage.success({\r\n            message: t('changeStatusSuccess', { action }),\r\n            center: true,\r\n          })\r\n        } else {\r\n          ElMessage.error({\r\n            message: res.msg,\r\n            center: true,\r\n          })\r\n        }\r\n        resolve(true)\r\n      })\r\n      .catch(() => {\r\n        row.statusLoading = false\r\n        resolve(false)\r\n      })\r\n  })\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <search-bar :show-toggle=\"false\">\r\n        <template #default=\"{ fold }\">\r\n          <el-form :model=\"data.search\" size=\"default\" label-width=\"80px\" inline-message inline class=\"search-form\" style=\"margin-bottom: 10px\">\r\n            <el-form-item :label=\"t('type')\">\r\n              <el-radio-group v-model=\"data.search.isGroup\" @change=\"currentChange()\">\r\n                <!--                <el-radio-button :value=\"1\">\r\n                  {{ t('groupBusiness') }}\r\n                </el-radio-button> -->\r\n                <el-radio-button :value=\"1\">\r\n                  {{ t('storeBusiness') }}\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item :label=\"t('store')\">\r\n              <el-select v-model=\"data.search.hcode\" class=\"w-50\" :placeholder=\"t('pleaseSelectStore')\" style=\"width: 220px\" disabled @change=\"currentChange()\">\r\n                <el-option v-for=\"item in merchants\" :key=\"item.hcode\" :label=\"item.hname\" :value=\"item.hcode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item :label=\"t('businessType')\" label-width=\"100\">\r\n              <el-select v-model=\"data.search.sendScene\" class=\"w-50\" :placeholder=\"t('pleaseSelectBusinessType')\" clearable style=\"width: 220px\" @change=\"currentChange()\">\r\n                <el-option v-for=\"item in bizTypes\" :key=\"item.code\" :label=\"item.value\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <!-- <el-form-item style=\"float: right\">\r\n              <el-button type=\"primary\" @click=\"onCreate()\">\r\n                <template #icon>\r\n                  <svg-icon name=\"ep:plus\" />\r\n                </template>\r\n                {{ t('templateApplication') }}\r\n              </el-button>\r\n            </el-form-item> -->\r\n          </el-form>\r\n        </template>\r\n      </search-bar>\r\n      <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\" @sort-change=\"sortChange\" @selection-change=\"data.batch.selectionDataList = $event\">\r\n        <el-table-column prop=\"sendScene\" :label=\"t('businessType')\" />\r\n        <el-table-column prop=\"tplName\" :label=\"t('templateName')\" />\r\n        <el-table-column prop=\"tplContent\" :label=\"t('templateContent')\" />\r\n        <el-table-column prop=\"enabled\" :label=\"t('isEnabled')\">\r\n          <template #default=\"scope\">\r\n            <el-switch v-model=\"scope.row.enabled\" :loading=\"scope.row.statusLoading\" inline-prompt :active-text=\"t('yes')\" :inactive-text=\"t('no')\" :active-value=\"true\" :inactive-value=\"false\" :before-change=\"() => onChangeStatus(scope.row)\" />\r\n          </template>\r\n        </el-table-column>\r\n        <!--        <el-table-column :label=\"t('operation')\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-link v-if=\"scope.row.state === 10\" type=\"primary\" @click=\"onEdit(scope.row)\">\r\n              {{ t('edit') }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>  编辑模块，业务模板不需要 -->\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n    </page-main>\r\n    <!--    <FormMode v-if=\"data.formMode === 'dialog' || data.formMode === 'drawer'\" -->\r\n    <!--      :templateCode=\"data.formModeProps.templateCode\" v-model=\"data.formModeProps.visible\" :mode=\"data.formMode\" -->\r\n    <!--      @success=\"getDataList\" /> -->\r\n    <BizTemplateEditForm v-if=\"data.formModeProps.visible\" v-model=\"data.formModeProps.visible\" :merchants=\"merchants\" :info=\"info\" @success=\"getDataList\" />\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    // 让 page-main 的高度自适应\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    flex-wrap: wrap;\r\n    margin-bottom: -18px;\r\n\r\n    :deep(.el-form-item) {\r\n      flex: 1 1 300px;\r\n\r\n      &:last-child {\r\n        margin-left: auto;\r\n\r\n        .el-form-item__content {\r\n          justify-content: flex-end;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n}\r\n</style>\r\n@/api/modules/smsTemplate.api\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "useRouter", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "useTabbar", "useSettingsStore", "data", "ref", "loading", "tableAutoHeight", "formMode", "formModeProps", "visible", "templateCode", "search", "isGroup", "sendScene", "hcode", "batch", "selectionDataList", "dataList", "merchants", "bizTypes", "dictTypes", "SYSTEM_SMS_TEMPLATE_TYPE", "getDataList", "value", "params", "gcode", "smsRechargeOrderApi", "templatePageList", "then", "res", "list", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "onMounted", "merchantApi", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "filter", "item", "dictType", "info", "row", "Promise", "resolve", "statusLoading", "id", "enabled", "templateUpdate", "code", "action", "ElMessage", "success", "message", "center", "error", "msg", "catch"], "mappings": "8+CA2FM,MAAAA,EAAEA,GAAMC,IAERC,EAAYC,IACOC,IACzB,MAAMC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,kBAAcC,EAAiBC,aAAAA,GAAiBC,IACtDC,IACcC,IAEvC,MAAMC,EAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAOjBC,SAAU,SAEVC,cAAe,CACbC,SAAS,EACTC,aAAc,IAGhBC,OAAQ,CAENC,QAAS,EAETC,UAAW,GACXC,MAAOtB,EAAUsB,OAGnBC,MAAO,CACLC,kBAAmB,IAGrBC,SAAU,KAGNC,EAAYd,EAAwC,IACpDe,EAAWf,EAAqB,IAChCgB,EAAY,CAACC,GAmBnB,SAASC,IACPnB,EAAKoB,MAAMlB,SAAU,EACrB,MAAMmB,EAAS,IACV5B,IACH6B,MAAOjC,EAAUiC,MACjBb,UAAWT,EAAKoB,MAAMZ,OAAOC,QAC7BE,MAAOX,EAAKoB,MAAMZ,OAAOG,MACzBD,UAAWV,EAAKoB,MAAMZ,OAAOE,WAE/Ba,EAAoBC,iBAAiBH,GAAQI,MAAMC,IACjD1B,EAAKoB,MAAMlB,SAAU,EAChBF,EAAAoB,MAAMN,SAAWY,EAAI1B,KAAK2B,KACpBnC,EAAA4B,MAAMQ,MAAQF,EAAI1B,KAAK4B,KAAA,GACnC,CAIH,SAASC,EAAWC,GAClBpC,EAAaoC,GAAML,MAAK,IAAMN,KAAa,CAIpC,SAAAY,GAAcC,EAAO,GAC5BrC,EAAgBqC,GAAMP,MAAK,IAAMN,KAAa,CAIhD,SAASc,IAAWC,KAAEA,EAAMC,MAAAA,IAC1BvC,EAAasC,EAAMC,GAAOV,MAAK,IAAMN,KAAa,CAxCpDiB,GAAU,KAOIC,EAAAV,KAAK,CAAEL,MAAOjC,EAAUiC,QAASG,MAAMC,IACjDX,EAAUK,MAAQM,EAAI1B,IAAA,IANZmB,IAPZmB,EAAYC,iBAAiBtB,GAAWQ,MAAMC,IACnCV,EAAAI,MAAQM,EAAI1B,KAAKwC,QAAQC,GAAcA,EAAKC,WAAaxB,GAAwB,GAOhF,IA6CR,MAAAyB,GAAO1C,EAAS,mzEAME2C,QACf,IAAIC,SAAkBC,IAC3BF,EAAIG,eAAgB,EACpB,MAAM1B,EAAS,CACbC,MAAOjC,EAAUiC,MACjBX,MAAOtB,EAAUsB,MACjBqC,GAAIJ,EAAII,GACRC,SAAyB,IAAhBL,EAAIK,QAAmB,QAAU,QAE5C1B,EACG2B,eAAe7B,GACfI,MAAMC,IAED,GADJkB,EAAIG,eAAgB,EACH,IAAbrB,EAAIyB,KAAY,CAClB,MAAMC,EAASR,EAAIK,QAAU9D,EAAE,WAAaA,EAAE,WAC9CkE,EAAUC,QAAQ,CAChBC,QAASpE,EAAE,sBAAuB,CAAEiE,WACpCI,QAAQ,GACT,MAEDH,EAAUI,MAAM,CACdF,QAAS7B,EAAIgC,IACbF,QAAQ,IAGZV,GAAQ,EAAI,IAEba,OAAM,KACLf,EAAIG,eAAgB,EACpBD,GAAQ,EAAK,GACd,IA9BP,IAAwBF"}