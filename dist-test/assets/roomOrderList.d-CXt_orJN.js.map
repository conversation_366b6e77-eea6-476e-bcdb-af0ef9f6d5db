{"version": 3, "file": "roomOrderList.d-CXt_orJN.js", "sources": ["../../src/views/room/realtime/components/roomOrderList.d.ts"], "sourcesContent": ["/** 创建预定订单 */\r\ndeclare namespace roomOrderList {\r\n  interface Request extends hgCode {\r\n    /** 批次列表 */\r\n    batches: Batch[]\r\n    /** 预订单号 */\r\n    bookNo?: string\r\n    /** 预订类型;general:(个人)普通订单  group：团队订单 */\r\n    bookType: string\r\n    /** 渠道代码 */\r\n    channelCode: string\r\n    /** 入住人姓名 */\r\n    checkinPerson?: string\r\n    /** 入住人电话 */\r\n    checkinPhone?: string\r\n    /** 入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队 */\r\n    checkinType?: string\r\n    /** 预订人(联系人);如果是团队预订，这里保存团队的联系人姓名 */\r\n    contact: string\r\n    /** 合同号;团队预订时输入 */\r\n    contractNo?: string\r\n    /** 延迟退房分钟数 */\r\n    delayMinute?: number\r\n    /** 担保方式 */\r\n    guarantyStyle?: string\r\n    /** 客人代码;会员代码、协议单位、中介代码 */\r\n    guestCode?: string\r\n    /** 客人名称;会员姓名、协议单位、中介名称 */\r\n    guestName?: string\r\n    /** 客源类型 */\r\n    guestSrcType: string\r\n    /** 小时房代码;入住类型为时租房时，该字段有值 */\r\n    hourCode?: string\r\n    /** id */\r\n    id?: number\r\n    /** 是否发短信;0：否 1：是 */\r\n    isSendSms: string\r\n    /** 市场活动代码 */\r\n    marketActivityCode?: string\r\n    /** 市场活动名称 */\r\n    marketActivityName?: string\r\n    /** 订单来源 */\r\n    orderSource: string\r\n    /** 外部订单号 */\r\n    outOrderNo?: string\r\n    /** 外部订单备注 */\r\n    outOrderRemark?: string\r\n    /** 预订人电话;联系电话 */\r\n    phone?: string\r\n    /** 预抵时间;普通预订存储，团队预订低离时间存储在预订房型中 */\r\n    planCheckinTime?: string\r\n    /** 预离时间;普通预订存储，团队预订低离时间存储在预订房型中 */\r\n    planCheckoutTime?: string\r\n    /** 平台 */\r\n    platform?: string\r\n    /** 订单备注 */\r\n    remark?: string\r\n    /** 保留时间 */\r\n    retainTime?: string\r\n    /** 销售员 */\r\n    seller?: string\r\n    /** 团队代码 */\r\n    teamCode?: string\r\n    /** 团队名称;团队预订时输入 */\r\n    teamName?: string\r\n    [property: string]: any\r\n  }\r\n  /** 批次列表 */\r\n  interface Batch {\r\n    /** 批次号 */\r\n    batchNo: string\r\n    /** 预订房型列表 */\r\n    bookRoomTypes: BookRoomType[]\r\n    /** 天数 */\r\n    days: number\r\n    /** 预低时间 */\r\n    planCheckinTime: string\r\n    /** 预离时间 */\r\n    planCheckoutTime: string\r\n    [property: string]: any\r\n  }\r\n  /** 预订房型列表 */\r\n  interface BookRoomType {\r\n    /** 赠早餐份数,前端提交赠早数 */\r\n    bkNum: number\r\n    /** 排房列表 */\r\n    bookRooms?: BookRoom[]\r\n    /** 每日价格 */\r\n    dayPrices: DayPrice[]\r\n    /** 是否会议室;0:客房 1:会议室 */\r\n    isMeetingRoom?: string\r\n    /** 价格类型;0：放盘价 1：手工价 */\r\n    priceType?: string\r\n    /** 预订房间数 */\r\n    roomNum: number\r\n    /** 房型代码 */\r\n    rtCode: string\r\n    /** 房型名称 */\r\n    rtName?: string\r\n    [property: string]: any\r\n  }\r\n  /** 排房列表 */\r\n  interface BookRoom {\r\n    /** 选中的房间是不是已有预订单占用;0:否 1:是 */\r\n    preOccupied: string\r\n    /** 房间代码 */\r\n    rCode: string\r\n    /** 房号 */\r\n    rNo: string\r\n    /** 状态 */\r\n    state?: string\r\n    [property: string]: any\r\n  }\r\n  /** 每日价格 */\r\n  interface DayPrice {\r\n    /** 赠早餐份数 */\r\n    bkNum?: number\r\n    /** 预订价格,单位元 */\r\n    price: number\r\n    /** 日期 */\r\n    priceDate: string\r\n    /** 价格策略代码 */\r\n    priceStrategyCode?: string\r\n    /** 价格类型 */\r\n    priceType?: string\r\n    /** 房包早餐数 */\r\n    roomBkNum?: number\r\n    /** 优惠价,单位元 */\r\n    vipPrice: number\r\n    /** 周几 */\r\n    week?: number\r\n    [property: string]: any\r\n  }\r\n  /** ota房型可以关联多个酒店房型 */\r\n  interface roomTypeRefList extends hgCode {\r\n    /** 酒店房型名称 */\r\n    rtName?: string\r\n    /** 酒店房型代码 */\r\n    rtCode: string\r\n    /** 渠道 */\r\n    channel?: string\r\n    /** 绑定状态 */\r\n    state?: number\r\n    /**  OTA房型列表 */\r\n    otaRoomTypes?: OtaRoomType[]\r\n    /** 渠道房型 */\r\n    otaRoomTypeName?: string\r\n    /** 酒店房型代码 */\r\n    roomTypeCode?: string\r\n  }\r\n  /** ota radio列表类型 */\r\n  interface otaOptions {\r\n    /** 值 */\r\n    value: string\r\n    /** 名称 */\r\n    label: string\r\n  }\r\n  /** ota房型列表 */\r\n  interface OtaRoomType {\r\n    /** ota产品列表 */\r\n    otaProducts?: OtaProduct[]\r\n    /** ota房型代码 */\r\n    otaRoomTypeCode: string\r\n    /** ota房型名称 */\r\n    otaRoomTypeName: string\r\n    [property: string]: any\r\n  }\r\n  /** ota产品列表 */\r\n  interface OtaProduct {\r\n    /** 产品代码 */\r\n    productCode?: string\r\n    /** 产品名称 */\r\n    productName?: string\r\n    [property: string]: any\r\n  }\r\n  /** 获得协议单位,中介列表,只包括代码和名称 */\r\n  interface simpleType {\r\n    /** 渠道代码 */\r\n    channel: string\r\n    /** 中介/协议单位代码 */\r\n    paCode: string\r\n    /** 中介/协议单位名称 */\r\n    paName: string\r\n    sellLevel: string\r\n  }\r\n}\r\nexport = roomOrderList\r\n"], "names": ["require_roomOrderList_d_040", "exports", "module", "roomOrderList"], "mappings": "qCA0LAA,KAAA,CAAA,qCAAAC,EAAAC,GAAAA,EAASD,QAAAE,aAAA"}