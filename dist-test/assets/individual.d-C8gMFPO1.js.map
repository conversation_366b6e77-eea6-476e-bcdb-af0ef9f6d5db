{"version": 3, "file": "individual.d-C8gMFPO1.js", "sources": ["../../src/views/room/booking/individual.d.ts"], "sourcesContent": ["/** 预订弹窗 */\r\ndeclare namespace individual {\r\n  /** 公用类型 */\r\n  interface type {\r\n    /** Code */\r\n    code: string\r\n    /** 名称 */\r\n    label: string\r\n  }\r\n  /** 协议类型 */\r\n  interface simpleType {\r\n    /** 中介/协议单位代码 */\r\n    paCode: string\r\n    /** 中介/协议单位名称 */\r\n    paName: string\r\n    /** 渠道代码\r\n     */\r\n    channel?: string\r\n  }\r\n  interface ParameterMaps {\r\n    bkNum: number\r\n    price: number\r\n    priceDate: string\r\n    priceStrategyCode: string\r\n    roomBkNum: number\r\n    vipPrice: number\r\n    week: number\r\n    priceType?: string\r\n  }\r\n  /** 会员信息类型 */\r\n  interface membersType {\r\n    mcode: string\r\n    name: string\r\n    phone: string\r\n    idNo: string\r\n  }\r\n  /** 预订人姓名 */\r\n  interface dataListType {\r\n    id: number\r\n    /** 姓名 */\r\n    name: string\r\n    /** 手机号 */\r\n    phone: string\r\n    /** 身份证 */\r\n    idNo: string\r\n  }\r\n}\r\n\r\nexport = individual\r\n"], "names": ["require_individual_d_047", "exports", "module", "individual"], "mappings": "qCAgDAA,KAAA,CAAA,kCAAAC,EAAAC,GAAAA,EAASD,QAAAE,UAAA"}