<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <CalculationMode>Interpretation</CalculationMode>
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="1">
      <printTogetherBill78 Ref="2" type="Stimulsoft.Report.Dictionary.StiJsonDatabase" isKey="true">
        <Alias>printTogetherBill78</Alias>
        <HeadersString />
        <Key />
        <Name>printTogetherBill78</Name>
        <PathData>D:\workspace\qz\frontend\aflower-pms-front2\public\reports\print\printTogetherBill78.json</PathData>
      </printTogetherBill78>
    </Databases>
    <DataSources isList="true" count="3">
      <root Ref="3" type="DataTableSource" isKey="true">
        <Alias>root</Alias>
        <Columns isList="true" count="21">
          <value>gcode,System.String</value>
          <value>hcode,System.String</value>
          <value>hname,System.String</value>
          <value>frontPhone,System.String</value>
          <value>address,System.String</value>
          <value>orderNo,System.String</value>
          <value>printDate,System.String</value>
          <value>name,System.String</value>
          <value>rtName,System.String</value>
          <value>guestSrcTypeName,System.String</value>
          <value>info,System.String</value>
          <value>balance,System.String</value>
          <value>point,System.String</value>
          <value>checkinTime,System.String</value>
          <value>checkoutTime,System.String</value>
          <value>payTotalFee,System.Decimal</value>
          <value>consumeTotalFee,System.Decimal</value>
          <value>paymentDetails,System.String</value>
          <value>consumptionDetails,System.String</value>
          <value>rNo,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>6b59abab930445dcb4b96b4b15b23c52</Key>
        <Name>root</Name>
        <NameInSource>printTogetherBill78.root</NameInSource>
      </root>
      <root_consumptionDetails Ref="4" type="DataTableSource" isKey="true">
        <Alias>root_consumptionDetails</Alias>
        <Columns isList="true" count="5">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>createTime,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>b9d1ee321dbf4b108569b5668bf4199a</Key>
        <Name>root_consumptionDetails</Name>
        <NameInSource>printTogetherBill78.root_consumptionDetails</NameInSource>
      </root_consumptionDetails>
      <root_paymentDetails Ref="5" type="DataTableSource" isKey="true">
        <Alias>root_paymentDetails</Alias>
        <Columns isList="true" count="5">
          <value>subCode,System.String</value>
          <value>subName,System.String</value>
          <value>fee,System.Decimal</value>
          <value>createTime,System.String</value>
          <value>relationId,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Key>30c331a1655642bfaa47e279bfc4058d</Key>
        <Name>root_paymentDetails</Name>
        <NameInSource>printTogetherBill78.root_paymentDetails</NameInSource>
      </root_paymentDetails>
    </DataSources>
    <Relations isList="true" count="2">
      <root Ref="6" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="4" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_consumptionDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>consumptionDetails</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
      <root Ref="7" type="DataRelation" isKey="true">
        <Alias>root</Alias>
        <ChildColumns isList="true" count="1">
          <value>relationId</value>
        </ChildColumns>
        <ChildSource isRef="5" />
        <Dictionary isRef="1" />
        <Name>root</Name>
        <NameInSource>root_paymentDetails</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>paymentDetails</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </root>
    </Relations>
    <Report isRef="0" />
    <Resources isList="true" count="0" />
    <UserFunctions isList="true" count="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <Key>b70194ebbc8245bfa54f55b8357b588f</Key>
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="15">
        <Text33 Ref="9" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>22.8,12,5.2,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text33</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text33>
        <Text38 Ref="10" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>21,12,1.8,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text38</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text38>
        <Text34 Ref="11" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>22.8,13.6,5.2,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text34</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text34>
        <Text43 Ref="12" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>21,13.6,1.8,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Name>Text43</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text43>
        <ReportTitleBand1 Ref="13" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,4.2</ClientRectangle>
          <Components isList="true" count="18">
            <Text5 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,11,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text5</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.hname}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text1 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,19,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text1</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>宾客账单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,4.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text3</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>NO:{root.orderNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,4.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Name>Text4</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>打印时间:{Time}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text11 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,2.4,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text6 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.4,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>客人姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text9 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,2.4,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>房号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,2.4,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.rNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text8 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12,2.4,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>房型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text7 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,2.4,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.rtName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text37 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>储值余额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text39 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,3,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.balance}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text20 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>积分余额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text41 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,3,11.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.point}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text15 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>入住时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text26 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,3.6,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.checkinTime.Substring(0, 16)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,3.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>离店时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text30 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,3.6,11.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="8" />
              <Parent isRef="13" />
              <Text>{root.checkoutTime.Substring(0, 16)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </ReportTitleBand1>
        <页眉1 Ref="32" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.4,19,0.6</ClientRectangle>
          <Components isList="true" count="3">
            <Text18 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text18</Name>
              <Page isRef="8" />
              <Parent isRef="32" />
              <Text>账目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0,7.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text19</Name>
              <Page isRef="8" />
              <Parent isRef="32" />
              <Text>时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text21</Name>
              <Page isRef="8" />
              <Parent isRef="32" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <PrintOn>OnlyFirstPage</PrintOn>
        </页眉1>
        <GroupHeaderBand1 Ref="36" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.8,19,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <文本1 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Margins>5,0,0,0</Margins>
              <Name>文本1</Name>
              <Page isRef="8" />
              <Parent isRef="36" />
              <Text>{root_consumptionDetails.createTime.Substring(0, 10)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本1>
          </Components>
          <Condition>{root_consumptionDetails.createTime.Substring(0, 10)}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupHeaderBand1>
        <DataBand1 Ref="38" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <CanBreak>True</CanBreak>
          <CanShrink>True</CanShrink>
          <ClientRectangle>0,8.2,19,0.8</ClientRectangle>
          <Components isList="true" count="3">
            <Text22 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="8" />
              <Parent isRef="38" />
              <Text>{root_consumptionDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text24 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0,7.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text24</Name>
              <Page isRef="8" />
              <Parent isRef="38" />
              <Text>{root_consumptionDetails.createTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text25</Name>
              <Page isRef="8" />
              <Parent isRef="38" />
              <Text>{root_consumptionDetails.fee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
          </Components>
          <Conditions isList="true" count="0" />
          <DataSourceName>root_consumptionDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="42" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.8,19,0.8</ClientRectangle>
          <Components isList="true" count="2">
            <Text17 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>小计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text28 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text28</Name>
              <Page isRef="8" />
              <Parent isRef="42" />
              <Text>{Sum(root_consumptionDetails.fee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupFooterBand1>
        <FooterBand1 Ref="45" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11.4,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text29 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="8" />
              <Parent isRef="45" />
              <Text>消费合计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text31 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text31</Name>
              <Page isRef="8" />
              <Parent isRef="45" />
              <Text>{Sum(root_consumptionDetails.fee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand1</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </FooterBand1>
        <页眉2 Ref="48" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,12.8,19,0.6</ClientRectangle>
          <Components isList="true" count="3">
            <Text32 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text32</Name>
              <Page isRef="8" />
              <Parent isRef="48" />
              <Text>账目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text35 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0,7.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text35</Name>
              <Page isRef="8" />
              <Parent isRef="48" />
              <Text>时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text36</Name>
              <Page isRef="8" />
              <Parent isRef="48" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>页眉2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </页眉2>
        <GroupHeaderBand2 Ref="52" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,14.2,19,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <文本2 Ref="53" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <Name>文本2</Name>
              <Page isRef="8" />
              <Parent isRef="52" />
              <Text>{root_paymentDetails.createTime.Substring(0, 10)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </文本2>
          </Components>
          <Condition>{root_paymentDetails.createTime.Substring(0, 10)}</Condition>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupHeaderBand2>
        <DataBand2 Ref="54" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <CanBreak>True</CanBreak>
          <CanShrink>True</CanShrink>
          <ClientRectangle>0,15.6,19,0.6</ClientRectangle>
          <Components isList="true" count="3">
            <Text42 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="8" />
              <Parent isRef="54" />
              <Text>{root_paymentDetails.subName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text44 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0,7.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text44</Name>
              <Page isRef="8" />
              <Parent isRef="54" />
              <Text>{root_paymentDetails.createTime}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="57" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text45</Name>
              <Page isRef="8" />
              <Parent isRef="54" />
              <Text>{root_paymentDetails.fee}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
          </Components>
          <Conditions isList="true" count="0" />
          <DataSourceName>root_paymentDetails</DataSourceName>
          <Expressions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
          <Sort isList="true" count="0" />
        </DataBand2>
        <GroupFooterBand2 Ref="58" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,17,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text46 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="8" />
              <Parent isRef="58" />
              <Text>小计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text47</Name>
              <Page isRef="8" />
              <Parent isRef="58" />
              <Text>{Sum(root_paymentDetails.fee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </GroupFooterBand2>
        <FooterBand2 Ref="61" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.4,19,2.2</ClientRectangle>
          <Components isList="true" count="3">
            <Text50 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <CanShrink>True</CanShrink>
              <ClientRectangle>0,1,19,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>5,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <PrintOn>OnlyLastPage</PrintOn>
              <Text>门店电话：{root.frontPhone}      宾客签名：

门店地址：{root.address}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text48 Ref="63" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,5,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <Text>付款合计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,0,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Expressions isList="true" count="0" />
              <Font>Arial,9,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Name>Text49</Name>
              <Page isRef="8" />
              <Parent isRef="61" />
              <Text>{Sum(root_paymentDetails.fee)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
          </Components>
          <Conditions isList="true" count="0" />
          <Expressions isList="true" count="0" />
          <Name>FooterBand2</Name>
          <Page isRef="8" />
          <Parent isRef="8" />
        </FooterBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Expressions isList="true" count="0" />
      <Guid>4ec33daf867c4f64a9b2ea132dee52e8</Guid>
      <LargeHeight>True</LargeHeight>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>11</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
    </Page1>
  </Pages>
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>宾客账单</ReportAlias>
  <ReportAuthor>xiao lin</ReportAuthor>
  <ReportChanged>12/15/2024 8:36:47 PM</ReportChanged>
  <ReportCreated>11/1/2024 10:23:35 AM</ReportCreated>
  <ReportFile>D:\workspace\qz\frontend\aflower-pms-front2\public\reports\print\printTogetherBillA4-1-3.mrt</ReportFile>
  <ReportGuid>36eb40424246458b82b4e8a3ac0e132b</ReportGuid>
  <ReportName>宾客账单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2024.4.1.0</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 宾客账单 : Stimulsoft.Report.StiReport
    {
        public 宾客账单()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
		#endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>